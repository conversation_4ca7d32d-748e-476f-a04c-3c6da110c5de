#!/usr/bin/env python3
"""
Test script to verify the service configuration helper works correctly
"""

import sys
import os

# Add the CodeGenerationAgent src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'CodeGenerationAgent', 'src'))

from code_generation_core_agent.utils.service_config_helper import (
    ServiceConfigHelper,
    ServiceAuthType,
    generate_service_action_request,
    is_oauth_service
)

def test_service_config_helper():
    """Test the service configuration helper"""
    
    print("🧪 Testing Service Configuration Helper")
    print("=" * 50)
    
    # Test OAuth services
    print("\n📱 Testing OAuth Services:")
    oauth_services = ServiceConfigHelper.get_oauth_services()
    print(f"OAuth services: {oauth_services}")
    
    for service in oauth_services:
        print(f"  - {service}: requires_oauth = {ServiceConfigHelper.requires_oauth(service)}")
        config = ServiceConfigHelper.get_service_config(service)
        print(f"    Display name: {config['display_name']}")
        print(f"    Env vars: {config['env_vars']}")
    
    # Test Manual services
    print("\n🔑 Testing Manual Setup Services:")
    manual_services = ServiceConfigHelper.get_manual_services()
    print(f"Manual services: {manual_services}")
    
    for service in manual_services:
        print(f"  - {service}: requires_manual_setup = {ServiceConfigHelper.requires_manual_setup(service)}")
        config = ServiceConfigHelper.get_service_config(service)
        print(f"    Display name: {config['display_name']}")
        print(f"    Env vars: {config['env_vars']}")
    
    # Test specific cases
    print("\n🎯 Testing Specific Cases:")
    test_cases = [
        ("supabase", True, False),  # Should be OAuth
        ("openai", False, True),    # Should be manual
        ("stripe", None, None),     # Not configured yet
        ("unknown", None, None),    # Unknown service
    ]
    
    for service, expected_oauth, expected_manual in test_cases:
        oauth_result = ServiceConfigHelper.requires_oauth(service)
        manual_result = ServiceConfigHelper.requires_manual_setup(service)
        auth_type = ServiceConfigHelper.get_service_auth_type(service)
        
        print(f"  - {service}:")
        print(f"    OAuth: {oauth_result} (expected: {expected_oauth})")
        print(f"    Manual: {manual_result} (expected: {expected_manual})")
        print(f"    Auth type: {auth_type}")
        
        # Validate results
        if expected_oauth is not None:
            assert oauth_result == expected_oauth, f"OAuth test failed for {service}"
        if expected_manual is not None:
            assert manual_result == expected_manual, f"Manual test failed for {service}"
    
    # Test action request generation
    print("\n📋 Testing Action Request Generation:")
    
    # Test Supabase OAuth action request
    supabase_action = ServiceConfigHelper.generate_action_request("supabase")
    print(f"Supabase action request:")
    print(f"  - action_type: {supabase_action['action_type']}")
    print(f"  - service_name: {supabase_action['service_name']}")
    print(f"  - oauth_flow: {supabase_action.get('oauth_flow', 'Not set')}")
    
    assert supabase_action['action_type'] == 'setup_service_oauth'
    assert supabase_action['oauth_flow'] == True
    
    # Test OpenAI manual action request
    openai_action = ServiceConfigHelper.generate_action_request("openai")
    print(f"\nOpenAI action request:")
    print(f"  - action_type: {openai_action['action_type']}")
    print(f"  - service_name: {openai_action['service_name']}")
    print(f"  - environment_variable: {openai_action['environment_variable']}")
    
    assert openai_action['action_type'] == 'setup_service'
    assert len(openai_action['environment_variable']) > 0
    
    # Test convenience functions
    print("\n🔧 Testing Convenience Functions:")

    # Test the main function orchestrator will use
    supabase_request = generate_service_action_request("supabase")
    print(f"generate_service_action_request('supabase'):")
    print(f"  - action_type: {supabase_request['action_type']}")
    print(f"  - oauth_flow: {supabase_request.get('oauth_flow')}")

    openai_request = generate_service_action_request("openai")
    print(f"generate_service_action_request('openai'):")
    print(f"  - action_type: {openai_request['action_type']}")
    print(f"  - has env vars: {len(openai_request.get('environment_variable', [])) > 0}")

    # Test quick OAuth check
    print(f"is_oauth_service('supabase'): {is_oauth_service('supabase')}")
    print(f"is_oauth_service('openai'): {is_oauth_service('openai')}")

    print("\n✅ All tests passed!")
    print("\n🎉 Service Configuration Helper is working correctly!")

    # Show summary
    print(f"\n📊 Summary:")
    print(f"  - Total services: {len(ServiceConfigHelper.get_all_services())}")
    print(f"  - OAuth services: {len(oauth_services)}")
    print(f"  - Manual services: {len(manual_services)}")

if __name__ == "__main__":
    test_service_config_helper()
