#!/bin/bash
set -e
export PYTHONPATH="$(realpath .)/src"

TEST_ARGS=""
INTERACTIVE=false
BENCHMARK_NAME=""

# Parse command line arguments
while getopts "it:" opt; do
    case ${opt} in
        i )
            INTERACTIVE=true
            TEST_ARGS="-s -v"
            ;;
        t )
            BENCHMARK_NAME="$OPTARG"
            TEST_ARGS="-s -v"
            ;;
        \? )
            echo "Usage: $0 [-i] [-t benchmark_name]"
            exit 1
            ;;
    esac
done

run_test() {
    local test_number=$1
    local test_name="${test_names[$test_number]}"
    echo "Running test with arguments: ${TEST_ARGS}"
    if [ -n "$BENCHMARK_NAME" ]; then
        # If benchmark name is provided, only run that specific test
        if [[ "$test_name" == *"$BENCHMARK_NAME"* ]]; then
            pytest "test/test_long_code_generation.py::TestCodeGenerator::$test_name" ${TEST_ARGS}
            exit 0
        fi
    else
        pytest "test/test_long_code_generation.py::TestCodeGenerator::$test_name" ${TEST_ARGS}
    fi
}

# Array of test names
test_names=(
    "test_background_color"
    "test_complete_codegen_hello_world_app"
    "test_complete_codegen_online_hello_world"
    "test_complete_codegen_blog_platform"
    "test_complete_codegen_event_countdown_timer"
    "test_complete_codegen_expense_tracker"
    "test_complete_codegen_language_learning_app"
    "test_complete_codegen_meme_generator"
    "test_complete_codegen_movie_recommendation_app"
    "test_complete_codegen_pomodoro_timer"
    "test_complete_codegen_portfolio_website_builder"
    "test_complete_codegen_task_management_kanban_board"
    "test_complete_codegen_retro_gomoku_backend"
)

if [ -n "$BENCHMARK_NAME" ] && ! $INTERACTIVE; then
    # Run only tests matching the benchmark name
    found_match=false
    for i in "${!test_names[@]}"; do
        if [[ "${test_names[$i]}" == *"$BENCHMARK_NAME"* ]]; then
            run_test $i
            found_match=true
        fi
    done
    if ! $found_match; then
        echo "No tests found matching benchmark name: $BENCHMARK_NAME"
        exit 1
    fi
elif $INTERACTIVE; then
    echo "Interactive mode. Please select a test to run:"
    for i in "${!test_names[@]}"; do
        echo "$((i+1)). ${test_names[$i]}"
    done
    read -p "Enter the number of the test you want to run (1-${#test_names[@]}): " selection
    if [[ "$selection" =~ ^[0-9]+$ ]] && [ "$selection" -ge 1 ] && [ "$selection" -le "${#test_names[@]}" ]; then
        run_test $((selection-1))
    else
        echo "Invalid selection. Exiting."
        exit 1
    fi
else
    # Run all tests
    for test_name in "${test_names[@]}"; do
        pytest "test/test_long_code_generation.py::TestCodeGenerator::$test_name" ${TEST_ARGS}
    done
fi
