{"cells": [{"cell_type": "markdown", "id": "6bdb446f-8a03-49c1-bed5-23a4e5d9658e", "metadata": {}, "source": ["# Multi project example"]}, {"cell_type": "code", "execution_count": 1, "id": "32ed9991-7daf-4d06-8f96-bc505f72a367", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:27.813404Z", "start_time": "2025-06-18T07:24:26.067009Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/kavia/CodeGenerationAgent/venv/lib/python3.12/site-packages/pymilvus/client/__init__.py:6: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.\n", "  from pkg_resources import DistributionNotFound, get_distribution\n"]}], "source": ["import os;\n", "\n", "from code_generation_core_agent.agents.project_welcome_page import create_project_welcome_page\n", "from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent, TaskExecutionReporter\n", "from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader\n", "from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType\n", "from code_generation_core_agent.chat.chat_enabled_worker import ChatEnabledWorker\n", "from code_generation_core_agent.config import config\n", "from code_generation_core_agent.llm.chat_interface import ChatInterface\n", "from code_generation_core_agent.llm.llm_interface import LLMInterface\n", "from code_generation_core_agent.project_schemas import ProjectSchema, ContainerType\n", "import asyncio\n", "import json"]}, {"cell_type": "markdown", "id": "8bab8724-2113-48dd-a4c3-36bfce5623e4", "metadata": {}, "source": ["## Overview\n", "\n", "The `create_project_welcome_page` function is an LLM project schema generator that takes high-level project requirements and generates a structured `ProjectSchema` with container configurations and dependencies. First we will create a single frontend projet without any backends.\n"]}, {"cell_type": "markdown", "id": "4d8a8e7444b22cea", "metadata": {}, "source": ["## Single Container setup.\n", "We only set `frontend_framework` in the project_config"]}, {"cell_type": "code", "execution_count": 2, "id": "e0ac4fe9-d02b-4f89-a561-2a2305903590", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:31.356803Z", "start_time": "2025-06-18T07:24:27.855764Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Event 'llm.timeoutevent' reported successfully\n", "Event 'llm.timeout.count' reported successfully\n"]}], "source": ["async def create_basic_project():\n", "    project_config = {\n", "        \"project_name\": \"TicTacToe\",\n", "        \"description\": \"A modern web application with API backend\",\n", "        \"frontend_framework\": \"React\",\n", "        \"backend_framework\": \"Not in use\", # Anything that signals to the LLM that this is not needed\n", "        \"database_framework\": \"Disabled\",\n", "    }\n", "\n", "    project_schema = await create_project_welcome_page(project_config)\n", "    return project_schema\n", "\n", "# Execute\n", "project = await(create_basic_project())"]}, {"cell_type": "code", "execution_count": 4, "id": "f9b5b45d-7bd9-43e9-880c-332356c936a0", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:31.372771Z", "start_time": "2025-06-18T07:24:31.369591Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["List of initialized containers:\n", "- tictactoe_frontend  | Generated description : Frontend client for TicTacToe game\n", "Container({'container_name': 'tictactoe_frontend', 'description': 'Frontend client for TicTacToe game', 'interfaces': 'Web UI', 'container_type': <ContainerType.FRONTEND: 'frontend'>, 'dependent_containers': [], 'workspace': '', 'ports': '', 'framework': 'react', 'type': '', 'buildCommand': '', 'startCommand': '', 'lintCommand': '', 'container_details': {'features': ['User interface for playing TicTacToe', 'Game status display', 'Restart game option', 'Responsive design'], 'colors': {'primary': '#1976D2', 'secondary': '#424242', 'accent': '#FFC107', 'background': '#FFFFFF'}, 'theme': 'light', 'layout_description': 'Central game board with top header and restart button below', 'style': 'modern'}, 'lintConfig': '', 'routes': [], 'apiSpec': '', 'auth': None, 'schema': '', 'migrations': '', 'seed': ''})\n"]}], "source": ["print(\"List of initialized containers:\")\n", "for container in project.containers:\n", "    print(\"-\", container.container_name, \" | Generated description :\", container.description)\n", "    print(container)"]}, {"cell_type": "markdown", "id": "c1cef0315288aa37", "metadata": {}, "source": ["## Multi Container setup setup\n", "We only set multiple frameworks for the project"]}, {"cell_type": "code", "execution_count": 5, "id": "45f185fae2ab6525", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:39.283104Z", "start_time": "2025-06-18T07:24:31.428027Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metrics submitted successfully: 2 datapoints\n", "List of initialized containers:\n", "- tictactoe_frontend  | Generated description : User interface for the tic-tac-toe game allowing players to interact with the game board and view game state.\n", "- tictactoe_backend  | Generated description : Handles game logic, player actions, and communicates with the database to store and retrieve game state.\n", "- tictactoe_database  | Generated description : Stores game states, player data, and scores.\n"]}], "source": ["# Multi-container setup\n", "async def create_basic_project():\n", "    project_config = {\n", "            \"project_name\": \"tict<PERSON><PERSON>\",\n", "            \"description\": \"Tic-tac-toe game.\",\n", "            \"frontend_framework\": \"React\",\n", "            \"mobile_framework\": \"None\",\n", "            \"backend_framework\": \"Flask\",\n", "            \"database_framework\": \"PostgresQL\",\n", "        }\n", "\n", "    project_schema = await create_project_welcome_page(project_config)\n", "    return project_schema\n", "\n", "# Execute\n", "project = await(create_basic_project())\n", "print(\"List of initialized containers:\")\n", "for container in project.containers:\n", "    print(\"-\", container.container_name, \" | Generated description :\", container.description)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "3cde0a22afc1c782", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:39.310529Z", "start_time": "2025-06-18T07:24:39.305813Z"}}, "outputs": [], "source": ["# Now add a third party integration. The valid third party services are defined in the directory:\n", "# src/code_generation_core_agent/services\n", "project.overview.third_party_services = [\"Supabase\"] # Must be set to pass the SUPABASE_ env vars to docker!\n", "\n", "# Set by the SupaBase service. Currently it MUST be part of the system env.\n", "#\n", "#        self.supabase_url = env.get(\"SUPABASE_URL\", \"\")\n", "#        self.supabase_key = env.get(\"SUPABASE_KEY\", \"\")\n", "#        self.supabase_db_url = env.get(\"SUPABASE_DB_URL\", \"\")\n", "#\n", "\n", "# We need to add the port defintions. Without port defintions the preview will not work and report an error\n", "# You can look up the various containers using the `get_containers_by_type` method.\n", "# We may have multiple containers of the same type in the future, but currently it is ok to assume that there is only one.\n", "# The default port from the config file is used\n", "bcs = project.get_containers_by_type(ContainerType.BACKEND)\n", "\n", "if bcs:\n", "    bcs[0].ports = config.get(\"CONTAINER\", \"default_backend_port\")\n", "    bcs[0].apiSpec = \"openapi.json\"  # Set default API spec for backend\n", "\n", "# Set database port\n", "dtbs = project.get_containers_by_type(ContainerType.DATABASE)\n", "\n", "if dtbs:\n", "    dtbs[0].ports = config.get(\"CONTAINER\", \"default_database_port\")\n", "\n", "# Set frontend port\n", "fcs = project.get_containers_by_type(ContainerType.FRONTEND)\n", "if fcs:\n", "    fcs[0].ports = config.get(\"CONTAINER\", \"default_frontend_port\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "7b1b58d0d5c2c03f", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:39.368488Z", "start_time": "2025-06-18T07:24:39.359236Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully saved manifest to /tmp/sample_session/.project_manifest.yaml\n", "Successfully loaded manifest from /tmp/sample_session/.project_manifest.yaml\n", "Project loaded from manifest file:  tictactoe\n"]}], "source": ["# Saving project to the manifest. The manifest should be saved at major exection checkpoints, like starting the code generation or stopping it.\n", "# Note that the manifest will remove all private info and env variables from the project to ensure that the manifest is not exposing any sensitive information.\n", "session_dir=\"/tmp/sample_session\"\n", "os.makedirs(session_dir, exist_ok=True)\n", "\n", "# By default you should save the manifest file in the session directory with the name `.project_manifest.yaml`\n", "manifest_path = os.path.join(session_dir, \".project_manifest.yaml\")\n", "project.save_to_manifest(manifest_path)\n", "\n", "# We can also load the manifest later. This is useful if you want to resume the project from a previous checkpoint.\n", "project_reloaded : ProjectSchema = ProjectSchema.load_from_file(manifest_path)\n", "print(\"Project loaded from manifest file: \", project_reloaded.overview.project_name)"]}, {"cell_type": "markdown", "id": "57b9c785fdab9a2d", "metadata": {}, "source": ["## Constructing a work item from project schema"]}, {"cell_type": "code", "execution_count": 7, "id": "b61407f9fe5a408c", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:39.460963Z", "start_time": "2025-06-18T07:24:39.456405Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"project_type\": \"multi_container\",\n", "  \"project_name\": \"tict<PERSON><PERSON>\",\n", "  \"description\": \"Tic-tac-toe game.\",\n", "  \"containers\": [\n", "    {\n", "      \"container_name\": \"tictactoe_frontend\",\n", "      \"platform\": \"web\",\n", "      \"framework\": \"React\",\n", "      \"description\": \"User interface for the tic-tac-toe game allowing players to interact with the game board and view game state.\",\n", "      \"interfaces\": \"HTTP REST API calls to backend for game actions and state updates.\",\n", "      \"relative_path\": \"tictactoe_frontend_workspace\",\n", "      \"dependencies\": [\n", "        \"tictactoe_backend\"\n", "      ],\n", "      \"container_type\": \"frontend\",\n", "      \"container_details\": {\n", "        \"features\": [\n", "          \"Render tic-tac-toe board\",\n", "          \"User login (optional)\",\n", "          \"Start new game\",\n", "          \"Display current game state\",\n", "          \"Make moves\",\n", "          \"Show winner or draw\",\n", "          \"Basic scoreboard\"\n", "        ],\n", "        \"colors\": {\n", "          \"primary\": \"#2196F3\",\n", "          \"secondary\": \"#8BC34A\",\n", "          \"accent\": \"#FF9800\",\n", "          \"background\": \"#FAFAFA\"\n", "        },\n", "        \"theme\": \"light\",\n", "        \"layout_description\": \"Centered board with top bar for navigation and status area; responsive for mobile and desktop.\",\n", "        \"style\": \"minimalistic\"\n", "      }\n", "    },\n", "    {\n", "      \"container_name\": \"tictactoe_backend\",\n", "      \"platform\": \"backend\",\n", "      \"framework\": \"Flask\",\n", "      \"description\": \"Handles game logic, player actions, and communicates with the database to store and retrieve game state.\",\n", "      \"interfaces\": \"REST API endpoints for game actions (new game, make move, get game state).\",\n", "      \"relative_path\": \"tictactoe_backend_workspace\",\n", "      \"dependencies\": [\n", "        \"tictactoe_database\"\n", "      ],\n", "      \"container_type\": \"backend\",\n", "      \"container_details\": {\n", "        \"features\": [\n", "          \"API endpoints for new game, moves, and state queries\",\n", "          \"Game logic validation\",\n", "          \"Persistence with database\",\n", "          \"Score tracking\"\n", "        ]\n", "      }\n", "    },\n", "    {\n", "      \"container_name\": \"tictactoe_database\",\n", "      \"platform\": \"database\",\n", "      \"framework\": \"PostgresQL\",\n", "      \"description\": \"Stores game states, player data, and scores.\",\n", "      \"interfaces\": \"SQL interface to backend for persistence operations.\",\n", "      \"relative_path\": \"tictactoe_database_workspace\",\n", "      \"dependencies\": [],\n", "      \"container_type\": \"database\",\n", "      \"container_details\": {\n", "        \"features\": [\n", "          \"Game records table\",\n", "          \"Player table (optional)\",\n", "          \"Scoreboard table\"\n", "        ]\n", "      }\n", "    }\n", "  ],\n", "  \"overview\": {\n", "    \"project_name\": \"tict<PERSON><PERSON>\",\n", "    \"description\": \"Tic-tac-toe game.\",\n", "    \"frontend_framework\": \"React\",\n", "    \"backend_framework\": \"Flask\",\n", "    \"database_framework\": \"PostgresQL\"\n", "  },\n", "  \"3rd_party_services\": [],\n", "  \"figma_components\": \"\",\n", "  \"manifest_path\": \"/tmp/sample_session/.project_manifest.yaml\"\n", "}\n"]}], "source": ["def create_project_work_item(project):\n", "    overview_data = {\n", "        \"project_name\": project.overview.project_name,\n", "        \"description\": project.overview.description,\n", "\n", "    }\n", "\n", "    for container in project.containers:\n", "        name: str = container.container_name\n", "        name.replace(\" \", \"_\")\n", "        container.workspace = name.replace(\" \", \"_\") + \"_workspace\"\n", "\n", "    frontends = project.get_containers_by_type(ContainerType.FRONTEND)\n", "    if frontends:\n", "        overview_data[\"frontend_framework\"] = frontends[0].framework\n", "\n", "    backends = project.get_containers_by_type(ContainerType.BACKEND)\n", "    if backends:\n", "        overview_data[\"backend_framework\"] = backends[0].framework\n", "\n", "    databases = project.get_containers_by_type(ContainerType.DATABASE)\n", "    if databases:\n", "        overview_data[\"database_framework\"] = databases[0].framework\n", "\n", "    mobiles = project.get_containers_by_type(ContainerType.MOBILE)\n", "    if mobiles:\n", "        overview_data[\"mobile_framework\"] = mobiles[0].framework\n", "\n", "    all_containers = []\n", "\n", "    # We can use a helper function to convert all containers to the work item format\n", "    for container in project.containers:\n", "        all_containers.append(container.to_work_item_format())\n", "\n", "    project_work_item = {\n", "        'project_type': 'multi_container',\n", "        'project_name': overview_data['project_name'],\n", "        'description': overview_data['description'],\n", "        'containers': all_containers,\n", "        'overview': overview_data,\n", "        '3rd_party_services': project.overview.third_party_services,\n", "        'figma_components': \"\",\n", "        'manifest_path': manifest_path # Mandatory for multi-project setup\n", "    }\n", "    return project_work_item\n", "\n", "work_item = create_project_work_item(project)\n", "print(json.dumps(work_item, indent=2))"]}, {"cell_type": "code", "execution_count": 8, "id": "35e14ad7ab91eaad", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:39.595502Z", "start_time": "2025-06-18T07:24:39.508226Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Internal model 'bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0' not found among available models. Using backup model.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Current directory is:  /home/<USER>/kavia/CodeGenerationAgent\n"]}], "source": ["# Starting code generation\n", "\n", "# This is not a fully functional reporter. See the QT app for reference.\n", "class MinimalReporter(TaskExecutionReporter):\n", "    \"\"\"Minimal reporter implementation for demonstration\"\"\"\n", "\n", "    def __init__(self, control):\n", "        self.control = control\n", "\n", "    def task_start_callback(self, task_description):\n", "        print(f\"Task started: {task_description}\")\n", "        self.control.stop() # This is for demo purpose just to interrupt the code generation\n", "\n", "    def progress_callback(self, status, total_tasks, latest_result, request_context):\n", "        print(f\"Progress: {status}\")\n", "\n", "    def terminal_output_callback(self, output):\n", "        print(f\"Terminal: {output}\")\n", "\n", "    def browser_output_callback(self, image):\n", "        print(\"Browser output received (image data)\")\n", "\n", "    def cost_update_callback(self, agent_costs, total_cost):\n", "        print(f\"Cost update: ${total_cost:.6f}\")\n", "\n", "    def user_query_callback(self, questions):\n", "        print(f\"User query: {questions}\")\n", "\n", "    def function_call_callback(self, call_description):\n", "        print(f\"Function call: {call_description.function_name}\")\n", "\n", "    def codeview_callback(self, code_data):\n", "        if isinstance(code_data, dict):\n", "            filename = code_data.get('filename', 'unknown')\n", "            print(f\"Code view: {filename}\")\n", "\n", "    def send_agent_message(self, message):\n", "        print(f\"Agent message: {message}\")\n", "\n", "    async def get_user_input(self, prompt):\n", "        # For demo purposes, return empty string or mock input\n", "        print(f\"User input requested: {prompt}\")\n", "        return \"Continue\"  # In real implementation, get actual user input\n", "\n", "    def send_code_panel_update(self, stream_id, content, metadata=None):\n", "        if metadata and metadata.get('type') == 'start':\n", "            print(f\"Code panel: Starting {metadata.get('file_path')}\")\n", "        elif metadata and metadata.get('type') == 'end':\n", "            print(f\"Code panel: Ending {metadata.get('file_path')}\")\n", "\n", "    def app_state_callback(self, url, state=None, container=None):\n", "        print(f\"App state: {container} -> {url} ({state})\")\n", "\n", "def setup_llm_and_chat(session_dir):\n", "    \"\"\"Setup LLM interface and chat\"\"\"\n", "\n", "    OPENAI_API_KEY = os.getenv(\"OPENAI_API_KEY\")\n", "    if not OPENAI_API_KEY:\n", "        raise ValueError(\"OPENAI_API_KEY environment variable is required\")\n", "\n", "    # Setup chat interface\n", "    chat = ChatInterface()\n", "    chat_worker = ChatEnabledWorker([], name=\"TaskExecutionAgent\")\n", "    chat.add_chat_user(chat_worker)\n", "\n", "    # Setup LLM interface\n", "    llm = LLMInterface(\n", "        llm_api_key=OPENAI_API_KEY,\n", "        session_dir=session_dir,\n", "        instance_name=\"TaskExecutionAgent\",\n", "        chat_worker=chat_worker\n", "    )\n", "\n", "    return llm, chat\n", "\n", "def setup_platform_config(agent_type=\"CodeGeneration\"):\n", "    \"\"\"Setup platform configuration for the agent\"\"\"\n", "    print(\"Current directory is: \", os.getcwd())\n", "    # Define agent configurations\n", "    AGENT_CONFIGS = {\n", "        \"DocumentCreation\": {\n", "            \"prompts_path\": \"src/code_generation_core_agent/agents/micro/prompts\"\n", "        },\n", "        \"CodeMaintenance\": {\n", "            \"prompts_path\": \"src/code_generation_core_agent/agents/micro/prompts\"\n", "        },\n", "        \"CodeGeneration\": {\n", "            \"prompts_path\": \"src/code_generation_core_agent/agents/micro/prompts\"\n", "        }\n", "    }\n", "\n", "    if agent_type not in AGENT_CONFIGS:\n", "        raise ValueError(f\"Unsupported agent type: {agent_type}\")\n", "\n", "    agents_config_info = AGENT_CONFIGS[agent_type]\n", "    prompts_base_path = agents_config_info[\"prompts_path\"]\n", "\n", "    # Setup platform loader\n", "    micro_base_path = os.path.dirname(prompts_base_path)\n", "    platform_loader = PlatformConfigLoader(micro_base_path)\n", "\n", "    selected_platform = ApplicationType.GENERIC.value\n", "    selected_framework = FrameworkType.DEFAULT.value\n", "\n", "    micro_agent_config = platform_loader.get_platform_config(\n", "        ApplicationType.from_string(selected_platform),\n", "        FrameworkType.from_string(selected_framework)\n", "    )\n", "\n", "    return micro_agent_config, prompts_base_path\n", "\n", "\n", "def create_task_execution_agent(llm, session_dir, micro_agent_config, prompts_base_path, work_item):\n", "    \"\"\"Create and configure the TaskExecutionAgent\"\"\"\n", "\n", "    model_name = os.getenv(\"MODEL_NAME\", config.get(\"LLM\", \"model\", fallback=\"gpt-4\"))\n", "\n", "    agent = TaskExecutionAgent(\n", "        llm=llm,\n", "        execution_base_path=session_dir,\n", "        micro_agents_config=micro_agent_config,\n", "        model_name=model_name,\n", "        prompts_base_path=prompts_base_path,\n", "        platform=work_item.get(\"platform\", ApplicationType.GENERIC.value),\n", "        framework=work_item.get(\"framework\", FrameworkType.DEFAULT.value)\n", "    )\n", "\n", "    return agent\n", "\n", "\n", "llm, chat = setup_llm_and_chat(session_dir)\n", "micro_agent_config, prompts_base_path = setup_platform_config(\"CodeGeneration\")\n", "\n", "# Create the agent\n", "agent = create_task_execution_agent(llm, session_dir, micro_agent_config, prompts_base_path, work_item)"]}, {"cell_type": "markdown", "id": "3dc33f9ee0e30abf", "metadata": {}, "source": ["# Execute work item"]}, {"cell_type": "code", "execution_count": null, "id": "c03d54ae56dbb908", "metadata": {"ExecuteTime": {"start_time": "2025-06-18T07:24:39.603486Z"}, "jupyter": {"is_executing": true}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:browsergym.core.env:Overriding the task's viewport parameter ({'width': 1280, 'height': 720} => {'width': 1200, 'height': 1600}). This might change the task's behaviour and difficulty.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["container_base_path /tmp/sample_session/tictactoe_frontend_workspace\n", "container_base_path /tmp/sample_session/tictactoe_backend_workspace\n", "container_base_path /tmp/sample_session/tictactoe_database_workspace\n", "Progress: Starting task execution process for CodeGeneration...\n", "Proxy server is not available. Using default chromium kwargs.\n", "Successfully loaded manifest from /tmp/sample_session/.project_manifest.yaml\n", "NoneType: None\n", "\n", "NoneType: None\n", "\n", "NoneType: None\n", "\n", "Successfully saved manifest to /tmp/sample_session/.project_manifest.yaml\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/kavia/CodeGenerationAgent/src/code_generation_core_agent/agents/preview/preview_manager.py\", line 298, in _run_container_thread\n", "    self._run_backend_container(container)\n", "  File \"/home/<USER>/kavia/CodeGenerationAgent/src/code_generation_core_agent/agents/preview/preview_manager.py\", line 410, in _run_backend_container\n", "    universal_project_setup.wait_for_project_setup(container.container_base_path, logger=self.logger)\n", "  File \"/home/<USER>/kavia/CodeGenerationAgent/src/code_generation_core_agent/agents/tools/init/universal_project_setup.py\", line 340, in wait_for_project_setup\n", "    raise FileNotFoundError(error_msg)\n", "FileNotFoundError: Cannot find .init-build-cmd file in /tmp/sample_session\n", "\n", "Cannot find .init-build-cmd file in /tmp/sample_session\n", "Project type ContainerType.FRONTEND\n", "Checking workspace /tmp/sample_session/tictactoe_frontend_workspace\n", "Project type ContainerType.BACKEND\n", "Checking workspace /tmp/sample_session/tictactoe_backend_workspace\n", "Project type ContainerType.DATABASE\n", "Checking workspace /tmp/sample_session/tictactoe_database_workspace\n", "Successfully saved manifest to /tmp/sample_session/.project_manifest.yaml\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/kavia/CodeGenerationAgent/src/code_generation_core_agent/agents/preview/preview_manager.py\", line 298, in _run_container_thread\n", "    self._run_backend_container(container)\n", "  File \"/home/<USER>/kavia/CodeGenerationAgent/src/code_generation_core_agent/agents/preview/preview_manager.py\", line 410, in _run_backend_container\n", "    universal_project_setup.wait_for_project_setup(container.container_base_path, logger=self.logger)\n", "  File \"/home/<USER>/kavia/CodeGenerationAgent/src/code_generation_core_agent/agents/tools/init/universal_project_setup.py\", line 340, in wait_for_project_setup\n", "    raise FileNotFoundError(error_msg)\n", "FileNotFoundError: Cannot find .init-build-cmd file in /tmp/sample_session\n", "\n", "Cannot find .init-build-cmd file in /tmp/sample_session\n", "Task started: Starting task: Hello! 😊 We're working on building a Tic-tac-toe game with a React frontend, Flask backend, and PostgreSQL database. What would you like to do next?\n", "\n", "- Write all the code for this\n", "- Create a plan\n", "- Build a test suite\n", "- Run the tests\n", "- Perform visual verification\n", "- Answer questions about the codebase / architecture\n", "- Help\n", "\n", "Please choose an option or let me know your next step! with agent user\n"]}], "source": ["async def execute_process_work_item(agent, work_item, session_dir, chat):\n", "    \"\"\"Execute the process_work_item method\"\"\"\n", "\n", "    # Create minimal reporter\n", "\n", "    # Create minimal control (optional - for pause/stop functionality)\n", "    class MinimalControl:\n", "        def __init__(self):\n", "            self.should_stop = False\n", "            self.is_paused = False\n", "\n", "        def check_status(self):\n", "            if self.should_stop:\n", "                raise StopIteration(\"Process stopped\")\n", "            # Handle pause logic if needed\n", "\n", "        def stop(self):\n", "            self.should_stop = True\n", "\n", "        def pause(self):\n", "            self.is_paused = True\n", "\n", "        def resume(self):\n", "            self.is_paused = False\n", "\n", "    control = MinimalControl()\n", "\n", "    reporter = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(control)\n", "\n", "    # Execute process_work_item\n", "    try:\n", "        result = await agent.process_work_item(\n", "            \"CodeGeneration\",  # or \"CodeMaintenance\", \"DocumentCreation\"\n", "            work_item,\n", "            use_retriever=False,  # Set to True if you want to use retrieval\n", "            control=control,\n", "            status_reporter=reporter,\n", "            previous_context=\"\",  # Empty for new execution, or pass previous context\n", "            chat_interface=chat,\n", "            git_links=work_item.get('github_links', [])  # Optional git links\n", "        )\n", "\n", "        print(f\"Process completed successfully!\")\n", "        print(f\"Result context length: {len(result) if result else 0}\")\n", "        return result\n", "\n", "    except Exception as e:\n", "        print(f\"Error during process_work_item execution: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        return None\n", "\n", "# Execute the process\n", "result = await execute_process_work_item(agent, work_item, session_dir, chat)"]}, {"cell_type": "code", "execution_count": null, "id": "68d32a7e4acd3acf", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:22.269371159Z", "start_time": "2025-06-18T07:22:56.793401Z"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5bafb6abacdf2401", "metadata": {"ExecuteTime": {"end_time": "2025-06-18T07:24:22.269599198Z", "start_time": "2025-06-18T07:22:56.847540Z"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}