[build-system]
requires = [ "poetry-core",]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "code_generation_core_agent"
version = "0.0.452"
description = "Code generation agent core"
authors = [ "Kavia.ai <<EMAIL>>",]
include = [ "config.default.ini",]
[[tool.poetry.packages]]
include = "code_generation_core_agent"
from = "src"

[[tool.poetry.packages]]
include = "dnparsers"
from = "src"

[tool.poetry.dependencies]
python = "^3.11"
selenium = "^4.25.0"
pillow = "^11.0.0"
GitPython = "^3.1.43"
python-dotenv = "^1.0.1"
pyyaml = "^6.0.2"
numpy = "^2.1.2"
pydantic = "^2.9.2"
pydantic-settings = "^2.6.0"
py2neo = "^2021.2.4"
docker = "^7.1.0"
pytest = "^8.3.3"
pytest-asyncio = "^0.24.0"
toml = "^0.10.2"
termcolor = "^2.5.0"
python-docx = "^1.1.2"
PyPDF2 = "^3.0.1"
python-pptx = "^1.0.2"
pylatexenc = "^2.10"
grep-ast = "^0.3.3"
tornado = "^6.4.1"
billiard = "^4.2.1"
pydevd = "^3.1.0"
html2text = "^2024.2.26"
browsergym = "^0.13.3"
playwright = "^1.39.0"
colorama = "^0.4.6"
rich = "^13.9.3"
datadog-api-client = "^2.29.0"
scikit-learn = "^1.5.2"
json5 = "^0.9.25"
pyperclip = "^1.9.0"
prompt_toolkit = "^3.0.48"
pydub = "^0.25.1"
importlib_resources = "^6.4.5"
psutil = "^6.1.0"
pypandoc = "^1.14"
diskcache = "^5.6.3"
diff_match_patch = "*"
backoff = "^2.2.1"
pexpect = "^4.9.0"
rapidfuzz = "^3.13.0"
langtrace-python-sdk = "^3.0.1"
boto3 = "^1.35.76"
flake8 = "^7.1.1"
nest_asyncio = "1.6.0"
python-Levenshtein = "^0.26"
pymilvus = "^2.5.0"
kubernetes = "^32.0.1"
firecrawl = "^2.5.4"
fastapi = "0.110.1"
flask_socketio = "*"
pypdf = "^5.0.0"
pdfplumber = "0.11.0"
pandas = "*"
openpyxl = "^3.1.5"
xlrd = "^2.0.1"
doc2docx = "^0.2.4"
striprtf = "^0.0.26"
odfpy = "^1.4.1"
beautifulsoup4 = "^4.12.0"
lxml = "^5.0.0"
PyMuPDF = "^1.24.0"
litellm = "1.75.8"
supabase = "2.15.3"
psycopg2-binary = "2.9.10"
pdfminer = "20191125"
universalinit-env = "^0.1.0"
tokenizers = "0.19.1"
openai = "1.99.6"
