
from code_generation_core_agent.project_schemas import ProjectSchema
from code_generation_core_agent.services.supabase_service import SupabaseService


def setup_3rd_party_services(project: ProjectSchema, logger=None):
    """
    Get list of 3rd party services
    :param service_name:
    :param project:
    :return:
    """
    return [SupabaseService(project, logger)]


def init_3rd_party_services(project: ProjectSchema, logger=None):
    """
    Init 3rd party services
    :param project:
    :return:
    """

    third_party_services = setup_3rd_party_services(project, logger)
    initialized_services = []
    for requested_service_names in project.overview.third_party_services:
        for available_service in third_party_services:
            if available_service.get_service_name() == requested_service_names:
                available_service.setup()
                initialized_services.append(available_service)

    return initialized_services