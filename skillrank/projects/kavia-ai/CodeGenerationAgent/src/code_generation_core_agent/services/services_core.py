from abc import abstractmethod, ABC
from code_generation_core_agent.project_schemas import ProjectSchema

class ServicesCore(ABC):

    @abstractmethod
    def __init__(self, project: ProjectSchema):
        self.project = project

    @abstractmethod
    def get_env_list(self) -> dict:
        """
        Get environment variables specific for the service
        :return:
        """
        return {}

    def setup(self) -> bool:
        """
        Perform any setup required for the service
        :return:
        """
        pass

    def get_service_name(self) -> str:
        return "ServicesCore"