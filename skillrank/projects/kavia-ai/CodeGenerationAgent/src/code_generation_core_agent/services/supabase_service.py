from code_generation_core_agent.agents.tools.supabase_tool import SupabaseTool, setup_supbase_project, supabase_connect
from code_generation_core_agent.project_schemas import ProjectSchema
from code_generation_core_agent.services.services_core import ServicesCore
from typing_extensions import override
import os

class SupabaseService(ServicesCore):

    def __init__(self, project : ProjectSchema, logger=None):

        self.logger = logger
        
        super().__init__(project)

        env = project.overview.env

        self.supabase_url = env.get("APP_SUPABASE_URL") or env.get("SUPABASE_URL", "")
        self.supabase_key = env.get("APP_SUPABASE_KEY") or env.get("SUPABASE_KEY", "")
        self.supabase_db_url = env.get("APP_SUPABASE_DB_URL") or env.get("SUPABASE_DB_URL", "")


        if self.logger:
            self.logger.info(f"SupabaseService initialized with URL: {self.supabase_url}")



    @override
    def get_env_list(self) -> dict:
        return {
            "SUPABASE_URL": self.supabase_url,
            "SUPABASE_KEY": self.supabase_key,
            "SUPABASE_DB_URL" : self.supabase_db_url
        }

    @override
    def setup(self) -> bool:
        """
        Perform any setup required for the service
        :return:
        """
        if self.logger:
            self.logger.info("Setting up Supabase project...")

        if self.supabase_url and self.supabase_key :
            result = supabase_connect(
                supabase_key=self.supabase_key,
                supabase_url=self.supabase_url,
                supabase_db_url=self.supabase_db_url
            )
            self.logger.info(f"Supabase init run with pre-defined URL and key.. Result: '${str(result)}'")

        else:
            if self.logger:
                self.logger.error("Supabase URL or key is not set. Cannot setup Supabase project (will try after user input).")
            return True
        return True

    @override
    def get_service_name(self) -> str:
        return "Supabase"