import asyncio
import concurrent
import copy
import enum
import json
import os
import threading
from typing import List, Dict, Any, Optional
import re

from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutionException, \
    DockerShellCallback
from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
from code_generation_core_agent.chat.cga_chat import CGAChatSystem
from code_generation_core_agent.config import config
from code_generation_core_agent.llm.chat_interface import MessageType


class State(enum.Enum):
    RUNNING = "RUNNING"
    STOPPED = "STOPPED"
    PAUSED = "PAUSED"
    ERROR = "ERROR"

def run_async_in_thread(async_func, *args, **kwargs):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        return loop.run_until_complete(async_func(*args, **kwargs))
    finally:
        loop.close()

class ServiceStatus:
    def __init__(self, status: State, message: str):
        self.status = status
        self.message = message


class BackendValidationError(Exception):
    """Raised when backend implementation validation fails"""
    pass


class ServiceExtractor:
    _instance = None
    _lock = threading.Lock()
    work_dir = None
    executor = None
    chat = None

    @classmethod
    def get_instance_or_none(cls):
        with cls._lock:
            return cls._instance

    def __new__(cls, logger=None):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ServiceExtractor, cls).__new__(cls)
                cls._instance._services = None
                cls._instance.logger = logger
            return cls._instance

    def get_service_status(self):
        """ Return status of all services as a dictionary """
        if not self._services:
            return {}
        
        return {service['folder']: service.get('status', State.STOPPED.value) 
                for service in self._services}

    def start_service(self, service_name):
        """
        Install and start a backend service by name.

        Args:
            service_name: Name of the service to start
        """
        if self.work_dir is None:
            raise ValueError("Base path not set")

        if not self._services:
            raise ValueError("No services available to start")

        service = next((s for s in self._services if s['folder'] == service_name), None)

        if not service:
            raise ValueError(f"Service {service_name} not found")

        if service.get('status') == State.RUNNING.value:
            self.logger.info(f"Service {service_name} is already running")
            return

        try:
            self._message_to_user(self.chat, f"Installing and starting backend service: {service_name}")

            service_directory = os.path.join(self.work_dir, service['folder'])
            self.logger.info(f"[BSE] Setting up service: {service_name}")

            install_commands = service['install_commands'].split('\n')
            for command in install_commands:
                command = f"cd {service_directory} && {command}"
                result, success = self.executor.run_one_shot_command(command, timeout=100)

                if success != 0:
                    self._message_to_user(self.chat, f"Error installing backend service: {result}")
                    service['status'] = State.ERROR.value
                    return
                else:
                    self._message_to_user(self.chat, f"Backend service installed successfully")

            startup_command = service['startup_command']
            startup_command = f"cd {service_directory} && {startup_command}"

            service['callback'] = self.BackendServiceCallback(service)
            process = self.start_long_running_process(startup_command, self.executor, service['callback'])
            service['process'] = process

            self._message_to_user(self.chat, f"Backend service started successfully")

            self.wait_for_open_ports(service, self.executor, self.chat)
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            self.logger.error(f"[BSE] Error installing or starting backend service: {str(e)}")
            self._message_to_user(self.chat, f"Error installing or starting backend service: {str(e)}")
            service['status'] = State.ERROR.value

    class BackendServiceCallback(DockerShellCallback):
        def __init__(self, service):
            self.service = service
            self.output_buffer = []
            self.max_lines = 80

        def on_output(self, output):
            # Decode bytes to string and split into lines
            if isinstance(output, bytes):
                output = output.decode('utf-8', errors='replace')
            lines = output.split('\n')
            self.output_buffer.extend(lines)
            # Keep only the last max_lines
            if len(self.output_buffer) > self.max_lines:
                self.output_buffer = self.output_buffer[-self.max_lines:]

        def on_exit(self, error):
            self.service['status'] = State.ERROR.value
            # Join the buffered lines and send to user
            last_output = '\n'.join(self.output_buffer)
            message = f"Service {self.service['folder']} exited with error. Last output:\n{last_output}"
            
            # Get the instance and call the method properly
            service_extractor = ServiceExtractor.get_instance_or_none()
            if service_extractor and service_extractor.chat:
                service_extractor._message_to_user(service_extractor.chat, message)

    def __init__(self, logger):
        self._services = None
        self.logger = logger
    def _message_to_user(self, chat: CGAChatSystem, message: str):
        id, result = chat._send_message_to_user(message,
                                                     needs_response=False,
                                                     blocking=False,
                                                     error=False,
                                                     parent_id=None,
                                                     message_type=MessageType.BACKEND_SERVICE)

    def validate_backend_implementation(self, implementation: Dict[str, Any]) -> None:
        """
        Validate that a backend implementation contains only the allowed fields with proper formats.

        Args:
            implementation: Dictionary containing backend implementation details

        Raises:
            BackendValidationError: If validation fails
        """
        allowed_fields = {
            'install_commands': str,
            'startup_command': str,
            'tcp_ports': str,
            'ci_lint_command': str,
            'folder': str
        }

        # Check for required fields
        required_fields = ['install_commands', 'startup_command', 'tcp_ports', 'folder']
        for field in required_fields:
            if field not in implementation:
                raise BackendValidationError(f"Missing required field: {field}")

        for field in implementation:
            if field not in allowed_fields:
                raise BackendValidationError(f"Unknown field: {field}")

        # Convert to JSON
        # ports = {"3000/tcp": 3000, "8088/tcp": 8088, "8089/tcp": 8089, "5900/tcp": 5900, "5002/tcp": 5001}

        port_config = config.get('CONTAINER', 'ports')
        container_ports = json.loads(port_config)
        configured_ports = {}

        for field, field_type in allowed_fields.items():
            if field in implementation and not isinstance(implementation[field], field_type):
                raise BackendValidationError(f"Field {field} must be of type {field_type.__name__}")

            try:
                if implementation['tcp_ports'].strip():
                    port_strings = implementation['tcp_ports'].split(',')
                    for port_str in port_strings:
                        port = int(port_str.strip())
                        if f"{port}/tcp" in container_ports.keys():
                            raise BackendValidationError(f"TCP port {port} is not allowed for this service. Used by another service")

                        if port in container_ports.values():
                            raise BackendValidationError(f"TCP port {port} is already in use. Used by another service")

                        if port < 1 or port > 65535:
                            raise BackendValidationError(f"TCP port {port} is out of valid range (1-65535)")
                        configured_ports[port_str.strip() + '/tcp'] = int(port)

                else:
                    raise BackendValidationError("TCP ports cannot be empty")
            except ValueError:
                raise BackendValidationError("TCP ports must be a comma-separated list of integers")
        config.set_runtime_config('CONTAINER', 'ports', json.dumps(configured_ports))
        # Validate folder path (basic check)
        if 'folder' in implementation and not implementation['folder'].strip():
            raise BackendValidationError("Folder path cannot be empty")

    def extract_backend_implementations(self, work_item: Dict[str, Any],
                                        executor: ExecutorBase,
                                        chat: CGAChatSystem,
                                        work_dir: str) -> List[Dict[str, Any]]:
        """
        Extract and validate backend implementations from a work item.

        Args:
            work_item: Dictionary containing work item details
            executor: Executor instance
            chat: Chat instance for sending messages
            work_dir: Base work directory

        Returns:
            List of validated backend implementation dictionaries

        Raises:
            BackendValidationError: If validation fails for any implementation
        """
        if 'backend_implementations' not in work_item:
            return []

        backend_implementations = copy.deepcopy(work_item['backend_implementations'])
        validated_implementations = []
        invalid_indexes = []

        for i, implementation in enumerate(backend_implementations):
            try:
                self.validate_backend_implementation(implementation)

                if not self.check_service_directory_exists(work_dir, implementation):
                    self._message_to_user(chat, f"Backend service directory not found: {implementation['folder']}")
                    invalid_indexes.append(i)
                    continue

                # Initialize status field
                implementation['status'] = State.STOPPED.value
                validated_implementations.append(implementation)
                self._message_to_user(chat, f"Added backend implementation from directory {implementation['folder']}")

            except BackendValidationError as e:
                self._message_to_user(chat, f"Backend implementation #{i + 1} validation failed: {str(e)}")
                invalid_indexes.append(i)

        if invalid_indexes and 'backend_implementations' in work_item:
            for idx in sorted(invalid_indexes, reverse=True):
                removed_impl = work_item['backend_implementations'].pop(idx)
                self._message_to_user(chat,
                                      f"Ignoring invalid backend implementation. Directory or configuration is missing: {removed_impl.get('folder', 'unknown')}")
        return validated_implementations



    def start_long_running_process(self, command, executor, callback):
        future = shared_loop.run_coroutine(executor.create_subprocess_shell(command, callback=callback))
        return future

    def check_service_directory_exists(self, work_dir: str, service: Dict[str, Any]) -> bool:
        """
        Check if the service directory exists.

        Args:
            work_dir: Base work directory
            service: Dictionary containing service details

        Returns:
            bool: True if directory exists, False otherwise
        """
        service_directory = os.path.join(work_dir, service['folder'])
        return os.path.isdir(service_directory)

    def _install_and_startup(self, work_dir: str, executor: ExecutorBase, chat: CGAChatSystem):
        """
        Install and start a backend service.

        Args:
            service: Dictionary containing backend service details
            executor: Executor instance for executing commands
            chat: Chat instance for sending messages
        """
        for service in self._services:
            try:
                self._message_to_user(chat, f"Installing backend service from directory {service['folder']}")

                service_directory = os.path.join(work_dir, service['folder'])
                self.logger.info("[BSE] Setting up service: ", service)
                install_command = service['install_commands'].split('\n')

                for command in install_command:
                    command = f"cd {service_directory} && {command}"
                    result, success = executor.run_one_shot_command(command, timeout=300)

                    if success != 0:
                        self._message_to_user(chat, f"Error installing backend service: {result}")
                        service['status'] = State.ERROR.value
                        return
                    else:
                        self._message_to_user(chat, f"Backend service installed successfully")

                    # Start the service

                startup_command = service['startup_command']
                startup_command = f"cd {service_directory} && {startup_command}"

                service['callback'] = self.BackendServiceCallback(service)
                process = self.start_long_running_process(startup_command, executor, service['callback'])
                service['process'] = process

                self._message_to_user(chat, f"Backend service started successfully")
                self.wait_for_open_ports(service, executor, chat)
            except Exception as e:
                import traceback
                print(traceback.format_exc())
                self.logger.error(f"[BSE] Error installing or starting backend service: {str(e)}")
                self._message_to_user(chat, f"Error installing or starting backend service: {str(e)}")
                service['status'] = State.ERROR.value

    def _get_service_by_folder(self, folder: str) -> Optional[Dict[str, Any]]:
        return next((s for s in self._services if s['folder'] == folder), None)

    def process_work_item(self, work_item: Dict[str, Any], base_path: str, executor: ExecutorBase,
                          chat: CGAChatSystem) -> Dict[str, Any]:
        """
        Process a work item and extract validated backend implementations.

        Args:
            work_item: Dictionary containing work item details
            base_path: Base path for the work item
            executor: Executor instance for executing commands
            chat: Chat instance for sending messages

        Returns:
            Modified work item with backend implementations processed

        Raises:
            BackendValidationError: If backend validation fails
        """

        # Assert executor is not running
        assert not executor.is_running()

        self.executor = executor
        self.work_dir = base_path
        self.chat = chat

        try:

            self._services = self.extract_backend_implementations(work_item, executor, chat, base_path)

            self.logger.info(f"[BSE] Extracted {len(self._services)} validated backend implementations")

            if not self._services:
                return work_item

            # Start the services
            self._install_and_startup(base_path, executor, chat)

            if 'backend_implementations' in work_item:
                for service in work_item['backend_implementations']:
                    if 'install_commands' in service:
                        del service['install_commands']

                    service['status'] = self._get_service_by_folder(service['folder'])['status']
                    service['note_for_agents'] = "This service is managed by the user. Do not attempt to install or start it manually."
            return work_item

        except BackendValidationError as e:
            self.logger.error(f"[BSE] Error processing work item: {str(e)}")
            self._message_to_user(chat, f"[BSE]: error extracting backend implementations.")
            raise e

    def wait_for_open_ports(self, service, executor, chat):
        """
        Wait for the specified TCP ports to be open after starting a service.

        Args:
            service: Dictionary containing service details, including tcp_ports
            executor: ExecutorBase instance for executing commands
            chat: CGAChatSystem instance for sending messages
        """
        if not service['tcp_ports'].strip():
            service['status'] = State.RUNNING.value
            return

        ports = [port.strip() for port in service['tcp_ports'].split(',')]
        max_attempts = 6

        for port in ports:
            self.logger.info(f"[BSE] Waiting for service -{service['folder']}- port {port} to open")
            self._message_to_user(chat, f"Waiting for port {port} to open...")

            for attempt in range(max_attempts):
                # Use netstat to check if the port is listening

                check_command = f"netstat -tuln | grep ':{port} '"
                try:
                    result, exit_code = executor.run_one_shot_command(check_command, timeout=5)
                except DockerExecutionException as e:
                    exit_code = 1

                if exit_code == 0:
                    self._message_to_user(chat, f"Port {port} is now open")
                    self.logger.info(f"[BSE] Port {port} is now open")
                    service['status'] = State.RUNNING.value
                    break

                if attempt == max_attempts - 1:
                    self._message_to_user(chat, f"Warning: Port {port} might not be open after waiting.")
                    service['status'] = State.ERROR.value
                import time
                time.sleep(1)
