import re
import logging


class LogSanitizer(logging.Filter):
    """
    Log sanitizer that redacts sensitive information.
    - Matches keys as single words (word boundary on both sides), not as part of a longer word or phrase.
    - Handles both key=value and JSON-style "key": "value" patterns.
    - Only the message string is sanitized, not the arguments tuple/dict, to preserve logging formatting.
    - Value can include tabs and newlines, but not spaces.
    - This sanitizer is line-based and does not parse nested JSON structures.
    - Note: Some false positives (like "Cache key") are acceptable to ensure no secrets are missed.
    """

    SENSITIVE_KEYWORDS = (
        'KEY', 'TOKEN', 'SECRET', 'PASSWORD', 'PASS', 'PWD', 'AUTH', 'CRED', 'API'
    )

    def __init__(self, replacement='***REDACTED***'):
        super().__init__()
        self.replacement = replacement
        
        # Pattern to exclude usage statistics
        self.usage_pattern = re.compile(
            r'(?i)(Prompt\s+Tokens|Completion\s+Tokens|Tokens)\s*[:=]\s*[\d,]+',
            re.IGNORECASE | re.MULTILINE
        )
        
        # Main pattern: single-word key ending with sensitive keyword, word boundary on both sides
        # Exclude common usage patterns like "Prompt Tokens", "Completion Tokens", "Tokens"
        self.pattern = re.compile(
            r'(?i)'
            r'\b([A-Z0-9_]*(' + '|'.join(self.SENSITIVE_KEYWORDS) + r')[A-Z0-9_]*)\b'  # Key
            r'(\s*["\']?\s*[:=]\s*["\']?)'  # Separator
            r'([^ \s"\'}\],\n\t]+)'  # Value (no spaces, but tabs/newlines allowed)
            r'(["\']?)',
            re.IGNORECASE | re.MULTILINE
        )
        # JSON-style pattern: "key": "value"
        self.json_pattern = re.compile(
            r'(?i)"([A-Z0-9_]*(' + '|'.join(self.SENSITIVE_KEYWORDS) + r')[A-Z0-9_]*)"\s*:\s*"([^\"]+)"',
            re.IGNORECASE | re.MULTILINE
        )
        
        # Pattern for name-value pairs where the key is in "name" field and value is in "value" field
        self.name_value_patterns = [
            # Standard format: "name": "KEY", "value": "secret"
            re.compile(
                r'(?i)"name"\s*:\s*"([A-Z0-9_]*(' + '|'.join(self.SENSITIVE_KEYWORDS) + r')[A-Z0-9_]*)"\s*[,}]\s*"value"\s*:\s*"([^\"]+)"',
                re.IGNORECASE | re.MULTILINE
            ),
            # Reversed format: "value": "secret", "name": "KEY"
            re.compile(
                r'(?i)"value"\s*:\s*"([^\"]+)"\s*[,}]\s*"name"\s*:\s*"([A-Z0-9_]*(' + '|'.join(self.SENSITIVE_KEYWORDS) + r')[A-Z0-9_]*)"',
                re.IGNORECASE | re.MULTILINE
            ),
            # With extra whitespace and different separators
            re.compile(
                r'(?i)"name"\s*:\s*"([A-Z0-9_]*(' + '|'.join(self.SENSITIVE_KEYWORDS) + r')[A-Z0-9_]*)"\s*[,}]\s*"value"\s*:\s*"([^\"]*)"',
                re.IGNORECASE | re.MULTILINE
            ),
        ]

    def filter(self, record):
        """Filter and sanitize log records."""
        if hasattr(record, 'msg'):
            record.msg = self._sanitize(str(record.msg))
        # Do NOT touch record.args at all
        return True

    def _sanitize(self, text):
        """Sanitize a text string by redacting sensitive values."""
        if not text:
            return text
            
        # First, protect usage statistics from being redacted
        protected_text = self.usage_pattern.sub(lambda m: f"PROTECTED_{hash(m.group(0))}", text)
        
        def repl(m):
            key = m.group(1)
            return f"{key}: {self.replacement}"
        sanitized_text = self.pattern.sub(repl, protected_text)
        
        # JSON-style
        def json_repl(m):
            key = m.group(1)
            return f'"{key}": "{self.replacement}"'
        sanitized_text = self.json_pattern.sub(json_repl, sanitized_text)
        
        # Name-value pairs (like {"name": "OPENAI_API_KEY", "value": "sk-proj-..."})
        for pattern in self.name_value_patterns:
            sanitized_text = pattern.sub(lambda m: f'"name": "{m.group(1)}", "value": "{self.replacement}"', sanitized_text)
            
        # Restore protected usage statistics
        for match in self.usage_pattern.finditer(text):
            placeholder = f"PROTECTED_{hash(match.group(0))}"
            sanitized_text = sanitized_text.replace(placeholder, match.group(0))
            
        return sanitized_text


class SanitizingStreamHandler(logging.StreamHandler):
    """Custom handler that sanitizes the fully formatted message."""

    def __init__(self, stream=None, replacement='***REDACTED***'):
        super().__init__(stream)
        self.sanitizer = LogSanitizer(replacement)

    def emit(self, record):
        """Emit a record with sanitized message."""
        # Format the message first
        msg = self.format(record)
        # Then sanitize the fully formatted message
        sanitized_msg = self.sanitizer._sanitize(msg)
        # Create a new record with the sanitized message
        new_record = logging.LogRecord(
            name=record.name,
            level=record.levelno,
            pathname=record.pathname,
            lineno=record.lineno,
            msg=sanitized_msg,
            args=(),
            exc_info=record.exc_info,
            func=record.funcName
        )
        super().emit(new_record)


def add_sanitizer(logger, replacement='***REDACTED***'):
    """Add sanitizer to an existing logger."""
    sanitizer = LogSanitizer(replacement)
    logger.addFilter(sanitizer)
    return logger


def add_sanitizing_handler(logger, replacement='***REDACTED***'):
    """Add a sanitizing handler that processes the fully formatted message."""
    handler = SanitizingStreamHandler(replacement=replacement)
    formatter = logging.Formatter('%(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    return logger


if __name__ == '__main__':
    # Setup logger with sanitizer
    logger = logging.getLogger('test')
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

    # Add sanitizer
    add_sanitizer(logger)

    # Test cases
    test_cases = [
        'Connected with GEMINI_API_KEY=sk-1234567890abcdef',
        'AWS_SECRET_ACCESS_KEY: "aws-secret-key-value-here"',
        'Settings: {"api_key": "my-secret-key", "user": "john"}',
        'DB_PASSWORD = mysecretpass123',
        "auth_token: 'bearer-token-12345'",
        'export OPENAI_API_KEY="sk-proj-abcdef123456"',
        'Config: API_KEY=12345, USER=admin, DEBUG=true',
        'Normal log without secrets',
        'STRIPE_SECRET_KEY:sk_live_1234567890',
        "MY_CUSTOM_SECRET_KEY = 'super-secret-value'",
    ]

    print("=== Log Sanitizer Test ===\n")
    for test in test_cases:
        logger.info(test) 