# --------------------------------------------------------------------------------
# Company Name: Kavia AI
# Author: <PERSON><PERSON><PERSON>
# Creation Date: Year (2024)
#
# Confidential Information of Kavia AI
# NOTICE: All information contained herein is, and remains the property of Kavia AI.
# The intellectual and technical concepts contained herein are proprietary to Kavia AI
# and may be covered by U.S. and Foreign Patents, patents in process, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material is strictly forbidden unless prior written permission is obtained
# from Kavia AI. Access to this file is granted on the condition that it will not be used for any purpose other than as specifically authorized
# in writing by Kavia AI, and subject to the terms of any agreement governing such access and use. Access to this file may also require
# a signed Non-Disclosure Agreement.
#
# --------------------------------------------------------------------------------

from py2neo import Graph, Node, Relationship
import configparser
import os
import asyncio
import time
from datetime import datetime
from typing import Any, Dict, List
import csv
import os
import json


# Define the path to the CSV file
LOG_FILE_PATH = 'query_log.csv'

class NodeDB:
    def __init__(self,uri,user,password):
        
        self.uri = uri
        self.user = user
        self.password = password

        self.graph = Graph(uri ,auth=(user, password))

        self.node_type_mapping = {
            "Project": {
                "api_fields": ["Title", "Description"],
                "db_fields": ["ProjectName", "ProjectDescription"]
            },
            # Add more mappings as needed
        }
        if not os.path.exists(LOG_FILE_PATH):
            with open(LOG_FILE_PATH, mode='w', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(['Query', 'TimeTaken'])

    async def async_run(self, query, **params):
        retries = 3  # Number of retries
        base_delay = 1  # Base delay in seconds
        start_time = time.time()
        for attempt in range(retries):
            try:
                result = await asyncio.to_thread(self.graph.run, query, **params)
                end_time = time.time()
                with open(LOG_FILE_PATH, mode='a', newline='') as file:
                    writer = csv.writer(file)
                    writer.writerow([query, end_time - start_time])
                
                return result  # Return the result on successful execution
            except Exception as e:
                print(f"Error running query on attempt {attempt+1}: {e}")
                
                if "Cannot connect" in str(e):  # Broaden the condition slightly
                    print("Re-initializing graph connection")
                    self.graph = Graph(self.uri, auth=(self.user, self.password))
                    
                elif attempt < retries - 1:
                    sleep_time = base_delay * (2 ** attempt)  # Exponential backoff
                    print(f"Retrying in {sleep_time} seconds...")
                    await asyncio.sleep(sleep_time)
                else:
                    print("All retry attempts failed.")
                    raise e  # Optionally re-raise the last exception after all retries fail

        return None  # You can decide what to return if all retries fail, if anything at all



    

    async def get_all_relationships(self, root_node_id, relationship_type, node_type=None, levels=1):
        """
        Retrieves all relationships of a given type starting from a root node, up to the specified number of levels.

        :param root_node_id: The ID of the root node.
        :param relationship_type: The type of the relationship to retrieve.
        :param node_type: The type of the nodes to retrieve (optional).
        :param levels: The maximum number of levels to traverse.
        :return: A list of all related nodes and their relationships.
        """
        if node_type:
            node_type_string = f":{node_type}"
        else:
            node_type_string = ""

        # Using parameterization for root_node_id and constructing the query safely for levels and relationship_type
        query = f"""
            MATCH (p) WHERE ID(p) = $root_node_id
            MATCH (p)-[:HAS_CHILD*1..{levels}]->(a1{node_type_string})-[r:{relationship_type}]-(a2{node_type_string})
            RETURN ID(a1) AS StartNodeID, type(r) AS type, properties(r) as properties, ID(a2) AS EndNodeID
        """
        try:
            query_result = await self.async_run(query, root_node_id=root_node_id)
            return query_result.data()
        except Exception as e:
            print(f"Failed to execute query: {e}")
            return []
        
    async def get_relationships_involving_node(self, node_id, relationship_type):
        """
        Retrieves all relationships of a given type where the specified node is either a source or a target.

        :param node_id: The ID of the node involved in the relationships.
        :param relationship_type: The type of the relationship to retrieve.
        :return: A list of relationships involving the specified node.
        """

        # Parameterized query to find all relationships where node_id is either source or target
        query = f"""
            MATCH (a1)-[r:{relationship_type}]->(a2)
            WHERE ID(a1) = $node_id OR ID(a2) = $node_id
            RETURN ID(a1) AS source, type(r) AS type, properties(r) as properties, ID(a2) AS target
        """
        try:
            query_result = await self.async_run(query, node_id=node_id)
            return query_result.data()
        except Exception as e:
            print(f"Failed to execute query: {e}")
            return []


    async def get_relationship(self, source, target, relationship_type=None):
        if relationship_type is None:
            retationship_type_string = ""
        else:
            retationship_type_string = f":{relationship_type}"
        query = f"""
        MATCH (n)-[r{retationship_type_string}]->(m)
        WHERE ID(n) = $source AND ID(m) = $target
        RETURN ID(n) AS source, ID(m) AS target, type(r) AS type, properties(r) AS properties
        """

        query_result = await self.async_run(query, source=source, target=target)

        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
        else:
            return None


    async def relationship_exists(self, start_node_id, end_node_id, relationship_type):
        """
        Checks if a relationship of a given type exists between two nodes.

        :param start_node_id: The ID of the starting node.
        :param end_node_id: The ID of the ending node.
        :param relationship_type: The type of the relationship to check for.
        :return: True if the relationship exists, False otherwise.
        """
        query = f"""
        MATCH (startNode)-[r:{relationship_type}]->(endNode)
        WHERE ID(startNode) = $start_node_id AND ID(endNode) = $end_node_id
        RETURN COUNT(r) > 0 AS relationshipExists
        """
        query_result = await self.async_run(query, start_node_id=start_node_id, end_node_id=end_node_id)
        result = query_result.data()

        # Assuming the query always returns a result, and the 'relationshipExists' key exists in the result.
        return result[0]['relationshipExists']
 
    # create a db node with api_data as a dictionary for attributes
    async def create_node(self, node_types, properties, parent_id=None):
        labels = ":".join(node_types)  # Prepare the labels string
        print(labels)
        # Dynamically construct the SET part of the query to set properties as attributes
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"CREATE (n:{labels}) SET {set_clauses} RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, **properties)

        result = query_result.data()[0]

        # Create the relationship if a parent_id is provided
        if parent_id is not None:
            await self.create_relationship(parent_id, result['id'], "HAS_CHILD")

        return result
    
    async def add_label_to_node(self, node_id, new_label):
        # This query fetches the existing labels of the node and adds the new label if it is not already there
        query = f"MATCH (n) WHERE ID(n) = $node_id RETURN LABELS(n) AS existing_labels"
        query_result = await self.async_run(query, node_id=node_id)
        labels = query_result.data()[0]['existing_labels']

        # Check if the new label is already part of the node's labels
        if new_label not in labels:
            updated_labels = ":".join(labels + [new_label])
            # Update the node with the new label added
            update_query = f"MATCH (n) WHERE ID(n) = $node_id SET n:{updated_labels} RETURN LABELS(n) AS new_labels"
            update_result = await self.async_run(update_query, node_id=node_id)
            labels = update_result.data()[0]['new_labels']

        return labels  # Return the updated label list for the node


    # create multiple nodes at once (optimize later to use a single query)
    async def create_nodes_old(self, node_types, propertieslist, parent_id=None):
        # Create a list of coroutine objects for each call to create_node
        tasks = [self.create_node(node_types, properties, parent_id) for properties in propertieslist]
        
        # Run all the create_node coroutine tasks concurrently and wait for all to complete
        results = await asyncio.gather(*tasks)
        
        return results

    async def create_nodes(self, node_types, properties_list, parent_id=None):
        if not properties_list:
            return []  # No properties to process

        labels = ":".join(node_types)  # Prepare the labels string
        print(labels)

        # Build the Cypher query
        if parent_id is not None:
            query = f"""
            MATCH (p) WHERE ID(p) = $parent_id
            UNWIND $properties_list AS props
            CREATE (n:{labels})
            SET n += props
            CREATE (p)-[r:HAS_CHILD]->(n)
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties, ID(r) AS rel_id
            """
            params = {'properties_list': properties_list, 'parent_id': parent_id}
        else:
            query = f"""
            UNWIND $properties_list AS props
            CREATE (n:{labels})
            SET n += props
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
            """
            params = {'properties_list': properties_list}

        # Execute the query with the parameters
        query_result = await self.async_run(query, **params)
        results = query_result.data()
        return results
    
    async def create_nodes_with_types_old(self, properties_list, parent_id=None):
        if not properties_list:
            return []  # No properties to process

        # Prepare the Cypher query using UNWIND
        if parent_id is not None:
            query = f"""
            MATCH (p) WHERE ID(p) = $parent_id
            UNWIND $properties_list AS props
            CREATE (n:{'{props.Type}'})
            REMOVE n.Type
            SET n += props
            CREATE (p)-[r:HAS_CHILD]->(n)
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties, ID(r) AS rel_id
            """
            params = {'properties_list': properties_list, 'parent_id': parent_id}
        else:
            query = f"""
            UNWIND $properties_list AS props
            CREATE (n:{'{props.Type}'})
            REMOVE n.Type
            SET n += props
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
            """
            params = {'properties_list': properties_list}

        # Execute the query
        query_result = await self.async_run(query, **params)
        results = query_result.data()
        return results
    

    async def create_nodes_with_types(self, properties_list, parent_id=None):
        if not properties_list:
            return []  # No properties to process

        params = {'properties_list': properties_list}
        if parent_id is not None:
            query = f"""
            UNWIND $properties_list AS props
            MATCH (p) WHERE ID(p) = $parent_id
            CALL apoc.create.node([props.Type], props) YIELD node AS n
            REMOVE n.Type
            CREATE (p)-[r:HAS_CHILD]->(n)
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties, ID(r) AS rel_id
            """
            params['parent_id'] = parent_id  # Add parent_id to the parameters
        else:
            query = f"""
            UNWIND $properties_list AS props
            CALL apoc.create.node([props.Type], props) YIELD node AS n
            REMOVE n.Type
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
            """
            params['parent_id'] = None  # Explicitly set parent_id to None

        # Execute the query
        results = await self.async_run(query, **params)
        return results.data()


    
    # create a relationship between two nodes using ids and relationship_type
    async def create_relationship(self, start_node_id, end_node_id, relationship_type, properties=None):
        properties = properties if isinstance(properties, dict) else {}

        # Dynamically construct the SET part of the query for properties
        set_clauses = ", ".join([f"r.{key} = ${key}" for key in properties.keys()])
        query = f"""
        MATCH (n), (m)
        WHERE ID(n) = $start_node_id AND ID(m) = $end_node_id
        CREATE (n)-[r:{relationship_type}]->(m)
        {f"SET {set_clauses}" if set_clauses else ""}
        RETURN type(r) AS relationship_type, properties(r) AS properties
        """

        query_result = await self.async_run(query, start_node_id=start_node_id, end_node_id=end_node_id, **properties)
        result = query_result.data()
        
        if not result:
            return None
        
        return result[0]

    
    async def update_relationship_properties(self, source, target, relationship_type, updated_properties):

        query = f"""
        MATCH (n)-[r:{relationship_type}]->(m)
        WHERE ID(n) = $source AND ID(m) = $target
        SET r += $updated_properties
        RETURN type(r) AS relationship_type, properties(r) AS properties
        """

        query_result = await self.async_run(query, source=source, target=target, updated_properties=updated_properties)
        result = query_result.data()

        if not result:
            return None

        return result[0]

    async def get_shared_ancestor(self, node_id1, node_id2):
        # Function to find the ancestors of a given node
        async def find_ancestors(node_id):
            query = """
            MATCH (ancestor)-[:HAS_CHILD*]->(n)
            WHERE ID(n) = $node_id
            RETURN collect(ID(ancestor)) AS ancestors
            """
            query_result = await self.async_run(query, node_id=node_id)
            result = query_result.data()[0]
            return set(result['ancestors'])

        # Find ancestors for both nodes
        ancestors1 = await find_ancestors(node_id1)
        ancestors2 = await find_ancestors(node_id2)

        # Find common ancestors
        common_ancestors = ancestors1.intersection(ancestors2)

        if not common_ancestors:
            return None  # No common ancestor found

        # Function to find the closest shared ancestor to node_id1
        async def find_closest_ancestor(common_ancestors):
            query = """
            UNWIND $common_ancestors AS ancestor_id
            MATCH (ancestor) WHERE ID(ancestor) = ancestor_id
            OPTIONAL MATCH path=(ancestor)-[:HAS_CHILD*]->(n1) WHERE ID(n1) = $node_id1
            RETURN ID(ancestor) AS id, LABELS(ancestor) AS labels, properties(ancestor) AS properties, 
                CASE WHEN path IS NOT NULL THEN length(path) ELSE 0 END AS depth
            ORDER BY depth ASC
            LIMIT 1
            """
            query_result = await self.async_run(query, common_ancestors=list(common_ancestors), node_id1=node_id1)
            result = query_result.data()[0]
            return result

        # Get the closest shared ancestor
        closest_ancestor = await find_closest_ancestor(common_ancestors)
        return closest_ancestor







    async def update_dependencies(self, node_id, dependencies):
        query = """
        MATCH (n)
        WHERE ID(n) = $node_id
        UNWIND $dependencies AS dependency_id
        MATCH (m)
        WHERE ID(m) = dependency_id
        MERGE (n)-[r:HAS_DEPENDENCY]->(m)
        """
        await self.async_run(query, node_id=node_id, dependencies=dependencies)
        
     
    async def get_node_by_id(self, node_id, node_type = None):
        match_label= "(n)"
        if node_type:
            match_label = f"(n:{node_type})"
            
        query = f"MATCH {match_label} WHERE ID(n)  = $node_id RETURN ID(n) AS node_id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, node_id=node_id)

        result = query_result.data()
        if result and len(result) > 0:
            result = result[0]
            return result
        return []
    
    async def get_node_by_label_id(self, node_id, node_type):
        query = f"MATCH (n:{node_type}) WHERE ID(n) = $node_id RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, node_id=node_id)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
        return []
    
    async def get_nodes_in_path(self, end_node_id, start_node_type):
        query = f"""
        MATCH path=(n:{start_node_type})-[:HAS_CHILD*]->(m)
        WHERE ID(m) = $end_node_id
        UNWIND nodes(path) AS node
        RETURN ID(node) AS id, LABELS(node) AS labels,  properties(node) AS properties
        """
        query_result = await self.async_run(query, end_node_id=end_node_id)
        result = query_result.data()
        return result
        
    async def get_nodes_by_ids_and_label(self, id_list, node_type):
        # Convert Python list to a list that can be passed to Cypher query
        query = f"MATCH (n:{node_type}) WHERE ID(n) IN $id_list RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_results = await self.async_run(query, id_list=id_list)
        results = query_results.data()
        # No need to check if results is not empty, as an empty result is valid when no nodes match the IDs
        return results

    async def get_nodes_by_ids(self, id_list):
        # Convert Python list to a list that can be passed to Cypher query
        query = f"MATCH (n) WHERE ID(n) IN $id_list RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_results = await self.async_run(query, id_list=id_list)
        results = query_results.data()
        # No need to check if results is not empty, as an empty result is valid when no nodes match the IDs
        return results

    async def get_node_by_property(self, node_type, property_name, property_value):
        query = f"MATCH (n:{node_type}) WHERE n.{property_name} = $property_value RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, property_value=property_value)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
    
    async def get_nodes_by_property(self, node_type, property_name, property_value):
        query = f"MATCH (n:{node_type}) WHERE n.{property_name} = $property_value RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, property_value=property_value)
        result = query_result.data()
        return result
    
    async def get_node_by_label(self, node_type: str):
        query = f"""
            MATCH (n:{node_type})
            WHERE '{node_type}' IN LABELS(n)
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            return result[0]  # Return the first matching node
        return None  # Return None if no matching node is found
    
    async def get_nodes_by_label(self, node_type):
        query = f"""
            MATCH (n:{node_type})
            RETURN ID(n) AS id, LABELS(n) AS labels,  properties(n) AS properties
        """
        query_result = await self.async_run(query)  # This will be a list of dictionaries with keys 'id', 'labels', and 'properties'
        
        result = query_result.data()
        return result
    
    async def get_nodes_connected_by_relationship(self, start_node_id, relationship_type, end_node_type):
        query = f"MATCH (n)-[r:{relationship_type}]-(m:{end_node_type}) WHERE ID(n) = {start_node_id} RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    
    async def get_connected_nodes(self, start_node_id, end_node_type=None):
        if end_node_type:
            end_node_type_string = f":{end_node_type}"
        else:
            end_node_type_string = ""
        query = f"MATCH (n)-[r]-(m{end_node_type_string}) WHERE ID(n) = $start_node_id RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        query_result = await self.async_run(query, start_node_id=start_node_id)
        result = query_result.data()
        return result
    
    async def get_connected_codegraph_nodes_and_relationships(self, start_node_id, end_node_type=None):
        
        if end_node_type:
            end_node_type_string = f":{end_node_type}"
        else:
            end_node_type_string = ""

        # Query to match connected nodes, include relationships, and determine direction
        query = f"""
        MATCH (n)-[r]->(m{end_node_type_string}) 
        WHERE ID(n) = $start_node_id 
        RETURN ID(m) AS node_id, 
            LABELS(m) AS labels, 
            properties(m) AS properties, 
            TYPE(r) AS relationship_type, 
            properties(r) AS relationship_properties,
            CASE WHEN (n)-[r]->(m) THEN 'outgoing' WHEN (n)<-[r]-(m) THEN 'incoming' ELSE 'undirected' END AS direction
        """
        
        # Execute query
        query_result = await self.async_run(query, start_node_id=start_node_id)
        
        # Process and filter duplicates by using a set to store unique node ids
        result = []
        seen_node_ids = set()

        def format_node_info(record):
            return {
                'node_id': record['node_id'],
                'labels': record['labels'],
                'properties': {
                    'name': record['properties'].get('name'),
                    'id': record['properties'].get('id'),
                    'description': record['properties'].get('description'),

                },
                'relationship_type': {
                    'type': record['relationship_type'],
                }
            }
        for record in query_result.data():
            node_id = record['node_id']
            if "ArchitectureRoot" in record['labels'] or "ArchitecturalComponent" in record['labels'] or "ArchitecturalElement" in record['labels']:
                continue
            if node_id not in seen_node_ids:
                seen_node_ids.add(node_id)
                record =format_node_info(record)
                result.append(record)
        
        return result
    async def get_nodes_in_reverse_direction(self, end_node_id, relationship_type, start_node_type):
        query = f"MATCH (n:{start_node_type})-[r:{relationship_type}]-(m) WHERE ID(m) = {end_node_id} RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_nodes_connected_by_multiple_hops(self, start_node_id, relationship_type, end_node_type, max_hops):
        query = (f"MATCH (n)-[:{relationship_type}*0..{max_hops}]->(m:{end_node_type}) "
                f"WHERE ID(n) = {start_node_id} "
                f"RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties")
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_root_node(self, node_id):
        query = f"MATCH (n)-[:HAS_CHILD*]->(m) WHERE ID(m) = {node_id} RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query)
        result = query_result.data()
        if result and len(result) > 0:
            return result[-1]
        
    async def get_parent_node(self, child_node_id):
        query = f"MATCH (n)-[:HAS_CHILD]->(m) WHERE ID(m) = $child_node_id RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, child_node_id=child_node_id)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
    
    async def get_child_nodes(self, parent_node_id, child_node_type=None):
        if child_node_type:
            query = f"MATCH (n)-[:HAS_CHILD]->(m:{child_node_type}) WHERE ID(n) = $parent_node_id RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        else:
            query = f"MATCH (n)-[:HAS_CHILD]->(m) WHERE ID(n) = $parent_node_id RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        query_result = await self.async_run(query, parent_node_id=parent_node_id)
        result = query_result.data()
        return result

    
    async def get_sibling_nodes(self, node_id, node_type=None):
        query = """
            MATCH (parent)-[:HAS_CHILD]->(givenNode)
            WHERE ID(givenNode) = $node_id
            MATCH (parent)-[:HAS_CHILD]->(sibling)
            WHERE ID(sibling) <> $node_id
        """
        
        # Optionally filter by the sibling node type if provided.
        if node_type:
            query += f"AND '{node_type}' in labels(sibling)"
            
        query += "RETURN ID(sibling) AS id, LABELS(sibling) AS labels, properties(sibling) AS properties"
        
        query_result = await self.async_run(query, node_id=node_id)
        result = query_result.data()
        return result


    
    async def get_node_tree(self, node_id, node_type):
        query = (
            "MATCH path=(root:{node_type})-[:HAS_CHILD*0..]->(child:{node_type}) "
            "WHERE ID(root) = $node_id "
            "WITH collect(path) AS paths "
            "CALL apoc.convert.toTree(paths) YIELD value "
            "RETURN value"
        ).format(node_type=node_type)  # Dynamically insert the node_type into the query string

        query_result = await self.async_run(query, node_id=node_id)
        result = query_result.data()
        return result[0]['value']
    
    async def get_top_level_nodes(self, root_node_id, node_type):
        query = """
        MATCH (n:Project|Product)-[:HAS_CHILD]-(n1:{node_type})
        
        WHERE ID(n) = {root_node_id}
        OPTIONAL MATCH (n1)-[r:HAS_CHILD]->(m:Requirement)
        OPTIONAL MATCH (m)<-[r:ASSIGNED_TO]-(u:User)
        RETURN DISTINCT ID(m) AS id, ID(n1) AS requirement_root_id, LABELS(m) AS labels, properties(m) AS properties, m.Status as status, m.configuration_status as configuration_status , m.Title as title, m.Type as type , ID(u) AS assignee_id, u.Email AS assignee_email, u.Name as assignee_name, r.assigned_at AS assigned_at
        """
        query = query.format(node_type=node_type, root_node_id=root_node_id)
        result = await self.async_run(query)
        return result.data()
    
    async def get_child_requirements(self, parent_id, node_type):
        query = f"""
            MATCH (n)-[:HAS_CHILD]->(m:{node_type})
            WHERE ID(n) = {parent_id}
            OPTIONAL MATCH (m)<-[r:ASSIGNED_TO]-(u:User)
            RETURN DISTINCT ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties, m.Status as status, m.configuration_status as configuration_status , m.Title as title, m.Type as type , ID(u) AS assignee_id, u.Email AS assignee_email, u.Name as assignee_name, r.assigned_at AS assigned_at
            """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_requirement_by_id(self, node_id, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD*]->(n:Requirement)
        WHERE ID(n) = {node_id} AND ID(p) = {project_id}
        OPTIONAL MATCH (n)<-[r:ASSIGNED_TO]-(u:User)
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties, n.Status as status, 
            n.configuration_status as configuration_status, n.Title as title, n.Type as type,
            u.Username AS assignee_id, u.Email AS assignee_email, u.Name as assignee_name, 
            r.assigned_at AS assigned_at
        """
        result = await self.async_run(query)
        result = result.data()
        if result:
            return result[0]
        else:
            return {}
    
    async def delete_node(self, node_id):

        self.pc_index.delete([str(node_id)])  # Delete node from PC index

        query = f"MATCH (n) WHERE ID(n) = $node_id DETACH DELETE n"
        await self.async_run(query, node_id=node_id)
    
        return True
    
    async def delete_node_by_filter(self, node_type, property_name, property_value):
        query = f"MATCH (n:{node_type}) WHERE n.{property_name} = $property_value DETACH DELETE n"
        await self.async_run(query, property_value=property_value)

    async def delete_nodes_by_label(self, labels):
        # Convert labels to a list if it's a single label
        if isinstance(labels, str):
            labels = [labels]
        
        # Build the label condition for the Cypher query
        label_condition = " OR ".join([f"'{label}' IN LABELS(n)" for label in labels])
        
        # Check if nodes with the specified labels exist
        query = f"MATCH (n) WHERE {label_condition} RETURN COUNT(n) AS count"
        result = await self.async_run(query)
        count = result.data()[0]['count']
        
        if count == 0:
            return "No available nodes to delete."
        
        # Delete nodes from the database
        query = f"MATCH (n) WHERE {label_condition} DETACH DELETE n"
        await self.async_run(query)
        
        return "Nodes deleted successfully."

    async def get_nodes_in_path(self, end_node_id, start_node_type):
        query = f"""
        MATCH path=(n:{start_node_type})-[:HAS_CHILD*]->(m)
        WHERE ID(m) = $end_node_id
        UNWIND nodes(path) AS node
        RETURN ID(node) AS id, LABELS(node) AS labels, properties(node) AS properties
        """
        query_result = await self.async_run(query, end_node_id=end_node_id)
        result = query_result.data()
        return result


    async def get_associated_item(self, node_id, type_1, type_2, relationship_type):
        query = f'''
            MATCH (p:{type_1})-[:{relationship_type}]->(prod:{type_2})
            WHERE ID(p) = {node_id}
            RETURN ID(prod) AS ProductID, prod.Name AS Name, prod.Description AS Description
        '''
        print(query)
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    @staticmethod
    def get_likely_associated_products():
        
        # return empty list if no likely associated products are found
        return []


    async def delete_node_recursively_by_relationship(self, start_node_id, relationship_type):
        # Query to find all nodes (and the start node) that will be deleted.
        find_nodes_query = f"""
        MATCH (n)-[r:{relationship_type}*]->(m) 
        WHERE ID(n) = {start_node_id} 
        RETURN DISTINCT ID(m) AS NodeID 
        UNION
        MATCH (n)
        WHERE ID(n) = {start_node_id}
        RETURN ID(n) AS NodeID
        """
        
        # Execute the query to find nodes
        result = await self.async_run(find_nodes_query)
        
        # Collecting all node IDs that will be deleted
        deleted_node_ids = [record["NodeID"] for record in result]

        if deleted_node_ids:
            # Convert list of IDs into a string to use in Cypher query
            ids_string = ",".join(map(str, deleted_node_ids))

            # Delete nodes by their IDs directly
            delete_nodes_query = f"MATCH (n) WHERE ID(n) IN [{ids_string}] DETACH DELETE n"
            await self.async_run(delete_nodes_query)

        return deleted_node_ids
    
    async def delete_node_and_associates(self, start_node_id, relationships):
        # Query to find all nodes connected by specified relationships
        find_nodes_query = f"""
        MATCH (n) WHERE ID(n) = {start_node_id}
        OPTIONAL MATCH (n)-[r:{' | '.join(relationships)}*]->(m)
        RETURN DISTINCT ID(n) AS NodeID, COLLECT(ID(m)) AS AssociatedNodeIDs
        """
        # Execute the query to find nodes
        result = await self.async_run(find_nodes_query)
        data = result.data()

        if not data:
            return "No nodes found to delete."

        # Collecting all node IDs that will be deleted
        node_ids_to_delete = set(data[0]['AssociatedNodeIDs'])
        node_ids_to_delete.add(data[0]['NodeID'])  # Add the start node ID

        # Convert set of IDs into a list to use in Cypher query
        ids_list = list(node_ids_to_delete)

        if ids_list:
            # Delete nodes by their IDs directly
            ids_string = ",".join(map(str, ids_list))
            delete_nodes_query = f"MATCH (n) WHERE ID(n) IN [{ids_string}] DETACH DELETE n"
            await self.async_run(delete_nodes_query)

        return f"Deleted nodes: {ids_list}"

    async def update_dependencies(self, node_id, dependencies):
        query = """
        MATCH (n)
        WHERE ID(n) = $node_id
        UNWIND $dependencies AS dependency_id
        MATCH (m)
        WHERE ID(m) = dependency_id
        MERGE (n)-[r:HAS_DEPENDENCY]->(m)
        """
        await self.async_run(query, node_id=node_id, dependencies=dependencies)

    async def get_descendant_nodes(self, parent_node_id, descendant_node_types=None, property_names=None, max_depth=None):
        node_type_condition = ""
        if descendant_node_types:
            # Convert the list to a string format suitable for Cypher query
            types_as_str = "[" + ", ".join(f"'{type_}'" for type_ in descendant_node_types) + "]"
 #           node_type_condition = f"AND ALL(label IN LABELS(m) WHERE label IN {types_as_str})"
            node_type_condition = f"AND ANY(label IN {types_as_str} WHERE label IN LABELS(m))"

        if property_names:
            property_selection = ", ".join(f"m.{prop} AS {prop}" for prop in property_names)
        else:
            property_selection = "properties(m) AS properties"

        query = f"""
        MATCH (n)-[:HAS_CHILD*1..{max_depth}]->(m)
        WHERE ID(n) = $parent_node_id {node_type_condition}
        RETURN ID(m) AS id, LABELS(m) AS labels, {property_selection}
        """
        query_result = await self.async_run(query, parent_node_id=parent_node_id)
        result = query_result.data()
        return result
    
  # Update node by id. The list of attributes to update may be just partial and not all attributes
    async def update_node_by_id(self, node_id, properties, node_type=None):
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"MATCH (n) WHERE ID(n) = $node_id SET {set_clauses} RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties "
        # Prepare parameters for the query
        params = {'node_id': node_id}
        params.update(properties)  # This adds each key-value pair in `properties` to the `params` dict
        query_result = await self.async_run(query, **params)
        result = query_result.data()

        if result and len(result) > 0:
            return result[0]
        else:
            return None

    
    # update multiple nodes at the same time.
    async def update_nodes_by_id(self, nodelist):
        # Start building the Cypher query
        query = "UNWIND $nodelist as nodeToUpdate MATCH (n) WHERE ID(n) = nodeToUpdate.id "

        # Dynamically build the SET part of the query based on dictionary keys
        set_clauses = []
        for key in nodelist[0].keys():  # Assuming all items in nodelist have the same schema
            if key != 'id':  # Skip the id key for SET operation
                set_clause = f"n.{key} = nodeToUpdate.{key}"
                set_clauses.append(set_clause)
        set_clauses_str = ', '.join(set_clauses)  # Join all SET clauses

        # Complete the query with the dynamically built SET part
        query += f"SET {set_clauses_str} "
        query += "RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"

        # Execute the query
        query_result = await self.async_run(query, nodelist=nodelist)
        result = query_result.data()
        return result

    async def get_graph_nodes(self, node_id: int, node_types: list = [], max_depth: int = 999):
        def format_node(node_data):
            return {
                "id": node_data.get("id"),
                "label": node_data.get("properties", {}).get("Title", None),
                'type': node_data.get("properties",{}).get("Type" , None),
                "title": node_data.get("properties", {}).get("Title", None),  # Extract title from properties
            }

        if node_types:
            node_types_str = '|'.join([f'{label}' for label in node_types])
            node_types_clause = f'(m:{node_types_str})'
        else:
            node_types_clause = '(m)'

        query = f"""
        MATCH path = (n)-[*1..{max_depth}]->{node_types_clause}
        WHERE ID(n) = $node_id
        WITH nodes(path) AS nodes, relationships(path) AS rels
        UNWIND nodes AS node
        UNWIND rels AS rel
        RETURN DISTINCT {{
            id: ID(node),
            label: node.title,
            properties: properties(node)
        }} AS nodeInfo,
        {{
            from: ID(startNode(rel)),
            to: ID(endNode(rel)),
            label: type(rel),
            properties: properties(rel)
        }} AS edgeInfo
        """

        query_result = await self.async_run(query, node_id=node_id)

        nodes = []
        edges = []
        appended_nodes = []
        for row in query_result:
            node_info, edge_info = row
            if node_info['id'] not in appended_nodes:
                appended_nodes.append(node_info['id'])
                nodes.append(format_node(node_info))

            edges.append(edge_info)

        return {"nodes": nodes, "edges": edges}


        
    
# Architectural Functions:
    async def get_root_architecture(self, project_id):
        query = f"""
        MATCH (p:Project|Product)-[:HAS_CHILD]->(a:ArchitectureRoot)-[:HAS_CHILD]->(a1:Architecture)
        WHERE ID(p) = {project_id}
        WITH a, COLLECT({{id: ID(a1), properties: properties(a1), labels: labels(a1)}}) AS children
        RETURN {{id: ID(a), properties: properties(a), labels: labels(a)}} AS root, children
        """
        
        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            result = result[0]
        return result

    async def get_child_architectures(self, parent_id):
        query = f"""
        MATCH (a:Architecture)-[:HAS_CHILD]->(a1:Architecture)
        WHERE ID(a) = {parent_id}
        RETURN ID(a1) AS id, properties(a1) AS properties, labels(a1) AS labels
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    async def get_architectural_element_by_property(self,parent_architecture_id, properties):
        
        
        if not properties :
            raise ValueError("Properties must be provided as a non-empty dictionary.")

        # Construct Property Filter
        property_filter = " AND ".join([f"a1.{key}='{value}'" for key, value in properties.items()])
        
        query = f"""
        MATCH (a:Architecture)-[r:HAS_CHILD|INTERFACES_WITH*0..6]-(a1:Architecture)
        WHERE ID(a) = {parent_architecture_id} AND {property_filter}
        RETURN ID(a1) AS id, properties(a1) AS properties, labels(a1) AS labels
        LIMIT 1
        """
        
        
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    async def get_architecture_details(self, project_id):
        # Get the project details along with Architecture interface details and design details
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD]->(a:ArchitectureRoot)-[:HAS_CHILD|INTERFACES_WITH*0..3]-(a1:Architecture|Interface|Design)
        WHERE ID(p) = {project_id}
        WITH p, a, 
            collect(DISTINCT a1) as a1s
        RETURN ID(p) as project_id, properties(p) as properties, labels(p) as labels, 
            ID(a) as architecture_root_id, properties(a) as architecture_root_properties, labels(a) as architecture_root_labels, 
            [a1 in a1s WHERE 'Architecture' IN labels(a1) | 
                {{ 
                    architecture_id: ID(a1), 
                    architecture_properties: properties(a1), 
                    architecture_labels: labels(a1) 
                }}
            ] as architecture_details,
            [a1 in a1s WHERE 'Interface' IN labels(a1) | 
                {{ 
                    interface_id: ID(a1), 
                    interface_properties: properties(a1), 
                    interface_labels: labels(a1) 
                }}
            ] as interface_details,
            [a1 in a1s WHERE 'Design' IN labels(a1) | 
                    {{ 
                    design_id: ID(a1), 
                    design_properties: properties(a1), 
                    design_labels: labels(a1) 
                }}
            ] as design_details
        """

        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            result = result[0]
        else:
            result = {}
        return result
    
    async def get_architecture_leaf_nodes(self, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD]->(a:ArchitectureRoot)-[:HAS_CHILD*0..]->(a1:ArchitectureLeaf)
        WHERE ID(p) = {project_id}
        RETURN ID(a1) AS id, a1.Title AS title, a1.Type AS type, a1.Description AS description
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        
        return result
   
    async def get_work_items(self, architecture_id):
        query = f"""
        MATCH (a:Architecture)
        WHERE ID(a) = {architecture_id}
        OPTIONAL MATCH (a)-[r:INTERFACES_WITH]-(a1:Architecture)
        OPTIONAL MATCH (interface_node)
        WHERE ID(interface_node) = r.interface_node_id
        OPTIONAL MATCH (a)-[:HAS_CHILD]->(d:Design)
        OPTIONAL MATCH (d)-[:HAS_CHILD]->(c)
        WHERE NOT c:Discussion
        RETURN 
            a.Title AS component_name,
            a.Description AS description,
            COLLECT(DISTINCT {{
                architecture_id: ID(a1),
                relationship_properties: properties(r),
                interface_node: CASE 
                    WHEN interface_node IS NOT NULL 
                    THEN {{id: ID(interface_node), properties: properties(interface_node)}}
                    ELSE NULL
                END
            }}) AS interfaces,
            {{
                id: ID(d),
                properties: properties(d),
                labels: labels(d)
            }} AS design,
            COLLECT(DISTINCT {{
                id: ID(c),
                properties: properties(c),
                labels: labels(c)
            }}) AS design_components
        """

        query_result = await self.async_run(query)

        result = query_result.data()[0]  # Assuming there's only one architecture with this ID

        work_item_map = {
            "component_name": result['component_name'],
            "description": result['description'],
            "interfaces": result['interfaces'],
            "design": result['design'],
            "Algorithm": [],
            "StateLogic": [],
            "Sequence": [],
            "StateDiagram": [],
            "UnitTest": [],
            "PerformanceTest": [],
            "Pseudocode": []
        }

        # Process design components
        for component in result['design_components']:
            label = component['labels'][0]
            if label in work_item_map:
                work_item_map[label].append(json.dumps(component['properties']))
            else:
                work_item_map[label] = [json.dumps(component['properties'])]

        return work_item_map

    async def get_design_node_with_components(self, architecture_id):
        query = f"""
        MATCH (a:Architecture)-[:HAS_CHILD]->(d:Design)-[:HAS_CHILD]->(s)
        WHERE ID(a) = {architecture_id} AND NOT s:Discussion
        RETURN ID(d) AS design_id, properties(d) AS design_properties, labels(d) AS design_labels,
            COLLECT(ID(s)) AS child_ids, COLLECT(labels(s)) AS child_labels , COLLECT(properties(s)) AS child_properties
        """

        query_result = await self.async_run(query)
        results = query_result.data()

        # Process Results
        formatted_results = []
        for row in results:
            design_node = {
                'id': row['design_id'],
                'properties': row['design_properties'],
                'labels': row['design_labels'],
          
            }
            for i, child_id in enumerate(row['child_ids']):
                
                if row['child_labels'][i][0] not in design_node:
                    design_node[row['child_labels'][i][0]] = []

                design_node[row['child_labels'][i][0]].append({
                    'id': child_id,
                    **row['child_properties'][i]
                })

            formatted_results.append(design_node)

        return formatted_results
    
    async def get_interfaces(self, architecture_id):
        query = f"""
        MATCH (a:Architecture)-[r:INTERFACES_WITH]-(a1:Architecture)
        WHERE ID(a) = {architecture_id}
        OPTIONAL MATCH (interface_node)
        WHERE ID(interface_node) = r.interface_node_id
        RETURN DISTINCT ID(a1) AS architecture_id,  
            properties(r) AS relationship_properties,
            CASE WHEN interface_node IS NOT NULL 
                    THEN {{id: ID(interface_node), properties: properties(interface_node)}}
                    ELSE NULL
            END AS interface_node
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_design_node(self, architecture_id):
        query = f"""
        MATCH (a:Architecture)-[:HAS_CHILD]->(d:Design)
        WHERE ID(a) = {architecture_id}
        RETURN ID(d) AS id, properties(d) AS properties, labels(d) AS labels
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_design_components(self, design_id):
        query = f"""
        MATCH (d:Design)-[:HAS_CHILD]->(c)
        WHERE ID(d) = {design_id} AND NOT c:Discussion
        RETURN ID(c) AS id, properties(c) AS properties, labels(c) AS labels
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    

#End of Architectural Functions
    
    async def get_general_discussions_by_user(self, user_id: str, _limit) -> List[Dict[str, Any]]:
        """
        Fetches GeneralDiscussion nodes that are children of a User node and have IsDeleted set to false.
        
        :param user_id: The ID (Username) of the user.
        :return: A list of dictionaries containing information about GeneralDiscussion nodes connected to the specified user and not marked as deleted.
        """
        query = """
        MATCH (u:User {Username: $user_id})-[:HAS_CHILD]->(d:GeneralDiscussion)
        WHERE d.IsDeleted = false
        RETURN d, ID(d) ORDER BY d.CreatedAt DESC LIMIT $limit
        """        
        try:
            query_result = await self.async_run(query, user_id=user_id, limit=_limit)
            result = query_result.data()
            if result and len(result) > 0:
                return result
            return []
        except Exception as e:
            raise 

# Indexing functions:     
    async def create_indexes(self):
        print("Creating indexes..")
        try:

            # Property Indexes
            await self.async_run("CREATE INDEX username_index IF NOT EXISTS FOR (u:User) ON (u.Username);")
            await self.async_run("CREATE INDEX user_id_index IF NOT EXISTS FOR (u:User) ON (u.user_id);")

            print("Indexes created successfully.")
        except Exception as e:
            print(f"Error creating indexes: {e}")
            

            
    
