
from code_generation_core_agent.database.NodeDB import NodeDB

from pydantic_settings import BaseSettings ,PydanticBaseSettingsSource
from pydantic import Field
from typing import List, Tuple, Type

class Settings(BaseSettings):

    # Neo4j Database Credentials
    NEO4J_CONNECTION_URI: str = Field(env="NEO4J_CONNECTION_URI", description="Neo4j connection URI")
    NEO4J_USER: str = Field(env="NEO4J_USER", description="User for Neo4j database")
    NEO4J_PASSWORD: str = Field(env="NEO4J_PASSWORD", description="Password for Neo4j database")


    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> <PERSON>ple[PydanticBaseSettingsSource, ...]:
        return dotenv_settings,env_settings ,init_settings, file_secret_settings
 
    class Config:
        validate_assignment = True
        case_sensitive = True
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Ignore extra variables
       
    
settings = Settings()
db = None

def connect_node_db() -> NodeDB:
    """Connect to the NodeDB."""
    global db
    db = NodeDB(settings.NEO4J_CONNECTION_URI,settings.NEO4J_USER,settings.NEO4J_PASSWORD)
    return db

def get_node_db() -> NodeDB:
    """Get the NodeDB instance."""
    if db is None:
        connect_node_db()
    return db
