import os
from abc import ABC
from dataclasses import dataclass, field, fields, asdict
from pathlib import Path
from typing import List, Optional, Dict, Any, get_type_hints, get_origin, get_args, Union
from enum import Enum
import json
import re

import yaml
from dotenv import dotenv_values

from code_generation_core_agent.agents.utilities import ApplicationType

from universalinit_env import map_common_to_framework, map_framework_to_common

"""
Container Environment Variables Save/Load Flow
=====================================================

1. SETTING ENVIRONMENT VARIABLES
---------------------------------
   Container Object
   ┌─────────────────────┐
   │ Container           │
   │ ┌─────────────────┐ │
   │ │ private:        │ │
   │ │   env: {        │ │ ← User sets env vars here
   │ │     "KEY": "val"│ │   container.env["KEY"] = "value"
   │ │   }             │ │
   │ └─────────────────┘ │
   └─────────────────────┘

2. SAVING TO MANIFEST (save_to_manifest)
-----------------------------------------
   ProjectSchema
   ┌─────────────────────┐
   │ containers: [...]   │
   └─────────────────────┘
            │
            │ save_to_manifest("manifest.yaml")
            ▼
   ┌─────────────────────┐    ┌─────────────────────────────┐
   │ manifest.yaml       │    │ .env file created at:       │
   │ (without private    │    │ {base_path}/{container_root}│
   │  fields)            │◄───┤ /.env                       │
   └─────────────────────┘    │                             │
                              │ KEY=value                   │
                              │ ANOTHER_KEY=another_value   │
                              └─────────────────────────────┘

3. LOADING PROJECT SCHEMA FROM MANIFEST (load_from_file)
------------------------------------------

The ProjectSchem is reconstructed from the manifest file and the .env files of the containers.

   ┌─────────────────────┐    ┌─────────────────────────────┐
   │ manifest.yaml       │    │ .env file read from:        │
   │                     │    │ {manifest_dir}/{container_  │
   └─────────────────────┘    │ root}/.env                  │
            │                 └─────────────────────────────┘
            │                             │
            │ load_from_file()            │
            ▼                             │
   ┌─────────────────────┐                │
   │ ProjectSchema       │                │
   │ ┌─────────────────┐ │                │
   │ │ Container       │ │◄───────────────┘
   │ │   .env          │ │  dotenv_values() loads
   │ │   populated     │ │  vars into private.env
   │ └─────────────────┘ │
   └─────────────────────┘

.env:
   SUPABASE_URL=...
   
.env.template - universal init   
   REACT_SUPABASE_URL={{SUBASE_URL}}
   




4. MANUAL .env MODIFICATION + SYNC
-----------------------------------

   User manually edits .env:
   ┌─────────────────────────────┐
   │ .env file                   │
   │ KEY=value                   │
   │ ANOTHER_KEY=another_value   │
   │ MANUAL_VAR=manual_value ←── │ User adds this manually
   └─────────────────────────────┘
            │
            │ sync_to_all_workspaces(project, base_path)
            │   └─> calls sync_workspace_to_manifest()
            ▼
   ┌─────────────────────┐
   │ Container           │
   │ ┌─────────────────┐ │
   │ │ private.env     │ │ ← dotenv_values() re-reads .env
   │ │ updated with    │ │   and updates private.env
   │ │ manual changes  │ │
   │ └─────────────────┘ │
   └─────────────────────┘
"""

def desc_for_llm(description: str) -> dict[str, dict[str, str]]:
    """Helper to create field metadata with description."""
    return {"metadata": {"description": description}}


class ContainerType(Enum):
    """Central enum for container types."""
    FRONTEND = "frontend"
    BACKEND = "backend"
    MOBILE = "mobile"
    DATABASE = "database"

    @classmethod
    def from_string(cls, value: str):
        """Convert string to ContainerType."""
        for member in cls:
            if member.value == value.lower():
                return member
        raise ValueError(f"Invalid ContainerType: {value}")

class CodeGenerationStatus(Enum):
    """Status of code generation."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"

@dataclass()
class ContainerPrivateFields:
    """
    Private fields are container specific informations that are not saved to the
    manifest.
    Private fields are never saved.
    """
    work_item : str = field(default="")
    previous_context : str = field(default="")
    code_generation_status : CodeGenerationStatus = field(default=CodeGenerationStatus.NOT_STARTED)

@dataclass
class ColorScheme:
    primary: str = field(**desc_for_llm("Primary color in HEX format"), default="")
    secondary: str = field(**desc_for_llm("Secondary color in HEX format"), default="")
    accent: str = field(**desc_for_llm("Accent color in HEX format"), default="")
    background: str = field(**desc_for_llm("Background color in HEX format"), default="")


class ContainerDetails(ABC):
    pass

@dataclass
class FrontendContainer(ContainerDetails):
    features: List[str] = field(default_factory=list, **desc_for_llm("List of features"))
    colors: ColorScheme = field(default_factory=ColorScheme, **desc_for_llm("Color scheme"))
    theme: str = field(default="", **desc_for_llm("Theme: light, dark, custom, or selectable"))
    layout_description: str = field(default="", **desc_for_llm("Layout description"))
    style: str = field(default="", **desc_for_llm("Style description (e.g., modern, minimalistic)"))


@dataclass
class Route:
    """
    Route represent the API and URL routing for the container.
    when path is set, the URL of the container will be "base_url/path"
    """
    path: str = field(default="")
    endpoint: str = field(default="")


@dataclass
class Auth:
    loginEndpoint: str = field(default="")
    registerEndpoint: str = field(default="")
    roles: List[str] = field(default_factory=list)


@dataclass
class Container:
    container_name: str = field(**desc_for_llm("Container name"))
    description: str = field(**desc_for_llm("Container purpose"))
    interfaces: str = field(**desc_for_llm("Exposed interfaces/APIs"))
    container_type: ContainerType = field(**desc_for_llm("Container type"))
    dependent_containers: List[str] = field(default_factory=list,
                                            **desc_for_llm("Containers that this container depends on. No cyclic dependencies allowed."))
    workspace: str = field(default="")
    container_root: str = field(default="") # Root path for the container, relative to base_directory
    port: str = field(default="")
    framework: str = field(default="", **desc_for_llm("Framework. Use the hint received from the user. Can't be empty"))
    type: str = field(default="")
    buildCommand: str = field(default="")
    startCommand: str = field(default="")
    installCommand: str = field(default="")
    lintCommand: str = field(default="")
    generateOpenapiCommand: str = field(default="")

    # Original fields from specific container types
    container_details: Optional[ContainerDetails] = field(default=None,**desc_for_llm("Container Details"))
    lintConfig: str = field(default="")
    routes: List[Route] = field(default_factory=list)
    apiSpec: str = field(default="")
    auth: Optional[Auth] = field(default=None)
    schema: str = field(default="")
    migrations: str = field(default="")
    seed: str = field(default="")
    #initialize the environment variables as empty dict
    env: Dict[str, str] = field(default_factory=dict)
    private: ContainerPrivateFields = field(default_factory=ContainerPrivateFields)

    def __repr__(self) -> str:
        container_dict = asdict(self)
        container_dict.pop('private', None)
        return f"Container({container_dict})"

    def _get_workspace(self) -> str:
        """
        Generate relative path from container name, matching GUI logic.
        """
        if self.workspace:
            return self.workspace
        return f"{self.container_name.replace(' ', '_')}_workspace"
    
    def _get_container_root(self) -> str:
        """
        Generate relative path from container name, matching GUI logic.
        """
        if self.container_root:
            return self.container_root
        
        workspace = self._get_workspace()

        # join container name with workspace
        return f"{workspace}/{self.container_name.replace(' ', '_')}"

    def to_work_item_format(self) -> dict[str, Any]:
        """
        Convert Container instance to the format that we need in the work item.
        """
        return {
            "container_name": self.container_name,
            "platform": get_platform_from_container_type(self.container_type).value,
            "framework": self.framework,
            "description": self.description,
            "interfaces": self.interfaces,
            "workspace": self._get_workspace(),
            "container_root": self._get_container_root(),
            "dependencies": self.dependent_containers.copy(),
            "container_type": self.container_type.value,
            "container_details": self.container_details if self.container_details else None,
        }

@dataclass
class ProjectOverview:
    project_name: str = field(**desc_for_llm("Project name"))
    description: str = field(**desc_for_llm("Project description"))
    third_party_services: Optional[list[str]] = field(default_factory=list, **desc_for_llm(
        "A list of 3rd party services that should be used in the project, e.g. Firebase, Supabase, Stripe, etc."))

    env: Dict[str, str] = field(default_factory=dict)


class Manifest:
    def __init__(self):
        pass

    def save_to_manifest(self, file_name: str):
        """Save the current instance to a YAML file."""
        file_path = Path(file_name)
        try:
            data_dict = asdict(self)

            # We never save private fields to the manifest, so remove them from the data dict
            if hasattr(self, 'containers') and self.containers:
                # Save environment variables for each container

                for container in self.containers:
                    env_file = os.path.join(file_path.parent, container.container_root, ".env")
                    # Create directory if it does not exist
                    if not os.path.exists(os.path.dirname(env_file)):
                        os.makedirs(os.path.dirname(env_file), exist_ok=True)

                    # Map env items to framework
                    if container.framework:
                        try:
                            mapped_env = map_common_to_framework(container.framework, container.env)
                            if hasattr(self, 'overview') and self.overview and hasattr(self.overview, 'env'):
                                mapped_env.update(map_common_to_framework(container.framework, self.overview.env))
                        except FileNotFoundError as e:
                            print(f"Warning: Framework {container.framework} not found. Using raw env vars.")
                            mapped_env = container.env
                    else:
                        mapped_env = container.env

                    # Save environment variables to the .env file
                    with open(env_file, 'w', encoding='utf-8') as env_file_handle:
                        for key, value in mapped_env.items():
                            env_file_handle.write(f"{key}={value}\n")

                for container_dict in data_dict.get('containers', []):
                    container_dict.pop('private', None)
            # Do not save env member

            #if 'overview' in data_dict and data_dict['overview'] and 'env' in data_dict['overview']:
            #    data_dict['overview'].pop('env', None)

            self._convert_enums_for_serialization(data_dict)

            file_path = Path(file_name)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as file:
                yaml.dump(data_dict, file, default_flow_style=False, indent=2, sort_keys=False)

            print(f"Successfully saved manifest to {file_name}")

        except Exception as e:
            print(f"Error saving manifest to {file_name}: {str(e)}")
            raise

    def _convert_enums_for_serialization(self, data):
        """Recursively convert enums to their values for YAML serialization."""
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, Enum):
                    data[key] = value.value
                elif isinstance(value, (dict, list)):
                    self._convert_enums_for_serialization(value)
        elif isinstance(data, list):
            for i, item in enumerate(data):
                if isinstance(item, Enum):
                    data[i] = item.value
                elif isinstance(item, (dict, list)):
                    self._convert_enums_for_serialization(item)

    def load_from_manifest(self, file_name: str):
        """Load data from a YAML file and populate the current instance."""
        try:
            file_path = Path(file_name)

            if not file_path.exists():
                raise FileNotFoundError(f"Manifest file {file_name} not found")

            with open(file_path, 'r', encoding='utf-8') as file:
                data_dict = yaml.safe_load(file)

            # converts ports to port backward compatibility
            if 'containers' in data_dict:
                for container in data_dict['containers']:
                    if 'port' not in container and 'ports' in container:
                        container['port'] = container['ports']
                        del container['ports']

            if data_dict is None:
                raise ValueError(f"Empty or invalid YAML file: {file_name}")

            loaded_schema = dict_to_project_schema(data_dict)

            for field_info in fields(self):
                setattr(self, field_info.name, getattr(loaded_schema, field_info.name))

            manifest_dir = file_path.parent

            if hasattr(self, 'containers') and self.containers:
                for container in self.containers:
                    if container.container_root:
                        env_file_path = os.path.join(manifest_dir, container.container_root, ".env")
                        if os.path.exists(env_file_path):
                            env_vars = dotenv_values(env_file_path)
                            if (container.framework):
                                try:
                                    env_vars = map_framework_to_common(container.framework, env_vars)
                                except FileNotFoundError as e:
                                    print(f"Warning: Framework {container.framework} not found. Using raw env vars.")
                                    env_vars = container.env
                            else:
                                print("Framework is not set, using raw env vars.")

                            container.env.update(env_vars)

        except Exception as e:
            print(f"Error loading manifest from {file_name}: {str(e)}")
            raise


@dataclass
class ProjectSchema(Manifest):
    overview: Optional[ProjectOverview] = field(default=None)
    containers: List[Container] = field(default_factory=list, **desc_for_llm("List of all project containers"))

    def __repr__(self) -> str:
        # Create a copy of containers without private fields
        containers_repr = []
        for container in self.containers:
            container_dict = asdict(container)
            container_dict.pop('private', None)  # Remove private field
            containers_repr.append(container_dict)



        return f"ProjectSchema(overview={self.overview}, containers={containers_repr})"

    @classmethod
    def load_from_file(cls, file_name: str) -> 'ProjectSchema':
        """Class method to create a new ProjectSchema instance from a YAML file."""
        instance = cls()
        instance.load_from_manifest(file_name)
        return instance

    def get_containers_by_type(self, container_type: ContainerType) -> List[Container]:
        """Return all containers of a specific type."""
        return [container for container in self.containers if container.container_type == container_type]

    def get_container_by_name(self, name: str) -> Optional[Container]:
        """Return container by name."""
        for container in self.containers:
            if container.container_name == name:
                return container
        return None

    def add_container(self, container: Container):
        """Add a container to the project."""
        self.containers.append(container)

    def remove_container(self, name: str) -> bool:
        """Remove a container by name. Returns True if removed, False if not found."""
        for i, container in enumerate(self.containers):
            if container.container_name == name:
                del self.containers[i]
                return True
        return False


def dataclass_to_llm_schema(dc_instance) -> dict[Any, Any] | None:
    """Convert a dataclass instance to a schema dict with descriptions."""
    if dc_instance is None:
        return None

    result = {}

    for field_info in fields(dc_instance):
        value = getattr(dc_instance, field_info.name)
        description = field_info.metadata.get("description", "")

        if isinstance(value, Enum):
            result[field_info.name] = value.value
        elif hasattr(value, "__dataclass_fields__"):
            nested_result = dataclass_to_llm_schema(value)
            if nested_result:
                result[field_info.name] = nested_result
        elif description:
            if value is None:
                result[field_info.name] = f"<{description}>"
            elif isinstance(value, list):
                if value and hasattr(value[0], "__dataclass_fields__"):
                    # List of dataclasses - process each one
                    list_result = []
                    for item in value:
                        item_result = dataclass_to_llm_schema(item)
                        if item_result:
                            list_result.append(item_result)
                    result[field_info.name] = list_result
                else:
                    result[field_info.name] = f"<list: {description}>"
            elif isinstance(value, dict):
                result[field_info.name] = f"<dict: {description}>"
            elif isinstance(value, str) and not value:
                result[field_info.name] = f"<string: {description}>"
            else:
                result[field_info.name] = value

    return result if result else None


def dict_to_project_schema(data: dict) -> ProjectSchema:
    """Convert dictionary response from LLM to typed ProjectSchema."""
    from dataclasses import fields, MISSING

    def dict_to_dataclass(data_class, data):
        if data is None:
            return None

        kwargs = {}
        for field in fields(data_class):
            field_value = data.get(field.name)
            field_type = field.type

            if hasattr(field_type, '__origin__') and field_type.__origin__ is Union:
                field_type = field_type.__args__[0] if field_type.__args__[1] is type(None) else field_type.__args__[1]

            # Handle enum types
            if hasattr(field_type, '__mro__') and Enum in field_type.__mro__:
                if field_value is not None:
                    try:
                        kwargs[field.name] = field_type(field_value)
                    except ValueError:
                        kwargs[field.name] = list(field_type)[0]
                else:
                    kwargs[field.name] = field.default if field.default is not MISSING else list(field_type)[0]
            elif hasattr(field_type, '__dataclass_fields__'):
                kwargs[field.name] = dict_to_dataclass(field_type,
                                                       field_value) if field_value else field.default_factory() if field.default_factory is not MISSING else None
            elif hasattr(field_type, '__origin__') and field_type.__origin__ is list:
                if field_value is not None:
                    list_item_type = field_type.__args__[0]
                    if hasattr(list_item_type, '__dataclass_fields__'):
                        kwargs[field.name] = [dict_to_dataclass(list_item_type, item) for item in field_value]
                    else:
                        kwargs[field.name] = field_value
                else:
                    kwargs[
                        field.name] = field.default_factory() if field.default_factory is not MISSING else field.default
            else:
                kwargs[
                    field.name] = field_value if field_value is not None else field.default_factory() if field.default_factory is not MISSING else field.default

        return data_class(**kwargs)

    return dict_to_dataclass(ProjectSchema, data)


def get_container_empty_work_item(container_type: ContainerType, container_details : ContainerDetails = None) -> Container:

    if container_type == ContainerType.FRONTEND:
        return Container(
            container_name='',
            description='',
            interfaces='',
            container_type=container_type,
            framework='',
            container_details=FrontendContainer() if container_details is None else container_details

        )
    elif container_type == ContainerType.BACKEND:
        return Container(
            container_name='',
            description='',
            interfaces='',
            container_type=container_type,
            framework='',
            container_details= container_details,
            auth=Auth()
        )
    elif container_type == ContainerType.MOBILE:
        return Container(
            container_name='',
            description='',
            interfaces='',
            container_type=container_type,
            container_details = container_details,
            framework=''
        )
    elif container_type == ContainerType.DATABASE:
        return Container(
            container_name='',
            description='',
            interfaces='',
            container_type=container_type,
            container_details = container_details,
            framework=''
        )
    else:
        return Container(
            container_name='',
            description='',
            interfaces='',
            container_type=container_type
        )


def convert_work_item_to_container_config(work_item_dict, container_type: ContainerType) -> Container:
    if container_type == ContainerType.FRONTEND:
        return Container(
            container_name=work_item_dict.get('component_name', ''),
            description=work_item_dict.get('description', ''),
            interfaces=work_item_dict.get('interfaces', ''),
            container_type=container_type,
            framework=work_item_dict.get('framework', 'React'),
            container_details=FrontendContainer(
                features=work_item_dict.get('algorithms', []) if isinstance(work_item_dict.get('algorithms'),
                                                                            list) else [],
                layout_description=work_item_dict.get('pseudocode', ''),
                theme='light'
            )
        )
    elif container_type == ContainerType.BACKEND:
        return Container(
            container_name=work_item_dict.get('component_name', ''),
            description=work_item_dict.get('description', ''),
            interfaces=work_item_dict.get('interfaces', ''),
            container_type=container_type,
            framework=work_item_dict.get('framework', 'FastAPI'),
            auth=Auth()
        )
    elif container_type == ContainerType.MOBILE:
        return Container(
            container_name=work_item_dict.get('component_name', ''),
            description=work_item_dict.get('description', ''),
            interfaces=work_item_dict.get('interfaces', ''),
            container_type=container_type,
            framework=work_item_dict.get('framework', 'Flutter')
        )
    elif container_type == ContainerType.DATABASE:
        return Container(
            container_name=work_item_dict.get('component_name', ''),
            description=work_item_dict.get('description', ''),
            interfaces=work_item_dict.get('interfaces', ''),
            container_type=container_type,
            framework=work_item_dict.get('framework', 'PostgreSQL')
        )
    else:
        return Container(
            container_name=work_item_dict.get('component_name', ''),
            description=work_item_dict.get('description', ''),
            interfaces=work_item_dict.get('interfaces', ''),
            container_type=container_type
        )


def create_project_schema_from_predefined(work_item, container_type: ContainerType, project_overview):
    """Create ProjectSchema directly from predefined work item without LLM"""

    schema = ProjectSchema(
        overview=ProjectOverview(
            project_name=project_overview.get("project_name", work_item.get('component_name', '')),
            description=project_overview.get("description", work_item.get('description', ''))
        )
    )

    container = convert_work_item_to_container_config(work_item, container_type)
    schema.add_container(container)

    return schema


def get_platform_from_container_type(container_type: ContainerType) -> ApplicationType:
    """
    Convert ContainerType to ApplicationType for platform usage.

    Args:
        container_type (ContainerType): The container type from project schemas

    Returns:
        ApplicationType: The corresponding application platform type
    """
    mapping = {
        ContainerType.FRONTEND: ApplicationType.WEB,
        ContainerType.BACKEND: ApplicationType.BACKEND,
        ContainerType.MOBILE: ApplicationType.MOBILE,
        ContainerType.DATABASE: ApplicationType.DATABASE
    }

    return mapping.get(container_type, ApplicationType.GENERIC)

def sync_to_all_workspaces(project: ProjectSchema, base_path: str):
    """
    Sync the project schema to all workspaces.
    """
    for container in project.containers:
        if container.workspace:
            workspace_path = os.path.join(base_path, container.workspace)
            sync_workspace_to_manifest(project, workspace_path, container.container_type, base_path)


def sync_workspace_to_manifest(
    project: ProjectSchema, 
    workspace: str, 
    project_type: ContainerType, 
    base_path: str = ""
) -> None:
    """
    Synchronize workspace configuration files to project manifest.
    
    Args:
        project: The project schema containing containers
        workspace: Path to the workspace directory
        project_type: Type of container to sync
        base_path: Base path for environment file resolution
    """
    print(f"Project type: {project_type}")
    
    # Get first container of specified type
    containers: list[Container] = project.get_containers_by_type(project_type)
    if not containers:
        print("ERROR: No containers found")
        return
    
    container = containers[0]
    if not container:
        print("ERROR sync_workspace_to_manifest: No project type found.")
        return
    
    # Sync workspace configuration files
    _sync_init_configs(workspace, container)
    
    # Sync environment variables
    _sync_environment_variables(base_path, container)


def _sync_init_configs(workspace: str, container: Container) -> None:
    """Synchronize .init configuration files to container settings."""
    init_dir = os.path.join(workspace, ".init")
    
    config_mappings = [
        (".init-run-tool", "startCommand"),
        (".init-build-cmd", "buildCommand"),
        (".init-install-dependencies", "installCommand"),
        (".init-generate-openapi", "generateOpenapiCommand"),
    ]
    
    print(f"Checking workspace: {workspace}")
    
    for filename, attr_name in config_mappings:
        _load_json_config(init_dir, filename, container, attr_name)
    
    linter_path = os.path.join(init_dir, ".linter.sh")
    if os.path.exists(linter_path) and not container.lintCommand:
        container.lintCommand = "./../.init/.linter.sh"

def _load_json_config(
    init_dir: str, 
    filename: str, 
    container: Container, 
    attr_name: str
) -> None:
    """
    Load a JSON configuration file and update container attribute if empty.
    
    Args:
        init_dir: Directory containing init files
        filename: Name of the JSON file to load
        container: Container object to update
        attr_name: Name of the container attribute to set
    """
    # Skip if attribute already has a value
    if getattr(container, attr_name, None):
        return
    
    filepath = os.path.join(init_dir, filename)
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
            command = data.get("command")
            if command:
                setattr(container, attr_name, command)
    except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
        print(f"Failed to load {filename}: {e}")

def _sync_environment_variables(base_path: str, container: Container) -> None:
    """Synchronize environment variables from .env file to container."""
    env_file_path = os.path.join(base_path, container.container_root, ".env")
    
    if not os.path.exists(env_file_path):
        return
    
    env_vars = dict(dotenv_values(env_file_path))
    if not env_vars:
        return

    if container.env is None:
        container.env = {}
    
    mapped_vars = _get_mapped_env_vars(container.framework, env_vars)
    
    container.env.update(mapped_vars)


def _get_mapped_env_vars(
    framework: Optional[str], 
    env_vars: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Map framework-specific environment variables to common format.
    
    Args:
        framework: The framework name (if any)
        env_vars: Original environment variables
        
    Returns:
        Mapped environment variables
    """
    if not framework:
        return env_vars
    
    try:
        return map_framework_to_common(framework, env_vars)
    except FileNotFoundError:
        print(f"Warning: Framework '{framework}' not found. Using raw env vars.")
        return env_vars

def update_project_env(project: ProjectSchema, env: Dict[str, str]):
    """
    Update the project environment variables.
    :param project: ProjectSchema instance
    :param env: Dictionary of environment variables to update
    """
    if not project.overview:
        return

    # Update project-level overview
    for key, value in env.items():
        project.overview.env[key] = value

    for container in project.containers:
        if container.env is None:
            container.env = {}

        for key, value in env.items():
            container.env[key] = value


if __name__ == "__main__":
    schema = ProjectSchema(
        overview=ProjectOverview(project_name="", description=""),
        containers=[
            get_container_empty_work_item(ContainerType.FRONTEND),
            get_container_empty_work_item(ContainerType.BACKEND),
            get_container_empty_work_item(ContainerType.MOBILE),
            get_container_empty_work_item(ContainerType.DATABASE)
        ]
    )
    schema.containers[0].container_name = "test_name"
    text_format = dataclass_to_llm_schema(schema)
    print(json.dumps(text_format, indent=2))





