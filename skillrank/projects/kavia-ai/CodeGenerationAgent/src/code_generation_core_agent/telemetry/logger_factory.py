import atexit
import os
import sys
import traceback

from code_generation_core_agent.config import config
from code_generation_core_agent.telemetry.datadog_logger import DatadogLogger

from code_generation_core_agent.config import config
from code_generation_core_agent.telemetry.datadog_logger import DatadogLogger


class LoggerFactory:
    _instance = None

    @staticmethod
    def get_logger():
        """
        Get the logger instance.
        """
        if LoggerFactory._instance is None:
            logger_type = config.get("LOGGING", "telemetry_implementation")
            if logger_type == "datadog":
                api_key = config.get("LOGGING", "datadog_api_key")
                app_key = config.get("LOGGING", "datadog_app_key")
                LoggerFactory._instance = DatadogLogger(api_key=api_key, app_key=app_key, environment=os.getenv("ENVIRONMENT", "dev"))
            else:
                raise ValueError(f"Unsupported logger type: {logger_type}")
        return LoggerFactory._instance

    @staticmethod
    def get_logger_session_id():
        """
        Get the logger session ID.
        """
        return LoggerFactory.get_logger().session_id

    @staticmethod
    def _setup_exception_handler():
        """
        Setup the exception handler to report unhandled exceptions.
        """
        sys.excepthook = LoggerFactory._exception_handler

    @staticmethod
    def setup_exit_handler(log_directory: str):
        """
        Setup the exit handler to upload logs to the telemetry server.
        """
        atexit.register(LoggerFactory._exit_handler, log_directory)
        LoggerFactory._setup_exception_handler()

    @staticmethod
    def _exit_handler(log_directory: str):
        if LoggerFactory._instance:
            LoggerFactory._close(log_directory)

    @staticmethod
    def _close(directory_path: str):
        json_log_dir = os.path.join(directory_path, "json")
       
        print("Please wait. Uploading non-empty log files to telemetry....")
        for filename in os.listdir(json_log_dir):
            if filename.endswith('.json.log'):
                file_path = os.path.join(json_log_dir, filename)
                if os.path.isfile(file_path) and os.path.getsize(file_path) > 0:
                    LoggerFactory.get_logger().close(file_path)

        print("Log upload complete.")

    @staticmethod
    def _exception_handler(exc_type, exc_value, exc_traceback):
        """
        Handle uncaught exceptions and report them to the telemetry server.
        """
        logger = LoggerFactory.get_logger()
        error_message = f"{exc_type.__name__}: {exc_value}"
        stack_trace = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.report_crash(error_message, stack_trace)

        sys.__excepthook__(exc_type, exc_value, exc_traceback)

