import os
import time
from datetime import datetime,timezone
from dateutil import parser
import json
from typing import Dict, Any, Union, List, Literal, Optional, Set

from datadog_api_client import ApiClient, Configuration
from datadog_api_client.v2.api.logs_api import LogsApi
from datadog_api_client.v2.model.http_log import <PERSON><PERSON><PERSON><PERSON>
from datadog_api_client.v2.model.http_log_item import HTTPLogItem
from datadog_api_client.v1.api.metrics_api import MetricsApi
from datadog_api_client.v1.model.series import Series
from datadog_api_client.v1.model.point import Point
from datadog_api_client.v1.model.metrics_payload import MetricsPayload

from code_generation_core_agent.config import config
from code_generation_core_agent.telemetry.base_logger import BaseLogger

APP_NAME = "code_generation_agent"


class DatadogLogger(BaseLogger):
    def __init__(self, api_key: str, app_key: str, environment: str = "dev"):
        """
        Initialize the Datadog logger with the given API and APP keys.

        :param api_key: The Datadog API key.
        :param app_key: The Datadog APP key.
        :param environment: The environment in which the agent is running.
             valid values are 'dev', 'prod', 'qa'    
        """
        super().__init__()
        self.configuration = Configuration()
        self.configuration.api_key['apiKeyAuth'] = api_key
        self.configuration.api_key['appKeyAuth'] = app_key
        self.configuration.server_variables["site"] = config.get("LOGGING", "datadog_server")
        self.environment = environment
        self._metrics_buffer: Dict[str, List[Dict[str, Any]]] = {}
        self._last_flush_time = time.time()
        self._flush_interval = 10  # seconds

        # Track which session metrics have already been sent to avoid duplication
        self._sent_session_metrics: Set[str] = set()

        # Track session start time for calculating session length
        self._session_start_time = time.time()
        self._session_ended = False
        
        # Test increment 2x the counter for LLM timeout
        self.report_event("llm.timeoutevent")
        self.report_event("llm.timeout.count")

    def close(self, log_file_path: str, log_type: str = "application", agent_type: str = None):
        """
        Close the logger and upload all logs to Datadog, supporting both JSON and plain text log formats.

        Args:
            log_file_path (str): Path to the log file to be uploaded
            log_type (str): Type of log (default: "application")
            agent_type (str, optional): Type of agent
        """
        with ApiClient(self.configuration) as api_client:
            api_instance = LogsApi(api_client)

            # Report final session metrics
            self._report_session_length()
            self.flush_metrics()

            log_items = []
            log_file_name = os.path.basename(log_file_path)
            current_time = datetime.now(timezone.utc)

            try:
                with open(log_file_path, 'r') as log_file:
                    for line in log_file:
                        line = line.strip()
                        if not line:
                            continue

                        # Default values
                        log_timestamp = current_time
                        message_dict = {}
                        agent_type_value = agent_type or "unknown"

                        try:
                            # Try to parse as JSON
                            log_json = json.loads(line)

                            # Try extracting and parsing timestamp
                            timestamp_str = log_json.get("timestamp")
                            try:
                                log_timestamp = parser.isoparse(timestamp_str).astimezone(timezone.utc) if timestamp_str else current_time
                            except Exception:
                                log_timestamp = current_time

                            message_dict = {
                                "message": log_json.get("message", "No message provided"),
                                "level": log_json.get("level", "INFO"),
                            }
                            # Add all other custom fields that might be present
                            for key, value in log_json.items():
                                if key not in ["message", "level", "timestamp", "agent_type"]:
                                    message_dict[key] = value
                            agent_type_value = log_json.get("agent_type", agent_type or "unknown")

                        except json.JSONDecodeError:
                            # Handle plain text line
                            message_dict = {
                                "message": line,
                                "level": "INFO"
                            }
                            log_timestamp = current_time
                            agent_type_value = agent_type or "unknown"

                        # Append to log items
                        log_items.append(
                            HTTPLogItem(
                                ddsource="python",
                                ddtags=f"env:{self.environment},session:{self.session_id},type:{log_type},log_file:{log_file_name},agent_type:{agent_type_value}",
                                message=json.dumps(message_dict),
                                service=APP_NAME,
                                timestamp=log_timestamp.isoformat(),
                                agent_type=agent_type_value
                            )
                        )

                # Upload logs in smaller batches
                batch_size = 1000
                for i in range(0, len(log_items), batch_size):
                    batch = log_items[i:i + batch_size]
                    try:
                        http_log = HTTPLog(batch)
                        api_response = api_instance.submit_log(http_log)
                        print(f"Successfully uploaded batch of {len(batch)} logs to Datadog")
                    except Exception as e:
                        print(f"Error uploading log batch to Datadog: {str(e)}")

                print("------->sessionid", self.session_id)

            except Exception as e:
                error_message = f"Error processing log file: {str(e)}"
                print(error_message)
                raise Exception(error_message)


    def report_crash(self, error_message: str, stack_trace: str, agent_type: str = None):
        with ApiClient(self.configuration) as api_client:
            api_instance = LogsApi(api_client)

            # Use provided agent_type or fall back to self.agent_type
            effective_agent_type = agent_type or 'unknown'

            body = HTTPLog(
                [
                    HTTPLogItem(
                        ddsource="python",
                        ddtags=f"env:{self.environment},session:{self.session_id},type:crash,status:ERROR, agent_type:{effective_agent_type}",
                        message=f"Error: {error_message}\n\nStack Trace:\n{stack_trace}",
                        service=APP_NAME,
                        agent_type=effective_agent_type
                    )
                ]
            )

            try:
                api_response = api_instance.submit_log(body)
                # Increment crash counter metric
            except Exception as e:
                error_message = f"Error uploading crash report: {str(e)}"
                print(error_message)
                raise Exception(error_message)

    def report_value(self, metric_name: str, value: Union[float, int],
                     tags: Optional[Dict[str, str]] = None,
                     metric_type: Literal["gauge", "count", "histogram"] = "gauge"):
        """
        Report a metric value to Datadog.

        :param metric_name: Name of the metric
        :param value: Value to record
        :param tags: Additional tags to associate with this metric
        :param metric_type: Type of metric (defaults to "gauge")
        """
        full_metric_name = f"{APP_NAME}.{metric_name}"
        self._add_metric(full_metric_name, value, metric_type, tags)
        self._check_flush_metrics()

    from datadog_api_client.v1.model.event_alert_type import EventAlertType

    def report_event(self, event_name: str, properties: Optional[Dict[str, Any]] = None,
                     alert_type: Optional[EventAlertType] = None):
        """
        Report a discrete event that occurred during the session.

        :param event_name: Name of the event
        :param properties: Additional properties to associate with this event
        :param alert_type: Optional alert type (EventAlertType.ERROR, EventAlertType.WARNING,
                          EventAlertType.INFO, EventAlertType.SUCCESS)
        """
        with ApiClient(self.configuration) as api_client:
            from datadog_api_client.v1.api.events_api import EventsApi
            from datadog_api_client.v1.model.event_create_request import EventCreateRequest

            api_instance = EventsApi(api_client)

            # Format timestamp
            current_time = int(time.time())

            # Build tags
            tags = [f"env:{self.environment}", f"session:{self.session_id}"]
            if properties:
                for key, value in properties.items():
                    # Convert properties to tags where appropriate
                    if isinstance(value, (str, int, float, bool)):
                        tags.append(f"{key}:{value}")

            # Create the event body
            event_body = EventCreateRequest(
                title=f"{APP_NAME}.{event_name}",
                text=f"Event: {event_name}" + (f"\nProperties: {properties}" if properties else ""),
                tags=tags,
                alert_type=alert_type,
                date_happened=current_time
            )

            try:
                api_response = api_instance.create_event(event_body)
                print(f"Event '{event_name}' reported successfully")
            except Exception as e:
                error_message = f"Error reporting event: {str(e)}"
                print(error_message)


    def record_gauge(self, metric_name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """
        Record a gauge metric (a single point-in-time value).

        :param metric_name: Name of the metric
        :param value: Value to record
        :param tags: Additional tags to associate with this metric
        """
        self._add_metric(f"{APP_NAME}.{metric_name}", value, "gauge", tags)
        self._check_flush_metrics()

    def record_histogram(self, metric_name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """
        Record a histogram metric (for calculating averages, percentiles, etc.).

        :param metric_name: Name of the metric
        :param value: Value to record
        :param tags: Additional tags to associate with this metric
        """
        self._add_metric(f"{APP_NAME}.{metric_name}", value, "histogram", tags)
        self._check_flush_metrics()

    def record_session_state(self, metric_name: str, value: Union[int, float],
                             tags: Optional[Dict[str, str]] = None):
        """
        Record a value that should be sent only once per session.
        This is useful for system state data that shouldn't be duplicated.

        :param metric_name: Name of the metric
        :param value: Value to record
        :param tags: Additional tags to associate with this metric
        """
        # Create a unique identifier for this metric in this session
        tag_str = ",".join(sorted([f"{k}:{v}" for k, v in (tags or {}).items()]))
        metric_key = f"{metric_name}:{tag_str}"

        # Only record this metric if we haven't sent it for this session already
        if metric_key not in self._sent_session_metrics:
            # Use gauge type for session state data
            self._add_metric(f"{APP_NAME}.{metric_name}", value, "gauge", tags)
            self._sent_session_metrics.add(metric_key)
            # Force flush to ensure this data is sent now
            self.flush_metrics()

    def _add_metric(self, metric_name: str, value: Union[int, float],
                    metric_type: Literal["gauge", "count", "histogram"],
                    tags: Optional[Dict[str, str]] = None):
        """
        Add a metric to the buffer for later submission.

        :param metric_name: Full name of the metric
        :param value: Value to record
        :param metric_type: Type of metric (gauge, count, histogram)
        :param tags: Additional tags to associate with this metric
        """
        timestamp = int(time.time())

        tag_list = [f"env:{self.environment}", f"session:{self.session_id}"]
        if tags:
            tag_list.extend([f"{k}:{v}" for k, v in tags.items()])

        if metric_name not in self._metrics_buffer:
            self._metrics_buffer[metric_name] = []

        self._metrics_buffer[metric_name].append({
            "points": [[timestamp, value]],
            "type": metric_type,
            "tags": tag_list
        })

    def _check_flush_metrics(self):
        """Check if it's time to flush metrics based on time or buffer size."""
        current_time = time.time()
        if current_time - self._last_flush_time >= self._flush_interval:
            self.flush_metrics()

    def flush_metrics(self):
        """Send all buffered metrics to Datadog."""
        if not self._metrics_buffer:
            return

        series_list = []

        for metric_name, metrics in self._metrics_buffer.items():
            for metric in metrics:
                # Skip using Point objects entirely - pass the points array directly to Series
                # This is the format Datadog expects according to their documentation
                series = Series(
                    metric=metric_name,
                    type=metric["type"],
                    points=metric["points"],
                    tags=metric["tags"]
                )
                series_list.append(series)

        with ApiClient(self.configuration) as api_client:
            api_instance = MetricsApi(api_client)

            # Create a proper MetricsPayload object
            body = MetricsPayload(series=series_list)

            try:
                api_response = api_instance.submit_metrics(body)
                print(f"Metrics submitted successfully: {len(series_list)} datapoints")
            except Exception as e:
                error_message = f"Error submitting metrics: {str(e)}"
                print(error_message)

        # Clear the buffer and update the last flush time
        self._metrics_buffer = {}
        self._last_flush_time = time.time()

    def _report_session_length(self):
        """
        Protected method to report the session length.
        This should only be called when the session is ending.
        """
        if not self._session_ended:
            self._session_ended = True
            session_length = time.time() - self._session_start_time
            # Report session length in seconds
            print("Session length:", session_length)
            self._add_metric(f"{APP_NAME}.session.length", session_length, "gauge")
            # Force a flush to ensure this metric is sent
            self.flush_metrics()

    def end_session(self):
        """
        End the current session and report its total length.
        This should be called when the application is shutting down cleanly.
        """
        self._report_session_length()

