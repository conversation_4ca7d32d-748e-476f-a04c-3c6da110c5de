import uuid
from abc import abstractmethod, ABC


class BaseLogger(ABC):
    def __init__(self):
        self.session_id = str(uuid.uuid4())

    @abstractmethod
    def close(self, log_file_path: str, log_type: str = "application"):
        pass

    @abstractmethod
    def report_crash(self, error_message: str, stack_trace: str):
        pass

    def get_session_id(self):
        return self.session_id
