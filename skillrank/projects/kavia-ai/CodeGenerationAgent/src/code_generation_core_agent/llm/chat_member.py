from enum import Enum


class ChatStatus(Enum):
    """
    ChatStatus is an enumeration of the possible statuses of a chat.
    """
    ACTIVE = "active"
    INACTIVE = "inactive"
    CLOSED = "closed"


class ChatMessageCallback:
    def __init__(self):
        pass
    def on_member_message(self, user_id: str, parent_id: str, message: str, chat_concluded: bool):
        """
        on_member_message is called when a message is received from the chat.
        args:
            user_id: str
                The user id of the sender
            parent_id: str
                The id of the parent message
            message: str
                The message
            chat_concluded: bool
        """
        pass


class ChatMember:
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.status = ChatStatus.ACTIVE

    def send_message(self, parent_id: str, message_id: str, message: str, attachments= None):
        """
        send_message sends a message to the chat member.
        """
        pass

    def get_user_id(self) -> str:
        """
        get_user_id returns the user id of the chat member.
        """
        return self.user_id

    def set_message_callback(self, callback: ChatMessageCallback):
        """
        set_message_callback sets the message callback.
        """
        pass