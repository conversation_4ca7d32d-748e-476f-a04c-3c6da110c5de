import asyncio
import logging
from typing import List

from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools
from code_generation_core_agent.agents.tools.strategic_analysis_tool import StrategicAnalysisTool
from code_generation_core_agent.agents.tools.search_and_download_tool import SearchAndDownloadTool
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.agents.tools.base_tool import ToolRegistry
from code_generation_core_agent.agents.tools.combined_tools import CombinedTools
from code_generation_core_agent.agents.tools.AgentPreProcessor import AgentPreProcessor
from code_generation_core_agent.agents.tools.tracking.shell_command_call_tracker import ShellCommandCall<PERSON>racker

def _get_executor(base_path):
    configuration = {
        "container_name": "kavia_default_container_image",
    }
    return DockerExecutor(session_dir=base_path, **configuration)

def test_tools(tool_instances, logger):
    """
    Test the tools by invoking their methods and logging the results.
    """
    for tool in tool_instances:
        logger.info(f"Testing tool: {tool.get_tool_name()}")
        try:
            if isinstance(tool, SearchAndDownloadTool):
                page_markdown = tool.generate_webpage_markdown("https://www.kavia.ai", "markdown")
                print(page_markdown)
        except Exception as e:
            logger.error(f"Error testing tool {tool.get_tool_name()}: {str(e)}")

def setup_dynamic_tool(
    request_context,
    llm,
    callback_functions,
    tools_logger,
    executor=None
):
    file_tools = ContainerFileTools(
        callback_functions,
        request_context['execution_base_path'],
        logger=tools_logger, llm=llm, executor=executor
    )
    strategy_tools = StrategicAnalysisTool(
        callback_functions,
        request_context['execution_base_path'],
        logger=tools_logger, llm=llm, executor=executor
    )
    search_and_download_tool = SearchAndDownloadTool(
        callback_functions,
        request_context['execution_base_path'],
        logger=tools_logger, llm=llm, executor=executor
    )

    tool_instances = [file_tools, strategy_tools, search_and_download_tool]

    agent_pre_processor = AgentPreProcessor(
        callback_functions,
        logger=tools_logger,
        shell_command_tracker=ShellCommandCallTracker(
            request_context['execution_base_path'])
    )

    combined_tool = CombinedTools(
        tool_instances,
        [agent_pre_processor],
        request_context['execution_base_path'],
        logger=tools_logger, llm=llm, executor=executor
    )

    return combined_tool

async def run_one_model(
    model: str,
    base_path: str,
    user_prompt: str,
):
    safe_model_name = model.replace("/", "_").replace(":", "_")
    print("\n" + "=" * 80)
    print(f"▶️  Starting streaming interface test for model: {model}")
    print("=" * 80 + "\n")

    # Fresh logger per model (same file path ok; names differ)
    tools_logger = setup_logger(f"tools_logger_{safe_model_name}", base_path)

    # New LLM interface per model sweep (same instance_name is fine)
    llm = LLMInterface(
        llm_api_key="dummy-api-key",
        session_dir=base_path,
        instance_name=f"test_instance_{model.replace('/', '_').replace(':','_')}"
    )

    # Callbacks
    callback_functions = {
        "terminal_output_callback": lambda x: print(f"Terminal Output: {x}"),
        "browser_output_callback": lambda x: print(f"Browser Output: {x}"),
        "user_query_callback": lambda x: print(f"User Query: {x}"),
        "function_call_callback": lambda x: print(f"Function Call: {x}"),
        "codeview_callback": lambda x: print(f"Code View: {x}"),
        "agent_message_callback": lambda x: print(f"Agent Message: {x}"),
    }

    request_context = {"execution_base_path": base_path}

    # Dedicated executor per model so we can cleanup reliably
    executor = _get_executor(base_path)

    try:
        # Tools wired to this LLM + executor
        combined_tools = setup_dynamic_tool(
            request_context=request_context,
            llm=llm,
            callback_functions=callback_functions,
            tools_logger=tools_logger,
            executor=executor
        )

        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": user_prompt},
                ]
            }
        ]

        # Stream response
        response = llm.llm_streaming_interaction_wrapper(
            messages=messages,
            user_prompt="",              # already included in messages
            system_prompt="",            # keep empty unless you want to override
            model=model,
            response_format={"type": "text"},
            function_schemas=combined_tools.function_schemas,
            function_executor=combined_tools.function_executor,
            max_retries=1,
        )

        async for chunk in response:
            print(chunk)

        print(f"\n✅ Streaming test complete for model: {model}\n")

    except Exception as e:
        tools_logger.exception(f"❌ Error while testing model {model}: {e}")
        print(f"❌ Error while testing model {model}: {e}")
    finally:
        # Best-effort cleanup
        try:
            await executor.cleanup_all_containers()
        except Exception as cleanup_err:
            tools_logger.warning(f"Cleanup warning for model {model}: {cleanup_err}")

async def main():
    # Choose a single base path you want to use for all runs
    base_path = "/Users/<USER>/Work/Kavia/workarea/kavia/codegen-tic-tac-toe-m"

    # The task you want each model to perform
    user_prompt = (
        "Please use the write tool to write a React/JS program for a tic-tac-toe game. "
        "Prior to writing code, please validate the plan using the StrategicAnalysis tool."
    )

    user_prompt = "say hello world"

    # List your models here; they'll be tested in order
    models: List[str] = [
        "gpt-4.1",
        "gpt-5",
        "o3",
        "claude-3-5-sonnet-20241022",
        "claude-sonnet-4-20250514",
        "azure/gpt-4.1",
        "azure/gpt-5",
        "azure/o3",
        "bedrock/converse/us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        #"bedrock/converse/us.anthropic.claude-sonnet-4-20250514-v1:0",
        "vertex_ai/gemini-2.5-pro",
        "vertex_ai/claude-sonnet-4",

    ]

    for model in models:
        await run_one_model(model=model, base_path=base_path, user_prompt=user_prompt)

if __name__ == "__main__":
    asyncio.run(main())