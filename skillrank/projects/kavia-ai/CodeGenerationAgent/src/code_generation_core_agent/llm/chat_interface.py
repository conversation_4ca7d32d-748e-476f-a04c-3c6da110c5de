from dataclasses import dataclass, field    
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Callable, Awaitable, AsyncGenerator, Any, Set
import uuid
from abc import ABC, abstractmethod


from code_generation_core_agent.chat.chat_worker_registry import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get_chat_worker_registry
from code_generation_core_agent.llm.chat_member import ChatMember


class MessageType(Enum):
    """Enumeration of possible message types in the chat system.

    Attributes:
        USER: Messages sent by users
        LLM: Responses from the language model
        TASK: Task-related messages
        SYSTEM: System-generated messages
        COMMAND: Command execution responses
    """
    USER = "user"
    LLM = "llm"
    TASK = "task"
    SYSTEM = "system"
    COMMAND = "command"
    ERROR = "error"
    BACKEND_SERVICE = "backend_service"


class MessageStatus(Enum):
    """Enumeration of possible message states in the chat system.

    Attributes:
        PENDING: Message is waiting to be processed
        STREAMING: Message content is currently streaming
        COMPLETED: Message has finished processing successfully
        FAILED: Message processing encountered an error
        NEEDS_RESPONSE: Message requires a response
        RESOLVED: Message has been resolved and requires no further action
    """
    PENDING = "pending"
    STREAMING = "streaming"
    COMPLETED = "completed"
    FAILED = "failed"
    NEEDS_RESPONSE = "needs_response"
    RESOLVED = "resolved"


@dataclass
class ChatMessage:
    """Represents a single message in the chat system.

    Attributes:
        id: Unique identifier for the message
        content: The actual message content
        msg_type: Type of the message (MessageType enum)
        timestamp: When the message was created
        status: Current status of the message (MessageStatus enum)
        requires_resolution: Whether this message needs to be resolved.
              This can be for PENDING or for NEEDS_RESPONSE type of messages.

        resolution_id: ID of the message that resolves this one, if any
        parent_id: ID of the parent message in a conversation chain
        metadata: Additional data associated with the message
    """
    id: str
    content: str
    msg_type: MessageType
    timestamp: datetime
    status: MessageStatus
    requires_resolution: bool = False
    resolution_id: Optional[str] = None
    parent_id: Optional[str] = None
    metadata: Dict = None
    attachments: List[Dict] = None
    extra: Dict = field(default_factory=dict)

    def __str__(self):
        """String representation of the ChatMessage instance."""
        return (f"ChatMessage(id={self.id}, type = {self.msg_type.value} content={self.content}, "
                f"status={self.status}), parent_id={self.parent_id}), "
                f"metadata={self.metadata}), timestamp={self.timestamp}),"
                f"requires_resolution={self.requires_resolution}), extra={self.extra})")


class ChatEventBus:
    """Centralized event bus for handling all chat events."""

    def __init__(self):
        self.messages: Dict[str, ChatMessage] = {}
        self.unresolved_messages: Dict[str, ChatMessage] = {}

        self._callbacks: Dict[str, List[Callable[..., Any]]] = {
            "message_added": [],
            "stream_chunk": [],
            "message_resolved": [],
            "needs_response": [],
            "command_executed": []
        }

    def add_callback(self, event_type: str, callback: Callable[..., Any]):
        if event_type not in self._callbacks:
            raise ValueError(f"Unknown event type: {event_type}")
        self._callbacks[event_type].append(callback)

    def trigger_event(self, event_type: str, *args):
        if event_type in self._callbacks:
            for callback in self._callbacks[event_type]:
                callback(*args)

    def resolve_message(self, message_id: str, resolution_content: Optional[str] = "Done") -> ChatMessage:
        """Resolve a message that requires resolution.

        Args:
            message_id: ID of message to resolve
            resolution_content: Optional content for resolution message

        Returns:
            ChatMessage: The resolved message

        Raises:
            ValueError: If message doesn't exist or doesn't require resolution
        """
        if message_id not in self.messages:
            raise ValueError(f"Message {message_id} not found")

        message = self.messages[message_id]

        resolution_message = ChatMessage(
            id=str(uuid.uuid4()),
            content=resolution_content,
            msg_type=MessageType.SYSTEM,
            timestamp=datetime.now(),
            status=MessageStatus.COMPLETED,
            parent_id=message_id
        )
        self.messages[resolution_message.id] = resolution_message
        message.resolution_id = resolution_message.id
        self.trigger_event("message_added", resolution_message)

        message.status = MessageStatus.RESOLVED
        if message_id in self.unresolved_messages:
            del self.unresolved_messages[message_id]

        self.trigger_event("message_resolved", message)
        return message


class CommandType(Enum):
    """Available command types in the chat system.

    Attributes:
        TASKS: Command to list pending tasks (/tasks)
        QUESTIONS: Command to list pending questions (/ql)
    """
    TASKS = "/tasks"
    QUESTIONS = "/ql"


class Command(ABC):
    """Abstract base class for implementing chat commands.

    Attributes:
        command_type: The type of command (CommandType enum)
    """

    def __init__(self, command_type: CommandType):
        self.command_type = command_type

    @abstractmethod
    def execute(self, chat_interface: 'BaseChatInterface') -> str:
        """Execute the command.

        Args:
            chat_interface: The chat interface instance to execute the command on

        Returns:
            str: The result of the command execution
        """
        pass


class TasksCommand(Command):
    """Command implementation for listing pending tasks.

    Returns a formatted string containing all unresolved tasks with their status
    and metadata.
    """

    def __init__(self):
        super().__init__(CommandType.TASKS)

    def execute(self, chat_interface: 'BaseChatInterface') -> str:
        """Execute the tasks command.

        Args:
            chat_interface: The chat interface to get tasks from

        Returns:
            str: Formatted list of pending tasks or "No pending tasks."
        """
        tasks = chat_interface.get_unresolved_messages()
        if not tasks:
            return "No pending tasks."

        result = "Pending Tasks:\n\n"
        for idx, task in enumerate(tasks, 1):
            status = "⏳ Pending" if task.status == MessageStatus.NEEDS_RESPONSE else "🔄 In Progress"
            result += f"{idx}. ID: {task.id}\n"
            result += f"   Status: {status}\n"
            result += f"   Content: {task.content}\n"
            if task.metadata:
                result += f"   Metadata: {task.metadata}\n"
            result += "\n"
        return result


class QuestionsListCommand(Command):
    """Command implementation for listing pending questions.

    Returns a formatted string containing all questions that need responses.
    """

    def __init__(self):
        super().__init__(CommandType.QUESTIONS)

    def execute(self, chat_interface: 'BaseChatInterface') -> str:
        """Execute the questions list command.

        Args:
            chat_interface: The chat interface to get questions from

        Returns:
            str: Formatted list of pending questions or "No pending questions."
        """
        questions = [msg for msg in chat_interface._event_bus.messages.values()
                     if msg.status == MessageStatus.NEEDS_RESPONSE]

        if not questions:
            return "No pending questions."

        result = "Pending Questions:\n\n"
        for idx, question in enumerate(questions, 1):
            result += f"{idx}. ID: {question.id}\n"
            result += f"   Asked at: {question.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
            result += f"   Question: {question.content}\n"
            if question.metadata:
                result += f"   Context: {question.metadata}\n"
            result += "\n"
        return result


class BaseChatInterface:
    """Base class providing common chat interface functionality.

    This class implements core message management and event handling features
    used by both user and LLM interfaces.

    Attributes:
        messages: Dictionary of all messages keyed by ID
        unresolved_messages: Dictionary of messages requiring resolution
        _callbacks: Dictionary of event handlers for different event types
    """

    def __init__(self, event_bus: ChatEventBus):
        self._event_bus = event_bus

    def _trigger_callbacks(self, event_type: str, *args):
        """Trigger all callbacks registered for an event type."""
        self._event_bus.trigger_event(event_type, *args)

    def get_unresolved_messages(self) -> List[ChatMessage]:
        """Retrieve all messages that still need resolution.

        Returns:
            List[ChatMessage]: List of unresolved messages
        """
        return list(self._event_bus.unresolved_messages.values())

    def get_message_chain(self, message_id: str) -> List[ChatMessage]:
        """Retrieve all messages in a conversation chain.

        Gets the complete chain of messages starting from the parent
        and including all responses.

        Args:
            message_id: ID of any message in the chain

        Returns:
            List[ChatMessage]: Ordered list of messages in the chain
        """
        if message_id not in self._event_bus.messages:
            return []

        chain = []
        current_id = message_id

        while current_id:
            message = self._event_bus.messages[current_id]
            chain.append(message)
            current_id = message.parent_id

        chain.reverse()

        responses = [msg for msg in self._event_bus.messages.values()
                     if msg.parent_id == message_id]
        chain.extend(responses)

        return chain

    def add_callback(self, event_type: str, callback: Callable[..., Any]):
        """Register a callback function for a specific event type.

        Args:
            event_type: Type of event to listen for
            callback: Function to call when event occurs

        Raises:
            ValueError: If event_type is not recognized
        """
        if event_type not in self._event_bus._callbacks:
            raise ValueError(f"Unknown event type: {event_type}")
        self._event_bus._callbacks[event_type].append(callback)


class UserChatInterface(BaseChatInterface):
    """Interface for handling user interactions in the chat system.

    Manages user message processing and command execution.

    Attributes:
        commands: Dictionary of available commands keyed by command string
    """

    def __init__(self, event_bus: ChatEventBus):
        super().__init__(event_bus)
        self.commands: Dict[str, Command] = {
            CommandType.TASKS.value: TasksCommand(),
            CommandType.QUESTIONS.value: QuestionsListCommand()
        }

    def send_message(self, content: str, parent_id: str, metadata: dict = {}, attachments=None, extra={}) -> ChatMessage:
        """Process user input, handling both commands and messages.

        Args:
            content: User input string

        Returns:
            ChatMessage: The processed message or command response
        """
        if content.strip() in self.commands:
            return self.execute_command(content.strip(), parent_id)

        return self.add_user_message(content, parent_id, metadata, attachments, extra)

    def execute_command(self, command_str: str, parent) -> ChatMessage:
        """Execute a chat command.

        Args:
            command_str: The command string to execute

        Returns:
            ChatMessage: Message containing the command response

        Raises:
            ValueError: If command_str is not a recognized command
        """
        if command_str not in self.commands:
            raise ValueError(f"Unknown command: {command_str}")

        command = self.commands[command_str]
        result = command.execute(self)

        message = ChatMessage(
            id=str(uuid.uuid4()),
            content=result,
            msg_type=MessageType.COMMAND,
            timestamp=datetime.now(),
            status=MessageStatus.COMPLETED,
            metadata={"command": command_str}
        )

        self._event_bus.messages[message.id] = message
        self._trigger_callbacks("command_executed", command_str, message)
        self._trigger_callbacks("message_added", message)
        return message

    def add_user_message(self, content: str, parent: str, metadata: dict = {},  attachments: List[Dict] = None, extra = {}) -> ChatMessage:
        """Add a new user message to the chat.

        Args:
            content: Message content

        Returns:
            ChatMessage: The created message
        """
        message = ChatMessage(
            id=str(uuid.uuid4()),
            content=content,
            msg_type=MessageType.USER,
            timestamp=datetime.now(),
            status=MessageStatus.PENDING,
            requires_resolution=True,
            parent_id=parent,
            metadata=metadata,
            attachments=attachments or [],
            extra=extra
        )

        self._event_bus.messages[message.id] = message
        self._event_bus.unresolved_messages[message.id] = message
        self._trigger_callbacks("message_added", message)
        if parent:
            self._event_bus.resolve_message(parent)
        return message

    def add_user_response(self, content: str, parent_id: str, attachments= [], extra = {}) -> ChatMessage:
        """Add a user response to a specific message.

        Args:
            content: Response content
            parent_id: ID of the message being responded to

        Returns:
            ChatMessage: The created response message

        Raises:
            ValueError: If parent_id doesn't exist or is already resolved
        """
        if parent_id not in self._event_bus.messages:
            raise ValueError(f"Parent message {parent_id} not found")

        parent_message = self._event_bus.messages[parent_id]
        if parent_message.status == MessageStatus.RESOLVED:
            raise ValueError(f"Message {parent_id} is already resolved")

        message = ChatMessage(
            id=str(uuid.uuid4()),
            content=content,
            msg_type=MessageType.USER,
            timestamp=datetime.now(),
            status=MessageStatus.COMPLETED,
            parent_id=parent_id,
            attachments=attachments or [],
            extra=extra
        )

        self._event_bus.messages[message.id] = message
        self._trigger_callbacks("message_added", message)
        return message


class LLMChatInterface(BaseChatInterface):
    """Interface for handling LLM (Language Learning Model) interactions.

    Manages system messages, LLM responses, and message resolution.
    """

    def dispatch_question_to_user(self, content: str, metadata: Optional[Dict] = None) -> ChatMessage:
        """Handle incoming system message.

        Args:
            content: Message content
            metadata: Optional metadata for the message

        Returns:
            ChatMessage: The created system message
        """
        message = ChatMessage(
            id=str(uuid.uuid4()),
            content=content,
            msg_type=MessageType.SYSTEM,
            timestamp=datetime.now(),
            status=MessageStatus.NEEDS_RESPONSE,
            requires_resolution=True,
            metadata=metadata or {},
        )

        self._event_bus.messages[message.id] = message
        self._event_bus.unresolved_messages[message.id] = message
        self._trigger_callbacks("message_added", message)
        self._trigger_callbacks("needs_response", message)
        return message

    def dispatch_error_message(self, content: str) -> ChatMessage:
        """Handle incoming error message.

        Args:
            content: Error message content

        Returns:
            ChatMessage: The created error message
        """
        message = ChatMessage(
            id=str(uuid.uuid4()),
            content=content,
            msg_type=MessageType.ERROR,
            timestamp=datetime.now(),
            status=MessageStatus.COMPLETED
        )

        self._event_bus.messages[message.id] = message
        self._trigger_callbacks("message_added", message)
        return message

    def send_message(self, content: str, status: MessageStatus, parent_id=None,
                     message_type: MessageType = MessageType.LLM, attachments: List[Dict] = None, extra = None) -> ChatMessage:
        """Handle incoming error message.

        Args:
            content: The message content to send.
            status: The status of the message (e.g., COMPLETED, NEEDS_RESPONSE).
            parent_id: Optional ID of the parent message in the conversation chain.
            message_type: The type of message (default is MessageType.LLM).
            attachments: Optional list of attachments for the message.
            extra: Optional dictionary for additional data.

        Returns:
            ChatMessage: The created and sent message.
        """

        requires_resolution = True if (status.value == MessageStatus.NEEDS_RESPONSE.value
                                       or status.value == MessageStatus.PENDING.value) else False
        message = ChatMessage(
            id=str(uuid.uuid4()),
            content=content,
            msg_type=message_type,
            timestamp=datetime.now(),
            status=status,
            parent_id=parent_id,
            requires_resolution=requires_resolution,
            attachments=attachments or [],
            extra=extra or {}
        )

        self._event_bus.messages[message.id] = message
        self._trigger_callbacks("message_added", message)
        return message

    async def stream_llm_response(self, stream: AsyncGenerator[str, None],
                                  parent_id: Optional[str] = None) -> str:
        """Handle streaming LLM response.

        Args:
            stream: Async generator yielding response chunks
            parent_id: Optional ID of message being responded to

        Returns:
            str: ID of the created message

        Raises:
            Exception: If streaming fails
        """
        message_id = str(uuid.uuid4())
        message = ChatMessage(
            id=message_id,
            content="",
            msg_type=MessageType.LLM,
            timestamp=datetime.now(),
            status=MessageStatus.STREAMING,
            parent_id=parent_id
        )

        self._event_bus.messages[message_id] = message
        self._trigger_callbacks("message_added", message)

        try:
            async for chunk in stream:
                message.content += chunk
                self._trigger_callbacks("stream_chunk", message, chunk)

            #message.status = MessageStatus.COMPLETED
            #self._trigger_callbacks("message_added", message)

        except Exception as e:
            message.status = MessageStatus.FAILED
            message.metadata = {"error": str(e)}
            self._trigger_callbacks("message_added", message)
            raise

        return message_id

    def need_response(self, message_id: str):
        """Mark a message as needing a response.

        Args:
            message_id: ID of message requiring a response
        """
        message = self._event_bus.messages[message_id]
        message.status = MessageStatus.NEEDS_RESPONSE

        self._trigger_callbacks("needs_response", message)


class ChatInterface:
    """Main chat interface coordinating between user and LLM interfaces.

    This is the primary class that should be instantiated by users of the library.
    It provides access to all chat functionality through a unified interface.

    Example:
        chat = ChatInterface()
        message = chat.process_input("Hello!")

        async def handle_llm_response():
            async def sample_stream():
                yield "Hello"
                yield " world!"
            await chat.stream_llm_response(sample_stream())

    Attributes:
        user_interface (UserChatInterface): Handles user interactions and commands
        llm_interface (LLMChatInterface): Handles LLM responses and system messages
    """

    def __init__(self):
        """Initialize the chat interface with shared event bus."""
        self._event_bus = ChatEventBus()
        self.user_interface = UserChatInterface(self._event_bus)
        self.llm_interface = LLMChatInterface(self._event_bus)
        self.chat_members = set()

    def get_user_interface(self) -> UserChatInterface:
        """Get the user interface instance.

        Returns:
            UserChatInterface: The user interface instance
        """
        return self.user_interface

    def add_callback(self, event_type: str, callback: Callable[..., Any]):
        """Register a callback for events."""
        self._event_bus.add_callback(event_type, callback)

    def _handle_event(self, event_type: str, *args):
        """Central event handler that distributes events to all registered callbacks."""
        for callback in self._event_bus._callbacks[event_type]:
            callback(*args)

    def process_input(self, content: str, parent_id: str = None, metadata: dict = {}, attachments: List[Dict] = None, extra= None) -> ChatMessage:
        """Process user input through the user interface.

        This is the main entry point for handling user messages and commands.

        Args:
            content: The user input string to process

        Returns:
            ChatMessage: The processed message or command response

        Example:
            chat = ChatInterface()
            # Process a regular message
            msg = chat.process_input("Hello, how are you?")
            # Process a command
            tasks = chat.process_input("/tasks")
        """
        return self.user_interface.send_message(content, parent_id, metadata, attachments, extra=extra)

    def get_message(self, message_id: str) -> Optional[ChatMessage]:
        """Retrieve a message by its ID from either interface.

        Args:
            message_id: The ID of the message to retrieve

        Returns:
            Optional[ChatMessage]: The message if found, None otherwise

        Example:
            message = chat.get_message("message-uuid")
            if message:
                print(f"Found message: {message.content}")
        """
        return self.messages.get(message_id)

    @property
    def messages(self) -> Dict[str, ChatMessage]:
        """Get all messages from both interfaces.

        Returns:
            Dict[str, ChatMessage]: Combined dictionary of all messages

        Example:
            all_messages = chat.messages
            print(f"Total messages: {len(all_messages)}")
        """
        return self.user_interface._event_bus.messages

    @property
    def unresolved_messages(self) -> Dict[str, ChatMessage]:
        """Get all unresolved messages from both interfaces.

        Returns:
            Dict[str, ChatMessage]: Combined dictionary of all unresolved messages

        Example:
            pending = chat.unresolved_messages
            print(f"Pending messages: {len(pending)}")
        """
        return {**self._event_bus.unresolved_messages}

    async def stream_llm_response(self, stream: AsyncGenerator[str, None],
                                  parent_id: Optional[str] = None) -> str:
        """Stream an LLM response through the LLM interface.

        This is the only asynchronous method in the interface.

        Args:
            stream: Async generator yielding response chunks
            parent_id: Optional ID of the message being responded to

        Returns:
            str: ID of the created message

        Raises:
            Exception: If streaming fails

        Example:
            async def handle_response():
                async def sample_stream():
                    yield "Hello"
                    yield " world!"
s
                message_id = await chat.stream_llm_response(
                    sample_stream(),
                    parent_id="some-message-id"
                )
        """
        return await self.llm_interface.stream_llm_response(stream, parent_id)

    def resolve_message(self, message_id: str, resolution_content: Optional[str] = None):
        self._event_bus.resolve_message(message_id, resolution_content)

    def add_chat_user(self, chat_member: ChatMember):
        self.chat_members.add(chat_member)

    def _find_new_chat_members(self):
        get_registry = get_chat_worker_registry()

        for worker in get_registry.get_all_workers():
            if worker.get_name() not in self.chat_members:
                self.add_chat_user(worker)

    def find_user(self, user_id: str) -> ChatMember | None:
        self._find_new_chat_members()

        for member in self.chat_members:
            if member.get_user_id() == user_id:
                return member

        return None
