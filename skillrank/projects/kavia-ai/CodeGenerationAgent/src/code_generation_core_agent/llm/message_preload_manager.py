from typing import List


class MessagePreloadManager:
    """
    This class is responsible for managing the preloading of messages.
    """

    def __init__(self, logger):
        self.llm_logger = logger

        pass

    def preload_messages(self, messages, preloaded_files: List[str], function_executor):
        """
        Preload files into the messages list before sending them to the LLM.
        Inserts preloaded file messages after the last system message.
        """
        if not preloaded_files:
            return messages

        preloaded_files = list(set(preloaded_files))

        function_result = function_executor("ContainerFileTools_read_files", {
            'file_paths': preloaded_files,
            'reason': "Preloading files for the LLM. This call doesn't cost any credits.",
        }
       )
        append_messages = []
        if function_result.get("status") == "SUCCESS" or function_result.get("status") == "PARTIAL_SUCCESS":
            for result in function_result.get("results", []):
                status = result.get("status", "FAILURE")
                file_name = result.get("path", "")
                content = result.get("content", "")

                self.llm_logger.info(f"Preloaded file: {file_name} with status: {status}")
                if status == "SUCCESS":
                    append_messages.append(
                        {
                            "role": "system",
                            "content": f"Preloaded file: '{file_name}'",
                            "file_name": file_name,
                            "note:": "This file always contains the latest version of the file!",
                            "file_content": content,
                            "is_preloaded": True,
                            "msg_type": "file_preload"
                        }
                    )
                else:
                    append_messages.append(
                        {
                            "role": "system",
                            "content": f"Ignored. Failed to preload file: '{file_name}'. It may not exist.",
                            "file_name": file_name,
                                "msg_type": "file_preload"
                            }
                    )

        # Remove any existing file_preload messages
        messages[:] = [msg for msg in messages if msg.get("msg_type") != "file_preload"]

        # Find the index of the last system message
        last_system_index = -1
        for i, msg in enumerate(messages):
            if msg.get("role", "") == "system":
                last_system_index = i

        # Insert preloaded messages after the last system message
        if last_system_index >= 0:
            messages[last_system_index + 1:last_system_index + 1] = append_messages
        else:
            # If no system messages found, append to the beginning
            messages[0:0] = append_messages

        return messages
