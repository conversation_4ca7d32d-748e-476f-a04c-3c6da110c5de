import os
import threading

from flask import Flask, render_template, request, jsonify, send_from_directory
import datetime

from werkzeug.utils import secure_filename
from flask_socketio import Socket<PERSON>, emit
from code_generation_core_agent.llm.chat.simulated_chat import SimulatedChatSystem
from queue import Queue
from threading import Thread

from code_generation_core_agent.tools.backend_service_extractor import ServiceExtractor

current_dir = os.path.dirname(os.path.abspath(__file__))
app = Flask(__name__, template_folder=os.path.join(current_dir, 'template'))
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Create event queue for handling callbacks
event_queue = Queue()
chat = None


# Configure upload settings
from code_generation_core_agent.config import config
def get_upload_folder():
    session_dir = os.path.join(
        config.get("SYSTEM", "workspace_dir"),
        "kavia",
        f"codegen-{os.getenv('SESSION_ROOT', '1157')}"
    )
    upload_folder = os.path.join(session_dir, "chat_uploads")
    os.makedirs(upload_folder, exist_ok=True)
    return upload_folder
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'py', 'js', 'json', 'yaml', 'yml', 'html', 'css', 'xml', 'zip', 'tar', 'gz', 'tgz', 'mp4', 'avi', 'mov', 'mkv'}    


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_file(file):
    # Check file size (e.g., 10MB limit)
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes
    
    if file.content_length > MAX_FILE_SIZE:
        return False, "File size exceeds limit"
    
    # Check file type
    if not allowed_file(file.filename):
        return False, "File type not allowed"
    
    # Basic content validation
    try:
        content = file.read(1024)  # Read first 1024 bytes
        file.seek(0)  # Reset file pointer
        
        # Check for executable content
        if content.startswith(b'MZ') or content.startswith(b'#!'):
            return False, "Executable files not allowed"
            
    except Exception as e:
        return False, f"File validation error: {str(e)}"
    
    return True, "File is valid"

@app.route('/api/upload_attachment', methods=['POST'])
def upload_attachment():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    # Validate file
    is_valid, message = validate_file(file)
    if not is_valid:
        return jsonify({'error': message}), 400
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Add timestamp to filename to prevent duplicates
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_")
        unique_filename = timestamp + filename
        upload_folder = get_upload_folder()
        filepath = os.path.join(upload_folder, unique_filename)
        file.save(filepath)
        return jsonify({
            'success': True,
            'filename': unique_filename,
            'originalName': filename,
            'path': filepath,
            'size': os.path.getsize(filepath)
        })
    return jsonify({'error': 'File type not allowed'}), 400

@app.route('/chat_uploads/<path:filename>')
def serve_uploaded_file(filename):
    return send_from_directory(get_upload_folder(), filename)

class RestThread(threading.Thread):
    def __init__(self, app):
        threading.Thread.__init__(self)
        self.app = app
        self.daemon = True

    def run(self):
        self.app.run(debug=True, use_reloader=False, host='0.0.0.0', port=5002)

class FlaskThread(threading.Thread):
    def __init__(self, app, socketio, chat_interface, simulator = None):
        threading.Thread.__init__(self)
        self.app = app
        self.socketio = socketio
        self.chat = chat_interface
        self.simulator = simulator
        self.daemon = True

    def run(self):
        # Register callbacks
        self.chat.add_callback("stream_chunk", handle_stream_chunk)
        self.chat.add_callback("message_added", handle_message_added)
        self.chat.add_callback("message_resolved", handle_message_resolved)
        self.chat.add_callback("needs_response", handle_message_needs_response)

        try:
            self.socketio.run(self.app, use_reloader=False, host='0.0.0.0', port=5003)
        finally:
            if self.simulator:
                self.simulator.stop()
                event_queue.join()

def message_to_dict(message):
    """Convert a ChatMessage to a dictionary for JSON serialization."""
    return {
        'id': message.id,
        'content': message.content,
        'msg_type': message.msg_type.value,
        'timestamp': message.timestamp.isoformat(),
        'status': message.status.value,
        'requires_resolution': message.requires_resolution,
        'resolution_id': message.resolution_id,
        'parent_id': message.parent_id,
        'metadata': message.metadata or {},
        'attachments': message.attachments or [],
        'extra': message.extra or {}
    }

@app.route('/api/ping', methods=['GET', 'POST'])
def ping():
    print("PING RECEIVED")
    return "pong"

@app.route('/api/send_message', methods=['POST'])
def send_message():
    try:
        data = request.get_json(force=True)

        if not data or 'content' not in data:
            return jsonify({'error': 'Missing content in request'}), 400

        # Add validation for content
        if data['content'] is None or not isinstance(data['content'], str):
            return jsonify({'error': 'Content must be a non-empty string'}), 400

        # Ensure content is not empty after stripping
        content = data['content'].strip()
        if not content:
            return jsonify({'error': 'Content cannot be empty'}), 400
        
        attachments = data.get('attachments', [])
        print("DEBUG: Received attachments:", attachments)
        message = chat.process_input(content, data.get('parent_id', None), metadata=data.get('metadata', {}),
            attachments=attachments  # Pass attachments to process_input)
        )

        # Validate message object
        if message is None:
            return jsonify({'error': 'Failed to process message'}), 500

        event_queue.put(('message_added', message_to_dict(message)))

        if message.requires_resolution:
            event_queue.put(('needs_response', message_to_dict(message)))
        return jsonify(message_to_dict(message))

    except Exception as e:
        error_response = {'error': f"Server error: {str(e)}"}
        event_queue.put(('error', error_response))
        return jsonify(error_response), 500

def handle_stream_chunk(message, chunk):
    """Queue streaming chunks from LLM responses."""
    event_queue.put(('message_chunk', {
        'msg_type': message.msg_type.value,
        'id': message.id,
        'content': chunk,
        'status': message.status.value,
        'parent_id': message.parent_id
    }))

def handle_message_status(message):
    """Queue message status updates."""
    event_queue.put(('message_status', {
        'id': message.id,
        'status': message.status.value,
        'parent_id': message.parent_id
    }))

def handle_message_resolved(message):
    """Queue message resolution."""
    event_queue.put(('message_resolved', {
        'id': message.id,
        'resolution_id': message.resolution_id,
        'status': message.status.value,
        'parent_id': message.parent_id
    }))

def handle_message_added(message):
    """Queue new messages."""
    event_queue.put(('message_added', message_to_dict(message)))

def handle_message_needs_response(message):
    """Queue messages that require a response."""
    event_queue.put(('needs_response', message_to_dict(message)))


def event_processor():
    """Process events from the queue and emit them via socketio."""
    while True:
        try:
            event_type, data = event_queue.get()
            with app.app_context():
                socketio.emit(event_type, data)
            event_queue.task_done()
        except Exception as e:
            print(f"Error processing event: {e}")

# Start event processor thread
event_thread = Thread(target=event_processor, daemon=True)
event_thread.start()

@app.route('/')
def index():
    return render_template('index.html')


# Restart a backend service with a given name
@app.route('/api/start_backend', methods=['POST'])
def start_backend():
    service_manager = ServiceExtractor.get_instance_or_none()
    if not service_manager:
        return jsonify({'status': 'error', 'error': 'Service manager not found'})

    data = request.get_json(force=True)
    service_name = data.get('service_name')
    if service_name:
        service_manager.start_service(service_name)
        return jsonify({'status': 'success'})

@app.route('/api/get_backend_service_status', methods=['GET'])
def get_backend_service_status():
    service_manager = ServiceExtractor.get_instance_or_none()

    if not service_manager:
        return jsonify({'status': 'error', 'error': 'Service manager not found'})

    status = service_manager.get_service_status()
    response = {'status': 'success'}
    response.update(status)
    return jsonify(response)



@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    # Send existing messages to new client
    messages = [message_to_dict(m) for m in chat.messages.values()]
    emit('initial_messages', {'messages': messages})

@socketio.on('send_message')
def handle_message(data):
    """Handle incoming messages from client."""
    try:
        message = chat.process_input(data['content'], data.get('parent_id'),metadata=data.get('metadata', {}), attachments = data.get('attachments', []), extra = data.get('extra', {}))
    except Exception as e:
        emit('error', {'error': str(e)})

@socketio.on('execute_command')
def handle_command(data):
    """Handle command execution."""
    try:
        message = chat.user_interface.execute_command(data['command'], data.get('parent_id'))
        emit('command_response', message_to_dict(message))
    except Exception as e:
        emit('error', {'error': str(e)})


def start_flask_server(chat_interface, simulator=None):
    """
    Helper function to start Flask server in a separate thread
    """
    global chat
    chat = chat_interface

    ws_thread = FlaskThread(app, socketio, chat, simulator)
    ws_thread.start()

    rest_thread = RestThread(app)
    rest_thread.start()

    return ws_thread, rest_thread
