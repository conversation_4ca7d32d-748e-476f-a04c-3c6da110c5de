from code_generation_core_agent.llm.chat_interface import *

import asyncio
from datetime import datetime
from typing import AsyncGenerator


async def simulate_llm_response(prompt: str) -> AsyncGenerator[str, None]:
    """Simulate an LLM streaming response"""
    words = f"Simulated LLM response to: {prompt}".split()
    for word in words:
        await asyncio.sleep(0.2)  # Simulate network delay
        yield word + " "


async def main():
    # Initialize the chat interface
    chat = ChatInterface()

    # Add some example callbacks
    def on_message_added(message: ChatMessage):
        print(f"\nNew message [{message.msg_type.value}]: {message.content}")

    def on_stream_chunk(message: ChatMessage, chunk: str):
        print(chunk, end="", flush=True)

    def on_message_resolved(message: ChatMessage):
        print(f"\nResolved message: {message.id}")

    # Register callbacks
    chat.add_callback("message_added", on_message_added)
    chat.add_callback("stream_chunk", on_stream_chunk)
    chat.add_callback("message_resolved", on_message_resolved)

    # Simulate user interaction
    print("\n=== Starting Chat Session ===\n")

    # Example 1: Simple user message and LLM response
    user_msg = chat.process_input("Hello, can you help me with Python programming?")
    print(f"User message ID: {user_msg.id}")

    # Simulate LLM response
    response_id = await chat.stream_llm_response(
        simulate_llm_response(user_msg.content),
        parent_id=user_msg.id
    )

    # Example 2: System message requiring resolution
    system_msg = chat.llm_interface.dispatch_question_to_user(
        "Please verify your Python environment version.",
        metadata={"priority": "high"}
    )

    # Use process_input from main ChatInterface for the response
    user_response = chat.process_input("I'm using Python 3.9")

    # Resolve the system message
    chat.resolve_message(
        system_msg.id,
        "Environment version verified: Python 3.9"
    )

    # Example 3: Command execution
    tasks_result = chat.process_input("/tasks")
    questions_result = chat.process_input("/ql")

    # Example 4: Message chain retrieval
    print("\nMessage chain for initial conversation:")
    chain = chat.user_interface.get_message_chain(user_msg.id)
    for msg in chain:
        print(f"- [{msg.msg_type.value}] {msg.content}")
        print(f"  ID: {msg.id}")
        print(f"  Parent ID: {msg.parent_id}")
        print()

    print("\n=== Chat Session Ended ===")


if __name__ == "__main__":
    asyncio.run(main())