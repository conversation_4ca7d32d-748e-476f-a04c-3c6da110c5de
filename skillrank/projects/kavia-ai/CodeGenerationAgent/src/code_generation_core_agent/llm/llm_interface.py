# --------------------------------------------------------------------------------
# Company Name: Kavia AI
# Author: <PERSON><PERSON><PERSON>
# Creation Date: Year (2024)
#
# Confidential Information of Kavia AI
# NOTICE: All information contained herein is, and remains the property of Kavia AI.
# The intellectual and technical concepts contained herein are proprietary to Kavia AI
# and may be covered by U.S. and Foreign Patents, patents in process, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material is strictly forbidden unless prior written permission is obtained
# from Kavia AI. Access to this file is granted on the condition that it will not be used for any purpose other than as specifically authorized
# in writing by Kavia AI, and subject to the terms of any agreement governing such access and use. Access to this file may also require
# a signed Non-Disclosure Agreement.
#
# --------------------------------------------------------------------------------

import asyncio
import collections
import logging
import os
import re
import threading
import time

from datetime import datetime
from typing import Dict, List, Any, Callable, AsyncGenerator, Union
import litellm
from litellm import ChatCompletionMessageToolCall, BadRequestError, ContextWindowExceededError, completion_cost
from dotenv import load_dotenv

from code_generation_core_agent.agents.framework.cost_tracker import CostTracer
from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator, SummarizerType
from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.chat.chat_enabled_worker import ChatEnabledWorker, ChatContextProvider
from code_generation_core_agent.config import config
from code_generation_core_agent.llm.message_preload_manager import MessagePreloadManager
from code_generation_core_agent.telemetry.logger_factory import LoggerFactory

load_dotenv()

import json
from litellm import ModelResponse

from dataclasses import dataclass


DEBUG_LLM = config.getboolean("LLM", "debug", fallback=False)

if DEBUG_LLM:
    litellm._turn_on_debug() 


@dataclass
class FunctionContext:
  id: str
  function_name: str
  function_arguments: Dict[str, Any]

litellm.set_verbose = config.getboolean('LLM', 'debug')

litellm.drop_params=True

def custom_stdout_logger(messages):
    print("litellm stdout logger: ", messages)


class MaxRetriesError(Exception):
    """Raised when the maximum number of retries is reached."""

    def __init__(self, message="Maximum number of retries reached", attempts=None):
        self.attempts = attempts
        self.message = message
        super().__init__(self.message)

    def __str__(self):
        if self.attempts is not None:
            return f"{self.message} after {self.attempts} attempts"
        return self.message


def is_valid_name(name: str) -> bool:
    """Check if a name matches the required pattern."""
    pattern = re.compile(r'^[a-zA-Z0-9_-]+$')
    return bool(pattern.match(name))


def append_cache_message(model, messages, logger):

    if (model is None or not model.startswith("claude")):
        return

    count = 0

    message_idx = 0

    for idx in range(len(messages)):
        message = messages[idx]
        content_list = message.get('content', [])
        content_list = content_list if content_list is not None else []
        for content in content_list:
            if isinstance(content, dict) and 'cache_control' in content:
                count += 1
                message_idx = idx

    total_message_length = sum(
        len(str(msg).encode('utf-8')) for msg in messages
    )
    # Calculate the total bytes of non-cache messages after message_idx
    total_bytes = sum(
        len(str(msg).encode('utf-8')) for msg in messages[message_idx + 1:]
    )

    from code_generation_core_agent.agents.micro.agent_system import MAX_NUMBER_OF_CACHE_CONTROL_MESSAGES

    threshold = config.getint('LLM', 'auto_prompt_caching_chunk_size')
    if count < MAX_NUMBER_OF_CACHE_CONTROL_MESSAGES and total_bytes >= threshold:
        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "This is an ephemeral cache control message."
                            "It is used to manage the cache state of the conversation. Continue.",
                    "cache_control": {
                        "type": "ephemeral"
                    }
                }
            ]
        })
        logger.info("Appended new ephemeral_cache message to messages list.")
        print("Appended ephemeral_cache message to messages list. Count: ", count, "Total bytes: ", total_bytes, " messages length: ", len(messages))
    else:
        print("Not appending ephemeral_cache message to messages list. Count: ", count, "Total bytes: ", total_bytes, " messages length: ", len(messages))
        logger.info(f"Not appending ephemeral_cache message to messages list. Count: {count}, Total bytes: {total_bytes}, messages length: {len(messages)}")


def prune_image_url_messages(
    messages: List[Dict[str, Any]],
    keep_first: bool = False,
) -> List[Dict[str, Any]]:
    """
    Return a *new* messages list with every message that contains an
    image_url payload removed.

    Parameters
    ----------
    messages   : full chat history you are about to cache
    keep_first : if True, the very first image message is kept but its
                 image part is stripped; all later ones are dropped.

    Why a copy?
    -----------
    Mutating the list you are iterating over can bite you (and may break
    references elsewhere).  Returning a fresh list is safer.
    """
    def _contains_image_url(msg: Dict[str, Any]) -> bool:
        """Detect OpenAI-style image messages."""
        if "content" not in msg:
            return False

        content = msg["content"]

        # Chat/image format: list of blocks
        if isinstance(content, list):
            return any(
                isinstance(block, dict) and block.get("type") == "image_url"
                for block in content
            )

        # Tool-response format: JSON string or dict
        if isinstance(content, str):
            try:
                payload = json.loads(content)
            except Exception:
                return False
        elif isinstance(content, dict):
            payload = content
        else:
            return False

        return "image_url" in payload or "screenshot" in payload

    pruned: List[Dict[str, Any]] = []
    seen_image = False

    for msg in messages:
        if _contains_image_url(msg):
            if keep_first and not seen_image:
                seen_image = True
                # Keep the message but *strip* the image block to save tokens
                new_msg = json.loads(json.dumps(msg))  # cheap deep-copy

                if isinstance(new_msg["content"], list):
                    new_msg["content"] = [
                        block
                        for block in new_msg["content"]
                        if not (
                            isinstance(block, dict)
                            and block.get("type") == "image_url"
                        )
                    ]
                else:  # tool string / dict
                    try:
                        payload = (
                            json.loads(new_msg["content"])
                            if isinstance(new_msg["content"], str)
                            else new_msg["content"]
                        )
                        payload.pop("image_url", None)
                        new_msg["content"] = (
                            json.dumps(payload)
                            if isinstance(new_msg["content"], str)
                            else payload
                        )
                    except Exception:
                        pass

                pruned.append(new_msg)
            # else: drop message entirely
        else:
            pruned.append(msg)

    return pruned


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, dict):
            return {k: v for k, v in obj.items() if not k.startswith('_')}

        elif isinstance(obj, ChatCompletionMessageToolCall):
            return self.encode_tool_call(obj)

        elif hasattr(obj, '__dict__'):
            obj_dict = obj.__dict__
            if 'function' in obj_dict and hasattr(obj_dict['function'], '__dict__'):
                return self.encode_tool_call(obj)
            else:
                return {k: v for k, v in obj_dict.items() if not k.startswith('_')}

        elif isinstance(obj, ModelResponse):
            return self.encode_model_response(obj)

        elif isinstance(obj, (str, int, float, bool, type(None))):
            return obj

        elif hasattr(obj, 'model_extra'):  # LiteLLM structure
            return self.encode_litellm_tool_call(obj)

        return super().default(obj)

    def encode_tool_call(self, obj):
        tool_call_id = getattr(obj, 'id', '')
        tool_call_type = getattr(obj, 'type', '')
        function = getattr(obj, 'function', None)
        if isinstance(function, dict):
            function_name = function.get('name', '')
            function_arguments = function.get('arguments', '')
        elif hasattr(function, '__dict__'):
            function_name = getattr(function, 'name', '')
            function_arguments = getattr(function, 'arguments', '')
        else:
            function_name = ''
            function_arguments = ''

        return {
            "id": tool_call_id,
            "type": tool_call_type,
            "function": {
                "name": function_name,
                "arguments": function_arguments
            }
        }

    def encode_litellm_tool_call(self, obj):
        model_extra = obj.model_extra
        function_obj = model_extra.get('function', {})

        tool_call_id = model_extra.get('id', '')
        tool_call_type = model_extra.get('type', '')
        function_name = function_obj.get('name', '')
        function_arguments = function_obj.get('arguments', '')

        return {
            "id": tool_call_id,
            "type": tool_call_type,
            "function": {
                "name": function_name,
                "arguments": function_arguments
            }
        }

    def encode_model_response(self, obj):
        return {
            "id": obj.id,
            "choices": obj.choices,
            "created": obj.created,
            "model": obj.model,
            "usage": obj.usage
        }

from typing import Callable, TypeVar, Protocol

from enum import Enum

class ValidationStatus:
    def __init__(self, state: str, completion_message: str, assistant_message: str):
        self.state = state  # complete - complete, partial - only partial content is available, 'has_error' - has some errors that would need repeating, or 'failed' - completely failed
        self.completion_message = completion_message
        self.assistant_message = assistant_message  # The assistant message if available
class OutputValidation(Protocol):
    async def __call__(self, output: str, is_complete: bool) -> ValidationStatus:
        """Validate output and return validation status.
        Args:
            output: The output to validate
            is_complete: Whether this is a complete response
        Returns:
            ValidationStatus object containing:
                - state: 'complete', 'partial', or 'failed'
                - completion_message: Overall status/error message. This would be the user message to the LLM
                - assistant_message: The assistant message to be returned to the LLM
        """
        

class MessageFormatter(logging.Formatter):
    def format(self, record):
        if isinstance(record.msg, (dict, list)):
            record.msg = json.dumps(record.msg, indent=2, cls=CustomJSONEncoder)
        elif isinstance(record.msg, str):
            try:
                # Try to parse the string as JSON
                parsed_json = json.loads(record.msg)
                # If successful, format it as pretty-printed JSON
                record.msg = json.dumps(parsed_json, indent=2, cls=CustomJSONEncoder)
            except json.JSONDecodeError:
                # If not JSON, apply the original formatting
                if '\n' in record.msg:
                    record.msg = self.format_multiline_string(record.msg)
                else:
                    record.msg = record.msg.strip()
        return super().format(record)

    def format_multiline_string(self, msg: str) -> str:
        lines = msg.split('\n')
        formatted_lines = [lines[0]] + [f"    {line}" for line in lines[1:]]
        return '\n'.join(formatted_lines)


def _save_current_state(args):
    # Replace keys in the args with ****
    saved_args = args.copy()
    saved_args['api_key'] = '*********'
    # Create a tempfile in /tmp/kavia
    # Save the args to the tempfile
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', delete=False, dir=config.get("SYSTEM", "workspace_dir")) as f:
        f.write(str(saved_args))
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print(f"      Saved last LLM args to {f.name}")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")

    pass


class LLMInterface(ChatContextProvider):
    def get_chat_context(self) -> Any:
        with self._message_lock:
            return self.current_messages

    def set_action(self, action: str):
        """
        set_action is called to set the action for the chat.
        """
        print("LLM-> injected action: ", action, " to the chat worker: ", self.instance_name)
        self.injected_actions.append(action)


    def __init__(self, llm_api_key, session_dir, instance_name, token_limit=None,
                 chat_worker: ChatEnabledWorker = None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.force_completion = None
        self.interrupt_reason = ""
        self.interrupt_requested = False
        self.instance_name = instance_name
        self.chat_worker = chat_worker
        if (self.chat_worker):
            self.chat_worker.set_context_provider(self)

        self.injected_actions = []

        self.llm_logger = setup_logger(f'llm_{instance_name}',
                                             session_dir)
        self.logger_factory = LoggerFactory.get_logger();

        self.preload_handler = MessagePreloadManager(self.llm_logger)

        self.client = litellm
        self.token_limit = token_limit
        self.prompt_tokens = 0
        self.completion_tokens = 0
        self.total_tokens = 0
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        self.message_summarizer = OutputTruncator(max_size=80000,
                                                  summarizer_type=SummarizerType.LLM_INTERACTION_SUMMARIZER,
                                                  logger=self.llm_logger)

        self.current_messages = {}

        self._lock = threading.Lock()
        self._message_lock = threading.Lock()
        self.main_loop_lock = threading.Lock()

        if config.getboolean('LLM', 'debug'):
            self.verbose_llm_logger = custom_stdout_logger
        else:
            self.verbose_llm_logger = None  
        self.stop_stream_requested = False
        self._stream_lock = threading.Lock()

    def stop_streaming(self):
        with self._stream_lock:
            self.stop_stream_requested = True

    def summarize_messages(self, messages):

        if (len(messages) <= 5 or
                (len(str(messages)) / 3) < config.getint("LLM",
                                                         "llm_wrapper_summarization_token_threshold")):  # Minimum threshold to avoid unnecessary summarization
            return messages
        considerations = next((msg['content'] for msg in messages[0:2] if msg.get('role') == 'user'), '')

        previous_summary = next((msg.get('content') for msg in messages if 'is_summary' in msg), None)

        start_index = 2

        # Do not summarize the first two messages and all the assistant messages
        for i in range(2, len(messages)):
            if type(messages[i]) == dict and messages[i].get('role') == 'assistant':
                start_index += 1
            else:
                break
        first_messages = messages[:start_index]

        preserved_tool_calls = config.getint('LLM', 'preserved_tool_calls')

        last_tool_calls_start_index = len(messages)

        for i, message in enumerate(reversed(messages)):
            if 'role' in message and message['role'] != 'tool' or message.get('tool_call_id') is None:
                preserved_tool_calls -= 1
                last_tool_calls_start_index = len(messages) - i - 1  # Keep the last message that is not a tool call

            if preserved_tool_calls == 0:
                break

        messages_to_summarize = messages[start_index:last_tool_calls_start_index]

        if not messages_to_summarize:
            return messages

        # Summarize messages
        summary = self.message_summarizer.truncate_output(
            messages_to_summarize,
            previous_summary,
            considerations,
            allways_truncate=True
        )

        # Create new message set
        new_messages = first_messages + [
            {'role': 'user', 'content': summary, 'is_summary': True}
        ] + messages[last_tool_calls_start_index:]  # Keep all messages from the last set of tool calls

        return new_messages

    def interrupt_task(self, interrupt_reason, remaining_calls):
        with self._lock:
            self.interrupt_requested = True
            self.interrupt_reason = interrupt_reason
            if remaining_calls <= 0:
                self.force_completion = True

    def is_interrupt_requested(self):
        with self._lock:
            return self.interrupt_requested, self.interrupt_reason

    def update_warning_messages(self, messages, remaining_calls, warning_threshold) -> None:
        """
        Update the messages list in place with a warning message if
        the remaining calls are below a certain threshold.
        """
        warning_prefix = "Warning: Only "

        interrupt_msg = ""

        if self.interrupt_requested:
            interrupt_msg = f" Interrupt of work flow requested by user: ```{self.interrupt_reason}```"

        # Remove existing warning messages
        messages[:] = [msg for msg in messages
                       if not (msg.get("role") == "system" and
                               msg.get("content", "").startswith(warning_prefix))]

        if warning_threshold >= remaining_calls > 0:
            warning_message = f"{warning_prefix}{remaining_calls} tool call(s) remaining." + interrupt_msg

            # Reset interrupt request. The interrupt is delivered to the LLM at this point
            self.interrupt_reason = ""
            self.interrupt_requested = False

            if remaining_calls == 1:
                warning_message += " This is your last function call. No more function calls after this call."
            messages.append({"role": "system", "content": warning_message})

        elif remaining_calls == 0:
            warning_message = "You have exhausted the allowed number of tools calls in a session. **Mandatory** - do not make any more tool calls. If you make any more tools call that will result in critical failure. Please complete the request and create the final output with the information that you have."
            messages.append({"role": "user", "content": warning_message})

    def _setup_llm_environment_vars(self, response_format, model, kwargs):
        kwargs["max_completion_tokens"] = 8000
        if 'bedrock' in model.lower():
            litellm.modify_params = True
            self.aws_access_key = os.getenv("CODEGEN_AWS_ACCESS_KEY_ID", os.getenv("AWS_ACCESS_KEY_ID"))
            self.aws_secret_key = os.getenv("CODEGEN_AWS_SECRET_ACCESS_KEY", os.getenv("AWS_SECRET_ACCESS_KEY"))
            self.aws_region = os.getenv("CODEGEN_AWS_REGION_NAME") or os.getenv('AWS_REGION_NAME', 'us-west-2')

            if not all([self.aws_access_key, self.aws_secret_key]):
                self.llm_logger.warning("AWS credentials not found. Some features may not work.")

            os.environ["AWS_ACCESS_KEY_ID"] = self.aws_access_key
            os.environ["AWS_SECRET_ACCESS_KEY"] = self.aws_secret_key
            os.environ["AWS_REGION_NAME"] = self.aws_region
        # Handle Vertex AI Claude models
        elif "vertex_ai/claude" in model.lower():
            kwargs["max_completion_tokens"] = 16000

            if os.getenv("GOOGLE_PROJECT_ID") and os.getenv("GOOGLE_LOCATION"):
                kwargs["vertex_ai_project"] = os.getenv("GOOGLE_PROJECT_ID")
                kwargs["vertex_ai_location"] = os.getenv("GOOGLE_LOCATION")
                if os.getenv("GOOGLE_APPLICATION_CREDENTIALS"):
                    kwargs["vertex_credentials"] = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
            else:
                raise ValueError(
                    "Google Cloud project ID and location not found. Please set the GOOGLE_PROJECT_ID and GOOGLE_LOCATION environment variables.")
        # Handle response_format for different model types
        elif ("gpt" in model.lower() or "o3" in model.lower()) and not 'azure' in model.lower():
            # For OpenAI models, include response_format
            kwargs["response_format"] = response_format
            kwargs["max_completion_tokens"] = 32000
            if "gpt-5" in model.lower():
                kwargs["reasoning_effort"] = config.get("LLM", "openai_reasoning_effort", fallback="low")
                self.llm_logger.info(f"Using OpenAI reasoning effort: {kwargs['reasoning_effort']}")
            elif "o3" in model.lower():
                effort = config.get("LLM", "openai_reasoning_effort", fallback="low")
                # O3 supports only supports low reasoning effort and up
                if effort == "minimal":
                    effort = "low"
                kwargs["reasoning_effort"] = effort
                self.llm_logger.info(f"Using OpenAI reasoning effort: {kwargs['reasoning_effort']}")
            if self.openai_api_key:
                kwargs["api_key"] = self.openai_api_key
            else:
                raise ValueError("OpenAI API key not found. Please set the OPENAI_API_KEY environment variable.")
        elif "claude" in model.lower():
            if "sonnet-4" in model.lower():
                kwargs["max_completion_tokens"] = 16000
            if self.anthropic_api_key:
                kwargs["api_key"] = self.anthropic_api_key
            else:
                raise ValueError(
                    "Anthropic API key not found. Please set the ANTHROPIC_API_KEY environment variable.")

        elif "gemini" in model.lower():
            kwargs["max_completion_tokens"] = 8000
            if os.getenv("GOOGLE_PROJECT_ID") and os.getenv("GOOGLE_LOCATION"):
                kwargs["google_project"] = os.getenv("GOOGLE_PROJECT_ID")
                kwargs["google_location"] = os.getenv("GOOGLE_LOCATION")
            else:
                raise ValueError(
                    "Google Cloud project ID and location not found. Please set the GOOGLE_PROJECT_ID and GOOGLE_LOCATION environment variables.")

        elif "azure" in model.lower():
            if os.getenv("AZURE_API_BASE") and os.getenv("AZURE_API_KEY") and os.getenv("AZURE_API_VERSION"):
                kwargs["api_base"] = os.getenv("AZURE_API_BASE")
                kwargs["api_key"] = os.getenv("AZURE_API_KEY")
                kwargs["api_version"] = os.getenv("AZURE_API_VERSION")
            else:
                raise ValueError(
                    "Azure api_base, api_key or api_version not found. Please set the AZURE_API_BASE, AZURE_API_KEY and AZURE_API_VERSION environment variables.")
        elif "openrouter" in model.lower():
            if os.getenv("OPENROUTER_API_KEY"):
                kwargs["api_key"] = os.getenv("OPENROUTER_API_KEY")
            else:
                raise ValueError(
                    "OpenRouter API key not found. Please set the OPENROUTER_API_KEY environment variable.")

    async def llm_interaction_wrapper(
            self,
            messages: List[Dict[str, Any]],
            user_prompt: str,
            system_prompt: str,
            model: str,
            response_format: Dict[str, Any],
            function_schemas: List[Dict[str, Any]] = None,
            function_executor: Callable = None,
            function_validator: Callable = None,
            stream: bool = False,
            user_id: str = 'admin',
            max_retries: int = 5,
            max_tool_calls: int = config.getint('LLM', 'max_tool_calls'),
            preload_files: bool = config.getboolean('LLM', 'preload_files'),
            warning_threshold: int = 5,
            validation_function: Callable = None,
            preloaded_files: List[str] = None,
            enable_cache: bool = False,
            output_validator: OutputValidation = None,
    ) -> ModelResponse:
        """
        Asynchronously interacts with a language model, handling various aspects of the interaction.

        Args:
            messages (list): A list of message dictionaries representing the conversation history.
            user_prompt (str): The current user's input prompt.
            system_prompt (str): The system prompt to guide the model's behavior.
            model (str): The name of the language model to use.
            response_format (dict): Specifies the desired format of the model's response.
            function_schemas (list, optional): A list of function schemas for function calling. Defaults to None.
            function_executor (callable, optional): A function to execute called functions. Defaults to None.
            stream (bool, optional): Whether to stream the response. Defaults to False.
            user_id (str, optional): Identifier for the user. Defaults to 'admin'.
            max_retries (int, optional): Maximum number of retries for rate limit errors. Defaults to 5.
            max_tool_calls (int, optional): Maximum number of tool calls allowed. Defaults to max_tool_calls cpmfog.
            warning_threshold (int, optional): Remaining tool calls threshold for displaying a warning message. Defaults to 5.
            validation_function (callable, optional): A function to validate the model's response. Defaults to None.
            preloaded_files (list of preloaded files, optional): A list of preloaded files to be added to the messages. Defaults to None.
        Returns:
            dict: The model's response, including content and any function calls.

        Functionality:
        - Processes and sanitizes input messages
        - Prepares prompts and handles system messages
        - Configures model parameters based on the specified model
        - Supports function calling with provided schemas and executor
        - Implements error handling and retries for rate limiting
        - Manages API keys for different model providers
        - Tracks token usage and calculates costs
        - Handles JSON parsing and validation for JSON responses
        - Provides detailed logging of the interaction
        - Supports both normal and streaming response modes

        Raises:
            ValueError: If required API keys are missing.
            MaxRetriesError: If the maximum number of retries is reached due to rate limiting or other errors.
            Exception: For various error conditions, including rate limit errors and JSON parsing failures.

        Note:
        This function is designed to be flexible and robust, handling many common scenarios in LLM interactions.
        It's particularly useful for applications requiring detailed control over LLM API calls.
        """
        # Lock main loop
        with self.main_loop_lock:
            return await self._llm_interaction_wrapper_locked(messages, user_prompt, system_prompt, model,
                                                              response_format,
                                                              function_schemas, function_executor, function_validator,
                                                              stream, user_id,
                                                              max_retries, max_tool_calls, preload_files,
                                                              warning_threshold,
                                                              validation_function, preloaded_files, enable_cache, output_validator)


    def blocking_call_wrapper(self, messages, function_schemas, model, response_format, stream, tool_choice: str):
        """
        Wrapper function to make blocking calls to the LLM API.

        Parameters:
        - tool_choice (str): The mode for tool calls. Defaults to "auto".
            Valid values are "auto" and "none".
        """
        append_cache_message(model, messages, self.llm_logger)

        self.llm_logger.info("++++++++ BEGIN LLM INPUT++++++++", extra={"log_type": "llm_input","event_type": "begin"})
        self.llm_logger.info(f"model:{model};format:{response_format};stream:{stream}")
        for message in messages:
            tool_calls = message.get('tool_calls', None)

            if tool_calls:
                for tool_call in tool_calls:
                    if isinstance(tool_call, dict):
                        original_name = tool_call.get('function', {}).get('name', '')
                        tool_call['function']['name'] = original_name.replace('.', '_')
                        function_name = tool_call['function']['name']
                        function_args = tool_call['function'].get('arguments', {})
                    else:
                        original_name = tool_call.function.name
                        tool_call.function.name = original_name.replace('.', '_')
                        function_name = tool_call.function.name
                        function_args = tool_call.function.arguments

                    if '.' in original_name:
                        print(
                            f"BUG: Function name '{original_name}' contains a dot.")
                    
                    self.llm_logger.info(f"Function call {function_name} with args: {function_args}", extra={"log_type": "function", "function_name": function_name, "event_type": "begin"})
            else:
                self.llm_logger.info(json.dumps(message, indent=2, cls=CustomJSONEncoder))

        self.llm_logger.info("++++++++++ END LLM INPUT+++++++++", extra={"log_type": "llm_input", "event_type": "end"})
        kwargs = {
            "messages": messages,
            "model": model,
            #"response_format": response_format,
            #"temperature": 0,
            "custom_llm_provider": CostTracer.get_provider(model),
            "stream": stream,
            "logger_fn": self.verbose_llm_logger,
            "timeout": 240, # sets a 5 minute timeout
            "stream_timeout": 30, # sets a 30s timeout for streaming calls
        }
        if stream:
            kwargs["stream_options"] = {"include_usage": True}

        if function_schemas is not None:
            kwargs["tools"] = function_schemas

        if function_schemas is not None and tool_choice == "auto":
            kwargs["tool_choice"] = tool_choice

        self._setup_llm_environment_vars(response_format, model, kwargs)

        try:
            return self.client.completion(**kwargs)
        except (BadRequestError, ContextWindowExceededError) as e:
            self.llm_logger.error(f"Error in LLM interaction: {e}")
            import traceback
            print("Last exception: ", str(traceback.format_exc()))
            _save_current_state(kwargs)
            raise e

    def llm_wrapper_start(self, messages, system_prompt, user_prompt, user_id, model, function_schemas):
        
        self.force_completion = False
        self.model = model

        self.llm_logger.info("+++++++++++++++++ BEGIN LLM SESSION ++++++++",extra={"log_type": "llm_session","event_type": "begin"})
        self.llm_logger.info(f"Function schemas: {json.dumps(function_schemas, indent=2)}")
        # Clear any previous interrupt requests
        self.interrupt_reason = ""
        self.interrupt_requested = False

        if model is None:
            raise ValueError("Model name is required.")

        # Prepare the system message

        if system_prompt:            
            messages.append({
                "role": "system",
                "created_at": datetime.now().isoformat(),
                "content": system_prompt
            })

        if user_prompt:            
            messages.append(
                {
                    "role": "user",
                    "user_id": user_id,
                    "created_at": datetime.now().isoformat(),
                    "content": user_prompt
                })

        from code_generation_core_agent.agents.prompt_helper import prompt_prepare_messages
        #prompt_prepare_messages(messages)
        return messages

        
    async def invoke_llm(self, messages, function_schemas, model, response_format, stream, max_retries, tool_choice: str = "auto"):
        attempt = 0
        last_exception = None

        while attempt < max_retries:
            try:
                return await self.loop.run_in_executor(None, self.blocking_call_wrapper, messages, function_schemas, model, response_format, stream,
                                                    tool_choice)
            except Exception as e:
                self.llm_logger.error(f"Error in LLM interaction (attempt {attempt + 1}/{max_retries}): {e}")
                import traceback
                last_exception = str(traceback.format_exc())
                print("Traceback: ", last_exception)
                wait_time = 2 ** attempt
                # Get exception name
                exception_name = type(e).__name__
                self.logger_factory.report_event("llm_interface." + exception_name)
                self.llm_logger.info(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
                attempt += 1

        self.llm_logger.error("Max retries hit, failing with last error.")
        if (last_exception):
            self.llm_logger.error(last_exception)
            print("Last exception: ", last_exception)

        self.logger_factory.report_event("llm_interface.max_retries_exceeded." + last_exception)

        raise MaxRetriesError(message=f"Max retries ({max_retries}) reached", attempts=attempt)
    
    def prepare_to_invoke_llm(self, messages, preload_files, preloaded_files, function_executor, max_tool_calls, tool_calls_count, warning_threshold, tool_call_log):
        
        forced_completition, result = self.check_force_completion(tool_call_log, model=self.model)
        remaining_calls = max_tool_calls - tool_calls_count

        if forced_completition:
            print("LLM FORCED COMPLETION. ", self)
            self.mock_completion = result
            return messages, True, remaining_calls

        # Preload files if the config parameter "preload_files" is set to true

        if preload_files:
            messages = self.preload_handler.preload_messages(messages,
                                                                preloaded_files,
                                                                function_executor)

        messages = self.summarize_messages(messages)

        

        interrupt_request, interrupt_reason = self.is_interrupt_requested()

        if interrupt_request:
            self.llm_logger.info(f"Interrupt requested: {self.interrupt_reason}")
            remaining_calls = 1

        if remaining_calls < 0:
            msg = (f"Error: Maximum number of tool calls reached.")
            self.llm_logger.error(msg)
            raise MaxRetriesError(message=msg, attempts=tool_calls_count)


        if self.chat_worker and self.injected_actions:
                injected_user_actions = str(self.injected_actions)
                self.injected_actions = []
                print("---- Injected user actions: ", injected_user_actions)
                messages.append(
                    {
                        "role": "user",
                        "content": "Additional user requests:" + str(injected_user_actions)
                    }
                )
                # Give an additional tool call for the injected user actions
                remaining_calls += 1

        # Notify LLM that we are running out of tool calls
        self.update_warning_messages(messages, remaining_calls, warning_threshold)
        # With thread lock set current messages
        with self._message_lock:
            self.current_messages = messages

        if self.token_limit is not None and self.total_tokens >= self.token_limit:
            self.llm_logger.info(f"Reached token limit of {self.token_limit}, stopping.\n")
            return messages, True, remaining_calls
        
        return messages, False, remaining_calls
        
    def update_completion_stats(self, response):
        usage = response.usage

        prompt_tokens = usage.prompt_tokens
        completion_tokens = usage.completion_tokens

        self.prompt_tokens += prompt_tokens
        self.completion_tokens += completion_tokens
        total_tokens = prompt_tokens + completion_tokens
        self.total_tokens += total_tokens

        CostTracer().add_cost_from_completion_response(self.instance_name, response)
            
        self.llm_logger.info(
                    f"Usage: Prompt Tokens: {prompt_tokens}, Completion Tokens: {completion_tokens}, Tokens: {total_tokens}")
        self.llm_logger.info(
                    f"Total input tokens {self.prompt_tokens}, Total output tokens {self.completion_tokens}, Total tokens {self.total_tokens} ")

    
    async def validate_output(self, messages, completion,output_validator, response_format, json_retry_count, max_json_retries, validation_function):

        response_messages = completion.choices[0].message
        finish_reason = completion.choices[0].finish_reason
        current_output = response_messages.content

        #if current_output is None:
        if not current_output: #if there is nothing to validate
            is_complete = True if finish_reason == "stop" else False
            self.llm_logger.info("Current output is None")
            return current_output, is_complete, messages
        else:   
            self.llm_logger.info(f"Content to validate: {finish_reason} - " + current_output)

        is_complete = finish_reason == "stop" # stop means the completion is complete
        
        if output_validator:  #use the new output validator if it is provided
            validation_result = await output_validator(current_output, is_complete)
            if validation_result is None:
                self.llm_logger.error("Validation result is None")
                validation_result = ValidationStatus(
                    state="failed",
                    completion_message="Validation failed - null result",
                    assistant_message=""
                )
            self.llm_logger.info(f"Validation result: {validation_result.state}")
            self.llm_logger.info(f"Validation message: {validation_result.completion_message}")

            if validation_result.state == "failed":
                # Handle validation failure
                # Handle validation failure
                messages.append({
                    "role": "assistant",
                    "content": validation_result.assistant_message
                })
                messages.append({
                    "role": "user",
                    "content": f"Previous output validation failed: {validation_result.completion_message}\n. Please take corrective actions, which may have to go beyond the current writes/edits."
                })
                return current_output, False, messages
            elif validation_result.state == "partial":
                # Handle partial validation
                
                messages.append({
                    "role": "assistant",
                    "content": validation_result.assistant_message,
                    "partial": "yes"
                })
                messages.append({
                            "role": "user",
                            "content": f"Please continue from where you left off. When resuming output after a partial response, only output new file operations those were not included in previous outputs. "
                            f"Do not repeat any file operations already provided, other than those marked as invalid. "
                            f"In case of build errors, files have already been written, so make addtional changes to fix the build errors. "
                            f"You may need to take additional actions to fix these issues (like fixing preexisting failures or installing packages, etc.). "
                            f"Here are the results from the last operations:\n {validation_result.completion_message}"
                        })
                return validation_result.assistant_message, False, messages
            else:  # complete
                accumulated_response = validation_result.assistant_message
        else:
            accumulated_response = current_output

        # Check if JSON output was requested
        if response_format and response_format.get("type") == "json_object":

            json_response = self.ensure_json_response(current_output)

            if "error" in json_response:
                accumulated_response = ""
                self.llm_logger.warning(
                    f"Failed to parse LLM response as JSON: {json_response['original_response']}")
                if json_retry_count < max_json_retries:
                    json_retry_count += 1
                    self.llm_logger.info(
                        f"Retrying JSON generation (Attempt {json_retry_count}/{max_json_retries})")

                    # Add the last message from LLM to the conversation
                    messages.append({"role": "assistant", "content": current_output})

                    # Add an error message to the conversation
                    error_message = f"The previous response was not valid JSON. Please generate a valid JSON response. Error: {json_response['error']}"
                    messages.append({"role": "user", "content": error_message})

                    return accumulated_response, False, messages
                else:
                    msg = f"Max JSON generation retries reached."
                    self.llm_logger.error(msg)
                    raise MaxRetriesError(message=msg, attempts=json_retry_count)

            if validation_function:
                valid, msg = validation_function(completion)
                if not valid:
                    accumulated_response = ""
                    self.llm_logger.warning(f"Validation failed: {msg}")
                    error_message = f"Previous response was not valid. Error: {msg}"
                    self.llm_logger.warning(f"Failed validation of completion: {completion}")
                    messages.append({"role": "user", "content": error_message})
                    return accumulated_response, False, messages

            response = json.dumps(json_response, indent=2)

            return response, is_complete, messages
        else:
            return accumulated_response, is_complete, messages
    


    def process_tool_calls(
        self,
        tool_calls,
        tool_call_log,
        messages,
        function_executor,
        function_validator,
        remaining_calls,
    ):
        """
        Build *one contiguous block* of role:"tool" responses for every tool_call_id,
        then append any follow-up messages (images, extra user prompts, etc.).
        """

        tool_responses: list[Dict] = []   # role:"tool" replies – added first
        follow_up_msgs: list[Dict] = []   # anything that is *not* a tool reply
        expected_ids: set[str] = set()    # sanity-check that we cover all ids

        for tool_call in tool_calls:
            # ---------------------------------------------------------------
            # 1.  Normalise the OpenAI / Litellm tool_call object to dict
            # ---------------------------------------------------------------
            if isinstance(tool_call, dict):
                function_name = tool_call.get("function", {}).get("name", "")
                function_args = tool_call.get("function", {}).get("arguments", "")
                call_id = tool_call.get("id", "")
            elif hasattr(tool_call, "function"):
                function_name = tool_call.function.name
                function_args = tool_call.function.arguments
                call_id = tool_call.id
            else:
                continue  # malformed – skip

            expected_ids.add(call_id)

            # ---------------------------------------------------------------
            # 2.  Parse arguments & validation
            # ---------------------------------------------------------------
            try:
                function_args = json.loads(function_args)
            except json.JSONDecodeError:
                err_msg = f"JSON decode error in tool call {json.dumps(tool_call)}"
                self.llm_logger.error(err_msg)
                tool_responses.append(
                    {"tool_call_id": call_id, "role": "tool", "content": err_msg}
                )
                continue

            tool_call_log.append({"name": function_name, "args": function_args})

            if function_validator:
                try:
                    function_validator(function_name, function_args)
                except ValueError as e:
                    err_msg = (
                        f"Invalid function call: {function_name} {function_args}. Error: {e}"
                    )
                    self.llm_logger.error(f"Validation failed: {e}")
                    tool_responses.append(
                        {"tool_call_id": call_id, "role": "tool", "content": err_msg}
                    )
                    continue

            # ---------------------------------------------------------------
            # 3.  Execute the function
            # ---------------------------------------------------------------
            if remaining_calls > 0:
                function_result = function_executor(function_name, function_args)
            else:
                function_result = {
                    "status": "ERROR",
                    "message": "Max tool calls reached. This function call is ignored.",
                }

            # ---------------------------------------------------------------
            # 4.  Post-process the result (image extraction, JSON encode, etc.)
            # ---------------------------------------------------------------
            if isinstance(function_result, Dict):
                function_result.pop("final_tool_call", None)

                if "image_url" in function_result:
                    image_url = function_result.pop("image_url")
                    follow_up_msgs.append(
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "Here's the image from the function call result",
                                },
                                {"type": "image_url", "image_url": image_url},
                            ],
                        }
                    )

                function_result = json.dumps(function_result)

            # ---------------------------------------------------------------
            # 5.  Build the role:"tool" reply (NO OTHER ROLES YET!)
            # ---------------------------------------------------------------
            self.llm_logger.info(
                f"LLM OUTPUT function ({call_id}) {remaining_calls}: {function_name}\n"
                f"Args: {json.dumps(function_args, indent=2, cls=CustomJSONEncoder)}"
            )

            if is_valid_name(function_name):
                tool_responses.append(
                    {
                        "tool_call_id": call_id,
                        "role": "tool",
                        "name": function_name,
                        "content": function_result,
                    }
                )
            else:
                self.llm_logger.error(f"Invalid function name: {function_name}")
                tool_responses.append(
                    {"tool_call_id": call_id, "role": "tool", "content": function_result}
                )

        # -------------------------------------------------------------------
        # 6.  Sanity check – every tool_call_id got a response
        # -------------------------------------------------------------------
        actual_ids = {tr["tool_call_id"] for tr in tool_responses}
        missing = expected_ids - actual_ids
        assert not missing, f"Missing tool replies for {missing}"

        # -------------------------------------------------------------------
        # 7.  Append in the ONLY valid order:
        #       assistant(tool_calls=…)  ← already sent *before* we were called
        #       tool(...) replies        ← add now
        #       any other roles          ← add after
        # -------------------------------------------------------------------
        messages.extend(tool_responses)
        messages.extend(follow_up_msgs)

        return messages
        
    async def _llm_interaction_wrapper_locked(
            self,
            messages: List[Dict[str, Any]],
            user_prompt: str,
            system_prompt: str,
            model: str,
            response_format: Dict[str, Any],
            function_schemas: List[Dict[str, Any]] = None,
            function_executor: Callable = None,
            function_validator: Callable = None,
            stream: bool = False,
            user_id: str = 'admin',
            max_retries: int = 4,
            max_tool_calls: int = config.getint('LLM', 'max_tool_calls'),
            preload_files: bool = config.getboolean('LLM', 'preload_files'),
            warning_threshold: int = 5,
            validation_function: Callable = None,
            preloaded_files: List[str] = None,
            enable_cache: bool = False,
            output_validator: OutputValidation = None,
    ) -> ModelResponse:

        self.loop = asyncio.get_running_loop()
     
        tool_call_log = []
        json_retry_count = 0
        max_json_retries = 3  # Maximum number of times to retry JSON generation
        tool_calls_count = 0
        messages = self.llm_wrapper_start(messages, system_prompt, user_prompt, user_id, model, function_schemas)

        while True:
            "retry loop for partial responses, function call handling, and retrying error responses"
            # Print retry count and message lenght. Also the number of different message types

            messages, should_return, remaining_calls= self.prepare_to_invoke_llm(messages, preload_files, preloaded_files, function_executor, max_tool_calls, tool_calls_count, warning_threshold, tool_call_log)
            if should_return:
                return self.mock_completion

            start_time = time.time()
            completion = await self.invoke_llm(messages, function_schemas, model, response_format, stream, max_retries,  tool_choice="auto")
            self.logger_factory.report_value("llm_interface.llm_response_time." + model, time.time() - start_time)

            self.update_completion_stats(completion)

            response_messages = completion.choices[0].message
            finish_reason = completion.choices[0].finish_reason


            if finish_reason == "length" or finish_reason == "stop":  
                # call output validator for partial and complete responses, also update accumulated response
                # also initiate retry so as to continue generation in case of partial response or invalid response                
                response_content, is_complete, messages = await self.validate_output(messages, completion, output_validator, response_format, json_retry_count, max_json_retries, validation_function)
                
                if is_complete:
                    
                    # If JSON wasn't requested, return the original response
                    self.llm_logger.info(f"LLM Final Response:\n{response_content}")
                    self.llm_logger.info("+++++++++++++++++ END LLM SESSION++++++++", extra={"log_type": "llm_session","event_type": "end"})
                    completion.choices[0].message.content = response_content
                    return completion
                
            tool_calls = response_messages.get('tool_calls', None)
                
            if tool_calls:
                tool_calls_count += 1
                messages.append(response_messages)
                messages = self.process_tool_calls(tool_calls, tool_call_log, messages, function_executor, function_validator, remaining_calls)

  

    def ensure_json_response(self, response_content):
        try:
            # Try to parse the entire response as JSON
            return json.loads(response_content)
        except json.JSONDecodeError:
            # If that fails, try to extract JSON from the response
            try:
                # Look for the first '{' and last '}'
                start = response_content.index('{')
                end = response_content.rindex('}') + 1
                json_str = response_content[start:end]
                return json.loads(json_str)
            except (ValueError, json.JSONDecodeError):
                # If all else fails, return an error JSON object
                return {"error": "Failed to parse response as JSON", "original_response": response_content}
        except Exception:
            return {"error": "Failed to parse response as JSON", "original_response": response_content}

    def exponential_backoff(self, retry):
        # Basic exponential backoff formula: 2^retry * 100 milliseconds
        return 2 ** retry * 0.1

    def generate_embedding(self, properties):
        # Convert properties to a string representation for embedding generation
        properties_text = self.properties_to_text(properties)
        # Generate embedding using OpenAI's API
        response = self.client.embeddings.create(
            input=properties_text,
            model="text-embedding-ada-002"  # Choose an appropriate model for your use case
        )
        embedding = response.data[0].embedding
        return embedding

    def properties_to_text(self, properties):
        # Convert node properties to a text representation
        # This could be as simple as concatenating key-value pairs or more complex depending on your needs
        return ", ".join([f"{key}: {value}" for key, value in properties.items()])

    def log_token_stats(self):
        pass

    def check_force_completion(self, tool_call_log, model):
        if not self.force_completion:
            return False, None
        self.llm_logger.info("Forced completion requested")
        fabricated_result = {
            "status": "interrupted",
            "reason": "Maximum tool calls reached. TASK IS NOT COMPLETED! Try again later",
            "message": "Task was interrupted, try again later. Tasks is NOT COMPLETED!",
            "partial_results": tool_call_log
        }

        mock_completion = ModelResponse(
            id="fabricated_response",
            choices=[{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": json.dumps(fabricated_result, indent=2)
                },
                "finish_reason": "forced_stop"
            }],
            created=int(datetime.now().timestamp()),
            tool_call_log=tool_call_log,
            model=model,
            usage={"prompt_tokens": self.prompt_tokens,
                   "completion_tokens": 0,
                   "total_tokens": self.total_tokens}
        )

        self.llm_logger.info("+++++ IMMEDIATE RETURN: NO LLM CALL MADE +++++")
        self.llm_logger.info(f"Fabricated result: {json.dumps(fabricated_result, indent=2)}")

        return True, mock_completion
    

    
    
    async def llm_streaming_interaction_wrapper(
        self,
        messages: List[Dict[str, Any]],
        user_prompt: str,
        system_prompt: str,
        model: str,
        response_format: Dict[str, Any],
        function_schemas: List[Dict[str, Any]] = None,
        function_executor: Callable = None,
        function_validator: Callable = None,
        user_id: str = 'admin',
        max_retries: int = 4,
        max_tool_calls: int = config.getint('LLM', 'max_tool_calls'),
        preload_files: bool = config.getboolean('LLM', 'preload_files'),
        warning_threshold: int = 5,
        validation_function: Callable = None,
        preloaded_files: List[str] = None,
        enable_cache: bool = False,
        output_validator: OutputValidation = None,
        wait_interval: float = 0.1,
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """
        Streaming variant of the LLM interaction wrapper.
        This generator yields each streamed chunk that is not a function/tool call.
        Chunks that contain tool call data (i.e. have a "tool_calls" key)
        are accumulated internally and later processed via process_tool_calls.
        
        Returns an AsyncGenerator that yields either:
        - String content for regular chunks
        - Dictionary with {"final": bool, "content": str} for final responses
        """
        # Initialize tracking variables
        tool_call_log = []
        json_retry_count = 0
        max_json_retries = 3
        tool_calls_count = 0
        has_tool_calls = False
        last_finish_reason = None
        with self._stream_lock:
            self.stop_stream_requested = False
        
        # Set up async loop and prepare messages
        self.loop = asyncio.get_running_loop()
        messages = self.llm_wrapper_start(messages, system_prompt, user_prompt, user_id, model, function_schemas)
        # Main processing loop to handle partial responses, tool calls, and retries
        while True:
            with self._stream_lock: #check for stop requests
                if self.stop_stream_requested:
                    self.llm_logger.info("Streaming session aborted before LLM call.")
                    yield {"final": True, "content": "[LLM STREAM INTERRUPTED BY USER BEFORE NEXT CALL]"}
                    return
            # Prepare for LLM invocation, checking for forced completion
            messages, should_return, remaining_calls = self.prepare_to_invoke_llm(
                messages, preload_files, preloaded_files, function_executor, max_tool_calls,
                tool_calls_count, warning_threshold, tool_call_log)
                
            if should_return:
                yield {"final": True, "content": self.mock_completion.choices[0].message.content}
                return

            # Invoke LLM with streaming enabled
            try:
                stream_gen = await self.invoke_llm(
                    messages, function_schemas, model, response_format, stream=True,
                    max_retries=max_retries, tool_choice="auto")
            except Exception as e:
                self.llm_logger.error(f"Error invoking LLM: {e}")
                yield {"final": True, "content": f"Error: {str(e)}"}
                return

            # ---------------------------------------------------------------------------
            # Process streaming chunks ---------------------------------------------------
            # ---------------------------------------------------------------------------
            accumulated_response   = ""             # full assistant text for this turn
            buffered_response      = ""             # pieces we stream to the UI
            accumulated_tool_calls = {}             # {id: {"function_name": str,
                                                    #        "function_args": str}}
            has_tool_calls         = False
            last_flush             = asyncio.get_event_loop().time()

            for chunk in stream_gen:
                with self._stream_lock: #check for stop requests
                    if self.stop_stream_requested:
                        self.llm_logger.info("Streaming interrupted by user request.")
                        yield {"final": True, "content": "[LLM STREAM INTERRUPTED BY USER]"}
                        return
                # a) keep the finish-reason from the last chunk we saw
                if chunk.choices[0].finish_reason:
                    last_finish_reason = chunk.choices[0].finish_reason
                    self.llm_logger.info(f"Finish reason: {last_finish_reason}")

                # b) cost / usage bookkeeping (unchanged)
                if getattr(chunk, "usage", None):
                    self.update_completion_stats(chunk)

                # c) guard against empty or malformed chunks
                if not (getattr(chunk, "choices", None) and chunk.choices[0].delta):
                    continue

                delta = chunk.choices[0].delta

                # -----------------------------------------------------------------------
                # 1. TOOL-CALL FRAGMENTS
                # -----------------------------------------------------------------------
                if getattr(delta, "tool_calls", None):
                    has_tool_calls = True

                    # Capture any *text* that accompanies the tool_call fragment
                    if getattr(delta, "content", None):
                        accumulated_response += delta.content
                        buffered_response    += delta.content

                    # Collect the structured pieces
                    tool_call = delta.tool_calls[0]

                    if tool_call.id:
                        tool_call_id = tool_call.id

                    if tool_call.function.arguments:
                        assert tool_call_id is not None
                        accumulated_tool_calls.setdefault(tool_call_id, {}).setdefault("function_args", "")
                        accumulated_tool_calls[tool_call_id]["function_args"] += tool_call.function.arguments

                    if tool_call.function.name:
                        assert tool_call_id is not None
                        accumulated_tool_calls.setdefault(tool_call_id, {})["function_name"] = tool_call.function.name

                # -----------------------------------------------------------------------
                # 2. PLAIN CONTENT CHUNKS
                # -----------------------------------------------------------------------
                elif getattr(delta, "content", None):
                    accumulated_response += delta.content
                    buffered_response    += delta.content

                # -----------------------------------------------------------------------
                # 3. FLUSH text to the caller every `wait_interval`
                # -----------------------------------------------------------------------
                now = asyncio.get_event_loop().time()
                if now - last_flush >= wait_interval and buffered_response:
                    yield buffered_response
                    buffered_response = ""
                    last_flush = now

            # ---------------------------------------------------------------------------
            # End of *for chunk* loop – the stream is finished
            # ---------------------------------------------------------------------------

            # Flush any remaining buffered UI text
            if buffered_response:
                yield buffered_response
                buffered_response = ""

            # Decide on a finish_reason (default to the last one we saw)
            finish_reason = last_finish_reason or "stop"

            # ---------------------------------------------------------------------------
            # 4. VALIDATE & HANDLE TOOL CALLS / PLAIN OUTPUT
            # ---------------------------------------------------------------------------
            # Build a synthetic completion object once, now that the assistant turn
            # is closed and stable.
            completion_obj = self._create_completion_object(
                accumulated_response,
                finish_reason=finish_reason if not has_tool_calls else "tool_calls",
                prompt_tokens=self.prompt_tokens,
                completion_tokens=self.completion_tokens,
                estimated_tokens=len(accumulated_response.split())
            )

            if has_tool_calls:
                validated_text, is_complete, messages = await self.validate_output(
                    messages,
                    completion_obj,
                    output_validator,
                    response_format,
                    json_retry_count,
                    max_json_retries,
                    validation_function,
                )
                # Get complete tool calls from the final chunk
                tool_calls = accumulated_tool_calls
                
                if tool_calls:
                    # Process the complete tool calls
                    tool_calls_count += 1
                    assistant_requested_tool_calls = self._convert_tool_calls(tool_calls)
                    messages.append(
                        {
                            "role": "assistant",
                            "tool_calls": assistant_requested_tool_calls,
                        }
                    )
                    messages = self.process_tool_calls(
                        assistant_requested_tool_calls, tool_call_log, messages, 
                        function_executor, function_validator, remaining_calls
                    )
                has_tool_calls = False
                finish_reason = "tool_calls"
                # Incomplete → emit what we’ve got and let the outer loop retry
                if validated_text:
                    yield {"final": False, "content": validated_text}
                self.llm_logger.info("++++++++ CONTINUE STREAMING SESSION After tool call   ++++++++")
                # loop again (outer while) so the assistant can respond
                continue

            # ---------------------------------------------------------------------------
            # 5. NO TOOL CALLS – normal assistant response
            # ---------------------------------------------------------------------------
            validated_text, is_complete, messages = await self.validate_output(
                messages,
                completion_obj,
                output_validator,
                response_format,
                json_retry_count,
                max_json_retries,
                validation_function,
            )

            if is_complete:
                yield {"final": True, "content": validated_text, "tool_call_log": tool_call_log}
                self.llm_logger.info("++++++++ END LLM STREAMING SESSION ++++++++")
                return
            else:
                # Incomplete → emit what we’ve got and let the outer loop retry
                if validated_text:
                    yield {"final": False, "content": validated_text}
                self.llm_logger.info("++++++++ CONTINUE STREAMING SESSION ++++++++")
                # outer while-loop iterates
                # -----------------------------------------------------------------------

    def _log_token_usage(self, provider_name):
        """Log current token usage with provider name."""
        self.llm_logger.info(
            f"{provider_name} streaming usage: Prompt Tokens: {self.prompt_tokens}, "
            f"Completion Tokens: {self.completion_tokens}, "
            f"Total Tokens: {self.total_tokens}, "
        )

    def _combine_tool_call_chunks(self, tool_call_chunks):
        """Combine tool call chunks into a coherent list of tool calls."""
        if not tool_call_chunks:
            return []
            
        # Create a dictionary to track tool calls by ID
        tool_calls_by_id = {}
        
        for chunk in tool_call_chunks:
            if not hasattr(chunk, 'tool_calls'):
                continue
                
            for tool_call in chunk.tool_calls:
                # Skip invalid tool calls
                if not hasattr(tool_call, 'id') or not tool_call.id:
                    continue
                    
                tool_call_id = tool_call.id
                
                # Initialize the tool call if not seen before
                if tool_call_id not in tool_calls_by_id:
                    tool_calls_by_id[tool_call_id] = {
                        "id": tool_call_id,
                        "type": getattr(tool_call, 'type', 'function'),
                        "function": {
                            "name": "",
                            "arguments": ""
                        }
                    }
                
                # Update function details if available
                if hasattr(tool_call, 'function'):
                    function = tool_call.function
                    current = tool_calls_by_id[tool_call_id]['function']
                    
                    # Update function name if provided
                    if hasattr(function, 'name') and function.name:
                        current['name'] = function.name
                        
                    # Append to arguments if provided
                    if hasattr(function, 'arguments') and function.arguments:
                        current['arguments'] += function.arguments
        
        # Convert to list and validate
        result = list(tool_calls_by_id.values())
        
        # Validate function names and arguments
        valid_calls = []
        for call in result:
            # Ensure function name is valid
            if is_valid_name(call['function']['name']):
                # Try to parse arguments as JSON
                try:
                    # Verify arguments are valid JSON by parsing and re-stringifying
                    args_str = call['function']['arguments']
                    json_args = json.loads(args_str)
                    call['function']['arguments'] = json.dumps(json_args)
                    valid_calls.append(call)
                except json.JSONDecodeError:
                    self.llm_logger.warning(
                        f"Invalid JSON in tool call arguments: {call['function']['arguments']}"
                    )
            else:
                self.llm_logger.warning(f"Invalid function name: {call['function']['name']}")
        
        return valid_calls

    def _create_completion_object(self, content, finish_reason, prompt_tokens, completion_tokens, estimated_tokens):
        """Create a completion object for validation purposes."""
        # Using a dictionary structure that mimics the expected ModelResponse
        return type("ModelResponse", (), {
            "id": f"stream_{datetime.now().timestamp()}",
            "choices": [
                type("Choice", (), {
                    "index": 0,
                    "message": type("Message", (), {
                        "role": "assistant",
                        "content": content
                    }),
                    "finish_reason": finish_reason
                })
            ],
            "created": int(datetime.now().timestamp()),
            "model": self.model,
            "usage": type("Usage", (), {
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens or estimated_tokens,
                "total_tokens": (prompt_tokens + (completion_tokens or estimated_tokens))
            })
        })
    
    def _convert_tool_calls(self, functions):
        """
        Convert tool calls from a dictionary format to a list of ChatCompletionMessageToolCall objects.

        Args:
            functions (dict): A dictionary containing the tool calls.

        Returns:
            List[ChatCompletionMessageToolCall]: A list of ChatCompletionMessageToolCall objects representing the converted tool calls.
        """
        assistant_requested_tool_calls: List[ChatCompletionMessageToolCall] = []

        for tool_id, tool in functions.items():
            function_name = tool["function_name"]
            function_args = tool["function_args"]
            
            """assistant_requested_tool_calls.append(ChatCompletionMessageToolCall(
                id = tool_id,
                type="function",
                function=Function(
                    name=function_name,
                    arguments=function_args
                )
            ))"""
            assistant_requested_tool_calls.append({
                        "id": tool_id,
                        "type": "function",
                        "function": {
                            "name": function_name,
                            "arguments": function_args
                        }
                    })

        return assistant_requested_tool_calls
    
    def is_cache_allowed(self, model_name):
        """
        Check if caching is allowed for the model string.
        """
        cache_models_string = config.get('LLM', 'cache_allowed_models', fallback='')
        cache_allowed_models = [model.strip() for model in cache_models_string.split(',') if model.strip()]
        return model_name in cache_allowed_models
            
