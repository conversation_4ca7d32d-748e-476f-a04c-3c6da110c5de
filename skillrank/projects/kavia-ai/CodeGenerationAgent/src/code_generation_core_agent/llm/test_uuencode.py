import base64

# Paste your Base64 string here (no data URL prefix)
b64 = """
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
"""

# Decode to bytes
img_data = base64.b64decode(b64)

# Write out to a .png (or .jpg) file
with open("logs/output.png", "wb") as f:
    f.write(img_data)

print("Wrote output.png, open it to verify")