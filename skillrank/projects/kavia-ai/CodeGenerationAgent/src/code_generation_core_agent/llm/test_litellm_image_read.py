import base64
import json
from litellm import completion

# --- Tool: reads image and returns data URI ---
def read_image_file(path: str) -> dict:
    with open(path, "rb") as img_file:
        encoded_string = base64.b64encode(img_file.read()).decode("utf-8")
    mime_type = "image/jpeg" if path.lower().endswith(".jpg") else "image/png"
    return {
        "image_url": f"data:{mime_type};base64,{encoded_string}"
    }

# --- Step 1: Define the tool for OpenAI ---
tools = [
    {
        "type": "function",
        "function": {
            "name": "read_image_file",
            "description": "Reads an image and encodes it so GPT-4 can see it",
            "parameters": {
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "Path to the image file"
                    }
                },
                "required": ["path"]
            }
        }
    }
]

# --- Step 2: User asks a question that may trigger the tool ---
first_response = completion(
    model="gpt-4.1",  # vision is not required *yet*
    messages=[
        {"role": "user", "content": "Can you describe the image at logs/image.png?"}
    ],
    tools=tools,
    tool_choice="auto"
)
import json

response = first_response

# Now extract the tool calls properly
tool_calls = response["choices"][0]["message"].get("tool_calls")

if tool_calls:
    for tool_call in tool_calls:
        print("Tool name:", tool_call["function"]["name"])
        print("Arguments:", tool_call["function"]["arguments"])
else:
    print("No tool calls were returned.")
# --- Step 3: Check if GPT-4 called the tool ---

if not tool_calls:
    print("GPT did not call any tools.")
    exit()

# --- Step 4: Execute the tool function ---
tool_call = tool_calls[0]
arguments = json.loads(tool_call["function"]["arguments"])
tool_result = read_image_file(**arguments)

messages=[
        {"role": "user", "content": "Can you describe the image at logs/image.jpg?"},
        {
            "role": "function",
            "name": "read_image_file",
            "content": json.dumps(tool_result)
        },
        """{
            "role": "user",
            "content": [
                {"type": "text", "text": "Here's the image."},
                {"type": "image_url", "image_url": {"url": tool_result["image_url"], "detail": "auto"}}
            ]
        }"""
    ]
second_response = completion(
    model="gpt-4.1", messages=messages,
    
)

# --- Step 6: Print what GPT-4 Vision said ---
print("\n--- GPT-4 VISION RESPONSE ---\n")
print(second_response['choices'][0]['message']['content'])