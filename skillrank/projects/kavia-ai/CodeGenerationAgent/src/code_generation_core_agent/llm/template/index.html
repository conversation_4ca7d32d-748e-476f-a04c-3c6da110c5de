<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Interface</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.0.2/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/lib/marked.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css" rel="stylesheet" />
    <script>
        // Initialize marked with options
        const markedInstance = new marked.Marked({
            breaks: false,  // Change this to false
            gfm: true,
            highlight: function(code, lang) {
                if (Prism.languages[lang]) {
                    return Prism.highlight(code, Prism.languages[lang], lang);
                }
                return code;
            }
        });
        // Helper function to safely parse markdown
        function parseMarkdown(content) {
            try {
                return markedInstance.parse(content || '');
            } catch (e) {
                console.error('Markdown parsing error:', e);
                return content || '';
            }
        }
    </script>
    <style>
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
    </style>


    <style>
    .command-suggestions {
        position: absolute;
        bottom: 100%;
        left: 0;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        width: 500px;
        max-height: 200px;
        overflow-y: auto;
        display: none;
    }

    .command-suggestion {
        padding: 0.5rem 1rem;
        cursor: pointer;
    }

    .command-suggestion:hover {
        background-color: #f3f4f6;
    }

    .command-suggestion.selected {
        background-color: #e5e7eb;
    }

    .command-tag {
        display: inline-flex;
        align-items: center;
        background-color: #e5e7eb;
        border-radius: 0.25rem;
        padding: 0.125rem 0.5rem;
        margin: 0 0.25rem;
        font-size: 0.875rem;
    }

    .command-tag-close {
        margin-left: 0.25rem;
        cursor: pointer;
    }
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    .loading-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes fadeInOut {
        0% { opacity: 0; transform: translateY(-10px); }
        20% { opacity: 1; transform: translateY(0); }
        80% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(10px); }
    }

    #greetingText {
        display: inline-block;
        animation: fadeInOut 3s ease-in-out infinite;
    }


    /* Markdown Styles */
    .markdown-content {
        line-height: 1.5;
    }
    .markdown-content p {
        margin-bottom: 0.5em;
    }
    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3 {
        margin: 0.5em 0;
    }
    .markdown-content pre {
        position: relative;
        margin: 0.5em 0;
        padding: 2.5rem 1rem 1rem 1rem; /* Increased top padding to make room for the copy button */
    }
    .markdown-content ul,
    .markdown-content ol {
        margin: 0.5em 0;
    }
    .markdown-content blockquote {
        margin: 0.5em 0;
    }
    .markdown-content table {
        margin: 0.5em 0;
    }
    
    /* Add styles for inline code */
    .markdown-content code {
        background: #f4f4f4;
        padding: 0.2em 0.4em;
        border-radius: 0.25rem;
        font-size: 0.9em;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
    }
    
    /* Ensure pre code blocks don't have the inline code styling */
    .markdown-content pre code {
        background: transparent;
        padding: 0;
    }
    
    /* Add these new styles */
    .copy-button {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.25rem;
        border-radius: 0.375rem;
        background-color: rgba(55, 65, 81, 0.5);
        backdrop-filter: blur(4px);
        transition: background-color 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .copy-button:hover {
        background-color: rgba(55, 65, 81, 0.8);
    }
    
    .copy-button svg {
        width: 16px;
        height: 16px;
        color: white;
    }

    .service-status {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-running {
        background-color: #10B981;  /* Green */
    }
    
    .status-error {
        background-color: #EF4444;  /* Red */
    }
    
    .status-stopped {
        background-color: #6B7280;  /* Gray */
    }

    .message-content {
        line-height: 1.5;
    }
    .message-content p {
        margin: 0.5em 0;  /* Reduce margin between paragraphs */
    }
    .message-content pre {
        margin: 0.5em 0;  /* Consistent margin for code blocks */
    }
    /* Add specific handling for code blocks */
    .message-content pre code {
        display: block;
        padding: 1em;
        background: #f6f8fa;
        border-radius: 6px;
        white-space: pre;  /* Preserve whitespace only in code blocks */
    }
    .message-content code {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        padding: 0.2em 0.4em;
        background-color: rgba(175, 184, 193, 0.2);
        border-radius: 6px;
        font-size: 85%;
    }
    
    .message-content h1, .message-content h2, .message-content h3,
    .message-content h4, .message-content h5, .message-content h6 {
        margin-top: 16px;
        margin-bottom: 8px;
        font-weight: 600;
        line-height: 1.25;
    }
    .message-content ul, .message-content ol {
        padding-left: 2em;
        margin: 8px 0;
    }
    .message-content blockquote {
        padding: 0 1em;
        color: #57606a;
        border-left: 0.25em solid #d0d7de;
        margin: 8px 0;
    }
    /* Add these to your existing styles */
        .copy-button {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            padding: 0.25rem;
            border-radius: 0.375rem;
            background-color: rgba(55, 65, 81, 0.5);
            backdrop-filter: blur(4px);
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .copy-button:hover {
            background-color: rgba(55, 65, 81, 0.8);
        }
        /* Image preview modal styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.75);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
        }
        .modal-content {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
        }
        .modal-close-button {
            position: absolute;
            top: 1rem;
            right: 1rem;
            color: white;
            cursor: pointer;
        }
        .modal-close-button:hover {
            color: rgb(209, 213, 219);
        }

        /* External Action Modal Styles */
        .external-action-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
        }
        
        .external-action-modal-content {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        /* Ensure the external action button styles are loaded and have high specificity */
        .external-action-button {
            background: linear-gradient(45deg, #3b82f6, #6366f1) !important;
            color: white !important;
            border: none !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 0.5rem !important;
            cursor: pointer !important;
            font-weight: 600 !important;
            font-size: 0.875rem !important;
            transition: all 0.2s ease !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            margin-top: 0.75rem !important;
            min-height: 40px !important;
            min-width: 140px !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            text-decoration: none !important;
            vertical-align: middle !important;
            line-height: 1.5 !important;
        }

        .external-action-button:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
            background: linear-gradient(45deg, #2563eb, #4f46e5) !important;
        }

        .external-action-button:active {
            transform: translateY(0) !important;
        }

        .external-action-button svg {
            width: 1rem !important;
            height: 1rem !important;
            flex-shrink: 0 !important;
        }
    </style>

</head>
<body class="bg-gray-100 p-4">
    <div id="greetingBanner" class="w-full bg-gradient-to-r from-blue-600 to-blue-800 text-white py-3 text-center text-xl font-semibold">
        <span id="greetingText">CGA internal debugger chat</span>
    </div>

    <div id="serviceStatusBar" class="w-full bg-white shadow-md p-2 sticky top-0 z-50 mt-4">
        <div class="container mx-auto max-w-4xl">
            <div class="flex flex-wrap gap-4" id="serviceList">
                <!-- Services will be dynamically inserted here -->
            </div>
        </div>
    </div>

    <div class="container mx-auto max-w-4xl mt-4">
        <div class="bg-white rounded-lg shadow-lg h-[600px] flex flex-col">
            <div id="messages" class="flex-1 overflow-y-auto p-4 space-y-3">
                <!-- Messages will be inserted here -->
            </div>

            <div class="border-t border-gray-200 p-3 bg-white rounded-b-lg">
                <div class="flex flex-col gap-2">
                    <!-- File attachments preview area -->
                    <div id="attachmentsPreview" class="flex flex-wrap gap-2"></div>
                    <div class="relative">
                        <textarea id="messageInput" rows="1"
                            class="w-full px-3 py-2 pr-16 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                            placeholder="Type a message..."
                            style="max-height: 150px;"></textarea>
                            <!-- File attachment button -->
                            <button onclick="triggerFileInput()" 
                            class="absolute right-16 bottom-2 text-gray-500 hover:text-gray-700 focus:outline-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h4.586A2 2 0 0112 3.586L15.414 7A2 2 0 0116 8.414V15a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm2 0v10h8V8.414L11.586 5H6z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        
                        <input type="file" 
                            id="fileInput" 
                            multiple 
                            class="hidden" 
                            onchange="handleFileSelect(event)" />
                        
                        <button onclick="sendMessage()"
                            class="absolute right-2 bottom-2 bg-blue-500 text-white px-3 py-1 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Send
                        </button>
                    </div>

                    <div class="flex gap-2">
                        <button onclick="executeCommand('/tasks')"
                            class="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600">
                            View Tasks
                        </button>
                        <button onclick="executeCommand('/ql')"
                            class="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600">
                            View Questions
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        const commands = ['/code'];
        const suggestionsDiv = document.getElementById('commandSuggestions');
        const messageInput = document.getElementById('messageInput');

        messageInput.addEventListener('input', function(e) {
            const text = e.target.value;
            const lastWord = text.split(' ').pop();

            if (lastWord.startsWith('/')) {
                const matches = commands.filter(cmd => cmd.startsWith(lastWord));
                if (matches.length > 0) {
                    showSuggestions(matches);
                } else {
                    hideSuggestions();
                }
            } else {
                hideSuggestions();
            }
        });

        messageInput.addEventListener('keydown', function(e) {
            if (suggestionsDiv.style.display === 'block') {
                const selected = suggestionsDiv.querySelector('.selected');

                if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    handleArrowKeys(e.key === 'ArrowDown');
                } else if (e.key === 'Enter' && selected) {
                    e.preventDefault();
                    selectCommand(selected.dataset.command);
                } else if (e.key === 'Tab' && selected) {
                    e.preventDefault();
                    selectCommand(selected.dataset.command);
                }
            }

            // Handle backspace for command tags
            if (e.key === 'Backspace' && this.selectionStart === this.selectionEnd) {
                const text = this.value;
                const cursorPos = this.selectionStart;
                const beforeCursor = text.substring(0, cursorPos);

                if (beforeCursor.endsWith('/code')) {
                    e.preventDefault();
                    this.value = text.substring(0, cursorPos - 6) + text.substring(cursorPos);
                    this.selectionStart = this.selectionEnd = cursorPos - 6;
                }
            }
        });

        function showSuggestions(matches) {
            suggestionsDiv.innerHTML = matches
                .map(cmd => `<div class="command-suggestion" data-command="${cmd}"><strong>${cmd}</strong> - Analyze code or get code metrics</div>`)
                .join('');

            suggestionsDiv.style.display = 'block';
            suggestionsDiv.querySelector('.command-suggestion').classList.add('selected');

            // Add click handlers
            suggestionsDiv.querySelectorAll('.command-suggestion').forEach(div => {
                div.addEventListener('click', () => selectCommand(div.dataset.command));
            });
        }

        function hideSuggestions() {
            suggestionsDiv.style.display = 'none';
        }

        function handleArrowKeys(isDown) {
            const suggestions = Array.from(suggestionsDiv.querySelectorAll('.command-suggestion'));
            const currentIndex = suggestions.findIndex(el => el.classList.contains('selected'));
            let newIndex;

            if (isDown) {
                newIndex = currentIndex < suggestions.length - 1 ? currentIndex + 1 : 0;
            } else {
                newIndex = currentIndex > 0 ? currentIndex - 1 : suggestions.length - 1;
            }

            suggestions[currentIndex].classList.remove('selected');
            suggestions[newIndex].classList.add('selected');
        }

        function selectCommand(command) {
            const input = document.getElementById('messageInput');
            const text = input.value;
            const lastSpaceIndex = text.lastIndexOf(' ');
            const newText = lastSpaceIndex === -1
                ? `/code `
                : text.substring(0, lastSpaceIndex + 1) + `/code `;

            input.value = newText;
            hideSuggestions();
            input.focus();
        }
        // Auto-expanding textarea
        const textarea = document.getElementById('messageInput');
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 150) + 'px';
        });

        // Utility functions
        function formatDate(isoString) {
            return new Date(isoString).toLocaleString();
        }

        function getMessageTypeColor(type) {
            const colors = {
                user: 'bg-blue-100',
                llm: 'bg-gray-100',
                system: 'bg-yellow-100',
                command: 'bg-purple-100',
                error: 'bg-red-100',
                task: 'bg-orange-100'
            };
            return colors[type] || 'bg-gray-50';
        }

        function getMessageAlignment(type) {
            return type === 'user' ? 'ml-auto' : '';
        }

        function createSpinner() {
            return `<svg class="loading-spinner w-4 h-4 text-gray-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>`;
        }

        function sendThreadedMessage(parentId, content, attachments = []) {
            console.log("Sending threaded message with parent ID:", parentId);
            if (!content.trim() && attachments.length === 0) return;
            console.log('[*] Sending threaded message:', parentId, content, attachments);
            socket.emit('send_message', {
                content: content,
                parent_id: parentId,
                attachments: attachments
            });
        }

        // NEW: Function to handle incoming messages and check for external action requests
        function handleIncomingMessage(message) {
            console.log('🔍 handleIncomingMessage called with:', message);
            console.log('🔍 handleIncomingMessage - message.extra:', message.extra);
            // Check if the message has an external action request
            if (message.extra && message.extra.action_request_to_user) {
                console.log('External action request detected:', message.extra.action_request_to_user);
                // Don't render the button immediately, let the normal message rendering happen first
                // The button will be added in the createMessageElement function
            }
            
            // Continue with normal message handling
            updateMessage(message);
        }

        
        function renderExternalActionButton(externalActionRequest, parentMessageId) {
            console.log('🔍 renderExternalActionButton - Starting function...');
            
            try {
                const button = document.createElement('button');
                
                // Set the primary class name
                button.className = 'external-action-button';
                
                // Apply inline styles as fallback to ensure visibility
                button.style.cssText = `
                    background: linear-gradient(45deg, #3b82f6, #6366f1);
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    cursor: pointer;
                    font-weight: 600;
                    font-size: 0.875rem;
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    margin-top: 0.75rem;
                    min-height: 40px;
                    min-width: 140px;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                `;
                
                button.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" style="width: 1rem; height: 1rem; flex-shrink: 0;" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                    </svg>
                    <span>Configure ${externalActionRequest.service_name || 'Service'}</span>
                `;
                
                button.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔍 External action button clicked!');
                    showExternalActionInput(externalActionRequest, parentMessageId);
                };
                
                // Ensure the button is accessible
                button.setAttribute('type', 'button');
                button.setAttribute('tabindex', '0');
                
                console.log('🔍 renderExternalActionButton - Button creation completed');
                return button;
                
            } catch (error) {
                console.error('🔍 renderExternalActionButton - Error:', error);
                return null;
            }
        }
        // Function to ensure CSS is loaded
        function ensureCSSLoaded(callback) {
            // Check if Tailwind CSS is loaded
            const testElement = document.createElement('div');
            testElement.className = 'hidden';
            document.body.appendChild(testElement);
            
            const isHidden = window.getComputedStyle(testElement).display === 'none';
            document.body.removeChild(testElement);
            
            if (isHidden) {
                // Tailwind is loaded
                callback();
            } else {
                // Wait a bit and try again
                setTimeout(() => ensureCSSLoaded(callback), 100);
            }
        }

        // NEW: Function to show external action input modal
        // Complete showExternalActionInput function
        function showExternalActionInput(externalActionRequest, parentMessageId) {
            console.log('🔍 showExternalActionInput called with:', externalActionRequest, parentMessageId);
            
            // Remove any existing modal
            const existing = document.getElementById('external-action-modal');
            if (existing) {
                console.log('🔍 Removing existing modal');
                existing.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'external-action-modal';
            modal.className = 'external-action-modal';

            const modalContent = document.createElement('div');
            modalContent.className = 'external-action-modal-content';

            // Modal header
            const header = document.createElement('div');
            header.className = 'flex justify-between items-center mb-4';
            
            const title = document.createElement('h3');
            title.className = 'text-lg font-semibold text-gray-900';
            title.textContent = `Configure ${externalActionRequest.service_name || 'Service'}`;
            
            const closeBtn = document.createElement('button');
            closeBtn.className = 'text-gray-400 hover:text-gray-600';
            closeBtn.innerHTML = `
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            `;
            closeBtn.onclick = () => {
                console.log('🔍 Close button clicked');
                modal.remove();
            };

            header.appendChild(title);
            header.appendChild(closeBtn);

            // Instructions section
            if (externalActionRequest.instructions) {
                const instructions = document.createElement('div');
                instructions.className = 'mb-4 p-3 bg-blue-50 rounded-lg text-sm text-blue-800';
                instructions.textContent = externalActionRequest.instructions;
                modalContent.appendChild(instructions);
                console.log('🔍 Instructions section added');
            }

            // Help URL link
            if (externalActionRequest.help_url) {
                const helpLink = document.createElement('div');
                helpLink.className = 'mb-4';
                helpLink.innerHTML = `
                    <a href="${externalActionRequest.help_url}" target="_blank" class="text-blue-500 hover:text-blue-700 text-sm">
                        📚 View documentation →
                    </a>
                `;
                modalContent.appendChild(helpLink);
                console.log('🔍 Help link added');
            }

            // Form for environment variables
            const form = document.createElement('form');
            form.className = 'space-y-4';
            form.id = 'external-action-form';
            console.log('🔍 Form element created');

            // Create form fields for each environment variable
            if (externalActionRequest.environment_variable && externalActionRequest.environment_variable.length > 0) {
                externalActionRequest.environment_variable.forEach((envVar, index) => {
                    console.log(`🔍 Creating field ${index + 1}: ${envVar.field_name}`);
                    
                    const fieldContainer = document.createElement('div');
                    fieldContainer.className = 'form-field-container';
                    
                    const label = document.createElement('label');
                    label.className = 'block text-sm font-medium text-gray-700 mb-1';
                    label.textContent = envVar.display_name || envVar.field_name;
                    label.setAttribute('for', `input-${envVar.field_name}`);

                    const input = document.createElement('input');
                    input.type = envVar.field_name.toLowerCase().includes('password') || 
                                envVar.field_name.toLowerCase().includes('key') || 
                                envVar.field_name.toLowerCase().includes('token') || 
                                envVar.field_name.toLowerCase().includes('secret') ? 'password' : 'text';
                    input.id = `input-${envVar.field_name}`;
                    input.name = envVar.field_name;
                    input.className = 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent';
                    input.placeholder = envVar.instructions || `Enter ${envVar.display_name || envVar.field_name}`;
                    input.required = true;

                    // Add field description if available
                    if (envVar.instructions && envVar.instructions !== input.placeholder) {
                        const description = document.createElement('p');
                        description.className = 'text-xs text-gray-500 mt-1';
                        description.textContent = envVar.instructions;
                        fieldContainer.appendChild(label);
                        fieldContainer.appendChild(input);
                        fieldContainer.appendChild(description);
                    } else {
                        fieldContainer.appendChild(label);
                        fieldContainer.appendChild(input);
                    }
                    
                    form.appendChild(fieldContainer);
                });
                console.log('🔍 All form fields created');
            } else {
                console.error('🔍 No environment variables found in request');
                return;
            }

            // Submit button container
            const submitContainer = document.createElement('div');
            submitContainer.className = 'flex justify-end gap-2 mt-6';

            const cancelBtn = document.createElement('button');
            cancelBtn.type = 'button';
            cancelBtn.className = 'px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500';
            cancelBtn.textContent = 'Cancel';
            cancelBtn.onclick = () => {
                console.log('🔍 Cancel button clicked');
                modal.remove();
            };

            const submitBtn = document.createElement('button');
            submitBtn.type = 'submit';
            submitBtn.id = 'external-action-submit-btn';
            submitBtn.className = 'px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500';
            submitBtn.textContent = 'Configure Service';

            submitContainer.appendChild(cancelBtn);
            submitContainer.appendChild(submitBtn);

            console.log('🔍 Submit buttons created');

            // Form submission handler - using addEventListener for better reliability
            const formSubmitHandler = function(e) {
                console.log('🔍 🔥 FORM SUBMIT EVENT TRIGGERED!');
                console.log('🔍 Event type:', e.type);
                console.log('🔍 Event target:', e.target);
                
                e.preventDefault();
                e.stopPropagation();
                console.log('🔍 preventDefault() and stopPropagation() called');
                
                try {
                    console.log('🔍 Starting form data collection...');
                    
                    // Collect form data
                    const formData = new FormData(form);
                    const configData = {};
                    
                    console.log('🔍 FormData created, iterating through entries...');
                    
                    // Validate and collect all form fields
                    let isValid = true;
                    let fieldCount = 0;
                    
                    for (let [key, value] of formData.entries()) {
                        fieldCount++;
                        console.log(`🔍 Field ${fieldCount}: ${key} = "${value}" (length: ${value.length})`);
                        
                        if (!value.trim()) {
                            console.log(`🔍 ❌ Validation failed for field: ${key}`);
                            alert(`Please enter a value for ${key}`);
                            isValid = false;
                            return;
                        }
                        configData[key] = value.trim();
                    }
                    
                    console.log(`🔍 Collected ${fieldCount} fields`);
                    console.log('🔍 Config data:', configData);
                    console.log(`🔍 Validation result: ${isValid ? 'PASSED' : 'FAILED'}`);
                    
                    if (!isValid) {
                        console.log('🔍 Form validation failed, stopping submission');
                        return;
                    }

                    // Create the extra object with the required format
                    const extra = {
                        type: "setup_service",
                        service_name: externalActionRequest.service_name,
                        action_type: externalActionRequest.action_type,
                        ...configData  // Spread the collected form data
                    };

                    console.log('🔍 Extra object created:', extra);

                    // Create a user message content
                    const responseContent = `✅ Service configuration submitted for ${externalActionRequest.service_name}`;
                    console.log('🔍 Response content:', responseContent);

                    // Send the user response message with extra data
                    console.log('🔍 About to call sendUserResponse...');
                    console.log('🔍 Parameters:', {
                        content: responseContent,
                        attachments: [],
                        extra: extra,
                        parentId: parentMessageId
                    });

                    // Check if sendUserResponse function exists
                    if (typeof sendUserResponse === 'function') {
                        console.log('🔍 sendUserResponse function found, calling...');
                        const result = sendUserResponse(responseContent, [], extra, parentMessageId);
                        console.log('🔍 sendUserResponse completed, result:', result);
                    } else {
                        console.error('🔍 ❌ sendUserResponse function not found!');
                        console.log('🔍 Available functions:', Object.getOwnPropertyNames(window).filter(name => typeof window[name] === 'function'));
                        
                        // Fallback: try to send via socket directly
                        if (typeof socket !== 'undefined' && socket.emit) {
                            console.log('🔍 Falling back to direct socket.emit');
                            const payload = {
                                content: responseContent,
                                attachments: [],
                                extra: extra,
                                parent_id: parentMessageId
                            };
                            socket.emit('send_message', payload);
                            console.log('🔍 Direct socket.emit completed');
                        } else {
                            console.error('🔍 ❌ Socket not available either!');
                            alert('Error: Unable to send response. Please check console for details.');
                            return;
                        }
                    }

                    // Show success feedback
                    console.log('🔍 Updating submit button to success state...');
                    submitBtn.textContent = '✅ Configured!';
                    submitBtn.disabled = true;
                    submitBtn.className = 'px-4 py-2 bg-green-500 text-white rounded-lg cursor-not-allowed';
                    console.log('🔍 Submit button updated');

                    // Close modal after a brief delay
                    console.log('🔍 Setting timeout to close modal in 2 seconds...');
                    setTimeout(() => {
                        console.log('🔍 Closing modal after timeout');
                        modal.remove();
                    }, 2000);
                    
                } catch (error) {
                    console.error('🔍 ❌ ERROR in form submit handler:', error);
                    console.error('🔍 ❌ Error message:', error.message);
                    console.error('🔍 ❌ Error stack:', error.stack);
                    alert(`Error submitting form: ${error.message}`);
                }
            };
            // Store the handler reference so we can call it manually if needed
            form._submitHandler = formSubmitHandler;
            form.addEventListener('submit', formSubmitHandler);

            // Add a direct click handler to submit button as backup
            submitBtn.addEventListener('click', function(e) {
                console.log('🔍 Submit button clicked directly');
                console.log('🔍 Button type:', e.target.type);
                console.log('🔍 Form validity:', form.checkValidity());
                
                // Prevent the default button behavior
                e.preventDefault();
                e.stopPropagation();
                
                // If the form is invalid, let the browser handle validation
                if (!form.checkValidity()) {
                    console.log('🔍 Form is invalid, letting browser handle validation');
                    form.reportValidity(); // Show validation messages
                    return;
                }
                
                // If the form is valid, manually trigger the submit event
                console.log('🔍 Form is valid, manually triggering submit event...');
                
                // Create and dispatch a submit event
                const submitEvent = new Event('submit', {
                    bubbles: true,
                    cancelable: true
                });
                
                console.log('🔍 Dispatching submit event...');
                const eventDispatched = form.dispatchEvent(submitEvent);
                console.log('🔍 Submit event dispatched:', eventDispatched);
                
                // If the event didn't work, call the handler directly
                if (eventDispatched) {
                    console.log('🔍 Submit event was handled by event listeners');
                } else {
                    console.log('🔍 Submit event was cancelled, trying direct handler call');
                    // Find the submit handler and call it directly
                    const submitHandler = form._submitHandler;
                    if (submitHandler) {
                        submitHandler(submitEvent);
                    }
                }
            });

            console.log('🔍 Form submit handlers attached');

            // Assemble the modal
            modalContent.appendChild(header);
            modalContent.appendChild(form);
            modalContent.appendChild(submitContainer);
            modal.appendChild(modalContent);

            // Add to DOM
            document.body.appendChild(modal);
            console.log('🔍 Modal added to DOM');

            // Focus first input
            const firstInput = form.querySelector('input');
            if (firstInput) {
                setTimeout(() => {
                    firstInput.focus();
                    console.log('🔍 First input focused');
                }, 100);
            }

            // Close on escape key
            const escapeHandler = function(e) {
                if (e.key === 'Escape') {
                    console.log('🔍 Escape key pressed, closing modal');
                    modal.remove();
                    document.removeEventListener('keydown', escapeHandler);
                }
            };
            document.addEventListener('keydown', escapeHandler);

            // Close on backdrop click
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    console.log('🔍 Backdrop clicked, closing modal');
                    modal.remove();
                }
            });

            console.log('🔍 showExternalActionInput setup complete');
        }

        // NEW: Function to send user response with extra data
        // Enhanced sendUserResponse function
        function sendUserResponse(content, attachments, extra, parentId) {
            console.log('🔍 sendUserResponse called');
            console.log('🔍 Content:', content);
            console.log('🔍 Attachments:', attachments);
            console.log('🔍 Extra:', extra);
            console.log('🔍 Parent ID:', parentId);
            
            try {
                // Transform the extra data to include vars list
                if (extra && extra.service_name) {
                    const vars = [];
                    
                    // Extract environment variables from extra object
                    Object.keys(extra).forEach(key => {
                        // Skip non-environment variable fields
                        if (!['type', 'service_name', 'action_type'].includes(key)) {
                            vars.push({
                                name: key,
                                value: extra[key]
                            });
                        }
                    });
                    
                    // Add the vars list to extra
                    extra.vars = vars;
                    
                    console.log('🔍 Transformed extra with vars:', extra);
                }
                
                const payload = {
                    content: content,
                    attachments: attachments || [],
                    extra: extra || {},
                    parent_id: parentId
                };
                
                console.log('🔍 Payload created:', payload);
                
                // Check if socket is available
                if (typeof socket === 'undefined') {
                    console.error('🔍 ❌ Socket is not defined!');
                    throw new Error('Socket connection not available');
                }
                
                if (!socket.emit) {
                    console.error('🔍 ❌ Socket.emit is not a function!');
                    throw new Error('Socket.emit is not available');
                }
                
                console.log('🔍 Sending via socket.emit...');
                socket.emit('send_message', payload);
                console.log('🔍 ✅ Message sent successfully');
                
                return payload;
                
            } catch (error) {
                console.error('🔍 ❌ Error in sendUserResponse:', error);
                throw error;
            }
        }

        function createMessageElement(message, isLastInThread = false) {
            console.log('🔍 createMessageElement called for message:', message.id);
            console.log('🔍 message.extra exists:', !!message.extra);
            console.log('🔍 action_request_to_user exists:', !!message.extra?.action_request_to_user);
    
            const wrapper = document.createElement('div');
            wrapper.id = `message-wrapper-${message.id}`;
            wrapper.className = 'mb-4';
            const messageDiv = document.createElement('div');
            messageDiv.id = `message-${message.id}`;
            messageDiv.className = `max-w-[85%] ${message.parent_id ? 'ml-8' : ''} ${getMessageAlignment(message.msg_type)}`;
            const messageContent = document.createElement('div');
            messageContent.className = `p-3 rounded-lg ${getMessageTypeColor(message.msg_type)}`;
            const header = document.createElement('div');
            header.className = 'flex flex-wrap gap-2 items-center text-xs text-gray-500 mb-1';
            if (message.parent_id) {
                const threadIndicator = document.createElement('span');
                threadIndicator.className = 'text-gray-400';
                threadIndicator.innerHTML = '↳ Reply';
                header.appendChild(threadIndicator);
            }
            const typeSpan = document.createElement('span');
            typeSpan.textContent = message.msg_type.toUpperCase();
            const statusBadge = document.createElement('span');
            statusBadge.className = getStatusBadgeClass(message.status);
            statusBadge.textContent = `${getStatusIcon(message.status)} ${message.status.replace('_', ' ').toUpperCase()}`;
            const timeSpan = document.createElement('span');
            timeSpan.className = 'ml-auto';
            timeSpan.textContent = formatDate(message.timestamp);
            header.appendChild(typeSpan);
            header.appendChild(statusBadge);
            header.appendChild(timeSpan);
            const content = document.createElement('div');
            content.className = 'text-sm text-gray-800 break-words message-content';
            content.innerHTML = parseMarkdown(message.content || '');
            
            // NEW: Check for external action request and add button
            console.log('🔍 About to check for action_request_to_user...');
            if (message.extra && message.extra.action_request_to_user) {
                console.log('🔍 ✅ FOUND action_request_to_user! Creating button...');
                console.log('🔍 action_request_to_user data:', message.extra.action_request_to_user);
                
                try {
                    console.log('🔍 Calling renderExternalActionButton...');
                    const actionButton = renderExternalActionButton(message.extra.action_request_to_user, message.id);
                    console.log('🔍 Button created successfully:', actionButton);
                    console.log('🔍 Button type:', typeof actionButton);
                    console.log('🔍 Button innerHTML:', actionButton ? actionButton.innerHTML : 'null');
                    
                    if (actionButton) {
                        const buttonContainer = document.createElement('div');
                        buttonContainer.className = 'mt-3 pt-2 border-t border-gray-200';
                        buttonContainer.appendChild(actionButton);
                        
                        console.log('🔍 Button container created:', buttonContainer);
                        console.log('🔍 Button container children:', buttonContainer.children.length);
                        
                        content.appendChild(buttonContainer);
                        console.log('🔍 Button container appended to content');
                        
                        // VERIFICATION: Check if it's actually in the DOM
                        setTimeout(() => {
                            const found = document.querySelector('.external-action-button');
                            console.log('🔍 VERIFICATION: Button found in DOM after timeout:', !!found);
                            if (found) {
                                console.log('🔍 VERIFICATION: Button details:', found);
                                // Make it super visible for testing
                                found.style.backgroundColor = 'red';
                                found.style.padding = '20px';
                                found.style.fontSize = '16px';
                                found.style.border = '3px solid black';
                            }
                        }, 100);
                    } else {
                        console.error('🔍 ❌ renderExternalActionButton returned null/undefined');
                    }
                    
                } catch (error) {
                    console.error('🔍 ❌ Error creating external action button:', error);
                    console.error('🔍 ❌ Error stack:', error.stack);
                }
            }

            if (message.options && message.options.length > 0) {
                // Parse the content to find and replace option lines
                var contentHtml = parseMarkdown(message.content || '');
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = contentHtml;
                
                // Create the options container
                var optionsContainer = document.createElement('div');
                optionsContainer.className = 'flex flex-col gap-2 my-3';
                
                // Create all option buttons
                for (var k = 0; k < message.options.length; k++) {
                    (function(option) {
                        var optionButton = document.createElement('button');
                        optionButton.className = 'px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors text-left';
                        optionButton.textContent = option;
                        optionButton.onclick = function() {
                            socket.emit('send_message', {
                                content: option,
                                parent_id: message.id,
                                attachments: []
                            });
                            
                            var buttons = optionsContainer.querySelectorAll('button');
                            for (var b = 0; b < buttons.length; b++) {
                                buttons[b].disabled = true;
                                buttons[b].className = 'px-4 py-2 bg-gray-400 text-white text-sm rounded-lg cursor-not-allowed text-left';
                            }
                            
                            optionButton.className = 'px-4 py-2 bg-green-500 text-white text-sm rounded-lg cursor-not-allowed text-left';
                        };
                        optionsContainer.appendChild(optionButton);
                    })(message.options[k]);
                }
                
                // Find and replace option elements while preserving order
                var optionPatterns = message.options.map(opt => opt.toLowerCase());
                var elements = tempDiv.querySelectorAll('li, p, ul, ol');
                var firstOptionElement = null;
                var elementsToRemove = [];
                
                // Find all elements containing options
                for (var i = 0; i < elements.length; i++) {
                    var element = elements[i];
                    var text = element.textContent.toLowerCase();
                    
                    for (var j = 0; j < optionPatterns.length; j++) {
                        if (text.includes(optionPatterns[j])) {
                            if (!firstOptionElement) {
                                firstOptionElement = element;
                            }
                            elementsToRemove.push(element);
                            break;
                        }
                    }
                }
                
                // Insert buttons where the first option was and remove option elements
                if (firstOptionElement) {
                    firstOptionElement.parentNode.insertBefore(optionsContainer, firstOptionElement);
                    elementsToRemove.forEach(function(el) {
                        if (el.parentNode) {
                            el.parentNode.removeChild(el);
                        }
                    });
                } else {
                    // Fallback: append at the end if no options found in text
                    tempDiv.appendChild(optionsContainer);
                }
                
                // CRITICAL FIX: Use appendChild instead of innerHTML to preserve event handlers
                content.innerHTML = '';  // Clear existing content
                while (tempDiv.firstChild) {
                    content.appendChild(tempDiv.firstChild);
                }
                
                // Handle reload logic
                var reloadedMessageIds = JSON.parse(sessionStorage.getItem('reloadedMessageIds') || '[]');
                if (!reloadedMessageIds.includes(message.id)) {
                    reloadedMessageIds.push(message.id);
                    sessionStorage.setItem('reloadedMessageIds', JSON.stringify(reloadedMessageIds));
                    window.location.reload();
                }
            } else {
                // No options - just display content normally
                content.innerHTML = parseMarkdown(message.content || '');
            }

            // Make code blocks copyable
            content.querySelectorAll('pre code').forEach((block) => {
                const pre = block.parentElement;
                pre.style.position = 'relative';
                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button';
                copyButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>`;
                copyButton.onclick = () => {
                    navigator.clipboard.writeText(block.textContent);
                    copyButton.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="20 6 9 17 4 12"></polyline>
                        </svg>`;
                    setTimeout(() => {
                        copyButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>`;
                    }, 2000);
                };
                pre.appendChild(copyButton);
            });
            messageContent.appendChild(header);
            messageContent.appendChild(content);
            // Add attachments section if message has attachments
            if (message.attachments && message.attachments.length > 0) {
                const attachmentsDiv = document.createElement('div');
                attachmentsDiv.className = 'mt-2 space-y-2 border-t border-gray-200 pt-2';
                const attachmentHeader = document.createElement('div');
                attachmentHeader.className = 'text-xs text-gray-500 mb-2';
                attachmentHeader.textContent = 'Attachments:';
                attachmentsDiv.appendChild(attachmentHeader);
                message.attachments.forEach(attachment => {
                    const attachmentElement = document.createElement('div');
                    attachmentElement.className = 'flex items-center gap-2 bg-gray-50 rounded p-2 hover:bg-gray-100 transition-colors';
                    const isImage = /\.(jpg|jpeg|png|gif)$/i.test(attachment.filename);
                    if (isImage) {
                        // Create thumbnail container
                        const thumbnailContainer = document.createElement('div');
                        thumbnailContainer.className = 'relative group cursor-pointer';
                        // Create thumbnail
                        const thumbnail = document.createElement('img');
                        thumbnail.src = `/chat_uploads/${attachment.filename}`;
                        thumbnail.className = 'w-20 h-20 object-cover rounded';
                        thumbnail.alt = attachment.originalName;
                        // Add click handler for preview
                        thumbnail.onclick = () => openImagePreview(attachment.filename);
                        // Add filename below thumbnail
                        const filenameSpan = document.createElement('span');
                        filenameSpan.className = 'text-xs text-gray-600 mt-1 block';
                        filenameSpan.textContent = attachment.originalName;
                        thumbnailContainer.appendChild(thumbnail);
                        thumbnailContainer.appendChild(filenameSpan);
                        attachmentElement.appendChild(thumbnailContainer);
                    } else {
                        // Create file icon and name layout
                        attachmentElement.innerHTML = `
                            <div class="flex items-center gap-2 w-full">
                                <svg class="w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 3.586L15.414 7A2 2 0 0116 8.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-sm text-gray-600 flex-grow">${attachment.originalName}</span>
                                <a href="/chat_uploads/${attachment.filename}"
                                download="${attachment.originalName}"
                                class="text-blue-500 hover:text-blue-700">
                                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            </div>
                        `;
                    }
                    attachmentsDiv.appendChild(attachmentElement);
                });
                messageContent.appendChild(attachmentsDiv);
            }
            messageDiv.appendChild(messageContent);
            wrapper.appendChild(messageDiv);
            // Create child container for replies
            const childContainer = document.createElement('div');
            childContainer.id = `children-${message.id}`;
            childContainer.className = 'pl-4 border-l-2 border-gray-300';
            wrapper.appendChild(childContainer);
            // Add reply field if needed
            if (message.status === 'needs_response') {
                const replyField = createReplyField(message.id);
                messageContent.appendChild(replyField);
            }
            return wrapper;
        }
        // Add this function for image preview
        function openImagePreview(filename) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
            const modalContent = document.createElement('div');
            modalContent.className = 'relative max-w-[90%] max-h-[90vh]';
            const img = document.createElement('img');
            img.src = `/chat_uploads/${filename}`;
            img.className = 'max-w-full max-h-[90vh] object-contain';
            const closeButton = document.createElement('button');
            closeButton.className = 'absolute top-4 right-4 text-white hover:text-gray-300';
            closeButton.innerHTML = `
                <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            `;
            closeButton.onclick = () => modal.remove();
            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };
            modalContent.appendChild(img);
            modalContent.appendChild(closeButton);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
        }

        function addMessageToThread(message, isLastInThread = false) {
            console.log('🔍 addMessageToThread called with:', message.id);
            console.log('🔍 addMessageToThread - message.extra:', message.extra);
            const messagesContainer = document.getElementById('messages');
            if (message.parent_id) {
                // This is a child message - add it to its parent's child container
                const parentChildContainer = document.getElementById(`children-${message.parent_id}`);
                if (parentChildContainer) {
                    parentChildContainer.appendChild(createMessageElement(message, isLastInThread));
                } else {
                    // Fallback if parent container not found
                    messagesContainer.appendChild(createMessageElement(message, isLastInThread));
                }
            } else {
                // This is a top-level message
                messagesContainer.appendChild(createMessageElement(message, isLastInThread));
            }
        }


        function getStatusBadgeClass(status) {
            const baseClasses = "px-2 py-0.5 text-xs font-medium rounded-full";
            switch (status) {
                case 'pending':
                    return `${baseClasses} bg-yellow-100 text-yellow-800`;
                case 'streaming':
                    return `${baseClasses} bg-blue-100 text-blue-800 animate-pulse`;
                case 'completed':
                    return `${baseClasses} bg-green-100 text-green-800`;
                case 'failed':
                    return `${baseClasses} bg-red-100 text-red-800`;
                case 'needs_response':
                    return `${baseClasses} bg-purple-100 text-purple-800`;
                case 'resolved':
                    return `${baseClasses} bg-gray-100 text-gray-800`;
                default:
                    return `${baseClasses} bg-gray-100 text-gray-800`;
            }
        }

        function getStatusIcon(status) {
            switch (status) {
                case 'pending':
                    return '⏳';
                case 'streaming':
                    return '🔄';
                case 'completed':
                    return '✅';
                case 'failed':
                    return '❌';
                case 'needs_response':
                    return '❓';
                case 'resolved':
                    return '✓';
                default:
                    return '•';
            }
        }

        function scrollToBottom() {
            const messagesContainer = document.getElementById('messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function updateMessage(message) {
            console.log('🔍 updateMessage called with:', message.id);
            console.log('🔍 updateMessage - message.extra:', message.extra);
            
            const messageElement = document.getElementById(`message-${message.id}`);
            if (!messageElement) {
                if (message.content) {
                    addMessageToThread(message);
                }
                return;
            }

            const status = message.status;
            const content = message.content;
            
            if (status) {
                const statusBadge = messageElement.querySelector('[class*="rounded-full"]');
                if (statusBadge) {
                    statusBadge.className = getStatusBadgeClass(status);
                    statusBadge.textContent = `${getStatusIcon(status)} ${status.replace('_', ' ').toUpperCase()}`;
                }
                
                const messageContent = messageElement.querySelector('.p-3.rounded-lg');
                const existingReplyField = messageContent.querySelector('.reply-field');
                
                if (status === 'needs_response' && !existingReplyField) {
                    messageContent.appendChild(createReplyField(message.id));
                } else if (status !== 'needs_response' && existingReplyField) {
                    existingReplyField.remove();
                }
                
                // Check for external action request
                if (message.extra && message.extra.action_request_to_user) {
                    console.log('🔍 updateMessage - FOUND action_request_to_user, checking if button exists');
                    
                    const existingActionButton = messageContent.querySelector('.external-action-button');
                    if (!existingActionButton) {
                        console.log('🔍 updateMessage - Creating external action button');
                        
                        try {
                            const actionButton = renderExternalActionButton(message.extra.action_request_to_user, message.id);
                            if (actionButton) {
                                const buttonContainer = document.createElement('div');
                                buttonContainer.className = 'mt-3 pt-2 border-t border-gray-200';
                                buttonContainer.appendChild(actionButton);
                                
                                const existingReplyField = messageContent.querySelector('.reply-field');
                                if (existingReplyField) {
                                    messageContent.insertBefore(buttonContainer, existingReplyField);
                                } else {
                                    messageContent.appendChild(buttonContainer);
                                }
                                
                                console.log('🔍 updateMessage - External action button added in updateMessage');
                            }
                        } catch (error) {
                            console.error('🔍 updateMessage - Error creating external action button:', error);
                        }
                    } else {
                        console.log('🔍 updateMessage - External action button already exists');
                    }
                }
            }

            // 🔥 CRITICAL FIX: Don't overwrite content if external action button exists
            if (content) {
                const contentDiv = messageElement.querySelector('.message-content');
                if (contentDiv) {
                    const hasExternalAction = message.extra && message.extra.action_request_to_user;
                    const existingActionButton = contentDiv.querySelector('.external-action-button');
                    
                    // Only update content if there's no external action button OR if it's streaming
                    if (!hasExternalAction || status === 'streaming' || !existingActionButton) {
                        if (status === 'streaming') {
                            // For streaming, just append the text directly
                            if (!contentDiv.streamBuffer) {
                                contentDiv.streamBuffer = '';
                            }
                            contentDiv.streamBuffer += content;
                            try {
                                contentDiv.innerHTML = parseMarkdown(contentDiv.streamBuffer);
                            } catch (e) {
                                contentDiv.innerHTML = contentDiv.streamBuffer;
                            }
                        } else {
                            // For complete messages, replace the entire content only if no button
                            contentDiv.streamBuffer = content;
                            contentDiv.innerHTML = parseMarkdown(content);
                        }
                    } else {
                        console.log('🔍 updateMessage - Skipping content update to preserve external action button');
                    }
                }
            }
            
            scrollToBottom();
        }

        function createReplyField(messageId) {
        console.log("|||||||||||||||||||||||||||||||| Creating reply field for message ID:", messageId);
        const replyContainer = document.createElement('div');
        replyContainer.className = 'mt-2 ml-8 reply-field';
        // Add attachments preview area for replies
            const attachmentsPreview = document.createElement('div');
            attachmentsPreview.className = 'flex flex-wrap gap-2 mb-2';
            attachmentsPreview.id = `reply-attachments-${messageId}`;
            const inputContainer = document.createElement('div');
            inputContainer.className = 'flex gap-2';
            const textarea = document.createElement('textarea');
            textarea.className = 'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm';
            textarea.placeholder = 'Reply to this message...';
            textarea.rows = 1;
            const buttonContainer = document.createElement('div');
            buttonContainer.className = 'flex flex-col gap-2';
            // Create hidden file input
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.multiple = true;
            fileInput.className = 'hidden';
            fileInput.id = `reply-file-input-${messageId}`;
            // Create attachment button
            const attachButton = document.createElement('button');
            attachButton.className = 'p-2 text-gray-500 hover:bg-gray-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500';
            attachButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h4.586A2 2 0 0112 3.586L15.414 7A2 2 0 0116 8.414V15a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm2 0v10h8V8.414L11.586 5H6z" clip-rule="evenodd" />
                </svg>
            `;
            // Create send button
            const sendButton = document.createElement('button');
            sendButton.className = 'p-2 text-blue-500 hover:bg-blue-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500';
            sendButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m22 2-7 20-4-9-9-4 20-7z"/>
                    <path d="M22 2 11 13"/>
                </svg>
            `;
            // Store attachments for this reply
            const attachments = new Map();
            // Handle file selection
            fileInput.addEventListener('change', async function() {
                const files = this.files;
                for (const file of files) {
                    try {
                        const formData = new FormData();
                        formData.append('file', file);
                        const response = await fetch('/api/upload_attachment', {
                            method: 'POST',
                            body: formData
                        });
                        const result = await response.json();
                        if (result.success) {
                            // Store attachment info
                            attachments.set(result.filename, result);
                            // Create preview element
                            const preview = document.createElement('div');
                            preview.className = 'flex items-center gap-2 bg-gray-100 rounded p-2 text-sm';
                            preview.innerHTML = `
                                <span class="text-gray-600 truncate max-w-[150px]">${result.originalName}</span>
                                <button class="text-red-500 hover:text-red-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            `;
                            // Add remove functionality
                            preview.querySelector('button').onclick = () => {
                                attachments.delete(result.filename);
                                preview.remove();
                            };
                            attachmentsPreview.appendChild(preview);
                        }
                    } catch (error) {
                        console.error('Error uploading file:', error);
                    }
                }
                // Clear file input
                this.value = '';
            });

            // Handle textarea auto-resize
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 150) + 'px';
            });
            // Handle enter key
            textarea.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendReply();
                }
            });
            // Function to send reply with attachments
            function sendReply() {
                const content = textarea.value.trim();
                if (!content && attachments.size === 0) return;
                sendThreadedMessage(messageId, content, Array.from(attachments.values()));
                textarea.value = '';
                textarea.style.height = 'auto';
                attachmentsPreview.innerHTML = '';
                attachments.clear();
            }
            // Add click handlers
            attachButton.onclick = () => fileInput.click();
            sendButton.onclick = sendReply;
            // Assemble the components
            buttonContainer.appendChild(attachButton);
            buttonContainer.appendChild(sendButton);
            inputContainer.appendChild(textarea);
            inputContainer.appendChild(buttonContainer);
            replyContainer.appendChild(fileInput);
            replyContainer.appendChild(attachmentsPreview);
            replyContainer.appendChild(inputContainer);
            return replyContainer;
        }

        socket.on('initial_messages', (data) => {
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = '';

            // Filter out system messages and sort by timestamp
            const sortedMessages = data.messages
                .filter(message => message.msg_type !== 'system')
                .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

            // Group messages by thread
            const threads = {};
            sortedMessages.forEach(message => {
                const threadId = message.parent_id || message.id;
                if (!threads[threadId]) {
                    threads[threadId] = [];
                }
                threads[threadId].push(message);
            });

            // Add messages, marking the last message in each thread
            sortedMessages.forEach(message => {
                if (!message.parent_id) {
                    // This is a top-level message
                    const threadMessages = threads[message.id] || [];
                    const isLastInThread = !threadMessages.find(m => m.id !== message.id);
                    addMessageToThread(message, isLastInThread);
                } else {
                    // This is a reply
                    const threadMessages = threads[message.parent_id] || [];
                    const isLastInThread = threadMessages[threadMessages.length - 1].id === message.id;
                    addMessageToThread(message, isLastInThread);
                }
            });

            scrollToBottom();
        });

        // MODIFIED: Use the new handleIncomingMessage function
        socket.on('message_received', (message) => {
            console.log('Message received:', message);
            handleIncomingMessage(message);
        });

        socket.on('message_chunk', (data) => {
            updateMessage(data);
        });

        socket.on('message_resolved', (data) => {
            console.log('Message Resolved ---> :', data);
            updateMessage(data);
        });

        socket.on('message_status', (data) => {
            console.log('Message status:', data);
            updateMessage(data);
        });

        socket.on('needs_response', (data) => {
            console.log('Message status:', data);
            updateMessage(data);
        });

        socket.on('message_added', (message) => {
            console.log('Received message:', message);
            console.log('Message extra:', message.extra);
            console.log('External action request:', message.extra?.action_request_to_user);
            if (message.msg_type === 'system') {
                console.log('System message:', message);
            } else {
               updateMessage(message);
            }
        });


        socket.on('command_response', (message) => {
            console.log('Command response:', message);
            const messagesContainer = document.getElementById('messages');
            messagesContainer.appendChild(createMessageElement(message));
            scrollToBottom();
        });

        socket.on('error', (data) => {
            console.error('Server error:', data.error);
        });

        // Global variable to store attachments
        let currentAttachments = [];

        function triggerFileInput() {
            document.getElementById('fileInput').click();
        }

        async function handleFileSelect(event) {
            const files = event.target.files;
            const previewArea = document.getElementById('attachmentsPreview');
            
            for (const file of files) {
                try {
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    const response = await fetch('/api/upload_attachment', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // Store attachment info
                        currentAttachments.push(result);
                        
                        // Create preview element
                        const preview = document.createElement('div');
                        preview.className = 'flex items-center gap-2 bg-gray-100 rounded p-2';
                        preview.innerHTML = `
                            <span class="text-sm text-gray-600">${result.originalName}</span>
                            <button onclick="removeAttachment('${result.filename}')" 
                                class="text-red-500 hover:text-red-700">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        `;
                        previewArea.appendChild(preview);
                    }
                } catch (error) {
                    console.error('Error uploading file:', error);
                }
            }
            
            // Clear file input
            event.target.value = '';
        }

        function removeAttachment(filename) {
            currentAttachments = currentAttachments.filter(att => att.filename !== filename);
            updateAttachmentPreviews();
        }

        function updateAttachmentPreviews() {
            const previewArea = document.getElementById('attachmentsPreview');
            previewArea.innerHTML = '';
            
            currentAttachments.forEach(att => {
                const preview = document.createElement('div');
                preview.className = 'flex items-center gap-2 bg-gray-100 rounded p-2';
                preview.innerHTML = `
                    <span class="text-sm text-gray-600">${att.originalName}</span>
                    <button onclick="removeAttachment('${att.filename}')" 
                        class="text-red-500 hover:text-red-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                `;
                previewArea.appendChild(preview);
            });
        }

        // Modify the existing sendMessage function to include attachments
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();
            const mainAttachments = Array.from(currentAttachments.values());
            if (!content && mainAttachments.length === 0) return;
            socket.emit('send_message', {
                content: content,
                attachments: mainAttachments
            });
            // Clear input and attachments
            input.value = '';
            input.style.height = 'auto';
            currentAttachments.clear();
            document.getElementById('attachmentsPreview').innerHTML = '';
        }
        function executeCommand(command) {
            socket.emit('execute_command', { command });
        }

        // Event listeners
        textarea.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Add this after the socket initialization
        marked.setOptions({
            gfm: true, // GitHub Flavored Markdown
            breaks: true, // Convert line breaks to <br>
            headerIds: false, // Disable header IDs for security
            sanitize: false, // We'll use DOMPurify instead if needed
            highlight: function(code, lang) {
                return code;
            }
        });

        // Add custom renderer to handle newlines better
        const renderer = new marked.Renderer();
        renderer.paragraph = function(text) {
            return text + '\n';
        };
        marked.use({ renderer });

        // Function to update service status
        function updateServiceStatus() {
            fetch('/api/get_backend_service_status')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const serviceList = document.getElementById('serviceList');
                        serviceList.innerHTML = '';
                        
                        // Remove status field from data
                        delete data.status;
                        
                        // Create service items
                        Object.entries(data).forEach(([serviceName, status]) => {
                            const serviceItem = document.createElement('div');
                            serviceItem.className = 'flex items-center gap-2 bg-gray-50 rounded-lg p-2';
                            
                            // Status indicator
                            const statusDot = document.createElement('span');
                            statusDot.className = `service-status status-${status.toLowerCase()}`;
                            
                            // Service name
                            const nameSpan = document.createElement('span');
                            nameSpan.className = 'font-medium';
                            nameSpan.textContent = serviceName;
                            
                            // Start button
                            const startButton = document.createElement('button');
                            startButton.className = `px-2 py-1 text-sm rounded ${
                                status.toLowerCase() === 'running' 
                                    ? 'bg-gray-300 cursor-not-allowed' 
                                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                            }`;
                            startButton.textContent = 'Start';
                            startButton.disabled = status.toLowerCase() === 'running';
                            
                            startButton.onclick = () => {
                                if (status.toLowerCase() !== 'running') {
                                    startService(serviceName);
                                }
                            };
                            
                            serviceItem.appendChild(statusDot);
                            serviceItem.appendChild(nameSpan);
                            serviceItem.appendChild(startButton);
                            serviceList.appendChild(serviceItem);
                        });
                    }
                })
                .catch(error => console.error('Error fetching service status:', error));
        }
        
        // Function to start a service
        function startService(serviceName) {
            fetch('/api/start_backend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ service_name: serviceName })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    updateServiceStatus(); // Refresh status immediately after starting
                } else {
                    console.error('Error starting service:', data.error);
                }
            })
            .catch(error => console.error('Error starting service:', error));
        }
        
        // Start periodic status updates
        updateServiceStatus(); // Initial update
        setInterval(updateServiceStatus, 10000); // Update every 10 seconds
    </script>
</body>
</html>