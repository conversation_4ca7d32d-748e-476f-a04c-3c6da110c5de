import asyncio
import threading
import queue
from typing import Optional
from dataclasses import dataclass
from datetime import datetime
import time
from code_generation_core_agent.llm.chat_interface import *


@dataclass
class SimulatedChatRequest:
    """Represents a request to the simulated chat system"""
    message_id: str
    content: str
    response_queue: asyncio.Queue


@dataclass
class TimedMessage:
    """Configuration for a message to be sent after a delay"""
    delay_seconds: int
    content: str
    metadata: Optional[Dict] = None


class ChatTimerSystem:
    """
    A separate system for handling timed messages in the chat interface.
    Can run alongside other chat systems without interfering with their operation.
    """

    def __init__(self, chat_interface: ChatInterface):
        self.chat_interface = chat_interface
        self.running = False
        self.timer_thread = None
        self.start_time = None
        self.scheduled_messages: list[TimedMessage] = []

    def schedule_message(self, message: TimedMessage):
        """Add a message to be sent after specified delay"""
        self.scheduled_messages.append(message)

    def start(self):
        """Start the timer system"""
        if not self.running:
            self.running = True
            self.start_time = time.time()
            self.timer_thread = threading.Thread(target=self._run_timer)
            self.timer_thread.daemon = True
            self.timer_thread.start()

    def stop(self):
        """Stop the timer system"""
        self.running = False
        if self.timer_thread:
            self.timer_thread.join()
            self.timer_thread = None

    def _run_timer(self):
        """Main timer loop that checks and sends scheduled messages"""
        while self.running:
            current_time = time.time()
            elapsed_time = current_time - self.start_time

            remaining_messages = []
            for message in self.scheduled_messages:
                if elapsed_time >= message.delay_seconds:
                    print(f">> Sending scheduled message after {message.delay_seconds} seconds...")
                    self.chat_interface.llm_interface.dispatch_question_to_user(
                        message.content,
                        metadata=message.metadata
                    )
                else:
                    remaining_messages.append(message)

            # Update list to only contain messages that haven't been sent
            self.scheduled_messages = remaining_messages

            # If no more messages and not running continuously, stop
            if not self.scheduled_messages:
                print(">> All scheduled messages sent")
                self.running = False
                break

            time.sleep(1)  # Check every second


def setup_chat_timer(chat_interface: ChatInterface) -> ChatTimerSystem:
    """Helper function to setup and start a timer system with predefined messages"""
    timer_system = ChatTimerSystem(chat_interface)

    weather_api_message = TimedMessage(
        delay_seconds=40,
        content="Please provide your weather.com API key to continue:",
        metadata={"request_type": "api_key", "service": "weather.com"}
    )

    timer_system.schedule_message(weather_api_message)
    timer_system.start()
    return timer_system

class SimulatedChatSystem:
    """
    A threaded simulated chat system that processes messages and generates responses.
    Runs in its own thread and communicates with the main system through message queues.
    """

    def __init__(self, chat_interface: ChatInterface):
        self.chat_interface = chat_interface
        self.request_queue = queue.Queue()
        self.running = False
        self.worker_thread = None
        print("Simulated chat system initialized")
        setup_chat_timer(chat_interface)
        self.chat_interface.add_callback("message_added", self._handle_new_message)

    def start(self):
        """Start the simulated chat system thread"""
        if not self.running:
            self.running = True
            self.worker_thread = threading.Thread(target=self._process_queue)
            self.worker_thread.daemon = True
            self.worker_thread.start()

    def stop(self):
        """Stop the simulated chat system thread"""
        self.running = False
        if self.worker_thread:
            self.request_queue.put(None)  # Sentinel value to stop the thread
            self.worker_thread.join()
            self.worker_thread = None

    def _handle_new_message(self, message: ChatMessage):
        """Handle incoming new messages"""
        if message.msg_type.value == MessageType.USER.value:
            response_queue = asyncio.Queue()

            request = SimulatedChatRequest(
                message_id=message.id,
                content=message.content,
                response_queue=response_queue
            )
            print(">> Generating response...")
            self.request_queue.put(request)
        else:
            print(">> Ignoring non-user message", message.msg_type, " expected type", MessageType.USER)

    def _handle_task(self, request: SimulatedChatRequest):
        pass

    def _process_queue(self):
        print(">> Starting simulated chat system...")
        """Main processing loop for the simulator thread"""
        while self.running:
            try:
                request = self.request_queue.get()

                if request.content.startswith("/task"):
                    self._handle_task(request)
                    continue

                if request is None:
                    break

                asyncio.run(self._generate_response(request))

            except Exception as e:
                import traceback
                traceback.print_exc()
                print(f"Error processing request: {e}")

    async def _generate_response(self, request: SimulatedChatRequest):
        """Generate a simulated response to a user message"""
        response_text = (
            f"This is a simulated answer to your question ({request.content}). Say Yes to implement this task.")
        need_response = True

        print(">> Generating response...")
        async def simulate_llm_response(prompt: str) -> AsyncGenerator[str, None]:
            """Simulate an LLM streaming response"""
            words = f"Simulated LLM response to: {prompt}".split()
            for word in words:
                await asyncio.sleep(0.05)  # Simulate network delay
                yield word + " "

        if request.content.lower() == "yes":
            response_text = "Task implemented successfully!"
            need_response = False

        response_id = await self.chat_interface.stream_llm_response(
            simulate_llm_response(response_text),
            parent_id=request.message_id
        )
        # Resolve parent message
        self.chat_interface.resolve_message(request.message_id, "Simulated resolve message")
        print(">> Need response: ", response_id)
        if need_response:
            self.chat_interface.llm_interface.need_response(response_id)
        else:
            print(">> No response needed")
            self.chat_interface.resolve_message(response_id, "Task implemented successfully!")