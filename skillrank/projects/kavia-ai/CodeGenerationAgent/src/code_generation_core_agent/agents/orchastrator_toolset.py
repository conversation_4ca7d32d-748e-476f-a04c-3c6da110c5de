from code_generation_core_agent.agents.framework.task_tracker import TaskTracker
from code_generation_core_agent.agents.tools.AgentPreProcessor import AgentPreProcessor
from code_generation_core_agent.agents.tools.combined_tools import CombinedTools


class OrchestratorToolSet():
    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        task_tracker = TaskTracker
        tool_instances = [task_tracker]
        agent_pre_processor = AgentPreProcessor(callback_functions,
                                                logger=logger)

        self.combined_tool = CombinedTools(tool_instances,
                                      [agent_pre_processor],
                                      base_path,
                                      logger=logger, llm=llm, executor=executor)

    def get_tool(self):
        return self.combined_tool