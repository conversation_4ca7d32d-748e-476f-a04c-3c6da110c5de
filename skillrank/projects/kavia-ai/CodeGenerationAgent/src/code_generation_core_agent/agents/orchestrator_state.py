import json
import os
from typing import Dict, List, Any, Optional


class OrchestratorState:
    """
    This class represents the state of the orchestrator agent.
    The goal is to be able to save and load the state of the orchestrator agent.
    """

    def __init__(self):
        self.request_context: Dict[str, Any] = {}
        self.messages: List[Dict[str, Any]] = []
        self.task_report: Dict[str, List[Dict[str, Any]]] = {
            "task_list": [],
            "completed_tasks": [],
            "future_steps": []
        }
        self.resume_mode: bool = False
        self.user_assigned_task: Optional[str] = None
        self.user_attachments: List[Dict[str, Any]] = []

    def save_state(self) -> str:
        """
        Serialize the current orchestration state to a JSON string.

        Returns:
            str: JSON string representing the current state
        """
        state = {
            "request_context": self.request_context,
            "messages": self.messages,
            "task_report": self.task_report,
        }
        return json.dumps(state)

    def load_state(self, state_json: str) -> bool:
        """
        Load orchestration state from a JSON string.

        Args:
            state_json: JSON string representing a saved state

        Returns:
            bool: True if state was successfully loaded, False otherwise
        """
        try:
            state = json.loads(state_json)
            self.request_context = state.get("request_context", {})
            self.messages = state.get("messages", [])
            self.task_report = state.get("task_report",
                {"task_list": [], "completed_tasks": [], "future_steps": []})
            self.resume_mode = True
            return True
        except Exception:
            return False

    def save_state_to_file(self, filepath: str) -> bool:
        """
        Save the current state to a file.

        Args:
            filepath: Path where the state should be saved

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            state_json = self.save_state()
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'w') as f:
                f.write(state_json)
            return True
        except Exception:
            return False

    def load_state_from_file(self, filepath: str) -> bool:
        """
        Load state from a file.

        Args:
            filepath: Path to the saved state file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(filepath, 'r') as f:
                state_json = f.read()
            return self.load_state(state_json)
        except Exception:
            return False

    def set_current_task(self, task: str, attachments: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        Set the current user assigned task and attachments.

        Args:
            task: The task description
            attachments: Optional list of attachments
        """
        self.user_assigned_task = task
        self.user_attachments = attachments or []

    def setup_new_task(self, agent_name: str, work_item: Dict[str, Any],
                      execution_base_path: str) -> None:
        """
        Setup state for a new task.

        Args:
            agent_name: Name of the agent
            work_item: Work item to process
            execution_base_path: Base path for execution
        """
        design_node = None
        remove_keys = []

        # Extract design node from work item
        for key in work_item:
            if key not in ['component_name', 'container_name', "container_type", "container_details", 'description', 'interfaces', 'algorithms', 
                        'framework', 'pseudocode', 'figma_components', 'container_details', 'platform', 
                        'test_cases', 'containers', "manifest_path", "dependencies","3rd_party_services", 
                         "architecture_diagrams", ]:  
                if not design_node:
                    design_node = {}
                if key == 'design':
                    design_node.update(work_item[key])
                else:
                    design_node[key] = work_item[key]
                remove_keys.append(key)
        
        for key in remove_keys:
            work_item.pop(key)

        self.request_context = {
            "agent_name": agent_name,
            "work_item": work_item,
            "design_node": design_node,
            "execution_base_path": execution_base_path,

        }

        self.task_report = {
            "task_list": [],
            "completed_tasks": [],
            "future_steps": []
        }

        self.messages = []
        self.resume_mode = False


    def update_task_status(self, new_task_list: List[Dict[str, Any]],
                          updated_future_steps: List[Dict[str, Any]],
                          completed_step: Dict[str, Any]) -> None:
        """
        Update the status of tasks in the task report.

        Args:
            new_task_list: List of updated tasks
            updated_future_steps: List of updated future steps
            completed_step: The step that was just completed
        """
        new_completed_tasks = []
        new_todo_tasks = []

        for task in new_task_list:
            if task["status"] == "completed":
                new_completed_tasks.append(task)
            else:  # Assume any other status is "to-do"
                new_todo_tasks.append(task)

        # Update the task report with the new lists
        self.task_report["completed_tasks"] = new_completed_tasks
        self.task_report["task_list"] = new_todo_tasks

        if updated_future_steps:
            self.task_report['future_steps'] = updated_future_steps