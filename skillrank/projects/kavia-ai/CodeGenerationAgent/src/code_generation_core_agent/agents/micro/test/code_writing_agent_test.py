import json
import os
import asyncio
import logging
import yaml
from typing import Dict, List, Any, Optional

from code_generation_core_agent.agents.framework.micro_agent_registry import MicroAgentRegistry, MicroAgentType
from code_generation_core_agent.agents.micro.code_writing_micro_agent import CodeWritingMicroAgent
from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType
from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.tools.knowledge_tools import KnowledgeTools
from code_generation_core_agent.agents.tools.visible_shell_tools import VisibleShellTools
from code_generation_core_agent.agents.tools.strategic_analysis_tool import StrategicAnalysisTool
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.combined_tools import CombinedTools
from code_generation_core_agent.agents.tools.AgentPreProcessor import AgentPreProcessor
from code_generation_core_agent.agents.tools.tracking.shell_command_call_tracker import ShellCommandCallTracker
from code_generation_core_agent.config import config


class MockToolCallTracker:
    def __init__(self):
        self.logs: List[Dict[str, Any]] = []
    
    def log_tool_call(self, tool_name: str, args: Dict[str, Any], result: Any):
        self.logs.append({
            'name': tool_name,
            'args': args,
            'result': result
        })


# Initialize Docker executor outside of the asyncio context
def initialize_docker_executor(base_path):
    """Initialize Docker executor outside of the asyncio context."""
    configuration = {
        "container_name": "kavia_default_container_image",
    }
    return DockerExecutor(session_dir=base_path, **configuration)


class TestCodeWritingAgent:
    def __init__(self, base_path: str, executor=None):
        self.base_path = base_path
        self.logger = setup_logger("test_code_writing_agent", base_path)
        self.tools_logger = setup_logger("tools", base_path)
        self.tool_call_tracker = MockToolCallTracker()
        
        # Use provided executor or create a new one
        self.executor = executor
        
        # Set up necessary paths and configurations
        self.prompts_base_path = "src/code_generation_core_agent/agents/micro/prompts"
        self.model_name = config.get("LLM", "model")
        
        # Load platform configuration similar to PyQt UI
        micro_base_path = os.path.dirname(self.prompts_base_path)
        platform_loader = PlatformConfigLoader(micro_base_path)
        self.platform = ApplicationType.COMMON
        self.framework = FrameworkType.DEFAULT
        self.micro_agent_config = platform_loader.get_platform_config(self.platform, self.framework)
        
        # Initialize LLM
        self.llm = self._initialize_llm()
        
        # Setup agent tools
        self.agent_tools = self._setup_dynamic_tools()
        
        # Initialize MicroAgentRegistry
        self.micro_agent_registry = MicroAgentRegistry(
            "CodeGeneration", 
            self.micro_agent_config,
            self.logger,
            self.prompts_base_path,
            self.model_name,
            self.base_path,
            None,
            self.agent_tools

        )
        
        # Initialize the agent
        self.agent = self._create_agent()
    
    def _initialize_llm(self) -> LLMInterface:
        """Initialize the LLM interface with appropriate settings."""
        api_key = os.environ.get("ANTHROPIC_API_KEY") or os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("No API key found. Set either ANTHROPIC_API_KEY or OPENAI_API_KEY")
        
        return LLMInterface(
            llm_api_key=api_key,
            session_dir=self.base_path,
            instance_name="test_instance"
        )
    
    def _setup_dynamic_tools(self) -> CombinedTools:
        """Set up the necessary tools for the code writing agent."""
        request_context = {
            "execution_base_path": self.base_path
        }
        
        callback_functions = {
            "terminal_output_callback": lambda x: print(f"Terminal Output: {x}"),
            "browser_output_callback": lambda x: print(f"Browser Output: {x}"),
            "user_query_callback": lambda x: print(f"User Query: {x}"),
            "function_call_callback": lambda x: print(f"Function Call: {x}"),
            "codeview_callback": lambda x: print(f"Code View: {x}"),
            "agent_message_callback": lambda x, y=None, **kwargs: print(f"Agent Message: {x}")
        }
        tools_logger = self.tools_logger
        llm = self.llm
        executor = self.executor
        
        shell_tools = VisibleShellTools(callback_functions,
                                    request_context['execution_base_path'],
                                    logger=tools_logger,
                                    llm=llm, executor=executor)

    

        strategy_tools = StrategicAnalysisTool(callback_functions,
                                          request_context['execution_base_path'],
                                          logger=tools_logger, llm=llm, executor=executor)

        knowledge_tools = KnowledgeTools(callback_functions,
                                     request_context['execution_base_path'],
                                     logger=tools_logger, llm=llm, executor=executor)
        
        container_file_tools = ContainerFileTools(callback_functions,
                                                  request_context['execution_base_path'],
                                                  logger=tools_logger, llm=llm, executor=executor)

        
        tool_instances = [shell_tools, strategy_tools, knowledge_tools, container_file_tools]
        
        agent_pre_processor = AgentPreProcessor(
            callback_functions,
            logger=self.logger,
            shell_command_tracker=ShellCommandCallTracker(self.base_path)
        )
        
        return CombinedTools(
            tool_instances,
            [agent_pre_processor],
            self.base_path,
            logger=self.logger,
            llm=self.llm,
            executor=self.executor
        )
    
    def _create_agent(self) -> CodeWritingMicroAgent:
        """Create and initialize the CodeWritingMicroAgent."""
        # First initialize the micro agents through the registry
        self.micro_agent_registry.init_micro_agents(self.agent_tools)
        
        # Get the CodeWritingAgent from the registry
        agents = self.micro_agent_registry.get_agents(MicroAgentType.CODER_MICRO_AGENTS)
        if "CodeWritingAgent" in agents:
            return agents["CodeWritingAgent"]
        
        # Fallback if agent not in registry (which shouldn't happen if config is correct)
        return CodeWritingMicroAgent(
            llm=self.llm,
            base_path=self.base_path,
            logger=self.logger,
            model_name=self.model_name,
            agent_tools=self.agent_tools,
            prompt_template=self.micro_agent_registry.agent_factory.load_prompt_template("CodeWritingAgent", self.logger),
            tools_to_use=self.micro_agent_config['micro_agents']["CodeWritingAgent"]['tools'],
            setup_steps=self.micro_agent_config['micro_agents']["CodeWritingAgent"].get('setup_steps', []),
            agent_factory=self.micro_agent_registry.agent_factory
        )
    
    
    

    async def run_single_test(self, task: str, subtask: Dict[str, Any] = None, work_item: Dict[str, Any] = None, request_details: Dict[str, Any] = None, streaming: bool = None) -> Dict[str, Any]:
        """
        Run a single test with the provided task.
        
        Args:
            task: The task description for the code writing agent
            subtask: Optional subtask details
            work_item: Optional work item details
            request_details: Optional request details
            streaming: True to use streaming API, False to use non-streaming, None for config default
                
        Returns:
            Dictionary containing the results of the code writing task
        """
        if not subtask:
            subtask = {"current_subtask": "Writing code", "subtask_details": "Implementing the requested functionality"}
            
        if not work_item:
            work_item = {"component_name": "Test Component", "description": "A test component for testing purposes"}
            
        if not request_details:
            request_details = {"base_path": self.base_path}
        
        print(f"\n--- Starting test: {task} ---")
        print(f"Base path: {self.base_path}")
        
        # Determine if we should use streaming mode
        use_streaming = config.getboolean('PROMPTS', 'streaming_support', fallback=False)
        if streaming is not None:
            use_streaming = streaming
        
        print(f"Using streaming mode: {use_streaming}")
        
        # Create tool call log to track calls
        tool_call_log = []
        
        try:
            # Get capabilities from the registry
            capabilities = self.micro_agent_registry.get_capabilities(MicroAgentType.CODER_MICRO_AGENTS)
            
            if use_streaming:
                # Use streaming API
                result = await self._run_streaming_test(
                    self.agent,
                    task, subtask, work_item, request_details,
                    tool_call_log, capabilities
                )
            else:
                # Use non-streaming API
                result = await self.agent.process_request(
                    task,
                    subtask=subtask,
                    work_item=work_item,
                    request_details=request_details,
                    tool_call_log=tool_call_log,
                    capabilities=capabilities
                )
            
            # Parse the result
            try:
                parsed_result = json.loads(result)
                print(f"Result: {json.dumps(parsed_result, indent=2)}")
            except (json.JSONDecodeError, TypeError):
                print(f"Raw result (not JSON): {result}")
                parsed_result = {"raw_result": result}
            
            # Print tool calls for debugging
            if tool_call_log:
                print(f"\nTool calls made ({len(tool_call_log)}):")
                for idx, call in enumerate(tool_call_log):
                    print(f"  {idx+1}. {call['name']}")
            
            return parsed_result
            
        except Exception as e:
            print(f"Error running test: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return {"error": str(e)}

    async def _run_streaming_test(self, agent, task, subtask, work_item, request_details, tool_call_log, capabilities):
        """
        Helper method to run a test using the streaming API and collect the final result.
        """
        if not hasattr(agent, 'process_request_streaming'):
            raise ValueError("Agent does not support streaming mode")
        
        # Get the streaming generator
        streaming_generator = agent.process_request_streaming(
            task,
            subtask=subtask,
            work_item=work_item,
            request_details=request_details,
            tool_call_log=tool_call_log,
            capabilities=capabilities
        )
        
        # Print chunks as they arrive
        print("\nStreaming output:")
        async for chunk in streaming_generator:
            if isinstance(chunk, dict):
                if "final" in chunk:
                    print(f"[FINAL: {chunk['final']}] {chunk.get('content', '')}")
                else:
                    print(f"[DICT] {json.dumps(chunk)}")
            else:
                print(f"{chunk}")
            #await asyncio.sleep(0.1)
        
        # The validator attached to the agent should have the final response
        if hasattr(agent._validator, 'final_response'):
            return agent._validator.final_response
        
        # Fallback if no final response in validator
        return json.dumps({
            "completion_status": "UNKNOWN",
            "note": "Final response not available - agent may not have completed properly"
        })

def main():
    # Setup base configuration
    base_path = os.environ.get("TEST_BASE_PATH", "/Users/<USER>/Work/Kavia/workarea/kavia/codegen-tic-tac-toe-m")
    
    # Create the base path if it doesn't exist
    if not os.path.exists(base_path):
        os.makedirs(base_path)
    
    # Initialize Docker executor outside of asyncio context
    docker_executor = initialize_docker_executor(base_path)
    
    # Define the async main function
    async def async_main():
        """# Parse command line arguments
        import argparse
        parser = argparse.ArgumentParser(description='Test the CodeWritingAgent')
        parser.add_argument('--suite', action='store_true', help='Run the test suite')
        parser.add_argument('--streaming', choices=['true', 'false'], 
                           help='Enable or disable streaming mode')
        parser.add_argument('--prompt', type=str, 
                           help='Custom prompt for single test mode')
        args = parser.parse_args()
        
        args =
        # Convert streaming arg to boolean
        streaming_mode = None
        if args.streaming:
            streaming_mode = args.streaming.lower() == 'true'"""
        
        # Initialize the test harness with the executor
        tester = TestCodeWritingAgent(base_path, executor=docker_executor)
        
        streaming_mode = True
        # Run the tests
        user_prompt = "Write a React/JS program for the tic-tac-toe game"
        await tester.run_single_test(user_prompt, streaming=streaming_mode)
        
        print("\nTesting complete.")
    
    # Run the async main function
    asyncio.run(async_main())


if __name__ == "__main__":
    main()