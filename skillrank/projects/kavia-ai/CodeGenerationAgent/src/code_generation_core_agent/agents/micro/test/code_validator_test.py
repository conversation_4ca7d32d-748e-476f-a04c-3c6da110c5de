import logging
import os
import tempfile
import shutil
from typing import List, Tu<PERSON>, Any

import pytest

from code_generation_core_agent.agents.micro.generic_file_writer_agent import CodeAgentValidator


# ────────────────────────────── STUBS ──────────────────────────────
class StubContainerFileTools:
    """Mimic the ContainerFileTools interface and capture every call."""

    def __init__(self):
        self.calls: List[Tuple[str, Any]] = []
        # flag so we can toggle the outcome of validate_build
        self._build_ok: bool = True
        self._build_msg: str = ""

    # public helpers -------------------------------------------------
    def set_build_outcome(self, ok: bool, msg: str = ""):
        self._build_ok, self._build_msg = ok, msg

    # tool‑API -------------------------------------------------------
    def write_file(self, path: str, content: str):
        self.calls.append(("write", path, content))
        return True

    def edit_file(self, block: str):
        self.calls.append(("edit", block))
        return {"status": "OK"}

    def delete_file(self, path: str):
        self.calls.append(("delete", path))
        return True

    def validate_build(self, workspace: str):
        self.calls.append(("validate", workspace))
        return (self._build_ok, self._build_msg)


class StubAndroidEmulatorTool:
    """Mock AndroidEmulatorTool for testing mobile linting."""
    
    def __init__(self):
        self.calls: List[Tuple[str, Any]] = []
        self._flutter_result = True
        self._android_result = True
    
    def set_flutter_analysis_result(self, result):
        self._flutter_result = result
    
    def set_android_analysis_result(self, result):
        self._android_result = result
    
    def analyze_flutter_code(self, project_path):
        self.calls.append(("analyze_flutter", project_path))
        return self._flutter_result
    
    def analyze_android_code(self, project_path):
        self.calls.append(("analyze_android", project_path))
        return self._android_result


class StubAgentTools:  # wraps the single stub so structure matches production
    def __init__(self, cft: StubContainerFileTools, include_android_tool: bool = False):
        self.tool_set = {"ContainerFileTools": cft}
        if include_android_tool:
            self.tool_set["AndroidEmulatorTool"] = StubAndroidEmulatorTool()


class StubAgent:  # minimal fields CodeAgentValidator touches
    def __init__(self):
        self.container_workspace = "/tmp/workspace"
        self.parser = None
        self.doc_mode = False


# common fixture ----------------------------------------------------
@pytest.fixture
def validator():
    cft = StubContainerFileTools()
    tools = StubAgentTools(cft)
    agent = StubAgent()
    v = CodeAgentValidator(
        logger=logging.getLogger("validator-tests"),
        agent_tools=tools,
        base_path="/tmp/base",  # paths are joined to this
        agent=agent,
    )
    # expose internals so tests can inspect side‑effects
    v._cft = cft  # type: ignore[attr-defined]
    return v


@pytest.fixture
def validator_with_android_tool():
    cft = StubContainerFileTools()
    tools = StubAgentTools(cft, include_android_tool=True)
    agent = StubAgent()
    
    # Create a temporary directory for testing
    temp_dir = tempfile.mkdtemp()
    
    v = CodeAgentValidator(
        logger=logging.getLogger("validator-tests"),
        agent_tools=tools,
        base_path=temp_dir,
        agent=agent,
    )
    v._cft = cft
    v._android_tool = tools.tool_set["AndroidEmulatorTool"]
    v._temp_dir = temp_dir
    return v


@pytest.fixture(autouse=True)
def cleanup_temp_dirs():
    """Clean up temporary directories after tests."""
    yield


# helper: drive the validator like the framework does ---------------
async def run_validation(v: CodeAgentValidator, payload: str, complete: bool = True):
    """Call the validator and return (status, cft.calls)."""
    status = await v(payload, is_complete=complete)
    return status, v._cft.calls  # type: ignore[attr-defined]


# ─────────────────────────────── TESTS ─────────────────────────────
@pytest.mark.asyncio
async def test_write_operation_success(validator):
    payload = (
        "Explanation: create hello file\n"
        "````write file=\"hello.txt\"\n"
        "Hello world!\n"
        "````\n"
    )
    status, calls = await run_validation(validator, payload)

    # container write happens exactly once with correct args
    writes = [c for c in calls if c[0] == "write"]
    assert writes == [("write", "/tmp/base/hello.txt", "Hello world!\n")]

    # validator marks the run complete & success message present
    assert status.state == "complete"
    assert "Wrote to file" in status.completion_message


@pytest.mark.asyncio
async def test_edit_operation_success(validator):
    diff = (
        "<<<<<<< SEARCH\n"
        "pritn('oops')\n"
        "=======\n"
        "print('oops')\n"
        ">>>>>>> REPLACE\n"
    )
    payload = (
        "Explanation: fix typo\n"
        "````edit file=\"foo.py\"\n"
        f"{diff}"
        "````\n"
    )

    status, calls = await run_validation(validator, payload)

    edits = [c for c in calls if c[0] == "edit"]
    assert len(edits) == 1
    # first line of edit block is absolute path followed by newline
    abs_path, _ = edits[0][1].split("\n", 1)
    assert abs_path == "/tmp/base/foo.py"

    assert status.state == "complete"
    assert "Successfully made" in status.completion_message


@pytest.mark.asyncio
async def test_delete_operation_success(validator):
    payload = "````delete file=\"obsolete.cfg\"````\n"
    status, calls = await run_validation(validator, payload)

    deletes = [c for c in calls if c[0] == "delete"]
    assert deletes == [("delete", "/tmp/base/obsolete.cfg")]
    assert status.state == "complete"
    assert "Deleted file" in status.completion_message


@pytest.mark.asyncio
async def test_build_error_detection(validator):
    # arrange: make build fail
    validator._cft.set_build_outcome(False, "compile failed")  # type: ignore[attr-defined]

    payload = (
        "Explanation: create bad file\n"
        "````write file=\"bad.py\"\n"
        "print('boom')\n"
        "````\n"
    )
    status, _ = await run_validation(validator, payload)

    assert status.state == "partial"
    assert "Build error detected" in status.completion_message


@pytest.mark.asyncio
async def test_duplicate_operations_skipped(validator):
    payload = (
        "Explanation: duplicate writes\n"
        "````write file=\"dup.txt\"\nfirst\n````\n"
        "Explanation: duplicate writes again\n"
        "````write file=\"dup.txt\"\nfirst\n````\n"
    )
    status, calls = await run_validation(validator, payload)

    # only one actual write should happen because hashes match
    writes = [c for c in calls if c[0] == "write"]
    assert len(writes) == 1
    assert status.state == "complete"


@pytest.mark.asyncio
async def test_no_valid_operations_triggers_error(validator):
    status, _ = await run_validation(validator, "Just some commentary", complete=True)

    assert status.state == "partial"
    assert "No valid file operations detected" in status.completion_message


@pytest.mark.asyncio
async def test_flutter_project_detection_and_linting_success(validator_with_android_tool):
    """Test that Flutter projects are detected and linting runs successfully."""
    # Create a pubspec.yaml file to simulate Flutter project
    pubspec_path = os.path.join(validator_with_android_tool._temp_dir, "pubspec.yaml")
    with open(pubspec_path, "w") as f:
        f.write("name: test_flutter_app\n")
    
    # Set successful Flutter analysis
    validator_with_android_tool._android_tool.set_flutter_analysis_result(True)
    
    payload = (
        "````write file=\"lib/main.dart\"\n"
        "void main() { print('Hello Flutter'); }\n"
        "````\n"
    )
    
    status, calls = await run_validation(validator_with_android_tool, payload)
    
    # Verify Flutter analysis was called
    flutter_calls = [c for c in validator_with_android_tool._android_tool.calls if c[0] == "analyze_flutter"]
    assert len(flutter_calls) == 1
    assert flutter_calls[0][1] == validator_with_android_tool.base_path
    
    # Should complete successfully
    assert status.state == "complete"
    assert "Mobile code analysis failed" not in status.completion_message


@pytest.mark.asyncio
async def test_flutter_project_linting_failure(validator_with_android_tool):
    """Test Flutter project with linting errors."""
    # Create pubspec.yaml
    pubspec_path = os.path.join(validator_with_android_tool._temp_dir, "pubspec.yaml")
    with open(pubspec_path, "w") as f:
        f.write("name: test_flutter_app\n")
    
    # Set Flutter analysis to fail
    validator_with_android_tool._android_tool.set_flutter_analysis_result(
        "Flutter analysis issues: Missing semicolon on line 5"
    )
    
    payload = (
        "````write file=\"lib/main.dart\"\n"
        "void main() { print('Hello Flutter') }\n"  # Missing semicolon
        "````\n"
    )
    
    status, calls = await run_validation(validator_with_android_tool, payload)
    
    # Should detect the error
    assert status.state == "partial"
    assert "Mobile code analysis failed" in status.completion_message
    assert "Missing semicolon" in status.completion_message


@pytest.mark.asyncio
async def test_android_project_detection_and_linting_success(validator_with_android_tool):
    """Test that Android projects are detected and linting runs successfully."""
    # Create gradlew file to simulate Android project
    gradlew_path = os.path.join(validator_with_android_tool._temp_dir, "gradlew")
    with open(gradlew_path, "w") as f:
        f.write("#!/bin/bash\n# Gradle wrapper\n")
    
    # Set successful Android analysis
    validator_with_android_tool._android_tool.set_android_analysis_result(True)
    
    payload = (
        "````write file=\"app/src/main/java/MainActivity.java\"\n"
        "public class MainActivity {}\n"
        "````\n"
    )
    
    status, calls = await run_validation(validator_with_android_tool, payload)
    
    # Verify Android analysis was called
    android_calls = [c for c in validator_with_android_tool._android_tool.calls if c[0] == "analyze_android"]
    assert len(android_calls) == 1
    
    assert status.state == "complete"


@pytest.mark.asyncio
async def test_android_project_linting_failure(validator_with_android_tool):
    """Test Android project with linting errors."""
    # Create gradlew file
    gradlew_path = os.path.join(validator_with_android_tool._temp_dir, "gradlew")
    with open(gradlew_path, "w") as f:
        f.write("#!/bin/bash\n")
    
    # Set Android analysis to fail
    validator_with_android_tool._android_tool.set_android_analysis_result(
        "Android analysis issues: Unused import detected"
    )
    
    payload = (
        "````write file=\"app/src/main/java/MainActivity.java\"\n"
        "import java.util.List;\npublic class MainActivity {}\n"  # Unused import
        "````\n"
    )
    
    status, calls = await run_validation(validator_with_android_tool, payload)
    
    assert status.state == "partial"
    assert "Mobile code analysis failed" in status.completion_message
    assert "Unused import" in status.completion_message


@pytest.mark.asyncio
async def test_non_mobile_project_skips_linting(validator_with_android_tool):
    """Test that non-mobile projects skip mobile linting."""
    # Don't create any mobile project files
    
    payload = (
        "````write file=\"server.py\"\n"
        "print('Hello Python')\n"
        "````\n"
    )
    
    status, calls = await run_validation(validator_with_android_tool, payload)
    
    # No mobile analysis should be called
    assert len(validator_with_android_tool._android_tool.calls) == 0
    assert status.state == "complete"


@pytest.mark.asyncio
async def test_mobile_linting_without_android_tool(validator):
    """Test mobile project when AndroidEmulatorTool is not available."""
    # Create pubspec.yaml but no AndroidEmulatorTool
    temp_dir = tempfile.mkdtemp()
    validator.base_path = temp_dir
    
    try:
        pubspec_path = os.path.join(temp_dir, "pubspec.yaml")
        with open(pubspec_path, "w") as f:
            f.write("name: test_app\n")
        
        payload = (
            "````write file=\"lib/main.dart\"\n"
            "void main() {}\n"
            "````\n"
        )
        
        status, calls = await run_validation(validator, payload)
        
        # Should complete successfully even without mobile linting
        assert status.state == "complete"
        assert "AndroidEmulatorTool not available" not in status.completion_message  # This is logged, not in completion message
        
    finally:
        shutil.rmtree(temp_dir)


@pytest.mark.asyncio
async def test_mobile_linting_exception_handling(validator_with_android_tool):
    """Test that exceptions in mobile linting don't break the whole validation."""
    # Create pubspec.yaml
    pubspec_path = os.path.join(validator_with_android_tool._temp_dir, "pubspec.yaml")
    with open(pubspec_path, "w") as f:
        f.write("name: test_app\n")
    
    # Mock the tool to raise an exception
    def raise_exception(project_path):
        raise Exception("Tool execution failed")
    
    validator_with_android_tool._android_tool.analyze_flutter_code = raise_exception
    
    payload = (
        "````write file=\"lib/main.dart\"\n"
        "void main() {}\n"
        "````\n"
    )
    
    status, calls = await run_validation(validator_with_android_tool, payload)
    
    # Should still complete, but with error message
    assert status.state == "partial"
    assert "Mobile code analysis failed" in status.completion_message
    assert "Tool execution failed" in status.completion_message


@pytest.mark.asyncio
async def test_flutter_project_has_precedence_over_android(validator_with_android_tool):
    """Test that Flutter projects are detected even when they contain Android structure."""
    # Create both pubspec.yaml and gradlew (Flutter projects contain Android)
    pubspec_path = os.path.join(validator_with_android_tool._temp_dir, "pubspec.yaml")
    with open(pubspec_path, "w") as f:
        f.write("name: test_flutter_app\n")
    
    android_dir = os.path.join(validator_with_android_tool._temp_dir, "android")
    os.makedirs(android_dir, exist_ok=True)
    gradlew_path = os.path.join(android_dir, "gradlew")
    with open(gradlew_path, "w") as f:
        f.write("#!/bin/bash\n")
    
    validator_with_android_tool._android_tool.set_flutter_analysis_result(True)
    
    payload = (
        "````write file=\"lib/main.dart\"\n"
        "void main() {}\n"
        "````\n"
    )
    
    status, calls = await run_validation(validator_with_android_tool, payload)
    
    # Should call Flutter analysis, not Android
    flutter_calls = [c for c in validator_with_android_tool._android_tool.calls if c[0] == "analyze_flutter"]
    android_calls = [c for c in validator_with_android_tool._android_tool.calls if c[0] == "analyze_android"]
    
    assert len(flutter_calls) == 1
    assert len(android_calls) == 0


# Add cleanup for temp directories
@pytest.fixture(autouse=True) 
def cleanup_validator_temp_dirs(request):
    """Clean up temporary directories created by validators."""
    yield
    
    # Clean up any validator with temp directories
    if hasattr(request, 'node'):
        for item in request.node.funcargs.values():
            if hasattr(item, '_temp_dir'):
                try:
                    shutil.rmtree(item._temp_dir)
                except:
                    pass  # Ignore cleanup errors


# ────────────────────────── MAIN SHIM ────────────────────────────
def main() -> None:
    """
    Run this test module directly:

        python test_code_agent_validator.py
    """
    import pytest
    import sys

    sys.exit(pytest.main([__file__]))


if __name__ == "__main__":
    main()