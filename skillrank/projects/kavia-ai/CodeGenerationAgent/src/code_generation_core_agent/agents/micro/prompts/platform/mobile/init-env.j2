Given the task of configuring a development environment for {{ component_name }}
({{ work_item_details }}) and considering the development system uses os
Linux and architecture {{ platform }} generate a list of components required for {{ application_type }} development. For each item in
your list, use the tools available to you to check that it is present and has an acceptable version, and if the item
is not present, install it.

Make sure that after defining dependencies necessary to the project you call get_dependency_version from the DependencyTools to check if any version that differs from required version returned by the tool.
Consider the versions returned from the get_dependency_version as the source of truth and store them to be used as reference further when creating the project's dependency file.
If one or more of the versions on the file differ from the source of truth you MUST overwrite the dependency version on the file with the correct version from the tool's output. For dependencies that are not returned by the tool which means they don't have required versions, you may use any stable version.
You should check the versions before writing them to the dependencies file. AND USE THE RIGHT VERSIONS when creating the file with necessary dependencies.

Required components to check and install:
1. Android SDK
2. Required Android SDK components:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android SDK Platform
{% if application_type == 'android' %}
   - Android Emulator
   - Google USB Driver
3. Java Development Kit (JDK)
4. Gradle Build Tool
{% else %}
3. Flutter SDK
4. Flutter dependencies:
   - Dart SDK
{% endif %}

Considerations:
- Ensure to only install the necessary components for {{ application_type }} development
{% if application_type == 'android' %}
- Configure ANDROID_HOME and JAVA_HOME environment variables
- Verify Android Studio or necessary command line tools are properly installed
{% endif %}

{% if application_type == 'android' %}
If possible and it is necessary, use install commands that request the latest version of an item without specifying a specific version number.
{% endif %}

In the case of any failing commands, use the results of each command to overcome the failure in a subsequent attempt. Note
that you are not creating the target app but only checking its dependencies. If you start a process with start_long_running_process
be sure to clean up by calling terminate_process.

Once all items are present create the file "{{ base_path }}/.init/.init-env".

Please format your final response as a JSON object with the following structure:
{
   "actions": "<summary of actions taken>"
}