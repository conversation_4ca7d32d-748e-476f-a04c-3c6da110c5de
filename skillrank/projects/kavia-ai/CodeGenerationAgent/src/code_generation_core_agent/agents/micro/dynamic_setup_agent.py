import os
import json
import asyncio
import traceback
import re
from typing import Dict, Any, Optional, List
from datetime import datetime
from code_generation_core_agent.agents.micro.agent_system import AgentRegistry, GenericMicroAgent
from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerInfo
from code_generation_core_agent.agents.micro.dsa.ContainerSetupPlan import ContainerSetupPlan
from code_generation_core_agent.agents.micro.dsa.ContainerStepStatus import ContainerStepStatus
from code_generation_core_agent.agents.micro.dsa.ContainerSetupLevel import ContainerSetupLevel
from code_generation_core_agent.agents.micro.dsa.ContainerSetupExecution import ContainerSetupExecution
from code_generation_core_agent.agents.micro.dsa.devops_architect_agent import DevOpsArchitectAgent
from code_generation_core_agent.agents.micro.dsa.devops_analyst_agent import DevOpsAnalystAgent
from code_generation_core_agent.agents.micro.dsa.devops_engineer_agent import DevOpsEngineerAgent
from code_generation_core_agent.agents.micro.dsa.devops_qa_agent import DevOpsQ<PERSON>gent
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.config import config


MAX_STEP_ATTEMPTS = 3


@AgentRegistry.register("DynamicSetupAgent")
class DynamicSetupAgent(GenericMicroAgent):
    """
    Dynamic Setup Agent that analyzes development environment requirements within existing Docker containers and generates
    customized setup steps for any development environment using LLM for inference.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.status_callback = None  # Callback for status reporting
        self.project_schema = None  # Reference to the project schema for script updates
        self.manifest_path = None  # Path to the manifest file for saving updates
        base_path = self.base_path
        agent_tools = self.agent_tools
        
        # Load consolidated mode configuration
        self.consolidated_mode = config.getboolean('DYNAMIC_SETUP', 'consolidated_mode', fallback=True)
        self.qa_enabled = config.getboolean('DYNAMIC_SETUP', 'qa_enabled', fallback=False)
        
        # Log configuration settings
        self.logger.info(f"DynamicSetupAgent initialized with consolidated_mode={self.consolidated_mode}, qa_enabled={self.qa_enabled}")
        
        # Determine per-agent LLM models from config with sensible defaults
        def _get_agent_model(mapping_key: str, default_model: str = "gpt-4.1") -> str:
            try:
                mapped = config.get('LLM_MODEL_MAPPING', mapping_key)
                return mapped if mapped else default_model
            except Exception:
                return default_model

        architect_model = _get_agent_model('DevOpsArchitectAgent')
        analyst_model = _get_agent_model('DevOpsAnalystAgent')
        engineer_model = _get_agent_model('DevOpsEngineerAgent')
        qa_model = _get_agent_model('DevOpsQAAgent')

        self.devops_architect = DevOpsArchitectAgent(base_path, architect_model)
        self.devops_analyst = DevOpsAnalystAgent(base_path, analyst_model)
        self.devops_engineer = DevOpsEngineerAgent(model=engineer_model, base_path=base_path, agent_tools=agent_tools, max_attempts=MAX_STEP_ATTEMPTS)
        self.devops_qa = DevOpsQAAgent(model=qa_model, base_path=base_path, max_attempts=MAX_STEP_ATTEMPTS)
        self.execution = ContainerSetupExecution(base_path=base_path,executor=agent_tools.executor)
    
    async def process_request(self, subtask=None,
                            work_item=None, request_details=None,
                            tool_call_log=None, capabilities: dict = None, container_name=None):
        try:
            self.logger.info(f"Dynamic setup agent processing request")
            
            # Extract status callback from request_details if available
            if request_details and 'status_callback' in request_details:
                self.status_callback = request_details['status_callback']
            
            # Extract project schema from request_details if available
            if request_details and 'project_schema' in request_details:
                self.project_schema = request_details['project_schema']
            
            # Extract manifest path from request_details if available
            if request_details and 'manifest_path' in request_details:
                self.manifest_path = request_details['manifest_path']
            
            # Extract and combine container information from work_item and request_details
            container_info = self._get_container_info(work_item, request_details, container_name)        
            
            if not container_info:
                raise Exception("No container information found for analysis")
            
            # Check consolidated mode flag to determine execution path
            if self.consolidated_mode:
                self.logger.info(f"Using consolidated mode for {container_info.container_name}")
                return await self._process_request_consolidated(container_info)
            else:
                self.logger.info(f"Using legacy step-by-step mode for {container_info.container_name}")
                return await self._process_request_legacy(container_info)

        except Exception as e:            
            print(traceback.format_exc())
            self.logger.error(f"Error processing request: {e}", exc_info=True)
            if 'container_info' in locals():
                self._report_status(container_info.container_name, 'failed', f"Setup plan execution failed for {container_info.framework}", str(e))
            raise e
    
    async def _process_request_consolidated(self, container_info: ContainerInfo):
        """
        Process request using consolidated mode (single script generation instead of step-by-step execution).
        This is the new streamlined approach that generates one bash script with all setup steps.
        """
        self._report_status(container_info.container_name, 'analyzing', f"Starting consolidated setup analysis for {container_info.framework}")
        
        # Step 1: Generate initial consolidated script using architect
        self.logger.info(f"Generating initial consolidated script for {container_info.container_name}")
        consolidated_script = await self.devops_architect.generate_consolidated_script(container_info)
        self.logger.info(f"Initial consolidated script generated for {container_info.container_name}")
        
        # Step 2: Architect-Analyst loop for script refinement (max 5 iterations)
        max_refine_iterations = 5
        for iteration in range(max_refine_iterations):
            self.logger.info(f"Script review iteration {iteration + 1}/{max_refine_iterations} for {container_info.container_name}")
            
            # Analyst reviews the script
            review_result = await self.devops_analyst.review_consolidated_script(
                script_content=consolidated_script,
                container_info=container_info,
                min_recommendation_level=ContainerSetupLevel.HIGH
            )
            
            # Check if analyst approves the script
            if review_result.get("approved", False):
                self.logger.info(f"Script approved by analyst on iteration {iteration + 1} for {container_info.container_name}")
                break
            
            # If not approved and we haven't reached max iterations, refine the script
            if iteration < max_refine_iterations - 1:
                self.logger.info(f"Script not approved, refining based on analyst feedback for {container_info.container_name}")
                consolidated_script = await self.devops_architect.refine_consolidated_script(
                    script_content=consolidated_script,
                    container_info=container_info,
                    review_feedback=review_result
                )
            else:
                # Max iterations reached without approval
                raise Exception(f"Consolidated script could not be finalized after {max_refine_iterations} iterations for {container_info.container_name}. Last review: {review_result}")
        
        # Step 3: Execute the approved consolidated script
        self._report_status(container_info.container_name, 'executing', f"Executing consolidated setup script for {container_info.framework}")
        self.logger.info(f"Executing consolidated script for {container_info.container_name}")
        
        execution_result = await self.devops_engineer.execute_consolidated_script(
            script_content=consolidated_script,
            container_info=container_info
        )
        
        self.logger.info(f"Consolidated script executed successfully for {container_info.container_name}")
        
        # Step 4: Parse sections and split into canonical scripts
        self.logger.info(f"Parsing consolidated script sections for {container_info.container_name}")
        canonical_scripts = self._parse_consolidated_script_sections(consolidated_script, container_info)
        
        # Step 5: Write canonical scripts to .init directory
        self.logger.info(f"Writing canonical scripts to .init directory for {container_info.container_name}")
        self._write_canonical_scripts_to_init(canonical_scripts, container_info)
        
        # Step 6: Update project schema with canonical commands
        self.logger.info(f"Updating project schema with canonical commands for {container_info.container_name}")
        self._update_project_schema_with_canonical_scripts(canonical_scripts, container_info.container_name)
        
        self._report_status(container_info.container_name, 'success', f"Consolidated setup completed successfully for {container_info.framework}")
        
        return {
            "status": "success",
            "message": f"Consolidated setup completed successfully for {container_info.framework}",
            "mode": "consolidated",
            "execution_result": execution_result,
            "canonical_scripts": canonical_scripts,
            "container_info": container_info
        }
    
    async def _process_request_legacy(self, container_info: ContainerInfo):
        """
        Process request using legacy step-by-step mode (original implementation).
        This maintains backward compatibility when consolidated mode is disabled.
        """
        # Step 1: Check for existing setup plan and resume if found
        container_setup_plan = self.execution.check_for_existing_plan(container_info)
        if container_setup_plan:
            self._report_status(container_info.container_name, 'resuming', f"Resuming setup from existing plan for {container_info.framework}")
            
            # Check if setup is already complete
            if self.execution.is_setup_complete(container_setup_plan):
                self._report_status(container_info.container_name, 'success', f"Setup already completed for {container_info.framework}")
                return {"status": "success", "message": "Setup already completed"}
        else:
            # Step 2: Analyze container and create new plan                
            self._report_status(container_info.container_name, 'analyzing', f"Starting setup plan analysis for {container_info.framework}")            
            container_setup_plan = await self._analyze_container_and_create_plan(container_info)
            self._report_status(container_info.container_name, 'analyzing', f"Setup plan created for {container_info.framework}")                
            
        # Step 3: Execute the setup plan
        self._report_status(container_info.container_name, 'executing', f"Starting setup plan execution for {container_info.framework}")            
        await self._execute_container_setup_plan(container_info, container_setup_plan)        
        self._report_status(container_info.container_name, 'success', f"Setup plan execution completed successfully for {container_info.framework}")            
        return {"status": "success"}
    
    async def _analyze_container_and_create_plan(self, container_info: ContainerInfo) -> ContainerSetupPlan:
        max_iterations = 3  
        plan : ContainerSetupPlan = await self.devops_architect.generate_initial_plan(container_info)
        
        for i in range(max_iterations):
            plan = await self.devops_analyst.review_plan(plan)
            # Early exit if analyst approves
            if plan.qa_approved:
                self.logger.info(f"Plan approved by analyst on iteration {i+1}")
                break
            # Architect refines the plan based on the full review
            plan = await self.devops_architect.refine_plan_from_analyst_feedback(plan)

        # End analysis phase metrics
        self.execution.save_container_setup_plan(plan)
        return plan
    
    async def _execute_container_setup_plan(self, container_info: ContainerInfo, container_setup_plan: ContainerSetupPlan) -> str:
        """Execute the development environment setup plan within existing container with QA validation (no DevOpsOrchestrator)."""
        max_attempts = self.devops_qa.max_attempts

        # Execute steps in dependency order with QA validation
        # Use a while loop to handle dynamic plan updates
        while True:
            
            # Get the next pending step in execution order
            next_step = container_setup_plan.get_next_pending_step()
            
            # If no pending steps, we're done
            if not next_step:
                break
                        
            while next_step.retry_attempts <= max_attempts:                
                # Step 1: DevOps Engineer executes the step
                next_step = await self.devops_engineer.execute_step(container_setup_plan, next_step)
                
                # Check if engineer is requesting architect help
                if next_step.needs_architect_help:
                    container_setup_plan = await self.devops_architect.refine_plan_from_engineer_feedback(container_setup_plan, next_step)
                    self.execution.save_container_setup_plan(container_setup_plan)
                    break
                                
                # Step 2: QA reviews the execution
                next_step = await self.devops_qa.review_step(container_setup_plan, next_step)
                
                if next_step.qa_approved:
                    self.logger.info(f"Step '{next_step.name}' approved by QA on attempt {next_step.retry_attempts}")
                    next_step.status = ContainerStepStatus.COMPLETED
                    next_step.reset_retry_attempts()
                    self.execution.save_container_setup_plan(container_setup_plan)
                    break                    
                else:
                    self.logger.warning(f"Step '{next_step.name}' needs retry on attempt {next_step.retry_attempts}: {next_step.qa_issues}")                    
                    self.execution.save_container_setup_plan(container_setup_plan)                    
                    await asyncio.sleep(2)
                
        # Update project schema if available with scripts
        self._update_project_schema_with_scripts(container_setup_plan, container_info.container_name)
    
    def _report_status(self, container_name: str, status: str, message: str, error: str = None):
        """Report status using callback if available"""
        self.logger.info(f"Reporting status for {container_name}: {status} - {message}")
        if self.status_callback:
            try:
                self.status_callback(container_name, status, message, error)
            except Exception as e:
                self.logger.error(f"Error calling status callback: {e}")
        else:
            self.logger.info(f"Status for {container_name}: {status} - {message}")

    def _get_container_info(self, work_item: Dict[str, Any], request_details: Dict[str, Any], container_name: str = None) -> ContainerInfo:
        """Extract container information from work_item or request_details and return as ContainerInfo object."""
        container_data = {}
        
        # Try to get container info from request_details first
        if request_details and 'container_info' in request_details:
            container_data = request_details['container_info']
        elif request_details and 'container_name' in request_details:
            container_name = request_details['container_name']
        
        # If we have a container_name, try to find it in work_item containers
        if container_name and work_item and 'containers' in work_item:
            for container in work_item['containers']:
                if container.get('container_name') == container_name:
                    container_data = container
                    break
        
        # If still no container_data, use work_item as fallback
        if not container_data and work_item:
            container_data = work_item
        
        # Ensure 'description' is always present - use work_item description if available
        if 'description' not in container_data or not container_data['description']:
            if work_item and 'description' in work_item and work_item['description']:
                container_data['description'] = work_item['description']
            else:
                container_data['description'] = ''
        
        container_data['workspace'] = request_details['container_path'] + '/' + container_name

        # safely remove workspace and recreate it
        workspace_path = container_data['workspace']
        
        # TODO: handle existing repo, for now assume it's a new repo
        os.makedirs(workspace_path, exist_ok=True)

        return ContainerInfo.from_dict(container_data)
    
    def _update_project_schema_with_scripts(self, container_setup_plan, container_name: str) -> bool:
        """
        Update the project schema with the generated scripts for a specific container.
        
        Args:
            container_setup_plan: The ContainerSetupPlan instance containing scripts
            container_name: Name of the container to update
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        # Validate that project schema exists
        if not hasattr(self, 'project_schema') or not self.project_schema:
            self.logger.warning("Project schema not available, skipping schema update")
            return False
        
        # Get scripts from the plan
        scripts = container_setup_plan.get_all_output_scripts()
        script_dict = {name: script.code for name, script in scripts.items()}
        
        # Find the container in the project schema
        container = self.project_schema.get_container_by_name(container_name)
        if not container:
            self.logger.error(f"Container {container_name} not found in project schema")
            return False
        
        # Update the container with the generated scripts
        if "install" in script_dict:
            container.installCommand = script_dict["install"]
        
        if "build" in script_dict:
            container.buildCommand = script_dict["build"]
        
        if "start" in script_dict:
            container.startCommand = script_dict["start"]
                
        self.logger.info(f"Successfully updated project schema with scripts for container {container_name}")
        
        # Save to manifest if manifest path exists
        if self.manifest_path and os.path.exists(self.manifest_path):
            self.project_schema.save_to_manifest(self.manifest_path)
        
        return True
    
    def _parse_consolidated_script_sections(self, consolidated_script: str, container_info: ContainerInfo) -> Dict[str, str]:
        """
        Parse section markers from consolidated script and split into canonical scripts.
        
        Args:
            consolidated_script: The complete consolidated bash script
            container_info: Container information for context
            
        Returns:
            Dict[str, str]: Mapping of script names to their content
        """
        self.logger.info(f"Parsing consolidated script sections for {container_info.container_name}")
        
        # Define the expected section markers
        section_markers = [
            "INSTALL",
            "SCAFFOLD", 
            "DEPS",
            "BUILD",
            "TEST",
            "START",
            "VALIDATE",
            "STOP"
        ]
        
        # Parse sections using regex to find section boundaries
        sections = {}
        script_lines = consolidated_script.split('\n')
        
        current_section = None
        current_content = []
        preamble_content = []
        found_first_section = False
        
        for line in script_lines:
            # Check if this line is a section marker
            section_match = re.match(r'#\s*===\s*COMMAND:\s*(\w+)\s*===', line.strip())
            
            if section_match:
                # Save previous section if exists
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                    self.logger.info(f"Found section: {current_section} with {len(current_content)} lines")
                
                # Start new section
                current_section = section_match.group(1)
                current_content = []
                found_first_section = True
            elif found_first_section:
                # Add line to current section (only after first section marker)
                current_content.append(line)
            else:
                # Collect preamble content (before first section marker)
                preamble_content.append(line)
        
        # Save the last section
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()
            self.logger.info(f"Found section: {current_section} with {len(current_content)} lines")
        
        # Store preamble content for inclusion in scripts
        preamble = '\n'.join(preamble_content).strip()
        if preamble:
            self.logger.info(f"Found preamble with {len(preamble_content)} lines")
            sections["PREAMBLE"] = preamble
        
        # Create canonical script mappings based on TASK-11 requirements
        canonical_scripts = {}
        
        # Get preamble content for inclusion in scripts
        preamble = sections.get("PREAMBLE", "")
        
        # install.sh = PREAMBLE + INSTALL + SCAFFOLD + DEPS
        install_parts = []
        if preamble:
            install_parts.append(preamble)
        if "INSTALL" in sections:
            install_parts.append(sections["INSTALL"])
        if "SCAFFOLD" in sections:
            install_parts.append(sections["SCAFFOLD"])
        if "DEPS" in sections:
            install_parts.append(sections["DEPS"])
        
        if install_parts:
            canonical_scripts["install"] = '\n\n'.join(install_parts)
        
        # scaffold.sh = PREAMBLE + SCAFFOLD (standalone)
        scaffold_parts = []
        if preamble:
            scaffold_parts.append(preamble)
        if "SCAFFOLD" in sections:
            scaffold_parts.append(sections["SCAFFOLD"])
        
        if scaffold_parts:
            canonical_scripts["scaffold"] = '\n\n'.join(scaffold_parts)
        
        # build.sh = PREAMBLE + BUILD
        build_parts = []
        if preamble:
            build_parts.append(preamble)
        if "BUILD" in sections:
            build_parts.append(sections["BUILD"])
        
        if build_parts:
            canonical_scripts["build"] = '\n\n'.join(build_parts)
        
        # start.sh = PREAMBLE + START (only if architect provided reusable start command)
        start_parts = []
        if preamble:
            start_parts.append(preamble)
        if "START" in sections:
            start_parts.append(sections["START"])
        
        if start_parts:
            canonical_scripts["start"] = '\n\n'.join(start_parts)
        
        # test.sh = PREAMBLE + TEST (optional)
        test_parts = []
        if preamble:
            test_parts.append(preamble)
        if "TEST" in sections:
            test_parts.append(sections["TEST"])
        
        if test_parts:
            canonical_scripts["test"] = '\n\n'.join(test_parts)
        
        # validation.sh = PREAMBLE + VALIDATE (internal)
        validation_parts = []
        if preamble:
            validation_parts.append(preamble)
        if "VALIDATE" in sections:
            validation_parts.append(sections["VALIDATE"])
        
        if validation_parts:
            canonical_scripts["validation"] = '\n\n'.join(validation_parts)
        
        # Log section parsing results
        self.logger.info(f"Parsed {len(sections)} sections from consolidated script")
        self.logger.info(f"Generated {len(canonical_scripts)} canonical scripts: {list(canonical_scripts.keys())}")
        
        return canonical_scripts
    
    def _write_canonical_scripts_to_init(self, canonical_scripts: Dict[str, str], container_info: ContainerInfo) -> None:
        """
        Write canonical scripts to the workspace .init folder and make them executable.
        
        Args:
            canonical_scripts: Mapping of script names to their content
            container_info: Container information for context
        """
        workspace = container_info.workspace
        init_dir = os.path.join(workspace, ".init")
        os.makedirs(init_dir, exist_ok=True)
        
        # Define the mapping of script names to filenames
        mapping = {
            "install": "install.sh",
            "build": "build.sh", 
            "start": "start.sh",
            "scaffold": "scaffold.sh",
            "test": "test.sh",
            "validation": "validation.sh",
        }
        
        for script_name, content in canonical_scripts.items():
            if script_name in mapping and content.strip():
                filename = mapping[script_name]
                path = os.path.join(init_dir, filename)
                
                # Add shebang and basic setup to each script
                script_content = f"""#!/bin/bash
set -euo pipefail

# Canonical script: {script_name}
# Generated from consolidated setup script
# Container: {container_info.container_name}
# Framework: {container_info.framework}

{content}
"""
                
                # Write script to file
                with open(path, 'w') as f:
                    f.write(script_content)
                
                # Make executable
                import stat
                os.chmod(path, stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)
                
                self.logger.info(f"Wrote canonical script {script_name} to {path}")
                
                # Log script content for debugging
                self.logger.info("-"*15 + f"Canonical Script {script_name}" + "-"*15)
                self.logger.info(script_content)
                self.logger.info("-"*40 + "\n")
    
    def _update_project_schema_with_canonical_scripts(self, canonical_scripts: Dict[str, str], container_name: str) -> bool:
        """
        Update the project schema with canonical commands from parsed scripts.
        
        Args:
            canonical_scripts: Mapping of script names to their content
            container_name: Name of the container to update
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        # Validate that project schema exists
        if not hasattr(self, 'project_schema') or not self.project_schema:
            self.logger.warning("Project schema not available, skipping schema update")
            return False
        
        # Find the container in the project schema
        container = self.project_schema.get_container_by_name(container_name)
        if not container:
            self.logger.error(f"Container {container_name} not found in project schema")
            return False
        
        # Update the container with the canonical scripts
        if "install" in canonical_scripts:
            container.installCommand = canonical_scripts["install"]
        
        if "build" in canonical_scripts:
            container.buildCommand = canonical_scripts["build"]
        
        if "start" in canonical_scripts:
            container.startCommand = canonical_scripts["start"]
        
        if "scaffold" in canonical_scripts:
            container.scaffoldCommand = canonical_scripts["scaffold"]
        
        if "test" in canonical_scripts:
            container.testCommand = canonical_scripts["test"]
        
        self.logger.info(f"Successfully updated project schema with canonical scripts for container {container_name}")
        
        # Save to manifest if manifest path exists
        if self.manifest_path and os.path.exists(self.manifest_path):
            self.project_schema.save_to_manifest(self.manifest_path)
        
        return True
        
    
    