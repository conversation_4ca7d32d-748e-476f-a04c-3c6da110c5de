import logging
from typing import Dict, Optional, List
from code_generation_core_agent.agents.micro.dsa.ContainerStepStatus import ContainerStepStatus
from code_generation_core_agent.agents.micro.dsa.ContainerSetupPlan import ContainerSetupPlan
from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerInfo
from dataclasses import dataclass, field
from datetime import datetime
import base64
import os
import json
import re
from code_generation_core_agent.agents.setup_logger import setup_logger


@dataclass
class StepExecutionRecord:
    """Record of a step's execution status and metadata"""
    step_id: str
    status: ContainerStepStatus
    timestamp: datetime = field(default_factory=datetime.now)
    attempts: int = 0
    last_error: Optional[str] = None


class StepExecutionHistory:
    """
    Simple class to track step execution history by step ID.
    Used to preserve step statuses when plans are refined.
    """
    
    def __init__(self):
        self._history: Dict[str, StepExecutionRecord] = {}
    
    def record_step_status(self, step_id: str, status: ContainerStepStatus, 
                          attempts: int = 0, error: Optional[str] = None) -> None:
        """Record the status of a step execution"""
        self._history[step_id] = StepExecutionRecord(
            step_id=step_id,
            status=status,
            attempts=attempts,
            last_error=error
        )
    
    def get_step_status(self, step_id: str) -> Optional[ContainerStepStatus]:
        """Get the recorded status for a step"""
        record = self._history.get(step_id)
        return record.status if record else None
    
    def get_step_record(self, step_id: str) -> Optional[StepExecutionRecord]:
        """Get the full execution record for a step"""
        return self._history.get(step_id)
    
    def has_step_record(self, step_id: str) -> bool:
        """Check if a step has been recorded"""
        return step_id in self._history
    
    def get_completed_steps(self) -> Dict[str, ContainerStepStatus]:
        """Get all completed steps (COMPLETED or FAILED)"""
        return {
            step_id: record.status 
            for step_id, record in self._history.items()
            if record.status in [ContainerStepStatus.COMPLETED, ContainerStepStatus.FAILED]
        }
    
    def get_pending_steps(self) -> Dict[str, ContainerStepStatus]:
        """Get all pending steps (PENDING, IN_PROGRESS, etc.)"""
        return {
            step_id: record.status 
            for step_id, record in self._history.items()
            if record.status not in [ContainerStepStatus.COMPLETED, ContainerStepStatus.FAILED]
        }
    
    def clear_history(self) -> None:
        """Clear all execution history"""
        self._history.clear()
    
    def to_dict(self) -> Dict[str, Dict]:
        """Convert history to a serializable dictionary"""
        return {
            step_id: {
                'step_id': record.step_id,
                'status': record.status.value,
                'timestamp': record.timestamp.isoformat(),
                'attempts': record.attempts,
                'last_error': record.last_error
            }
            for step_id, record in self._history.items()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Dict]) -> 'StepExecutionHistory':
        """Create StepExecutionHistory from a dictionary"""
        history = cls()
        for step_id, record_data in data.items():
            history._history[step_id] = StepExecutionRecord(
                step_id=record_data['step_id'],
                status=ContainerStepStatus(record_data['status']),
                timestamp=datetime.fromisoformat(record_data['timestamp']),
                attempts=record_data.get('attempts', 0),
                last_error=record_data.get('last_error')
            )
        return history


class ContainerSetupExecution:
    """
    Handles execution-related functionality for container setup plans.
    This includes plan persistence, resume functionality, and execution tracking.
    """
    
    def __init__(self, base_path: str, executor):
        self.logger = setup_logger("ContainerSetupExecution", base_path)
        self.executor = executor
    
    def get_safe_directory_name(self, name: str) -> str:
        """
        Convert a name to a safe directory name by replacing problematic characters.
        
        Args:
            name: The original name
            
        Returns:
            str: Safe directory name
        """
        # Replace problematic characters with underscores
        safe_name = re.sub(r'[^a-zA-Z0-9._-]', '_', name)
        # Remove leading/trailing dots and underscores
        safe_name = safe_name.strip('._')
        # Ensure it's not empty
        if not safe_name:
            safe_name = "unknown"
        return safe_name
    
    def get_plan_storage_path(self, container_info: ContainerInfo) -> str:
        """
        Get the storage path for setup plans.
        
        Args:
            container_info: Information about the container
            
        Returns:
            str: Path to the plan storage directory
        """
        # Use the container's home directory (/home/<USER>'s home
        home_dir = "/home/<USER>"
        
        # Get the last three path components (workspace name plus two parent directories)
        workspace_path = container_info.workspace
        path_parts = workspace_path.rstrip('/').split('/')
        if len(path_parts) >= 3:
            # Take the last three components: parent2/parent1/workspace_name
            workspace_name = '/'.join(path_parts[-2:])
        else:
            # Fallback to just the workspace name if path is too short
            workspace_name = os.path.basename(container_info.workspace)
        
        container_name = container_info.container_name
        
        # Create safe directory names
        safe_workspace_name = self.get_safe_directory_name(workspace_name)
        safe_container_name = self.get_safe_directory_name(container_name)
        
        return f"{home_dir}/.init/{safe_workspace_name}/{safe_container_name}"
    
    def check_for_existing_plan(self, container_info: ContainerInfo) -> Optional[ContainerSetupPlan]:
        """
        Check for existing setup plan in the container's home directory.
        
        Args:
            container_info: Information about the container
            
        Returns:
            ContainerSetupPlan if found and valid, None otherwise
        """
        try:
            # Use $HOME/.init/[workspace_name]/[container_name]/setup_plan.json
            # This avoids repository pollution and cross-container contamination
            plan_storage_path = self.get_plan_storage_path(container_info)
            plan_file = f"{plan_storage_path}/setup_plan.json"
            
            # Check if file exists inside the container
            try:
                # Use the executor to check if the file exists inside the container
                check_cmd = f"test -f {plan_file} && echo 'exists' || echo 'not_exists'"
                output, returncode = self.executor.run_one_shot_command(check_cmd)
                if returncode != 0 or 'not_exists' in output:
                    self.logger.info(f"No existing setup plan found at {plan_file}")
                    return None
            except Exception as e:
                self.logger.warning(f"Error checking file existence in container: {e}")
                return None
            
            # Load the plan from file
            try:
                # Use the executor to read the file inside the container
                cat_cmd = f"cat {plan_file}"
                output, returncode = self.executor.run_one_shot_command(cat_cmd)
                if returncode != 0:
                    self.logger.error(f"Failed to read plan file {plan_file} from container: {output}")
                    return None
                
                # Clean and validate the JSON output
                json_content = output.strip()
                if not json_content:
                    self.logger.warning(f"Plan file {plan_file} is empty")
                    return None
                
                plan_data = json.loads(json_content)
                self.logger.info(f"Plan file {plan_file} loaded successfully")
            except Exception as e:
                self.logger.warning(f"Error reading plan file from container: {e}")
                return None
            
            # Reconstruct the plan from saved data
            container_setup_plan = ContainerSetupPlan.from_dict(plan_data)
            
            # Validate the plan
            if not self.validate_loaded_plan(container_setup_plan, container_info):
                self.logger.warning(f"Loaded plan validation failed for {container_info.container_name}")
                return None
            
            # Log plan status
            execution_summary = container_setup_plan.get_execution_state_summary()
            self.logger.info(f"Loaded existing plan for {container_info.container_name}:")
            self.logger.info(f"  - Total steps: {execution_summary['total_steps']}")
            self.logger.info(f"  - Completed: {execution_summary['completed_steps']}")
            self.logger.info(f"  - Failed: {execution_summary['failed_steps']}")
            self.logger.info(f"  - Pending: {execution_summary['pending_steps']}")
            self.logger.info(f"  - Progress: {execution_summary['progress_percentage']}%")
            
            return container_setup_plan
            
        except Exception as e:
            self.logger.error(f"Error loading existing plan for {container_info.container_name}: {e}")
            return None
    
    def validate_loaded_plan(self, plan: ContainerSetupPlan, container_info: ContainerInfo) -> bool:
        """
        Validate that a loaded plan is compatible with the current container.
        
        Args:
            plan: The loaded ContainerSetupPlan
            container_info: Current container information
            
        Returns:
            bool: True if plan is valid, False otherwise
        """
        try:
            # Check if container info matches
            if plan.container_name != container_info.container_name:
                self.logger.warning(f"Container name mismatch: plan={plan.container_name}, current={container_info.container_name}")
                return False
            
            # Check if workspace matches
            if plan.container_info.workspace != container_info.workspace:
                self.logger.warning(f"Workspace mismatch: plan={plan.container_info.workspace}, current={container_info.workspace}")
                return False
            
            # Check if plan has steps
            if not plan.steps:
                self.logger.warning("Loaded plan has no steps")
                return False
            
            # Check for dependency issues
            dependency_issues = plan.validate_step_dependencies()
            if dependency_issues:
                self.logger.warning(f"Loaded plan has dependency issues: {dependency_issues}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating loaded plan: {e}")
            return False
    
    def is_setup_complete(self, plan: ContainerSetupPlan) -> bool:
        """
        Check if the setup is already complete based on the plan status.
        
        Args:
            plan: The ContainerSetupPlan to check
            
        Returns:
            bool: True if setup is complete, False otherwise
        """
        try:
            execution_summary = plan.get_execution_state_summary()
            
            # Setup is complete if all steps are completed and no steps failed
            if (execution_summary['completed_steps'] == execution_summary['total_steps'] and 
                execution_summary['failed_steps'] == 0 and 
                execution_summary['pending_steps'] == 0):
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking setup completion: {e}")
            return False
    
    def save_plan_progress(self, container_setup_plan: ContainerSetupPlan):
        """
        Save the plan after each step execution for resume capability.
        
        Args:
            container_info: Information about the container
            container_setup_plan: The current setup plan
            step_name: Optional name of the step that was just executed
        """
        try:
            # Use $HOME/.init/[workspace_name]/[container_name]/setup_plan.json
            plan_storage_path = self.get_plan_storage_path(container_setup_plan.container_info)
            
            # Create directory inside the container
            mkdir_cmd = f"mkdir -p {plan_storage_path}"
            output, returncode = self.executor.run_one_shot_command(mkdir_cmd)
            if returncode != 0:
                self.logger.warning(f"Failed to create directory {plan_storage_path} in container: {output}")
            
            # Save the complete plan with current execution state
            plan_file = f"{plan_storage_path}/setup_plan.json"
            plan_data = container_setup_plan.to_dict()
            
            plan_json = json.dumps(plan_data, indent=2, ensure_ascii=False)
            # Use base64 encoding to safely transfer JSON data without quote issues
            encoded_json = base64.b64encode(plan_json.encode('utf-8')).decode('utf-8')
            write_cmd = f'echo "{encoded_json}" | base64 -d > {plan_file}'
            output, returncode = self.executor.run_one_shot_command(write_cmd)
            if returncode != 0:
                self.logger.error(f"Failed to write plan file {plan_file} to container: {output}")
            
        except Exception as e:
            self.logger.error(f"Error saving plan progress: {e}")
    
    def save_container_setup_plan(self, container_setup_plan: ContainerSetupPlan):
        """Save the container setup plan and write scripts to the workspace .init folder."""
        # save in the container repository
        workspace = container_setup_plan.container_info.workspace
        plan_storage_path = f"{workspace}/.init"
        os.makedirs(plan_storage_path, exist_ok=True)
        
        # Save static plan (without execution results) to setup_plan.json
        plan_file = f"{plan_storage_path}/setup_plan.json"
        static_plan_data = container_setup_plan.to_dict()
        
        with open(plan_file, 'w') as f:
            json.dump(static_plan_data, f, indent=2)
        
        self.logger.info(f"Static plan saved to {plan_file}")
        
        # Write scripts from plan steps to the .init folder
        self._write_scripts_to_init(container_setup_plan)
        
        self.save_plan_progress(container_setup_plan)
    
    def save_setup_shell_script(self, container_setup_plan: ContainerSetupPlan, consolidated_script: str):
        """Save the consolidated shell script to a file in the container's home directory."""
        # Validate that setup was successful before generating script
        if len(container_setup_plan.get_completed_steps()) == 0:
            self.logger.info("Skipping shell script generation - setup was not successful")
            return
            
        self.logger.info("Generating consolidated shell script for successful setup")
        
        workspace = container_setup_plan.container_info.workspace
        plan_storage_path = f"{workspace}/.init"
        script_file = f"{plan_storage_path}/setup_script.sh"
        
        os.makedirs(plan_storage_path, exist_ok=True)
        with open(script_file, 'w') as f:
            f.write(consolidated_script)
        
        # Make the script executable
        import stat
        os.chmod(script_file, stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)  # 755 permissions
        
        self.logger.info(f"Consolidated shell script saved to {script_file}")
        self.logger.info(f"Script is executable and ready for autonomous execution")
            
        # Log script summary
        script_lines = consolidated_script.split('\n')
        total_lines = len(script_lines)
        comment_lines = len([line for line in script_lines if line.strip().startswith('#')])
        command_lines = len([line for line in script_lines if line.strip() and not line.strip().startswith('#') and not line.strip().startswith('set') and not line.strip().startswith('log')])
        
        self.logger.info(f"Script summary: {total_lines} total lines, {comment_lines} comments, {command_lines} commands")
        self.logger.info("Consolidated shell script generated and saved successfully")
    
    def _write_scripts_to_init(self, plan) -> None:
        """Write scripts from plan steps to the workspace .init folder and make them executable."""
        workspace = plan.container_info.workspace
        init_dir = os.path.join(workspace, ".init")
        os.makedirs(init_dir, exist_ok=True)
        
        # Get all output scripts from the plan steps
        output_scripts = plan.get_all_output_scripts()
        
        mapping = {
            "install": "install.sh",
            "build": "build.sh",
            "start": "start.sh",
            "scaffold": "scaffold.sh",
            "test": "test.sh",
            "validation": "validation.sh",
        }
        
        for key, filename in mapping.items():
            if key in output_scripts and output_scripts[key].code:
                # beauty print of the script content
                self.logger.info("-"*15 + f"Script {key}" + "-"*15)
                self.logger.info(f"{output_scripts[key].code}")
                self.logger.info("-"*40 + "\n")
                path = os.path.join(init_dir, filename)
                with open(path, 'w') as f:
                    f.write(output_scripts[key].code.strip() + "\n")                    
                import stat
                os.chmod(path, stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)
                self.logger.info(f"Wrote script {key} to {path}")
