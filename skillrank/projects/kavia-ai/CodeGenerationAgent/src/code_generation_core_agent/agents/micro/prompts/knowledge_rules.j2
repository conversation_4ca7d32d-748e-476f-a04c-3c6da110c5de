{% block files_yaml %}
#Rules for using KnowledgeTools:
- Use the KnowledgeTools functions to get information about the project codebase, and to determine what files in the project are relevant
to your task.  
- If you want to get more information about one or more files use KnowledgeTools_get_source_files_knowledge to access more information and if necessary use ContainerFileTools to directly read relevant files in order to ensure your decisions are based on accurate information.
- If the information you gather suggests there may be additional sources of information then do additional KnowledgeTools_find_relevant_files
searches and reads so your decisions are based on comprehensive information.  
- If KnowledgeTools_find_relevant_files results include an entry stating that the results are abridged, consider using separate searches with fewer search terms in each, or enabling and_search.
- *Important* If files are listed in the  ‘important_files’ section of the prompt do not use ContainerFileTools or KnowledgeTools to access information about those files (unless the you made previous edits on those files). 
{% endblock %}