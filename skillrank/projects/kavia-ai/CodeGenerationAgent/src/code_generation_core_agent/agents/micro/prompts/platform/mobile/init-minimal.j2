Given the task of configuring a development environment for {{ component_name }} with details (placed in back ticks) of:  
```
({{ work_item_details }})
```

{% if application_type == 'android' %}
In order to create a minimal Android app, use the start_long_running_process to execute the following command:
"bash {{ work_path }}/docker-android/init-android-project.sh <ApplicationName>"
Before running the command, ensure you are in the working directory {{ base_path }}.

{% elif application_type == 'ios' %}
Use the available tools to create the initial iOS app. Use the create_ios_app from the IOSTools. No files should be created or modified outside of the base folder {{ base_path }}. Your only goal is to call the create_ios_app with the correct input according to the user request, the template_name shall be either iOSAppSwift or iOSAppObjectiveC corresponding to the chosen programming language requested by the user. If not specified consider the iOSAppSwift as default.

{% elif application_type == 'flutter' %}
Use the start_long_running_process in order to execute the flutter create command that allows us to create a minimal template app.
The app MUST be created in a subdirectory of the base folder named {{ base_path }}/<ApplicationName>.
{% endif %}

Make sure that after defining dependencies necessary to the project you call get_dependency_version from the DependencyTools to check if any version that differs from required version returned by the tool.
Consider the versions returned from the get_dependency_version as the source of truth and store them to be used as reference further when creating the project's dependency file.
If one or more of the versions on the file differ from the source of truth you MUST overwrite the dependency version on the file with the correct version from the tool's output. For dependencies that are not returned by the tool which means they don't have required versions, you may use any stable version.
You should check the versions before writing them to the dependencies file. AND USE THE RIGHT VERSIONS when creating the file with necessary dependencies.

Once the minimal app is created, create the file "{{ base_path }}/.init/.init-minimal".

Create the minimal app but don't try to run it.
If you start a process with start_long_running_process be sure to clean up by calling terminate_process.

Please format your final response as a JSON object with the following structure:
{
    "actions": "<summary of actions taken>"
}