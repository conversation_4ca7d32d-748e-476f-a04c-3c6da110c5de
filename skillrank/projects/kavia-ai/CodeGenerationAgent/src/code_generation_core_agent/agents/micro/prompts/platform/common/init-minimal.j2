Given the task of configuring a development environment for {{ work_item.component_name }} with
a details (placed in back ticks) of:
```
({{ work_item_details }})
```
Considering the development system uses os Linux and architecture {{ platform }}
perform any actions required to create or minimal application (a valid application but is minimal like a hello-world app)
with all the necessary and appropriate folder structure, config files, source files, etc. Use the available tools to accomplish this goal. If
there is a standard automated tool for the minimal app creation please use it. No files should be created
or modified outside of the base folder {{ base_path }}. The app MUST be created in a subdirectory of the base
folder named {{ base_path }}/<ApplicationName>.

Make sure that after defining dependencies necessary to the project you call get_dependency_version from the DependencyTools to check if any version that differs from required version returned by the tool.
Consider the versions returned from the get_dependency_version as the source of truth and store them to be used as referecence further when creating the project's dependency file.
If one or more of the versions on the file differ from the source of truth you MUST overwrite the dependency version on the file with the correct version from the tool's output. For dependencies that are not returned by the tool which means they don't have required versions, you may use any stable version.
You should check the versions before writing them to the dependencies file. AND USE THE RIGHT VERSIONS when creating the file with necessary dependencies.

Once the minimal app is created, create the file
"{{ base_path }}/.init/.init-minimal". Create the minimal app but don't try to run it.

You MUST initialize any npm/React/Javascript app as follows:
```
# Get the template:
git clone https://github.com/Kavia-ai/cra-template-kavia {{ base_path }}/.assets/cra-template-kavia
# Create the app:
npx create-react-app@latest <ApplicationName> --template file:{{ base_path }}/.assets/cra-template-kavia
```

If you start a process with start_long_running_process be sure to clean up by calling terminate_process.

Considerations:
 - If you are using an automated tool to create the minimal app scaffolding: consider the project details when choosing an app
template.
 - If the project specifies javascript you MUST always prefer initializing with javascript over typescript.
 - Install the linter and formatter for the language of the project. For example, for a javascript project, install eslint with npm to the project.
 - Create a shell script `{{ base_path }}/.init/.linter.sh` in the , that is an executable file, reuses arguments and that can be used run the linter on file or directories
   and performs a packaging test
   Example:
   ```
   #!/bin/bash
   cd <application_dir>
   
   # 1.) Run the linter on the files or directories passed as arguments
   npx eslint --fix "$@"
   ESLINT_EXIT_CODE=$?
   
   # 2.) Test the packaging of the application
   npm run build
   BUILD_EXIT_CODE=$?
   
   # Exit with error if either command failed
   if [ $ESLINT_EXIT_CODE -ne 0 ] || [ $BUILD_EXIT_CODE -ne 0 ]; then
      exit 1
   fi
   ```

Please format your final response as a JSON object with the following structure:
{
   "actions": "<summary of actions taken>"
}