import os
import re
from typing import Callable, List

class FileResolver:
    def __init__(self, base_paths:List[str]):
        self.base_paths = base_paths

    def _check_file_exists(self, filename):
        exists = False
        if os.path.exists(filename):
            exists = True
        return exists

    def _list_directory(self, directory):
        list = []
        try:
            list = os.listdir(directory)
        except FileNotFoundError:
            pass
        except NotADirectoryError:
            pass
        return list

    def _replace_tags(self, text: str, handler: Callable[[str, str], str], resolved_files:List[str]) -> str:
        """
        Replace tags of the form @<type>-<item> in the text by invoking the handler.

        Args:
            text (str): The input text containing tags.
            handler (Callable[[str, str], str]): Function that takes (type, item)
                                                and returns the replacement string.

        Returns:
            str: The text with all tags replaced.
        """
        # Regex matches @ followed by type, a dash, then item
        tag_pattern = re.compile(r'@([A-Za-z0-9_]+)-([^\s@,:;!?\)\}\]]+)')

        def _replacer(match):
            tag_type, tag_item = match.group(1), match.group(2)
            replacement = handler(tag_type, tag_item, resolved_files)
            # If handler returns an empty string, leave tag unchanged
            if replacement == "":
                return match.group(0)
            return replacement

        return tag_pattern.sub(_replacer, text)

    def _tag_handler(self, tag_type:str, tag_item:str, resolved_files:List[str] ) -> str:
        tag_value = ""
        if tag_type.lower() == 'file':
            tag_value = self._resolve_file(tag_item, resolved_files)
        return tag_value   

    def _resolve_file(self, path_fragment:str, resolved_files:List[str]) -> str:
        resolved_path = ""
        append_suffix = ""
        if path_fragment.endswith('.'):
            path_fragment = path_fragment[:-1]
            append_suffix = '.'
        def _find_file(folder_path, path_fragment):
            check_path = os.path.join(folder_path, path_fragment)
            if self._check_file_exists(check_path):
                return check_path
            items = self._list_directory(folder_path)
            for item in items:
                item_path = os.path.join(folder_path, item)
                nest = self._list_directory(item_path + "/")
                if len(nest) > 0:
                    skip_folder = False
                    if item.startswith('.'):
                        skip_folder = True
                    if not skip_folder:
                        found_path = _find_file(item_path, path_fragment)
                        if found_path:
                            return found_path
            return None
        def _convert_to_relative(base_path, file_path):
            from pathlib import Path
            def strip_path_head(p: Path, n=1):
                parts = p.parts[1 + n:] if p.is_absolute() else p.parts[n:]
                return Path(*parts)
            filepath = Path(file_path)
            basepath = Path(base_path)
            try:
                filename = str(filepath.relative_to(basepath))
            except ValueError:
                while len(filepath.parts) > 1:
                    filepath = strip_path_head(filepath)
                    checkpath = os.path.join(base_path,str(filepath))
                    if self._check_file_exists(checkpath):
                        return checkpath
                filename= file_path
            return filename
        for base_path in self.base_paths:
            resolved_path = _find_file(base_path, path_fragment)
            if resolved_path:
                resolved_path = _convert_to_relative(base_path, resolved_path)
                break
        if resolved_path:
            if not resolved_files == None:
                resolved_files.append(resolved_path)
            if append_suffix:
                resolved_path += append_suffix
        return resolved_path

    def resolveFileTags(self, user_input:str, resolved_files:List[str] = None) -> str:
        preprocessed_user_input =  self._replace_tags(user_input, self._tag_handler, resolved_files)
        return preprocessed_user_input
