import os
import sys
import fnmatch
import json


def is_binary_file(file_path):
    """
    Determine whether a file is binary based on its extension and content.

    Uses a hardcoded set of known binary and text file extensions.
    If the file extension is recognized, returns the corresponding value.
    Otherwise, reads the first 1024 bytes from the file and returns True
    if a null byte is found (indicating binary data), else False.
    """
    # Sets of known file extensions.
    BINARY_EXTENSIONS = {
        # Executable and library files
        ".exe", ".dll", ".so", ".dylib", ".bin", ".dat", ".class", ".jar",

        # Image formats
        ".png", ".jpg", ".jpeg", ".gif", ".ico", ".bmp", ".tiff", ".tif",

        # Document formats
        ".pdf",

        # Archive and compressed formats
        ".zip", ".tar", ".gz", ".7z", ".rar", ".iso",

        # Multimedia formats
        ".mp3", ".mp4", ".avi", ".mov", ".mkv", ".flv", ".webm",

        # Font and other binary resources
        ".woff", ".woff2", ".otf", ".ttf", ".eot"
    }
    TEXT_EXTENSIONS = {

        # Documentation and plain text
        ".txt", ".md", ".markdown", ".rst", ".tex",

        # Programming languages and source code
        ".py",  # Python
        ".c", ".h",  # C
        ".cpp", ".hpp", ".cxx", ".hxx",  # C++
        ".java",  # Java
        ".js", ".jsx",  # JavaScript
        ".ts", ".tsx",  # TypeScript
        ".rb",  # Ruby
        ".php", ".phtml",  # PHP
        ".sh", ".bash", ".zsh", ".ksh",  # Shell scripts
        ".pl", ".pm",  # Perl
        ".go",  # Go
        ".cs",  # C#
        ".swift",  # Swift
        ".kt", ".kts",  # Kotlin
        ".scala", ".sc",  # Scala
        ".r",  # R
        ".m", ".mm",  # Objective-C / Objective-C++
        ".hs", ".lhs",  # Haskell
        ".lisp", ".cl", ".el",  # Lisp variants
        ".erl", ".hrl",  # Erlang
        ".ex", ".exs",  # Elixir
        ".rs",  # Rust
        ".dart",  # Dart
        ".fs", ".fsi", ".fsx",  # F#
        ".groovy", ".gvy", ".gy", ".gsh",  # Groovy
        ".vb",  # Visual Basic

        # Markup, configuration, and data formats
        ".html", ".htm", ".xhtml",  # HTML
        ".css", ".scss", ".sass", ".less",  # CSS and preprocessors
        ".json",  # JSON
        ".yaml", ".yml",  # YAML
        ".xml",  # XML
        ".ini", ".cfg", ".conf",  # INI/Config files
        ".properties",  # Java properties
        ".toml",  # TOML
        ".env",  # Environment variables

        # Scripting and command files
        ".bat", ".cmd",  # Batch scripts (Windows)
        ".ps1",  # PowerShell scripts

        # Others
        ".asm", ".s",  # Assembly language
        ".sql"  # SQL scripts
    }

    _, ext = os.path.splitext(file_path)
    ext = ext.lower()

    if ext in BINARY_EXTENSIONS:
        return True
    if ext in TEXT_EXTENSIONS:
        return False

    # Fallback: read the first 1024 bytes and check for null bytes.
    try:
        with open(file_path, "rb") as f:
            chunk = f.read(1024)
            return b'\0' in chunk
    except Exception:
        # If there is an error reading the file, assume it is binary.
        return True


class FileLister:
    def __init__(self, threshold=100, emit_important_files=True,
                 exclude_dirs=None, exclude_files=None, exclude_extensions=None,
                 include_files=None, print_contents_threshold=1024,
                 common_directories=None):
        # Global threshold used for expansion quota (only in "list-only" mode).
        self.important_files_threshold = threshold
        self.emit_important_files = emit_important_files
        # Dictionary mapping folder paths to their cumulative file count.
        self.folder_counts = {}
        # Dictionary mapping folder paths to the cumulative byte count of text files.
        self.folder_bytes = {}
        # Threshold (in bytes) for printing full file content.
        self.print_contents_threshold = print_contents_threshold
        # Exclusion lists.
        self.exclude_dirs = exclude_dirs if exclude_dirs is not None else []
        self.exclude_files = exclude_files if exclude_files is not None else ["files.yaml"]
        self.exclude_extensions = exclude_extensions if exclude_extensions is not None else [".log"]
        # Files that must be included even if not printed in the normal expansion.
        self.include_files = include_files if include_files is not None else []
        # Common directories to always show from base folder
        self.common_directories = common_directories if common_directories is not None else ["attachments", "chat_uploads", "assets"]
        # Set to record relative paths of printed files.
        self.printed_files = set()
        # Activity report details
        self.activity_data = None
        self.activity_report_path = '.file_activity_report.json'

    def _is_excluded(self, item, is_dir, parent_folder=None):
        """
        Determine if a file or directory should be excluded.
        First, check built-in rules. Then, if parent_folder is provided,
        try to read a .gitignore file and use its patterns (using fnmatch).
        """
        if is_dir:
            if item.startswith('.') or item in self.exclude_dirs:
                return True
        else:
            if item.startswith('.') or item in self.exclude_files:
                return True
            for ext in self.exclude_extensions:
                if item.endswith(ext):
                    return True

        # Now check .gitignore rules in the parent folder, if provided.
        if parent_folder:
            gitignore_path = os.path.join(parent_folder, ".gitignore")
            if os.path.isfile(gitignore_path):
                try:
                    with open(gitignore_path, "r") as f:
                        lines = f.readlines()
                    # Filter out comments and empty lines.
                    patterns = [line.strip() for line in lines if line.strip() and not line.strip().startswith("#")]
                    # For a simple approach, apply each pattern to the item name.
                    for pattern in patterns:
                        # (For a more complete solution you might want to use a library like pathspec.)
                        if fnmatch.fnmatch(item, pattern):
                            return True
                except Exception:
                    pass
        return False

    def _list_directory(self, folder):
        """Safely list the items in a folder; return an empty list on error."""
        try:
            folder = os.path.normpath(folder)
            if os.path.isdir(folder):
                return os.listdir(folder)
            return []
        except Exception:
            return []

    def count_files(self, folder, rel_path: str = ""):
        """
        Recursively count important files (skipping excluded items) and accumulate
        the total byte size of text (non‑binary) files.

        While walking, this pass also:
        • builds self.include_files_resolved   (every real file that matched
            any pattern in self.include_files);
        • builds self._dirs_with_includes      (all directories on the paths
            leading to those files).

        Returns a tuple  (file_count, total_bytes).
        """
        # ── first call: clear/initialise the helper sets ────────────────────────
        if rel_path == "":
            self.include_files_resolved = set()
            self._dirs_with_includes   = set()

        total       = 0
        bytes_total = 0

        for item in self._list_directory(folder):
            item_path = os.path.join(folder, item)
            is_dir    = os.path.isdir(item_path)

            # skip excluded names early
            if self._is_excluded(item, is_dir=is_dir, parent_folder=folder):
                continue

            if is_dir:
                # ── recurse ────────────────────────────────────────────────────
                sub_count, sub_bytes = self.count_files(
                    item_path,
                    os.path.join(rel_path, item) if rel_path else item
                )
                total       += sub_count
                bytes_total += sub_bytes
            else:
                total += 1
                if not is_binary_file(item_path):
                    try:
                        bytes_total += os.path.getsize(item_path)
                    except Exception:
                        pass

                # ── include‑file pattern matching ────────────────────────────────────────
                rel_file = os.path.join(rel_path, item) if rel_path else item

                for pattern in self.include_files:
                    if os.sep in pattern:
                        # pattern contains a path fragment → match against full relative path
                        matched = fnmatch.fnmatch(rel_file, pattern)
                    else:
                        # bare filename → match against just the basename
                        matched = fnmatch.fnmatch(item, pattern)

                    if matched:
                        self.include_files_resolved.add(rel_file)

                        # mark every ancestor directory so we can force‑expand later
                        parts = rel_file.split(os.sep)
                        for i in range(1, len(parts)):
                            self._dirs_with_includes.add(os.sep.join(parts[:i]))
                        break   # no need to test further patterns

        # cache totals for fast lookup elsewhere
        self.folder_counts[folder] = total
        self.folder_bytes [folder] = bytes_total
        return total, bytes_total


    def _get_file_lines(self, folder, f, prefix, print_file_contents, current_rel=""):
        file_path = os.path.join(folder, f)
        if print_file_contents and not is_binary_file(file_path):
            try:
                with open(file_path, "r") as file:
                    content = file.read()
                # Use the full relative path when printing file contents.
                relative_path = os.path.join(current_rel, f) if current_rel else f
                lines = [f"{relative_path}:"]
                lines.extend(content.splitlines())
                return lines
            except Exception:
                return [prefix + f]
        else:
            # When listing only file names, ignore the full relative path and use indentation.
            return [prefix + f]

    def expand_recursively(self, folder, prefix, current_rel, remaining_quota, print_file_contents):
        """
        Same public contract as before, but a directory is expanded even when the
        quota is 0 **if** that directory (or any of its descendants) contains an
        include‑file we resolved in count_files().
        """
        lines = []

        # ── early‑exit only when BOTH conditions hold ──────────────────────────
        if (remaining_quota is not None
                and remaining_quota <= 0
                and current_rel not in self._dirs_with_includes):
            return lines, remaining_quota

        items  = sorted(self._list_directory(folder))
        files  = []
        subdirs = []

        for item in items:
            item_path = os.path.join(folder, item)
            if os.path.isdir(item_path):
                if not self._is_excluded(item, is_dir=True, parent_folder=folder):
                    subdirs.append(item)
            else:
                if not self._is_excluded(item, is_dir=False, parent_folder=folder):
                    files.append(item)

                # ── files first (names or full content) ────────────────────────────────
                for f in files:
                    rel_path = os.path.join(current_rel, f) if current_rel else f

                    # skip if this file was already printed from a different path level
                    if rel_path in self.printed_files:
                        continue

                    is_priority = rel_path in self.include_files_resolved

                    # skip non‑priority files once quota is exhausted
                    if remaining_quota is not None and remaining_quota <= 0 and not is_priority:
                        continue

                    # print the line(s)
                    file_lines = self._get_file_lines(
                        folder, f, prefix, print_file_contents, current_rel
                    )
                    lines.extend(file_lines)

                    # normal files consume quota; priority files do not
                    if remaining_quota is not None and not is_priority:
                        remaining_quota -= 1

                    # mark as printed so we never add it again
                    self.printed_files.add(rel_path)

        # ── order subdirs by file‑count (smallest first) ───────────────────────
        subdir_info = []
        for sd in subdirs:
            subdir_info.append(
                (sd, self.folder_counts.get(os.path.join(folder, sd), 0))
            )
        subdir_info.sort(key=lambda x: x[1])

        for sd, count in subdir_info:
            rel_sd       = os.path.join(current_rel, sd) if current_rel else sd
            force_expand = rel_sd in self._dirs_with_includes
            if remaining_quota is None or remaining_quota > 0 or force_expand:
                # add the one‑line summary ONLY if we still have quota
                if remaining_quota is None or remaining_quota > 0:
                    lines.append(prefix + f"{sd}/: {count} items")
                    if remaining_quota is not None and not force_expand:
                        remaining_quota -= 1
                sub_lines, remaining_quota = self.expand_recursively(
                    os.path.join(folder, sd),
                    prefix + "    ",
                    rel_sd,
                    remaining_quota,
                    print_file_contents,
                )
                lines.extend(sub_lines)
            else:
                # no quota && nothing to force => just show summary
                lines.append(prefix + f"{sd}/: {count} items")

        return lines, remaining_quota

    def collect_include_files(self, folder, current_rel=""):
        """
        Recursively traverse 'folder' and collect the full relative paths of all files
        that are not excluded (using the same exclusion rules as elsewhere) and are non-binary.
        This ensures that all remaining files are considered important.
        """
        collected = []
        items = self._list_directory(folder)
        for item in items:
            item_path = os.path.join(folder, item)
            new_rel = os.path.join(current_rel, item) if current_rel else item
            # If it's a directory, process it recursively.
            if os.path.isdir(item_path):
                if not self._is_excluded(item, is_dir=True, parent_folder=folder):
                    collected.extend(self.collect_include_files(item_path, new_rel))
            else:
                # For files, apply the same exclusion logic and skip binary files.
                if (not self._is_excluded(item, is_dir=False, parent_folder=folder)
                    and not is_binary_file(item_path)):
                    collected.append(new_rel)
        return collected

    def _load_activity_report(self, base_folder):
        """
        Load the activity report JSON file if it exists in the base folder.
        """
        report_path = os.path.join(base_folder, self.activity_report_path)
        if os.path.exists(report_path):
            try:
                with open(report_path, 'r') as f:
                    self.activity_data = json.load(f)
                return True
            except Exception as e:
                print(f"Error loading activity report: {e}")
        return False

    def _get_active_files_section(self, base_folder):
        """
        Generate the most active files section based on the activity report.
        Returns a list of formatted lines with highlighting for priority files.
        Only processes the overall most active files, ignoring recent activity.
        """
        if not self.activity_data:
            return []

        lines = ["\n⭐ MOST ACTIVE FILES - PRIORITY ⭐"]

        # Track printed active files to avoid duplicates when printing normal directory structure
        active_files = set()

        # Process overall most active files
        if "top_n_overall" in self.activity_data and self.activity_data["top_n_overall"]:
            lines.append("  🔍 Overall Most Accessed:")
            for item in self.activity_data["top_n_overall"]:
                if "file" in item and "count" in item:
                    # Make path relative to base folder if it's a subpath
                    file_path = item["file"]
                    if file_path.startswith(base_folder):
                        rel_path = os.path.relpath(file_path, base_folder)
                    else:
                        rel_path = file_path

                    lines.append(f"    ➤ {rel_path} (accessed {item['count']} times)")
                    active_files.add(rel_path)
                    self.printed_files.add(rel_path)

        # If any active files were found, print their content if below threshold
        active_files_list = list(active_files)
        if active_files_list and self.folder_bytes.get(base_folder, 0) < self.print_contents_threshold:
            lines.append("\n  📄 Content of Active Files:")
            for rel_path in active_files_list:
                file_path = os.path.join(base_folder, rel_path)
                if os.path.exists(file_path) and not is_binary_file(file_path):
                    try:
                        with open(file_path, "r") as file:
                            content = file.read()
                        lines.append(f"    {rel_path}:")
                        for line_content in content.splitlines():
                            lines.append(f"      {line_content}")
                    except Exception:
                        lines.append(f"    {rel_path}: <Error reading file>")

        return lines

    
    def _resolve_include_files(self, base_folder):
        """
        Return a list of *relative* paths that
        • exist,
        • are not excluded,
        • are text files.
        """
        resolved = []
        for rel_path in self.include_files:
            full_path = os.path.join(base_folder, rel_path)
            if (os.path.isfile(full_path) and
                not self._is_excluded(os.path.basename(rel_path), is_dir=False, parent_folder=os.path.dirname(full_path)) and
                not is_binary_file(full_path)):
                resolved.append(os.path.normpath(rel_path))
        return resolved
    
    def _add_common_directories(self, base_folder, filesystem_lines, remaining_quota):
        """
        Add common directories from the base folder to the filesystem listing.
        Returns the updated remaining_quota.
        """
        if not self.common_directories:
            return remaining_quota
            
        # Add a separator for common directories
        filesystem_lines.append("\n--- Common Directories ---")
        
        for common_dir in self.common_directories:
            common_path = os.path.join(base_folder, common_dir)
            if os.path.isdir(common_path) and not self._is_excluded(common_dir, is_dir=True, parent_folder=base_folder):
                count = self.folder_counts.get(common_path, 0)
                
                # Always show the directory summary
                filesystem_lines.append(f"{common_dir}/: {count} items")
                
                # If we have quota or this directory has include files, expand it
                force_expand = common_dir in self._dirs_with_includes
                if (remaining_quota is not None and remaining_quota > 0) or force_expand:
                    if remaining_quota is not None and not force_expand:
                        remaining_quota -= 1
                        
                    sub_lines, remaining_quota = self.expand_recursively(
                        common_path,
                        "    ",
                        common_dir,
                        remaining_quota,
                        False
                    )
                    filesystem_lines.extend(sub_lines)
        
        return remaining_quota
    
    # ──────────────────────────────────────────────────────────────
    # NEW:  strict two-level “interfaces/” collector
    # ──────────────────────────────────────────────────────────────
    def _add_interfaces(self, base_folder: str):
        """
        Locate every directory literally named “interfaces” that lies
        1 or 2 levels beneath *base_folder*:

            <workspace>/interfaces/
            <workspace>/<container_root>/interfaces/

        For every **non-binary** file inside those dirs **that is not
        excluded by the usual rules**, add it to both inclusion sets.

        Anything deeper than two levels, or living in an excluded path,
        is ignored.
        """
        # level-1 scan  (direct children of base_folder)
        for lvl1 in self._list_directory(base_folder):
            lvl1_path = os.path.join(base_folder, lvl1)
            if not os.path.isdir(lvl1_path) or self._is_excluded(lvl1, True, base_folder):
                continue

            # <workspace>/interfaces
            self._maybe_add_interfaces_dir(lvl1_path, base_folder)

            # level-2 scan  (<workspace>/*)
            for lvl2 in self._list_directory(lvl1_path):
                lvl2_path = os.path.join(lvl1_path, lvl2)
                if (not os.path.isdir(lvl2_path) or
                        self._is_excluded(lvl2, True, lvl1_path)):
                    continue

                # <workspace>/<container_root>/interfaces
                self._maybe_add_interfaces_dir(lvl2_path, base_folder)

    def _maybe_add_interfaces_dir(self, parent_dir: str, base_folder: str):
        """
        If *parent_dir/interfaces* exists **and itself is not excluded**,
        walk its contents (recursively) and push every non-binary file
        through the normal inclusion pipeline.

        The usual `exclude_files`, `exclude_extensions`, and `.gitignore`
        rules are honoured on each file name.
        """
        interfaces_dir = os.path.join(parent_dir, "interfaces")
        if (not os.path.isdir(interfaces_dir) or
                self._is_excluded("interfaces", True, parent_dir)):
            return

        for root, _, files in os.walk(interfaces_dir):
            rel_root = os.path.relpath(root, base_folder)
            for fname in files:
                if self._is_excluded(fname, False, root):
                    continue
                full = os.path.join(root, fname)
                if is_binary_file(full):
                    continue
                rel = os.path.join(rel_root, fname)

                # avoid duplicates
                if rel in self.include_files_resolved:
                    continue

                self.include_files.append(rel)
                self.include_files_resolved.add(rel)

                # mark ancestors so the tree can be force-expanded
                parts = rel.split(os.sep)
                for i in range(1, len(parts)):
                    self._dirs_with_includes.add(os.sep.join(parts[:i]))
                    
    
    def list_as_json(self, base_folder, container_workspace=None, sort_key=None):
        """
        Return a JSON-serialisable dict with keys:
            • base_directory
            • filesystem      – directory outline
            • important_files – [{relative_path, content}, …]

        When container_workspace is provided, the tree is scoped to that
        sub-directory, plus common and interfaces directories.
        """
        import os

        # ---------------------------------------------------------------- 0
        effective_base = (
            os.path.join(base_folder, container_workspace)
            if container_workspace else base_folder
        )
        effective_base = os.path.abspath(effective_base)
        if os.path.commonpath([effective_base, os.path.abspath(base_folder)]) != os.path.abspath(base_folder):
            raise ValueError("container_workspace must sit inside base_folder")

        # ---------------------------------------------------------------- 1  <USER> <GROUP>
        if self._load_activity_report(base_folder):
            for item in self.activity_data.get("top_n_overall", []):
                p = item.get("file")
                if not p:
                    continue
                if p.startswith(base_folder):
                    p = os.path.relpath(p, base_folder)
                if not container_workspace or p.startswith(container_workspace):
                    self.include_files.append(p)

        # ----------------------------------------------------------------
        # 2. Count file size for both effective base and common directories
        # ----------------------------------------------------------------
        _, total_bytes = self.count_files(effective_base)
        
        # Also count files in common directories to ensure they're included in counts
        for common_dir in self.common_directories:
            common_path = os.path.join(base_folder, common_dir)
            if os.path.isdir(common_path):
                self.count_files(common_path, common_dir)
        
        display_all_text = total_bytes < self.print_contents_threshold

        # ---------------------------------------------------------------- 2½  always pull interfaces/*
        self._add_interfaces(base_folder)

        # ----------------------------------------------------------------
        # 3. Build directory outline (rooted at base_folder)
        # ----------------------------------------------------------------
        filesystem_lines = [
            "Current folder structure:",
            f"base folder: {base_folder}",
        ]
        if container_workspace:
            filesystem_lines.append(f"container workspace: {container_workspace}")

        remaining_quota = self.important_files_threshold

        if container_workspace:
            rel_ws   = container_workspace
            ws_path  = os.path.join(base_folder, rel_ws)
            count_ws = self.folder_counts.get(ws_path, 0)

            filesystem_lines.append(f"{rel_ws}/: {count_ws} items")
            sub_lines, remaining_quota = self.expand_recursively(
                ws_path, "    ", rel_ws, remaining_quota, False
            )
            filesystem_lines.extend(sub_lines)

            remaining_quota = self._add_common_directories(
                base_folder, filesystem_lines, remaining_quota
            )
        else:
            # original “list everything” branch (unchanged)
            items = sorted(self._list_directory(base_folder))
            files_top, subdirs = [], []
            for item in items:
                p = os.path.join(base_folder, item)
                if os.path.isdir(p):
                    if not self._is_excluded(item, True, base_folder):
                        subdirs.append((item, self.folder_counts.get(p, 0)))
                elif not self._is_excluded(item, False, base_folder):
                    files_top.append(item)

            for f in files_top:
                filesystem_lines.extend(
                    self._get_file_lines(base_folder, f, "", False, "")
                )
                self.printed_files.add(f)

            subdirs.sort(key=lambda x: x[1])
            for sd, cnt in subdirs:
                p  = os.path.join(base_folder, sd)
                rp = os.path.relpath(p, base_folder)
                fx = rp in self._dirs_with_includes
                if remaining_quota > 0 or fx:
                    if remaining_quota > 0:
                        filesystem_lines.append(f"{rp}/: {cnt} items")
                        if not fx:
                            remaining_quota -= 1
                    sub, remaining_quota = self.expand_recursively(
                        p, "    ", rp, remaining_quota, False
                    )
                    filesystem_lines.extend(sub)
                else:
                    filesystem_lines.append(f"{rp}/: {cnt} items")

        # ---------------------------------------------------------------- 4  <USER>
        <GROUP> = []

        if display_all_text:
            # small repo – dump everything from effective_base + commons
            for rel in sorted(self.collect_include_files(effective_base, "")):
                full = os.path.join(effective_base, rel)
                if is_binary_file(full):
                    continue
                try:
                    content = open(full, "r").read()
                except Exception:
                    content = ""
                important_files_list.append({"relative_path": os.path.relpath(full, base_folder), "content": content})

            for cd in self.common_directories:
                cp = os.path.join(base_folder, cd)
                if os.path.isdir(cp):
                    for rel in sorted(self.collect_include_files(cp, cd)):
                        full = os.path.join(base_folder, rel)
                        if is_binary_file(full):
                            continue
                        try:
                            content = open(full, "r").read()
                        except Exception:
                            content = ""
                        important_files_list.append({"relative_path": rel, "content": content})

            for rel_path in sorted(self.include_files_resolved):
                # skip anything we already dumped above
                if any(rel_path == item["relative_path"] for item in important_files_list):
                    continue
                # honour binary check
                full_path = os.path.join(base_folder, rel_path)
                if not os.path.isfile(full_path) or is_binary_file(full_path):
                    continue
                try:
                    with open(full_path, "r") as f:
                        content = f.read()
                except Exception:
                    content = ""
                important_files_list.append(
                    {"relative_path": rel_path, "content": content}
                )


        else:
            # large repo – selective
            bytes_budget = self.print_contents_threshold
            for rel_path in sorted(self.include_files_resolved,key=sort_key):
                if (container_workspace
                        and not rel_path.startswith(container_workspace)
                        and "/interfaces/" not in rel_path      # <-- allow interface files
                        and not any(rel_path.startswith(cd) for cd in self.common_directories)):
                    continue

                full = os.path.join(base_folder, rel_path)
                if not os.path.isfile(full) or is_binary_file(full):
                    continue

                size = os.path.getsize(full)
                interfaces_priority = ("/interfaces/" in rel_path or rel_path.endswith("/interfaces"))

                # 🔔 interfaces priority: ignore budget for big interface files
                if size > bytes_budget and not interfaces_priority:
                    continue

                with open(full, "r") as f:
                    if size > bytes_budget and interfaces_priority:
                        # include head and mark truncation
                        content = f.read(bytes_budget) + "\n... <truncated> ..."
                        bytes_budget = 0
                    else:
                        content = f.read()
                        bytes_budget -= size

                important_files_list.append({"relative_path": rel_path, "content": content})

        # ---------------------------------------------------------------- 5
        return {
            "base_directory": base_folder,
            "filesystem": "\n".join(filesystem_lines),
            "important_files": important_files_list,
        }