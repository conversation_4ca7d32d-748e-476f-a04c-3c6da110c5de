#!/usr/bin/env python3
import os
import sys

from code_generation_core_agent.agents.micro.file_lister import FileL<PERSON>

# Default base directory to inspect
default_directory = "/Users/<USER>/Work/Kavia/workarea/kavia/codegen-tic-tac-toe-m"
#default_directory = "/Users/<USER>/Work/Kavia/workarea/kavia/codegen-kavia-website"

# Default container workspace to restrict scope within the base directory
default_container_workspace = "tic_tac_toe_frontend_workspace"
#default_container_workspace = "kavia-website-v2"

def main():
    # Parse CLI args
    if len(sys.argv) < 2:
        print("Usage: python important_files_test.py <base_directory> [<container_workspace>]")
        print(f"Using default base_directory: {default_directory}")
        base_directory = default_directory
    else:
        base_directory = sys.argv[1]

    if len(sys.argv) < 3:
        container_workspace = default_container_workspace
        print(f"Using default container_workspace: {container_workspace}")
    else:
        container_workspace = sys.argv[2]

    # Threshold and filters
    threshold = 500
    exclude_dirs = ["node_modules", "venv", "public", "build", "dist", ".git", "__pycache__", ".idea", "logs"]
    exclude_files = ["secret.txt", "config.yaml", "debug.log", "package-lock.json"]
    exclude_extensions = [".tmp", ".bak", ".log"]
    include_files = ["UserProfilePage.js"]

    # Instantiate the file lister
    lister = FileLister(
        threshold=threshold,
        emit_important_files=True,
        exclude_dirs=exclude_dirs,
        exclude_files=exclude_files,
        exclude_extensions=exclude_extensions,
        include_files=include_files,
        print_contents_threshold=10000 * 1024
    )

    # Call list_as_json with the container_workspace
    output = lister.list_as_json(base_directory, container_workspace=container_workspace)
    print(output)

if __name__ == '__main__':
    main()