Rules to follow when the container/component that you are writing code has dependencies on other containers/components:

1. Download and read the API spec of the container/component that your code has a dependency on. This should be done by using the download_asset call using the endpoint specified in the 'API Spec' key for the container.
2. If the container is a database (check if 'db_env_vars' key exists), use the environment variable names listed in the 'db_env_vars' field to connect to the database. These will be comma-separated names like "MYSQL_URL, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB, MYSQL_PORT". The environment will already have these variables configured with the appropriate values and also have a '.env' file. Do not assume what these variables contain based on their names - use them as provided.
3. If the API specification is not available for the container read the code of the container/component to discover the API.