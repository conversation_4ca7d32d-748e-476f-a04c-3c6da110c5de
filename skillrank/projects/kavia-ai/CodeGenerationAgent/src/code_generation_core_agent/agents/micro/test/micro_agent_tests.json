{"base_path": "/Users/<USER>/Work/Kavia/workarea/kavia/codegen-tic-tac-toe-m", "prompts_base_path": "src/code_generation_core_agent/agents/micro/prompts", "docker_config": {"container_name": "kavia_default_container_image"}, "test_cases": [{"name": "Simple Code Writing Test - Streaming", "agent_type": "CodeWritingAgent", "streaming": true, "prompt": {"task": "Write a React/JS App for tic-tac-toe game", "subtask": {"current_subtask": "Writing code", "subtask_details": "write code for the whole application"}, "work_item": {}, "request_details": {"requirements": ["Support AI player", "Victory celebration", "Diffuculty levels"]}}}, {"name": "Code Review Test - Non-Streaming", "agent_type": "QuestionAnsweringAgent", "streaming": false, "prompt": {"task": "Code base analysis and review", "subtask": {"current_subtask": "Tell me about this codebase", "subtask_details": "Create an architecture document and review the code"}, "work_item": {}, "request_details": {}}}, {"name": "Test Writing Agent - Streaming", "agent_type": "TestCodeWritingAgent", "streaming": true, "prompt": {"task": "Generate unit tests the codebase", "subtask": {"current_subtask": "Writing tests", "subtask_details": "Creating comprehensive unit tests"}, "work_item": {}, "request_details": {}}}, {"name": "Documentation Agent Test - Non-Streaming", "agent_type": "DocumentationAgent", "streaming": false, "prompt": {"task": "Generate API documentation", "subtask": {"current_subtask": "Writing documentation", "subtask_details": "Creating API documentation with examples"}, "work_item": {"component_name": "API Documentation", "description": "Generate comprehensive API documentation"}, "request_details": {}}}], "global_config": {"timeout": 300, "retry_count": 3, "log_level": "INFO", "environment": {"PYTHONPATH": ".", "PYTEST_DISABLE_PLUGIN_AUTOLOAD": "true"}}}