# supabase_configuration_micro_agent.py
from code_generation_core_agent.agents.micro.agent_system import AgentRegistry
from code_generation_core_agent.agents.micro.generic_file_writer_agent import GenericFileWriterMicroAgent
@AgentRegistry.register("SupabaseConfigurationAgent")
class SupabaseConfigurationMicroAgent(GenericFileWriterMicroAgent):
    """Micro agent for handling test code writing and modification tasks"""


    async def process_request(self, action, subtask=None,
                            work_item=None, request_details=None,
                            tool_call_log=None,
                            capabilities: dict = None, container_name=None):
        """
        Process a code writing request and handle the file operations specified in the response.
        Uses the standard non-streaming mode.
        """
        self.set_doc_mode(True)
        self.set_session_reuse(True)

        return await super().process_request(
            action, subtask, work_item, request_details,
            tool_call_log, capabilities, container_name=container_name
        )
    
    async def process_request_streaming(self, action, subtask=None,
                                      work_item=None, request_details=None,
                                      tool_call_log=None,
                                      capabilities: dict = None, container_name=None):
        """
        Process a code writing request using the streaming LLM capabilities.
        This version processes JSONL format and executes operations as they arrive.
        """

        async for chunk in super().process_request_streaming(
        action, subtask, work_item, request_details,
        tool_call_log, capabilities, container_name=container_name,
        ):
            yield chunk