from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerInfo
from code_generation_core_agent.agents.micro.dsa.ContainerSetupStep import ContainerSetupStep
from code_generation_core_agent.agents.micro.dsa.ContainerStepCategory import ContainerStepCategory
from code_generation_core_agent.agents.micro.dsa.ContainerStepStatus import ContainerStepStatus
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
import logging


@dataclass
class ChangeLogEntry:
    """Represents a single change log entry with timestamp and metadata"""
    timestamp: datetime
    message: str
    change_type: str = "initial"  # "initial", "refinement", "engineer_feedback", "analyst_review"
    source: str = "devops_architect"  # "devops_architect", "engineer", "analyst"



@dataclass
class ContainerSetupPlan:
    container_info: ContainerInfo
    steps: List[ContainerSetupStep] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    reasoning: str = ""
    change_logs: List[ChangeLogEntry] = field(default_factory=list)
    
    # QA-related fields
    qa_approved: bool = False
    qa_summary: str = ""
    qa_issues: List[str] = field(default_factory=list)
    qa_recommendations: List[Dict[str, Any]] = field(default_factory=list)

    # Convenience properties for easy access to container info
    @property
    def container_name(self) -> str:
        return self.container_info.container_name

    @property
    def container_type(self) -> str:
        return self.container_info.container_type

    @property
    def framework(self) -> str:
        return self.container_info.framework

    @property
    def platform(self) -> str:
        return self.container_info.platform

    @property
    def alternative_frameworks(self) -> List[str]:
        return self.container_info.alternative_frameworks

    def add_change_log_entry(self, message: str, change_type: str = "general", 
                           source: str = "devops_architect"):
        """Add a new change log entry"""
        entry = ChangeLogEntry(
            timestamp=datetime.now(),
            message=message,
            change_type=change_type,
            source=source
        )
        self.change_logs.append(entry)

    def get_latest_change_log(self) -> str:
        """Get the message from the most recent change log entry"""
        if not self.change_logs:
            return ""
        return self.change_logs[-1].message

    def get_change_log_history(self) -> List[ChangeLogEntry]:
        """Get all change log entries in chronological order"""
        return sorted(self.change_logs, key=lambda x: x.timestamp)

    def add_step(self, step: ContainerSetupStep):
        """Add a step to the plan"""
        self.steps.append(step)

    def get_steps_by_category(self, category: ContainerStepCategory) -> List[ContainerSetupStep]:
        """Get steps by category"""
        return [step for step in self.steps if step.category == category]

    def get_steps_by_priority(self, priority: int) -> List[ContainerSetupStep]:
        """Get steps by execution priority"""
        def get_step_priority(step):
            step_priority = step.execution_priority
            if isinstance(step_priority, str):
                try:
                    step_priority = int(step_priority)
                except (ValueError, TypeError):
                    step_priority = 5  # Default fallback
            return step_priority
        
        return [step for step in self.steps if get_step_priority(step) == priority]

    def get_pending_steps(self) -> List[ContainerSetupStep]:
        """Get steps that are still pending"""
        return [step for step in self.steps if step.status == ContainerStepStatus.PENDING]

    def get_completed_steps(self) -> List[ContainerSetupStep]:
        """Get steps that have been completed"""
        return [step for step in self.steps if step.status == ContainerStepStatus.COMPLETED]

    def get_failed_steps(self) -> List[ContainerSetupStep]:
        """Get steps that have failed"""
        return [step for step in self.steps if step.status == ContainerStepStatus.FAILED]
    
    def get_next_pending_step(self) -> Optional[ContainerSetupStep]:
        """
        Get the next pending step in execution order.
        
        Returns:
            ContainerSetupStep if a pending step is found, None if all steps are completed or failed
        """
        current_steps = self.get_execution_order()
        
        # Find the next pending step
        for step in current_steps:
            if step.status == ContainerStepStatus.PENDING:
                return step
        
        return None
    
    def get_step_by_id(self, step_id: str) -> Optional[ContainerSetupStep]:
        """
        Get a step by its ID.
        
        Args:
            step_id: The ID of the step to find
            
        Returns:
            ContainerSetupStep if found, None otherwise
        """
        for step in self.steps:
            if step.id == step_id:
                return step
        return None
    
    def get_retry_statistics(self) -> Dict[str, Any]:
        """Get retry statistics for all steps"""
        total_retries = sum(step.retry_attempts for step in self.steps)
        steps_with_retries = [step for step in self.steps if step.retry_attempts > 0]
        max_retries = max(step.retry_attempts for step in self.steps) if self.steps else 0
        
        return {
            "total_retries": total_retries,
            "steps_with_retries": len(steps_with_retries),
            "max_retries_per_step": max_retries,
            "retry_details": [
                {
                    "step_id": step.id,
                    "step_name": step.name,
                    "retry_attempts": step.retry_attempts,
                    "status": step.status.value if step.status else None
                }
                for step in steps_with_retries
            ]
        }
    
    def get_execution_state_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current execution state of the plan.
        
        Returns:
            Dict containing execution state information
        """
        from code_generation_core_agent.agents.micro.dsa.ContainerStepStatus import ContainerStepStatus
        
        total_steps = len(self.steps)
        completed_steps = len(self.get_completed_steps())
        failed_steps = len(self.get_failed_steps())
        pending_steps = len(self.get_pending_steps())
        in_progress_steps = len([step for step in self.steps if step.status == ContainerStepStatus.IN_PROGRESS])
        skipped_steps = len([step for step in self.steps if step.status == ContainerStepStatus.SKIPPED])
        
        # Calculate progress percentage
        progress_percentage = (completed_steps / total_steps * 100) if total_steps > 0 else 0
        
        # Get retry statistics
        retry_stats = self.get_retry_statistics()
        
        return {
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "failed_steps": failed_steps,
            "pending_steps": pending_steps,
            "in_progress_steps": in_progress_steps,
            "skipped_steps": skipped_steps,
            "progress_percentage": round(progress_percentage, 1),
            "retry_statistics": retry_stats,
            "container_name": self.container_name,
            "framework": self.framework,
            "platform": self.platform
        }

    def get_script_by_name(self, name: str) -> Optional['Script']:
        """Get the most recent script by name from any step's output."""
        from code_generation_core_agent.agents.micro.dsa.Script import Script
        
        for step in self.steps:
            for script in step.output_scripts:
                if script.name == name:
                    return script
        return None

    def get_all_output_scripts(self) -> Dict[str, 'Script']:
        """Get all output scripts as a dictionary."""
        from code_generation_core_agent.agents.micro.dsa.Script import Script
        
        scripts = {}
        for step in self.steps:
            for script in step.output_scripts:
                scripts[script.name] = script
        return scripts

    def get_all_script_codes(self) -> Dict[str, str]:
        """Get all script codes as a dictionary."""
        scripts = {}
        for step in self.steps:
            if step.script_code:
                scripts[step.script_name] = step.script_code
        return scripts

    def validate_script_names(self) -> List[str]:
        """Validate that all script names are canonical and unique."""
        issues = []
        canonical_names = {"install", "scaffold", "test", "build", "start", "validation"}
        
        # Check input scripts
        script_names = set()
        for step in self.steps:
            if step.script_code:
                if step.script_name not in canonical_names:
                    issues.append(f"Invalid script name '{step.script_name}' in step '{step.id}'")
                if step.script_name in script_names:
                    issues.append(f"Duplicate input script name '{step.script_name}' in step '{step.id}'")
                script_names.add(step.script_name)
        
        # Check output scripts
        output_script_names = set()
        for step in self.steps:
            for script in step.output_scripts:
                if script.name not in canonical_names:
                    issues.append(f"Invalid output script name '{script.name}' in step '{step.id}'")
                if script.name in output_script_names:
                    issues.append(f"Duplicate output script name '{script.name}' in step '{step.id}'")
                output_script_names.add(script.name)
        
        return issues

    def generate_plan_summary(self) -> str:
        """
        Generate a human-readable text summary of the execution plan.
        
        Returns:
            str: Formatted text summary of the plan with step order and dependencies
        """
        if not self.steps:
            return "No steps available in the plan."
        
        summary = "Execution Plan:\n"
        for idx, step in enumerate(self.get_execution_order(), 1):
            dependencies = ', '.join(step.dependencies) if step.dependencies else 'None'
            summary += f"{idx}. {step.name}: {step.description} (Depends on: {dependencies})\n"
        return summary


    def validate_step_dependencies(self) -> List[str]:
        """Validate step dependencies and return any issues"""
        issues = []
        step_ids = {step.id for step in self.steps}
        
        for step in self.steps:
            for dep_id in step.dependencies:
                if dep_id not in step_ids:
                    issues.append(f"Step '{step.id}' depends on '{dep_id}' which does not exist")
        
        return issues

    def get_execution_order(self) -> List[ContainerSetupStep]:
        """Get steps in proper execution order based on dependencies and priority"""
        # Enhanced topological sort with priority consideration
        steps = self.steps.copy()
        execution_order = []
        completed = set()
        
        while steps:
            # Find steps with no pending dependencies
            ready_steps = [
                step for step in steps
                if all(dep in completed for dep in step.dependencies)
            ]
            
            if not ready_steps:
                # Circular dependency or missing step - add remaining steps with warnings
                execution_order.extend(steps)
                break
            
            # Sort ready steps by execution priority (lower number = higher priority)
            def get_priority(step):
                priority = step.execution_priority
                if isinstance(priority, str):
                    try:
                        priority = int(priority)
                    except (ValueError, TypeError):
                        priority = 5  # Default fallback
                return priority
            
            ready_steps.sort(key=get_priority)
            
            # Add ready steps to execution order
            for step in ready_steps:
                execution_order.append(step)
                completed.add(step.id)
                steps.remove(step)
        
        return execution_order

    def to_dict(self, exclude_container_info: bool = False, exclude_qa_fields: bool = False):
        """Convert the plan (and nested dataclasses) to a serializable dict, using asdict and handling Enums.
        
        Args:
            exclude_container_info: If True, excludes the container_info property from the output dict
            exclude_qa_fields: If True, excludes all QA-related fields from the output dict
        """
        from dataclasses import asdict, is_dataclass
        from enum import Enum
        from datetime import datetime
        
        def enum_to_value(obj):
            if isinstance(obj, Enum):
                return obj.value
            if isinstance(obj, datetime):
                return obj.isoformat()
            if is_dataclass(obj):
                return asdict(obj, dict_factory=enum_to_value_dict)
            if isinstance(obj, list):
                return [enum_to_value(i) for i in obj]
            if isinstance(obj, dict):
                return {k: enum_to_value(v) for k, v in obj.items()}
            return obj
            
        def enum_to_value_dict(items):
            return {k: enum_to_value(v) for k, v in items}
            
        d = asdict(self, dict_factory=enum_to_value_dict)
        d.pop('logger', None)
        
        if exclude_container_info:
            d.pop('container_info', None)
        
        if exclude_qa_fields:
            # Remove QA fields from the plan
            d.pop('qa_approved', None)
            d.pop('qa_summary', None)
            d.pop('qa_issues', None)
            d.pop('qa_recommendations', None)
        
        # Handle Script objects in steps
        for step in d.get('steps', []):
            if 'output_scripts' in step:
                step['output_scripts'] = [script.to_dict() if hasattr(script, 'to_dict') else script for script in step['output_scripts']]
            
            # Remove QA fields from steps if requested
            if exclude_qa_fields:
                step.pop('qa_approved', None)
                step.pop('qa_summary', None)
                step.pop('qa_issues', None)
            
        return d

    @classmethod
    def from_dict(cls, data):
        """Reconstruct a ContainerSetupPlan from a dict, handling nested dataclasses and Enums using dacite."""
        import dacite
        from code_generation_core_agent.agents.micro.dsa.ContainerSetupStep import ContainerSetupStep
        from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerInfo
        from code_generation_core_agent.agents.micro.dsa.ContainerStepCategory import ContainerStepCategory
        from code_generation_core_agent.agents.micro.dsa.ContainerStepStatus import ContainerStepStatus
        from code_generation_core_agent.agents.micro.dsa.Script import Script
        
        config = dacite.Config(
            type_hooks={
                ContainerStepCategory: ContainerStepCategory,
                ContainerStepStatus: ContainerStepStatus,
                Script: Script.from_dict
            },
            cast=[ContainerStepCategory, ContainerStepStatus]
        )
        return dacite.from_dict(
            data_class=cls,
            data=data,
            config=config
        )

    @classmethod
    def create_from_llm_analysis(cls, container_info: ContainerInfo, analysis: Dict[str, Any], logger: logging.Logger) -> 'ContainerSetupPlan':
        """Create a ContainerSetupPlan from LLM analysis results"""
        # Extract LLM-generated steps from analysis
        llm_steps = analysis.get("setup_steps", [])
        
        # Convert LLM-generated steps to ContainerSetupStep objects
        container_steps = []
        for step_data in llm_steps:
            container_step = cls._create_step_from_llm_data(step_data, logger)
            container_steps.append(container_step)
        
        # Create the plan
        plan = cls(
            container_info=container_info,
            steps=container_steps,
            dependencies=analysis.get("dependencies", []),
            reasoning=analysis.get("reasoning", ""),
            change_logs=[] # Initialize change_logs
        )
        
        # Add initial change log entry if reasoning or change_log is provided
        change_log = analysis.get("change_log", "")
        
        if change_log:
            plan.add_change_log_entry(
                message=change_log,
                change_type="initial",
                source="devops_architect",
            )
        
        # Validate step dependencies
        dependency_issues = plan.validate_step_dependencies()
        if dependency_issues:
            logger.warning(f"Step dependency issues found: {dependency_issues}")
        
        return plan

    @classmethod
    def create_refined_plan(cls, container_info: ContainerInfo, 
                                                   analysis: Dict[str, Any], 
                                                   original_plan: 'ContainerSetupPlan',
                                                   logger: logging.Logger) -> 'ContainerSetupPlan':
        """
        Create a refined plan with LLM-controlled step statuses (no automatic preservation).
        
        Args:
            container_info: Information about the container being set up
            analysis: LLM analysis results containing the refined plan (including status decisions)
            original_plan: The original plan used for shell script and retry attempt preservation
            logger: Optional logger for debugging
            
        Returns:
            ContainerSetupPlan with LLM-determined step statuses
        """
        # Create a mapping of step IDs to their statuses from the original plan
        original_step_script_codes = {step.id: step.script_code for step in original_plan.steps}
        original_step_output_scripts = {step.id: step.output_scripts for step in original_plan.steps}
        
        # Extract LLM-generated steps from analysis
        llm_steps = analysis.get("setup_steps", [])
        
        # Convert LLM-generated steps to ContainerSetupStep objects (LLM controls status decisions)
        container_steps = []
        for step_data in llm_steps:
            container_step = cls._create_step_from_llm_data(step_data, logger)
            container_steps.append(container_step)
        
        # Create the plan
        plan = cls(
            container_info=container_info,
            steps=container_steps,
            dependencies=analysis.get("dependencies", []),
            reasoning=analysis.get("reasoning", ""),
            change_logs=[], # Initialize change_logs
        )
        
        # Add refinement change log entry
        change_log = analysis.get("change_log", "")
        
        if change_log:
            plan.add_change_log_entry(
                message=change_log,
                change_type="refinement",
                source="devops_architect",
            )
        
        # Validate step dependencies
        dependency_issues = plan.validate_step_dependencies()
        if dependency_issues:
            logger.warning(f"Step dependency issues found: {dependency_issues}")
        
        return plan

    @staticmethod
    def _create_step_from_llm_data(step_data: Dict[str, Any], logger: logging.Logger) -> ContainerSetupStep:
        """Convert LLM-generated step data to ContainerSetupStep object"""
    
        # Handle category conversion with proper fallback
        category_str = step_data.get("category", ContainerStepCategory.CUSTOM.value)
        try:
            category = ContainerStepCategory(category_str)
        except ValueError:
            logger.warning(f"Invalid category '{category_str}' for step {step_data.get('id', 'unknown')}, using default 'environment'")
            category = ContainerStepCategory.CUSTOM
        
        # Ensure execution_priority is an integer
        execution_priority = step_data.get("execution_priority", 5)
        if isinstance(execution_priority, str):
            try:
                execution_priority = int(execution_priority)
            except (ValueError, TypeError):
                logger.warning(f"Invalid execution_priority '{execution_priority}' for step {step_data.get('id', 'unknown')}, using default 5")
                execution_priority = 5
        
        # Handle status from LLM data with validation
        status = ContainerStepStatus.PENDING  # default
        llm_status = step_data.get("status", "pending")
        try:
            # Map string status to enum
            status = ContainerStepStatus(llm_status)
        except ValueError:
            logger.warning(f"Invalid status '{llm_status}' for step {step_data.get('id', 'unknown')}, using default 'pending'")
            status = ContainerStepStatus.PENDING
        
        # Handle script name
        script_name = step_data.get("script_name", "install")
        if script_name not in {"install", "scaffold", "test", "build", "start", "validation"}:
            logger.warning(f"Invalid script_name '{script_name}' for step {step_data.get('id', 'unknown')}, using default 'install'")
            script_name = "install"
        
        # Get script code if provided
        script_code = step_data.get("script_code", None)
        
        return ContainerSetupStep(
            id=step_data["id"],
            name=step_data["name"],
            description=step_data["description"],
            category=category,
            dependencies=step_data.get("dependencies", []),
            tools_required=["VisibleShellTools", "ContainerFileTools", "DependencyTools", "ContainerWriteTools"],
            status=status,
            execution_priority=execution_priority,
            script_name=script_name,
            script_code=script_code,
            retry_attempts=step_data.get("retry_attempts", 0)
        )

    
    def create_initial_consolidated_script(self) -> str:
        """
        Create the initial consolidated shell script from output scripts.
        This method generates a basic shell script template from the scripts.
        
        Returns:
            str: Initial consolidated shell script
        """
        from datetime import datetime
        
        script_lines = [
            "#!/bin/bash",
            "",
            "# =============================================================================",
            f"# Consolidated Setup Script for {self.container_name}",
            f"# Framework: {self.framework}",
            f"# Platform: {self.platform}",
            f"# Generated: {datetime.now().isoformat()}",
            "# =============================================================================",
            "",
            "# Set strict error handling",
            "set -euo pipefail",
            "",
            "# Function to log messages",
            'log() { echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"; }',
            "",
            "# Start setup",
            'log "Starting development environment setup"',
            'log "Container: ' + self.container_name + '"',
            'log "Framework: ' + self.framework + '"',
            'log "Platform: ' + self.platform + '"',
            "",
        ]
        
        # Add scripts in order
        scripts = []
        for step in self.steps:
            for script in step.output_scripts:
                scripts.append((step.name, script))
        
        # Sort by step execution order
        execution_order = self.get_execution_order()
        ordered_scripts = []
        for step in execution_order:
            for script in step.output_scripts:
                ordered_scripts.append((step.name, script))
        
        # Add script content
        for i, (step_name, script) in enumerate(ordered_scripts, 1):
            script_lines.extend([
                f"# Step {i}: {step_name} - {script.name}",
                'log "Executing ' + script.name + ' script"',
                "",
                "# Execute script for this step",
                script.code.strip(),
                "",
                'log "Step ' + str(i) + ' completed successfully"',
                "",
            ])
        
        # Add completion message
        script_lines.extend([
            'log "Development environment setup completed successfully!"',
            'log "Container is ready for development"',
            "",
            "# =============================================================================",
            "# Setup Complete",
            "# =============================================================================",
        ])
        
        return "\n".join(script_lines)
    
    def get_recommendations(self, recommendation_level: 'ContainerSetupLevel' = None) -> List[Dict[str, str]]:
        """
        Extract recommendations at or above the specified priority level from QA review.
        
        Args:
            recommendation_level: The minimum priority level to filter for (default: CRITICAL)
                                 Returns recommendations at or above this level
            
        Returns:
            List of recommendation objects with 'level' and 'description' fields
        """
        from code_generation_core_agent.agents.micro.dsa.ContainerSetupLevel import ContainerSetupLevel
        
        if recommendation_level is None:
            recommendation_level = ContainerSetupLevel.CRITICAL
            
        recommendations = self.qa_recommendations
        filtered_recommendations = []
        
        # Get the levels we want to include
        target_levels = ContainerSetupLevel.get_levels_at_or_above(recommendation_level)
        
        for rec in recommendations:
            level_str = rec.get("level", "").upper()
            for level in target_levels:
                if level_str == level.value:
                    filtered_recommendations.append(rec)
                    break
        
        return filtered_recommendations

    def get_formatted_steps_scripts(self) -> str:
        """
        Pretty print the steps in execution order with their scripts.
        
        Returns:
            str: Formatted string showing steps with separators and script details
        """
        if not self.steps:
            return "No steps available in the plan."
        
        output_lines = []
        execution_order = self.get_execution_order()
        
        for i, step in enumerate(execution_order, 1):
            # Add separator
            output_lines.append("-" * 72)
            
            # Add step information
            output_lines.append(f"Step {i}: {step.name}")
            output_lines.append(f"Script: {step.script_name}")
            
            # Add script code if available
            if step.script_code:
                output_lines.append("Script Code:")
                output_lines.append(step.script_code.strip())
            else:
                output_lines.append("Script Code: (No script code available)")
            
            # Add separator
            output_lines.append("-" * 72)
            output_lines.append("")  # Empty line for readability
        
        return "\n".join(output_lines)
