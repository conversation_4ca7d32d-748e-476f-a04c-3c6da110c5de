container:
  container_name: kavia_ios_container

task_agents:
  CodeGeneration:
    micro_agents:
      - InitialSetupAgent
      - CodeAnalysisAgent
      - CodeWritingAgent
      - TestCodeWritingAgent
      - TestCaseCreationAgent
      - TestDataGenerationAgent
      - TestExecutionAgent
      - VisualVerificationAgent
      - SearchAgent
      - ContainerBuilderAgent

  CodeMaintenance:
    micro_agents:
      - InitialSetupAgent
      - CodeAnalysisAgent
      - CodeWritingAgent
      - TestCodeWritingAgent
      - TestCaseCreationAgent
      - TestDataGenerationAgent
      - TestExecutionAgent
      - VisualVerificationAgent
      - SearchAgent

micro_agents:
  ContainerBuilderAgent:
    capabilities:
      - Build a container image for the provided work item
    tools:
      - WorkInputDiscovery
    prompt_file: container_builder_agent.j2
    class: ContainerBuilderAgent
  
  InitialSetupAgent:
    capabilities:
      - Perform initial setup of development iOS environment
    tools:
      - InitialSetupTools
      - DependencyTools
      - IOSTools
    prompt_file: initial_setup_agent.j2
    class: InitialSetupMicroAgent
    setup_steps:
      init-minimal: prompts/platform/mobile/init-minimal.j2
      init-files: prompts/platform/common/init-files.j2

  CodeAnalysisAgent:
    capabilities:
      - Analyze what source code already exists and come up with a strategy and plan for handling various tasks and subtasks
      - Answer questions about the code or tests
      - Generate code sections to answer questions about code and tests
    tools:
      - KnowledgeTools
      - ContainerFileTools

    prompt_file: code_analysis_agent_prompt.j2
    class: CodeAnalysisMicroAgent  # Only if this exists

  CodeWritingAgent:
    capabilities:
      - Write or update code on the repository based on the provided direction and using the provided strategy
      - refactor_code to improve code quality or to address issues
      - Builds the iOS application
      - Run the tests for the iOS application
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - ReviewStatusTools
      - IOSTools
      - KnowledgeTools
      - EnvironmentSummary
      - DependencyTools
    prompt_file: code_writing_agent_prompt.j2

  EnvironmentSetupAgent:
    capabilities:
      - Install tools and packages necessary for code development and testing
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - ReviewStatusTools
      - KnowledgeTools
      - EnvironmentSummary

  TestCodeWritingAgent:
    capabilities:
      - Write code for unit tests on to the repository
      - write code for integration tests on to the repository
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - ReviewStatusTools
      - KnowledgeTools
      - EnvironmentSummary
    prompt_file: test_code_writing_agent_prompt.j2

  TestCaseCreationAgent:
    capabilities:
      - Create test cases for new functionality to be added to the component
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - EnvironmentSummary
      - ReviewStatusTools
    prompt_file: test_case_creation_agent_prompt.j2    

  TestDataGenerationAgent:
    capabilities:
      - Create synthetic test data for testing the component based on test cases
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - ReviewStatusTools
      - KnowledgeTools
      - EnvironmentSummary
    prompt_file: test_data_generation_agent_prompt.j2

  TestExecutionAgent:
    capabilities:
      - Execute test cases for the component using IOSTools's test_ios_app tool
    tools:
      - IOSTools
      - ReviewStatusTools
      - KnowledgeTools
      - EnvironmentSummary
    prompt_file: test_execution_agent_prompt.j2

  VisualVerificationAgent:
    capabilities:
      - Visually verify the application.
      - Runs the iOS simulator when needed.
      - Builds the iOS application.
      - Runs the iOS application in the simulator.
      - Takes screenshots from the iOS application running in the simulator.
    tools:
      - VisibleShellTools
      - IOSTools
      - KnowledgeTools
    prompt_file: visual_verification_agent_prompt.j2

file_lister:
  exclude_dirs:
    - "Pods"
    - "DerivedData"
    - "build"
    - ".swiftpm"
  exclude_files:
    - "*.xcuserstate"
    - ".DS_Store"
  exclude_extensions:
    - ".ipa"
    - ".app"
    - ".xcarchive"
  include_files:
    - "Podfile"
    - "*.xcodeproj/project.pbxproj"
    - "*.xcworkspace/contents.xcworkspacedata"