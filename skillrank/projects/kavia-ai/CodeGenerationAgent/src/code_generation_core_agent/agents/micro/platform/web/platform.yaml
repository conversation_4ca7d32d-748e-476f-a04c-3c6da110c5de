include:
  - platform/common/platform.yaml

file_lister:
  exclude_dirs:
    - ".git"
    - "__pycache__"
    - ".pytest_cache"
    - ".venv"
    - "venv"
    - "env"
    - "build"
    - "dist"
    - "public"
    - "node_modules"
    - "target"
    - "build"
    - "dist"
    - "logs"
  exclude_files:
    - "files.yaml"
    - ".DS_Store"
    - ".gitignore"
    - "*.pyc"
    - "package-lock.json"
  exclude_extensions:
    - ".log"
    - ".pyc"
    - ".pyo"
    - ".pyd"
    - ".so"
    - ".dll"
    - ".dylib"
    - ".svg"
  include_files:
    - "README.md"
    - "requirements.txt"
    - "setup.py"
    - "App.js"