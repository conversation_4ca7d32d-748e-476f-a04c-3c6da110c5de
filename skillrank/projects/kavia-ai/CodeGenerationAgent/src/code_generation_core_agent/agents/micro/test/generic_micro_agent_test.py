import json
import os
import async<PERSON>
from pathlib import Path
from typing import Dict, List, Any, Optional
from code_generation_core_agent.agents.framework.micro_agent_registry import MicroAgentRegistry
from code_generation_core_agent.agents.framework.micro_agent_registry import MicroAgentType
from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.tools.knowledge_tools import KnowledgeTools
from code_generation_core_agent.agents.tools.visible_shell_tools import VisibleShellTools
from code_generation_core_agent.agents.tools.strategic_analysis_tool import StrategicAnalysisTool
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.combined_tools import CombinedTools
from code_generation_core_agent.agents.tools.AgentPreProcessor import AgentPreProcessor
from code_generation_core_agent.agents.tools.tracking.shell_command_call_tracker import <PERSON><PERSON>om<PERSON>Call<PERSON><PERSON>
from code_generation_core_agent.config import config
from code_generation_core_agent.agents.utilities import Model<PERSON>ameSelector
import yaml

from code_generation_core_agent.agents.utilities import TaskExecutionReporter
from code_generation_core_agent.agents.utilities import FunctionCallDescription


class MockReporter(TaskExecutionReporter):
    def __init__(self):
        super().__init__()
        self.execution_report = []

    def progress_callback(self, status, total_tasks, latest_result, request_context):
        pass

    def terminal_output_callback(self, output):
        pass

    def browser_output_callback(self, image):
        pass

    def cost_update_callback(self, agent_costs, total_cost):
        pass

    def function_call_callback(self, call_description: FunctionCallDescription):
        """
        Return the function call description to the user.
        """
        pass

    def codeview_callback(self, code: str):
        pass


    def task_start_callback(self, task_description):
        pass


    def user_query_callback(self, questions):
        pass


    def send_agent_message(self, message):
        pass


    def get_user_input(self, prompt):
        pass


    def send_code_panel_update(self, stream_id, content, metadata=None):
        pass
    def send_code_panel_update(self, stream_id, content, metadata=None):
        pass
    def app_state_callback(self, url, state=None):
        pass

    def universal_init_status_callback(self, container_name, status, message=None, error=None):
        """
        Report the status of universal initialization for a container/component.
        
        Args:
            container_name (str): Name of the container or component being initialized
            status (str): Status of initialization ('starting', 'success', 'failed', 'completed')
            message (str, optional): Status message or description
            error (str, optional): Error message if initialization failed
        """
        pass
    
class MicroAgentTester:
    def __init__(self, base_path: str, config_file: str, executor: DockerExecutor):
        self.base_path = base_path
        self.config_file = config_file
        self.executor = executor
        self.logger = setup_logger("micro_agent_tester", base_path)
        self.tools_logger = setup_logger("tools", base_path)
        # Load test configuration
        with open(config_file, 'r') as f:
            self.test_config = json.load(f)
        # Initialize components
        self.llm = self._initialize_llm()
        self.agent_tools = self._setup_dynamic_tools()
        self.micro_agent_registry = None  # Will be initialized per test
    def _initialize_llm(self) -> LLMInterface:
        """Initialize the LLM interface."""
        api_key = os.environ.get("ANTHROPIC_API_KEY") or os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("No API key found. Set either ANTHROPIC_API_KEY or OPENAI_API_KEY")
        return LLMInterface(
            llm_api_key=api_key,
            session_dir=self.base_path,
            instance_name="test_instance"
        )
    def _setup_dynamic_tools(self) -> CombinedTools:
        """Set up the necessary tools for the agents."""
        callback_functions = {
            "terminal_output_callback": lambda x: print(f"Terminal Output: {x}"),
            "browser_output_callback": lambda x: print(f"Browser Output: {x}"),
            "user_query_callback": lambda x: print(f"User Query: {x}"),
            "function_call_callback": lambda x: print(f"Function Call: {x}"),
            "codeview_callback": lambda x: print(f"Code View: {x}"),
            "agent_message_callback": lambda x, y=None, **kwargs: print(f"Agent Message: {x}")
        }
        # Initialize all required tools
        tool_instances = [
            VisibleShellTools(callback_functions, self.base_path,
                            logger=self.tools_logger, llm=self.llm, executor=self.executor),
            StrategicAnalysisTool(callback_functions, self.base_path,
                                logger=self.tools_logger, llm=self.llm, executor=self.executor),
            KnowledgeTools(callback_functions, self.base_path,
                          logger=self.tools_logger, llm=self.llm, executor=self.executor),
            ContainerFileTools(callback_functions, self.base_path,
                             logger=self.tools_logger, llm=self.llm, executor=self.executor)
        ]
        agent_pre_processor = AgentPreProcessor(
            callback_functions,
            logger=self.logger,
            shell_command_tracker=ShellCommandCallTracker(self.base_path)
        )
        return CombinedTools(
            tool_instances,
            [agent_pre_processor],
            self.base_path,
            logger=self.logger,
            llm=self.llm,
            executor=self.executor
        )
    async def run_test_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single test case."""
        agent_type = test_case['agent_type']
        prompt = test_case['prompt']
        streaming = test_case.get('streaming', False)
        print(f"\n=== Running test case: {test_case.get('name', 'Unnamed')} ===")
        print(f"Agent: {agent_type}")
        print(f"Streaming: {streaming}")
        agent_config_file = "src/code_generation_core_agent/agents/micro/platform/common/platform.yaml"
        # Load agent configuration
        with open(agent_config_file, 'r') as f:
            agent_config = yaml.safe_load(f)
        # Initialize MicroAgentRegistry for this test
        self.micro_agent_registry = MicroAgentRegistry(
            "CodeGeneration",
            agent_config,
            self.logger,
            prompts_base_path=self.test_config.get('prompts_base_path'),
            model_name=ModelNameSelector(config.get("LLM", "model")),
            execution_base_path=self.base_path,
            reporter = None,
            agent_tools=self.agent_tools
        )
        # Initialize the agents with tools
        self.micro_agent_registry.init_micro_agents(self.agent_tools, MockReporter())
        try:
            # Get the appropriate agent
            agents = self.micro_agent_registry.get_agents(MicroAgentType.CODER_MICRO_AGENTS)
            if not agents or agent_type not in agents:
                return {"status": "ERROR", "message": f"Agent {agent_type} not found"}
            agent = agents[agent_type]
            tool_call_log = []
            capabilities = self.micro_agent_registry.get_capabilities(MicroAgentType.CODER_MICRO_AGENTS)
            if streaming:
                result = []
                async for chunk in agent.process_request_streaming(
                    prompt['task'],
                    subtask=prompt.get('subtask'),
                    work_item=prompt.get('work_item'),
                    request_details=prompt.get('request_details'),
                    tool_call_log=tool_call_log,
                    capabilities=capabilities
                ):
                    if isinstance(chunk, dict):
                        print(f"[CHUNK] {json.dumps(chunk)}")
                        if chunk.get('final'):
                            result.append(chunk['content'])
                    else:
                        print(f"[CHUNK] {chunk}")
                        result.append(str(chunk))
                final_result = ''.join(result)
            else:
                final_result = await agent.process_request(
                    prompt['task'],
                    subtask=prompt.get('subtask'),
                    work_item=prompt.get('work_item'),
                    request_details=prompt.get('request_details'),
                    tool_call_log=tool_call_log,
                    capabilities=capabilities
                )
            return {
                "status": "SUCCESS",
                "result": final_result,
                "tool_calls": len(tool_call_log)
            }
        except Exception as e:
            return {
                "status": "ERROR",
                "message": str(e),
                "tool_calls": len(tool_call_log) if 'tool_call_log' in locals() else 0
            }
    async def run_all_tests(self):
        """Run all test cases from the configuration."""
        results = []
        for test_case in self.test_config['test_cases']:
            result = await self.run_test_case(test_case)
            results.append({
                "name": test_case.get('name', 'Unnamed'),
                "result": result
            })
        return results
    
def initialize_docker(config: Dict) -> DockerExecutor:
    """Initialize Docker executor with configuration."""
    docker_config = config.get('docker_config', {
        "container_name": "kavia_default_container_image",
    })
    executor = DockerExecutor(
        session_dir=config['base_path'],
        container_name=docker_config['container_name'],
    )
    return executor

def main():
    # Example JSON configuration file structure
    example_config = {
        "base_path": "/Users/<USER>/Work/Kavia/workarea/kavia/codegen-tic-tac-toe-m",
        "prompts_base_path": "src/code_generation_core_agent/agents/micro/prompts",
        "docker_config": {
            "container_name": "kavia_default_container_image",
        },
        "test_cases": [
            {
                "name": "Simple Code Writing Test",
                "agent_type": "CodeWritingAgent",
                "streaming": True,
                "prompt": {
                    "task": "Write a Python function to calculate factorial",
                    "subtask": {
                        "current_subtask": "Writing code",
                        "subtask_details": "Implementing factorial function"
                    },
                    "work_item": {
                        "component_name": "Factorial Calculator",
                        "description": "A function to calculate factorial of a number"
                    },
                    "request_details": {
                        "language": "python",
                        "output_file": "factorial.py"
                    }
                }
            }
            # Add more test cases here
        ]
    }
    # Load actual configuration file
    config_file = os.environ.get("TEST_CONFIG_FILE", "src/code_generation_core_agent/agents/micro/test/micro_agent_tests.json")
    base_path = os.environ.get("TEST_BASE_PATH", "/Users/<USER>/Work/Kavia/workarea/kavia/codegen-tic-tac-toe-m")
    # Ensure base path exists
    Path(base_path).mkdir(parents=True, exist_ok=True)
    # Create example config if it doesn't exist
    if not os.path.exists(config_file):
        with open(config_file, 'w') as f:
            json.dump(example_config, f, indent=2)
        print(f"Created example configuration file: {config_file}")
        return
    # Load configuration
    with open(config_file, 'r') as f:
        config = json.load(f)
    try:
        # Initialize Docker before entering async context
        print("Initializing Docker...")
        executor = initialize_docker(config)
        async def run_tests():
            try:
                tester = MicroAgentTester(base_path, config_file, executor)
                results = await tester.run_all_tests()
                # Print results
                print("\n=== Test Results ===")
                for result in results:
                    print(f"\nTest: {result['name']}")
                    print(f"Status: {result['result']['status']}")
                    if result['result']['status'] == 'ERROR':
                        print(f"Error: {result['result']['message']}")
                    else:
                        print(f"Tool calls: {result['result']['tool_calls']}")
            except Exception as e:
                print(f"Error during test execution: {str(e)}")
            finally:
                # Cleanup async resources if needed
                pass
        # Run the async main function
        print("Starting test execution...")
        asyncio.run(run_tests())
    except Exception as e:
        print(f"Error during initialization: {str(e)}")
    finally:
        # Cleanup Docker
        try:
            print("Cleaning up Docker resources...")
            executor.cleanup()
        except Exception as e:
            print(f"Error during Docker cleanup: {str(e)}") 
if __name__ == "__main__":
    main()