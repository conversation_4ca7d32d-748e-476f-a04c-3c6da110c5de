import json
import logging
from datetime import datetime
from code_generation_core_agent.agents.micro.dsa.ContainerSetupPlan import Container<PERSON>etup<PERSON><PERSON>
from code_generation_core_agent.agents.micro.dsa.ContainerSetupStep import ContainerSetupStep
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerInfo
from code_generation_core_agent.agents.micro.dsa.ContainerSetupLevel import Container<PERSON>etup<PERSON>evel
from code_generation_core_agent.agents.micro.dsa.prompt_utils import render_jinja_template
from code_generation_core_agent.agents.setup_logger import setup_logger
from typing import Dict, Any, List, Optional


# TODO: Make these configurable 
MIN_RECOMMENDATION_LEVEL = ContainerSetupLevel.HIGH


class DevOpsArchitectAgent:
    """
    DevOps Architect Agent: Generates and refines container setup plans using LLMInterface.
    """
    MAX_REFINEMENTS = 10  # Maximum number of plan refinements to prevent infinite loops
    
    def __init__(self, base_path: str, model_name: str = "gpt-4"):
        self.logger = setup_logger("DevOpsArchitectAgent", base_path)
        self.model_name = model_name
        self.llm = LLMInterface(
            llm_api_key=None,
            session_dir=base_path,
            instance_name="devops_architect"
        )
        self.refinement_count = 0  # Track total refinements during execution

    def get_refinement_count(self) -> int:
        """Get the current refinement count."""
        return self.refinement_count

    def reset_refinement_count(self) -> None:
        """Reset the refinement count to 0."""
        self.refinement_count = 0

    def check_max_refinements_reached(self) -> None:
        """
        Check if maximum refinements have been reached and raise an exception if so.
        
        Raises:
            Exception: If maximum refinements limit is exceeded
        """
        if self.refinement_count >= self.MAX_REFINEMENTS:
            raise Exception(f"Maximum plan refinements ({self.MAX_REFINEMENTS}) exceeded.")

    def _build_unified_plan_prompt(self, container_info: ContainerInfo,
                                  current_plan_json: str = "", review_data: Dict[str, Any] = None, 
                                  refinement_source: str = "analyst") -> str:
        """
        Builds a unified prompt that maintains consistency between initial plan generation and refinement.
        
        Args:
            container_info: Container information
            current_plan_json: Current plan JSON (for refinement)
            review_data: Review data (for refinement)
            refinement_source: Source of the refinement request ("analyst" or "engineer")
        """
        # Use the Jinja template to render the unified prompt
        template_vars = {
            "container_info": container_info,
            "current_plan_json": current_plan_json,
            "review_data": review_data,
            "refinement_source": refinement_source,
            "review_data_json": json.dumps(review_data, indent=2) if review_data else ""
        }
        prompt = render_jinja_template("setup_plan_generation.j2", template_vars, self.logger)   
        self.logger.debug(f"setup plan prompt: {prompt}")
        return prompt

    async def detect_framework_and_platform(self, container_info: ContainerInfo) -> None:
        """
        Step 1: Detect the framework and platform from container request details.
        Updates the container_info object directly with the detected information.
        """
        # Render the prompt using the Jinja template
        template_vars = {
            "container_info": container_info
        }
        prompt = render_jinja_template("framework_detection.j2", template_vars, self.logger)
        
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=prompt,
            system_prompt="You are a DevOps Framework Detection Specialist.",
            model=self.model_name,
            response_format={"type": "json_object"}
        )
        
        detection_result = completion.choices[0].message.content
        framework_analysis = json.loads(detection_result)
        container_info.update_framework_analysis(framework_analysis)

    async def generate_setup_plan(self, container_info: ContainerInfo) -> ContainerSetupPlan:
        """
        Step 2: Generate a comprehensive setup plan based on the detected framework and platform.
        """
        # Use the unified prompt builder for initial plan generation
        prompt = self._build_unified_plan_prompt(container_info)
        self.logger.info(f"setup plan prompt: \n{prompt}")
        
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=prompt,
            system_prompt="You are a Senior DevOps Architect specializing in containerized development environments.",
            model=self.model_name,
            response_format={"type": "json_object"}
        )
        
        plan_result = completion.choices[0].message.content
        plan_dict = json.loads(plan_result)
        
        # Create plan object
        plan_obj = ContainerSetupPlan.create_from_llm_analysis(
            container_info=container_info,
            analysis=plan_dict,
            logger=self.logger
        )
        formatted_steps_scripts = plan_obj.get_formatted_steps_scripts()
        self.logger.info(f"Formatted steps scripts: \n{formatted_steps_scripts}")
        return plan_obj

    async def generate_consolidated_script(self, container_info: ContainerInfo) -> str:
        """
        Generate a single consolidated bash script for automated development setup.
        This method uses a two-step process: framework detection followed by script generation.
        
        Args:
            container_info: Container information containing workspace path and Dockerfile summary
            
        Returns:
            str: Single bash script string (no JSON, no prose)
        """
        # Step 1: Detect framework and platform (reuse existing method)
        self.logger.info(f"Starting framework detection for container: {container_info.container_name}")
        await self.detect_framework_and_platform(container_info)
        self.logger.info(f"Framework detection completed. Detected: {container_info.framework} on {container_info.platform}")
        
        # Step 2: Generate consolidated script based on detected framework
        self.logger.info(f"Starting consolidated script generation for framework: {container_info.framework}")
        
        # Render the prompt using the Jinja template
        template_vars = {
            "container_info": container_info
        }
        prompt = render_jinja_template("setup_script_generation.j2", template_vars, self.logger)
        self.logger.info(f"Consolidated script generation prompt: \n{prompt}")
        
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=prompt,
            system_prompt="You are a Senior DevOps Architect specializing in containerized development environments.",
            model=self.model_name,
            response_format={"type": "text"}  # No JSON format - expect raw text
        )
        
        script_result = completion.choices[0].message.content
        self.logger.info(f"Consolidated script generation completed for container: {container_info.container_name}")
        
        # Return the script as a single string (no JSON parsing needed)
        return script_result

    async def refine_consolidated_script(self, script_content: str, container_info: ContainerInfo, 
                                       review_feedback: Dict[str, Any]) -> str:
        """
        Refine a consolidated script based on analyst feedback.
        
        Args:
            script_content: The current bash script content to refine
            container_info: Container information for context
            review_feedback: Feedback from the analyst containing issues and recommendations
            
        Returns:
            str: Refined bash script string
        """
        self.logger.info(f"Refining consolidated script for container: {container_info.container_name}")
        
        # Prepare template variables
        template_vars = {
            "script_content": script_content,
            "container_info": container_info,
            "review_feedback": review_feedback
        }
        
        # Render the prompt using the Jinja template
        prompt = render_jinja_template("setup_script_refinement.j2", template_vars, self.logger)
        self.logger.info(f"Script refinement prompt: \n{prompt}")
        
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=prompt,
            system_prompt="You are a Senior DevOps Architect specializing in containerized development environments.",
            model=self.model_name,
            response_format={"type": "text"}  # No JSON format - expect raw text
        )
        
        refined_script = completion.choices[0].message.content
        self.logger.info(f"Script refinement completed for container: {container_info.container_name}")
        
        # Return the refined script as a single string
        return refined_script

    async def generate_initial_plan(self, container_info: ContainerInfo) -> ContainerSetupPlan:
        """
        Generate an initial setup plan for the given container using LLMInterface and return a ContainerSetupPlan object.
        This method now uses a two-step process: framework detection followed by setup plan generation.
        """
        # Step 1: Detect framework and platform
        self.logger.info(f"Starting framework detection for container: {container_info.container_name}")
        await self.detect_framework_and_platform(container_info)
        self.logger.info(f"Framework detection completed. Detected: {container_info.framework} on {container_info.platform}")
        
        # Step 2: Generate setup plan based on detected framework
        self.logger.info(f"Starting setup plan generation for framework: {container_info.framework}")
        plan_obj = await self.generate_setup_plan(container_info)
        self.logger.info(f"Setup plan generation completed for container: {container_info.container_name}")
        
        return plan_obj
            
    async def refine_plan_from_engineer_feedback(self, plan: ContainerSetupPlan, 
                                                 step: ContainerSetupStep) -> ContainerSetupPlan:
        """
        Refine the setup plan based on engineer feedback. This method encapsulates the logic for creating
        the review structure from the step object and then calls the main refine_plan method.
        
        Args:
            plan: The current setup plan to refine
            step: The step object containing engineer feedback
            
        Raises:
            Exception: If maximum refinements limit is exceeded
        """
        self.logger.warning(f"DevOps Engineer requesting architect help for step '{step.name}' (attempt {step.retry_attempts}): {step.architect_help_request}")

        # Check if max refinements reached before incrementing
        self.check_max_refinements_reached()
        
        # Increment refinement counter
        self.refinement_count += 1
        self.logger.info(f"Plan refinement #{self.refinement_count}/{self.MAX_REFINEMENTS} requested for step '{step.name}'")
        
        architect_help_request = step.architect_help_request or ""
        step_summary = step.summary or ""
        
        # Create a more detailed review structure for the architect
        architect_review = {
            "summary": f"DevOps Engineer encountered critical blocking issue in step '{step.name}'",
            "blocking_issue": f"Critical blocking issue in step '{step.name}': {architect_help_request}"
        }
        
        # Call the main refine_plan method with the created review
        refined_plan = await self.refine_plan(plan.container_info, plan, architect_review, refinement_source="engineer")        
        step.reset_retry_attempts()
        self.logger.info(f"Plan was refined by architect after engineer help request for step '{step.name}'")        
        return refined_plan
    
    async def refine_plan_from_analyst_feedback(self, plan: ContainerSetupPlan) -> ContainerSetupPlan:
        """
        Refine the setup plan based on analyst feedback. This method encapsulates the logic for creating
        the review structure from analyst results and then calls the main refine_plan method.
        
        Args:
            plan: The current setup plan to refine (contains container_info and QA recommendations)
            
        Raises:
            Exception: If maximum refinements limit is exceeded
        """
        # Check if max refinements reached before incrementing
        self.check_max_refinements_reached()
        
        # Increment refinement counter
        self.refinement_count += 1
        self.logger.info(f"Plan refinement #{self.refinement_count}/{self.MAX_REFINEMENTS} requested from analyst feedback")
        
        # Get container_info and recommendations from the plan
        container_info = plan.container_info
        
        review_data = {
            "summary": plan.qa_summary,
            "issues": plan.qa_issues,
            "recommendations": plan.qa_recommendations,
            "min_recommendation_level": MIN_RECOMMENDATION_LEVEL.value
        }
        return await self.refine_plan(container_info, plan, review_data, refinement_source="analyst")

    async def refine_plan(self, container_info: ContainerInfo, plan: ContainerSetupPlan, 
                          feedback_data: Dict[str, Any], refinement_source: str = "analyst") -> ContainerSetupPlan:
        
        # Use the unified prompt builder for refinement
        current_plan_json = json.dumps(plan.to_dict(exclude_container_info=True, exclude_qa_fields=True), indent=2)
        prompt = self._build_unified_plan_prompt(
            container_info=container_info,
            current_plan_json=current_plan_json,
            review_data=feedback_data,
            refinement_source=refinement_source
        )
        
        self.logger.info(f"Refining plan with {refinement_source} feedback: {feedback_data}")
        self.logger.info(f"Refining setup plan prompt: \n{prompt}")
        
        # Log execution state before refinement
        original_summary = plan.get_execution_state_summary()
        self.logger.info(f"Execution state before refinement: {original_summary['completed_steps']}/{original_summary['total_steps']} steps completed ({original_summary['progress_percentage']}%)")
        
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=prompt,
            system_prompt="You are a Senior DevOps Architect specializing in containerized development environments.",
            model=self.model_name,
            response_format={"type": "json_object"}
        )
        revised_result = completion.choices[0].message.content
        plan_dict = json.loads(revised_result)
        
        # Create refined plan object w
        plan_obj = ContainerSetupPlan.create_refined_plan(
            container_info=container_info,
            analysis=plan_dict,
            original_plan=plan,
            logger=self.logger
        )

        # Merge change logs from original plan into the refined plan
        plan_obj.change_logs = plan.change_logs + plan_obj.change_logs
        
        # Log execution state after refinement
        refined_summary = plan_obj.get_execution_state_summary()
        self.logger.info(f"Execution state after refinement: {refined_summary['completed_steps']}/{refined_summary['total_steps']} steps completed ({refined_summary['progress_percentage']}%)")
        formatted_steps_scripts = plan_obj.get_formatted_steps_scripts()
        self.logger.info(f"Formatted steps scripts after refinement: \n{formatted_steps_scripts}")
        return plan_obj

