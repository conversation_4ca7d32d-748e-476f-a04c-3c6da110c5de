Given the task of running the {{ application_type }} application for {{ component_name }}
({{ work_item.description }}) on the Android emulator, execute the proper {% if application_type == 'android' %}command sequence{% else %}flutter run command{% endif %}.

Follow these steps:

{% if application_type == 'android' %}
1. Ensure you are in the Android project directory {{ base_path }}/<ApplicationName>.

2. Build the application:
   - Use start_long_running_process tool to execute:
   - Command: "./gradlew :app:installDebug"
   - Working directory: Project root containing settings.gradle

3. Run the application:
   - Wait for the build to complete and then use the start_long_running_process tool to execute:
   - Command: "adb shell am start -n <package_name>/<activity_name>"
   - Replace <package_name> and <activity_name> with the appropriate values
{% else %}
1. Ensure you are in the Flutter project directory
2. Use start_long_running_process tool to execute:
   - Command: "flutter run --machine"
   - Working directory: Project root containing pubspec.yaml
{% endif %}

Note: Do not call the terminate_process tool as the application will be running in the emulator.

The current project folder structure is:
{{ folder_structure }}

Once the correct command has been determined, create the file "{{ base_path }}/.init/.init-run-tool" and
write the command to it in a JSON object with the following structure:
{
    "command": "<proper command to run app>",
    "working_directory": "<proper working directory>"
}

Please format your final response as a JSON object with the following structure:
{
    "actions": "<summary of actions taken>"
}