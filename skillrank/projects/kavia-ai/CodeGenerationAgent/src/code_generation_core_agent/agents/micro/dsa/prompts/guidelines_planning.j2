{# Planning Common Guidelines for Setup Plan Generation and Review
   ==============================================================
   
   This template contains planning-specific guidelines that should be referenced by both
   setup plan generation and review templates to ensure consistency.
#}

{% include 'guidelines_common.j2' %}

**ENVIRONMENT PERSISTENCE GUIDELINES:**
- **GLOBAL ENVIRONMENT SETUP**: Environment persistence via `/etc/profile.d/*.sh` is REQUIRED and CORRECT for system tools and framework CLIs
- **PATH EXPORTS**: Global PATH exports for framework CLIs (e.g., RubyGems bin, npm global bin) are REQUIRED and CORRECT for development environments
- **FRAMEWORK ENVIRONMENT VARIABLES**: Global framework environment variables (e.g., RAILS_ENV, NODE_ENV) set via `/etc/profile.d/*.sh` are ACCEPTABLE and RECOMMENDED for development environments
- **TOOL AVAILABILITY**: All installed tools and framework CLIs must be available in future shell sessions through proper PATH and environment variable persistence
- **NO INLINE OVERRIDES**: Once tools are properly persisted globally, do not recommend inline PATH exports or environment variable setting in individual steps

**PLANNING-SPECIFIC SCRIPT REQUIREMENTS:**
- **IDEMPOTENCY**: Check if tools/configs already exist and skip re-installation to avoid redundant work
- **VALIDATION**: After install, verify tool versions are accessible on PATH

**STEP CATEGORY DEFINITIONS:**
- **environment**: Setup ALL development tools and runtime environment in one comprehensive step
- **scaffolding**: Create project structure AND initialize framework in single step (MUST use workspace path from Container Information)
- **dependencies**: Install ALL packages, dependencies, AND configuration in one step
- **testing**: Setup testing framework, create dummy tests, and run tests to verify testing works
- **validation**: Build and run the application to verify the complete development environment works
- **custom**: Any other step that does not fit into the other categories and is required by the framework or application requirements

**VALIDATION STEP REQUIREMENTS:**
- **BUILD**: Build the application to verify environment is properly configured
- **START**: Run/start the application to verify it works correctly
- **STOP**: Stop/terminate the application cleanly after validation
- **TEST**: Test basic functionality (server responds, app launches, etc.)
- **EVIDENCE**: Provide clear evidence that the development environment is fully functional
