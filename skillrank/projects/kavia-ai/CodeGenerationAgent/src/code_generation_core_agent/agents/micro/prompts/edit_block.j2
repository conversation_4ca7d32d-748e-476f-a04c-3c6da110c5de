 {# Edit tool usage #}
{%  block edits %}

**Edit Block Generation Guidelines**

Use incremental edits on a file via `ContainerFileTools_edit_file` (if available) or an edit block in the output. Follow these rules precisely:

1. **Block Format**
   ```
   <file-path>
   <<<<<<< SEARCH
   <exact matching lines from the file>
   =======
   <replacement lines>
   >>>>>>> REPLACE
   ```
   - The first line is the file path alone, with no formatting or escaping.
   - Each block has exactly one `<<<<<<< SEARCH`, one `=======`, and one `>>>>>>> REPLACE`.

2. **Exact Matching and Replacement**
   - The **SEARCH** section must match the existing content **character for character**, including comments, docstrings, and any container markup (JSON, XML, quotes, etc.).
   - Replacement applies to **all** matching occurrences. Include enough context lines to ensure uniqueness.

3. **Block Size and Organization**
   - Keep each block focused on a small, self-contained change. Avoid long runs of unchanged lines.
   - If multiple related edits could cascade, combine them into a single block rather than chaining dependent blocks.
   - To move code within a file, use two separate blocks: one to delete the code at its original location, and one to insert it at the new location.

4. **Quality Checks Before Submission**
   - Ensure no code is duplicated within a block.
   - Confirm the **REPLACE** section does not simply repeat the entire **SEARCH** section with additions.
   - Verify each block follows the exact format and contains only the changing lines plus minimal context.

5. **Examples**
   **Import `math` and remove custom `factorial`:**
   ```
   mathweb/flask/app.py
   <<<<<<< SEARCH
   from flask import Flask
   =======
   import math
   from flask import Flask
   >>>>>>> REPLACE
   ```

   ```
   mathweb/flask/app.py
   <<<<<<< SEARCH
   def factorial(n):
       "compute factorial"

       if n == 0:
           return 1
       else:
           return n * factorial(n-1)
   =======
   >>>>>>> REPLACE
   ```

   ```
   mathweb/flask/app.py
   <<<<<<< SEARCH
       return str(factorial(n))
   =======
       return str(math.factorial(n))
   >>>>>>> REPLACE
   ```

**CRITICAL:**
- Never introduce duplicate code in a SEARCH/REPLACE block.
- Only generate blocks for files you fully understand.
- Avoid special characters (e.g., °C, °F) unless they appear literally in the file.

{%  endblock %}
