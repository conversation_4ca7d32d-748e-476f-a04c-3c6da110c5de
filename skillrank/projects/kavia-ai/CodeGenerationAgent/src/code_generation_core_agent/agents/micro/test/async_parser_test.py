# test_async_fence_parser.py
import asyncio
import uuid
import os
import sys
import pytest
from _pytest.config import Config

from code_generation_core_agent.agents.micro.generic_file_writer_agent import AsyncFenceParser


# ────────────────────────── GLOBAL CHUNK SIZE ─────────────────────
#
# Every test feeds the parser in slices of this many bytes.
# • 0   → feed the whole payload in one go
# • 1   → byte-by-byte
# • N   → N-byte chunks
#
CHUNK_SIZE: int = int(os.getenv("ASYNC_PARSER_CHUNK_SIZE", "0"))


# ── PYTEST HOOK ──────────────────────────────────────────────────
def pytest_addoption(parser):
    parser.addoption(
        "--chunk",
        action="store",
        type=int,
        default=None,          # ← None means “not provided”
        help="Bytes per chunk when streaming to AsyncFenceParser",
    )

def pytest_configure(config: Config):
    global CHUNK_SIZE
    opt = config.getoption("--chunk")
    CHUNK_SIZE = opt if opt is not None else 0      # ← fallback to 0


# ────────────────────────── FIXTURES & HELPERS ────────────────────
@pytest.fixture
def stubbed_uuid(monkeypatch):
    """Deterministic uuid.uuid4(), so we can assert stream-ids."""
    monkeypatch.setattr(
        uuid,
        "uuid4",
        lambda: uuid.UUID("12345678-1234-5678-1234-************")
    )


def make_parser(recorder, block_result=None):
    """
    Build an AsyncFenceParser with side-effects captured in *recorder*.
    """
    async def _handle_block(op, path, content, expl):
        recorder.append((
            "handle_block",
            dict(op=op, path=path, content=content, expl=expl)
        ))
        return block_result or [f"{op}:{path}:{content}"]

    return AsyncFenceParser(
        send_update=lambda sid, chunk, meta: recorder.append(
            ("send_update", sid, chunk, meta)
        ),
        handle_block=_handle_block,
        agent=None,
    )


async def feed_by_chunks(parser, text: str):
    outs = []
    if CHUNK_SIZE <= 0:
        outs.extend(await parser.feed(text))
    else:
        for i in range(0, len(text), CHUNK_SIZE):
            outs.extend(await parser.feed(text[i : i + CHUNK_SIZE]))
    # NEW: drain any trailing partial line
    outs.extend(await parser.flush())
    return outs


# ────────────────────────── THE TESTS ─────────────────────────────
@pytest.mark.asyncio
async def test_single_line_noop_is_ignored(stubbed_uuid):
    calls = []
    parser = make_parser(calls, block_result=["deleted foo"])
    out = await feed_by_chunks(parser, "````noop````\n")
    assert out == [] and calls == []


@pytest.mark.asyncio
async def test_single_line_delete_with_explanation(stubbed_uuid):
    calls = []
    parser = make_parser(calls, block_result=["deleted foo"])

    # feed the two logical lines separately so the first one is
    # definitely flushed from the buffer before we hit the delete line
    await feed_by_chunks(parser, "Explanation: remove unused helper\n")
    user_out = await feed_by_chunks(parser, "````delete file=\"foo.py\"````\n")

    # the parser must emit whatever handle_block returned
    assert user_out == ["deleted foo"]

    # there must be exactly a start and an end update
    su = [c for c in calls if c[0] == "send_update"]
    assert [s[3]["type"] for s in su] == ["start", "end"]
    assert su[0][3]["file_path"] == "foo.py"
    assert su[0][3]["operation"] == "delete"


@pytest.mark.asyncio
async def test_write_block_lifecycle(stubbed_uuid):
    calls = []
    parser = make_parser(calls, block_result=["wrote bar"])
    body = (
        "````write file=\"bar.txt\"\n"
        "first\n"
        "second\n"
        "````\n"
    )
    user_out = await feed_by_chunks(parser, body)

    # The user receives whatever the handler returned
    assert user_out == ["wrote bar"]

    # Collect the streamed file-body lines from send_update events
    streamed_lines = [
        ev[2]                       # the chunk
        for ev in calls
        if ev[0] == "send_update" and ev[3]["type"] == "content"
    ]
    assert "".join(streamed_lines) == "first\nsecond\n"
    assert user_out == ["wrote bar"]

    kinds = [ev[3]["type"] for ev in calls if ev[0] == "send_update"]
    assert kinds == ["start", "content", "content", "end"]

@pytest.mark.asyncio
async def test_multiline_noop_is_swallowed(stubbed_uuid):
    calls = []
    parser = make_parser(calls)
    chunk = (
        "hi\n"
        "````noop\n"
        "ignored\n"
        "````\n"
        "bye\n"
        "\n"
    )
    out = await feed_by_chunks(parser, chunk)

    # Only the text outside the noop fence survives
    #assert out == ["hi\n", "bye\n"]
    assert out == ["hi\n", "ignored\n", "````\n", "bye\n", "\n"]
    # No start/content/end updates for noop blocks
    assert all(c[0] != "send_update" for c in calls)

@pytest.mark.asyncio
async def test_explanation_line_is_passed_to_handler(stubbed_uuid):
    calls = []
    parser = make_parser(calls)

    payload = (
        "Explanation: write a greeting file\n"
        "````write file=\"hello.txt\"\n"
        "Hello world\n"
        "````\n"
    )
    out = await feed_by_chunks(parser, payload)

    hb = next(c for c in calls if c[0] == "handle_block")[1]
    assert hb["expl"] == "write a greeting file"

@pytest.mark.asyncio
async def test_edit_block_roundtrip(stubbed_uuid):
    calls = []
    parser = make_parser(calls, block_result=["patched"])
    body = (
        "Explanation: fix typo\n"
        "````edit file=\"foo.py\"\n"
        "<<<<<<< SEARCH\n"
        "pritn('oops')\n"
        "=======\n"
        "print('oops')\n"
        ">>>>>>> REPLACE\n"
        "````\n"
    )
    user_out = await feed_by_chunks(parser, body)

    hb = next(c for c in calls if c[0] == "handle_block")[1]
    assert hb["op"] == "edit"
    assert hb["path"] == "foo.py"
    assert "print('oops')" in hb["content"]
    assert user_out == ["patched"]

# ────────────────────────── MAIN SHIM ────────────────────────────
def main() -> None:
    """
    Run the test file directly:
        python test_async_fence_parser.py          → whole payloads
        python test_async_fence_parser.py --chunk 8 → 8-byte chunks
    The --chunk flag is forwarded to pytest **only** when the user
    supplied it, avoiding “unrecognised arguments” errors.
    """
    import argparse, os, sys, pytest, importlib

    ap = argparse.ArgumentParser()
    ap.add_argument(
        "-c", "--chunk",
        type=int,
        help="Bytes per chunk when streaming to AsyncFenceParser"
    )
    args, remaining = ap.parse_known_args()

    # Default to 0 (whole payloads) if the user didn’t pass --chunk
    chunk_val = 0 if args.chunk is None else args.chunk
    os.environ["ASYNC_PARSER_CHUNK_SIZE"] = str(chunk_val)

    # Forward the flag to pytest ONLY if the user actually set it
    chunk_args = ["--chunk", str(chunk_val)] if args.chunk is not None else []

    # Run pytest, treating THIS module as a plugin so hooks are visible
    plugin_module = importlib.import_module(__name__)
    sys.exit(
        pytest.main(
            remaining + [__file__] + chunk_args,
            plugins=[plugin_module]
        )
    )


if __name__ == "__main__":
    main()