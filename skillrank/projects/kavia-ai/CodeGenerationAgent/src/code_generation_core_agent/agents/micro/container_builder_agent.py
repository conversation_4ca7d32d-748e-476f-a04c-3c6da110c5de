import json
import subprocess
from typing import Dict, List

from code_generation_core_agent.agents.micro.agent_system import AgentRegistry, GenericMicroAgent
from code_generation_core_agent.agents.tools.tracking.shell_command_call_tracker import ShellCommandCallTracker
from code_generation_core_agent.agents.utilities import get_container_name
from code_generation_core_agent.config import config


@AgentRegistry.register("ContainerBuilderAgent")
class ContainerBuilderAgent(GenericMicroAgent):

    def function_executor_internal(self, function_name, function_args):
        result = self.agent_tools.function_executor(function_name, function_args)
        return result

    def get_executor(self):
        validator = self.agent_tools.function_validator
        executor = self.agent_tools.function_executor
        return executor, validator
    def _get_function_call_history(self) -> List[Dict]:
        call_tracker = ShellCommandCallTracker(self.base_path)
        return call_tracker.asJson()

    def _get_container_info(self) -> str:
        container_name = get_container_name()
        # Use double curly braces to escape them in Python string formatting
        cmd = f"docker history --no-trunc {container_name} --format '{{{{.CreatedBy}}}}'"

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"Failed to get container history: {result.stderr}")

        return '\n'.join(line.strip() for line in result.stdout.strip().split('\n') if line.strip())


    async def process_request(self, action, subtask = None, work_item = None, request_details = None,
                              tool_call_log = None, capabilities: dict = None):

        container_info = self._get_container_info()
        call_history = self._get_function_call_history()

        context = {
            'action': action,
            'work_item': work_item,
            'request_details': request_details,
            'agent_list': capabilities.keys(),
            'agent_capabilities': capabilities,
            'number_of_allowed_calls': 25,
            'subtask': subtask,
            'base_path': self.base_path,
            'container_info': container_info,
            'call_history': call_history,
        }
        system_prompt = self.render_prompt('system', context)
        user_prompt = self.render_prompt('user', context)
        executor, validator = self.get_executor()

        completion = await self.llm.llm_interaction_wrapper(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            user_prompt=None,
            system_prompt=None,
            model=self.model_name,
            response_format={"type": "json_object"},
            function_schemas=self.agent_tools.get_function_schemas(['KnowledgeTools',
                                                                    'ContainerFileTools',
                                                                    'VisibleShellTools']),
            function_executor=executor,
            function_validator=validator
        )

        raw_content = completion.choices[0].message.content.strip()
        result = json.loads(raw_content)

        #docker_file = result["dockerfile"]["content"]
        #docker_file_path = os.path.join(self.base_path, ".Dockerfile")

        #with open(docker_file_path, "w") as f:
        #    f.write(docker_file)




