container:
  container_name: kavia_default_container_image

task_agents:
  CodeGeneration:
    micro_agents:
      - InitialSetupAgent
      - DynamicSetupAgent
      - PlanningAgent
      - CodeWritingAgent
      - TestCodeWritingAgent
      - TestExecutionAgent
      - BugFixingAndVerificationAgent
      - QuestionAnsweringAgent
      - DocumentationAgent
      - GeneralistAgent
      - HelpAgent
      - DesignExtractionAgent
      - SupabaseConfigurationAgent
      - FigmaExtractionAgent
      - WebsiteExtractionAgent


  CodeMaintenance:
    micro_agents:
      - InitialSetupAgent
      - DynamicSetupAgent
      - PlanningAgent
      - CodeWritingAgent
      - TestCodeWritingAgent
      - TestExecutionAgent
      - VisualVerificationAgent
      - ContainerBuilderAgent
      - BugFixingAndVerificationAgent
      - QuestionAnsweringAgent
      - DocumentationAgent
      - GeneralistAgent
      - HelpAgent
      - DesignExtractionAgent
      - SupabaseConfigurationAgent
      - FigmaExtractionAgent
      - WebsiteExtractionAgent
      #- ManifestGenerationAgent

  DocumentCreation:
    micro_agents:
      - DocumentationAgent
      - QuestionAnsweringAgent
      - GeneralistAgent
      - PlanningAgent
      - HelpAgent
      - ManifestGenerationAgent
      - FigmaExtractionAgent
      - WebsiteExtractionAgent

micro_agents:
  GeneralistAgent:
    capabilities:
      - Take activities that do not fit into any other category, like web searching for information, data processing/converting, or performing general tasks
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - WebBrowser
      - KnowledgeTools
      - SearchAndDownloadTool
    prompt_file: generalist_agent_prompt.j2
    class: GeneralistMicroAgent

  ContainerBuilderAgent:
    capabilities:
      - Build a container image for the provided work item
    tools:
      - WorkInputDiscovery
    prompt_file: container_builder_agent.j2
    class: ContainerBuilderAgent

  DesignExtractionAgent:
    capabilities:
      - Extract design elements from an image or screenshot to be used as assets for the application
    tools:
      - ContainerFileTools
    prompt_file: design_extraction_agent.j2
    class: DesignExtractionMicroAgent

  WebsiteExtractionAgent:
    capabilities:
      - Extract design elements from a website to be used as assets for the application
    tools:
      - ContainerFileTools
      - SearchAndDownloadTool
    prompt_file: website_extraction_agent.j2
    class: WebsiteExtractionMicroAgent


  
  InitialSetupAgent:
    capabilities:
      - Perform initial setup of development environment
    tools:
      - InitialSetupTools
      - DependencyTools
    prompt_file: initial_setup_agent.j2
    class: InitialSetupMicroAgent
    setup_steps:
      init-env: prompts/platform/common/init-env.j2
      init-minimal: prompts/platform/common/init-minimal.j2
      init-files: prompts/platform/common/init-files.j2
      init-style: prompts/platform/common/init-style.j2
      init-run-tool: prompts/platform/common/init-run-tool.j2
      init-test-tool: prompts/platform/common/init-test-tool.j2    

  PlanningAgent:
    capabilities:
      - Analyze what source code already exists and come up with a strategy and plan for handling various tasks and subtasks
    tools:
      - KnowledgeTools
      - ContainerFileTools
      - StrategicAnalysisTool

    prompt_file: planning_agent_prompt.j2

  HelpAgent:
    capabilities:
      - Provide general information about the Kavia Code Generation Agent and provide help with using the agent
    tools:
      - SearchAndDownloadTool

    prompt_file: help_agent_prompt.j2

  CodeWritingAgent:
    capabilities:
      - Write or update code on the repository based on the provided direction and using the provided strategy
      - refactor_code to improve code quality or to address issues
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - KnowledgeTools
      - ProjDefnTools
      - EnvironmentSummary
      - StrategicAnalysisTool
      - DependencyTools
      - SearchAndDownloadTool

    prompt_file: code_writing_agent_prompt.j2
    class: CodeWritingMicroAgent  # Only if this exists
  
  SupabaseConfigurationAgent:
    capabilities:
      - Configure or update Supabase settings for the project
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - KnowledgeTools
      - SearchAndDownloadTool
      - SupabaseTool


    prompt_file: supabase_configuration_agent_prompt.j2
    class: SupabaseConfigurationMicroAgent  # Only if this exists

  TestCodeWritingAgent:
    capabilities:
      - Write code for unit tests on to the repository
      - write code for integration tests on to the repository
    tools:
      - ContainerFileTools
      - StrategicAnalysisTool
      - VisibleShellTools
      - KnowledgeTools
      - EnvironmentSummary
      - SearchAndDownloadTool
    prompt_file: test_code_writing_agent_prompt.j2
    class: TestCodeWritingMicroAgent  # Only if this exists
  
  TestExecutionAgent:
    capabilities:
      - Execute test cases for the component
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - WebBrowser
      - ReviewStatusTools
      - KnowledgeTools
      - EnvironmentSummary
      - SearchAndDownloadTool
    prompt_file: test_execution_agent_prompt.j2
  VisualVerificationAgent:
    capabilities:
      - Visually verify the application or website.
    tools:
      - VisibleShellTools
      - WebBrowser
      - KnowledgeTools
      - ContainerFileTools
      - SearchAndDownloadTool
    prompt_file: visual_verification_agent_prompt.j2
    class: VisualVerificationMicroAgent
  BugFixingAndVerificationAgent:
    capabilities:
      - Analyze and fix bugs in the code and verify the fix
    tools:
      - ContainerFileTools
      - VisibleShellTools
      - WebBrowser
      - KnowledgeTools
      - EnvironmentSummary
      - StrategicAnalysisTool
      - SearchAndDownloadTool
    prompt_file: bugfixing_and_verification_agent_prompt.j2
    class: BugFixingAndVerificationMicroAgent
  QuestionAnsweringAgent:
    capabilities:
      - Answer questions related to the codebase, features, or architecture, or provide information about the codebase
    tools:
      - KnowledgeTools
      - ProjDefnTools
      - ContainerFileTools
      - VisibleShellTools
      - SearchAndDownloadTool
    prompt_file: question_answering_agent_prompt.j2
    class: QuestionAnsweringMicroAgent
  DocumentationAgent:
    capabilities:
      - Generate documentation about architectural requirements, product requirements, test coverage, code quality, security, and compliance
      - Generate documentation about any aspects of the codebase, perform detailed analysis to provide insights, and create documentation files
      - Generate any user requested documentation
    tools:
      - DocumentManager
      - ContainerFileTools
      - KnowledgeTools
      - ProjDefnTools
      - SearchAndDownloadTool
    prompt_file: documentation_agent_prompt.j2
    class: DocumentationMicroAgent
  ManifestGenerationAgent:
    capabilities:
      - Create a Manifest file for the project
    tools:
      - ContainerFileTools
      - KnowledgeTools
      - SearchAndDownloadTool
    prompt_file: manifest_generation_agent_prompt.j2
    class: ManifestGenerationMicroAgent
  FigmaExtractionAgent:
    capabilities:
      - Extract design elements from a Figma file to be used as assets for the application and create html/css/js files that represent the design.
      - Review previously created html/css/js files by comparing those with the corresponding figma files and update them based on user feedback.
    tools:
      - ContainerFileTools
      - KnowledgeTools
      - SearchAndDownloadTool
    prompt_file: figma_extraction_agent.j2
    class: FigmaExtractionMicroAgent

  DynamicSetupAgent:
    capabilities:
      - Analyze and setup any container development environment dynamically
      - Support for frontend, backend, database, and cache containers
      - LLM-powered framework inference for containers
      - Intelligent error recovery and validation
      - Container-specific setup planning and execution
    tools:
      - ContainerFileTools
      - ContainerWriteTools
      - VisibleShellTools
      - DependencyTools
      - KnowledgeTools
    prompt_file: dynamic_setup_agent.j2
    class: DynamicSetupMicroAgent


file_lister:
  # Default configuration that works across platforms
  exclude_dirs:
    - ".git"
    - "__pycache__"
    - ".pytest_cache"
    - ".venv"
    - "venv"
    - "env"
    - "build"
    - "dist"
    - "public"
    - "node_modules"
    - "target"
    - "build"
    - "dist"
    - "logs"
  exclude_files:
    - "files.yaml"
    - ".DS_Store"
    - ".gitignore"
    - "*.pyc"
    - "package-lock.json"
    - "stdout.txt"
  exclude_extensions:
    - ".log"
    - ".pyc"
    - ".pyo"
    - ".pyd"
    - ".so"
    - ".dll"
    - ".dylib"
  include_files:
    - "README.md"
    - "setup.py"
    - "App.js"


