# question_answering_micro_agent.py
from code_generation_core_agent.agents.micro.agent_system import GenericMicroAgent, AgentRegistry
import uuid
import json
from code_generation_core_agent.agents.micro.agent_system import AgentRegistry
@AgentRegistry.register("QuestionAnsweringAgent")
class QuestionAnsweringMicroAgent(GenericMicroAgent):
    """Micro agent for handling code writing and modification tasks"""

    
    async def process_request_streaming(self, action, subtask=None,
                                      work_item=None, request_details=None,
                                      tool_call_log=None,
                                      capabilities: dict = None, container_name=None):
        """
        Process a code writing request using the streaming LLM capabilities.
        This version processes JSONL format and executes operations as they arrive.
        """

        self.set_session_reuse(True)

        accumulated_response = ""

        async for chunk in super().process_request_streaming(
        action, subtask, work_item, request_details,
        tool_call_log, capabilities, container_name=container_name
        ):
            if chunk and isinstance(chunk, str):
                accumulated_response += chunk
                
            yield chunk

        stream_id = f"document_{uuid.uuid4().hex[:8]}"
        self.reporter.send_code_panel_update(
            stream_id,
            accumulated_response,
            {
                "type": "document_update",
                "file_path": None,
                "operation": None
            }
        )
        