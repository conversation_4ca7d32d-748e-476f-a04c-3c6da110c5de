from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional


@dataclass
class Script:
    name: str  # install, scaffold, test, build, start, validation
    code: str  # The actual script content
    
    def generate_diff(self, other: 'Script') -> str:
        """Generate diff between this script and another (for future use)."""
        # Implementation for future use
        pass
    
    def to_dict(self):
        """Convert to dictionary for serialization."""
        return {
            'name': self.name,
            'code': self.code,
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Script':
        """Create Script from dictionary."""
        return cls(
            name=data['name'],
            code=data['code'],
        )
