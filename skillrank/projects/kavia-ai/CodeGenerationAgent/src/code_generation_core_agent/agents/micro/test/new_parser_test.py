import random
import re
from typing import Iterable, Generator, Optional

# --- File Operation Handlers ---

def send_code_panel_update(op_type: str, file_path: str, content_chunk: str) -> None:
    """
    Stream a piece of the edit/write block as soon as it's received.
    op_type: "edit" or "write"
    file_path: path of the file being operated on
    content_chunk: a line (with trailing newline) from the block
    """
    # Replace this stub with your actual streaming handler
    print(f"[STREAM {op_type.upper()}] {file_path}: {content_chunk.rstrip()}")

def process_file_operation(op_type: str, file_path: str, full_content: str, explanation: Optional[str]) -> None:
    """
    Called once the entire block is received.
    op_type: "edit" or "write"
    file_path: path of the file
    full_content: the entire diff or file contents
    explanation: the text of the preceding Explanation: line, or None
    """
    # Replace this stub with your actual final-processing logic
    print(f"\n[PROCESS {op_type.upper()}] {file_path} (Explanation: {explanation})")
    print(full_content)

# --- Streaming Parser ---

def parse_llm_stream(chunks: Iterable[str]) -> Generator[str, None, None]:
    """
    Parse incoming LLM output chunks, separating user text from edit/write blocks.

    Yields:
      - str: user‑facing text lines (outside of edit/write blocks)
    Side‑effects:
      - send_file_operation(...) for every line in a block
      - process_file_operation(...) once per complete block
    """
    state = "TEXT"
    block_type = None
    file_path = None
    block_lines = []
    last_explanation = None

    buffer = ""
    fence_re = re.compile(r'^```(edit|write)\s+file="([^"]+)"\s*$')
    expl_re = re.compile(r'^Explanation:\s*(.*)$')

    for chunk in chunks:
        buffer += chunk
        parts = buffer.split("\n")
        if buffer.endswith("\n"):
            lines, buffer = parts, ""
        else:
            lines, buffer = parts[:-1], parts[-1]

        for line in lines:
            if state == "TEXT":
                # Capture Explanation: lines
                m_expl = expl_re.match(line)
                if m_expl:
                    last_explanation = m_expl.group(1).strip()
                    continue

                # Detect start of block
                m_fence = fence_re.match(line)
                if m_fence:
                    state = "BLOCK"
                    block_type = m_fence.group(1)
                    file_path = m_fence.group(2)
                    block_lines = []
                else:
                    # Plain text → yield to caller
                    yield line + "\n"

            elif state == "BLOCK":
                if line.strip() == "```":
                    # End of block → process
                    full = "".join(block_lines)
                    process_file_operation(block_type, file_path, full, last_explanation)
                    state = "TEXT"
                    block_type = None
                    file_path = None
                    block_lines = []
                    last_explanation = None
                else:
                    # Stream chunk immediately and store for full processing
                    send_code_panel_update(block_type, file_path, line + "\n")
                    block_lines.append(line + "\n")

    # Flush any trailing text
    if state == "TEXT" and buffer:
        yield buffer

# --- Test Program ---

# Static LLM output containing text and two file operations
test_input = (
    "Hello user!\n"
    "Explanation: Update greet function\n"
    "```edit file=\"utils/greet.py\"\n"
    "<<<<<<< SEARCH\n"
    "def greet(name):\n"
    "    print(\"Hi\", name)\n"
    "=======\n"
    "def greet(name: str) -> None:\n"
    "    print(f\"Hello, {name}!\")\n"
    ">>>>>>> REPLACE\n"
    "```\n"
    "Thank you!\n"
    "Explanation: New main script\n"
    "```write file=\"main.py\"\n"
    "import utils.greet\n"
    "\n"
    "def main():\n"
    "    utils.greet.greet(\"World\")\n"
    "\n"
    "if __name__ == \"__main__\":\n"
    "    main()\n"
    "```\n"
    "Goodbye.\n"
)

def random_chunks(s: str, max_chunk: int = 10):
    """Yield the string in random-sized chunks to simulate streaming."""
    i = 0
    while i < len(s):
        size = random.randint(1, max_chunk)
        yield s[i:i + size]
        i += size

def test_parser():
    """Run the parser on the random-chunked test input and validate output."""
    user_texts = []
    for txt in parse_llm_stream(random_chunks(test_input)):
        if txt.strip():
            user_texts.append(txt)

    # Expected plain-text segments
    expected_texts = ["Hello user!\n", "Thank you!\n", "Goodbye.\n"]
    assert user_texts == expected_texts, f"Text mismatch: {user_texts} vs {expected_texts}"

    print("\nAll tests passed!")

if __name__ == "__main__":
    test_parser()