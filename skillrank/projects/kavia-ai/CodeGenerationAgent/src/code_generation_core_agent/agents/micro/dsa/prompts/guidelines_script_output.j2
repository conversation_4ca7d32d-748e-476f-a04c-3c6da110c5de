{# Script Output Format Guidelines for Setup Script Generation and Refinement
   ====================================================================
   
   This template contains the output format requirements that should be referenced by both
   setup script generation and refinement templates to ensure consistency.
#}

**OUTPUT FORMAT:**
Return ONLY the bash script as a single fenced code block. No JSON, no prose, no explanations.

```bash
#!/usr/bin/env bash
set -euo pipefail

# Detect privilege level
if [ "$(id -u)" -eq 0 ]; then
    SUDO=""
else
    SUDO="sudo"
fi

# Set workspace path
WORKSPACE="{{ container_info.workspace_path }}"
cd "$WORKSPACE"

# === COMMAND: INSTALL ===
# Install system tools and development utilities
# [Your install commands here]

# === COMMAND: SCAFFOLD ===
# Create project structure and initialize framework
# [Your scaffold commands here]

# === COMMAND: DEPS ===
# Install project dependencies
# [Your dependency installation commands here]

# === COMMAND: BUILD ===
# Build the application
# [Your build commands here]

# === COMMAND: TEST ===
# Run tests
# [Your test commands here]

# === COMMAND: START ===
# Start the application (canonical launch command)
# [Your start commands here - clean launch only, no verification]

# === COMMAND: VALIDATE ===
# Comprehensive validation (verify app is running + runtime health checks)
# [Your validation commands here - do NOT duplicate BUILD or TEST steps]

# === COMMAND: STOP ===
# Stop the application
# [Your stop commands here]
```
