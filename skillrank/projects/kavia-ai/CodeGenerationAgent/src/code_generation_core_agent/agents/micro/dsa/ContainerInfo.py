from dataclasses import dataclass, field
from typing import Any, Dict, List

# Default Dockerfile summary for the current container environment
DEFAULT_DOCKERFILE_SUMMARY = "OS: Ubuntu 24.04 (Debian family), Package Manager: apt-get, Sudo: Present (NOPASSWD), Preinstalled: git, curl, wget, python3, python3-pip, nodejs, npm, build-essential, postgresql, mysql-server, mongodb-org, redis-server, dotnet-sdk-8.0, uvicorn, celery, redis, requests, beautifulsoup4, sphinx, mkdocs, pylint, flask, awscli, boto3, yarn, typescript, @vue/cli, @angular/cli, create-react-app, express-generator, nodemon, pm2, eslint, prettier, webpack, jest"


@dataclass
class ContainerInfo:
    """Data class for container information"""
    container_name: str
    container_type: str = "unknown"
    framework: str = "unknown"
    platform: str = "unknown"
    description: str = ""
    workspace: str = ""

    #container_details: Dict[str, Any] = field(default_factory=dict)
    reasoning: str = ""
    alternative_frameworks: List[str] = field(default_factory=list)
    requirements: List[str] = field(default_factory=list)
    dockerfile_summary: str = DEFAULT_DOCKERFILE_SUMMARY

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ContainerInfo':
        """Create ContainerInfo from dictionary"""
        return cls(
            container_name=data.get('container_name', 'unknown'),
            container_type=data.get('container_type', 'unknown'),
            framework=data.get('framework', 'unknown'),
            platform=data.get('platform', 'unknown'),
            description=data.get('description', ''),
            workspace=data.get('workspace', ''),

            reasoning=data.get('reasoning', ''),
            alternative_frameworks=data.get('alternative_frameworks', []),
            requirements=data.get('requirements', []),
            dockerfile_summary=data.get('dockerfile_summary', DEFAULT_DOCKERFILE_SUMMARY)
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert ContainerInfo to dictionary"""
        return {
            'container_name': self.container_name,
            'container_type': self.container_type,
            'framework': self.framework,
            'platform': self.platform,
            'description': self.description,
            'workspace': self.workspace,

            'reasoning': self.reasoning,
            'alternative_frameworks': self.alternative_frameworks,
            'requirements': self.requirements,
            'dockerfile_summary': self.dockerfile_summary
        }

    def update_detected_info(self, framework: str, platform: str) -> None:
        """Update the container info with detected framework and platform values"""
        self.framework = framework
        self.platform = platform

    def update_framework_analysis(self, framework_analysis: Dict[str, Any]) -> None:
        """Update the container info with complete framework analysis results"""
        self.framework = framework_analysis.get('detected_framework', self.framework)
        self.platform = framework_analysis.get('platform_type', self.platform)

        self.reasoning = framework_analysis.get('reasoning', '')
        self.alternative_frameworks = framework_analysis.get('alternative_frameworks', [])
        self.requirements = framework_analysis.get('requirements', [])

    def has_detected_info(self) -> bool:
        """Check if the container info has been updated with detected framework and platform values"""
        return self.framework != "unknown" and self.platform != "unknown"

    def get_formatted_info(self, include_requirements: bool = True) -> str:
        """
        Returns a formatted string containing all container information for use in prompts.
        This method centralizes the formatting logic used across different agents.
        """
        requirements_text = self.get_requirements_text()
        
        return f"""
Container Name: {self.container_name}
Container Type: {self.container_type}
Platform: {self.platform}
Framework: {self.framework}
Workspace: {self.workspace}

{requirements_text if include_requirements else ""}
"""

    def get_requirements_text(self) -> str:
        """
        Returns a plain text formatted output of the requirements list.
        Each requirement is on a new line with a bullet point.
        """
        if not self.requirements:
            return "No requirements specified"
        
        formatted_requirements = []
        for i, requirement in enumerate(self.requirements, 1):
            formatted_requirements.append(f"{i}. {requirement}")
        
        return f"Requirements:\n" + "\n".join(formatted_requirements)



    @property
    def requirements_text(self) -> str:
        """
        Property that returns a plain text formatted output of the requirements list.
        Convenient access to formatted requirements.
        """
        return self.get_requirements_text()