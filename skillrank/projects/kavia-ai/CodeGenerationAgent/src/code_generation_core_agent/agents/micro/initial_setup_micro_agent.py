import os
import json

import jinja2
from code_generation_core_agent.agents.micro.agent_system import AgentReg<PERSON>ry, GenericMicroAgent
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType

@AgentRegistry.register("InitialSetupAgent")
class InitialSetupAgent(GenericMicroAgent):

    ACTION_MESSAGES = {
        'init-env': "Establishing and satisfying dependencies...",
        'init-minimal': "Creating basic application scaffolding...",
        'init-files': "Reviewing setup...",
        'init-style': "Adjusting setup...",
        'init-run-tool': "Checking runtime...",
        'init-test-tool': "Checking test infrastructure...",
    }

    def get_action_count(self):
        return len(self.ACTION_MESSAGES)

    def _generate_folder_structure(self,base_folder):
        def folder_to_dict(folder_path):
            # Get list of items in the folder
            items = self._list_directory(folder_path) #os.listdir(folder_path)
            
            # If folder has more than 20 items, return just the folder name
            if len(items) > 20:
                return os.path.basename(folder_path)
            
            # Otherwise, build a dictionary representing the folder contents
            folder_dict = {}
            for item in items:
                item_path = os.path.join(folder_path, item)
                nest = self._list_directory(item_path+"/")
                if len(nest) > 0:
                    # Recursively process sub-folders
                    folder_dict[os.path.join(folder_path,item)] = folder_to_dict(item_path)
                else:
                    # Add file names as dictionary keys with None as their value
                    folder_dict[os.path.join(folder_path,item)] = None
            return folder_dict
        
        # Generate the folder structure dictionary
        folder_structure = folder_to_dict(base_folder)
        
        # Convert the dictionary to a JSON object
        folder_structure_json = json.dumps(folder_structure, indent=4)
        
        return folder_structure_json

    def _ensure_test_setup(self, filename):
        import json
        try:
            contents = self._read_file(filename)
            if len(contents) > 0:
                data = json.loads(contents)
                if data['command'] and data['working_directory']:
                    if "npm test" in data['command']:
                        self._make_directory(data['working_directory']+'/src/__tests__')
        except Exception as e:
            print(f"Error: parsing {filename} had exception {e}")
            pass

    def _get_work_item_details( self, work_item):
        work_item_details = ""
        if work_item:
            fields = []
            key_exclude = ['component_name','asset','figma','test']
            for key in work_item.keys():
                use_key = True
                for exclude in key_exclude:
                    if exclude in key.lower():
                        use_key = False
                        break
                if use_key:
                    try:
                        value = work_item.get(key)
                        if isinstance( value, dict):
                            value = self._get_work_item_details(value)
                        if value:
                            field_text = f"{key}: {value} "
                            fields.append(field_text)
                    except Exception:
                        self.logger.error(f"initial_setup: error including work_item field {key}")
            work_item_details = ", ".join(fields)
        return work_item_details

    def render_prompt(self, prompt_type, context):
        context['prompt_type'] = prompt_type
        if prompt_type == 'system':
            return "You are an expert software engineer."
        if prompt_type == 'user':
            current_dir = os.path.dirname(os.path.abspath(__file__))
            env = jinja2.Environment(
                loader=jinja2.FileSystemLoader(current_dir),
                trim_blocks=True,
                lstrip_blocks=True
            )
            
            action = context.get("action", "")
            if action in self.setup_steps:
                try:
                    template_path = self.setup_steps[action]
                    template = env.get_template(template_path)
                    work_item = context.get('work_item', {})
                    selected_platform = work_item.get('platform')
                    selected_framework = work_item.get('framework')
                    needs_folder_structure = action in ['init-files', 'init-run-tool', 'init-test-tool', 'init-style']
                    is_mobile_template = selected_platform == ApplicationType.MOBILE.value

                    template_context = {
                        'base_path': self.base_path,
                        'platform': selected_platform,
                        'framework': selected_framework,
                        'work_item': work_item,
                        'work_item_details': self._get_work_item_details(work_item),
                        'component_name': work_item.get('component_name', ''),
                        'application_type': (
                            selected_framework
                            if is_mobile_template
                            else None
                        ),
                        'folder_structure': (
                            self._generate_folder_structure(self.base_path) 
                            if needs_folder_structure 
                            else None
                        ),
                        'work_path': os.path.dirname(os.path.dirname(self.base_path)) if is_mobile_template else None,
                    }
                    prompt = template.render(**template_context).strip()
                    return prompt
                    
                except jinja2.exceptions.TemplateNotFound:
                    print(f"Template not found: {template_path}")
                    return ""
                except jinja2.exceptions.TemplateError as e:
                    # Full backtrace and template part that caused the error
                    from traceback import format_exc
                    print(f"Template error: {format_exc()}")
                    # Template path
                    print(f"Template path: {template_path}")
                    print(f"Template error: {str(e)}")
                    return ""
        
        return ""

    def _execute_cmd(self, cmd):
        from code_generation_core_agent.agents.tools.shared_loop import shared_loop
        from code_generation_core_agent.config import config
        timeout = config.getint("TOOLS", "shell_command_timeout")
        async def _one_shot_docker_command():
            stdout_data, returncode = await self.agent_tools.executor.create_subprocess_shell_one_shot(cmd)
            return stdout_data, returncode
        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command())
            output, returncode = future.result(timeout=timeout)
            return output, returncode
        except Exception as e:
            print(f"Error: cmd {cmd} had exception {e}")
        return None, None

    def _read_file(self, filename):
        contents = None
        cmd = f'cat {filename}'
        output, returncode = self._execute_cmd(cmd)
        if returncode == 0:
            contents = output
        return contents

    def _make_directory(self, directory):
        cmd = f'mkdir {directory}'
        self._execute_cmd(cmd)

    def _list_directory(self, directory):
        file_list = []
        cmd = f'ls -1 {directory}'
        output, returncode = self._execute_cmd(cmd)
        if returncode == 0:
            file_list = output.splitlines()
        return file_list

    def _check_file_exists(self, filename):
        exists = False
        cmd = f'ls -al {filename}'
        output, returncode = self._execute_cmd(cmd)
        if returncode == 0:
            exists = True
        return exists

    def get_action(self):
        for action in self.setup_steps:
            if not self._check_file_exists(self.base_path+f"/.{action}"):
                self.logger.info(f"initial_setup: get_action {action}")
                return action, self.ACTION_MESSAGES[action]
        return None, None

    @staticmethod
    def check_final(function_name, function_args, file, result):

        final = False
        if function_name == "VisibleShellTools_execute_immediate_return_command" or function_name == "VisibleShellTools_start_long_running_process":
            if function_args["command"].find(file) >= 0:
                if result["status"] == "SUCCESS":
                    final = True

        if function_name == "ContainerFileTools_create_file" or function_name == "ContainerFileTools_write_file":
            if function_args["file_path"].find(file) >= 0:
                if result["status"] == "SUCCESS":
                    final = True

        if function_name == "ContainerFileTools_edit_file":
            if function_args["changes"].find(file) >= 0:
                if result["status"] == "SUCCESS":
                    final = True

        if final:
            result["final_tool_call"]= final

    def function_executor_env(self, function_name, function_args):
        result= self.agent_tools.function_executor(function_name, function_args)
        self.check_final(function_name, function_args, ".init/.init-env", result)
        return result

    def function_executor_minimal(self, function_name, function_args):
        need_tsconfig = False
        app_folder = None
        if 'command' in function_args:
            command = function_args['command']
            if command.startswith('npx create-react-app'):
                if '--template typescript' in command:
                    need_tsconfig = True
                args = command.split()
                if len(args) > 2:
                    app_folder = os.path.join( self.base_path, args[2])
                    if self._check_file_exists(app_folder):
                        self.logger.info(f"intial_setup: removing existing folder {app_folder}")
                        self._execute_cmd(f'rm -rf {app_folder}')
        result= self.agent_tools.function_executor(function_name, function_args)
        if need_tsconfig and app_folder:
            tsconfig_filename = os.path.join( app_folder, 'tsconfig.json')
            if not self._check_file_exists(tsconfig_filename):
                self.logger.info(f"generating tsconfig.json file {tsconfig_filename}")
                self._execute_cmd( f'cd {app_folder} && npx tsc --init')
        self.check_final(function_name, function_args, ".init/.init-minimal", result)
        return result

    def function_executor_style(self, function_name, function_args):
        result= self.agent_tools.function_executor(function_name, function_args)
        self.check_final(function_name, function_args, ".init/.init-style", result)
        return result

    def function_executor_files(self, function_name, function_args):
        result= self.agent_tools.function_executor(function_name, function_args)
        self.check_final(function_name, function_args, ".init/.init-files", result)
        return result

    def function_executor_run_tool(self, function_name, function_args):
        result= self.agent_tools.function_executor(function_name, function_args)
        self.check_final(function_name, function_args, ".init/.init-run-tool", result)
        return result

    def function_executor_test_tool(self, function_name, function_args):
        result= self.agent_tools.function_executor(function_name, function_args)
        self.check_final(function_name, function_args, ".init/.init-test-tool", result)
        return result

    def get_executor(self, action):
        validator = self.agent_tools.function_validator
        executor = self.agent_tools.function_executor
        executor_map = {
            'init-env': self.function_executor_env,
            'init-minimal': self.function_executor_minimal,
            'init-style': self.function_executor_style,
            'init-files': self.function_executor_files,
            'init-run-tool': self.function_executor_run_tool,
            'init-test-tool': self.function_executor_test_tool,
        }
        if len(executor_map.keys()) != len(self.ACTION_MESSAGES):
            self.logger.error(f"Attention: there's an aditional or mising ACTION_MESSAGE at the executor_map!")

        return executor_map.get(action, executor), validator

    async def process_request(self, action, subtask=None,
                              work_item=None, request_details=None,
                              tool_call_log=None, capabilities: dict = None, container_name=None):

        if action not in self.setup_steps.keys():
            action, _ = self.get_action()

        self.logger.info(f"initial_setup: start action: {action}")
        context = {
            'action': action,
            'work_item': work_item,
            'request_details': request_details,
            'agent_list': capabilities.keys(),
            'agent_capabilities': capabilities,
            'number_of_allowed_calls': 25,
            'subtask': subtask,
        }

        system_prompt = self.render_prompt('system', context)
        user_prompt = self.render_prompt('user', context)
        selected_platform = work_item.get("platform", ApplicationType.COMMON.value)
        selected_framework = work_item.get("framework", FrameworkType.DEFAULT.value)
        schemas = None
        if action == "init-run-tool":
            if  selected_framework in [ApplicationType.FLUTTER.value, ApplicationType.ANDROID.value]:
                schemas = self.agent_tools.get_function_schemas(["VisibleShellTools","AndroidEmulatorTools","ContainerFileTools"])
            else:
                schemas = self.agent_tools.get_function_schemas(["VisibleShellTools","WebBrowser","ContainerFileTools"])
            schemas_new = []
            for schema in schemas:
                add = True
                if schema['function']['name'].startswith("WebBrowser"):
                    if not schema['function']['name'] == "WebBrowser_goto":
                        add = False
                if add:
                    schemas_new.append(schema)
            schemas = schemas_new
        else:
            schemas = self.agent_tools.get_function_schemas(["ContainerFileTools","VisibleShellTools"])

        #if action == "init-minimal" and selected_framework == ApplicationType.IOS:
            #schemas = self.agent_tools.add_function_schemas_to_existing(schemas, ["IOSTools"])

        schemas = self.agent_tools.add_function_schemas_to_existing(schemas, ["DependencyTools"])

        executor, validator = self.get_executor(action)

        try:
            completion = await self.llm.llm_interaction_wrapper(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                user_prompt=None,
                system_prompt=None,
                model=self.model_name_selector.get_selected_model(),
                response_format={"type": "json_object"},
                function_schemas=schemas,
                function_executor=executor,
                function_validator=validator
            )

            content = completion.choices[0].message.content.strip()
        except Exception as e:
            self.logger.error(f"initial_setup: exception {e}")
            raise

        if action == "init-test-tool":
            self._ensure_test_setup(f"{self.base_path}/.init-test-tool")

        self.logger.info(f"initial_setup: done action: {action}")

        return content
