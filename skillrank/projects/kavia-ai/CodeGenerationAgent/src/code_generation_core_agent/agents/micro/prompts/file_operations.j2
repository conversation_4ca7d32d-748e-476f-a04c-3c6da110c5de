MANDATORY OUTPUT FORMAT - FILE OPERATIONS
CRITICAL: Your response MUST include one or more file_operations to complete this code task. Do not stop your response until you have provided all necessary file operations using the format below.
Your output will be in the following file_operations format. You must use the file_operations format to generate write, edit, and delete operations for files, with any associated comments and explanations.
(you must use your native tool call format for all other tool calls).
VALIDATION REQUIREMENT: Your response must contain at least one file operation block (write, edit, or delete), or it will fail validation and be considered incomplete.
+++++++++ file_operations format:
1. GENERAL OUTPUT STRUCTURE
• All user‑facing text (explanations, commentary, questions) must appear outside of fenced code blocks.
• Every code edit or file write must be wrapped in a fenced code block with a special info string so our parser can detect it.  The fence is made out of 4 backticks, not 3. These fences are collectively called file_operation blocks.
• IMPORTANT: Continue your response until all required file operations are provided. Do not stop after providing only explanations or summaries.
2.a. <PERSON>ARCH‑REPLACE DIFF BLOCKS
• Use this format to patch existing files:
````edit file="path/to/file.ext"      
<<<<<<< SEARCH
<old lines, verbatim>
=======
<new lines, verbatim>
>>>>>>> REPLACE   
````   
• Before the block, output a single line:
Explanation: <brief natural‑language description of why or what is changing>
• Do not issue mutiple cascading edit blocks with overlapping changes.
• Please make sure you have read the latest revision of the file before making any edits to it.
• Make sure the search block does not include <any></any> other text other than the lines to be replaced. Do not include any comments or any other text in the search block.
• Make sure that the search block uniquely identifies the lines to be replaced. If the search block does not uniquely identify the lines to be replaced, please use a different search block that uniquely identifies the lines to be replaced.
• If multiple portions of the file matches the search blocks all of those would be replaced, so make sure that the search block is unique to the lines you want to replace.
• If substantial changes are needed to the file, please use a write block instead of an edit block.
• If you have attempted multiple edits and they have failed, please use a write block instead of an edit block.
2.b. FULL FILE WRITE BLOCKS
• Use this format to create or overwrite entire text files:
````write file="path/to/file.ext"
<complete contents of the file, verbatim>
````      
• Before the block, output a single line:
Explanation: <brief natural‑language description of the file's purpose or changes>
• IMPORTANT: Write blocks contain ONLY the complete file contents. Do NOT use SEARCH/REPLACE markers (<<<<<<< or >>>>>>>). Those are only for edit blocks.
2.c. DELETE FILE BLOCKS
• Use this format to delete files:
````delete file="path/to/file.ext"````
• Before the block, output a single line:
Explanation: <brief natural‑language description of the file's purpose or changes>
2.d. NOOP BLOCKS
• If no changes are needed, output a single line:
````noop````
3. STRICT FENCING RULES
• Only the markers edit file="…", write file="…" and `delete_file="…" are allowed inside the opening fence.
• No other text, headers, or comments may appear inside these fences.
• Preserve all special characters (quotes, backslashes, emojis, Unicode, etc.) exactly as provided—no escaping.
• Important fences around write and edit blocks are made out of 4 backticks, not 3.
• If you have other fences in the file, make sure the fences with 4 backticks around the write and edit blocks are closed at the end of those blocks and are not confused with the other fences in the file.
4.a. EXAMPLES  

   **User‑facing text goes here.**  

   Explanation: Add logging to handle null users  
   ````edit file="src/users.py"
   <<<<<<< SEARCH
       def get_user(id):
           return db.find(id)
   =======
       def get_user(id):
           user = db.find(id)
           if user is None:
               log.error(f"User {id} not found")
           return user
   >>>>>>> REPLACE
   ````
   **User‑facing text continues here.**

4.b. EXAMPLE 2
    **User‑facing text goes here.**  
    
    Explanation: Add logging to handle null users  
    ````write file="src/users.py"
         def get_user(id):
              user = db.find(id)
              if user is None:
                log.error(f"User {id} not found")
              return user
    ````
    **User‑facing text continues here.**

4.c. EXAMPLE 3
    **User‑facing text goes here.**  
    
    Explanation: Add logging to handle null users  
    ````delete file="src/users.py"
    ````
    **User‑facing text continues here.**
4.d. Example 4
    Code already exists and meets requirements, no changes needed.   
    ````noop````

5. COMPLETION REQUIREMENTS
MANDATORY: Your response is only complete when you have provided all necessary file operations. The sequence should be:

Brief task summary (optional)
ALL required file operations using the format above
Final completion message: "Task completed: [brief summary]"

DO NOT STOP your response after providing only explanations, plans, or summaries. Continue until all file operations are implemented.
For any build or runtime error that is directly related to a missing dependency or misconfigured environment—and which cannot be resolved via file edits—you must immediately generate an appropriate shell tool call using your native tool call format.

Do not use file_operation blocks or text inside four backticked fences for tool calls. Use the native tool call format for all other tool calls. 
CRITICAL BINARY FILE RESTRICTION:
- file_operations (write/edit blocks) are for TEXT FILES ONLY
- For binary files (images, videos, executables, etc.), you MUST use shell commands using uour native tooll call format to copy/move them
- If you attempt to write binary content using file_operations, it will corrupt the file
- Always verify file extensions: .jpg, .png, .gif, .pdf, .mp4, etc. require shell commands, not file_operations


