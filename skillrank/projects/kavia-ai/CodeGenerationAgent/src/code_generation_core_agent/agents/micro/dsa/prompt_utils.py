import os
import json
import jinja2
import logging
from typing import Optional, Dict, Any
from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerInfo


def render_jinja_template(
    template_name: str, 
    template_vars: Dict[str, Any], 
    logger: Optional[logging.Logger] = None
) -> str:
    """
    Generic Jinja template renderer.
    
    Args:
        template_name: Name of the template file (e.g., "framework_detection_agent.j2")
        template_vars: Dictionary of variables to pass to the template
        logger: Optional logger instance for error logging
        
    Returns:
        The rendered template string
        
    Raises:
        ValueError: If template is not found or rendering fails
    """
    try:
        # Get the directory containing the prompts
        current_dir = os.path.dirname(os.path.abspath(__file__))
        prompts_dir = os.path.join(current_dir, "prompts")
        
        # Set up Jinja environment
        env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(prompts_dir),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Load and render the template
        template = env.get_template(template_name)
        prompt = template.render(**template_vars)
        
        return prompt
        
    except jinja2.exceptions.TemplateNotFound:
        error_msg = f"Template '{template_name}' not found"
        if logger:
            logger.error(error_msg)
        raise ValueError(error_msg)
    except jinja2.exceptions.TemplateError as e:
        error_msg = f"Template error in '{template_name}': {str(e)}"
        if logger:
            logger.error(error_msg)
        raise ValueError(error_msg)
    except Exception as e:
        error_msg = f"Unexpected error rendering template '{template_name}': {str(e)}"
        if logger:
            logger.error(error_msg)
        raise ValueError(error_msg)
