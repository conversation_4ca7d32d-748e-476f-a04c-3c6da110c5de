{# Base Common Guidelines for All Setup Templates
   ==============================================
   
   This template contains the foundational guidelines that are shared across
   all setup templates (planning, review, and execution) to ensure consistency.
#}

**CONTAINER CONTEXT:**
{{ container_info.get_formatted_info() }}

**CONTAINER EXECUTION CONTEXT:**
- The Docker container is ALREADY CREATED and running
- The goal is to setup the development environment WITHIN the existing docker container
- DO NOT suggest or recommend Dockerfile or container image build steps
- The setup will be executed by an engineer agent in a non-interactive, autonomous manner using shell commands
- The setup must be optimized for fast, automated execution - avoid fine-grained manual steps or interactive prompts

{% if container_info.dockerfile_summary %}
During your analysis use the following Docker image information to better understand the environment:

**CONTAINER IMAGE SUMMARY:**
{{ container_info.dockerfile_summary }}

**CONTAINER IMAGE PRE-INSTALLED TOOLS:**
- Do NOT reinstall tools that are already available in the Docker image
- Use existing package managers and runtimes from the Docker image
- Avoid redundant installations of global tools (npm -g, pip, etc.)
- Cross-check against the installed tools baseline and Docker image content
- DO NOT recommend installing tools that already appear installed in the container image (e.g., via apt-get install, pip install, npm -g install)
- If you recommend action due to a version mismatch, explicitly call out the version issue and required change
{% endif %}

**SCRIPT WORKSPACE PATH GUIDELINES:**
- **USE CONTAINER INFO WORKSPACE**: The workspace path from the Container Information section is the authoritative source for the target workspace folder
- **IGNORE DOCKERFILE WORKSPACE**: Do not extract or infer workspace paths from the Dockerfile content
- **SCRIPTS MUST USE CONTAINER INFO WORKSPACE**: All scaffolding, build, and execution scripts must use the workspace path specified in the Container Information
- **ABSOLUTE PATHS**: Use absolute paths based on the container info workspace for all file operations and project setup

**SCRIPT STRUCTURE REQUIREMENTS:**
- **SHEBANG**: Start with exactly `#!/usr/bin/env bash`
- **ERROR HANDLING**: Include `set -euo pipefail` early in the script
- **NON-INTERACTIVE**: Use non-interactive flags for package installs (--yes, --force, --quiet, etc.)
- **PRIVILEGE**: Use `sudo` for privileged operations when needed (container has sudo)
- **WORKSPACE USAGE**: Use the workspace path from Container Information for all file operations
- **NO HARDCODED PATHS**: Don't hardcode workspace paths; use the container info workspace path consistently

**SCRIPT OPTIMIZATION REQUIREMENTS:**
- **CONCISE COMMANDS**: Use one-liners and chained commands where possible (e.g., `apt update && apt install -y package`)
- **COMBINE OPERATIONS**: Merge related commands with && or ; to reduce line count
- **MINIMAL OUTPUT**: Use --quiet, -q, or redirect to /dev/null for non-essential output
- **SKIP VERBOSE FLAGS**: Omit --verbose, --debug unless debugging is needed
- **USE ALIASES**: Use shorter command aliases when available (e.g., `npm i` instead of `npm install`)
- **REDUCE ECHO STATEMENTS**: Minimize status messages, focus on essential output only
- **BATCH OPERATIONS**: Group similar operations in single commands
- **AVOID REDUNDANT CHECKS**: Skip existence checks unless critical for idempotency
- **TARGET: 5-15 LINES MAX**: Aim for scripts with minimal line count while maintaining functionality
- **IDEMPOTENCY**: Check if tools/configs already exist and skip re-installation

**SCRIPT ENVIRONMENT PERSISTENCE GUIDELINES:**
- **GLOBAL ENVIRONMENT SETUP**: Set up global environment variables via `/etc/profile.d/*.sh` for framework CLIs
- **TOOL AVAILABILITY**: Ensure all installed tools are available in future shell sessions
- **PATH EXPORTS**: Configure PATH exports for framework binaries
- **FRAMEWORK ENVIRONMENT VARIABLES**: Set global framework environment variables (e.g., RAILS_ENV, NODE_ENV) via `/etc/profile.d/*.sh`

**SCRIPT ERROR HANDLING GUIDELINES:**
- **STRICT ERROR HANDLING**: Use `set -euo pipefail` for strict error handling
- **CLEAR ERROR MESSAGES**: Provide clear error messages for each section
- **APPROPRIATE EXIT CODES**: Exit with appropriate error codes on failure
- **VALIDATION CHECKS**: Include validation checks after each major operation