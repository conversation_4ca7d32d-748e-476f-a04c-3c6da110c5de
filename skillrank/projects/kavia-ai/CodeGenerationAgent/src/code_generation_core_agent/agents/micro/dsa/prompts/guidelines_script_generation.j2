{# Script Generation Common Guidelines for Setup Script Generation and Refinement
   ===========================================================================
   
   This template contains script generation-specific guidelines that should be referenced by both
   setup script generation and refinement templates to ensure consistency.
#}

{% include 'guidelines_common.j2' %}

**SCRIPT-SPECIFIC STRUCTURE REQUIREMENTS:**
- Required command markers are present based on framework requirements: INSTALL, SCAFFOLD, DEPS are typically required; BUILD for compiled languages; TEST for frameworks with testing; START for server applications; VALIDATE and STOP for applications that run
- Command markers use exact format: `# === COMMAND: COMMAND_NAME ===`
- Each command section contains appropriate script content
- START command is clean launch only (no verification)
- VALIDATE command focuses on runtime validation (not build/test duplication)
- STOP command properly terminates the application

**COMMAND CATEGORY VALIDATION:**
Ensure the script includes appropriate commands based on the framework and application type:

- **INSTALL**: Setup ALL development tools and runtime environment (REQUIRED for all frameworks)
- **SCAFFOLD**: Create project structure AND initialize framework (REQUIRED for all frameworks, MUST use workspace path)
- **DEPS**: Install ALL packages, dependencies, AND configuration (REQUIRED for all frameworks)
- **BUILD**: Build the application to verify environment setup (REQUIRED for compiled languages like Go, Rust, C++; OPTIONAL for interpreted languages like Python, Node.js)
- **TEST**: Setup testing framework and run tests to verify testing works (REQUIRED if framework supports testing; OPTIONAL for simple scripts)
- **START**: Start the application (clean launch only, no verification) (REQUIRED for server applications, web apps, long-running processes; OPTIONAL for CLI tools, one-off scripts)
- **VALIDATE**: Comprehensive validation: verify app is running + test functionality + stop (REQUIRED if START is present; OPTIONAL for CLI tools)
- **STOP**: Stop the application cleanly (REQUIRED if START is present; OPTIONAL for CLI tools)

**START COMMAND GUIDELINES:**
The START command should:
- Launch the application cleanly without verification
- Be a canonical command that can be reused downstream
- Focus only on starting the app, not checking if it's running
- Use appropriate flags for non-interactive operation

**VALIDATION COMMAND GUIDELINES:**
The VALIDATE command should:
- Verify the application is running (after START command)
- Perform basic runtime health checks (e.g., HTTP endpoint response, process status, port availability)
- Provide clear success/failure indicators
- Fail loudly with descriptive error messages if any step fails
- Focus on runtime validation, not build or test validation

**SCRIPT REVIEW CHECKLIST (CRITICAL if violated for INSTALL command; HIGH for others):**
- Follow all script structure requirements from the common guidelines above
- Use workspace path from container info consistently
- Avoid reinstalling tools already available in Dockerfile
- Use non-interactive flags for package installations
- Include proper error handling and validation
