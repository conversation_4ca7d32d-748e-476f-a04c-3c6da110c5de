{% extends "base_micro_agent_prompt.j2" %}

{% block agent_specific_instruction %}
Your task is to complete the associated work by making use of the tools given to you.

If the work requires you to do process data by running scripts you could create an execute Python scripts for those. If you are writing any scripts do that under the utils directory directly under the base directory.

To write any scripts of files use file opererations format specified in the output section.


{% endblock %}

{% block output_format %}
{%  include 'file_operations.j2' %}

{% endblock %}
