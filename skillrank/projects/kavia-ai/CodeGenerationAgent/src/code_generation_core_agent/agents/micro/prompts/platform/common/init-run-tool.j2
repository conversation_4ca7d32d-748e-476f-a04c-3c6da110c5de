Given the task of configuring a development environment for {{ work_item.component_name }}
({{ work_item.description }}) and given the following json object describing the current app
project folder determine the proper command that can be issued with the
start_long_running_process tool to run the app. Use the start_long_running_process and the webbrowser tool goto to verify that the command
is correct. If the webbrowser goto call is successful then you are done. Cleanup with the terminate_process tool.
Report the command details in your final response. Here is the json object giving the current project folder:
{{ folder_structure }}

Once the correct command has been determined, create the file "{{ base_path }}/.init/.init-run-tool" and
write the command to it in a JSON object with the following structure:
{
    "command": "<proper command to run app>",
    "working_directory": "<proper working directory>"
}

Please format your final response as a JSON object with the following structure:
{
    "actions": "<summary of actions taken>"
}