{# Execution Common Guidelines for Setup Plan Execution and QA
   ==========================================================
   
   This template contains execution-specific guidelines that should be referenced by both
   setup plan execution and QA templates to ensure consistency.
#}

{% include 'guidelines_common.j2' %}

**EXECUTION-SPECIFIC SCRIPT REQUIREMENTS:**
- **IDEMPOTENCY**: Check if tools/configs already exist and skip re-installation to avoid redundant work
- **VALIDATION**: After install, verify tool versions are accessible on PATH
- **OUTPUT SCRIPTS**: Provide updated scripts with any modifications made during execution
- **SCRIPT NAMES**: Must be canonical: install, scaffold, test, build, start, validation

**ERROR HANDLING PRINCIPLES:**
- **FAIL FAST**: Do not mask errors; provide clear error messages
- **ESCALATE WHEN NEEDED**: Escalate to architect only when genuinely needing intervention
- **COMMON FIXES FIRST**: Attempt to fix common issues before escalating
- **DECISIVE ACTION**: Act decisively based on available information

**EXECUTION RULES:**
- <PERSON><PERSON><PERSON> finish with suggestions, questions, or alternatives
- Execute clear actions immediately
- Complete the step successfully OR escalate to architect
- Provide clear feedback on what was accomplished and any issues encountered
