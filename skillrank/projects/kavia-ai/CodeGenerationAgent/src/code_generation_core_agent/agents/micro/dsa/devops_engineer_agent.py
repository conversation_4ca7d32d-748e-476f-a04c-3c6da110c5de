import logging
from typing import Dict, Any, List
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.micro.dsa.ContainerSetupStep import ContainerSetupStep
from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerIn<PERSON>
from code_generation_core_agent.agents.micro.dsa.ContainerSetupPlan import ContainerSetupPlan
from code_generation_core_agent.agents.micro.dsa.prompt_utils import render_jinja_template
from code_generation_core_agent.agents.setup_logger import setup_logger
import json
import os
import shlex


class DevOpsEngineerAgent:
    """
    DevOps Engineer Agent: Executes container setup steps using LLMInterface.
    """
    def __init__(self, model: str, base_path: str = None, agent_tools=None, max_attempts: int = 3):
        self.model = model
        self.base_path = base_path
        self.agent_tools = agent_tools
        self.logger = setup_logger("DevOpsEngineerAgent", base_path)
        self.max_attempts = max_attempts
        self.llm = LLMInterface(
            llm_api_key=None,
            session_dir=base_path,
            instance_name="devops_engineer",
        )

    def get_system_prompt(self) -> str:
        return """
        You are an expert DevOps Engineer specializing in development environment setup within Docker containers.
        
        Your responsibilities:
        1. Execute development environment setup steps efficiently and correctly
        2. Follow best practices for the specific framework and platform
        3. Ensure security and proper error handling
        4. Use available tools effectively to complete tasks
        5. Provide clear, detailed output of your work
        6. Return both a summary of actions performed AND a shell script with successfully executed commands
        7. PROACTIVELY RESOLVE COMMON ISSUES before escalating to architect
        8. Optimize shell scripts for autonomous, non-interactive execution with best practices        
        """

    async def execute_consolidated_script(self, script_content: str, container_info: ContainerInfo) -> Dict[str, Any]:
        """
        Execute a consolidated setup script by writing it to .init/setup_all.sh and running it.
        
        Args:
            script_content: The consolidated bash script content
            container_info: Container information for context
            
        Returns:
            Dict containing execution results with status, output, and exit code
            
        Raises:
            Exception: If script execution fails with non-zero exit code
        """
        self.logger.info(f"Executing consolidated script for {container_info.container_name}")
        
        try:
            # Step 1: Write consolidated script to .init/setup_all.sh
            # Use workspace path from container_info if available, otherwise use relative path
            if container_info.workspace:
                script_path = os.path.join(container_info.workspace, ".init", "setup_all.sh")
            else:
                script_path = ".init/setup_all.sh"
            self.logger.info(f"Writing consolidated script to {script_path}")
            
            # Use ContainerWriteTools if available, otherwise fall back to direct file writing
            if self.agent_tools and "ContainerWriteTools" in self.agent_tools.tool_set:
                write_result = self.agent_tools.tool_set["ContainerWriteTools"].write_file(
                    file_path=script_path,
                    content=script_content
                )
                if write_result.get("status") != "SUCCESS":
                    raise Exception(f"Failed to write script file: {write_result.get('message', 'Unknown error')}")
            else:
                # Fallback: write file directly using executor
                if not self.agent_tools or not self.agent_tools.executor:
                    raise Exception("No agent_tools or executor available for file writing")
                
                # Create .init directory if it doesn't exist
                if container_info.workspace:
                    init_dir = os.path.join(container_info.workspace, ".init")
                    mkdir_cmd = f"mkdir -p {shlex.quote(init_dir)}"
                else:
                    mkdir_cmd = "mkdir -p .init"
                output, return_code = self.agent_tools.executor.run_one_shot_command(mkdir_cmd)
                if return_code != 0:
                    raise Exception(f"Failed to create .init directory: {output}")
                
                # Write script content to file
                encoded_content = script_content.replace("'", "'\"'\"'")  # Escape single quotes
                escaped_script_path = shlex.quote(script_path)
                write_cmd = f"echo '{encoded_content}' > {escaped_script_path}"
                output, return_code = self.agent_tools.executor.run_one_shot_command(write_cmd)
                if return_code != 0:
                    raise Exception(f"Failed to write script file: {output}")
            
            # Step 2: Make script executable
            self.logger.info(f"Making script executable: {script_path}")
            escaped_script_path = shlex.quote(script_path)
            chmod_cmd = f"chmod +x {escaped_script_path}"
            output, return_code = self.agent_tools.executor.run_one_shot_command(chmod_cmd)
            if return_code != 0:
                raise Exception(f"Failed to make script executable: {output}")
            
            # Step 3: Execute the script
            self.logger.info(f"Executing consolidated script: {script_path}")
            execute_cmd = f"bash {escaped_script_path}"
            output, return_code = self.agent_tools.executor.run_one_shot_command(execute_cmd, timeout=300)  # 5 minute timeout
            
            # Step 4: Handle execution results
            if return_code != 0:
                self.logger.error(f"Consolidated script execution failed with exit code {return_code}")
                self.logger.error(f"Script output: {output}")
                raise Exception(f"Consolidated script execution failed with exit code {return_code}. Output: {output}")
            
            self.logger.info(f"Consolidated script executed successfully for {container_info.container_name}")
            
            return {
                "status": "success",
                "exit_code": return_code,
                "output": output,
                "script_path": script_path,
                "message": f"Consolidated script executed successfully for {container_info.framework}"
            }
            
        except Exception as e:
            self.logger.error(f"Error executing consolidated script: {str(e)}")
            raise e

    async def execute_step(self, plan: ContainerSetupPlan, step: ContainerSetupStep) -> ContainerSetupStep:
        step.increment_retry_attempts()

        if step.retry_attempts >= self.max_attempts:
            raise Exception(f"Step '{step.name}' has already exhausted all retry attempts ({step.retry_attempts}/{self.max_attempts}), setup failed")

        self.logger.info(f"Attempt {step.retry_attempts}/{self.max_attempts} for step '{step.name}'")
        
        # Generate plan summary from the plan
        plan_summary = plan.generate_plan_summary()
        
        # Prepare template variables
        template_vars = {
            "step": step,
            "container_info": plan.container_info,
            "plan_summary": plan_summary,
            "max_attempts": self.max_attempts,
            "previous_qa_result": {
                "qa_approved": step.qa_approved,
                "qa_summary": step.qa_summary,
                "qa_issues": step.qa_issues
            } if step.qa_approved is not None else None
        }

        # Render the prompt using the Jinja template
        prompt = render_jinja_template("setup_plan_execution.j2", template_vars, self.logger)
        self.logger.info(f"setup plan execution prompt: \n{prompt}")
        function_schemas = []
        if self.agent_tools and step.tools_required:
            function_schemas = self.agent_tools.get_function_schemas(step.tools_required)
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=prompt,
            system_prompt=self.get_system_prompt(),
            model=self.model,
            response_format={"type": "json_object"},
            function_schemas=function_schemas,
            function_executor=self.agent_tools.function_executor if self.agent_tools else None,
            function_validator=self.agent_tools.function_validator if self.agent_tools else None
        )
        
        # Get the JSON response directly from the LLM
        response_data = json.loads(completion.choices[0].message.content)

        # Update the step object with LLM response data
        step.summary = response_data.get("description", "")
        step.architect_help_request = response_data.get("help_description", "")
        step.needs_architect_help = response_data.get("needs_architect_help", False)
        
        # Handle output scripts
        if "output_scripts" in response_data and response_data["output_scripts"]:
            from code_generation_core_agent.agents.micro.dsa.Script import Script
            for script_data in response_data["output_scripts"]:
                script = Script(
                    name=script_data["name"],
                    code=script_data["code"]
                )
                step.output_scripts.append(script)

        return step


 