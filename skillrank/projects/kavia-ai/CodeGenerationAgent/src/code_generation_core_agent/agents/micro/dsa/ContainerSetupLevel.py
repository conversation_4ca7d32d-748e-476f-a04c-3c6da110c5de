from enum import Enum
from typing import List

class ContainerSetupLevel(Enum):
    """
    Enumeration for recommendation priority levels.
    """
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"
    
    def is_at_or_above(self, other_level: 'ContainerSetupLevel') -> bool:
        """
        Check if this recommendation level is at or above the specified level.
        
        Args:
            other_level: The level to compare against
            
        Returns:
            True if this level is at or above the other level, False otherwise
        """
        level_order = {
            ContainerSetupLevel.CRITICAL: 4,
            ContainerSetupLevel.HIGH: 3,
            ContainerSetupLevel.MEDIUM: 2,
            ContainerSetupLevel.LOW: 1
        }
        return level_order[self] >= level_order[other_level]
    
    @classmethod
    def get_levels_at_or_above(cls, minimum_level: 'ContainerSetupLevel') -> List['ContainerSetupLevel']:
        """
        Get all recommendation levels at or above the specified minimum level.
        
        Args:
            minimum_level: The minimum priority level to include
            
        Returns:
            List of recommendation levels at or above the specified minimum level
        """
        return [level for level in cls if level.is_at_or_above(minimum_level)] 