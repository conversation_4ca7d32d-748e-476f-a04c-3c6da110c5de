from typing import Dict, List, Optional
from code_generation_core_agent.agents.micro.file_lister import FileLister
from code_generation_core_agent.config import config

class FileListerFactory:
    @staticmethod
    def create_from_platform_config(platform_config: Dict) -> FileLister:
        """
        Create a FileLister instance from platform configuration.
        
        Args:
            platform_config: The platform configuration dictionary
            
        Returns:
            FileLister: Configured FileLister instance
        """
        file_lister_config = platform_config.get('file_lister', {})
        
        return FileLister(
            threshold=config.getint('PROMPTS', 'important_files_threshold', fallback = 100),
            emit_important_files=file_lister_config.get('emit_important_files', True),
            exclude_dirs=file_lister_config.get('exclude_dirs', []),
            exclude_files=file_lister_config.get('exclude_files', ["files.yaml"]),
            exclude_extensions=file_lister_config.get('exclude_extensions', [".log"]),
            include_files=file_lister_config.get('include_files', []),
            print_contents_threshold=config.getint('PROMPTS', 'directory_size_threshold', fallback=100*1024)
        )