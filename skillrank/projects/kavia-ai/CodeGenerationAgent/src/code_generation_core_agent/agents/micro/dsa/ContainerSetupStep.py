from code_generation_core_agent.agents.micro.dsa.ContainerStepCategory import ContainerStepCategory
from code_generation_core_agent.agents.micro.dsa.ContainerStepStatus import ContainerStepStatus
from code_generation_core_agent.agents.micro.dsa.Script import Script

from dataclasses import dataclass, field
from typing import List, Optional


@dataclass
class ContainerSetupStep:
    id: str
    name: str
    description: str
    category: ContainerStepCategory
    script_name: str  # Which script this step is responsible for (install, scaffold, test, build, start, validation)
    dependencies: List[str] = field(default_factory=list)
    tools_required: List[str] = field(default_factory=list)
    status: ContainerStepStatus = ContainerStepStatus.PENDING
    execution_priority: int = 5  # Default priority (1=highest, 10=lowest)
    privilege_level: str = "auto"  # 'user', 'system', or 'auto' (try user, then escalate)
    retry_attempts: int = 0  # Track retry attempts for this step
    
    # Script-related fields
    script_code: Optional[str] = None  # Original script code from architect
    output_scripts: List[Script] = field(default_factory=list)  # Generated by engineer
    
    # LLM response fields
    summary: Optional[str] = None  # Summary of actions performed by engineer
    architect_help_request: Optional[str] = None  # Description of help needed from architect
    needs_architect_help: bool = False  # Whether engineer needs architect help
    
    # QA-related fields
    qa_approved: bool = False  # Whether QA approved this step
    qa_summary: Optional[str] = None  # QA validation summary
    qa_issues: List[str] = field(default_factory=list)  # Issues found by QA

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category.value if self.category else None,
            'dependencies': self.dependencies,
            'tools_required': self.tools_required,
            'status': self.status.value if self.status else None,
            'execution_priority': self.execution_priority,
            'privilege_level': self.privilege_level,
            'retry_attempts': self.retry_attempts,
            'script_name': self.script_name,
            'script_code': self.script_code,
            'output_scripts': [script.to_dict() for script in self.output_scripts],
            'summary': self.summary,
            'architect_help_request': self.architect_help_request,
            'needs_architect_help': self.needs_architect_help,
            'qa_approved': self.qa_approved,
            'qa_summary': self.qa_summary,
            'qa_issues': self.qa_issues,
        }

    def increment_retry_attempts(self):
        """Increment the retry attempts counter for this step"""
        self.retry_attempts += 1
        return self.retry_attempts

    def reset_retry_attempts(self):
        """Reset the retry attempts counter for this step"""
        self.retry_attempts = 0

    def get_retry_info(self) -> dict:
        """Get retry information for this step"""
        return {
            'retry_attempts': self.retry_attempts,
            'step_id': self.id,
            'step_name': self.name,
            'status': self.status.value if self.status else None
        }

    @classmethod
    def from_dict(cls, data):
        """Reconstruct a ContainerSetupStep from a dict, handling Enums using dacite."""
        import dacite
        from code_generation_core_agent.agents.micro.dsa.ContainerStepCategory import ContainerStepCategory
        from code_generation_core_agent.agents.micro.dsa.ContainerStepStatus import ContainerStepStatus
        from code_generation_core_agent.agents.micro.dsa.Script import Script
        
        config = dacite.Config(
            type_hooks={
                ContainerStepCategory: ContainerStepCategory,
                ContainerStepStatus: ContainerStepStatus,
                Script: Script.from_dict
            },
            cast=[ContainerStepCategory, ContainerStepStatus]
        )
        return dacite.from_dict(
            data_class=cls,
            data=data,
            config=config
        )