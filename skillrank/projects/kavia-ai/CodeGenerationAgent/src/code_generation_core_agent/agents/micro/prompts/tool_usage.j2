{# Tool Usage Instructions #}
{% block tool_usage %}
You have access to various tools to assist you in your task. Please make calls to function from those tools to perform your task prior to creating the final output. Use the proper format and conventions for tool calling. Do not provide tool calls as plain text output.
If you need to read multiple files, batch the read operations together. Always prefer the least number of read operations necessary to complete the task.

Function-calling protocol (STRICT)
	•	When you need to use a tool you MUST return it via the native tool-calling format.


=== Additional Guidelines for Shell Commands and Tool Calls ===

- **Dependency Installation and Environment Fixes:**
  - If you encounter a build or quality issue due to missing dependencies (e.g., 'Module not found: Error: Can\'t resolve "react-router-dom"'), or if an environment-related error is detected, you must execute a VisibleShellTools_start_long_running_process tool call to remedy this issue by installing the missing package(s). 


- **Critical Error Handling:**
  - If a critical error (e.g., a build error) is detected, analyze the cause. If the error is fixable via a non-interactive shell command (such as installing a missing dependency), immediately issue a tool call to perform that fix.

Assume test run always happens in a non-interative CI mode.

You are allowed to use only {{number_of_allowed_calls}} tools/function calls to complete this task.

When providing shell instructions:

1. Ensure all shell commands and installation steps are non-interactive and do not require user input. If you encounter need to issue interactive prompts, find a way to suppress them. For example, don't use npm test, instead use CI=true npm test.
2. Set environment variables when needed to avoid interactive prompts.
3. For package managers, use options that suppress interactive prompts and automatically accept defaults.
4. When encountering failed commands, do not assume they are always related to missing tools. Consider other potential issues such as:
    - Misconfigured code
    - Incorrect build structure
    - Environment variable issues
    - Permissions problems
    - Incompatible versions of dependencies
    - Network-related failures
5. Do not use shell commands for reading, writing or modifying files. Use the appropriate tools for those tasks. Do not use shell commands to create files or directories into which you are going to write. 
Folder structure is automatically created when trying to write a file. For example, if you try to write a file file1 in directory dir1, the directory dir1 will be created automatically if it does not exist. 
If you try to write a file file1 in directory dir1/dir2, the directories dir1 and dir2 will be created automatically if they do not exist. If you want to rename, move or copy files you should use shell commands for doing that using VisibleShellTools_execute_immediate_return_command.


When starting up services ensure that services are always started as background processes and not in the foreground to avoid blocking the execution of other commands.

For any commands that may not return a result immediately ALWAYS use the VisibleShellTools_start_long_running_process function. Example: npm start

If your writes or edits resulted in a build error you should fix the issue.
The right solution for a build/quality issue can be:
    1.) Fix the issue in the code by issuing a write or edit operation.
    2.) Install dependencies using tool calls
    3.) Review integration of the recent changes and make further edits or writes to fix the issue.

** Service and Port Management: **

If a port is already in use you should assume that the requested service is already running and can hot-reload the changes.

{% endblock %}