include:
  - platform/common/platform.yaml

container:
  container_name: kavia_default_container_image

micro_agents:
  InitialSetupAgent:
    capabilities:
      - Perform initial setup of development environment for android / kotlin
    tools:
      - InitialSetupTools
      - DependencyTools
      - AndroidEmulatorTool
    prompt_file: initial_setup_agent.j2
    class: InitialSetupMicroAgent
    setup_steps:
      init-env: prompts/platform/mobile/init-env.j2
      init-minimal: prompts/platform/mobile/init-minimal.j2
      init-files: prompts/platform/common/init-files.j2
      init-style: prompts/platform/mobile/init-style.j2
      init-run-tool: prompts/platform/mobile/init-run-tool.j2
      init-test-tool: prompts/platform/common/init-test-tool.j2

file_lister:
  exclude_dirs:
    - "build"
    - ".gradle"
    - ".idea"
    - "captures"
    - "app/build"
  exclude_files:
    - "local.properties"
    - "*.iml"
  exclude_extensions:
    - ".apk"
    - ".aar"
    - ".dex"
  include_files:
    - "build.gradle"
    - "settings.gradle"
    - "gradle.properties"