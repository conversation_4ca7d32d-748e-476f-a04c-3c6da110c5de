import logging
import json
import async<PERSON>
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.micro.dsa.ContainerSetupStep import ContainerSetupStep
from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerInfo
from code_generation_core_agent.agents.micro.dsa.ContainerSetupPlan import ContainerSetupPlan
from code_generation_core_agent.agents.micro.dsa.prompt_utils import render_jinja_template
from typing import Dict, Any
from code_generation_core_agent.agents.setup_logger import setup_logger


class DevOpsQAAgent:
    """
    DevOps QA Agent: Reviews DevOps Engineer step execution and enforces quality standards.
    """
    def __init__(self, model: str, base_path: str = None, max_attempts: int = 3):
        self.model = model
        self.base_path = base_path
        self.logger = setup_logger("DevOpsQAAgent", base_path)
        self.max_attempts = max_attempts
        self.llm = LLMInterface(
            llm_api_key=None,
            session_dir=base_path,
            instance_name="devops_qa",
        )

    def get_system_prompt(self) -> str:
        return """You are a Senior DevOps Quality Assurance Specialist with extensive experience in container orchestration, infrastructure automation, and deployment pipelines.

Your primary responsibility is to validate that the DevOps engineer has successfully completed the assigned tasks by reviewing their execution summary. Focus on verifying that the core objectives were achieved based on the reported evidence in the summary, NOT the implementation details in the shell script.

CORE RESPONSIBILITIES:
1. **Objective Validation**: Verify that the core task objectives were achieved successfully based on the execution summary
2. **Evidence Review**: Check that the engineer provided clear evidence of task completion in their summary
3. **Error Assessment**: Verify that no critical errors were reported in the execution summary
4. **Basic Security**: Flag obvious security issues mentioned in the summary (like hardcoded credentials)
5. **Documentation**: Ensure the summary adequately documents what was accomplished

QUALITY STANDARDS:
- **Critical Issues**: Only reject for actual errors, crashes, or security vulnerabilities reported in the summary
- **Core Objectives**: Verify the main task goal was achieved based on reported outcomes (e.g., tool installed, service running)
- **Basic Evidence**: Confirm there's clear proof of success in the summary (e.g., version check output, validation results)
- **Error Handling**: Ensure any errors mentioned were properly addressed according to the summary

IMPORTANT: You MUST respond with valid JSON format only.
"""

    async def review_step(self, plan: ContainerSetupPlan, step: ContainerSetupStep) -> ContainerSetupStep:
        # Generate plan summary from the plan
        plan_summary = plan.generate_plan_summary()
        
        # Prepare template variables        
        template_vars = {
            "step": step,
            "container_info": plan.container_info,
            "plan_summary": plan_summary,
            "max_attempts": self.max_attempts
        }

        qa_prompt = render_jinja_template("setup_plan_execution_qa.j2", template_vars, self.logger)
        
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": qa_prompt}],
            user_prompt=qa_prompt,
            system_prompt=self.get_system_prompt(),
            model=self.model,
            response_format={"type": "json_object"},
            function_schemas=None,
            function_executor=None,
            function_validator=None
        )
        
        # Get the JSON response directly from the LLM
        response_content = completion.choices[0].message.content
        json_data = json.loads(response_content)
        
        # Extract the simple JSON fields with defaults
        approval_status = json_data.get('approval_status', False)
        issues_found = json_data.get('issues_found', [])
        validation_summary = json_data.get('validation_summary', 'N/A')
        
        # Update the step object with QA results
        step.qa_approved = approval_status
        step.qa_summary = validation_summary
        step.qa_issues = issues_found
        
        return step 
