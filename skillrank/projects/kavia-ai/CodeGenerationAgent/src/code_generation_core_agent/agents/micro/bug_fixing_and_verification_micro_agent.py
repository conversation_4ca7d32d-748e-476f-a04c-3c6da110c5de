# bug_fixing_and_verification_micro_agent.py
from code_generation_core_agent.agents.micro.agent_system import  AgentRegistry
from code_generation_core_agent.agents.micro.generic_file_writer_agent import GenericFileWriterMicroAgent
import uuid
import json
from code_generation_core_agent.agents.micro.agent_system import AgentRegistry
@AgentRegistry.register("BugFixingAndVerificationAgent")
class BugFixingAndVerificationMicroAgent(GenericFileWriterMicroAgent):
    """Micro agent for handling code writing and modification tasks"""

    
    async def process_request_streaming(self, action, subtask=None,
                                      work_item=None, request_details=None,
                                      tool_call_log=None,
                                      capabilities: dict = None, container_name=None):
        """
        Process a code writing request using the streaming LLM capabilities.
        This version processes operation format and executes operations as they arrive.
        """

        async for chunk in super().process_request_streaming(
        action, subtask, work_item, request_details,
        tool_call_log, capabilities, container_name=container_name
        ):
            yield chunk

    
        