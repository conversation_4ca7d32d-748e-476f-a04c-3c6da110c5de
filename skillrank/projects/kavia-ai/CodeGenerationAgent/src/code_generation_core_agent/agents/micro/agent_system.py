# agent_system.py

import os
import importlib
import inspect
import json
import jinja2
import yaml

from code_generation_core_agent.agents.micro.session_merger import SessionMerger
from code_generation_core_agent.config import config
from code_generation_core_agent.llm.llm_interface import MaxRetriesError
from code_generation_core_agent.agents.utilities import TaskExecutionReporter
from code_generation_core_agent.agents.micro.file_lister import <PERSON><PERSON><PERSON>
from code_generation_core_agent.agents.micro.file_lister_factory import FileListerFactory  # Add this import

MAX_NUMBER_OF_CACHE_CONTROL_MESSAGES=4

class BaseMicroAgent:
    def __init__(self, llm, base_path, logger, model_name_selector, agent_tools, prompt_template, tools_to_use, setup_steps, agent_factory, reporter: TaskExecutionReporter = None   ):
        self.llm = llm
        self.project_base_path = base_path
        self.base_path = base_path
        self.logger = logger
        self.model_name_selector = model_name_selector
        self.agent_tools = agent_tools
        self.prompt_template = prompt_template
        self.agent_factory = agent_factory
        self.tools_to_use = tools_to_use
        self.setup_steps = setup_steps
        self.emit_important_files = config.getboolean('PROMPTS', 'emit_important_files')
        self.enable_cache = config.getboolean('LLM', 'enable_cache', fallback=False)
        self.is_processing = False
        self.reporter = reporter
        self.reuse_sessions = False
        self.container_workspace = None


    async def process_request(self, action, work_item, request_context, container_name=None):
        """
        Enhanced process_request with container context support
        """

        # Call the original implementation (to be implemented by subclasses)
        raise NotImplementedError("Subclasses must implement process_request method")

    def set_session_reuse(self, reuse: bool):
        """
        Set whether to reuse sessions for this agent.
        """
        self.reuse_sessions = reuse

    def is_session_reuse_enabled(self):
        """
        Check if session reuse is enabled for this agent.
        """
        return self.reuse_sessions

class AgentRegistry:
    _registry = {}

    @classmethod
    def register(cls, agent_type):
        def decorator(agent_class):
            cls._registry[agent_type] = agent_class
            return agent_class

        return decorator

    @classmethod
    def get_agent(cls, agent_type):
        return cls._registry.get(agent_type)
    
    @classmethod
    def clear_registry(cls):
        cls._registry.clear()


class MicroAgentFactory:
    def __init__(self, config_in, prompts_base_path):
        self.config = config_in
        self.prompts_base_path = prompts_base_path
        self.class_path = self.get_class_path()
        self.agent_list = config_in.get('agent_list', [])
        self.agent_capabilities = config_in.get('capabilities', {})
        self.load_agents()
        self.messages = []

    def get_agent_list(self):
        return self.agent_list

    def get_agent_capabilities(self):
        print("Agent capabilities ", self.agent_capabilities)
        return self.agent_capabilities

    def get_class_path(self):
        # If the last directory is 'prompts', use the parent directory
        if os.path.basename(self.prompts_base_path) == 'prompts':
            return os.path.dirname(self.prompts_base_path)
        # Otherwise, use the prompts_base_path as is
        return self.prompts_base_path

    def load_agents(self):

        self._discovered_agent_modules = {}

        for filename in os.listdir(self.class_path):
            if filename.endswith('.py') and not filename.startswith('__'):
                module_name = f'agents.{filename[:-3]}'
                self._discovered_agent_modules[module_name] = {
                    'path': os.path.join(self.class_path, filename),
                    'loaded': False
                }

    def create(self, agent_type, llm, base_path, logger, model_name_selector, agent_tools, tools_to_use, setup_steps,
               reporter: TaskExecutionReporter = None):
        prompt_template = self.load_prompt_template(agent_type, logger)

        agent_class = AgentRegistry.get_agent(agent_type)

        if agent_class is None:
            self._lazy_load_agent(agent_type, logger)
            agent_class = AgentRegistry.get_agent(agent_type)

        if agent_class:
            return agent_class(llm, base_path, logger, model_name_selector, agent_tools, prompt_template, tools_to_use,
                               setup_steps, self, reporter)
        else:
            logger.info(f"No specific agent registered for {agent_type}. Using GenericMicroAgent.")
            return GenericMicroAgent(llm, base_path, logger, model_name_selector, agent_tools, prompt_template,
                                     tools_to_use, setup_steps, self, reporter)

    def _lazy_load_agent(self, agent_type, logger):
        """
        Lazily load agent modules when they are first requested
        """
        for module_name, module_info in self._discovered_agent_modules.items():
            if not module_info['loaded']:
                try:
                    spec = importlib.util.spec_from_file_location(module_name, module_info['path'])
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    for name, obj in inspect.getmembers(module):
                        if inspect.isclass(obj) and issubclass(obj, BaseMicroAgent) and obj != BaseMicroAgent:
                            AgentRegistry.register(name)(obj)

                    module_info['loaded'] = True

                    if AgentRegistry.get_agent(agent_type):
                        break

                except Exception as e:
                    logger.error(f"Error lazy loading module {module_name}: {str(e)}")

    def load_prompt_template(self, agent_type, logger):
        try:
            # Check if the agent_type exists in the micro_agents configuration
            if agent_type not in self.config['micro_agents']:
                raise ValueError(f"Agent type '{agent_type}' not found in configuration")

            # Get the agent configuration
            agent_config = self.config['micro_agents'][agent_type]

            # Check if the prompt_file is specified in the agent configuration
            if 'prompt_file' not in agent_config:
                raise ValueError(f"Prompt file not specified for agent type '{agent_type}'")

            prompt_filename = agent_config['prompt_file']
            templates_dir = self.prompts_base_path

            env = jinja2.Environment(
                loader=jinja2.FileSystemLoader(templates_dir),
                trim_blocks=True,
                lstrip_blocks=True
            )

            try:
                template = env.get_template(prompt_filename)
                return template
            except jinja2.exceptions.TemplateNotFound:
                error_msg = f"Prompt template file '{prompt_filename}' not found for agent type '{agent_type}'"
                logger.error(error_msg)
                raise ValueError(error_msg)
            except Exception as e:
                error_msg = f"Error loading prompt template for {agent_type}: {str(e)}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        except KeyError as ke:
            error_msg = f"Missing key in configuration: {str(ke)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error loading prompt template for {agent_type}: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
    def finalize(self):
        """
        Finalize the factory by clearing the registry and discovered modules.
        This is useful for cleaning up resources when the factory is no longer needed.
        """
        AgentRegistry.clear_registry()
        self._discovered_agent_modules.clear()


# Function to load the YAML config
def load_agent_config(config_path):
    with open(config_path, 'r') as file:
        return yaml.safe_load(file)


@AgentRegistry.register("GenericMicroAgent")
class GenericMicroAgent(BaseMicroAgent):
    session_merger = SessionMerger()
    def _execute_cmd(self, cmd):
        from code_generation_core_agent.agents.tools.shared_loop import shared_loop
        from code_generation_core_agent.config import config
        timeout = config.getint("TOOLS", "shell_command_timeout")

        async def _one_shot_docker_command():
            stdout_data, returncode = await self.agent_tools.executor.create_subprocess_shell_one_shot(cmd)
            return stdout_data, returncode

        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command())
            output, returncode = future.result(timeout=timeout)
            return output, returncode
        except Exception as e:
            print(f"Error: cmd {cmd} had exception {e}")
        return None, None

    def _list_directory(self, directory):
        dir_list = []
        cmd = f'ls -1 {directory}'
        output, returncode = self._execute_cmd(cmd)
        if returncode == 0:
            dir_list = output.splitlines()
        return dir_list

    def _read_file(self, filename):
        contents = None
        cmd = f'cat {filename}'
        output, returncode = self._execute_cmd(cmd)
        if returncode == 0:
            contents = output
        return contents


    def _list_important_files(self, base_folder, container_workspace=None):
        output = ""
        if self.emit_important_files:
            # Get the platform configuration from agent_tools
            config = self.agent_factory.config
            
            # Create FileLister instance using the factory
            lister = FileListerFactory.create_from_platform_config(config)

            if hasattr(self, 'include_directories') and self.include_directories is not None:
                lister.common_directories.append(self.include_directories)    

            if hasattr(self, 'important_files_threshold') and self.important_files_threshold is not None:
                lister.important_files_threshold = self.important_files_threshold
            
            output = lister.list_as_json(base_folder, container_workspace=container_workspace)
        return output


    def render_prompt(self, prompt_type, context):
        context['prompt_type'] = prompt_type
        return self.prompt_template.render(context)

    def interrupt_llm(self, interrupt_reason, remaining_calls=2) -> bool:
        if self.is_processing:
            self.llm.interrupt_task(interrupt_reason, remaining_calls)
            return True
        return False

    def inject_action(self, action):
        self.llm.set_action(action)

    def work_in_progress(self):
        return self.llm.is_work_in_progress()
    
    def _prepare_request_params(self, action, subtask=None, work_item=None,
                                request_details=None, capabilities=None, 
                                output_validator=None, container_name=None):

        
        if container_name and work_item:
            container_info = request_details.get('container_info', {})
            if container_info:
                self.container_workspace = container_info.get('workspace', None)
            else:
                self.container_workspace = None
        else:
            self.container_workspace = None

        self._cleanup_request_details(request_details)
        self._cleanup_work_item(work_item)

        max_calls = config.getint('LLM', 'max_tool_calls')

        context = {
            'action': action,
            'work_item': work_item,
            'request_details': request_details,
            'important_files': self._list_important_files(self.base_path, self.container_workspace),
            'agent_list': capabilities.keys(),
            'agent_capabilities': capabilities,
            'subtask': subtask,
            'number_of_allowed_calls': max_calls,
            'have_projdefn': request_details.get('have_projdefn'),
        }

        system_prompt = self.render_prompt('system', context)
        user_prompt = self.render_prompt('user', context)

        model = self.model_name_selector.get_selected_model()
        system_message = {"role": "system", "content": system_prompt}
        user_message = {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": user_prompt,
                },
            ]
        }

        # The logic is the following:
        # If session reuse is enabled, we try to merge the system and user messages.
        # The try_merge will determine if the old messages can be reused. It uses a truncator to
        # check if the new user message is similar to the old one.
        # If the system message is different, we will never reuse the session.

        if self.is_session_reuse_enabled():
            tmp_user_message = {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": action,
                    },
                ]
            }
            merged_messages = self.session_merger.try_merge(system_message, tmp_user_message)
            if merged_messages:
                messages = merged_messages
                self.logger.info(f"Reusing session with {len(messages)} messages.")
            else:
                messages = [system_message, user_message]
        else:
            messages = [system_message, user_message]

        self.system_message = system_message

        self._append_cache_control(model, messages)

        params = {
            "messages": messages,
            "user_prompt": None,
            "system_prompt": None,
            "model": model,
            "function_schemas": self.agent_tools.get_function_schemas(self.tools_to_use),
            "function_executor": self.agent_tools.function_executor,
            "function_validator": self.agent_tools.function_validator,
            "response_format": {"type": "text"},
        }

        if output_validator is not None:
            params["output_validator"] = output_validator

        return params

    async def process_request(self, action, subtask=None,
                              work_item=None, request_details=None,
                              tool_call_log=None,
                              capabilities: dict = None,
                               output_validator = None, container_name=None):
        self.is_processing = True

        params = self._prepare_request_params(
            action, subtask, work_item, request_details,
            capabilities,  output_validator, container_name=container_name
        )

        try:
            completion = None
            completion = await self.llm.llm_interaction_wrapper(**params)
        except MaxRetriesError:            
            if self.model_name_selector.is_backup_model():
                raise MaxRetriesError(f"Max Retries Failed with backup model: {self.model_name_selector.get_selected_model()}")
            else:
                self.model_name_selector.select_backup_model()
                self.logger.warning(f"Failing back to backup model: {self.model_name}")
                try:
                    params["model"] = self.model_name_selector.get_selected_model()
                    completion = await self.llm.llm_interaction_wrapper(**params)
                except MaxRetriesError:
                    raise MaxRetriesError(f"Failed to interact with LLM using backup model: {self.model_name_selector.get_selected_model()}")

        finally:
            self.session_merger.set_session_data(self.system_message, params['messages'])
            self.is_processing = False

        if tool_call_log != None and completion != None:
            if 'tool_call_log' in completion:
                tool_call_log.extend(completion['tool_call_log'])

        if completion and hasattr(completion, 'choices') and len(completion.choices) > 0 and hasattr(completion.choices[0], 'message'):
            content = completion.choices[0].message.content.strip()
        else:
            content = ""
        return content
    
    async def process_request_streaming(self, action, subtask=None,
                                  work_item=None, request_details=None,
                                  tool_call_log=None,
                                  capabilities: dict = None,
                                  output_validator=None,
                                  container_name=None):  # NEW: Container context parameter
        """
        Process a request using streaming LLM capabilities with container context support
        """
        

        self.is_processing = True

        params = self._prepare_request_params(
                action, subtask, work_item, request_details,
                capabilities, output_validator, container_name,
            )


        try:
            # Using the streaming interaction wrapper - yielding each chunk directly
            output = ""
            async for chunk in self.llm.llm_streaming_interaction_wrapper(**params):
                # Directly yield each chunk without accumulation
                yield chunk
                if chunk and isinstance(chunk, str):
                    output += chunk
            messages = params['messages']
            if output and len(output) > 0:
                assistant_message = {
                    "role": "assistant",
                    "content": [
                        {
                            "type": "text",
                            "text": output,
                        },
                    ]
                }
                messages.append(assistant_message)
            self.session_merger.set_session_data(self.system_message, messages=messages)
        finally:
            self.is_processing = False

        # Add tool call logs to the provided log if available
        if tool_call_log is not None and isinstance(chunk,dict) and 'tool_call_log' in chunk:
            tool_call_log.extend(chunk['tool_call_log'])

    def _append_cache_control(self, model, messages):
        # Count the number of messages with cache_control
        # We can add max 4 cache control messages, otherwise we will get an error from the LLM
        count = 0

        if (model is None or not model.startswith("claude")) or not self.enable_cache:
            return

        for message in messages:
            for content in message.get('content', []):
                if isinstance(content, dict) and 'cache_control' in content:
                    count += 1

        if count < MAX_NUMBER_OF_CACHE_CONTROL_MESSAGES:
            messages[-1]['content'][0]['cache_control'] = {"type": "ephemeral"}

    def stop_stream(self):
        """
        Interrupt the LLM streaming operation.
        """
        if self.is_processing:
            self.llm.stop_streaming()
            return True
        return False
    
    def _cleanup_request_details(self, request_details):
        """
        Clean up the request details to avoid confusing the LLM.
        """
        if request_details is not None:
            # Remove large or sensitive data from request_details
            for key in ["base_path", "container_base_path", "container_info", "operation_mode"]:
                if key in request_details:
                    del request_details[key]
            
    def _cleanup_work_item(self, work_item):
        """
        Clean up the work item to avoid confusing the LLM.
        """
        if work_item is not None:
            # Remove large or sensitive data from work_item
            for key in ["manifest_path", ]:
                if key in work_item:
                    del work_item[key]