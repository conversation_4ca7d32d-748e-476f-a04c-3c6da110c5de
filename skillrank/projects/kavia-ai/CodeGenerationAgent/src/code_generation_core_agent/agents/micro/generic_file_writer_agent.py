# generic_file_writer_agent.py

import json
import logging
import asyncio
import uuid
import os
import re
from pathlib import Path

from typing import (
    List, Dict, Set, Optional, Any, Callable, Tuple
)
from dataclasses import dataclass, field

from code_generation_core_agent.agents.micro.agent_system import GenericMicroAgent
from code_generation_core_agent.llm.llm_interface import ValidationStatus, OutputValidation


@dataclass
class OperationResult:
    success: bool
    message: str
    build_error: Optional[str] = None


@dataclass
class ValidationResults:
    valid_operations: List[str] = field(default_factory=list)
    invalid_operations: List[Dict] = field(default_factory=list)



class AsyncFenceParser:
    """
    Async parser for ````edit````, ````write````, ````delete````, and ````noop```` fences.

    Special cases handled transparently:

    • ````noop````           → ignored (single- or two-line)
    • ````delete file="…"````  → processed as an empty-body delete block
    """

    # ───────────── Regexes ─────────────
    # Standard fence opener (edit/write/delete/noop)
    fence_re = re.compile(r'^````(edit|write|delete|noop)(?:\s+file="([^"]+)")?\s*$')
    # Single-line noop: ````noop````
    single_line_noop_re = re.compile(r'^````noop\s*````\s*$')
    # Single-line delete: ````delete file="foo.py"````
    single_line_delete_re = re.compile(r'^````delete\s+file="([^"]+)"\s*````\s*$')
    # Explanation: …
    expl_re = re.compile(r'^Explanation:\s*(.*)$')

    # ───────────── Init ─────────────
    def __init__(
        self,
        send_update: Callable[[str, str, dict], None],
        handle_block: Callable[[str, str, str, Optional[str]], asyncio.Future],
        agent: Optional["GenericMicroAgent"] = None,
    ):
        self.send_update = send_update
        self.handle_block = handle_block
        self.agent = agent

        self.state = "TEXT"
        self.last_expl: Optional[str] = None
        self.buffer = ""
        self.block_type: Optional[str] = None
        self.file_path: Optional[str] = None
        self.block_lines: List[str] = []
        self.in_file_display = False
        self._stream_id: Optional[str] = None

    # ───────────── feed() ─────────────
    async def feed(self, chunk: str) -> List[str]:
        """
        Feed a string chunk (possibly partial) into the parser.

        Returns a list of *fully-formed* user-facing messages that should be
        emitted **after** the chunk is processed.
        """
        out: List[str] = []
        self.buffer += chunk

        # Separate complete lines from a possible trailing partial line
        parts = self.buffer.splitlines(keepends=True)
        complete, self.buffer = [], ""
        for part in parts:
            if part.endswith("\n"):
                complete.append(part)
            else:
                self.buffer = part  # last, incomplete line

        for line in complete:
            stripped = line.rstrip("\n")

            # ─────────────── TEXT MODE ───────────────
            if self.state == "TEXT":
                # Explanation
                m = self.expl_re.match(stripped)
                if m:
                    self.last_expl = m.group(1).strip()
                    continue

                # ── SINGLE-LINE NOOP ──
                if self.single_line_noop_re.match(stripped):
                    self.last_expl = None
                    continue  # swallow whole line

                # ── SINGLE-LINE DELETE ──
                m = self.single_line_delete_re.match(stripped)
                if m:
                    path = m.group(1)
                    stream_id = f"delete_{path}_{uuid.uuid4().hex[:8]}"
                    self.send_update(stream_id, "", {
                        "type": "start", "file_path": path, "operation": "delete"
                    })
                    # empty content
                    msgs = await self.handle_block("delete", path, "", self.last_expl)
                    self.send_update(stream_id, "", {
                        "type": "end", "file_path": path, "operation": "delete"
                    })
                    if self.agent and getattr(self.agent, "doc_mode", False):
                        doc_stream = f"doc_{path}_{uuid.uuid4().hex[:8]}"
                        self.send_update(doc_stream, "", {
                            "type": "document_update", "file_path": path, "operation": "delete"
                        })
                    out.extend(msgs)
                    self.last_expl = None
                    continue  # nothing reaches user

                # ── MULTI-LINE FENCE START? ──
                m = self.fence_re.match(stripped)
                if m:
                    op, path = m.group(1), m.group(2)

                    # noop start → ignore until closed, without user output
                    if op == "noop":
                        self.last_expl = None
                        continue

                    # enter BLOCK mode for edit/write/delete
                    self.state = "BLOCK"
                    self.block_type, self.file_path = op, path
                    self.block_lines = []
                    self._stream_id = f"{op}_{path}_{uuid.uuid4().hex[:8]}"
                    self.send_update(self._stream_id, "", {
                        "type": "start", "file_path": str(path), "operation": op
                    })
                    self.in_file_display = True
                    continue

                # plain text passthrough
                out.append(line)

            # ───── BLOCK MODE ─────
            else:
                if stripped == "````":  # close fence
                    full = "".join(self.block_lines)

                    self.send_update(self._stream_id, "", {
                        "type": "end", "file_path": str(self.file_path),
                        "operation": self.block_type
                    })
                    self.in_file_display = False

                    msgs = await self.handle_block(
                        self.block_type, self.file_path or "", full, self.last_expl
                    )
                    out.extend(msgs)

                    if self.agent and getattr(self.agent, "doc_mode", False):
                        doc_stream = f"doc_{self.file_path}_{uuid.uuid4().hex[:8]}"
                        self.send_update(doc_stream, full, {
                            "type": "document_update",
                            "file_path": str(self.file_path),
                            "operation": self.block_type
                        })

                    # reset
                    self.state = "TEXT"
                    self.last_expl = None
                    self.block_type = None
                    self.file_path = None
                    self.block_lines = []
                    self._stream_id = None
                else:
                    # stream & buffer inside a block
                    self.send_update(self._stream_id, line, {
                        "type": "content", "file_path": str(self.file_path),
                        "operation": self.block_type
                    })
                    self.block_lines.append(line)

        return out

    # ───────────── flush() ─────────────
    async def flush(self) -> List[str]:
        out: List[str] = []
        if self.state == "TEXT" and self.buffer:
            out.append(self.buffer)
            self.buffer = ""
        return out

    # ───────────── reset() ─────────────
    def reset(self):
        """
        Clear any in-flight state so the next feed() starts fresh.
        """
        # Close any open UI stream if needed
        if self.in_file_display and self._stream_id:
            self.send_update(
                self._stream_id, "", {
                    "type": "end",
                    "file_path": str(self.file_path),
                    "operation": self.block_type
                }
            )

        # Full reset
        self.state = "TEXT"
        self.last_expl = None
        self.buffer = ""
        self.block_lines = []
        self.block_type = None
        self.file_path = None
        self.in_file_display = False
        self._stream_id = None


class GenericFileWriterMicroAgent(GenericMicroAgent):
    """Micro agent for handling code writing and modification tasks with fence-based parser."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.doc_mode = False
        self.parser = None
    
    def set_doc_mode(self, doc_mode: bool):
        self.doc_mode = doc_mode

    async def process_request(self, action, subtask=None,
                              work_item=None, request_details=None,
                              tool_call_log=None,
                              capabilities: dict = None, container_name=None) -> str:
        
        validator = CodeAgentValidator(
            self.llm.llm_logger, self.agent_tools, self.base_path, self
        )
        response = await super().process_request(
            action, subtask, work_item, request_details,
            tool_call_log, capabilities, 
            output_validator=validator, container_name=container_name
        )
        self.llm.llm_logger.info(f"Non‑streaming response: {response}")
        # Run final build check
        await validator.finalize()
        return json.dumps(validator.extract_operations_summary(False))

    async def process_request_streaming(self, action, subtask=None,
                                        work_item=None, request_details=None,
                                        tool_call_log=None,
                                        capabilities: dict = None, container_name=None):
        
            
        validator = CodeAgentValidator(
            self.llm.llm_logger, self.agent_tools, self.base_path, self
        )

        self.parser = AsyncFenceParser(
            send_update=self.reporter.send_code_panel_update,
            handle_block=lambda t, p, c, e: asyncio.ensure_future(
                self._handle_block(t, p, c, e, validator)
            ),
            agent =self
        )

        streaming_gen = super().process_request_streaming(
            action, subtask, work_item, request_details,
            tool_call_log, capabilities, 
            output_validator=validator, container_name=container_name
        )        

        interrupted = False

        async for chunk in streaming_gen:
            if isinstance(chunk, str):
                outputs = await self.parser.feed(chunk)
                self.llm.llm_logger.debug(f"chunk {chunk}\noutputs: {outputs}")
                for line in outputs:
                    yield line
            else:
                if chunk.get("final") == True and "LLM STREAM INTERRUPTED" in chunk.get("content", ""):
                    interrupted = True
                    self.parser.reset()
                    break
                else:
                    continue

        final_outs = await self.parser.flush()
        for line in final_outs:
            yield line

        summary = validator.extract_operations_summary(interrupted)
        yield summary

    async def _handle_block(self,
                             op_type: str,
                             file_path: str,
                             full_content: str,
                             explanation: Optional[str],
                             validator: 'CodeAgentValidator'
                             ) -> List[str]:
        line_hash = hash(full_content)

        op = {
            "operation_type": op_type.upper(),
            "file_path": str(file_path),
        }
        if op_type == "write":
            op["content"] = full_content
        elif op_type == "edit":
            op["changes"] = [full_content]
        

        success = await validator.process_operation(op, line_hash)
        # Produce user-facing message
        msg = self._print_op({
            "type": op_type.upper(),
            "file_path": str(file_path),
            "explanation": explanation or "",
            "success": success,
            "message": validator.completion_messages[-1] if validator.completion_messages else ""
        })
        return [msg]

    def _print_op(self, op: Dict[str, Any]) -> str:
        return f"{op.get('explanation','')}\n\n"


class CodeAgentValidator(OutputValidation):
    """
    Validates and applies WRITE / EDIT / DELETE operations – now with proper
    handling for single-line ````noop```` and single-line
    ````delete file="…"```` fences.

    * Skips duplicate operations by hash.
    * Defers build-check until finalize().
    """

    # ─────────────────────────── Init ───────────────────────────
    def __init__(self, logger: logging.Logger, agent_tools: Any,
                 base_path: str,
                 agent: Optional[GenericMicroAgent] = None):
        self.logger = logger
        self.agent_tools = agent_tools
        self.base_path = base_path
        self.agent = agent

        self.processed_operations: Dict[str, List[Dict]] = {}
        self.completion_messages: List[str] = []
        self.has_build_error = False
        self.has_edit_error = False
        self.processed_lines: Set[int] = set()
        self.max_build_errors = 5
        self.build_error_count = 0
        self.noop_seen = False          # at least one noop fence emitted
        self.empty_output_count = 0
        self.max_empty_outputs = 3      # max empty outputs before giving up

    # ────────────────────── Helpers ──────────────────────
    def _abs_path(self, p: str) -> str:
        return p if os.path.isabs(p) else os.path.join(self.base_path, p)

    # ────────────────────── Core write/edit/delete ──────────────────────
    async def process_operation(self, op: Dict, h: Optional[int] = None) -> bool:
        if h and h in self.processed_lines:
            return True                                # duplicate

        typ = op["operation_type"]
        file_path = op.get("file_path", "unknown")

        if typ == "WRITE":
            res = self.agent_tools.tool_set["ContainerFileTools"].write_file(
                self._abs_path(file_path), op.get("content", "")
            )
            return self._handle_result(res, op, file_path, h,
                                       "Wrote to file", "Write to file failed")

        if typ == "EDIT":
            successes = 0
            abs_path = self._abs_path(file_path)
            for change in op.get("changes", []):
                block = f"{abs_path}\n{change}"
                res = self.agent_tools.tool_set["ContainerFileTools"].edit_file(block)
                if res and res.get("status") != "ERROR":
                    successes += 1
                else:
                    self.has_edit_error = True
                    err = res.get("message", "Unknown error") if isinstance(res, dict) else ""
                    self.completion_messages.append(
                        f"Edit failed for {file_path}: {err}. "
                        "Please read the file before issuing another edit. "
                        "After three failed edits, switch to a WRITE block."
                    )
            if successes:
                self.processed_operations.setdefault(file_path, []).append(op)
                self.completion_messages.append(f"Successfully made {successes} edits to {file_path}")
                if h:
                    self.processed_lines.add(h)
                return True
            return False

        if typ == "DELETE":
            res = self.agent_tools.tool_set["ContainerFileTools"].delete_file(
                self._abs_path(file_path)
            )
            return self._handle_result(res, op, file_path, h,
                                       "Deleted file", "Delete file failed")

        return False                                      # unreachable

    def _handle_result(self, result, op, file_path, h, ok_msg, err_msg):
        if isinstance(result, dict) and result.get("BUILD_STATUS_ERROR"):
            self.has_build_error = True
            self.completion_messages.append(
                f"{err_msg}: {file_path} but build failed: "
                f"{result.get('build_errors_after_this_edit', 'Unknown build error')}"
            )
            if h: self.processed_lines.add(h)
            return False

        if result:
            self.processed_operations.setdefault(file_path, []).append(op)
            self.completion_messages.append(f"{ok_msg}: {file_path}")
            if h: self.processed_lines.add(h)
            return True

        return False

    async def validate_mobile_code(self, app_type: str, project_path: str):
        """
        Run mobile-specific code validation after file operations.
        """
        if "AndroidEmulatorTool" not in self.agent_tools.tool_set:
            self.logger.info("AndroidEmulatorTool not available, skipping mobile linting")
            return True, ""
        
        android_tool = self.agent_tools.tool_set["AndroidEmulatorTool"]
        
        try:
            if app_type.lower() == "flutter":
                result = android_tool.analyze_flutter_code(project_path)
            elif app_type.lower() in ["android", "kotlin"]:
                result = android_tool.analyze_android_code(project_path)
            else:
                return True, ""
            
            if result is True:
                self.logger.info(f"Mobile code analysis passed for {app_type}")
                return True, ""
            elif isinstance(result, str):
                if result.startswith("Error"):
                    self.logger.error(f"Mobile code analysis failed: {result}")
                    return False, result
                else:
                    self.logger.warning(f"Mobile code analysis found issues: {result}")
                    return False, result
            else:
                self.logger.warning(f"Unexpected result from mobile analysis: {result}")
                return True, ""
                
        except Exception as e:
            error_msg = f"Exception during mobile code analysis: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg


    def _find_file_in_project(self, filename: str) -> bool:
        """
        Search for a specific file in the project directory.
        """
        try:
            for root, dirs, files in os.walk(self.base_path):
                if filename in files:
                    return True
            return False
        except Exception as e:
            self.logger.warning(f"Error searching for {filename}: {e}")
            return False

    def _detect_app_type(self) -> Tuple[bool, str]:
        """
        Detect if the current project is a mobile project and its type.
        
        Returns:
            Tuple[bool, str]: (is_mobile, app_type)
        """
        # Check for Flutter project first (as Flutter projects also contain Android structure)
        if self._find_file_in_project("pubspec.yaml"):
            return True, "flutter"
        
        # Check for Android project
        if self._find_file_in_project("build.gradle") or self._find_file_in_project("settings.gradle") or self._find_file_in_project("gradlew"):
            return True, "android"
            
        return False, "unknown"

    async def finalize(self):

        if self.agent and getattr(self.agent, "doc_mode", False):
            self.logger.info("Doc mode enabled, skipping final build validation")
            return
        """
        Run validation after all operations including mobile linting.
        """
        try:
            ok, msg = self.agent_tools.tool_set["ContainerFileTools"].validate_build(self.agent.container_workspace)
            if not ok:
                self.has_build_error = True
                self.build_error_count += 1
                self.completion_messages.append(f"Build error detected: {msg}")
        except Exception as e:
            self.logger.error(f"Error during build validation: {e}")

    def _is_absolute_path(self, path_str):
        path = Path(path_str)
        return path.is_absolute()

    def _to_relative_path(self, path_str, base_dir):
        path = Path(path_str)
        base = Path(base_dir)
        return str(path.relative_to(base))

    def extract_operations_summary(self, interrupted) -> Dict[str, Any]:
        ops = []
        for path, lst in self.processed_operations.items():
            # Check if the path is absolute
            if self._is_absolute_path( path):
                # Convert to relative path
                path = self._to_relative_path(path, self.base_path)
            for op in lst:
                ops.append({
                    "operation_type": op["operation_type"],
                    "file_path": str(path),
                    "explanation": op.get("explanation", "")
                })
        status = "PARTIAL" if (self.has_build_error or self.has_edit_error) else "COMPLETE"
        if interrupted:
            status = "INTERRUPTED due to user request"
        return {"completion_status": status, "file_operations": ops}


    async def __call__(self, output: str, is_complete: bool) -> ValidationStatus:

        # ---------- Regexes ----------
        expl_re  = re.compile(r'^Explanation:\s*.*$')
        start_re = re.compile(r'^````(edit|write|delete|noop)(?:\s+file="([^"]+)")?\s*$')
        end_re   = re.compile(r'^````\s*$')
        single_noop_re   = re.compile(r'^````noop\s*````\s*$')
        single_delete_re = re.compile(r'^````delete\s+file="([^"]+)"\s*````\s*$')
        start_any = re.compile(r'^````')

        lines = output.splitlines(keepends=True)

        # ---------- Scan ----------
        in_block = False
        block_type = file_path = None
        buffer: List[str] = []
        expl_line = None

        for line in lines:
            if not in_block:
                # capture Explanation
                if expl_re.match(line):
                    expl_line = line
                    continue

                # single-line noop
                if single_noop_re.match(line):
                    self.noop_seen = True
                    continue  # swallow completely

                # single-line delete
                m_sd = single_delete_re.match(line)
                if m_sd:
                    fp = m_sd.group(1)
                    op = {"operation_type": "DELETE", "file_path": fp}
                    await self.process_operation(op, hash(fp + "_single_delete"))
                    continue

                # multi-line fence start
                m = start_re.match(line)
                if m:
                    in_block, block_type, file_path = True, m.group(1), m.group(2)
                    buffer = []
                    if block_type == "noop":
                        self.noop_seen = True
                    continue

                # strange fence?
                if start_any.match(line):
                    self.has_edit_error = True
                    self.completion_messages.append(
                        "Unknown operation inside the fence. "
                        "Allowed operations are `write`, `edit`, `delete`, or `noop`."
                        "maybe you meant to do a tool call? If so, use the standard tool call syntax\n"
                    )
                    continue

            else:  # inside a multi-line block
                if end_re.match(line):
                    if block_type != "noop":
                        full = "".join(buffer)
                        h = hash(full)
                        if h not in self.processed_lines:
                            op = {
                                "operation_type": block_type.upper(),
                                "file_path": str(file_path)
                            }
                            if block_type == "write":
                                op["content"] = full
                            else:                          # edit / delete
                                op["changes"] = [full]
                            if not await self.process_operation(op, h):
                                self.has_edit_error = True
                    # reset
                    in_block = False
                    block_type = file_path = None
                    buffer = []
                    expl_line = None
                else:
                    if block_type != "noop":
                        buffer.append(line)

        # ---------- Post-processing ----------
        if is_complete and in_block:
            # unfinished block warning (size check left unchanged)
            self.has_edit_error = True
            self.completion_messages.append(
                "Single write/edit blocks cannot exceed 8 K tokens. "
                "Split your response into multiple ≤8 K blocks."
            )

        # no-ops only?
        if is_complete and not self.processed_operations and not self.noop_seen and self.empty_output_count < self.max_empty_outputs:
            self.has_edit_error = True
            self.empty_output_count += 1
            self.completion_messages.append(
                "No valid file operations detected. Include at least one "
                "`write` / `edit` / `delete` / `noop` block before finishing."
            )

        # ---------- Build check ----------
        if is_complete and not self.has_edit_error and self.build_error_count < self.max_build_errors and self.agent and not getattr(self.agent, "doc_mode", False):
            ok, msg = self.agent_tools.tool_set["ContainerFileTools"].validate_build(
                self.agent.container_workspace
            )
            if not ok:
                self.has_build_error = True
                self.build_error_count += 1
                self.completion_messages.append(f"Build error detected: {msg}")

        # ---------- Mobile check ----------
        if is_complete and not self.has_edit_error:
            try:
                is_mobile_framework, app_type = self._detect_app_type()
                
                if is_mobile_framework:
                    self.logger.info(f"Running mobile linting for {app_type} project at {self.base_path}")
                    analysis_ok, analysis_msg = await self.validate_mobile_code(app_type, self.base_path)
                    
                    if not analysis_ok:
                        self.has_build_error = True
                        self.completion_messages.append(f"Mobile code analysis failed: {analysis_msg}")
                        self.logger.warning(f"Mobile linting failed: {analysis_msg}")
                    else:
                        self.logger.info("Mobile linting passed successfully")
            except Exception as e:
                self.logger.error(f"Error during mobile linting: {e}")
                self.has_build_error = True
                self.completion_messages.append(f"Mobile linting skipped due to error: {str(e)}")

        # ---------- Assistant message ----------
        assistant_lines = lines
        if in_block:  # drop unterminated tail
            drop_idx = max(i for i, L in enumerate(lines) if start_re.match(L))
            if drop_idx > 0 and expl_re.match(lines[drop_idx - 1]):
                drop_idx -= 1
            assistant_lines = lines[:drop_idx]

        assistant_text = "".join(assistant_lines)

        state = ("complete"
                if is_complete and not (self.has_build_error or self.has_edit_error)
                else "partial")
        comp_msg = "\n".join(self.completion_messages) or "processing complete"

        # reset message buffers
        self.completion_messages = []
        self.has_build_error = self.has_edit_error = False

        status = ValidationStatus(
            state=state,
            completion_message=comp_msg,
            assistant_message=assistant_text
        )

        if self.agent and getattr(self.agent, "parser", None):
            self.agent.parser.reset()

        return status