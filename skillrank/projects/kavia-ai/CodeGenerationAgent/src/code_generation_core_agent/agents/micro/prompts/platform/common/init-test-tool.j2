Given the task of configuring a development environment for {{ work_item.component_name }}
({{ work_item.description }}) and given the following json object describing the current app
project folder determine the proper command that can be issued with the
start_long_running_process tool to run the app tests. Use the start_long_running_process its output to verify that the command
is correct. If the start_long_running_process output shows tests being run then you are done. Cleanup with the terminate_process tool.
Report the command details in your final response. Here is the json object giving the current project folder:
{{ folder_structure }}

Once the correct command has been determined, create the file "{{ base_path }}/.init/.init-test-tool" and
write the command to it in a JSON object with the following structure:
{
    "command": "<proper command to run app tests>",
    "working_directory": "<proper working directory>"
}

Only and if the code requires a configure build step (C++, Rust, etc) then the required build command should be written to the
file "{{ base_path }}/.init/.init-build-cmd" as one linerer in a JSON object with the following structure:
{
   "command": "<compilation validation command>",
        // Validation command should be a one liner to validate the build/compilation of the code
        // Examples:
        // For C++: cmake -S . -B build -Wno-dev && cmake --build build --config Release -- -w
        // For Rust: cargo build --all-targets  
        // For React: npm install && npx tsc --noEmit && npm test -- --ci
   "working_directory": "<proper working directory>"
}

Please format your final response as a JSON object with the following structure:
{
    "actions": "<summary of actions taken>"
}