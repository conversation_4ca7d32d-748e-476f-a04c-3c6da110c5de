import os
import yaml
from typing import Dict
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType

def unique_list(seq):
    seen = set()
    result = []
    for item in seq:
        if item not in seen:
            seen.add(item)
            result.append(item)
    return result
class PlatformConfigLoader:
    def __init__(self, base_config_path: str):
        self.base_config_path = base_config_path
        self.platform_configs: Dict[ApplicationType, dict] = {}
        self._load_platforms()

    
    def merge_configs(self, base: dict, override: dict) -> dict:
        """Deep merge two configurations with override taking precedence,
        and extend lists for keys under 'file_lister' with deduplication."""
        merged = base.copy()
        for key, value in override.items():
            # Special handling for "tools": override entirely.
            if key == "tools":
                merged[key] = value
                continue

            # Special extension behavior for "file_lister"
            if key == "file_lister":
                if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        if sub_key in merged[key]:
                            # If both values are lists, concatenate and deduplicate.
                            if isinstance(merged[key][sub_key], list) and isinstance(sub_value, list):
                                combined = merged[key][sub_key] + sub_value
                                merged[key][sub_key] = unique_list(combined)
                            elif isinstance(merged[key][sub_key], dict) and isinstance(sub_value, dict):
                                merged[key][sub_key] = self.merge_configs(merged[key][sub_key], sub_value)
                            else:
                                merged[key][sub_key] = sub_value
                        else:
                            merged[key][sub_key] = sub_value
                else:
                    merged[key] = value
                continue

            # General recursive merge for dicts.
            if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
                merged[key] = self.merge_configs(merged[key], value)
            else:
                merged[key] = value
        return merged

    def _load_yaml(self, path: str) -> dict:
        """Load YAML file with inheritance support"""
        with open(path, 'r') as f:
            config = yaml.safe_load(f)
            
        if 'include' in config:
            includes = config.pop('include')
            if not isinstance(includes, list):
                includes = [includes]
                
            for include in includes:
                include_path = os.path.join(self.base_config_path, include)
                included_config = self._load_yaml(include_path)
                config = self.merge_configs(included_config, config)
                
        return config

    def _load_platforms(self):
        """Load all platform configurations"""
        common_path = os.path.join(self.base_config_path, "platform", ApplicationType.COMMON.value, "platform.yaml")
        if not os.path.exists(common_path):
            raise FileNotFoundError(f"Common platform config not found at: {common_path}")
        self.platform_configs[ApplicationType.COMMON] = self._load_yaml(common_path)

        platform_dir = os.path.join(self.base_config_path, "platform")
        for platform_name in os.listdir(platform_dir):
            if platform_name !=  ApplicationType.COMMON.value and os.path.isdir(os.path.join(platform_dir, platform_name)):
                config_path = os.path.join(platform_dir, platform_name, "platform.yaml")
                if os.path.exists(config_path):
                    self.platform_configs[ApplicationType.from_string(platform_name)] = self._load_yaml(config_path)
                else:
                    for framework in os.listdir(os.path.join(platform_dir, platform_name)):
                        framework_path = os.path.join(platform_dir, platform_name, framework)
                        if os.path.isdir(framework_path):
                            config_path = os.path.join(framework_path, "platform.yaml")
                            if os.path.exists(config_path):
                                self.platform_configs[FrameworkType.from_string(framework)] = self._load_yaml(config_path)
                            else:
                                raise FileNotFoundError(f"Platform config not found at: {config_path}")

    def get_platform_config(self, 
                   platform: ApplicationType = ApplicationType.COMMON,
                   framework: FrameworkType = FrameworkType.DEFAULT
                   ) -> dict:
        """Get merged configuration for specified platform"""

        if platform == ApplicationType.GENERIC:
            return self.platform_configs[ApplicationType.COMMON]
        
        if platform == ApplicationType.MOBILE:
            return self.platform_configs[framework]
        else:
            if platform not in self.platform_configs:
                #raise ValueError(f"Unknown platform/framework: {platform}/{framework}")
                print(f"Warning: Unknown platform/framework: {platform}/{framework}. Falling back to COMMON config.")
                return self.platform_configs[ApplicationType.COMMON]

            return self.platform_configs[platform]
