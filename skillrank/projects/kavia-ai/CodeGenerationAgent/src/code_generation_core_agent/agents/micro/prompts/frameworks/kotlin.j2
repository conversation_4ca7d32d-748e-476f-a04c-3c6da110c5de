=== Kotlin Development CRITICAL rules ===:
- Generate traditional Android code that works with .dcl files and any Kotlin version
- Do NOT use Jetpack Compose
- Write all dependencies with EXPLICIT versions: "dependency:1.12.0" not "dependency:"
- Place UI in res/layout/*.xml files, NOT in Kotlin composables
- Use AndroidX libraries without Compose dependencies
- Use findViewById or View Binding for UI references
- Use traditional Android Views with XML layouts
