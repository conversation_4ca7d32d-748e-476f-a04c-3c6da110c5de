import logging
from code_generation_core_agent.agents.micro.dsa.ContainerSetupPlan import ContainerSetupPlan
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.micro.dsa.ContainerInfo import ContainerInfo
from code_generation_core_agent.agents.micro.dsa.ContainerSetupLevel import ContainerSetupLevel
from code_generation_core_agent.agents.micro.dsa.prompt_utils import render_jinja_template
from typing import Dict, Any, List
import json
from code_generation_core_agent.agents.setup_logger import setup_logger


class DevOpsAnalystAgent:
    """
    DevOps Analyst Agent: Reviews and critiques a container setup plan, suggesting improvements or corrections.
    """
    def __init__(self, base_path: str, model_name: str = "gpt-4"):
        self.logger = setup_logger("DevOpsAnalystAgent", base_path)
        self.model_name = model_name
        self.llm = LLMInterface(
            llm_api_key=None,
            session_dir=base_path,
            instance_name="devops_analyst"
        )



    async def review_plan(self, plan : ContainerSetupPlan, min_recommendation_level: ContainerSetupLevel = ContainerSetupLevel.CRITICAL) -> Dict[str, Any]:
        """
        Review the given setup plan, returning feedback and recommendations.
        The container_info is accessed from the plan itself.
        """
        # Accept both dict and ContainerSetupPlan for plan
        plan_json = json.dumps(plan.to_dict(exclude_container_info=True), indent=2)

        # Get container_info from the plan
        container_info = plan.container_info
        required_levels = ContainerSetupLevel.get_levels_at_or_above(ContainerSetupLevel.CRITICAL)

        # Prepare template variables
        template_vars = {
            "container_info": container_info,
            "plan_json": plan_json,
            "min_recommendation_level": min_recommendation_level,
            "required_levels": required_levels
        }

        # Render the prompt using the Jinja template
        prompt = render_jinja_template("setup_plan_review.j2", template_vars, self.logger)
        self.logger.info(f"setup plan review prompt: \n{prompt}")
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=prompt,
            system_prompt="You are a Senior DevOps Analyst and QA Reviewer specializing in containerized development environments.",
            model=self.model_name,
            response_format={"type": "json_object"}
        )
        review_result = completion.choices[0].message.content
        review_data = json.loads(review_result)
        
        # Map QA results back to the plan with qa_ prefix
        plan.qa_approved = review_data.get("approved", False)
        plan.qa_summary = review_data.get("summary", "")
        plan.qa_issues = review_data.get("issues_found", [])
        plan.qa_recommendations = review_data.get("recommendations", [])
        
        return plan

    async def review_consolidated_script(self, script_content: str, container_info: ContainerInfo, 
                                       min_recommendation_level: ContainerSetupLevel = ContainerSetupLevel.CRITICAL) -> Dict[str, Any]:
        """
        Review a consolidated setup script, returning approval status and feedback.
        
        Args:
            script_content: The bash script content to review
            container_info: Container information for context
            min_recommendation_level: Minimum level of recommendations to consider for approval
            
        Returns:
            Dict containing approval status and feedback:
            {
                "approved": bool,
                "summary": str,
                "issues_found": List[str],
                "recommendations": List[Dict[str, str]]
            }
        """
        required_levels = ContainerSetupLevel.get_levels_at_or_above(min_recommendation_level)

        # Prepare template variables
        template_vars = {
            "container_info": container_info,
            "script_content": script_content,
            "min_recommendation_level": min_recommendation_level,
            "required_levels": required_levels
        }

        # Render the prompt using the Jinja template
        prompt = render_jinja_template("setup_script_review.j2", template_vars, self.logger)
        self.logger.info(f"consolidated script review prompt: \n{prompt}")
        
        completion = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=prompt,
            system_prompt="You are a Senior DevOps Analyst and QA Reviewer specializing in containerized development environments.",
            model=self.model_name,
            response_format=None  # Allow free text response to parse APPROVED: header
        )
        
        review_result = completion.choices[0].message.content
        self.logger.info(f"consolidated script review result: \n{review_result}")
        
        # Parse the response to extract approval status and feedback
        return self._parse_consolidated_script_review(review_result)

    def _parse_consolidated_script_review(self, review_result: str) -> Dict[str, Any]:
        """
        Parse the review result to extract approval status and return the rest as raw text.
        
        Args:
            review_result: The raw review response from the LLM
            
        Returns:
            Dict containing approval status and raw review text
        """
        lines = review_result.strip().split('\n')
        
        # Extract approval status from first line
        approved = False
        if lines and lines[0].strip().startswith('APPROVED:'):
            approved_text = lines[0].strip().split(':', 1)[1].strip().lower()
            approved = approved_text == 'true'
        
        # Return the rest of the review as raw text (skip the APPROVED line)
        raw_review_text = '\n'.join(lines[1:]).strip()
        
        return {
            "approved": approved,
            "raw_review_text": raw_review_text
        } 