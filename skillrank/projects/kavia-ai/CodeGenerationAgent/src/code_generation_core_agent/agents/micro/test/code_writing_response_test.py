import json
import os
from code_generation_core_agent.agents.micro.code_writing_micro_agent import CodeWritingMicroAgent
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools
from pathlib import Path
import asyncio
import logging

class MockAgentTools:
    """Mock agent tools class that provides the required toolset"""
    def __init__(self, base_path, logger):
        self.base_path = base_path
        self.logger = logger
        self.tool_set = {}
        
        
        # Initialize Docker executor
        docker_config = {
            "container_name": "kavia_test_container",
        }
        self.executor = DockerExecutor(session_dir=self.base_path, **docker_config)
        
        # Initialize ContainerFileTools
        callback_functions = {
            "terminal_output_callback": print,
            "codeview_callback": None,
            "agent_message_callback": None
        }
        self.container_file_tools = ContainerFileTools(
            callback_functions=callback_functions,
            base_path=self.base_path,
            logger=self.logger,
            executor=self.executor
        )

        self.tool_set[self.container_file_tools.get_tool_name()] = self.container_file_tools
    
    def get_tools(self):
        """Return the toolset dictionary"""
        return {
            "executor": self.executor,
            "ContainerFileTools": self.container_file_tools
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        #if self.executor:
            #await self.executor.shutdown()

def setup_logger():
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger("test_code_writing_agent")

def setup_test_environment(base_path):
    test_dir = Path(os.path.join(base_path, "test"))
    test_dir.mkdir(exist_ok=True, parents=True)
    
    sample_file_path = os.path.join(base_path, "test", "sample_existing_file.py")
    with open(sample_file_path, "w") as f:
        f.write("def old_function():\n    print('old')\n")
    
    return test_dir

def cleanup_test_environment(base_path):
    import shutil
    test_path = os.path.join(base_path, "test")
    if os.path.exists(test_path):
        shutil.rmtree(test_path)

def load_test_data():
    test_data_path = os.path.join(os.path.dirname(__file__), "code_writing_agent_test_data.json")
    with open(test_data_path, "r") as f:
        return json.load(f)

class TestCodeWritingAgent:
    def __init__(self):
        self.base_path = "/tmp/code_writing_test"
        self.base_path = "/Users/<USER>/Work/Kavia/workarea/kavia/codegen-github-explorer-4"
        self.logger = setup_logger()
        
        # Create mock agent tools
        self.mock_tools = MockAgentTools(self.base_path, self.logger)
        
        # Create agent instance with mock tools
        self.agent = CodeWritingMicroAgent(
            llm=None,
            base_path=self.base_path,
            logger=self.logger,
            model_name="test-model",
            agent_tools=self.mock_tools,
            prompt_template="",
            tools_to_use=[],
            setup_steps=[],
            agent_factory=None
        )

    async def run_test(self):
        try:
            # Setup test environment
            print("Setting up test environment...")
            setup_test_environment(self.base_path)
            
            # Load test data
            print("Loading test data...")
            test_data = load_test_data()
            
            # Update file paths in test data
            for op in test_data["file_operations"]:
                if "file_path" in op:
                    op["file_path"] = os.path.join(self.base_path, op["file_path"])
            
            # Process file operations
            print("Processing file operations...")
            results = await self.agent._process_file_operations(test_data["file_operations"])
            
            # Display and verify results
            self.display_results(results)
            self.verify_file_contents()
            
        except Exception as e:
            print(f"Error during test execution: {str(e)}")
            raise
        
        finally:
            await self.cleanup()

    async def cleanup(self):
        print("\nCleaning up test environment...")
        cleanup_test_environment(self.base_path)
        await self.mock_tools.cleanup()

    def display_results(self, results):
        print("\nTest Results:")
        print("=" * 50)
        for result in results:
            print("\nOperation:", result.get("operation"))
            print("File Path:", result.get("file_path"))
            print("Status:", result.get("status"))
            if "error" in result:
                print("Error:", result.get("error"))
            if "result" in result:
                print("Result:", result.get("result"))
            print("-" * 50)

    def verify_file_contents(self):
        print("\nVerifying File Contents:")
        print("=" * 50)
        
        files_to_check = [
            os.path.join(self.base_path, "test", "sample_new_file.py"),
            os.path.join(self.base_path, "test", "sample_existing_file.py")
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                print(f"\nContents of {file_path}:")
                with open(file_path, "r") as f:
                    print(f.read())
            else:
                print(f"\nFile {file_path} does not exist")
            print("-" * 50)

def main():
    test = TestCodeWritingAgent()
    asyncio.run(test.run_test())

if __name__ == "__main__":
    main()