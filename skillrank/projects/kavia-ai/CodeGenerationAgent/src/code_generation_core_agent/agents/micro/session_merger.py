from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator, SummarizerType, \
    parse_json_with_cleanup


class SessionMerger:
    """
    A class to merge multiple sessions into a single session.
    """
    def __init__(self):
        self.session_data = ["<Empty session data>"]
        self.previous_system_messages = []
        self.truncator = OutputTruncator(10000, summarizer_type=SummarizerType.SESSION_CONTINUATION_SUMMARIZER)

    def set_session_data(self, system_messages, messages ):
        """
        Set the session data to be merged.

        Args:
            session_data (str): The session data to be merged.
        """
        self.session_data = messages
        self.previous_system_messages = system_messages

    def _format_session_data(self, user_messages):
        import json

        session_data = {
            "chat_history": self.session_data,
            "new_user_message": user_messages,
        }

        return json.dumps(session_data, indent=2, ensure_ascii=False) + "\n"

    def try_merge(self, system_messages, user_messages):

        # Step 1. Compare the old system messages with the new ones
        # If system messages are different, we cannot merge.

        if self.previous_system_messages != system_messages:
            return False

        # Step 2. Format the session data
        session_data = self._format_session_data(user_messages)
        # Print out the first 1000 bytes of the session data for debugging

        result = self.truncator.truncate_output(session_data, allways_truncate=True)

        try:
            result = parse_json_with_cleanup(result)
            if result['is_continuation']:
                self.session_data.append(user_messages)
                return self.session_data
            else:
                return None
        except Exception as e:
            return None



