from abc import ABC, abstractmethod

from code_generation_core_agent.agents.tools.AgentPreProcessor import FunctionCallDescription
import logging
import os


class CodeGenerationControl(ABC):
    @abstractmethod
    def pause(self):
        pass

    @abstractmethod
    def resume(self):
        pass

    @abstractmethod
    def stop(self):
        pass

    @abstractmethod
    def check_status(self):
        pass

    @abstractmethod
    def reset(self):
        pass


class CodeGenerationReporter(ABC):
    @abstractmethod
    def progress_callback(self, status, total_tasks, latest_result, request_context):
        pass

    @abstractmethod
    def terminal_output_callback(self, output):
        pass

    @abstractmethod
    def browser_output_callback(self, image):
        pass

    @abstractmethod
    def cost_update_callback(self, agent_costs, total_cost):
        pass

    def function_call_callback(self, call_description: FunctionCallDescription):
        """
        Return the function call description to the user.
        """
        pass

    @abstractmethod
    def task_start_callback(self, task_description):
        pass

    @abstractmethod
    def user_query_callback(self, questions):
        """
        Callback to handle user queries.
        :param questions: List of questions for the user

        :return:
            Updated questions with answers
        """
        pass



