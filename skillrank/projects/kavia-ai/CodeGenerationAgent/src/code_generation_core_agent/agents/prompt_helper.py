import base64
import re
import tempfile
import requests
import os
import atexit


def _encode_file_to_base64(file_path):
    base64_data = "<no data>"
    try:
        with open(file_path, 'rb') as file:
            file_contents = file.read()
        base64_encoded = base64.b64encode(file_contents)
        base64_data = base64_encoded.decode('utf-8')
    except Exception as e:
        print(f"Error: exception during base64 encoding {e}")
        pass
    return base64_data


def _tagged_item_data(item_type, data, file_format="html"):
    item_data = "<no_data>"
    if item_type == 'image' or item_type == 'grafik':  # Handle both 'image' and 'grafik' types
        if file_format == 'text':
            item_data = f"![{item_type}]({data})"
        elif file_format == 'html':
            item_data = f"<img src=\"{data}\" width=\"40\" height=\"40\"/>"
        elif file_format == 'prompt':
            if data in _imageMap:
                data = _imageMap[data]
            # Unescape URL backslashes
            data = data.replace('\/', '/')
            image_name = data.lower()
            image_format = 'none'
            if image_name.endswith('.png'):
                image_format = 'png'
            elif image_name.endswith('.jpg') or image_name.endswith('.jpeg'):
                image_format = 'jpeg'
            if image_name.startswith("http://") or image_name.startswith("https://"):
                item_data = {'type': 'image_url', 'image_url': {'url': f'{data}'}}
            else:
                try:
                    if not os.path.exists(data):
                        raise Exception("File does not exist")

                    image_base64 = _encode_file_to_base64(data)
                    item_data = {'type': 'image_url',
                                'image_url': {'url': f'data:image/{image_format};base64,{image_base64}'}}
                except Exception:
                    item_data = {'type': 'image_url', 'image_url': {'url': 'data:image/none;base64,'}}
        elif file_format == 'strip':
            item_data = ""
    return item_data


def prompt_prepare(text, format="html"):
    # Regular expression pattern to match the tagged format: ![<type>](<data>)
    pattern = r"!\[(?P<type>[^\]]+)\]\((?P<data>[^\)]+)\)"

    def replace_tag(match):
        type_str = match.group('type')
        data_str = match.group('data')
        return _tagged_item_data(type_str, data_str, format)

    result_text = re.sub(pattern, replace_tag, text)
    return result_text


def prompt_get_tagged_items(text):
    items = []
    # Regular expression pattern to match the tagged format: ![<type>](<data>)
    pattern = r"!\[(?P<type>[^\]]+)\]\((?P<data>[^\)]+)\)"
    matches = re.findall(pattern, text)
    for match in matches:
        type = match[0]
        data = match[1]
        item = _tagged_item_data(type, data, "prompt")
        items.append(item)
    return items


def prompt_prepare_messages(messages):
    for message in messages:
        if isinstance(message['content'], str):
            items = prompt_get_tagged_items(message['content'])
            if len(items):
                content = []
                stripped = prompt_prepare(message['content'], "strip")
                content.append({'type': 'text', 'text': f'{stripped}'})
                for item in items:
                    content.append(item)
                message['content'] = content


def _download_image(urlString):
    image_name = urlString
    file_type = None
    if urlString.lower().endswith(('.jpg', '.jpeg')):
        file_type = '.jpeg'
    if urlString.lower().endswith('.png'):
        file_type = '.png'
    if file_type:
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_type)
        response = requests.get(urlString)
        if response.status_code == 200:
            # Write the image content to the temporary file
            with open(temp_file.name, 'wb') as f:
                f.write(response.content)
            print(f"Image downloaded and saved to {temp_file.name}")
            image_name = temp_file.name
    return image_name


def prompt_prepare_image(image_url):
    local_file = _download_image(image_url)
    if local_file:
        _imageMap[local_file] = image_url
    return local_file


_imageMap = {}


def _cleanup_image_map():
    for filename in _imageMap.keys():
        try:
            if os.path.exists(filename):
                print(f"Deleting temp file {filename}: {e}")
                os.remove(filename)
        except Exception as e:
            print(f"Error deleting file {filename}: {e}")


atexit.register(_cleanup_image_map)
