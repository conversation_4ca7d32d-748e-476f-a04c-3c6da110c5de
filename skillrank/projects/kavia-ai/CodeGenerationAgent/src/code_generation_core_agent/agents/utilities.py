from enum import Enum
import logging
import os
from abc import ABC, abstractmethod
from typing import Optional

from code_generation_core_agent.agents.framework.task_tracker import TaskTracker
from code_generation_core_agent.agents.framework.document_manager import DocMgr
from code_generation_core_agent.agents.tools.AgentPreProcessor import Agent<PERSON><PERSON><PERSON>rocessor, FunctionCallDescription
from code_generation_core_agent.agents.tools.android_emulator_tools import AndroidEmulatorTool
from code_generation_core_agent.agents.tools.combined_tools import CombinedTools
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools
from code_generation_core_agent.agents.tools.search_and_download_tool import SearchAndDownloadTool
from code_generation_core_agent.agents.tools.supabase_tool import SupabaseTool
from code_generation_core_agent.agents.tools.environment_summary import EnvironmentSummary
from code_generation_core_agent.agents.tools.git_tool import GitTools
from code_generation_core_agent.agents.tools.knowledge_tools import KnowledgeTools
from code_generation_core_agent.agents.tools.review_status_tools import ReviewStatusTools
from code_generation_core_agent.agents.tools.strategic_analysis_tool import Strategic<PERSON><PERSON>ys<PERSON>Tool
from code_generation_core_agent.agents.tools.tracking.shell_command_call_tracker import ShellCommandCallTracker
from code_generation_core_agent.agents.tools.visible_shell_tools import VisibleShellTools
from code_generation_core_agent.agents.tools.web_browser import WebBrowser
from code_generation_core_agent.agents.tools.work_input_discovery_tool import WorkInputDiscovery
from code_generation_core_agent.agents.tools.work_item_info_retriever import WorkItemInfoRetriever
from code_generation_core_agent.agents.tools.ios_tools import IOSTools
from code_generation_core_agent.agents.tools.dependencies import DependencyTools
from code_generation_core_agent.config import config

import platform

from code_generation_core_agent.docker_version import  KAVIA_CONTAINER_VERSION


# Function to get the container name based on the architecture
def get_container_name():
    architecture = platform.machine()

    arch_suffix = None
    if architecture in ['arm64', 'aarch64']:
        arch_suffix = 'arm64' # Mobile only works amd64 architecture, change to amd64 for testing purposes ONLY
    elif architecture in ['x86_64', 'amd64']:
        arch_suffix = 'amd64'
    else:
        raise ValueError(f"Unsupported architecture: {architecture}")

    container_key = f'container_name_{arch_suffix}'

    if config.has_option('CONTAINER', container_key):
        return config.get('CONTAINER', container_key) + ":" + KAVIA_CONTAINER_VERSION + f"-{arch_suffix}"
    else:
        raise ValueError(f"Container name for architecture '{architecture}' not found in config.")

def pull_current_container():
    """
    Pull current container and print progress
    :return:
    """
    container_name = get_container_name()
    print(f"Pulling container {container_name}. This will take some time...")
    pull_output = os.popen(f"docker pull {container_name}").read()
    print(pull_output)
    if "Error" in pull_output:
        raise RuntimeError(f"Failed to pull container {container_name}.")
    else:
        print(f"Container {container_name} pulled successfully.")

class PrintLogger:
    """A simple logger that falls back to print statements."""

    def debug(self, message):
        print(f"DEBUG: {message}")

    def info(self, message):
        print(f"INFO: {message}")

    def warning(self, message):
        print(f"WARNING: {message}")

    def error(self, message):
        print(f"ERROR: {message}")

    def critical(self, message):
        print(f"CRITICAL: {message}")


class ApplicationType(Enum):
    GENERIC = "generic"
    COMMON = "common"
    MOBILE = "mobile"
    WEB = "web"
    BACKEND = "backend"
    DATABASE = "database"

    @staticmethod
    def map_to_known_labels(self, label: str):
        """
        Map to know labels like IOS value ios to "iOS" and android to "Android
        """
        if label == "mobile":
            return "Mobile"
        if label == "common":
            return "Common"
        if label == "web":
            return "Web"
        if label == "backend":
            return "Backend"
        if label == "generic":
            return "Generic"
        return label

    @classmethod
    def from_string(cls, value: str) -> Optional['ApplicationType']:
        if not value:
            return None

        value_lower = value.lower()
        try:
            return cls(value_lower)
        except ValueError:
            try:
                return getattr(cls, value_lower.upper())
            except AttributeError:
                return None


class FrameworkType(Enum):
    ANGULAR = "angular"
    ASTRO = "astro"
    DEFAULT = "default"
    NEXTJS = "nextjs"
    QWIK = "qwik"
    NUXT = "nuxt"
    REACT = "react"
    REACT_NATIVE = "reactnative"
    SLIDEV = "slidev"
    REMIX = "remix"
    REMOTION = "remotion"
    SVELTE = "svelte"
    TYPESCRIPT = "typescript"
    VITE = "vite"
    VUE = "vue"
    EXPRESS = "express"
    DJANGO = "django"
    DOTNET = "dotnet"
    FASTAPI = "fastapi"
    FLASK = "flask"
    SPRINGBOOT = "springboot"
    ANDROID = "android"
    FLUTTER = "flutter"
    IOS = "ios"
    KOTLIN = "kotlin"
    LIGHTNINGJS = "lightningjs"
    MONGODB = "mongodb"
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"


    @staticmethod
    def get_frameworks_for_platform(platform_type: ApplicationType):
        frameworks_map = {
            ApplicationType.MOBILE: [
                FrameworkType.DEFAULT,
                FrameworkType.ANDROID,
                FrameworkType.FLUTTER,
                FrameworkType.IOS,
                FrameworkType.KOTLIN,
                FrameworkType.REACT_NATIVE,
            ],
            ApplicationType.COMMON: [FrameworkType.DEFAULT],
            ApplicationType.WEB: [
                FrameworkType.DEFAULT,
                FrameworkType.ASTRO,
                FrameworkType.LIGHTNINGJS,
                FrameworkType.NEXTJS,
                FrameworkType.QWIK,
                FrameworkType.REACT,
                FrameworkType.SLIDEV,
                FrameworkType.SVELTE,
                FrameworkType.TYPESCRIPT,
                FrameworkType.VITE,
                FrameworkType.VUE,
                FrameworkType.ANGULAR,
                FrameworkType.NUXT,
                FrameworkType.REMIX,
                FrameworkType.REMOTION,
            ],
            ApplicationType.BACKEND: [
                FrameworkType.DEFAULT,
                FrameworkType.EXPRESS,
                FrameworkType.DJANGO,
                FrameworkType.DOTNET,
                FrameworkType.FASTAPI,
                FrameworkType.FLASK,
                FrameworkType.SPRINGBOOT,
            ],
            ApplicationType.GENERIC: [
                FrameworkType.DEFAULT,
            ]
        }
        return frameworks_map.get(platform_type, [FrameworkType.DEFAULT])

    @staticmethod
    def map_to_known_labels(label: str):
        """
        Map known labels to user-friendly display names
        """
        if label == "angular":
            return "Angular"
        if label == "nuxt":
            return "Nuxt"
        if label == "default":
            return "Default"
        if label == "astro":
            return "Astro"
        if label == "nextjs":
            return "NextJS"
        if label == "qwik":
            return "Qwik"
        if label == "react":
            return "React"
        if label == "reactnative":
            return "ReactNative"
        if label == "slidev":
            return "Slidev"
        if label == "remix":
            return "Remix"
        if label == "remotion":
            return "Remotion"
        if label == "svelte":
            return "Svelte"
        if label == "typescript":
            return "Typescript"
        if label == "vite":
            return "Vite"
        if label == "vue":
            return "Vue"
        if label == "express":
            return "Express"
        if label == "django":
            return "Django"
        if label == "dotnet":
            return "DotNet"
        if label == "fastapi":
            return "FastAPI"
        if label == "flask":
            return "Flask"
        if label == "springboot":
            return "SpringBoot"
        if label == "kotlin":
            return "Kotlin"
        if label == "lightningjs":
            return "LightningJS"
        if label == "android":
            return "Android"
        if label == "mongodb":
            return "MongoDB"
        if label == "mysql":
            return "MySQL"
        if label == "postgresql":
            return "PostgreSQL"
        if label == "sqlite":
            return "SQLite"
        return label

    @classmethod
    def from_string(cls, value: str) -> Optional['FrameworkType']:
        if not value:
            return None
        value_lower = value.lower()
        try:
            return cls(value_lower)
        except ValueError:
            try:
                return getattr(cls, value_lower.upper())
            except AttributeError:
                return None
    @staticmethod
    def get_database_frameworks():
        """
        Returns a list of database frameworks.
        """
        return [
            FrameworkType.POSTGRESQL,
            FrameworkType.MYSQL,
            FrameworkType.SQLITE,
            FrameworkType.MONGODB
        ]

    @staticmethod
    def get_backend_frameworks():
        """
        Returns a list of backend frameworks.
        """
        return [
            FrameworkType.FASTAPI,
            FrameworkType.DJANGO,
            FrameworkType.DOTNET,
            FrameworkType.EXPRESS,
            FrameworkType.FLASK,
            FrameworkType.SPRINGBOOT,
        ]

    @staticmethod
    def get_mobile_frameworks():
        """
        Returns a list of mobile frameworks.
        """
        return [
            FrameworkType.FLUTTER,
            FrameworkType.ANDROID,
            FrameworkType.IOS,
            FrameworkType.KOTLIN,
            FrameworkType.REACT_NATIVE,
        ]

    @staticmethod
    def get_frontend_frameworks():
        """
        Returns a list of frontend frameworks.
        """
        return [
            FrameworkType.REACT,
            FrameworkType.VUE,
            FrameworkType.ANGULAR,
            FrameworkType.SVELTE,
            FrameworkType.NEXTJS,
            FrameworkType.NUXT,
            FrameworkType.QWIK,
            FrameworkType.ASTRO,
            FrameworkType.REMIX,
            FrameworkType.REMOTION,
            FrameworkType.VITE,
            FrameworkType.SLIDEV,
            FrameworkType.TYPESCRIPT,
            FrameworkType.LIGHTNINGJS,
        ]

class TaskExecutionControl(ABC):
    @abstractmethod
    def pause(self):
        pass

    @abstractmethod
    def resume(self):
        pass

    @abstractmethod
    def stop(self):
        pass

    @abstractmethod
    def check_status(self):
        pass

    @abstractmethod
    def reset(self):
        pass


class TaskExecutionReporter(ABC):
    @abstractmethod
    def progress_callback(self, status, total_tasks, latest_result, request_context):
        pass

    @abstractmethod
    def terminal_output_callback(self, output):
        pass

    @abstractmethod
    def browser_output_callback(self, image):
        pass

    @abstractmethod
    def cost_update_callback(self, agent_costs, total_cost):
        pass

    def function_call_callback(self, call_description: FunctionCallDescription):
        """
        Return the function call description to the user.
        """
        pass

    def codeview_callback(self, code: str):
        pass

    @abstractmethod
    def task_start_callback(self, task_description):
        pass

    @abstractmethod
    def user_query_callback(self, questions):
        pass

    @abstractmethod
    def send_agent_message(self, message):
        pass

    @abstractmethod
    def get_user_input(self, prompt):
        pass

    @abstractmethod
    def send_code_panel_update(self, stream_id, content, metadata=None):
        pass

    @abstractmethod
    def app_state_callback(self, url, state=None):
        pass

    @abstractmethod
    def universal_init_status_callback(self, container_name, status, message=None, error=None):
        """
        Report the status of universal initialization for a container/component.
        
        Args:
            container_name (str): Name of the container or component being initialized
            status (str): Status of initialization ('starting', 'success', 'failed', 'completed')
            message (str, optional): Status message or description
            error (str, optional): Error message if initialization failed
        """
        pass


class DebugMode:
    skip_initial_setup = False
    skip_asset_extractor = False
    skip_code_generation = False
    skip_docker_export = False
    skip_interface_extraction = False


def setup_dynamic_tool(request_context,
                       llm,
                       callback_functions, tools_logger,
                       executor=None,
                       work_item=None,
                       platform=ApplicationType.COMMON.value,
                       framework=FrameworkType.DEFAULT.value,
                       is_interactive=True,
                       ):
    shell_tools = VisibleShellTools(callback_functions,
                                    request_context['execution_base_path'],
                                    logger=tools_logger,
                                    llm=llm, executor=executor)

    review_status_tools = ReviewStatusTools(callback_functions,
                                            request_context['execution_base_path'],
                                            logger=tools_logger, llm=llm, executor=executor)

    file_tools = ContainerFileTools(callback_functions,
                                    request_context['execution_base_path'],
                                    logger=tools_logger, llm=llm, executor=executor)


    search_and_download_tool = SearchAndDownloadTool(callback_functions,
                                                     request_context['execution_base_path'],
                                                     logger=tools_logger, llm=llm, executor=executor)
    
    # Supabase tool for database operations
    supabase_tool = SupabaseTool(callback_functions,
                                    request_context['execution_base_path'],
                                    logger=tools_logger, llm=llm, executor=executor)

    strategy_tools = StrategicAnalysisTool(callback_functions,
                                           request_context['execution_base_path'],
                                           logger=tools_logger, llm=llm, executor=executor)

    knowledge_tools = KnowledgeTools(callback_functions,
                                     request_context['execution_base_path'],
                                     logger=tools_logger, llm=llm, executor=executor)

    task_tracker_tool = TaskTracker(callback_functions,
                                    request_context['execution_base_path'],
                                    tools_logger,
                                    os.path.join(request_context['execution_base_path'], ".task_tracker.json"),
                                    str(work_item),
                                    llm=llm, executor=executor)

    document_tools = DocMgr(callback_functions,
                            request_context['execution_base_path'],
                            logger=tools_logger, llm=llm, executor=executor)

    # Disable environment summary for now, knowledge tools should be used instead
    # environment_summary = EnvironmentSummary(callback_functions,
    #                                          request_context['execution_base_path'],
    #                                          logger=tools_logger, llm=llm, executor=executor)

    git_tools = GitTools(callback_functions,
                         request_context['execution_base_path'],
                         logger=tools_logger, llm=llm, executor=executor,
                         access_token=os.getenv("GITHUB_ACESS_TOKEN"))

    containers = work_item.get('containers', [{}])
    mobile_container_exists = any(container.get('platform') == ApplicationType.MOBILE.value for container in containers)

    if platform == ApplicationType.MOBILE.value or mobile_container_exists:
        android_emulator_tools = AndroidEmulatorTool(
            callback_functions,
            request_context['execution_base_path'],
            logger=tools_logger, llm=llm, executor=executor
        )
    else:
        android_emulator_tools = None

    if framework == FrameworkType.IOS.value:
        ios_tools = IOSTools(callback_functions,
                             request_context['execution_base_path'],
                             logger=tools_logger,
                             llm=llm,
                             executor=executor)
    else:
        ios_tools = None

    dependency_tools = DependencyTools(callback_functions,
                                       request_context['execution_base_path'],
                                       logger=tools_logger,
                                       llm=llm,
                                       executor=executor)

    work_item_info_retriever = WorkInputDiscovery(callback_functions,
                                                  request_context['execution_base_path'],
                                                  logger=tools_logger, llm=llm, executor=executor)

    tool_instances = [shell_tools,
                      review_status_tools,
                      file_tools,
                      search_and_download_tool,
                      supabase_tool,
                      knowledge_tools,
                      git_tools,
                      task_tracker_tool,
                      document_tools,
                      dependency_tools,
                      work_item_info_retriever,
                      strategy_tools
                      ]

    if android_emulator_tools:
        tool_instances.append(android_emulator_tools)

    if ios_tools:
        tool_instances.append(ios_tools)

    if is_interactive:
        browser_tools = WebBrowser(callback_functions,
                                   request_context['execution_base_path'],
                                   logger=tools_logger, llm=llm, executor=executor)

    tool_instances.append(browser_tools)

    agent_pre_processor = AgentPreProcessor(callback_functions,
                                            logger=tools_logger,
                                            shell_command_tracker=ShellCommandCallTracker(
                                                request_context['execution_base_path']))

    combined_tool = CombinedTools(tool_instances,
                                  [agent_pre_processor],
                                  request_context['execution_base_path'],
                                  logger=tools_logger, llm=llm, executor=executor)

    return combined_tool


def find_tool_instance(tool_instances, tool_name):
    if tool_name in tool_instances:
        return tool_instances[tool_name]
    return None


class ModelNameSelector:
    """
    Manages model selection using configuration settings from the MODELS section.
    
    The config file entries should follow this format:
        key = internal_model_string | Display String
        
    One entry may be designated as the backup model by using the special key "backup":
        backup = internal_backup_model_string | Backup Display String
        
    In this design:
      - The initializer takes an internal model string.
      - The public interface (get_available_models and set_model) uses display strings only.
      - The internal model string is returned by get_selected_model() for backend purposes.
    """

    def __init__(self, selected_internal: str, delimiter: str = "|"):
        """
        Initialize with the initial internal model string.
        
        Args:
            selected_internal (str): The initial internal model string.
            delimiter (str): Delimiter used to separate internal and display strings in the config.
        """
        self.delimiter = delimiter
        self._available_models = {}  # mapping: display string -> internal string
        self.backup_model = None  # tuple: (display, internal) for backup model
        self._load_available_models()

        # Find the display string corresponding to the provided internal string.
        found = False
        for display, internal in self._available_models.items():
            if internal == selected_internal:
                self.selected_model_internal = selected_internal
                self.selected_model_display = display
                found = True
                break

        if not found:
            logging.warning(
                f"Internal model '{selected_internal}' not found among available models. Using backup model.")
            self.select_backup_model()

    def _load_available_models(self):
        """
        Loads available models from the MODELS section in the configuration.
        
        Models with key "backup" are stored separately.
        Other models populate the _available_models dictionary mapping display strings to internal strings.
        """
        if not config.has_section("MODELS"):
            logging.error("No MODELS section found in configuration.")
            return

        for option in config.options("MODELS"):
            value = config.get("MODELS", option)
            parts = [part.strip() for part in value.split(self.delimiter)]
            if len(parts) != 2:
                logging.warning(f"Skipping invalid model entry '{option}': {value}")
                continue
            internal_str, display_str = parts
            if option.lower() == "backup":
                self.backup_model = (display_str, internal_str)
            else:
                self._available_models[display_str] = internal_str

    def get_available_models(self):
        """
        Retrieves available models as a list of display strings.
        
        Returns:
            list: The list of available model display strings.
        """
        return list(self._available_models.keys())

    def set_model(self, display_model: str):
        """
        Sets the selected model using the provided display string.
        
        Args:
            display_model (str): The display string of the model to select.
        """
        if display_model in self._available_models:
            self.selected_model_display = display_model
            self.selected_model_internal = self._available_models[display_model]
        else:
            logging.error(f"Model with display string '{display_model}' is not available.")

    def get_selected_model(self):
        """
        Returns the currently selected internal model string (for backend use).
        
        Returns:
            str: The internal model string.
        """
        return self.selected_model_internal

    def get_selected_model_display(self):
        """
        Returns the currently selected display string (for user interfaces).
        
        Returns:
            str: The display string of the selected model.
        """
        return self.selected_model_display

    def select_backup_model(self):
        """
        Selects the backup model as defined in the configuration file.
        If no backup model is defined, it falls back to the first available model.
        """
        if self.backup_model:
            display, internal = self.backup_model
            self.selected_model_display = display
            self.selected_model_internal = internal
            logging.info(f"Selected backup model: {display}")
        elif self._available_models:
            first_display = next(iter(self._available_models))
            self.selected_model_display = first_display
            self.selected_model_internal = self._available_models[first_display]
            logging.info(f"No backup defined; selected first available model: {first_display}")
        else:
            logging.error("No models available to select as backup.")

    def is_backup_model(self):
        """
        Checks whether the currently selected model is the backup model.
        
        Returns:
            bool: True if the selected model is the backup, else False.
        """
        if self.backup_model:
            backup_display, _ = self.backup_model
            return self.selected_model_display == backup_display
        return False

