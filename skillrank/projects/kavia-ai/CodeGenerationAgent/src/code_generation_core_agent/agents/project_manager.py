from code_generation_core_agent.config import config

PROJECT_MANIFEST_FILE_NAME = "project_manifest.yaml"

DEFAULT_MULTI_CONTAINER_CONFIG =  {
            "backend": ("CONTAINER", "default_backend_port",  config.get("default_backend_port")),
            "frontend": ("CONT<PERSON>IN<PERSON>", "default_frontend_port", config.get("default_frontend_port")),
            "mobile": ("PREVIEW", "mobile_ports", config.get("default_mobile_port")),
            "database": ("CONTAINER", "default_database_port", config.get("default_database_port")),
        }

CONTAINER_FORMAT_VERSION = "1.0.0"

class ProjectManager:
    """
    Project manager class that maintains manifests of the projects
    """

    def __init__(self, project_root_path):
        self.project_root_path = project_root_path
