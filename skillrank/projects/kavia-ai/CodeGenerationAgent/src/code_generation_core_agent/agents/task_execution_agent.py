import time
from enum import Enum
import json
import logging
import os
import threading
from datetime import datetime
import jinja2
import asyncio
import nest_asyncio
from typing import List
import yaml

from code_generation_core_agent.agents import preview
from code_generation_core_agent.agents.framework.cost_tracker import Cost<PERSON>racer, CircuitBreakerCallback
from code_generation_core_agent.agents.framework.llm_user_input_processor import LLMUserInputProcessor, InputData
from code_generation_core_agent.agents.framework.micro_agent_registry import MicroAgentRegistry, MicroAgentType
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.executor.file_watcher import start_file_watcher
from code_generation_core_agent.agents.tools.executor.local_executor import LocalExecutor
from code_generation_core_agent.agents.tools.init import universal_project_setup
from code_generation_core_agent.agents.utilities import FrameworkType, setup_dynamic_tool, find_tool_instance, \
    DebugMode, \
    TaskExecutionControl, TaskExecutionReporter, ApplicationType, ModelNameSelector, get_container_name
from code_generation_core_agent.chat.cga_chat import CGAChatSystem
from code_generation_core_agent.chat.cga_user_service import CGAUserService, UserMessage, CGAChatInputType
from code_generation_core_agent.config import config
from code_generation_core_agent.agents.framework.knowledge_helper import Knowledge_Helper
from code_generation_core_agent.agents.framework.knowledge import Knowledge
from code_generation_core_agent.llm.chat_interface import ChatInterface, MessageType
from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.project_schemas import ProjectSchema, sync_workspace_to_manifest, ContainerType, update_project_env
from code_generation_core_agent.services.service_factory import init_3rd_party_services
from code_generation_core_agent.services.services_core import ServicesCore
from code_generation_core_agent.telemetry.logger_factory import LoggerFactory
from code_generation_core_agent.tools.backend_service_extractor import ServiceExtractor
from code_generation_core_agent.agents.preview import PreviewManager
from code_generation_core_agent.agents.framework.ve_server.ve_server import VisualEditServer
from code_generation_core_agent.agents.orchestrator_state import OrchestratorState
from universalinit_env import map_common_to_framework, map_framework_to_common

REQUIRED_ENV_VARS = ["OPENAI_API_KEY", "ANTHROPIC_API_KEY"]

# Apply nest_asyncio to allow nested event loops
nest_asyncio.apply()

def _validate_environment_var():
    for var in REQUIRED_ENV_VARS:
        if var not in os.environ:
            raise ValueError(f"Environment variable {var} is not set. YOU MUST set this value in the .env file")


class TaskExecutionAgent:
    class CostExceededCallback(CircuitBreakerCallback):
        def __init__(self, chat_interface=None):
            self.chat = chat_interface

        def set_chat_interface(self, chat_interface):
            self.chat = chat_interface

        def trigger_warning(self, limit: int):
            if self.chat:
                self.chat._send_message_to_user("90% of max session length reached!")

        def trigger_pause(self, limit : int):
            if self.chat:
                self.chat._send_message_to_user("Max session length reached")

        def trigger_error(self, limit : int):
            if self.chat:
                self.chat._send_message_to_user("Exceeded max session size. Restart code generation session to continue.")

    def _config_global_log_dir(self, execution_base_path):
        """
        Setup execution base path
        """
        self.logger = setup_logger("TaskExecutionAgent", execution_base_path)
        self.cost_callback = TaskExecutionAgent.CostExceededCallback(None)
        self.cost_tracer = CostTracer()
        self.cost_tracer.set_log_dir(execution_base_path)
        self.cost_tracer.register_cost_threshold_callback(self.cost_callback)


    def __init__(self, llm,
                 execution_base_path,
                 micro_agents_config,
                 model_name,
                 prompts_base_path,
                 platform=ApplicationType.COMMON.value,
                 framework=FrameworkType.DEFAULT.value
        ):

        # This needs to be done before the cost tracker is initialized
        self._config_global_log_dir(execution_base_path)

        self.chat = None
        _validate_environment_var()
        self._check_config(execution_base_path)
        
        self.project_base_path = None
        self.containers = []
        self.container_registry = {}
        self.current_container_name = None
        
        self.hmi_tool = None
        self.git_tool = None
        self.llm = llm
        self.base_path = execution_base_path
        self.prompts_base_path = prompts_base_path
        self.model_name_selector = ModelNameSelector(model_name)
        self.config = micro_agents_config
        self.agent_tools = None
        self.request_exit = False
        self.count_work_item = 0
        self.count_work_item_repeat = 0
        self.prompt_template = self.load_prompt_template()
        self.messages = []
        self.user_input_processor = LLMUserInputProcessor(self.logger)
        self._lock = threading.Lock()
        self.task_report = {}
        self.platform = platform
        self.framework = framework
        self.micro_agent_registry = None
        self.ve_server = None
        self.preview_manager = PreviewManager()
        self.loop_question = asyncio.new_event_loop()
        self.thread_question = threading.Thread(target=self._run_question_event_loop, daemon=True)
        self.thread_question.start()
        self.executor = self._get_executor()
        self.init_debug_modes()
        self.backend_service_extractor = ServiceExtractor(self.logger)
        self.orchestrator_model = None
        self.project_setup_completed = False # This is used to ensure the project setup (post-process) has completed
        self.previews_started = False
        self._current_invoked_agent = None
        self._invoke_lock = threading.Lock()
        self._invoke_agent_in_progress = False

        self.project_schema = None
        # OrchestratorState instance
        self.orchestrator_state = OrchestratorState()
        self.codebases = None  

    async def _setup_preview_manager(self, work_item, agent_name):

        manifest_path = work_item.get("manifest_path", None)
        self.manifest_path = manifest_path
        if not manifest_path or not os.path.isfile(manifest_path):
            self.logger.info(f"No manifest path not found. Skipping preview setup. Mainfest_path: {manifest_path}")
            return

        project : ProjectSchema = ProjectSchema.load_from_file(manifest_path)
        initialized_services = init_3rd_party_services(project, self.logger)

        work_item.pop("manifest_path")
        if not hasattr(self, "preview_manager") or self.preview_manager is None:
            self.preview_manager = PreviewManager.get_instance(self.executor, initialized_services)


        self.initialized_services = initialized_services
        self.preview_manager.set_initialized_services(initialized_services)
        pm = self.preview_manager

        pm.set_agent_tool_set(self.agent_tools.tool_set)
        pm.set_base_path(self.base_path)
        pm.set_executor(self.executor)
        pm.set_logger(self.logger)
        pm.set_operation_mode(agent_name)
        pm.set_manifest_path(manifest_path)
        pm.set_build_completed_callback(self._on_preview_build_completed)
        
        # add environment variables to the preview manager
        if project.overview.env:
            pm.set_environment_variables(project.overview.env)

        pm.setup_containers(project)
        project.save_to_manifest(manifest_path)
        self.project_schema = project

    def _on_preview_build_completed(self, container_name, preview_url, state, error):
        if not error:
            import re
            def extract_port(url):
                match = re.match(r'^https?://[^:/]+:(\d+)', url)
                if match:
                    return int(match.group(1))
                return 3000
            enable_visual_edit = config.getboolean("TOOLS","enable_visual_edit")
            self.logger.info(f"enable_visual_edit: {enable_visual_edit}")
            if enable_visual_edit:
                work_item = self.request_context.get('work_item')
                if work_item:
                    containers = work_item.get('containers')
                    if containers:
                        for container in containers:
                            if container_name == container['container_name']:
                                platform = container.get('platform')
                                container_type = container.get('container_type')
                                if platform == ApplicationType.WEB.value or container_type == ContainerType.FRONTEND.value:
                                    if not self.ve_server:
                                        app_port = extract_port(preview_url)
                                        if 'relative_path' in container:
                                            frontend_base_path = os.path.join(self.base_path,container['relative_path'])
                                        else:
                                            frontend_base_path = os.path.join(self.base_path,container['workspace'])
                                        self.ve_server = VisualEditServer(
                                                                        self.base_path,
                                                                        frontend_base_path,
                                                                        self.platform,
                                                                        self.framework,
                                                                        self.executor,
                                                                        self.agent_tools,
                                                                        self.logger,
                                                                        app_port)
                                        self.ve_server.start()
                                    preview_url = self.ve_server.get_preview_url(preview_url)
        """Callback when a container build completes."""
        self.logger.info(f"Container '{container_name}' build completed: {state} at {preview_url}")
        from code_generation_core_agent.agents.preview.preview_manager import PreviewState
        if error:
            self.logger.error(f"Container '{container_name}' build failed: {error}")

        if preview_url and hasattr(self, "reporter") and self.reporter:
            message = f"Preview for {container_name} is now available at: {preview_url}"
            self._send_message_to_user(message, message_type=MessageType.COMMAND)
            # Optionally, update the UI with container-specific state
            self.reporter.app_state_callback(url=preview_url, state=state, container=container_name)
        elif state == PreviewState.FAILED.value:
            self.logger.error(f"Preview for {container_name} failed to build.\n Error: {error}")


    def _send_message_to_user(self, chat_message,
                              needs_user_input=False,
                              blocking=False,
                              error=False,
                              parent_id=None,
                              message_type : MessageType = None,
                              extra=None):
        # Print traceback here
        import traceback
        print(traceback.format_exc())
        if self.chat:
            id, result = self.chat._send_message_to_user(chat_message,
                                                         needs_response=needs_user_input,
                                                         blocking=blocking,
                                                         error=error,
                                                         parent_id=parent_id,
                                                         message_type = message_type, extra=extra)
            return id, result
        else:
            if blocking:
                return "", self.reporter.get_user_input(chat_message)
            else:
                return "", self.reporter.send_agent_message(chat_message)

    def _get_answer_from_user(self, questions: dict):
        for question_id, question_data in questions.items():
            while question_data['status'] != "answered":
                id, response = self._send_message_to_user(question_id, question_data['text'], blocking=False)
                if response:
                    questions[question_id]['answer'] = response.get("content")
                    questions[question_id]['status'] = "answered"

    def init_debug_modes(self):

        debug_skip_orchestrator_steps = os.getenv("DEBUG_SKIP_ORCHESTRATOR_STEPS", "")
        debug_skip_orchestrator_steps = debug_skip_orchestrator_steps.split(",")
        self.debug_mode = DebugMode()
        self.debug_mode.skip_initial_setup = "initial_setup" in debug_skip_orchestrator_steps
        self.debug_mode.skip_asset_extractor = "asset_extractor" in debug_skip_orchestrator_steps
        self.debug_mode.skip_code_generation = "code_generation" in debug_skip_orchestrator_steps
        self.debug_mode.skip_docker_export = "docker_export" in debug_skip_orchestrator_steps
        self.debug_mode.skip_interface_extraction = "interface_extraction" in debug_skip_orchestrator_steps
        # If any of the steps are skipped print a message
        if any([self.debug_mode.skip_initial_setup, self.debug_mode.skip_asset_extractor,
                self.debug_mode.skip_code_generation, self.debug_mode.skip_docker_export,
                self.debug_mode.skip_interface_extraction]):
            print(f">>>>>> Skipping the following steps: {debug_skip_orchestrator_steps} <<<<<<<<")

    @staticmethod
    def _check_config(execution_base_path):
        """
        Validate configuration
        """
        workspace_dir = config.get("SYSTEM", "workspace_dir")
        # execution_base_path should be a subdirectory of workspace_dir
        if not os.path.abspath(execution_base_path).startswith(os.path.abspath(workspace_dir)):
            raise ValueError(f"Execution base path '{execution_base_path}' must be a subdirectory of '{workspace_dir}'")

        # Check if the workspace directory exists and it is a writable directory
        if not os.path.exists(workspace_dir):
            raise ValueError(f"Workspace directory '{workspace_dir}' does not exist")

        if not os.path.isdir(workspace_dir):
            raise ValueError(f"Workspace directory '{workspace_dir}' is not a directory")

        if not os.access(workspace_dir, os.W_OK):
            raise ValueError(f"Workspace directory '{workspace_dir}' is not writable")

    def _get_executor(self):

        self.feature_flag_docker = os.getenv("FEATURE_FLAG_USE_DOCKER", "true").lower() == "true"
        if self.feature_flag_docker:
            executor = DockerExecutor(session_dir=self.base_path,
                                  logger=self.logger,
                                  container_name=get_container_name())
            executor.background_startup()
            return executor
        else:
            executor = LocalExecutor()
            return executor

    def _configure_dynamic_tools(self, request_context,
                                 llm, work_item):

        callback_functions = {
            'terminal_output_callback': self.reporter.terminal_output_callback,
            'browser_output_callback': self.reporter.browser_output_callback,
            'user_query_callback': self._get_answer_from_user,
            'function_call_callback': self.reporter.function_call_callback,
            'codeview_callback': self.reporter.codeview_callback,
            'agent_message_callback': self._send_message_to_user,
        }

        tools_logger = setup_logger("DynamicTool", request_context['execution_base_path'])

        combined_tool = setup_dynamic_tool(request_context, llm, 
                                           callback_functions,
                                           tools_logger,
                                           executor=self.executor,
                                           work_item=work_item,
                                           platform=self.platform,
                                           framework=self.framework)

        if config.getboolean('TOOLS', 'enable_git_tool'):
            self.git_tool = find_tool_instance(combined_tool.tool_set, "GitTools")

        return combined_tool

    def set_user_input(self, user_input):
        ingested = False

        if self.agent_tools and self.hmi_tool:
            ingested = self.hmi_tool.set_user_input(user_input)

        if not ingested:
            # Extract attachments from user input if present
            attachments = user_input.get('attachments', [])
            extra = user_input.get('extra', {})
            
            input_data = InputData(
                user_input.get('content', user_input), 
                self.request_context.get("current_task"),
                attachments=attachments,
                extra=extra,
            )
            processed_user_input = self.user_input_processor.summarize_user_input(input_data)

            if processed_user_input.answer_question:
                self._execute_answer_question(user_input)
            else:
                self.user_assigned_task = processed_user_input.cleaned_user_input
                # Store attachments in request context
                self.request_context['attachments'] = attachments

                if processed_user_input.interrupt_task:
                    self.micro_agent_registry.interrupt_all_agents(processed_user_input.interrupt_reason)
    @staticmethod
    def _setup_telemetry(base_path):
        telemetry_enabled = config.getboolean("LOGGING", "enable_telemetry")
        if telemetry_enabled:
            from code_generation_core_agent.telemetry.logger_factory import LoggerFactory
            config_log_path = config.get("SYSTEM", "config_dir")
            if config_log_path:
                # Get last directory from base_path to use as the session_id
                session_id = os.path.basename(os.path.normpath(base_path))
                log_path = os.path.join(config_log_path, session_id, "logs")
            else:
                log_path = os.path.join(base_path, "logs")

            LoggerFactory.setup_exit_handler(str(log_path))

    def _initialize_agent_costs(self):
        """
        Initialize the cost tracker for each agent
        Must be called after the micro_agents have been extracted
        """
        for agent_name in self.micro_agent_registry.get_micro_agent_names():
            self.cost_tracer.add_cost(agent_name, 0)


    def _run_question_event_loop(self):
        asyncio.set_event_loop(self.loop_question)
        self.loop_question.run_forever()

    def _execute_answer_question(self, question):
        timeout = 20
        try:
            future = asyncio.run_coroutine_threadsafe(self._answer_question(question), self.loop_question)
            result = future.result(timeout=timeout)
            if result:
                self._send_message_to_user(result)
        except Exception as e:
            print(f"Error: _execute_ask_question had exception {e}")
        return None

    async def _answer_question(self, question):
        schemas = self.agent_tools.get_function_schemas(["KnowledgeTools", "ContainerFileTools"])
        validator = None
        executor = self.agent_tools.function_executor

        system_prompt = "You are an expert software engineer."

        user_prompt = 'Your task is to answer questions about a software project.  Use the provided KnowledgeTools tools to get ' \
                      'information about the project codebase and to determine what files in the project are relevant to the question. ' \
                      'Use ContainerFileTools to directly read relevant files in order to ensure your answers are accurate.\n ' \
                      'For example, if you use KnowledgeTools_find_relevant_files with a set of search terms and this returns a ' \
                      'large number of files you might use KnowledgeTools_get_source_file_knowledge to get more information about some ' \
                      'likely files to decide which are most promising, then use ContainerFileTools_read_files to read these files ' \
                      'to get the best information on which to base your answer. If the information you gather suggests there may ' \
                      'be additional sources of information then do additional KnowledgeTools_find_relevant_files searches and reads ' \
                      'so that your answer can be based on comprehensive information.  If your answer contains multiline text be sure ' \
                      'to format it properly for JSON.\n' \
                      f'The current question is: {question}\n' \
                      'Please format your final response as a JSON object with the following structure:\n' \
                      '{{ ' \
                      '   "answer": "<answer to question>"\n' \
                      '}}'
        try:
            completion = await self.llm.llm_interaction_wrapper(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                user_prompt=None,
                system_prompt=None,
                model=self.model_name,
                response_format={"type": "json_object"},
                function_schemas=schemas,
                function_executor=executor,
                function_validator=validator
            )
            response = json.loads(completion.choices[0].message.content.strip())

            if self.reporter.cost_update_callback:
                all_costs = self.cost_tracer.get_all_costs()
                total_cost = self.cost_tracer.get_total_cost()
                self.reporter.cost_update_callback(all_costs, total_cost)

            return response["answer"]
        except Exception as e:
            print(f"Exception {e}")

        if self.reporter.cost_update_callback:
            all_costs = self.cost_tracer.get_all_costs()
            total_cost = self.cost_tracer.get_total_cost()
            self.reporter.cost_update_callback(all_costs, total_cost)

    # Update the _run_universal_init method

    async def _run_universal_init(self, work_item, agent_name):
        """
        Run universal init for single container or all containers in project based on work_item_type
        """
        self.logger.info("Running universal init")

        results = []
        for container_name, container_info in self.container_registry.items():
            self.logger.info(f"Initializing container: {container_name}")
            
            # Report initialization start
            if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                self.reporter.universal_init_status_callback(
                    container_name,
                    'starting',
                    f"Starting universal initialization for {container_name}"
                )
            
            container_base_path = self.get_container_base_path(container_name)

            # Create container-specific work item
            container_work_item = {
                'container_name': container_name,
                'component_name': container_name,
                'framework': container_info['framework'],
                'platform': container_info['platform'],
                'description': work_item.get('description', f'{container_name} container'),
            }

            try:
                result = universal_project_setup.setup_project(
                    container_work_item,
                    container_base_path,
                    self.executor,
                    self.logger,
                    container_info['framework'],
                    container_info['platform']
                )

                if result:
                    self.logger.info(f"Universal init completed successfully for {container_name}")
                    results.append({container_name: result})
                    
                    # Report success
                    if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                        self.reporter.universal_init_status_callback(
                            container_name,
                            'success',
                            f"Universal initialization completed successfully for {container_name}"
                        )
                else:
                    self.logger.error(f"Universal init failed for {container_name}")
                    
                    # Report failure
                    if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                        self.reporter.universal_init_status_callback(
                            container_name,
                            'failed',
                            f"Universal initialization failed for {container_name}",
                            "Setup project returned no result"
                        )
            except Exception as e:
                self.logger.error(f"Universal init failed for {container_name} with exception: {e}")
                
                # Report error
                if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                    self.reporter.universal_init_status_callback(
                        container_name,
                        'failed',
                        f"Universal initialization failed for {container_name}",
                        str(e)
                    )

        # Report overall completion
        if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
            total_containers = len(self.container_registry)
            successful_results = len(results)
            self.reporter.universal_init_status_callback(
                "overall",
                'completed',
                f"Universal initialization completed: {successful_results}/{total_containers} containers initialized successfully"
            )

        return results


    def _ensure_project_setup_completed(self, work_item):
        # This function is run only once per task execution
        self.logger.info("Ensuring project setup is completed...")

        if self.project_setup_completed:
            return
        
        try:
            if not self.previews_started:
                self._send_message_to_user("Waiting for project setup to complete...",
                                        needs_user_input=False,
                                        blocking=False)
            
            for container_name, container_info in self.container_registry.items():
                container_base_path = self.get_container_base_path(container_name)
                project_monitoring_thread = threading.Thread(
                    target=self.start_project_monitoring,
                    daemon=True,
                    args=(self.executor, container_base_path)
                )
                project_monitoring_thread.start()
                universal_project_setup.wait_for_project_setup(container_base_path, logger=self.logger)
        except Exception as e:
            self.logger.error(f"Error waiting for project setup to complete: {e}")
        # DON'T start previews here - they're already started in _setup_code_generation
        # This method only ensures the setup is completed
        self.project_setup_completed = True

    async def _run_initial_setup_agent(self, work_item, agent_name):
        """
        Run the initial setup agent if it exists, or use universal init if framework is supported
        """

        if self.debug_mode.skip_initial_setup:
            self.logger.info("Skipping initial setup agent due to debug mode.")
            return
        
        # Check if we should use universal init based on framework and platform detection
        if self._should_use_universal_init(work_item):
            self.logger.info("Framework is supported, using universal init instead of initial setup agent")
            self._send_message_to_user("Initializing project with universal init...")
            self.reporter.task_start_callback("Starting universal project initialization")
            
            try:
                result = await self._run_universal_init_for_work_item(work_item)
                if result:
                    self.logger.info("Universal init completed successfully")
                    self._send_message_to_user("Universal project initialization completed successfully")
                else:
                    self.logger.warning("Universal init completed with no results")
                    self._send_message_to_user("Universal project initialization completed with warnings")
                return
            except Exception as e:
                self.logger.error(f"Error in universal init: {e}")
                self._send_message_to_user(f"Universal init failed: {str(e)}", error=True)
                # Fall back to initial setup agent on error
                self.logger.info("Falling back to initial setup agent due to universal init error")
        
        # Use initial setup agent for unsupported frameworks or when universal init fails
        self.logger.info("Using initial setup agent for unsupported framework or fallback")
        
        # fallback to initial setup agent
        agent_map = self.micro_agent_registry.get_agents(MicroAgentType.INITIAL_SETUP_MICRO_AGENT)

        if not agent_map:
            self.logger.warning("No initial setup agent found in micro agent registry")
            return

        initial_setup_agent = agent_map[MicroAgentType.INITIAL_SETUP_MICRO_AGENT.value]

        # Some steps may fail during the initial setup. We give some runway for the setup
        # to restart.
        initial_setup_max_step_count = initial_setup_agent.get_action_count() + 5

        if agent_name == "CodeGeneration":
            while initial_setup_max_step_count > 0:
                initial_setup_max_step_count -=1

                action, progress_message = initial_setup_agent.get_action()

                if action is None:
                    break
                try:
                    self._send_message_to_user(progress_message)
                    self.reporter.task_start_callback(
                        f"Starting with agent {MicroAgentType.INITIAL_SETUP_MICRO_AGENT.value}")
                    setup_result = await initial_setup_agent.process_request(
                        action, work_item=work_item,
                        request_details=self.request_context,
                        capabilities=self.micro_agent_registry.get_capabilities(
                            MicroAgentType.INITIAL_SETUP_MICRO_AGENT)
                    )
                except Exception as e:
                    import traceback
                    print(traceback.format_exc())
                    self.logger.error(f"Error in initial setup agent: {e}\n")
                    return

    async def export_docker_container(self):
        """
         Export container for multi-container projects
        """
        if not self.debug_mode.skip_docker_export:
            print("Exporting container...")
            self._send_message_to_user("Exporting container...")
            # Final export of the container
            out = await self._export_container()

    async def shutdown(self):
        if self.agent_tools:
            self.agent_tools.shutdown()
        if self.ve_server:
            self.ve_server.stop()


    def _user_input_available_callback(self, user_input: UserMessage):
        if (user_input.input_type.value == CGAChatInputType.TASK.value):
            self.user_assigned_task = user_input.content
            self.user_attachments = user_input.attachments
            if user_input.extra:
                extra = user_input.extra
                if extra.get('type') in ['setup_service', 'setup_service_oauth_complete']:
                    # create a dict of environment variables
                    vars = extra.get('vars', [])
                    env = {}
                    for var in vars:
                        if var.get('name') and var.get('value'):
                            env[var['name']] = var['value']

                    # For OAuth completion, also log the project details
                    if extra.get('type') == 'setup_service_oauth_complete':
                        service_name = extra.get('service_name', 'Unknown')
                        project_name = extra.get('project_name', 'Unknown')
                        project_id = extra.get('project_id', 'Unknown')
                        api_url = extra.get('api_url', 'Unknown')

                        self.logger.info(f"OAuth setup completed for {service_name}")
                        self.logger.info(f"  - Project: {project_name} (ID: {project_id})")
                        self.logger.info(f"  - API URL: {api_url}")
                        self.logger.info(f"  - Environment variables configured: {list(env.keys())}")

                    self.preview_manager.set_environment_variables(env)
                    update_project_env(self.project_schema, env)
                    self.project_schema.save_to_manifest(self.manifest_path)

                    # Log successful environment variable update
                    if extra.get('type') in ['setup_service', 'setup_service_oauth_complete']:
                        self.logger.info(f"Project manifest updated with {len(env)} environment variables")

            self.micro_agent_registry.interrupt_all_agents("New user input available")

        if (user_input.input_type.value == CGAChatInputType.CODE.value):
            asyncio.run_coroutine_threadsafe(
                self._handle_answer_processing(user_input),
                self.loop_question
            )

        if (user_input.input_type.value == CGAChatInputType.QUESTION.value):
            asyncio.run_coroutine_threadsafe(
                self._handle_answer_processing(user_input),
                self.loop_question
            )

    async def _handle_answer_processing(self, user_input: UserMessage):
        timeout = 120
        try:
            result = await asyncio.wait_for(
                self._answer_question(user_input.content),
                timeout=timeout
            )

            if result:
                self._send_message_to_user(result, parent_id=user_input.message_id)
        except asyncio.TimeoutError:
            error_msg = f"Operation timed out after {timeout} seconds"
            logging.error(error_msg)
            self._send_message_to_user(
                error_msg,
                error=True,
                parent_id=user_input.message_id
            )
        except Exception as e:
            logging.error(f"Error: _execute_ask_question had exception {e}")
            self._send_message_to_user(
                f"Error occurred: {str(e)}",
                error=True,
                parent_id=user_input.message_id
            )

    def init_chat(self, chat_interface):
        self.chat = CGAChatSystem(chat_interface, disable_task_creation=True)
        self.cost_callback.set_chat_interface(self.chat)
        self.cga_user_service = CGAUserService()
        self.cga_user_service.register_user_instruction_available_callback(self._user_input_available_callback)

    async def process_work_item(self, agent_name,
                                work_item,
                                use_retriever,
                                control: TaskExecutionControl,
                                status_reporter: TaskExecutionReporter,
                                chat_interface: ChatInterface = None,
                                git_links : List[str] =None,
                                previous_context=None,
                                budget=None,
                                ):
        """
        Process a work item

        :param agent_name: Name of the agent
        :param work_item: Work item to process
        :param use_retriever: Use the work item retriever
        :param control: TaskExecutionControl implementation
        :param status_reporter: TaskExecutionReporter implementation
        :param chat_interface: ChatInterface - optional for enabling user chat
        :param previous_context: Previous context for the agent
        """
        self._cleanup_work_item(work_item)
        if chat_interface:
            self.init_chat(chat_interface)

        self._initialize_project_mode(work_item)

        self.logger.info(f"Received work_item for {agent_name}: {work_item}")
        
        if status_reporter:
            self.set_status_reporter(status_reporter)
        if self.reporter.progress_callback:
            self.reporter.progress_callback(f"Starting task execution process for {agent_name}...", "", "", "")

        if previous_context:
            # Use OrchestratorState to load state
            self.orchestrator_state.load_state(previous_context)
        else:
            # Use OrchestratorState to setup new task
            self.orchestrator_state.setup_new_task(agent_name, work_item, self.base_path)
 

        # Set local references for convenience (optional, or update all code to use orchestrator_state directly)
        self.request_context = self.orchestrator_state.request_context
        self.messages = self.orchestrator_state.messages
        self.task_report = self.orchestrator_state.task_report
        self.resume_mode = self.orchestrator_state.resume_mode
        self.user_assigned_task = self.orchestrator_state.user_assigned_task
        self.user_attachments = self.orchestrator_state.user_attachments


        self._setup_telemetry(self.base_path)

        self.agent_tools = self._configure_dynamic_tools(self.request_context, self.llm,
                                                    work_item)

        self.micro_agent_registry = MicroAgentRegistry(agent_name, self.config, self.logger,
                                                       self.prompts_base_path, self.model_name_selector,
                                                       self.base_path, self.reporter, self.agent_tools)
               

        self._initialize_agent_costs()

        await self._setup_preview_manager(work_item, agent_name)

        if self.containers:
            first_container = self.containers[0].get('container_name')
            self.preview_manager.set_current_container(first_container)

        await self._setup_code_generation(agent_name, previous_context, work_item)

        # Save build commands to the manifest

        if self.project_schema:
            for container in self.project_schema.containers:
                if self.container_registry.get(container.container_name):
                    sync_workspace_to_manifest(self.project_schema,
                                              self.get_container_base_path(container.container_name),
                                              container.container_type, self.base_path)

            self.project_schema.save_to_manifest(self.manifest_path)
        result = None

        while True and not self.request_exit:
            if self.debug_mode.skip_code_generation:
                self.logger.info("Skipping code generation due to debug mode.")
                self.request_exit = True
                break
            try:
                control.check_status()

                if self.user_assigned_task:
                    self.request_context["user_input"] = self.user_assigned_task
                    self.request_context["attachments"] = self.user_attachments
                    self.user_assigned_task = None
                    self.user_attachments = None

                if budget and self.cost_tracer.get_total_cost() > budget:
                    self.logger.info(f"Finishing task due to budget limit {budget}")
                    break

                current_subtask, next_step = await self.generate_next_step(agent_name)

                if not next_step: 
                    self.logger.info("No more steps to execute. Task execution completed.")
                    break

                micro_agent_name = next_step["micro_agent"]
                action = next_step["action"]
                request_details = next_step.get("request_details")
                container_name = next_step.get("container_name")
                if request_details is None:
                    request_details = {"base_path": self.base_path}
                else:
                    request_details["base_path"] = self.base_path

                self.logger.info(
                    f"Next step: Agent - {micro_agent_name}, Action - {action}, Request Content - {json.dumps(request_details, indent=2)}\n")
                self.logger.info(f"Task list: {self.task_report['task_list']}\n")
                self.logger.info(f"Future steps: {self.task_report['future_steps']}\n")

                total_task_list = self.task_report["task_list"] + self.task_report["future_steps"]

                self.reporter.task_start_callback(f"Starting task: {action} with agent {micro_agent_name}")


                if micro_agent_name == "user":
                    output_message = f"{next_step.get('action')} \n\n {next_step.get('output_message')}"

                    #  Note: Call self.chat.interrupt_all_blocking_requests() to interrupt any blocking requests
                        
                    extra = {}

                    if next_step.get("action_request_to_user"):
                        extra["action_request_to_user"] = next_step.get("action_request_to_user")

                    id, user_response = self._send_message_to_user(output_message, needs_user_input=True, blocking=True, extra=extra)

                    self.request_context["user_input"] = user_response.get("content")
                    self.request_context["attachments"] = user_response.get("attachments", [])
                    if "key" in str(user_response).lower():
                        self.logger.warning("User input contains a key **REDACTED**")
                    else:
                        self.logger.info(f"User input: {user_response}")
                    
                    last_step = next_step
                    self.request_context["last_result"] = "user_response"
                    self.request_context["last_step"] = last_step

                    # Here we postpone the project setup completion until the user input is processed
                    # This gives use more time and mask the time needed to complete the project setup
                    if agent_name == "CodeGeneration":
                        self._ensure_project_setup_completed(work_item)
                else:
    
                    # We need to ensure the project setup has completed before invoking the agent
                    if agent_name == "CodeGeneration":
                        self._ensure_project_setup_completed(work_item)

                    result = await self.invoke_agent(micro_agent_name, action, current_subtask,
                                               work_item=self.request_context['work_item'],
                                               request_details=request_details,
                                               container_name=container_name)
                    
                    # Rebuild the app if needed
                    if micro_agent_name in ["CodeWritingAgent", "BugFixingAndVerificationAgent"]:
                        if self.preview_manager:
                            self.logger.info("Reloading the app ...")
                            if container_name:
                                # Set the current container for preview manager
                                self.preview_manager.set_current_container(container_name)
                                
                                if self.project_schema and \
                                   self.project_schema.get_container_by_name(container_name):
                                    container = self.project_schema.get_container_by_name(container_name)
                                    # sync workspace to manifest
                                    sync_workspace_to_manifest(self.project_schema,
                                                self.get_container_base_path(container.container_name),
                                                container.container_type, self.base_path)
                                    # save manifest
                                    self.project_schema.save_to_manifest(self.manifest_path)
                                
                            status = self.preview_manager.run_current_container(run_dependencies=True, restart=True)
                            if status:
                                self.logger.info(f"Status: {status.get('status')}; {status.get('message')}")
                            
                        

                    last_step = next_step
                    self.request_context["last_result"] = result
                    self.request_context["last_step"] = last_step

                    if self.git_tool:
                        self.git_tool.git_add_all(self.base_path)
                        self.git_tool.git_commit(
                            message=f"Step completed: {json.dumps(last_step)} {datetime.now().isoformat()}",
                            repository_path=self.base_path)
                        self.git_tool.git_push(self.base_path)

                    total_task_list = self.task_report["task_list"] + self.task_report["future_steps"]

                    if self.reporter.progress_callback:
                        self.reporter.progress_callback(
                            f"Step completed: {json.dumps(last_step)}",
                            json.dumps(total_task_list),
                            json.dumps(result),
                            json.dumps(self.request_context)
                        )

                    self.logger.info(f"Completed step: {json.dumps(next_step, indent=4)}\n")
                    self.logger.info(f"Result: {json.dumps(result, indent=2)}\n")
                    self.logger.info(f"Completed tasks: {self.task_report['completed_tasks']}\n")
                    self.logger.info(f"Task list: {self.task_report['task_list']}\n")
                    self.logger.info(f"Future steps: {self.task_report['future_steps']}\n")
            except StopIteration:
                self.logger.info("Task execution stopped.")
                break
            except Exception as e:
                import traceback
                print(traceback.format_exc())
                self.logger.error(f"Error in processing work item: {str(e)}")
                self.logger.error(traceback.format_exc())
                self._send_message_to_user(f"Error in processing work item: {str(e)}", error=True)
                if (not await self.should_continue_on_error()):
                    break
                else:
                    self.model_name_selector.select_backup_model()
                    self.logger.warning(f"Failing back to backup model: {self.model_name_selector.get_selected_model()}")

        self.llm.log_token_stats()
        # at the very end of process_work_item:
        # stash both the orchestrator’s “chat history” and the task-list state
        self.request_context["messages"]    = self.messages
        self.request_context["task_report"] = self.task_report

        self.micro_agent_registry.finalize()
        # Save orchestrator state and return as JSON
        return self.orchestrator_state.save_state()
        

    def _initialize_project_mode(self, work_item):
        """
        Initialize the agent for project-level operations
        
        Args:
            work_item: Work item containing project and container information
        """
        self.project_base_path = self.base_path
        self.containers = work_item.get('containers', [])
        self.container_registry = self._build_container_registry(self.containers)
        
        self.logger.info(f"Initialized project mode with {len(self.containers)} containers")

        for container_name in self.container_registry:
            self.logger.info(f"  - {container_name}: {self.container_registry[container_name]['workspace']}")

    def _build_container_registry(self, containers):
        """
        Build a registry of containers with their metadata
        
        Args:
            containers: List of container configurations from work_item
            
        Returns:
            dict: Container registry keyed by container name
        """
        registry = {}
        for container in containers:
            container_name = container.get('container_name')
            if not container_name:
                continue

            # relative_path is a MUST
            container_base_path = str(os.path.join(self.project_base_path, container.get('relative_path') or container.get('workspace')))
            registry[container_name] = {
                'container_name': container_name,
                'container_type': container.get('container_type', 'unknown'),
                'workspace': container.get('relative_path') or container.get('workspace', container_name + "_workspace"),
                'framework': container.get('framework', FrameworkType.DEFAULT.value),
                'platform': container.get('platform', ApplicationType.COMMON.value),
                'dependencies': container.get('dependencies', []),
                'container_base_path': container_base_path,
                'container_root': container.get('container_root', container_base_path),
                'config': container.get('config', {})
            }

        return registry

    def get_container_base_path(self, container_name=None):
        """
        Get the effective base path for a container or project
        
        Args:
            container_name: Optional container name
            
        Returns:
            str: Effective base path
        """
        if container_name:
            container_info = self.container_registry.get(container_name)
            if container_info:
                return f"{self.base_path}/{container_info['workspace']}"
            return self.project_base_path
        return self.base_path

    def set_current_container(self, container_name):
        """Set the current container context"""
        if container_name in self.container_registry:
            self.current_container_name = container_name
            return True
        return False
        
    
    async def _setup_services_in_work_item(self, work_item):
        """
        Setup services in work items
        """
        self.backend_service_extractor.process_work_item(work_item, self.base_path, self.executor, self.chat)
        return work_item

    def set_status_reporter(self, status_reporter):
        """
        Set the status reporter for the task execution agent.

        :param status_reporter: An instance of TaskExecutionReporter to report the status of the task execution.
        """
        self.reporter = status_reporter

    async def _setup_code_generation(self, agent_name, previous_context, work_item):

        #IMPORTANT: _setup_services_in_work_item must run before any other process touches the
        # executor. The _setup_services_in_work_item is responsible to configure the open TCP ports
        # for the container. Move any docker code before this and you will have a bad time and exceptions.

        #if not agent_name == "DocumentCreation" and not agent_name == "CodeMaintenance":
            #if previous_context is None:
                # Pre-process the work item. Start up any backend services
                #work_item = await self._setup_services_in_work_item(work_item)
        
        self.codebases = [] 
        if self.project_schema:
            for container in self.project_schema.containers:
                if self.container_registry.get(container.container_name):
                    # Add container name to the list
                    self.codebases.append(container.container_name)
            
            self.logger.info(f"Added containers to codebases: {self.codebases}")
        
        self.logger.info(self.codebases)
        self.knowledge_helper = Knowledge_Helper(self)

        self.knowledge = Knowledge.getKnowledge()

        if not agent_name == "DocumentCreation" and not agent_name == "CodeMaintenance":
                # Run the initial setup agent if it exists
                uinit_result = await self._run_universal_init(work_item, agent_name)
                if self.preview_manager and not self.previews_started:
                    self.logger.info("Starting preview containers after universal init...")
                    self.preview_manager.run_all_containers(run_dependencies=True)
                    self.previews_started = True
                else:
                    self.logger.info("Preview containers already started or preview manager not available")
                
                # If result is empty array or all members are none:
                if not uinit_result or all(value is None for value in uinit_result):
                    await self._run_initial_setup_agent(work_item, agent_name)
                    # The setup might still be running in background

        elif agent_name == "CodeMaintenance":
            try:
                if self.preview_manager and not self.previews_started:
                    self.logger.info(f"Starting previews in {agent_name} mode...")
                    self.preview_manager.run_all_containers(run_dependencies=True)
                    self.previews_started = True
            except Exception as e:
                self.logger.error(f"An unexpected error happened while starting previews in {agent_name} mode: {e}")                

        self.knowledge.start()



    async def _export_container(self):
        """
        Export the container for the project
        """
        try:
            container_builder_agent = self.micro_agent_registry.get_agents(
                MicroAgentType.CONTAINER_BUILDER_MICRO_AGENT)[MicroAgentType.CONTAINER_BUILDER_MICRO_AGENT.value]

            result = await container_builder_agent.process_request(
                None, work_item=None,
                capabilities=self.micro_agent_registry.get_capabilities(MicroAgentType.CONTAINER_BUILDER_MICRO_AGENT))


        except Exception as e:
            import traceback
            print(traceback.format_exc())
            print(f"Error in asset extractor agent: {str(e)}")
            raise

    def load_prompt_template(self):
        env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(self.prompts_base_path),
            trim_blocks=True,
            lstrip_blocks=True
        )
        return env.get_template(config.get("PROMPTS", "task_manager_prompt"))

    def render_prompt(self, prompt_type, context):
        context['prompt_type'] = prompt_type
        return self.prompt_template.render(context)

    def _extract_json(self, message):
        """
        Attempt to extract JSON from text which may contain it in form that is not directly parseable.
        For instance, an llm might return an invalid response, then after being told of the error, returns
        something like 'I appologize, here is the json object {...}'
        """
        import litellm
        data_object = None
        self.logger.info("Attempting to extract JSON from a response\n")
        prompt = (f"Examine the following text to see if a valid JSON object"
                  f" can be extracted from it.  If so, return only the portion of the text"
                  f" that comprises the JSON object stripping away any other text."
                  f" If no valid JSON object can be found return the text 'None'."
                  f"\nThe text to examine:"
                  f"\n\n{message}")
        try:
            response = litellm.completion(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that examines text."},
                    {"role": "user", "content": prompt}
                ]
            )
            output = response.choices[0].message.content
            try:
                data_object = json.loads(output)
            except json.JSONDecodeError:
                pass
        except Exception as e:
            evlogger = LoggerFactory.get_logger()
            evlogger.report_event("orchestrator.json_extraction_error")
            self.logger.error(f"Failed to extract JSON {e}\n")
            pass

        return data_object

    def validate_orchestrator_response(self, completion):
        """
        This function validates if the response from the orchestrator is valid.
        If we get invalid parameters we give a chance to the orchestrator to correct them.

        return bool, string - bool is whether the response is valid, string is the error message
        """

        try:
            content = completion.choices[0].message.content
            response = json.loads(content)
        except json.JSONDecodeError:
            start = content.index('{')
            end = content.rindex('}') + 1
            json_str = content[start:end]

            try:
                response = json.loads(json_str)
            except json.JSONDecodeError:
                response = self._extract_json(completion.choices[0].message.content)
                if response:
                    pass
                else:
                    return False, "Invalid JSON response"

        required_fields = ["current_subtask", "subtask_details", "next_step"]

        for field in required_fields:
            if field not in response:
                return False, f"Missing required field: {field}"

        next_step = response["next_step"]


        required_next_step_fields = ["micro_agent", "action"]
        for field in required_next_step_fields:
            if field not in next_step:
                return False, f"Missing required field in next_step: {field}"

        agent = next_step["micro_agent"]

        if not self.micro_agent_registry.is_valid_micro_agent(agent) and agent != "user":
            return False, f"Micro-agent {agent} not found in available agents"

        return True, ""

    

    def report_messages_to_cga_chat(self, messages, response):
        """
        Reports only assistant messages to the CGA chat system by filtering out user messages.

        Args:
            messages (list): List of message dictionaries containing role and content
        """
        # Filter out user messages and keep only assistant messages
        processed_messages = [
            message for message in messages
            if message.get("role") == "assistant" or message.get("role") == "tool"
        ]
        # Report the filtered messages to the CGA user service
        state = "{code_generation_orchestration :'{%s}', last_response: '{%s}' }" % (str(processed_messages), response)
        self.cga_user_service.set_agent_state(state)

    def get_current_container_env(self):
        current_container = self.preview_manager.get_current_container()
        ret = ""

        if (current_container):
            schema_container = current_container.project_schema_container

            try:
                mapped_keys = map_common_to_framework(schema_container.framework, schema_container.env).keys()
            except FileNotFoundError as e:
                self.logger.error(f"Error mapping common environment variables to framework: {e}. Using raw env vars.")
                mapped_keys = []

            if mapped_keys:
                ret += ", ".join(mapped_keys)
            else:
                ret += "None"

        else:
            self.logger.warning("No current container set in preview manager. Cannot get environment variables.")
            ret = "No current container set in preview manager. Cannot get environment variables."

        if current_container:
            self.logger.info(f"Defined environment variables: {ret} in container: {current_container.container_name}")
        else:
            self.logger.info(f"Defined environment variables: {ret}")
        return ret

    async def generate_next_step(self, agent_name=None):


        env_file_keys = self.get_current_container_env()
        
        cga_system_description = self._get_cga_system_description()
        
        context = {
            "agent_name": self.request_context["agent_name"],
            "work_item": self.request_context["work_item"],
            "last_step": self.request_context.get("last_step", {}),
            "last_result": self.request_context.get("last_result", {}),
            "available_agents": self.micro_agent_registry.get_capabilities(MicroAgentType.CODER_MICRO_AGENTS).keys(),
            "capabilities": self.micro_agent_registry.get_capabilities(MicroAgentType.CODER_MICRO_AGENTS),
            "have_design_node": True if not (self.request_context.get('design_node') == None) else False,
            "env_file_keys": env_file_keys,
            "cga_system_description" : cga_system_description,
            "attachments": self.request_context.get("attachments", []),
        }

        context.update({
                "containers": list(self.container_registry.keys()),
                "container_registry": self.container_registry,
                "current_container": self.current_container_name,
            })

        system_prompt = self.render_prompt("system", context)
        user_prompt = self.render_prompt("user", context)

        if not self.messages:
            # Add the system prompt for this invocation
            self.messages.append({"role": "system", "content": system_prompt})

            # Add the user prompt for this invocation
            self.messages.append({"role": "user", "content": user_prompt})

        else:
            # Subsequent invocations: add new messages to the existing list
            if self.request_context.get("last_result"):
                last_agent = self.request_context["last_step"]["micro_agent"]

                if last_agent != "user":
                    self.messages.append({
                        "role": "user",
                        "name": last_agent,
                        "content": json.dumps(self.request_context["last_result"]),
                        "message_type": "last_agent_result"
                    })
            if self.resume_mode:
                self.messages.append({
                    "role": "user",
                    "content": "You are resuming a previously stopped session. Please set the agent for the next step to 'user' and ask them what steps they would like to do next. Give them some options based on the previous state.",
                })
                self.resume_mode = False
                

        #create a temporary messages list to pass to the llm, so that some of the temporary messages are not added to the main messages list

        tmp_messages = self.messages.copy()

        #add steps in a 'assistant' message in this messages list
        if self.task_report["task_list"]:
            tmp_messages.append({
                "role": "user",
                "content": f"Current tasks: {json.dumps(self.task_report.get('task_list'), indent=2)}\n, Future tasks: {json.dumps(self.task_report.get('future_steps'), indent=2)}",
                "message_type": "task_list"
            })


        if self.request_context.get("user_input"):
            self.messages.append({
                "role": "user",
                "content": self.request_context["user_input"],
                "message_type": "user_input"
            })
            tmp_messages.append({
                "role": "user",
                "content": self.request_context["user_input"],
                "message_type": "user_input"
            })

            if self.request_context.get("attachments"):
                # If there are attachments, add them to the messages
                self.messages.append({
                    "role": "user",
                    "content": f"Attachments: {json.dumps(self.request_context['attachments'], indent=2)}",
                    "message_type": "attachments"
                })
                tmp_messages.append({
                    "role": "user",
                    "content": f"Attachments: {json.dumps(self.request_context['attachments'], indent=2)}",
                    "message_type": "attachments"
                })


            # Clear the user input
            self.request_context["user_input"] = None
            self.request_context["attachments"] = []

        function_schemas = None
        function_executor = None
        function_validator = None
        validator_function = self.validate_orchestrator_response

        if (config.get("PROMPTS", "task_manager_prompt") == "orchestrator_taskmanager.j2"):
            function_executor = self.agent_tools.function_executor
            function_validator = self.agent_tools.function_validator
            validator_function = self.validate_orchestrator_response
            #validator_function = None #temporarily disabling the validation function

        if self.orchestrator_model == None:
            self.orchestrator_model = config.get("LLM", "orchestrator_model", fallback=self.model_name_selector.get_selected_model())
            model = config.get("LLM", "initial_orchestrator_model", fallback=self.orchestrator_model) #use the initial orchestrator model first time.
        else:
            model = self.orchestrator_model

        completion = await self.llm.llm_interaction_wrapper(
            messages=tmp_messages,
            user_prompt=None,
            system_prompt=None,
            model = model,
            response_format={"type": "json_object"},
            validation_function=validator_function,
            function_schemas=None,
            function_executor=function_executor,
            function_validator=function_validator
        )
        response = json.loads(completion.choices[0].message.content)

        next_step = response["next_step"]
        subtask = {
            "current_subtask": response.get("current_subtask"),
            "subtask_details": response.get("subtask_details")
        }

        updated_task_list = response.get("pending_steps_list", [])

        updated_future_steps = response.get("updated_future_steps", [])

        if "action_request_to_user" in response:
            next_step["action_request_to_user"] = response["action_request_to_user"]

        if next_step:
            self.update_task_status(updated_task_list, updated_future_steps, next_step)

            if next_step["micro_agent"] == "user":
                # If the next step is to get user input
                output_message = next_step.get("output_message")
                if not output_message:
                    output_message = next_step.get("action")
                self.messages.append({
                    "role": "assistant",
                    "content": "User input required: " + output_message
                })
            else:

                container_name = next_step.get("container_name") 

                self.messages.append({
                    "role": "assistant",
                    "content": f"Invoking agent {next_step['micro_agent']} with action: {next_step['action']}" +
                            (f" on container: {container_name}" if container_name else "")
                })
        if (self.chat):
            self.report_messages_to_cga_chat(self.messages, response)

        return subtask, next_step

    def update_task_status(self, new_task_list, updated_future_steps, completed_step):
        new_completed_tasks = []
        new_todo_tasks = []

        for task in new_task_list:
            if task["status"] == "completed":
                new_completed_tasks.append(task)
            else:  # Assume any other status is "to-do"
                new_todo_tasks.append(task)

        # Update the request context with the new lists
        self.task_report["completed_tasks"] = new_completed_tasks
        self.task_report["task_list"] = new_todo_tasks
        if updated_future_steps:
            self.task_report['future_steps'] = updated_future_steps

        # Log the updates
        self.logger.info(f"Updated task lists:")
        self.logger.info(f"Completed tasks: {len(new_completed_tasks)}")
        self.logger.info(f"To-do tasks: {len(new_todo_tasks)}")
        self.logger.info(f"Future steps: {len(self.task_report['future_steps'])}")

    
    def _cleanup_work_item(self, work_item):
        # If dict, remove empty keys. Empty keys may indicate to the LLM that a particular field is not needed/disabled.
        if isinstance(work_item, dict):
            keys_to_remove = [key for key, value in work_item.items() if value is None or value == "" or value == [] or value == {}]

            for key in keys_to_remove:
                del work_item[key]


    async def invoke_agent(self, agent_name, action, subtask, work_item, request_details, container_name=None):
        """
        Invoke an agent with optional container context
        """
        import json

        agent = self.micro_agent_registry.get_agents(MicroAgentType.CODER_MICRO_AGENTS)[agent_name]
        log_factory = LoggerFactory.get_logger()
        start_time = time.time()
        knowledge = None

        request_details = request_details.copy() if request_details else {}

        # Set container context if provided and in project mode
        if container_name:
            self.set_current_container(container_name)
            # Update request_details with container information
            request_details.update({
                "container_name": container_name,
                "container_info": self.container_registry.get(container_name, {}),
        })

        if container_name:
            env_path =  os.path.join(self.get_container_base_path(container_name), container_name, ".env")
        else:
            env_path = os.path.join(self.base_path, ".env")

        request_details.update({
            "container_env": self.get_current_container_env(),
        })

        if agent_name in ['PlanningAgent', 'CodeWritingAgent']:
            design_node = self.request_context.get('design_node', None)
            knowledge = Knowledge.getKnowledge()
            if design_node and knowledge:
                knowledge.addTaskKnowledge('design', design_node)

        max_retries = 4
        attempt = 0

        if self.preview_manager:
            work_item["running_containers"] = self.preview_manager.get_running_containers()
        
        while attempt < max_retries:
            tool_call_log = []
            try:
                with self._invoke_lock:
                    self._invoke_agent_in_progress = True
                # Store the one micro-agent you’re about to run
                self._current_invoked_agent = agent
                # Use streaming if enabled in config
                use_streaming = config.getboolean('PROMPTS', 'streaming_support', fallback=False)
                if self.chat and use_streaming and hasattr(agent, 'process_request_streaming'):
                    accumulated_response = []
                    # Create an async generator to wrap the streaming response
                    async def response_stream():
                        async for chunk in agent.process_request_streaming(
                            action,
                            subtask=subtask,
                            work_item=work_item,
                            request_details=request_details,
                            tool_call_log=tool_call_log,
                            capabilities=self.micro_agent_registry.get_capabilities(MicroAgentType.CODER_MICRO_AGENTS),
                            container_name=container_name  
                        ):
                            if not isinstance(chunk, dict):
                                if chunk:
                                    accumulated_response.append(str(chunk))
                                    yield chunk
                                    self.logger.debug(f"Streaming response: {chunk}")
                            else:
                                accumulated_response.append(json.dumps(chunk))
                    # Use the chat interface's built-in streaming method
                    message_id = await self.chat.chat_interface.llm_interface.stream_llm_response(
                        response_stream(),
                        parent_id=work_item.get('parent_id') if work_item else None
                    )

                    if accumulated_response:
                        result_data = {
                            'result': accumulated_response[-1] if accumulated_response else '',
                            'agent_message_log': accumulated_response[0:-1] if len(accumulated_response) > 1 else []
                        }
                        result = json.dumps(result_data)
                    else:
                        result = json.dumps({'result': '', 'agent_message_log': []})

                else:
                    # Non-streaming mode
                    result = await agent.process_request(
                        action,
                        subtask=subtask,
                        work_item=work_item,
                        request_details=request_details,
                        tool_call_log=tool_call_log,
                        capabilities=self.micro_agent_registry.get_capabilities(MicroAgentType.CODER_MICRO_AGENTS),
                        container_name=container_name  
                    )
                # Handle costs and reporting

                if self.reporter.cost_update_callback:
                    all_costs = self.cost_tracer.get_all_costs()
                    total_cost = self.cost_tracer.get_total_cost()
                    self.reporter.cost_update_callback(all_costs, total_cost)
                break
            except Exception as e:
                log_factory.report_event("orchestrator.agent_error." + e.__class__.__name__)
                self.logger.error(f"Error invoking agent '{agent_name}' for action '{action}': {e}")

                raise e
            finally:
                # Clear the “in progress” state
                with self._invoke_lock:
                    self._invoke_agent_in_progress = False
                self._current_invoked_agent = None
        if knowledge:
            knowledge.removeTaskKnowledge('design')

        if agent_name == "DocumentationAgent":
            # Examine the DocumentationAgent report to detect the creation/modification of documentation files
            report = json.loads(result)
            doc_mgr = self.agent_tools.get_tool("DocumentManager")
            doc_mgr.review_work(request_details, report, tool_call_log)

        end_time = time.time()
        elapsed_time = end_time - start_time
        log_factory.report_value("agent_execution_time." + agent_name, elapsed_time)
        self.logger.info(f"Agent '{agent_name}' stopped after {elapsed_time:.2f} seconds")
        return result

    def stop(self):
        # 1) log and mark for exit
        self.logger.info("Requesting orchestrator to stop")
        with self._lock:
            self.request_exit = True
        # 2) stop any in‐flight LLM streaming
        self.stop_streaming()

        if self.chat:
            self.chat.interrupt_all_blocking_requests()
        # 3) inject a dummy “empty” TASK‐type user message so that any blocking
        #    get_user_input() immediately returns
        from code_generation_core_agent.chat.cga_user_service import UserMessage, CGAChatInputType

        #self.chat.send_stop_message()

    def stop_streaming(self):
        with self._invoke_lock:
            if self._invoke_agent_in_progress and self._current_invoked_agent:
                self.logger.info("Streaming stop requested on current agent")
                agent = self._current_invoked_agent
                if hasattr(agent.llm, "stop_streaming"):
                    return agent.llm.stop_streaming()
            self.logger.info("Streaming stop ignored (no invoke_agent in progress)")
            return False
        

    async def should_continue_on_error(self) -> bool:
        user_response = {"content": ""}

        while user_response.get("content", "").strip().lower() not in ["yes", "no"]:
            _, user_response = self._send_message_to_user(
                "We have encountered an internal error. Should we retry and continue? (yes/no)",
                needs_user_input=True,
                blocking=True,
                error=True)

        user_response = user_response.get("content").strip().lower()

        if user_response == "yes":
            self.request_context["user_input"] = "Continue the implementation."

        self.logger.info(f"User input: {user_response}")
        if user_response == "no":
            return False
        return True

    def _remove_old_task_list_items(self):
        """
        Remove old steps list from the messages list
        """
        found_first = False
        for message in reversed(self.messages):
            if isinstance(message, dict):
                if 'tool_call_id' in message and 'content' in message:
                    try:
                        result = json.loads(message['content'])
                        if 'steps_list' in result:
                            if found_first:
                                result['steps_list'] = "Removed. New steps list is available in the task list."
                                message['content'] = json.dumps(result)
                            else:
                                found_first = True
                    except json.JSONDecodeError:
                        continue

    def get_available_models(self):
        """
        Get available models for the agent
        """
        return self.model_name_selector.get_available_models()
    
    def get_selected_model(self):
        """
        Get selected model for the agent
        """
        return self.model_name_selector.get_selected_model_display()
    
    def set_model(self, model_name):
        """
        Set selected model for the agent
        """
        self.model_name_selector.set_model(model_name)
          
    def start_project_monitoring(self, executor, base_path):
        start_file_watcher(executor, base_path, self.logger)
        pass

    def _should_use_universal_init(self, work_item):
        """
        Check if universal init should be used based on framework and platform detection.
        Uses logic similar to _initialize_component in universal_project_setup.py
        
        Args:
            work_item: Work item containing framework and platform information
            
        Returns:
            bool: True if universal init should be used, False if initial setup agent should be used
        """
        try:
            # Import the shared validation function
            from code_generation_core_agent.agents.tools.init.universal_project_setup import validate_and_normalize_framework
            
            # Check containers first (for multi-container projects)
            containers = work_item.get('containers', [])
            if containers:
                # For multi-container projects, check if any container has supported framework
                for container in containers:
                    platform = container.get('platform', 'common').lower()
                    
                    # Use shared validation function
                    normalized_framework = validate_and_normalize_framework(container, platform)
                    if normalized_framework:
                        self.logger.info(f"Universal init supported for container {container.get('container_name')} with framework {normalized_framework}")
                        return True
                
                # If no containers have supported frameworks, use initial setup agent
                self.logger.info("No containers have supported frameworks, using initial setup agent")
                return False
            
            # For single component projects
            platform = work_item.get('platform', 'common').lower()
            
            # Use shared validation function
            normalized_framework = validate_and_normalize_framework(work_item, platform)
            if normalized_framework:
                self.logger.info(f"Universal init supported for framework {normalized_framework}")
                return True
            else:
                self.logger.info(f"Framework not supported, using initial setup agent")
                return False
                
        except Exception as e:
            self.logger.error(f"Error checking universal init support: {e}")
            # Default to initial setup agent on error
            return False

    async def _run_universal_init_for_work_item(self, work_item):
        """
        Run universal init for a specific work item using the same logic as _run_universal_init
        but tailored for the initial setup context.
        
        Args:
            work_item: Work item to process
            
        Returns:
            List of results from universal init
        """
        self.logger.info("Running universal init for work item")
        
        results = []
        containers = work_item.get('containers', [])
        
        if containers:
            # Multi-container project
            for container in containers:
                container_name = container.get('container_name')
                if not container_name:
                    continue
                    
                self.logger.info(f"Initializing container: {container_name}")
                
                # Report initialization start
                if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                    self.reporter.universal_init_status_callback(
                        container_name, 
                        'starting', 
                        f"Starting universal initialization for {container_name}"
                    )
                
                container_base_path = self.get_container_base_path(container_name)
                
                # Create container-specific work item with component_name
                container_work_item = {
                    'container_name': container_name,
                    'component_name': container_name,  # Ensure component_name is set
                    'framework': container.get('framework', 'default'),
                    'platform': container.get('platform', 'common'),
                    'description': work_item.get('description', f'{container_name} container'),
                }
                
                try:
                    result = universal_project_setup.setup_project(
                        container_work_item,
                        container_base_path,
                        self.executor,
                        self.logger,
                        container.get('framework', 'default'),
                        container.get('platform', 'common')
                    )
                    
                    if result:
                        self.logger.info(f"Universal init completed successfully for {container_name}")
                        results.append({container_name: result})
                        
                        # Report success
                        if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                            self.reporter.universal_init_status_callback(
                                container_name,
                                'success',
                                f"Universal initialization completed successfully for {container_name}"
                            )
                    else:
                        self.logger.error(f"Universal init failed for {container_name}")
                        
                        # Report failure
                        if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                            self.reporter.universal_init_status_callback(
                                container_name,
                                'failed',
                                f"Universal initialization failed for {container_name}",
                                "Setup project returned no result"
                            )
                except Exception as e:
                    self.logger.error(f"Universal init failed for {container_name} with exception: {e}")
                    
                    # Report error
                    if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                        self.reporter.universal_init_status_callback(
                            container_name,
                            'failed',
                            f"Universal initialization failed for {container_name}",
                            str(e)
                        )
        else:
            # Single component project
            component_name = work_item.get('component_name') or work_item.get('container_name', 'default')
            framework = work_item.get('framework', 'default')
            platform = work_item.get('platform', 'common')
            
            # Report initialization start
            if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                self.reporter.universal_init_status_callback(
                    component_name,
                    'starting',
                    f"Starting universal initialization for {component_name}"
                )
            
            # Ensure the work_item has component_name set
            work_item_with_component = work_item.copy()
            work_item_with_component['component_name'] = component_name
            
            try:
                result = universal_project_setup.setup_project(
                    work_item_with_component,
                    self.base_path,
                    self.executor,
                    self.logger,
                    framework,
                    platform
                )
                
                if result:
                    self.logger.info(f"Universal init completed successfully for {component_name}")
                    results.append({component_name: result})
                    
                    # Report success
                    if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                        self.reporter.universal_init_status_callback(
                            component_name,
                            'success',
                            f"Universal initialization completed successfully for {component_name}"
                        )
                else:
                    self.logger.error(f"Universal init failed for {component_name}")
                    
                    # Report failure
                    if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                        self.reporter.universal_init_status_callback(
                            component_name,
                            'failed',
                            f"Universal initialization failed for {component_name}",
                            "Setup project returned no result"
                        )
            except Exception as e:
                self.logger.error(f"Universal init failed for {component_name} with exception: {e}")
                
                # Report error
                if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
                    self.reporter.universal_init_status_callback(
                        component_name,
                        'failed',
                        f"Universal initialization failed for {component_name}",
                        str(e)
                    )
        
        # Report overall completion
        if self.reporter and hasattr(self.reporter, 'universal_init_status_callback'):
            total_containers = len(containers) if containers else 1
            successful_results = len(results)
            self.reporter.universal_init_status_callback(
                "overall",
                'completed',
                f"Universal initialization completed: {successful_results}/{total_containers} containers/components initialized successfully"
            )
        
        return results

    def _get_cga_system_description(self):
        current_container = self.preview_manager.get_current_container()
        ret = ""
        SYSTEM_DESCRIPTOR="""
** CODE EXECUTION and PREVIEW SYSTEM DESCRIPTION **
The developed code/applications are started/packaged automatically. The user starts/stop the applications
independently from you. The user knows this process as starting a 'preview'.
The following containers/previews are started on various ports:
```
%s
```
Do not interfere with the preview system. If there are issues with it, you may fix the code, but don't try to start the processes
yourself.
** END OF CODE EXECUTION DESCRIPTION **
        """
        if current_container and self.project_schema:
            for c in self.project_schema.containers:
                ret += f"{c.container_name} of type {c.framework} on TCP port {c.port}\n"

            ret = SYSTEM_DESCRIPTOR % ret
        return ret


