from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
from logging import Logger
import time

PORT_RANGE_START = 3000
PORT_RANGE_END = 3100
DEFAULT_PORT = '3000'
TIMEOUT = 30 # seconds

def get_port_number_from_url_with_fallback(
        url: str,
        default_port = DEFAULT_PORT
    ) -> str:
    parts = url.split(':')
    port_str = parts[-1].split('/')[0] if len(parts) > 1 else ""
    port_valid = port_str.isdigit()
    return port_str if port_valid else default_port

def _validate_pid(pid_str: str) -> str:
    """
    Validates that the given string is a valid PID (numeric and positive).
    Returns the PID if valid, empty string otherwise.
    """
    if not pid_str or not pid_str.strip():
        return ""
    
    pid_str = pid_str.strip()
    
    # Check if it's a valid number
    if not pid_str.isdigit():
        return ""
    
    # Check if it's a reasonable PID (positive integer)
    try:
        pid = int(pid_str)
        if pid <= 0:
            return ""
        return str(pid)
    except ValueError:
        return ""

def _get_pid_from_port(port: int, executor: ExecutorBase, logger: Logger) -> str:
    """
    Gets the PID of the process using the specified port.
    Returns validated PID string or empty string if no valid PID found.
    """
    port = int(port)
    
    # Method 1: Try ss
    ss_cmd = f"ss -tlnp 2>/dev/null | grep ':{port} ' | sed 's/.*pid=\\([0-9]*\\).*/\\1/' | head -1"
    logger.info(f"Running ss command to find PID on port {port}: {ss_cmd}")
    output, return_code = executor.run_one_shot_command(ss_cmd, timeout=TIMEOUT)
    
    if return_code == 0:
        if output.strip():
            validated_pid = _validate_pid(output.strip())
            if validated_pid:
                logger.debug(f"Found PID {validated_pid} using ss for port {port}")
                return validated_pid
        else:
            logger.debug(f"No process found on port {port} (ss returned empty)")
            return ""
    
    # Method 2: Try lsof as fallback (only if ss failed)
    logger.debug(f"Previous ss command failed (return code {return_code})")
    lsof_cmd = f"lsof -ti :{port} 2>/dev/null || echo ''"
    logger.info(f"Running lsof command to find PID on port {port}: {lsof_cmd}")
    output, return_code = executor.run_one_shot_command(lsof_cmd, timeout=TIMEOUT)
    
    if return_code == 0:
        if output.strip():
            first_pid = output.strip().split('\n')[0]
            validated_pid = _validate_pid(first_pid)
            if validated_pid:
                logger.debug(f"Found PID {validated_pid} using lsof for port {port}")
                return validated_pid
        else:
            logger.debug(f"No process found on port {port} (lsof returned empty)")
            return ""
    
    # Method 3: Try netstat as fallback (only if ss and lsof failed)
    logger.debug(f"Previous ss and lsof commands failed (return code {return_code})")
    netstat_cmd = f"netstat -tlnp 2>/dev/null | grep ':{port} ' | awk '{{print $NF}}' | cut -d'/' -f1 | head -1"
    logger.info(f"Running netstat command to find PID on port {port}: {netstat_cmd}")
    output, return_code = executor.run_one_shot_command(netstat_cmd, timeout=TIMEOUT)
    
    if return_code == 0:
        if output.strip():
            validated_pid = _validate_pid(output.strip())
            if validated_pid:
                logger.debug(f"Found PID {validated_pid} using netstat for port {port}")
                return validated_pid
        else:
            logger.debug(f"No process found on port {port} (netstat returned empty)")
            return ""

    logger.debug(f"All methods failed to find PID for port {port}")
    return ""

def is_port_available(
        current_port: str|int,
        executor: ExecutorBase,
        logger: Logger,
        port_range_start = PORT_RANGE_START,
        port_range_end = PORT_RANGE_END,
    ) -> bool:

    if isinstance(current_port, str):
        current_port = int(current_port)

    if not port_range_start <= current_port <= port_range_end:
        return False
    
    pid = _get_pid_from_port(current_port, executor, logger)
    return pid == ""

def find_available_port(
        current_port_in_use: str|int,
        executor: ExecutorBase,
        logger: Logger,  # Added missing logger parameter
        port_range_start = PORT_RANGE_START,
        port_range_end = PORT_RANGE_END,
        default_port = DEFAULT_PORT
    ) -> str:
    current_port = int(current_port_in_use)
    for port in range(port_range_start, current_port):
        if is_port_available(port, executor, logger): 
            return str(port)
    for port in range(current_port + 1, port_range_end + 1):  # Fixed range end
        if is_port_available(port, executor, logger): 
            return str(port)
    return default_port

def log_info_of_pid_running_on_port(port: int, executor: ExecutorBase, logger: Logger):
    pid = _get_pid_from_port(port, executor, logger)

    if not pid:
        logger.info(f"No process is running on port {port}")
        return

    logger.info(f"PID: {pid}")

    cmd = f"ps -p {pid} -o pid,user,comm,lstart,%cpu,%mem,cmd"
    logger.info(f"Running command to get process info: {cmd}")
    output, return_code = executor.run_one_shot_command(cmd, timeout=TIMEOUT)
    if return_code != 0:
        logger.info(f"Error when getting process info, return code {return_code} with output: {output}")
        return

    logger.info(f"Process info:\n{output.strip()}")

def stop_process_using_port(port: int, executor: ExecutorBase, logger: Logger) -> bool:
    try:
        # Get the PID
        pid = _get_pid_from_port(port, executor, logger)
        
        if not pid:
            logger.info(f"No process found running on port {port}")
            return True
            
        logger.info(f"Attempting to gracefully stop process {pid} on port {port}")
        
        # Try SIGTERM (15) - graceful termination
        cmd = f"kill -15 {pid} 2>/dev/null || true"
        logger.info(f"Running command to stop process on port {port}: {cmd}")
        stop_result, stop_code = executor.run_one_shot_command(cmd, timeout=TIMEOUT)

        # Wait a moment to see if process terminates
        time.sleep(5)

        logger.info(f"Process on port {port} responded with {stop_result.strip()} and code {stop_code} to SIGTERM")
        
        # Check if process is still running
        check_pid = _get_pid_from_port(port, executor, logger)
        
        if not check_pid:
            logger.info(f"Process on port {port} stopped gracefully")
            return True
            
        # If still running, try SIGKILL (9) as a last resort
        logger.warning(f"Process on port {port} didn't respond to graceful termination, using force")
        cmd = f"kill -9 {pid} 2>/dev/null || true"
        logger.info(f"Running command to force kill process on port {port}: {cmd}")
        kill_result, kill_code = executor.run_one_shot_command(cmd, timeout=TIMEOUT)
        
        logger.info(f"Force kill result: {kill_result.strip()} with code {kill_code}")

        if kill_code != 0:
            logger.error(f"Failed to forcefully terminate process on port {port}")
            return False
            
        # Final check to confirm the process is gone
        final_check = _get_pid_from_port(port, executor, logger)
        if not final_check:
            logger.info(f"Process on port {port} terminated successfully")
            return True
        else:
            logger.error(f"Process on port {port} is still running after force kill")
            return False
            
    except Exception as e:
        logger.error(f"Failed to stop process on port {port}: {e}")
        return False