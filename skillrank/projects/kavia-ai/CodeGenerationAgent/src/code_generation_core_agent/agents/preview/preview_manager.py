import asyncio
from datetime import datetime
import json
import os
import threading
from enum import Enum
from queue import Queue, Empty

import yaml
from typing_extensions import override

from code_generation_core_agent.agents.preview.monitored_process import MonitoredProcess, MonitoredProcessCallback
from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
from code_generation_core_agent.agents.tools.init import universal_project_setup
from code_generation_core_agent.agents.tools.init.universal_project_setup import run_command_template_to_command
from code_generation_core_agent.agents.tools.mobile import mobile_project_builder
from code_generation_core_agent.agents.tools.mobile.appetize_mobile_logger import AppetizeSessionLogger
from code_generation_core_agent.agents.utilities import ApplicationType, find_tool_instance, FrameworkType
from logging import Logger
from pathlib import Path
from time import sleep
from code_generation_core_agent.agents.preview.port_utils import (get_port_number_from_url_with_fallback,
                                                                  stop_process_using_port,
                                                                  log_info_of_pid_running_on_port)
from code_generation_core_agent.services.services_core import ServicesCore
from code_generation_core_agent.project_schemas import Container, ProjectSchema

class PreviewState(Enum):
    """Enum defining all possible preview application states"""
    NOT_STARTED = "not_started"
    BUILDING = "building"
    RUNNING = "running"
    STOPPED = "stopped"
    FAILED = "failed"

    def __str__(self):
        return self.value


class PreviewHolder:
    """Represents one previewable container (web, mobile, database, etc.)"""
    def __init__(self, container:Container, container_type:str, base_path:str):
        self.project_schema_container = container
        self.container_name =container.container_name
        self.container_type = container_type
        self.framework = container.framework
        self.port = container.port or None
        self.depends_on = container.dependent_containers or []
        self.status = PreviewState.NOT_STARTED.value
        self.url = None
        self.api_route = None
        self.db_env_vars = None
        self.error = None
        self.timestamp = None
        self.workspace = container.workspace
        joined_path = os.path.join(base_path, container.workspace)

        self.container_base_path = os.path.normpath(joined_path)
        self.host = os.getenv("HOST", "0.0.0.0")
        self.container_root = container.container_root if container.container_root else ""
        self.installCommand = container.installCommand if container.installCommand else ""
        self.lintCommand = container.lintCommand if container.lintCommand else ""
        self.generateOpenapiCommand = container.generateOpenapiCommand if container.generateOpenapiCommand else ""
        self.startCommand = container.startCommand if container.startCommand else ""
        self.buildCommand = container.buildCommand if container.buildCommand else ""
        self.installed = False  # Track if dependencies have been installed
        self.built = False  # Track if the container has been built

        self.failed_dependencies = []  # Track which dependencies failed
        self.warning = None  # Track warning messages

    RUNTIME_STATE_KEYS = [
        "status",
        "url",
        "api_route",
        "db_env_vars",
        "error",
        "timestamp",
        "installed",
        "built",
        "failed_dependencies",
        "warning",
    ]

    def copyRuntimeStateFrom(self, other: 'PreviewHolder'):
        for key in self.RUNTIME_STATE_KEYS:
            setattr(self, key, getattr(other, key))

    def to_dict(self) -> dict:
        return {
            "status": self.status,
            "timestamp": self.timestamp,
            "url": self.url,
            "api_route": self.api_route,
            "db_env_vars": self.db_env_vars,
            "error": self.error,
            "failed_dependencies": self.failed_dependencies,
            "warning": self.warning
        }

class MPCallback(MonitoredProcessCallback):
    def __init__(self, pm_manager: 'PreviewManager', container: 'PreviewHolder'):
        self.container = container
        self.pm_manager = pm_manager
        # Pass the container as the token to the parent class
        super().__init__(token=container)

    @override
    def on_exit(self, return_code: int, last_output: str, token: object):
        """Handle process exit - update container status and cleanup"""
        container : Container = token  # The token is the container we passed in __init__

        # Update container status based on exit code
        if return_code == 0:
            container.status = PreviewState.STOPPED.value
            if self.pm_manager.logger:
                self.pm_manager.logger.info(f"Container {container.container_name} exited gracefully")
        else:
            container.status = PreviewState.FAILED.value
            container.error = f"Process exited with code {return_code}: {last_output}"
            if self.pm_manager.logger:
                self.pm_manager.logger.error(
                    f"Container {container.container_name} failed with exit code {return_code}")

        container.timestamp = datetime.now().isoformat()

        if container.container_name in self.pm_manager.running_containers:
            del self.pm_manager.running_containers[container.container_name]

        # Notify callback if set
        if self.pm_manager.build_completed_callback:
            self.pm_manager.build_completed_callback(
                container.container_name,
                container.url,
                container.status,
                container.error
                )
        else:
            self.pm_manager.logger.info(f"Container {container.container_name} completed with exit code {return_code}. No callback set to propagate status.")

class PreviewManager():
    """Singleton manager to orchestrate multi-container previews"""

    _instance = None
    _initialized = False
    _status_info_lock = threading.Lock()
    _instance_lock = threading.Lock()
    _message_handler_thread = None
    _message_queue : Queue = Queue()
    _do_exit = False

    class PMRequest:
        """Request object for PreviewManager operations"""
        def __init__(self, name: str, run_dependencies: bool = True,
                     restart: bool = True, exit: bool = False):
            self.name = name
            self.run_dependencies = run_dependencies
            self.restart = restart
            self.exit = exit

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()

    def __del__(self):
        """Destructor to clean up resources"""
        if self._message_handler_thread and self._message_handler_thread.is_alive():
            # Send exit request to the message handler
            self._message_queue.put(self.PMRequest(name="", exit=True))
            self._message_handler_thread.join(timeout=1)
            self._do_exit = True

    def message_handler_loop(self):
        while not self._do_exit:
            try:
                # Timeout to be able to check for exit condition
                request: PreviewManager.PMRequest = self._message_queue.get(timeout=1)

                if self._do_exit:
                    return

                # Reset the queue item to None to avoid processing it again
                # We need to be able to catch exit without blocking
                if request is None:
                    continue

                # Handle exit request to the message handler (your original logic)
                if request.exit:
                    self._do_exit = True
                    return

                # Collect ALL requests from the queue
                all_requests = [request]

                # Drain the entire queue
                while True:
                    try:
                        next_request = self._message_queue.get_nowait()
                        if next_request is None:
                            continue

                        # If we find an exit request, put it back and break
                        # Let the main loop handle it properly
                        if next_request.exit:
                            return

                        all_requests.append(next_request)

                    except Empty:
                        # No more items in queue, break out of drain loop
                        break

                # Process requests in order, but skip duplicates (keep only the last occurrence)
                seen_containers = set()
                for request in reversed(all_requests):
                    if request.name not in seen_containers:
                        seen_containers.add(request.name)
                        if request.name:
                            self._run_container_thread(
                                request.name,
                                request.run_dependencies,
                                request.restart
                            )
                    if self.logger:
                        self.logger.error(f"Skipping duplicate request for container: {request.name}")

            except Empty:
                # No requests in the queue, continue waiting
                continue
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error in message handler loop: {e}")
                else:
                    print(f"Error in message handler loop: {e}")
    def _on_monitored_process_exit(self, return_code: int, output: str, token: object):
        """
        Callback when a monitored process exits.
        This is used to handle process exit events and can be extended for custom behavior.
        """
        if self.logger:
            self.logger.info(f"Monitored process exited with code {return_code}. Output: {output}")
        else:
            print(f"Monitored process exited with code {return_code}. Output: {output}")

    def __new__(cls, executor: ExecutorBase = None, initialized_services: list[ServicesCore] = None):
        with cls._instance_lock:
            if cls._instance is None:
                cls._instance = super(PreviewManager, cls).__new__(cls)
            return cls._instance

    @classmethod
    def get_instance(cls, executor: ExecutorBase = None, initialized_services : list[ServicesCore] = None):
        return cls(executor, initialized_services)

    @classmethod
    def reset_for_testing(cls):
        """Reset singleton for testing - use with caution"""
        with cls._instance_lock:
            # Signal any existing thread to exit
            cls._do_exit = True

            if cls._instance:
                cls._instance.shutdown()

            if (hasattr(cls, '_message_handler_thread') and
                    cls._message_handler_thread and
                    cls._message_handler_thread.is_alive()):
                cls._message_handler_thread.join(timeout=1)

            # Reset all state including _do_exit BEFORE creating new instance
            cls._instance = None
            cls._initialized = False
            cls._message_handler_thread = None
            cls._do_exit = False

    def __init__(self, executor: ExecutorBase = None, initialized_services : list[ServicesCore] = None):
        with self._instance_lock:
            if not PreviewManager._initialized:
                self.initialized_services = initialized_services
                self.executor: ExecutorBase = executor
                self.agent_tool_set = None
                self.base_path: Path = None
                self.logger: Logger = None
                self.platform = None
                self.manifest_path = None

                # Defaults for web containers
                self.default_port = "3000"
                self.server_initialization_logs_wait_in_seconds = 5
                self.db_run_path = None
                self.copied_db_env_vars = False

                # Containers and status file
                self.containers: dict[str, PreviewHolder] = {}
                self.container_status_file: str = None

                # Thread-safety
                self.preview_status_lock = threading.Lock()

                # Callback when any container build completes
                self.build_completed_callback = None

                # Operation mode
                self.agent_name_for_operation_mode = "CodeGeneration"
                self.waited_for_project_setup = False

                self.running_containers: dict[str, MonitoredProcess] = {}

                self.continue_on_dependency_failure = True

                PreviewManager._initialized = True
                self._message_handler_thread = threading.Thread(target=self.message_handler_loop, daemon=True)
                self._message_handler_thread.start()
                self.appetize_logger = None


    def set_initialized_services(self, initialized_services : list[ServicesCore]):
        self.initialized_services = initialized_services

    # Configuration setters
    def set_agent_tool_set(self, agent_tool_set: dict):
        self.agent_tool_set = agent_tool_set

    def set_base_path(self, base_path: Path):
        self.base_path = base_path

    def set_executor(self, executor: ExecutorBase):
        self.executor = executor

    def set_logger(self, logger: Logger):
        self.logger = logger

    def set_platform(self, platform: ApplicationType):
        self.platform = platform

    def set_manifest_path(self, manifest_path: str):
        self.manifest_path = manifest_path

    def set_operation_mode(self, agent_name: str):
        """Set the operation mode for the preview manager"""
        if self.logger:
            self.logger.info(f"Setting PreviewManager operation mode to {agent_name}")
        self.agent_name_for_operation_mode = agent_name

    def set_environment_variables(self, env_vars: dict):
        """Set environment variables for the containers"""
        # keep all the environment variables in a dictionary. If the same variable is set multiple times, the last one will be used.
        with self._status_info_lock:
            if not hasattr(self, 'environment_variables'):
                self.environment_variables = {}
            for key, value in env_vars.items():
                self.environment_variables[key] = value

    def set_build_completed_callback(self, callback):
        """Set a callback fn(name, url, status) when a container status changes"""
        with self._status_info_lock:
            self.build_completed_callback = callback

    # Container registration
    def register_container(self, container, container_type: str, preserve_state=False):
        """Register a new container for preview orchestration"""
        with self._status_info_lock:
            name = container.container_name
            new_container = PreviewHolder(container, container_type, self.base_path)

            print(f"ZK ({self})Registering container: {name} of type {container_type} at {new_container.container_base_path}")
            if name in self.containers and preserve_state:
                new_container.copyRuntimeStateFrom(self.containers[name])
                
                self.containers[name] = new_container
            else:
                self.containers[name] = new_container

    def get_container_by_type(self, container_type: str) -> list[PreviewHolder]:
        """Get all containers of a specific type"""
        with self._status_info_lock:
            result =  [c for c in self.containers.values() if c.container_type == container_type]
        return result

    def get_container_by_name(self, name: str) -> PreviewHolder:
        """Get a container by its name"""
        with self._status_info_lock:
            result = self.containers.get(name)
        return result

    def set_current_container(self, name: str):
        """Set the current container by name"""
        with self._status_info_lock:
            if name in self.containers:
                self.current_container = self.containers[name]

    def get_current_container(self) -> PreviewHolder:
        """Get the currently set container"""
        return getattr(self, 'current_container', None)

    def get_dependent_containers(self, name: str) -> list[PreviewHolder]:
        """Get all containers that depend on the specified container"""
        return [c for c in self.containers.values() if name in c.depends_on]

    def setup_containers(self, manifest, preserve_state=False):
        """Set up and register containers based on the new manifest "containers" list format."""
        # Load manifest (falls back to empty dict)

        type_map = {
            "database": ApplicationType.DATABASE.value,
            "backend":  ApplicationType.BACKEND.value,
            "frontend": ApplicationType.WEB.value,
            "mobile": ApplicationType.MOBILE.value,
        }
        # Iterate over the unified containers list
        for container in manifest.containers:
            ctype = container.container_type

            if not ctype:
                continue

            self.register_container(
                container, container_type=type_map.get(ctype.value, ApplicationType.COMMON.value), preserve_state=preserve_state
            )

    def set_continue_on_dependency_failure(self, value: bool):
        """Configure whether to continue building when dependencies fail"""
        with self._status_info_lock:
            self.continue_on_dependency_failure = value

    def _refresh_containers_from_manifest(self):
        if self.manifest_path and os.path.exists(self.manifest_path):
            try:
                project = ProjectSchema.load_from_file(self.manifest_path)
                self.setup_containers(project, preserve_state=True)
            except Exception as e:
                self.logger.error(f"Failed to refresh containers from manifest: {e}")

    # Status file handling
    def _get_container_status_file(self) -> str:
        if not self.container_status_file and self.base_path:
            self.container_status_file = os.path.join(self.base_path, '.preview_containers_status')
        return self.container_status_file

    def _set_database_container_environment_variables(self, run_path: str = None, container: PreviewHolder = None):
        self.db_run_path = run_path
        cmd = f"cd {run_path}/db_visualizer && cat *.env"
        out, code = self.executor.run_one_shot_command(cmd)
        env_vars = {}
        if code == 0:
            for line in out.splitlines():
                if line.startswith("export "):
                    key, value = line[7:].split("=", 1)
                    env_vars[key.strip('"')] = value.strip('"')
        else:
            self.logger.error(f"Error fetching environment variables: {out}")
            env_vars = {}
        container.db_env_vars = env_vars
        self.set_environment_variables(env_vars)
        for key, value in env_vars.items():
            self.executor.append_env_variables({key: value})

    def _parse_env_content(self, content: str) -> dict:
        """Parse .env content into a dictionary, handling comments and export statements.
        
        Args:
            content: The raw .env file content
            
        Returns:
            Dictionary mapping environment variable names to their complete line content
        """
        env_vars = {}
        
        for line in content.split('\n'):
            if not line.strip(): # Skip empty lines
                continue
            
            if line.strip().startswith('#'): # Skip comment lines
                continue
            
            line = line.strip()
            if line.startswith('export '):
                line = line[7:]  # Remove 'export ' (7 characters)
            
            if '=' in line:
                key_without_export_prefix = line.split('=')[0].strip()
                env_vars[key_without_export_prefix] = line
                
        return env_vars

    def _append_db_env_variables(self, run_path: str):
        if not self.db_run_path:
            self.logger.warning("Database run path not set, cannot append DB env variables")
            return

        cmd = f"cd {self.db_run_path}/db_visualizer && cat *.env"
        env_content, return_code = self.executor.run_one_shot_command(cmd)

        if return_code != 0:
            self.logger.error(f"Error fetching DB environment variables: {env_content}")
            return

        cmd = f"cd {run_path} && cat .env 2>/dev/null || true"
        existing_env_content, return_code = self.executor.run_one_shot_command(cmd)

        if existing_env_content.strip() == "":
            self.logger.info(f"No existing .env file found at {run_path}, creating a new one")

        db_env_vars = self._parse_env_content(env_content)
        existing_env_vars = self._parse_env_content(existing_env_content)

        new_lines = []
        for key, line in db_env_vars.items():
            if key not in existing_env_vars:
                new_lines.append(line)

        if new_lines:
            content = '\n'.join(new_lines)
            cmd = f"cd {run_path} && echo '{content}' >> .env"
            out, return_code = self.executor.run_one_shot_command(cmd)

            if return_code == 0:
                self.logger.info(f"Appended {len(new_lines)} DB environment variables to {run_path}/.env")
                self.copied_db_env_vars = True
            else:
                self.logger.error(f"Error appending environment variables to {run_path}/.env: {out}")
        else:
            self.logger.info(f"No new DB environment variables to append to {run_path}/.env")
            self.copied_db_env_vars = True

    def _create_server_logs_file(self, working_path: str, framework: str, container_name: str) -> Path:
        sanitized_framework = ''.join(c if c.isalnum() else '_' for c in framework)
        sanitized_framework = sanitized_framework.strip('_').lower() or "framework"

        server_logs_folder = Path(working_path).parent / ".server_logs"
        server_logs_folder.mkdir(parents=True, exist_ok=True)

        log_file = server_logs_folder / f"{sanitized_framework}_server.log"
        self.logger.info(f"Initializing preview server for {container_name} ({sanitized_framework}) with logs at: {log_file}")
        return log_file

    # High-level orchestration
    def run_application(self) -> dict:
        """Start all registered containers asynchronously"""
        for name in self.containers:
            self.run_container(name)
        return {name: {"status": PreviewState.BUILDING.value} for name in self.containers}

    def run_current_container(self, run_dependencies: bool = True, restart: bool = False) -> dict:
        """Start the currently set container and its dependencies in background"""
        current_container = self.get_current_container()
        if not current_container:
            return {"status": PreviewState.FAILED.value, "message": "No current container set"}

        return self.run_container(current_container.container_name, run_dependencies, restart)

    def run_container(self, name: str, run_dependencies=False, restart=False) -> dict:
        """
        Start a single container and its dependencies in background
        This is a thread safe method that can be called from multiple threads.
        """
        with self.preview_status_lock:
            self._refresh_containers_from_manifest()
        pm_request = PreviewManager.PMRequest(name, run_dependencies, restart)
        self._message_queue.put(pm_request)

        return {"status": PreviewState.BUILDING.value, "message": f"Starting container {name}..."}

    def _find_root_container(self, containers: dict[str, PreviewHolder]) -> PreviewHolder:
        """Find a root container"""
        all_dependencies = set()
        for container in self.containers.values():
            all_dependencies.update(container.depends_on)

        for name, container in self.containers.items():
            if name not in all_dependencies:
                return container

    def run_all_containers(self, run_dependencies: bool = True) -> dict:
        """Start all registered containers and their dependencies in background"""
        root_container = self._find_root_container(self.containers)

        if not run_dependencies or not root_container:
            for name in self.containers:
                self.run_container(name, run_dependencies=False, restart=False)
        else:
            self.run_container(root_container.container_name, run_dependencies=True, restart=False)
        return {"status": PreviewState.BUILDING.value, "message": "Starting all containers..."}

    def _run_container_thread(self, name: str, run_dependencies: bool = True, restart: bool = False, _processing_chain: set = None):

        if _processing_chain is None:
            _processing_chain = set()
        
        if name in _processing_chain:
            self.logger.warning(f"Cyclic dependency detected: {name} is already being processed")
            return
        
        container = self.containers.get(name)

        if not container:
            return

        if (container.status in (PreviewState.RUNNING.value,
                                PreviewState.BUILDING.value)
                and not restart):
            # Already running or building, skip
            return

        container.status = PreviewState.BUILDING.value

        _processing_chain.add(name)
        
        # 1) Ensure dependencies are running
        failed_dependencies = []
        if run_dependencies and container.depends_on:
            for dep in container.depends_on:
                dep_c = self.containers.get(dep)
                if dep_c and dep_c.status not in (PreviewState.RUNNING.value,
                                                  PreviewState.BUILDING.value):
                    self._run_container_thread(dep, _processing_chain=_processing_chain.copy())
                    while dep_c.status not in (PreviewState.RUNNING.value, PreviewState.FAILED.value):
                        threading.Event().wait(0.5)
                    if dep_c.status != PreviewState.RUNNING.value:
                        failed_dependencies.append(dep)
                        if self.logger:
                            self.logger.warning(
                                f"Dependency {dep} failed for container {container.container_name}, "
                                f"continuing with build anyway"
                            )

        # Store failed dependencies info but continue building
        if failed_dependencies:
            container.failed_dependencies = failed_dependencies
            if not self.continue_on_dependency_failure:
                # Original behavior - fail immediately
                container.status = PreviewState.FAILED.value
                # Maintain exact old error format for single dependency (common case)
                container.error = f"Dependency {failed_dependencies[0]} failed"
                container.timestamp = datetime.now().isoformat()
                if self.build_completed_callback:
                    self.build_completed_callback(container.container_name, container.url, container.status, container.error)
                return

        # 2) Mark building and attempt to run regardless of dependency status
        container.status = PreviewState.BUILDING.value
        container.timestamp = datetime.now().isoformat()
        try:
            self._run_container(container)
            container.status = PreviewState.RUNNING.value
            container.error = None  # Clear any previous error on success

            # Add warning if running with failed dependencies
            if failed_dependencies:
                warning_msg = f"Running with failed dependencies: {', '.join(failed_dependencies)}"
                container.warning = warning_msg
                if self.logger:
                    self.logger.warning(f"Container {container.container_name}: {warning_msg}")

        except Exception as e:
            container.status = PreviewState.FAILED.value
            error_msg = str(e)
            if failed_dependencies:
                error_msg = f"{error_msg} (Failed dependencies: {', '.join(failed_dependencies)})"
            container.error = error_msg
            container.timestamp = datetime.now().isoformat()

            import traceback
            if self.logger:
                self.logger.error(f"Container {container.container_name} failed: {error_msg}")
                self.logger.error(traceback.format_exc())
            else:
                print(f"Container {container.container_name} failed: {error_msg}")
                print(traceback.format_exc())
        finally:
            container.timestamp = datetime.now().isoformat()
            if self.build_completed_callback:
                self.build_completed_callback(container.container_name, container.url, container.status, container.error)

    def _collect_service_env_variables(self):
        result = {}
        if not self.initialized_services:
            return result

        for item in self.initialized_services:
            result.update(item.get_env_list())
        return result

    def _run_mobile_specific(self, container: PreviewHolder):
        """Handle mobile container specific logic"""
        android_tools = find_tool_instance(self.agent_tool_set, "AndroidEmulatorTool")
        result = mobile_project_builder.mobile_project_builder(
            app_type=container.framework,
            android_emulator_tools=android_tools,
            project_root_path=container.container_base_path,
            executor=self.executor,
            logger=self.logger,
            restart=True
        )

        if isinstance(result, str) and result.startswith("Error"):
            raise RuntimeError(f"Mobile project builder failed: {result}")

        if not result:
            raise RuntimeError("Mobile preview build failed - no result returned")

        # Convert app URL to embed URL for preview
        container.url = result.replace("/app/", "/embed/")

        # Set up the mobile runtime logger
        self.appetize_logger = AppetizeSessionLogger(execution_base_path=self.base_path)
        self.appetize_logger.sync_logs(["Mobile preview started", f"Container URL: {container.url}"])

    def _set_container_urls(self, container: PreviewHolder, port: int):
        """Set URLs based on container type"""
        base_url = f'{os.getenv("host", "http://localhost")}:{port}'
        c : Container = container.project_schema_container

        url_path = None
        if c and c.routes:
            url_path = c.routes[0].path
            if url_path and not url_path.startswith("/"):
                url_path = f"/{url_path}"

        if container.container_type == ApplicationType.WEB.value or container.framework == FrameworkType.REACT_NATIVE.value:
            endpoint = url_path if url_path else ""
            container.url = f"{base_url}{endpoint}/preview.html"
            container.api_route = ""
        elif container.container_type == ApplicationType.BACKEND.value:
            endpoint = url_path if url_path else "/docs"
            container.url = f"{base_url}{endpoint}"
            container.api_route = f"{base_url}/openapi.json"
        elif container.container_type == ApplicationType.DATABASE.value:
            container.url = base_url

    def _run_container(self, container: PreviewHolder):
        """Generic container runner that handles common setup and delegates specific logic"""
        try:
            if self.agent_name_for_operation_mode == "CodeGeneration" and not self.waited_for_project_setup:
                universal_project_setup.wait_for_project_setup(container.container_base_path, logger=self.logger)
                self.waited_for_project_setup = True
                self.logger.info(f"Project setup completed for {container.container_name}, will skip wait on subsequent runs")

            project_dir = f"{Path(container.container_base_path)}"

            if container.container_type == ApplicationType.MOBILE.value and not container.framework == FrameworkType.REACT_NATIVE.value:
                self.logger.info(f"Running mobile preview for {container.container_name}")
                container.url = None
                self._run_mobile_specific(container)
                return

            # Get framework info (common for web/backend/database)
            run_command, run_path = universal_project_setup.get_framework_run_info(
                project_dir,
                self.logger, container.container_name
            )

            # Install container dependencies:
            if not container.installed:
                if not self._update_dependencies(project_dir, container):
                    # Log warning but continue - some containers might work without deps
                    self.logger.warning(f"Dependency installation failed for {container.container_name}, continuing anyway")

            # Add fallback logic when universal setup fails or times out
            if not run_path:
                run_path = os.path.join(self.base_path, container.container_root)
                self.logger.warning(
                    f"Universal setup path not available for {container.container_name}, using manifest path")
            else:
                if container.container_type == ApplicationType.BACKEND.value:
                    # For backend containers, we need to generate the OpenAPI spec
                    self._generate_openapi_json_file(container)

            # Container-specific setup
            if container.container_type == ApplicationType.DATABASE.value:
                self._set_database_container_environment_variables(run_path, container)
            else:
                # Web and backend containers need DB env vars
                if not self.copied_db_env_vars:
                    self._append_db_env_variables(run_path)

            # Port setup (common for all non-mobile containers)
            port = int(container.port or self.default_port)

            if port:
                self._stop_running_container_and_release_port(container.container_name, int(port))

            if container.status in [PreviewState.RUNNING.value, PreviewState.BUILDING.value]:
                if not self._update_dependencies(project_dir, container):
                    raise RuntimeError(f"Failed to install dependencies for {container.container_type} preview")

            # Add service environment variables for non-database containers
            if container.container_type != ApplicationType.DATABASE.value:
                self.executor.append_env_variables(self._collect_service_env_variables())

            # Decide on the start command. If the container has a specific start command, use it.
            # This is mainly for the scenario where the container was already built before and we stored
            # the start command in the container manifest.
            # The manifest start command takes precedence over the universal init command.
            if not container.startCommand and not run_command:
                raise RuntimeError(f"No start command available for container {container.container_name}. ")
            
            if container.startCommand != "":
                self.logger.info(f"Running {container.container_type} preview, with command: {container.startCommand} at port {port} and host {container.host}")
                start_cmd = run_command_template_to_command(container.startCommand, port, container.host, container.framework, self.executor)
            elif run_command:
                self.logger.info(f"Running {container.container_type} preview, with command: {run_command} at port {port} and host {container.host}")
                start_cmd = run_command_template_to_command(run_command, port, container.host, container.framework, self.executor)
            else:
                raise(f"No start command available for container {container.container_name}")

            # We track the running containers in a dictionary

            process = MonitoredProcess(
                self.executor,
                start_cmd, # Insert the universal init host/port values
                run_path,
                logger=self.logger,
                error_callback=MPCallback(self, container),
                # This is currently not used, but if needed this is the place where you can set environment variables
                # specific to the startup command
                # Otherwise, the environment variables are set in the executor on global level
                env={},
                )

            self.running_containers[container.container_name] = process

            process.start_process()

            is_port_ready = process.wait_for_port_open(port, timeout=30)

            if not is_port_ready:
                raise RuntimeError(f"Port {port} is not ready for {container.container_name} ({container.framework})")

            self._set_container_urls(container, port)

        except Exception as e:
            raise RuntimeError(f"{container.container_type.capitalize()} container setup failed: {str(e)}")

    # -- Helpers ------------------------------------------------------------

    def _check_and_prepare_port(self, port: int) -> bool:
        """Stop any process listening on `port`"""
        try:
            log_info_of_pid_running_on_port(port, self.executor, self.logger)
            stop_process_using_port(port, self.executor, self.logger)
        except Exception as e:
            self.logger.error(f"Error clearing port {port}: {e}")
        return True

    def _stop_running_container_and_release_port(self, container_name: str, port: int):
        # Check if container_name is in running_containers and stop it
        if container_name in self.running_containers:
            process = self.running_containers[container_name]
            process.shut_down()

        # Release the port if it was used
        self._check_and_prepare_port(port)

    def stop_container(self, name: str)  -> bool:
        """
        Stop a specific container by name

        We can stop a container if it is running or building.
        If the container is already stopped, it returns True.
        """
        with self.preview_status_lock:
            container = self.containers.get(name)
            if not container:
                return False

            if container.status == PreviewState.STOPPED.value:
                return True

            if container.status == PreviewState.RUNNING.value or container.status == PreviewState.BUILDING.value:
                if name in self.running_containers:
                    process = self.running_containers[name]
                    process.shut_down()
                    del self.running_containers[name]
                    container.status = PreviewState.STOPPED.value
                    return True

            return False

    def _update_dependencies(self, run_path: str, container : PreviewHolder) -> bool:
        """Run install dependencies command in the project's directory"""
        try:
            self.logger.info(f"Updating dependencies for {container.container_name}, expected to be {self.get_current_container()}")

            install_dependencies_command, dependencies_path = universal_project_setup.get_framework_install_dependencies_info(run_path, self.logger, container.container_name)

            build_command, build_path = universal_project_setup.get_framework_build_info(
                    run_path, self.logger, container.container_name)

            if container.container_type == ApplicationType.BACKEND.value:
                if not build_command or not build_path:
                    build_command = container.buildCommand
                    build_path = os.path.join(self.base_path, container.container_root)

                    if not (build_command and build_path):
                        self.logger.error("No build command or build path found for the backend project.")
                        return False

                self.logger.info(f"Configuring environment for {container.container_name} at {build_path}")
                cmd = f"cd {build_path} && {build_command}"
                out, code = self.executor.run_one_shot_command(cmd)

                if code != 0:
                    self.logger.warning(f"Failed to configure environment: {out}")
                    return False
                else:
                    container.installed = True
                    return True

            else:
                if not install_dependencies_command or not dependencies_path:
                    install_dependencies_command = container.installCommand
                    dependencies_path = os.path.join(self.base_path, container.container_root)

                    if not (install_dependencies_command and dependencies_path):
                        self.logger.error("No install dependencies command found for the project.")
                        return False

                self.logger.info(f"Installing dependencies for {container.container_name} at {dependencies_path}")
                cmd = f"cd {dependencies_path} && {install_dependencies_command}"
                out, code = self.executor.run_one_shot_command(cmd)

                if code != 0:
                    self.logger.warning(f"Failed to install dependencies: {out}")
                    return False
                else:
                    container.installed = True
                    return True

        except Exception as e:
            self.logger.error(f"An unexpected error occurred while installing {container.container_name} container dependencies: {e}")
            return False

    def _generate_openapi_json_file(self, container: PreviewHolder):
        # determine project directory
        project_dir = Path(container.container_base_path)
        # fetch openapi_command and openapi_path via universal setup
        openapi_command, openapi_path = universal_project_setup.get_generate_openapi_info(project_dir, self.logger, container.container_name)
        if not openapi_command or not openapi_path:
            return self.logger.error(f"OpenAPI generation command or path not found for {container.container_name}.")

        cmd = f"cd {openapi_path} && {openapi_command}"
        try:
            output, return_code = self.executor.run_one_shot_command(cmd)
            if return_code != 0:
                self.logger.error(f"Error creating openapi.json file: {output}")
                return

            self.logger.info(f"Succesfully created interfaces/openapi.json API spec for {container.container_name}")

        except Exception as e:
            self.logger.error(f"Unexpected error while generating OpenAPI spec for {container.container_name}: {str(e)}")

    def get_running_containers(self) -> dict:
        """Get a dictionary of currently running containers with their URLs"""
        running_containers = {}
        for name, container in self.containers.items():
            max_retries = 20
            while container.status == PreviewState.BUILDING.value:
                # Wait for the container to finish building
                sleep(1)
                max_retries -= 1
                if max_retries <= 0:
                    break

            if container.status == PreviewState.RUNNING.value and container.url:
                running_containers[name] = {
                    "url": container.url,
                    "type": container.container_type,
                    "framework": container.framework,
                    "workspace": container.workspace,
                }

                # Include dependency status if there were failures
                if container.failed_dependencies:
                    running_containers[name]["failed_dependencies"] = container.failed_dependencies
                    running_containers[name]["warning"] = container.warning

                if container.container_type == ApplicationType.BACKEND.value:
                    running_containers[name]["api_spec"] = container.api_route
                if container.container_type == ApplicationType.DATABASE.value:
                    # Adds the database environment variables NAMES to the env_vars
                    environment_variables_names = ', '.join(list(container.db_env_vars.keys())) if container.db_env_vars else ''
                    running_containers[name]["db_env_vars"] = environment_variables_names
        return running_containers

    def shutdown(self):
        """Shutdown the preview manager"""
        # Signal thread to exit
        self._do_exit = True

        # Stop all running containers
        for container_name, process in self.running_containers.items():
            try:
                process.shut_down()
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error stopping container {container_name}: {e}")

        # Stop message handler thread
        if self._message_handler_thread and self._message_handler_thread.is_alive():
            self._message_queue.put(self.PMRequest(name="", exit=True))
            self._message_handler_thread.join(timeout=2)

        self.containers.clear()
        self.running_containers.clear()