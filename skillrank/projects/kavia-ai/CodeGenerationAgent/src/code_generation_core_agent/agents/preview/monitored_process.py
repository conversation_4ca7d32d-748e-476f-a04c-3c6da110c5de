from code_generation_core_agent.agents.preview.port_utils import is_port_available
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerShellCallback
from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
import logging
import socket
import time
from typing import Optional
import collections


class MonitoredProcessCallback:
    """

    """
    def __init__(self, token: object):
        """
        Initialize the callback with an optional token.
        Args:
            token (Optional[object]): Optional token for additional context. The token can be an arbitrary object
                                        its purpose is to provide additional context or state.
                                        The token will be passed to the on_exit method.
        """
        self.token = token

    def on_exit(self, return_code: int, output: str, token: object):
        """
        Called when the process exits.

        Args:
            return_code (int): The exit code of the process.
            output (str): The output captured from the process.
            token (Optional[object]): Optional token for additional context. The token can be an arbitrary object
                                     its purpose is to provide additional context or state.
        """
        pass

class MonitoredProcess(DockerShellCallback):
    """
    class for tracking a state of a process
    """
    _post_fix = "; EXIT_CODE=$?; echo \"--PROCESS EXITED--\" && sleep 0.5 && exit $EXIT_CODE"

    def __init__(self, executor: ExecutorBase, cmd: str, working_dir: str,
                 logger,
                 error_callback: MonitoredProcessCallback = None,
                 env: dict[str, str] = None):

        self.executor = executor
        self.max_lines = 80
        self.output_buffer = collections.deque(maxlen=80)

        self.is_started = False
        self.cmd = cmd # shell will always exit with 0 code
        self.working_dir = working_dir
        self.logger = logger
        self.failed = False
        self.error_callback = error_callback

        if env is not None and isinstance(env, dict):
            self.env = [f"{key}={value}" for key, value in env.items()]
        else:
            self.env = []

        self.process = None  # Initialize process to None
        self.future = None  # Initialize future to None

    async def _start_long_running_process_async(self):
        """Async version of starting the process"""
        try:

            self.process = await self.executor.create_subprocess_shell(
                self.cmd + self._post_fix,
                callback=self,
                work_directory=self.working_dir,
                env=self.env
            )
            self.is_started = True
            return self.process
        except Exception as e:
            self.logger.error(f"Failed to start process: {e}")
            self.is_started = False
            raise

    def _start_long_running_process(self):
        """Start the process using shared_loop"""
        self.future = shared_loop.run_coroutine(self._start_long_running_process_async())
        return self.future

    def start_process(self):
        """
        Start the process with the given command and environment variables.
        This returns immediately - use start_process_sync() if you need to wait.
        """
        if self.future:
            return self.future

        return self._start_long_running_process()

    def start_process_sync(self):
        """
        Start the process synchronously - waits for the process to be ready.
        Returns the actual process object.
        """
        if self.is_started:
            return self.process

        future = self._start_long_running_process()
        try:
            self.process = future.result()  # This will block until process is created
            return self.process
        except Exception as e:
            self.logger.error(f"Failed to start process synchronously: {e}")
            self.is_started = False
            raise

    async def start_process_async(self):
        """
        Async version of start_process that waits for the process to actually start

        Returns:
            The process object
        """
        if self.is_started:
            return self.process

        return await self._start_long_running_process_async()

    def shut_down(self):
        """
        Shut down the process if it is running.
        """
        # If we have a future but no process yet, wait for it
        if self.future is not None and self.process is None:
            try:
                self.process = self.future.result(timeout=5)  # Wait up to 5 seconds
            except Exception as e:
                self.logger.error(f"Failed to get process from future: {e}")
                return

        if self.is_started and self.process is not None:
            try:
                # Pass the actual process object, not the future
                shared_loop.run_coroutine(self.executor.terminate_process(self.process))
                self.is_started = False
                self.process = None
            except Exception as e:
                self.logger.error(f"Error shutting down process: {e}")
        else:
            self.logger.debug(f"Process is not started or already shut down: {self.cmd}")

    async def shut_down_async(self):
        """
        Async version of shut_down
        """
        if self.is_started and self.process is not None:
            try:
                await self.executor.terminate_process(self.process)
                self.is_started = False
                self.process = None
            except Exception as e:
                self.logger.error(f"Error shutting down process: {e}")

    def on_output(self, output):
        """Handle output from the process"""
        if isinstance(output, bytes):
            output = output.decode('utf-8', errors='replace')

        lines = output.split('\n')
        self.output_buffer.extend(lines)

    def on_exit(self, return_code):
        """Called when the process exits"""
        self.is_started = False
        self.process = None
        last_output = '\n'.join(self.output_buffer)
        if return_code != 0:
            self.failed = True
            self.logger.error(f"Process '{self.cmd}' failed with code {return_code}. Last output: {last_output}")
        else:
            self.logger.info(f"Process '{self.cmd}' completed successfully with code {return_code}")
        if self.error_callback is not None:
            try:
                self.error_callback.on_exit(return_code, last_output, self.error_callback.token)
            except Exception as e:
                self.logger.error(f"Error in error callback on_exit: {e}")

    def is_failed(self):
        """
        Check if the process has failed

        Returns:
            bool: True if process has failed (non-zero exit code)
        """
        return self.failed

    def is_running(self):
        """
        Check if the process is currently running

        Returns:
            bool: True if process is started and running
        """
        # If we have a direct process reference, use that
        if self.process is not None:
            return self.is_started

        # If we have a future but no process yet, check if the future is done
        if self.future is not None and self.future.done():
            try:
                self.process = self.future.result()
                return self.is_started and self.process is not None
            except Exception:
                return False

        # Future pending or no future - use is_started flag
        return self.is_started

    def get_last_output(self):
        """
        Get the last output of the process.

        :return: The last output as a string.
        """
        return '\n'.join(self.output_buffer)

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - ensure cleanup"""
        self.shut_down()

    def wait_for_port_open(self, port: int = None,
                           timeout: float = 30.0, check_interval: float = 0.5) -> bool:
        """
        Wait for a port to be open with a timeout.

        Args:
            host: The host to check (default: "localhost")
            port: The port number to check
            timeout: Maximum time to wait in seconds (default: 30.0)
            check_interval: Time between checks in seconds (default: 0.5)

        Returns:
            bool: True if port is open within timeout, False otherwise
        """
        if port is None:
            self.logger.error("Port must be specified")
            return False

        start_time = time.time()

        while time.time() - start_time < timeout:
            is_available = is_port_available(port, self.executor, self.logger, port_range_start=port, port_range_end=port)

            # Nothing on the port, continue checking/waiting
            if is_available:
                time.sleep(check_interval)
                continue
            # Port is open, return True
            else:
                self.logger.info(f"Port {port} is open")
                return True
        self.logger.error(f"Port {port} did not open within {timeout} seconds")
        return False

    def __enter__(self):
        """Context manager entry - start the process"""
        self.start_process_sync()
        return self