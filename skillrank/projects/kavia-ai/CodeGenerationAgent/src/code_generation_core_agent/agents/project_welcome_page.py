
from code_generation_core_agent.llm.llm_interface import LLMInterface
import json

from code_generation_core_agent.project_schemas import ProjectSchema, ProjectOverview, dataclass_to_llm_schema, \
    dict_to_project_schema, get_container_empty_work_item, ContainerType


async def create_project_welcome_page(project_overview) -> ProjectSchema:

    # Create a list of keys to avoid changing the dictionary during iteration
    for key in list(project_overview.keys()):
        if project_overview[key] == "Not Selected" or project_overview[key] == "None":
            project_overview.pop(key, None)

    llm = LLMInterface( llm_api_key=None, session_dir="/tmp", instance_name="ProjectWelcomePage")

    try:

        containers_array = []

        for key in project_overview:
            if key.endswith("_framework"):
                container_type = key.replace("_framework", "")
                containers_array.append(
                    get_container_empty_work_item(ContainerType(container_type))
                )

        schema = ProjectSchema(
            overview=ProjectOverview(project_name="", description=""),
            containers=containers_array
        )

        schema_as_text = json.dumps( dataclass_to_llm_schema(schema), indent=2)
        prompt = f"""
       You are an expert software architect. 
       Your task is to review the input given by the user about a project and fill in the missing information in the provided template.
       Below is a template with placeholder values (marked with < >). Replace all placeholder values with appropriate real values based on the project description. Keep the exact same JSON structure - only replace the placeholder text.
       Remove the container types/fields not required for the project. 
       Dependencies should be in the implementation order. For example, the frontend should depend on the backend (only when they exists in the schema).
    
       Always set the framework based on the hint.
       The names should consist of lowercase words separated by underscores.
       Only output the JSON, nothing else.

        {schema_as_text}
        The values should be appropriate for the project described below. Only output the JSON, nothing else.

        Project description:
        \"\"\"
        {project_overview}
        \"\"\"
        """

        response = await llm.llm_interaction_wrapper(messages=[], user_prompt=prompt,
                                                     system_prompt="",
                                                     response_format={"type": "json_object"}, model="gpt-4.1", )


        content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
        result = json.loads(content.strip())
        processed_result = dict_to_project_schema(result)
        return processed_result
    except Exception as e:
        raise RuntimeError(f"Failed to parse LLM response: {e}\nResponse was:\n{response}")

