import logging
import os
import sys
import json
import datetime
from logging.handlers import RotatingFileHandler
from code_generation_core_agent.config import config
from code_generation_core_agent.tools.logsanitizer import LogSanitizer


class JSONFormatter(logging.Formatter):
    """
    Custom JSON formatter for log messages.
    Formats log records as JSON objects with standardized fields.
    """
    def format(self, record):
        """
        Format the log record as a JSON object.
        
        Parameters:
        -----------
        record : logging.LogRecord
            The log record to format
            
        Returns:
        --------
        str : JSON formatted log entry
        """
        # Create the base log object
        log_object = {
            'timestamp': datetime.datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger_name': record.name,
            'agent_type': record.name,  # Using logger name as agent_type
            'message': record.getMessage(),
        }

        if record.exc_info:
            log_object['exception'] = self.formatException(record.exc_info)

        # This will add ALL extra fields including log_type and event_type
        for key, value in record.__dict__.items():
            # Skip standard LogRecord attributes and private attributes
            if key not in ['args', 'asctime', 'created', 'exc_info', 'exc_text', 
                          'filename', 'funcName', 'id', 'levelname', 'levelno', 
                          'lineno', 'module', 'msecs', 'message', 'msg', 'name', 
                          'pathname', 'process', 'processName', 'relativeCreated', 
                          'stack_info', 'thread', 'threadName'] and not key.startswith('_'):
                log_object[key] = value

        try:
            return json.dumps(log_object)
        except Exception as e:
            # Fallback if JSON serialization fails
            return json.dumps({
                'timestamp': datetime.datetime.now().isoformat(),
                'level': 'ERROR',
                'logger_name': record.name,
                'agent_type': record.name,
                'message': f'Error formatting log message: {str(e)}',
                'original_message': str(record.msg)
            })


# PUBLIC_INTERFACE
def setup_logger(name, log_path, level=logging.INFO, config_dir=None):
    """
    Sets up a logger with both JSON and plain-text handlers (dual-sink).

    Parameters:
    -----------
    name : str
        Name of the logger
    log_path : str
        Either a direct log file path or a base directory path
    level : int, optional
        Logging level (default: logging.INFO)
    config_dir : str, optional
        Configuration directory. If provided, logs will be saved in config_dir/basename(log_path)/logs.
        If not provided, it will check the config module.

    Returns:
    --------
    logging.Logger: Logger with JSON + plain text sinks + optional console sink.
    """
    # Check config module if config_dir not explicitly provided
    if config_dir is None:
        config_dir = config.get("SYSTEM", "config_dir")

    # Determine log directory
    if config_dir:
        # Get the last directory from log_path
        base_path = os.path.basename(os.path.normpath(log_path))
        forced_log_dir = os.path.join(config_dir, base_path)
        os.makedirs(forced_log_dir, exist_ok=True)
    else:
        forced_log_dir = log_path

    # Enable all logging
    logging.disable(logging.NOTSET)

    # Get or create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Prevent propagation to parent loggers
    logger.propagate = False

    # Create logs directory if it doesn't exist
    log_dir = os.path.join(forced_log_dir, "logs")
    os.makedirs(log_dir, exist_ok=True)

    # Create file handler
    file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
    file_handler.setLevel(level)

    # Create formatter
    # === Pretty Sink (local file) ===
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)

    # Add handler to logger
    logger.addHandler(file_handler)

    # === JSON Sink ===
    json_log_dir = os.path.join(log_dir, "json")  # New subdirectory for JSON logs
    os.makedirs(json_log_dir, exist_ok=True)      # Ensure it exists

    json_file_path = os.path.join(json_log_dir, f"{name}.json.log")
    json_handler = logging.FileHandler(json_file_path)
    json_handler.setLevel(level)
    json_handler.setFormatter(JSONFormatter())
    logger.addHandler(json_handler)

    # === Console Sink (optional) ===
    try:
        enable_console = config.get("LOGGING", "enable_console_logging")
        if enable_console.lower() in ('true', '1', 'yes', 'on'):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(level)
            # Use a simpler format for console output
            console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
    except Exception as e:
        # If config reading fails, silently continue without console logging
        pass

    # === Add LogSanitizer filter ===
    logger.addFilter(LogSanitizer())

    return logger