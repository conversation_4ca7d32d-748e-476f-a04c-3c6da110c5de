import json

from .base_tool_interface import BaseToolInterface
from .schemas.decorators import register_function
from .shared_loop import shared_loop

class ReviewStatusTools(BaseToolInterface):

    def get_tool_name(self):
        return "ReviewStatusTools"

    def __init__(self, callback_functions, base_path, logger, llm, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.output_callback = callback_functions.get("terminal_output_callback", None)
        self.logger = logger
        self.llm = llm

        if not self.output_callback:
            self.logger.warning("Output callback not provided for ReviewStatusTools.")

    def _send_output(self, message):
        if self.output_callback:
            self.output_callback(message)

    @register_function(
        description="Review the progress made so far and suggest a new plan if necessary.",
        parameters={
            "type": "object",
            "properties": {
                "actions_taken": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of actions taken so far.",
                },
                "results_observed": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Results observed from actions taken.",
                },
                "current_state": {
                    "type": "string",
                    "description": "Summary of the current state of the problem.",
                }
            },
            "required": ["actions_taken", "results_observed", "current_state"]
        }
    )
    def progress_and_plan(self, actions_taken, results_observed, current_state):
        self.logger.info("Starting review_progress_and_plan")
        try:
            future = shared_loop.run_coroutine(
                self._async_review_progress_and_plan(actions_taken, results_observed, current_state)
            )
            result = future.result(timeout=30)  # Adjust timeout as needed
            self.logger.info("review_progress_and_plan completed successfully")
            return result
        except Exception as e:
            self.logger.error(f"Error in review_progress_and_plan: {str(e)}")
            return {"status": "ERROR", "message": f"Review failed: {str(e)}"}

    async def _async_review_progress_and_plan(self, actions_taken, results_observed, current_state):
        self._send_output("Reviewing progress and planning next steps...")

        prompt = f"""
        Based on the following summary of actions and results:

        Actions taken: {actions_taken}
        Results observed: {results_observed}
        Current state: {current_state}

        Please analyze the progress made so far, identify any patterns of repetition or ineffective strategies, 
        and suggest a new approach. Consider the following:

        1. Are we making progress towards solving the problem?
        2. Are we repeating any actions without different results?
        3. What are the main obstacles we're facing?
        4. Is there an alternative approach we haven't considered?
        5. Should we continue with a modified plan or conclude that the current approach is not viable?

        Provide your analysis and recommendation in the following JSON format:
        {{
            "analysis": "<your analysis of the situation>",
            "new_approach": "<description of the new approach, if any>",
            "continue": <true/false>,
            "reason": "<reason for continuing or stopping>"
        }}
        """

        response = await self.llm.llm_interaction_wrapper(
            messages=[{"role": "user", "content": prompt}],
            user_prompt=None,
            system_prompt="You are an expert problem-solver analyzing the progress of a complex task.",
            model="gpt-4o",
            response_format={"type": "json_object"}
        )

        result = json.loads(response.choices[0].message.content)

        self._send_output(f"Analysis completed. Recommendation: {'Continue' if result['continue'] else 'Stop'}")

        return result
