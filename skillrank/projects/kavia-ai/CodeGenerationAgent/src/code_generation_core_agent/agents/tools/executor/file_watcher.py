import json
import os
import shlex
import time

from code_generation_core_agent.agents.tools.container_file_tools import DockerFileToolsError
from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
import concurrent.futures
from code_generation_core_agent.config import config

SHELL_CMD_DEFAULT_TIMEOUT = config.getint("TOOLS", "shell_command_timeout")
FUTURE = None

def get_full_path(base_path, file_path):
    normalized_path = os.path.normpath(file_path)
    if normalized_path.startswith('..'):
        raise ValueError("Access to parent directories is not allowed")
    return os.path.join(base_path, normalized_path)

def start_long_running_process(command, executor):
    future = shared_loop.run_coroutine(executor.create_subprocess_shell(command))
    return future

def run_docker_command(executor, cmd, work_directory=None):
    async def _one_shot_docker_command(wdir):
        stdout_data, return_code = await executor.create_subprocess_shell_one_shot(cmd, work_directory=wdir)
        return stdout_data, return_code

    try:
        future = shared_loop.run_coroutine(_one_shot_docker_command(work_directory))
        output, returncode = future.result(timeout=SHELL_CMD_DEFAULT_TIMEOUT)
        if returncode != 0:
            raise DockerFileToolsError(f"Command {cmd} failed with {returncode}. Error: ```{output}```")
        return output
    except concurrent.futures.TimeoutError:
        raise DockerFileToolsError(f"Docker command execution timed out"
                                   f" after {SHELL_CMD_DEFAULT_TIMEOUT} seconds")
    except DockerFileToolsError:
        raise
    except Exception as e:
        raise DockerFileToolsError(f"Error executing Docker command. Error: {str(e)}")

def extract_working_directory(file_content):
    try:
        data = json.loads(file_content)
        if "working_directory" in data:
            return data["working_directory"]
        else:
            return None
    except json.JSONDecodeError:
        return None

def read_file(file_path, base_path, executor):
    try:
        full_path = get_full_path(base_path, file_path)
        escaped_path = shlex.quote(full_path)
        cmd = f"cat {escaped_path}"

        output = run_docker_command(executor, cmd, work_directory=base_path)
        return output

    except ValueError as e:
        return None
    except Exception as e:
        import traceback
        traceback.print_exc()
        return None


def start_file_watcher(executor: ExecutorBase, work_directory: str, logger) -> None:
    # Read .init/.init-build-cmd from the work directory using read_file
    file_path = ".init/.init-build-cmd"
    logger.info(f"Reading file: {file_path}")
    # Read the file content
    file_content = read_file(file_path, work_directory, executor)

    app_path = extract_working_directory(file_content)
    global FUTURE

    FUTURE = start_long_running_process(f"cd {app_path} && nohup watch.py -n 20 -d {app_path} --update-interval 1 -o {work_directory}/.file_activity_report.json &> /tmp/watch.log &", executor)
