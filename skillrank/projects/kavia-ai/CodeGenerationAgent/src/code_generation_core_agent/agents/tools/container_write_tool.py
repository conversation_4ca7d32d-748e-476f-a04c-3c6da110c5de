import os
import shlex
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.config import config

auto_ci = config.getboolean("TOOLS", "auto_ci", fallback=False)


class ContainerWriteTools(BaseToolInterface):
    """
    Tool for writing files using the existing ContainerFileTools _write_file method.
    This tool is specifically designed for use by the DynamicSetupAgent
    to avoid conflicts with other file writing approaches.
    """
    
    def get_tool_name(self):
        return "ContainerWriteTools"

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.logger = logger
        # Create a ContainerFileTools instance to use its _write_file method
        from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools
        self.container_file_tools = ContainerFileTools(callback_functions, base_path, logger, llm, executor)

    @register_function(
        description=(
            "Writes content to a file. Creates the file if it doesn't exist, or overwrites it if it does. "
            "Use this to create new files or replace existing file content. "
            "The function will create any necessary directories in the path. "
            "This function is specifically designed for container setup operations. "
            "Example: ContainerWriteTools_write_file(file_path='.init/install.sh', content='#!/bin/bash\\necho \"Hello World\"')"
        ),
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "Relative path to the file from base_directory."
                },
                "content": {
                    "type": "string",
                    "description": "Content to write to the file."
                }
            },
            "required": ["file_path", "content"]
        }
    )
    def write_file(self, file_path, content):
        """
        Write content to a file using the existing ContainerFileTools _write_file method.
        
        Args:
            file_path (str): Path to the file to write
            content (str): Content to write to the file
            
        Returns:
            dict: Status and result of the file operation
        """
        result = self.container_file_tools._write_file(file_path, content, None, None, False, create=True)
        return result
