# Function call tracker interface

class FunctionCallDescription():
    def __init__(self, time: int, function_name: str, arguments: str, reason: str,
                 observations: str, root_cause_analysis: str):
        self.time = time
        self.function_name = function_name
        self.arguments = arguments
        self.reason = reason
        self.observations = observations
        self.root_cause_analysis = root_cause_analysis



class FunctionCallTraker():
    def trace_function_call(self, time : int, function_name: str, arguments: str, reason: str,
                            observations: str, root_cause_analysis: str):
        pass

    def trace_function_return(self, function_name: str, arguments: str, is_error: bool, data: str):
        pass

