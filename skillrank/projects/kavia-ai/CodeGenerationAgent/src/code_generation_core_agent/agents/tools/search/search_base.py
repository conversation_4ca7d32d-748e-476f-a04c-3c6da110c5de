import abc
import logging
from typing import Dict, List, Optional, Any

from code_generation_core_agent.agents.tools.base_tool import BaseTool


class SearchResult:
    """Class representing a single search result."""

    def __init__(
            self,
            title: str,
            url: Optional[str] = None,
            snippet: Optional[str] = None,
            metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a search result.

        Args:
            title: Title of the search result
            url: URL of the search result (optional)
            snippet: Short description or snippet from the content (optional)
            metadata: Additional metadata about the result (optional)
        """
        self.title = title
        self.url = url
        self.snippet = snippet
        self.metadata = metadata or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert the search result to a dictionary."""
        return {
            "title": self.title,
            "url": self.url,
            "snippet": self.snippet,
            "metadata": self.metadata
        }
    def __str__(self):
        """String representation of the search result."""
        return f"SearchResult(title={self.title}, url={self.url}, snippet={self.snippet}, metadata={self.metadata})"

class SearchResponse:
    """Class representing a complete search response."""

    def __init__(
            self,
            results: List[SearchResult],
            query: str,
            total_results: Optional[int] = None,
            search_engine: Optional[str] = None,
            answer: Optional[str] = None,
            error: Optional[str] = None
    ):
        """
        Initialize a search response.

        Args:
            results: List of search results
            query: The original search query
            total_results: Total number of results available (optional)
            search_engine: Name of the search engine used (optional)
            answer: Direct answer if available (optional)
            error: Error message if the search failed (optional)
        """
        self.results = results
        self.query = query
        self.total_results = total_results
        self.search_engine = search_engine
        self.answer = answer
        self.error = error

    def to_dict(self) -> Dict[str, Any]:
        """Convert the search response to a dictionary."""
        return {
            "results": [result.to_dict() for result in self.results],
            "query": self.query,
            "total_results": self.total_results,
            "search_engine": self.search_engine,
            "answer": self.answer,
            "error": self.error
        }


class BaseSearchAPI(abc.ABC):
    """
    Base class for online search APIs (Google, DuckDuckGo, Perplexity, etc.).

    This class provides a common interface for different search engines.
    Implement this class to support specific search engines.
    """

    def __init__(
            self,
            logger: Optional[logging.Logger] = None,
            api_key: Optional[str] = None,
            timeout: int = 30,
            max_results: int = 10,
            safe_search: bool = True
    ):
        """
        Initialize the search API.

        Args:
            logger: Logger instance
            api_key: API key for the search service (if required)
            timeout: Timeout for API requests in seconds
            max_results: Maximum number of results to return
            safe_search: Whether to enable safe search filtering
        """
        self.api_key = api_key
        self.timeout = timeout
        self.max_results = max_results
        self.safe_search = safe_search
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        self.logger = logger or logging.getLogger(__name__)

    @abc.abstractmethod
    def search(
            self,
            query: str,
            num_results: Optional[int] = None,
            **kwargs
    ) -> SearchResponse:
        """
        Perform a search with the given query.

        Args:
            query: Search query string
            num_results: Number of results to return (defaults to self.max_results if None)
            **kwargs: Additional search engine specific parameters

        Returns:
            SearchResponse object containing the search results
        """
        pass

    def validate_api_key(self) -> bool:
        """
        Validate that the API key is valid.

        Returns:
            True if the API key is valid, False otherwise
        """
        return self.api_key is not None

    def parse_response(self, raw_response: Any, query: str, max_results : int) -> SearchResponse:
        """
        Parse the raw response from the search API into a standardized SearchResponse.

        Args:
            raw_response: The raw response from the search API

        Returns:
            SearchResponse object containing the parsed results
        """
        raise NotImplementedError("Subclasses must implement parse_response()")

    def build_search_url(self, query: str, **params) -> str:
        """
        Build the URL for the search request.

        Args:
            query: Search query
            **params: Additional parameters for the search URL

        Returns:
            The complete URL for the search request
        """
        raise NotImplementedError("Subclasses must implement build_search_url()")

    def handle_error(self, error: Exception) -> SearchResponse:
        """
        Handle errors that occur during the search.

        Args:
            error: The exception that occurred

        Returns:
            SearchResponse with error information
        """
        error_message = f"Search failed: {str(error)}"
        self.logger.error(error_message)
        return SearchResponse(results=[], query="", error=error_message)