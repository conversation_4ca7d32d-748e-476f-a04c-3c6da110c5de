import re
import shlex
from typing import List, Dict, Any, <PERSON><PERSON>
import json

import concurrent.futures
from litellm import completion
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.container_file_tools import SHELL_CMD_DEFAULT_TIMEOUT, \
    DockerFileToolsError, ContainerFileTools
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
from code_generation_core_agent.agents.framework.cost_tracker import CostTracer
from code_generation_core_agent.config import config


def custom_stdout_logger(messages):
    # print("litellm stdout logger: ", messages)
    pass


class StrategicAnalysisError(Exception):
    """Custom exception for StrategicAnalysisTool errors."""
    pass


class StrategicAnalysisTool(BaseToolInterface):
    def get_tool_name(self):
        return "StrategicAnalysisTool"

    def _run_docker_command(self, cmd, work_directory=None):
        async def _one_shot_docker_command(wdir):
            stdout_data, return_code = await self.executor.create_subprocess_shell_one_shot(cmd, work_directory=wdir)
            return stdout_data, return_code

        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command(work_directory))
            output, returncode = future.result(timeout=SHELL_CMD_DEFAULT_TIMEOUT)
            if returncode != 0:
                raise DockerFileToolsError(f"Command {cmd} failed with {returncode}. Error: ```{output}```")
            return output
        except concurrent.futures.TimeoutError:
            raise DockerFileToolsError(f"Docker command execution timed out"
                                       f" after {SHELL_CMD_DEFAULT_TIMEOUT} seconds")
        except DockerFileToolsError:
            raise
        except Exception as e:
            raise DockerFileToolsError(f"Error executing Docker command. Error: {str(e)}")

    def _quick_docker_command(self, cmd, work_directory=None, timeout=1):
        """Run docker command with a very short timeout"""
        async def _one_shot_docker_command(wdir):
            stdout_data, return_code = await self.executor.create_subprocess_shell_one_shot(cmd, work_directory=wdir)
            return stdout_data, return_code

        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command(work_directory))
            output, returncode = future.result(timeout=timeout)  # Only 1 second timeout
            if returncode != 0:
                raise DockerFileToolsError(f"Command {cmd} failed with {returncode}")
            return output
        except concurrent.futures.TimeoutError:
            raise DockerFileToolsError(f"Docker command execution timed out after {timeout} seconds")
        except DockerFileToolsError:
            raise
        except Exception as e:
            raise DockerFileToolsError(f"Error executing Docker command. Error: {str(e)}")

    def _read_file(self, file_path):
        try:
            self.logger.info(f"Reading file {file_path}")
            full_path = ContainerFileTools._get_full_path(self.base_path, file_path)
            escaped_path = shlex.quote(full_path)
            
            # Quick check with 1-second timeout
            check_cmd = f"ls {escaped_path} 2>/dev/null"
            try:
                self._quick_docker_command(check_cmd, timeout=1)
            except DockerFileToolsError:
                self.logger.info(f"File {file_path} does not exist")
                return ""

            # If file exists, read it with normal timeout
            cmd = f"cat {escaped_path}"
            self.logger.info(f"Executing command: {cmd}")
            output = self._run_docker_command(cmd)
            return output
            
        except Exception as e:
            from traceback import print_exc
            print_exc()
            self.logger.error(f"Error reading file {file_path}: {str(e)}")
            return ""
    @staticmethod
    def parse_json_with_cleanup(response_string):
        """
        Attempts to parse JSON from a string, with fallback cleanup for markdown-formatted JSON.

        Args:
            response_string (str): The string containing JSON, potentially with markdown formatting

        Returns:
            dict: Parsed JSON object

        Raises:
            json.JSONDecodeError: If JSON parsing fails even after cleanup
        """
        try:
            return json.loads(response_string)
        except json.JSONDecodeError:
            cleaned = re.sub(r'```json\s*|\s*```', '', response_string)
            cleaned = cleaned.strip()
            return json.loads(cleaned)

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.logger = logger
        self.knowledge_lookup = callback_functions.get("knowledge_lookup", None)
        self.cost_tracer = CostTracer()

        self.executor = executor
        self.use_reasoning: bool = config.getboolean('TOOLS', 'use_reasoning')

    def _read_file_content(self, file_path: str) -> Tuple[str, str]:
        """
        Helper function to read file content.

        Args:
            file_path: Path to the file to read

        Returns:
            Tuple containing (file_content, error_message)
            If successful, error_message will be empty string
            If failed, file_content will be empty string
        """
        try:
            return self._read_file(file_path), ""
        except Exception as e:
            error_msg = f"Failed to read file {file_path}: {str(e)}"
            self.logger.error(error_msg)
            return "", error_msg

    async def _execute_operation(self, operation, *args, **kwargs):
        """Execute an operation with standard error handling."""
        try:
            result = await operation(*args, **kwargs)
            return {"status": "SUCCESS", **result}
        except StrategicAnalysisError as e:
            self.logger.error(f"Strategic analysis error: {str(e)}")
            return {"status": "ERROR", "message": str(e)}
        except Exception as e:
            self.logger.error(f"Unexpected error in strategic analysis: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"status": "ERROR", "message": f"An unexpected error occurred: {str(e)}"}

    async def _get_llm_analysis(self, prompt: str) -> Dict[str, Any]:
        from asyncio import get_event_loop
        """Get strategic analysis from LLM."""
        try:
            messages = [
                {"role": "system", "content": "You are a strategic code analysis system."},
                {"role": "user", "content": prompt}
                ]
            self.logger.info(f"Getting LLM analysis with prompt: {prompt}")
            response = await get_event_loop().run_in_executor(
                None,
                lambda: completion(
                    model="o3-mini",
                    messages=messages,
                    logger_fn=custom_stdout_logger,
                )
            )
            self.cost_tracer.add_cost_from_completion_response("StrategicAnalysisTool", response)
            self.logger.info(f"LLM response: {response.choices[0].message.content}")
            return self.parse_json_with_cleanup(response.choices[0].message.content)
        except Exception as e:
            self.logger.error(f"LLM analysis failed: {str(e)}")
            raise StrategicAnalysisError(f"Failed to get LLM analysis: {str(e)}")

    #@register_function(
        description="""Analyzes the current context using LLM and provides strategic guidance.
        Takes into account the files to be processed, current understanding, relevant knowledge keywords,
        agent type, and the desired goal to achieve. Called when a strategic analysis is needed for a task or when the previous strategic analysis is fully implemented.""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_paths": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of file paths to be analyzed"
                },
                "current_state": {
                    "type": "string",
                    "description": "Detailed description of the current state and understanding of the project. This MUST always include the progress made so far and the changes done."
                },
                "knowledge_keywords": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Keywords for knowledge base lookup"
                },
                "agent_type": {
                    "type": "string",
                    "description": "Type of agent making the call (e.g., 'CodeWritingAgent', 'TestWritingAgent')"
                },
                "goal": {
                    "type": "string",
                    "description": "The high level description of the current goal."
                }
            },
            "required": [
                "file_paths",
                "current_state",
                "knowledge_keywords",
                "agent_type",
                "goal"
            ],
            "additionalProperties": False
        }
    #)
    def analyze_context(
            self,
            file_paths: List[str],
            current_state: str,
            knowledge_keywords: List[str],
            agent_type: str,
            goal: str
    ) -> Dict[str, Any]:
        """
        Analyzes the context using LLM and provides structured strategic guidance.

        Args:
            file_paths: List of files to be processed
            current_state: Current problem understanding text
            knowledge_keywords: Keywords for knowledge lookup
            agent_type: Type of agent making the call
            goal: Desired goal to achieve

        Returns:
            Dict containing:
            - status: SUCCESS/ERROR
            - refined_understanding: Adjusted understanding of the problem
            - strategic_approach: High-level approach suggestion
            - action_steps: List of specific actionable items
            - challenges_and_mitigations: Potential challenges and solutions
            - relevant_knowledge: Knowledge snippets from lookup
            - warnings: Optional warnings or considerations
            - unreadable_files: List of files that couldn't be read
        """

        files_content = []
        unreadable_files = []
        for file_path in file_paths:
            content, error = self._read_file_content(file_path)
            if error:
                print(f"Error reading file {file_path}: {error}")
                unreadable_files.append({"file": file_path, "error": error})
            else:
                print(f"Strategy analysis: Read file {file_path}")
                files_content.append({"file": file_path, "content": content})

        async def operation():

            if not goal:
                return {"status": "ERROR", "message": "A clear goal must be specified"}
            # Log the request
            self.logger.info(f"Processing strategic analysis for {agent_type} agent with goal: {goal}")


            relevant_knowledge = []
            if self.knowledge_lookup:
                try:
                    for keyword in knowledge_keywords:
                        knowledge_result = self.knowledge_lookup(keyword)
                        if knowledge_result:
                            relevant_knowledge.extend(knowledge_result)
                except Exception as e:
                    self.logger.warning(f"Knowledge lookup failed: {str(e)}")

            # Get LLM analysis
            context = {
                "files_content": files_content,
                "current_state": current_state,
                "knowledge_keywords": knowledge_keywords,
                "agent_type": agent_type,
                "goal": goal
            }

            #llm_analysis = await self._get_llm_analysis(context)

            result = {
                **llm_analysis,
                "relevant_knowledge": relevant_knowledge,
                "unreadable_files": unreadable_files
            }

            warnings = []
            if unreadable_files:
                warnings.append(f"Failed to read {len(unreadable_files)} files")
            if len(file_paths) > 10:
                warnings.append("Large number of files to process - consider batch processing")
            if warnings:
                result["warnings"] = warnings

            return  {
                "status": "SUCCESS",
                "value": f"{result}"
            }

        future  = shared_loop.run_coroutine(self._execute_operation(operation))
        return future.result()

    @register_function(
        description="""Reviews and validates an implementation plan against the codebase and project context.
        Analyzes the provided plan, files, and context to ensure the plan is complete, accurate, and achievable.
        This function should be called only once in a session and only if the plan is complex enough to require a detailed review.""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "implementation_plan": {
                    "type": "object",
                    "properties": {
                        "steps": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "description": {"type": "string", "description": "Description of the implementation step"},
                                    "details": {"type": "string", "description": "Additional details that describes all aspects of the step"},
                                    "files_to_modify": {
                                        "type": "array",
                                        "items": {"type": "string"},
                                        "description": "Relative paths of files (from base_folder) that will be modified in this step"
                                    },
                                    "expected_changes": {"type": "string", "description": "Expected changes to be made"},
                                },
                                "required": ["description", "files_to_modify", "expected_changes"]
                            },
                            "description": "Ordered list of implementation steps"
                        },
                        "assumptions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of assumptions made in the plan"
                        }
                    },
                    "required": ["steps"]
                },
                "project_context": {
                    "type": "object",
                    "properties": {
                        "goal": {"type": "string", "description": "Overall goal of the implementation"},
                        "requirements": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Specific requirements to be met"
                        },
                        "constraints": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Any constraints or limitations"
                        }
                    },
                    "required": ["goal"]
                },
                "agent_type": {
                    "type": "string",
                    "description": "Type of agent requesting the review"
                }
            },
            "required": [
                "implementation_plan",
                "project_context",
                "agent_type"
            ],
            "additionalProperties": False
        }
    )
    def review_implementation_plan(
            self,
            implementation_plan: Dict[str, Any],
            project_context: Dict[str, Any],
            agent_type: str
    ) -> Dict[str, Any]:
        """
        Reviews and validates an implementation plan against the codebase and project context.

        Args:
            implementation_plan: The proposed implementation plan with steps, files, and assumptions
            project_context: Context about the project including goals and requirements
            agent_type: Type of agent requesting the review

        Returns:
            Dict containing:
            - status: SUCCESS/ERROR
            - review_result: {
                - is_valid: Whether the plan is valid
                - validation_details: Specific validation findings
                - suggested_changes: List of suggested changes to the plan
                - risks_and_mitigations: Identified risks and suggested mitigations
                - missing_prerequisites: Any missing prerequisites or dependencies
                - file_specific_feedback: Feedback about specific files in the plan
            }
            - warnings: Optional warnings about the plan or review process
        """
        async def operation():
            # Validate input plan structure
            if not implementation_plan.get("steps"):
                return {"status": "ERROR", "message": "Implementation plan must contain steps"}

            # Collect all files referenced in the plan
            all_files = set()
            for step in implementation_plan["steps"]:
                all_files.update(step.get("files_to_modify", []))

            # Read all referenced files
            files_content = []
            unreadable_files = []
            for file_path in all_files:
                content, error = self._read_file_content(file_path)
                if error:
                    unreadable_files.append({"file": file_path, "error": error})
                else:
                    files_content.append({"file": file_path, "content": content})

            # Look up relevant knowledge
            relevant_knowledge = []
            if knowledge_keywords and self.knowledge_lookup:
                try:
                    for keyword in knowledge_keywords:
                        knowledge_result = self.knowledge_lookup(keyword)
                        if knowledge_result:
                            relevant_knowledge.extend(knowledge_result)
                except Exception as e:
                    self.logger.warning(f"Knowledge lookup failed: {str(e)}")

            # Prepare context for LLM analysis
            context = {
                "files_content": files_content,
                "implementation_plan": implementation_plan,
                "project_context": project_context,
                "agent_type": agent_type,
                "relevant_knowledge": relevant_knowledge
            }

            # Get LLM analysis
            prompt = f"""As a strategic code analysis system, review the following implementation plan and context:

    Context:
    - Agent Type: {agent_type}
    - Project Goal: {project_context['goal']}
    - Implementation Plan Steps: {json.dumps(implementation_plan['steps'], indent=2)}
    - Files Content: {files_content}
    - Knowledge Base Findings: {relevant_knowledge}

    Review the implementation plan for:
    1. Completeness and correctness
    2. Technical feasibility
    3. Alignment with project goals
    4. File modifications accuracy
    5. Dependencies and prerequisites
    6. Potential risks and challenges

    Provide a structured review including:
    1. Overall plan validity
    2. Specific validation findings
    3. Suggested improvements or changes
    4. Risk assessment and mitigations
    5. File-specific feedback
    6. Missing prerequisites or dependencies

    Format the response as a JSON object with these specific keys:
    is_valid (boolean), validation_details (string), suggested_changes (array), risks_and_mitigations (array), missing_prerequisites (array), file_specific_feedback (object with file paths as keys)"""

            llm_analysis = await self._get_llm_analysis( prompt)

            # Prepare warnings
            warnings = []
            if unreadable_files:
                warnings.append(f"Failed to read {len(unreadable_files)} files: {', '.join(f['file'] for f in unreadable_files)}")
            if len(implementation_plan["steps"]) > 10:
                warnings.append("Large number of implementation steps - consider breaking down into smaller phases")

            return {
                "status": "SUCCESS",
                "review_result": {
                    **llm_analysis,
                    "unreadable_files": unreadable_files
                },
                "warnings": warnings if warnings else None
            }

        if self.use_reasoning:
            future = shared_loop.run_coroutine(self._execute_operation(operation))
            return future.result()
        else:
            return {"status": "SUCCESS", "review_result": "Plan looks good. Please proceed."}