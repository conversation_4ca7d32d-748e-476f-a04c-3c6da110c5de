import traceback

import yaml
from typing import Any, Dict, List, Tuple
import os
import re
from string import Formatter


class PreCheckError(Exception):
    pass

class Check:
    """
    Check class that executes a check code and returns a result.
    """
    def __init__(self, name: str, code: str, error_message: str):
        self.name = name
        self.code = code
        self.error_message = error_message

    def execute(self, arg_name: str, value: Any, **kwargs) -> Tuple[bool, str, str]:
        try:
            local_scope = {'value': value, 're': re, **kwargs}
            exec(self.code, {}, local_scope)

            if 'result' not in local_scope:
                raise PreCheckError("Check code must set a 'result' variable")

            result = local_scope['result']
            if not isinstance(result, bool):
                raise PreCheckError("'result' must be a boolean value")

            if result:
                return True, "", ""
            else:
                return False, self.error_message.format(arg_name=arg_name, value=value, **kwargs), ""
        except Exception as e:
            error_message = f"Error in check '{self.name}': {str(e)}"
            backtrace = traceback.format_exc()
            return False, error_message, backtrace


class PreCheckLoader:
    """
    Class to load pre-checks from a YAML file and execute them.
    """
    def __init__(self, yaml_file: str):
        self.yaml_file = yaml_file
        self.checks: Dict[str, Check] = {}
        self.function_checks: Dict[str, Dict[str, List[Dict[str, Any]]]] = {}
        self.base_path = None
        self.script_dir = os.path.dirname(os.path.abspath(__file__))

    def load_yaml(self, base_path: str):
        self.base_path = base_path

        yaml_path = os.path.join(self.script_dir, self.yaml_file)
        config = self._load_yaml_with_includes(yaml_path, base_path)

        for check in config['checks']:
            self.checks[check['name']] = Check(check['name'], check['code'], check['error_message'])

        self.function_checks = config['functions']

    def _load_yaml_with_includes(self, yaml_path: str, base_path: str) -> Dict:
        with open(yaml_path, 'r') as file:
            content = file.read()
            content = CustomFormatter().format(content, base_path=base_path)
            config = yaml.safe_load(content)

        if 'include' in config:
            for include_file in config['include']:
                include_path = os.path.join(os.path.dirname(yaml_path), include_file)
                included_config = self._load_yaml_with_includes(include_path, base_path)
                self._merge_configs(config, included_config)

        return config

    def _merge_configs(self, main_config: Dict, included_config: Dict):
        for key, value in included_config.items():
            if key not in main_config:
                main_config[key] = value
            elif isinstance(main_config[key], dict) and isinstance(value, dict):
                self._merge_configs(main_config[key], value)
            elif isinstance(main_config[key], list) and isinstance(value, list):
                main_config[key].extend(value)

    def execute_pre_checks(self, function_name: str, kwargs: Dict[str, Any]) -> Tuple[bool, str, str]:
        if function_name not in self.function_checks:
            print(f"No pre-checks defined for function '{function_name}'")
            return True, "", ""

        for arg_name, checks in self.function_checks[function_name].items():
            if arg_name not in kwargs:
                return False, f"Required argument '{arg_name}' is missing", ""

            for check_info in checks:
                check = self.get_check(check_info['name'])
                if not check:
                    return False, f"Unknown check: {check_info['name']}", ""

                # Changed: Combine kwargs and check_info
                execution_context = {**kwargs, **check_info}
                result, error_message, backtrace = check.execute(arg_name, kwargs[arg_name], **execution_context)
                if not result:
                    return False, error_message, backtrace

        return True, "", ""

    def get_check(self, name: str) -> Check:
        return self.checks.get(name)


class CustomFormatter(Formatter):
    def get_value(self, key, args, kwargs):
        if isinstance(key, str):
            return kwargs.get(key, '{' + key + '}')
        else:
            return super().get_value(key, args, kwargs)


