from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function 
from code_generation_core_agent.config import config
import os
import requests
import base64
import traceback
from typing import Dict, Any, Optional, Union

class AndroidEmulatorTool(BaseToolInterface):
    def get_tool_name(self):
        return "AndroidEmulatorTool"
    
    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        # Initialize service configuration
        self.service = config.get("TOOLS", "android_emulator_service", fallback="appetize")
        self.api_url = config.get("TOOLS", "android_emulator_service_url", fallback="https://api.appetize.io/v1/apps")
        self.api_key = os.getenv('APPETIZE_TOKEN')
        self.output_callback = callback_functions.get('terminal_output_callback', None)
        self.logger = logger
        self.executor = executor
        from code_generation_core_agent.agents.preview import PreviewManager
        self.preview_manager = PreviewManager()

    def _get_container_root_dir(self, project_path:str):
        manifest_path = os.path.join(project_path, ".project_manifest.yaml")
        current_container = self.preview_manager.get_current_container()
        if current_container and os.path.exists(manifest_path):
            try:
                from code_generation_core_agent.project_schemas import ProjectSchema
                project_schema = ProjectSchema.load_from_file(manifest_path)
                container = project_schema.get_container_by_name(current_container.container_name)
                if container:
                    container_root = container.container_root
                    if container_root:
                        return os.path.join(project_path, container_root)
                    else:
                        self.logger.warning(f"Container '{current_container}' has no root directory defined in project manifest.")
                        return None
                else:
                    self.logger.warning(f"Container '{current_container}' not found in project manifest.")
                    return None
            except Exception as e:
                self.logger.error(f"Failed to load project manifest from {self}: {str(e)}")
                return None

    def _send_output(self, message):
        if self.output_callback:
            self.output_callback(message)
            
    def _authorization_header(self):
        auth_str = self.api_key + ':'
        encoded_bytes = base64.b64encode(auth_str.encode('utf-8'))
        encoded_str = encoded_bytes.decode('utf-8')
        return {
            'Authorization': f'Basic {encoded_str}'
        }
    
    @register_function(
        description="Build an Android application (APK) from source code",
        parameters={
            "type": "object",
            "properties": {
                "project_path": {"type": "string", "description": "Path to the Android or Flutter project"},
                "app_type": {"type": "string", "description": "Type of application (android or flutter)"},
                "output_path": {"type": "string", "description": "Path where the APK should be saved"},
                "first_run": {"type": "boolean", "description": "Indicates if this is the first build run"}
            },
            "required": ["project_path", "app_type", "output_path", "first_run"]
        }
    )
    def build_application(self, project_path, app_type, output_path, first_run) -> Union[bool, str]:
        """
        Build an Android application (APK) from source code.
        
        Args:
            project_path: Path to the Android or Flutter project
            app_type: Type of application (android or flutter)
            output_path: Path where the APK should be saved
            
        Returns:
            Union[bool, str]: True if successful, error message with stacktrace if failed
        """
        try:
            self.logger.info(f"Building {app_type} application in {project_path}")
            self._send_output(f"Starting build process for {app_type} application...")
            
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            if app_type.lower() == "android" or app_type.lower() == "kotlin":
                result = self._build_android(project_path, output_path, first_run)
            elif app_type.lower() == "flutter":
                result = self._build_flutter(project_path, output_path, first_run)
            else:
                error_msg = f"Unsupported app type: {app_type}. Supported types are 'android' and 'flutter'."
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            # Check if result is an error message
            if isinstance(result, str) and result.startswith("Error:"):
                return result
            
            return True if result else "Error: Build failed - no output path returned"
                
        except Exception as e:
            error_msg = f"Error building application: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            self._send_output(f"Error building application: {str(e)}")
            return f"Error: {error_msg}"
    
    def _handle_gradle_locks(self, project_path):
        """Remove Gradle lock files that might prevent builds"""
        try:
            command = "find . -name '*.lock' -type f -delete"
            self.executor.run_one_shot_command(cmd=command, work_directory=project_path.__str__())
            self.logger.info("Removed Gradle lock files")
            return True
        except Exception as e:
            self.logger.warning(f"Failed to remove lock files: {str(e)}")
            return False

    def _build_android(self, project_path, output_path, first_run) -> Union[str, str]:
        """
        Build an Android application.
        
        Returns:
            Union[str, str]: Output path if successful, error message if failed
        """
        try:
            if not first_run:
                # Clean up any existing Gradle daemons
                stop_command = "./gradlew --stop"
                self.logger.info(f"Stopping Gradle daemons: {stop_command}")
                self.executor.run_one_shot_command(cmd=stop_command, timeout=60, work_directory=project_path.__str__())

                # Clean up lock files
                self._handle_gradle_locks(project_path)
            
            # Execute Gradle build command
            command = f"./gradlew --no-daemon --console=plain assembleDebug"
            
            self._send_output(f"Running command: {command}")
            stdout, return_code = self.executor.run_one_shot_command(cmd=command, timeout=900, work_directory=project_path.__str__())
            
            if return_code != 0:
                error_msg = f"Build failed with return code {return_code}: {stdout}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            # Find and copy the APK to the output location
            apk_path = self._find_apk_android(project_path, "debug")
            if not apk_path:
                error_msg = "Could not find generated APK"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"

            move_command = f"mv {apk_path} {output_path}"
            move_stdout, move_return_code = self.executor.run_one_shot_command(move_command)
            
            if move_return_code != 0:
                error_msg = f"Failed to move APK to output location: {move_stdout}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            self._send_output(f"Build successful. APK saved to {output_path}")
            return output_path
            
        except Exception as e:
            error_msg = f"Error in Android build: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
    
    def _build_flutter(self, project_path, output_path, first_run) -> Union[str, str]:
        """
        Build a Flutter application.
        
        Returns:
            Union[str, str]: Output path if successful, error message if failed
        """
        try:
            # Clean up any existing Gradle daemons
            if os.path.exists(os.path.join(project_path, "android")) and not first_run:
                android_dir = os.path.join(project_path, "android")

                self.logger.info("Stopping Gradle daemons in Flutter's Android directory...")
                if os.path.exists(os.path.join(android_dir, "gradlew")):
                    stop_command = "./gradlew --stop"
                    self.executor.run_one_shot_command(cmd=stop_command, timeout=60, work_directory=android_dir.__str__())
                
                # Clean up lock files
                self._handle_gradle_locks(android_dir)
            
            # Now run the Flutter build command
            command = f"flutter build apk --release --target-platform android-x64"
            
            self._send_output(f"Running command: {command}")
            stdout, return_code = self.executor.run_one_shot_command(cmd=command, timeout=900, work_directory=project_path.__str__())
            
            if return_code != 0:
                error_msg = f"Flutter build failed with return code {return_code}: {stdout}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            # Flutter's APK is typically in a specific location
            flutter_apk_path = os.path.join(project_path, "build", "app", "outputs", "apk", "release", 
                                          f"app-release.apk")
            
            if not os.path.exists(flutter_apk_path):
                error_msg = f"Could not find generated Flutter APK at {flutter_apk_path}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            move_command = f"mv {flutter_apk_path} {output_path}"
            move_stdout, move_return_code = self.executor.run_one_shot_command(move_command)
            
            if move_return_code != 0:
                error_msg = f"Failed to move Flutter APK to output location: {move_stdout}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            self._send_output(f"Flutter build successful. APK saved to {output_path}")
            return output_path
            
        except Exception as e:
            error_msg = f"Error in Flutter build: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
    
    def _find_apk_android(self, project_path, build_type) -> Optional[str]:
        """
        Find the APK file in the Android project outputs
        
        Returns:
            Optional[str]: Path to the APK file or None if not found
        """
        possible_paths = [
            # Main module
            os.path.join(project_path, "app", "build", "outputs", "apk", build_type, "app-" + build_type + ".apk"),
            # Dynamic module name
            os.path.join(project_path, "app", "build", "outputs", "apk", build_type)
        ]
        
        # Check direct paths first
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # If not found, search in the build directory
        search_dir = os.path.join(project_path, "app", "build", "outputs", "apk")
        if os.path.exists(search_dir):
            for root, dirs, files in os.walk(search_dir):
                for file in files:
                    if file.endswith(".apk"):
                        return os.path.join(root, file)
        
        return None
    
    @register_function(
        description="Upload an Android APK to the configured emulator service",
        parameters={
            "type": "object",
            "properties": {
                "apk_path": {"type": "string", "description": "Path to the APK file to upload"}
            },
            "required": ["apk_path"]
        }
    )
    def upload_application(self, apk_path) -> Union[Dict[str, Any], str]:
        """
        Upload an Android APK to the configured emulator service.
        
        Args:
            apk_path: Path to the APK file to upload
            
        Returns:
            Union[Dict[str, Any], str]: Dictionary with upload response information if successful, error message if failed
        """
        try:
            self.logger.info(f"Uploading APK: {apk_path}")
            self._send_output(f"\nUploading APK to {self.service}...")
            
            if not os.path.exists(apk_path):
                error_msg = f"APK file not found at {apk_path}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            if self.service.lower() == "appetize":
                result = self._upload_to_appetize(apk_path)
                if isinstance(result, str) and result.startswith("Error:"):
                    return result
                return result
            else:
                error_msg = f"Unsupported emulator service: {self.service}. Currently only 'appetize' is supported."
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
                
        except Exception as e:
            error_msg = f"Error uploading application: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            self._send_output(f"Error uploading application: {str(e)}")
            return f"Error: {error_msg}"
    
    def _upload_to_appetize(self, apk_path) -> Union[Dict[str, Any], str]:
        """
        Upload an APK to Appetize.io.
        
        Returns:
            Union[Dict[str, Any], str]: Dictionary with upload response information if successful, error message if failed
        """
        try:
            if not self.api_key:
                error_msg = "Appetize API key not set in environment (APPETIZE_TOKEN)"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            files = {
                'file': (os.path.basename(apk_path), open(apk_path, 'rb'))
            }
            
            data = {
                'platform': 'android',
                'fileType': 'apk',
                'appPermissions.run': 'public',
                'appPermissions.debugLog': 'public',
                'appPermissions.adbConnect': 'public',
            }
            
            self._send_output("\nSending request to Appetize.io...")
            response = requests.post(
                self.api_url, 
                headers=self._authorization_header(), 
                data=data, 
                files=files
            )
            
            if response.status_code != 200:
                error_msg = f"Appetize API error (status {response.status_code}): {response.text}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            result = response.json()
            public_key = result.get('publicKey')
            app_url = result.get('publicURL')
            manage_url = result.get('manageURL')
            
            if not public_key or not app_url:
                error_msg = f"Invalid response from Appetize API: missing publicKey or publicURL"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            self._send_output(f"Upload successful. Your app is available at: {app_url}")
            
            return {
                "public_key": public_key,
                "app_url": app_url,
                "manage_url": manage_url
            }
            
        except Exception as e:
            error_msg = f"Error in Appetize upload: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
    
    @register_function(
        description="Update an existing application in the configured emulator service",
        parameters={
            "type": "object",
            "properties": {
                "apk_path": {"type": "string", "description": "Path to the updated APK file"},
                "public_key": {"type": "string", "description": "Public key of the existing application"}
            },
            "required": ["apk_path", "public_key"]
        }
    )
    def update_application(self, apk_path, public_key) -> Union[str, str]:
        """
        Update an existing application in the configured emulator service.
        
        Args:
            apk_path: Path to the updated APK file
            public_key: Public key of the existing application
            
        Returns:
            Union[str, str]: App URL if successful, error message if failed
        """
        try:
            self.logger.info(f"Updating application with key {public_key}")
            self._send_output(f"Updating application in {self.service}...")
            
            if not os.path.exists(apk_path):
                error_msg = f"APK file not found at {apk_path}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            if self.service.lower() == "appetize":
                return self._update_in_appetize(apk_path, public_key)
            else:
                error_msg = f"Unsupported emulator service: {self.service}. Currently only 'appetize' is supported."
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
                
        except Exception as e:
            error_msg = f"Error updating application: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            self._send_output(f"Error updating application: {str(e)}")
            return f"Error: {error_msg}"
    
    def _update_in_appetize(self, apk_path, public_key) -> Union[str, str]:
        """
        Update an APK in Appetize.io.
        
        Returns:
            Union[str, str]: App URL if successful, error message if failed
        """
        try:
            if not self.api_key:
                error_msg = "Appetize API key not set in environment (APPETIZE_TOKEN)"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            url = f'{self.api_url}/{public_key}'

            files = {
                'file': (os.path.basename(apk_path), open(apk_path, 'rb'))
            }

            data = {
                'platform': 'android',
                'fileType': 'apk',
                'appPermissions.run': 'public',
                'appPermissions.debugLog': 'public',
                'appPermissions.adbConnect': 'public',
            }
            
            self._send_output(f"Sending update request to Appetize.io for app {public_key}...")
            response = requests.post(
                url, 
                headers=self._authorization_header(), 
                data=data, 
                files=files
            )
            
            if response.status_code != 200:
                error_msg = f"Appetize API error (status {response.status_code}): {response.text}"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            result = response.json()
            app_url = result.get('publicURL')
            
            if not app_url:
                error_msg = f"Invalid response from Appetize API: missing publicURL"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            self._send_output(f"Update successful. Your updated app is available at: {app_url}")
            
            return app_url
            
        except Exception as e:
            error_msg = f"Error in Appetize update: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
    
    @register_function(
        description="Run static analysis on Flutter code to check for errors",
        parameters={
            "type": "object", 
            "properties": {
                "project_path": {"type": "string", "description": "Path to the Flutter project"}
            },
            "required": ["project_path"]
        }
    )
    def analyze_flutter_code(self, project_path) -> Union[bool, str]:
        """
        Run flutter analyze to check for code issues.
        
        Returns:
            Union[bool, str]: True if no issues, error message if issues found
        """
        try:
            command = "flutter analyze"
            work_directory = self._get_container_root_dir(project_path)
            if not work_directory:
                error_msg = f"Could not determine project root directory for Flutter project"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            stdout, return_code = self.executor.run_one_shot_command(
                cmd=command, 
                timeout=300, 
                work_directory=work_directory
            )
            
            if return_code != 0:
                self.logger.warning(f"Flutter analyze found issues: {stdout}")
                return f"Flutter analysis issues: {stdout}"
            
            self.logger.info("Flutter analyze passed - no issues found")
            return True
            
        except Exception as e:
            error_msg = f"Error running flutter analyze: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"

    @register_function(
        description="Run static analysis on Android/Kotlin code to check for errors",
        parameters={
            "type": "object",
            "properties": {
                "project_path": {"type": "string", "description": "Path to the Android project"}
            },
            "required": ["project_path"]
        }
    )
    def analyze_android_code(self, project_path) -> Union[bool, str]:
        """
        Run gradlew check to validate Android/Kotlin code.
        
        Returns:
            Union[bool, str]: True if no issues, error message if issues found
        """
        try:
            command = "./gradlew check --console=plain"
            work_directory = self._get_container_root_dir(project_path)
            if not work_directory:
                error_msg = f"Could not determine project root directory for Android/Kotlin project"
                self.logger.error(error_msg)
                return f"Error: {error_msg}"
            
            stdout, return_code = self.executor.run_one_shot_command(
                cmd=command,
                timeout=600,
                work_directory=work_directory
            )
            
            if return_code != 0:
                self.logger.warning(f"Gradle check found issues: {stdout}")
                return f"Android analysis issues: {stdout}"
            
            self.logger.info("Gradle check passed - no issues found")
            return True
            
        except Exception as e:
            error_msg = f"Error running gradle check: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"