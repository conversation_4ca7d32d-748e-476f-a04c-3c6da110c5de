checks:
  - name: regex
    code: |
      result = re.match(pattern, str(value)) is not None
    error_message: "Argument '{arg_name}' with value '{value}' does not match pattern '{pattern}'"

  - name: range
    code: |
      min_val, max_val = min, max
      result = min_val <= float(value) <= max_val
    error_message: "Argument '{arg_name}' with value {value} is not in range [{min}, {max}]"

  - name: length
    code: |
      min_length, max_length = min, max
      result = min_length <= len(str(value)) <= max_length
    error_message: "Argument '{arg_name}' length must be between {min} and {max}"