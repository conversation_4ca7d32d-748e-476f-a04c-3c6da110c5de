import os
import shutil

from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function

class IOSTools(BaseToolInterface):
    def get_tool_name(self):
        return 'IOSTools'

    def __init__(self, callbacks, base_path, logger, llm=None, executor=None):
        super().__init__(callbacks, base_path, logger, llm, executor)
        self.base_path = base_path
        self.logger = logger
        self.executor = executor
        self.default_template_name = "iOSAppSwift"
        self.default_makefile_path = os.path.join('ios', 'Makefile')
        self.already_called_create_ios_app = False

    def set_output_callback(self, callback):
        self.output_callback = callback

    def _copy_file(self, file_path: str = None, working_directory: str = None):
        if not file_path: file_path = self.default_makefile_path
        if not working_directory: working_directory = self.base_path

        shutil.copy2(file_path, working_directory)
    
    def _copy_template(self, template_name: str = None, working_directory: str = None):
        if not template_name: template_name = self.default_template_name
        if not working_directory: working_directory = self.base_path

        template_path = os.path.join('ios', 'templates', template_name)
        destination_path = os.path.join(working_directory, self.default_template_name)

        shutil.copytree(template_path, destination_path, dirs_exist_ok=True)

    def _execute_cmd(self, cmd):
        import subprocess
        try:
            process = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                check=False
            )
            output = str(process.stdout + process.stderr)
            returncode = int(process.returncode)
            return output, returncode 
        except subprocess.SubprocessError as e:
            output = str(e)
            returncode = -1
            return output, returncode
    
    def _format_output(self, output, returncode):
        status = "SUCCESS" if returncode == 0 else "ERROR"
        formatted_output = {
            "status": status,
            "output": str(output),
            "returncode": int(returncode)
        }
        return formatted_output

    def _run_makefile_command(self, app_name: str = "iOSAppSwift", target_command: str = "build", working_directory: str = None):
        if not working_directory: working_directory = self.base_path
        make_command_prefix = f"make -C {working_directory} -f {working_directory}/Makefile"

        command = f"{make_command_prefix} {target_command} APP_NAME={app_name}"
        output, returncode = self._execute_cmd(command)

        return self._format_output(output, returncode)

    @register_function(
        description="""Builds the iOS application.
        Use it to build the current iOS application when needed.
        Usage example: build_ios_app(app_name='iOSAppSwift')""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "app_name": {
                    "type": "string",
                    "enum": ["iOSAppSwift", "iOSAppObjectiveC"],
                    "description": "Name of the iOS app to build. If not provided, it uses default template for a Swift app, be sure to provide it if not a swift app is requested."
                }
            },
            "required": []
        }
    )
    def build_ios_app(self, app_name: str = "iOSAppSwift"):
        working_directory = self.base_path
        target_command = "build"
        return self._run_makefile_command(
            app_name,
            target_command,
            working_directory
        )
    
    @register_function(
        description="""Runs tests for the iOS application.
        Use it to run the current test cases.
        Usage example: test_ios_app(app_name='iOSAppSwift')""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "app_name": {
                    "type": "string",
                    "enum": ["iOSAppSwift", "iOSAppObjectiveC"],
                    "description": "Name of the iOS app to test. If not provided, it uses default template for a Swift app, be sure to provide it if not a swift app is requested."
                }
            },
            "required": []
        }
    )
    def test_ios_app(self, app_name: str = "iOSAppSwift"):
        working_directory = self.base_path
        target_command = "test"
        return self._run_makefile_command(
            app_name,
            target_command,
            working_directory
        )
    
    @register_function(
        description="""Runs the iOS application in the simulator.
        Use it to run the application if needed, make sure to build it first.
        Usage example: run_ios_app(app_name='iOSAppSwift')""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "app_name": {
                    "type": "string",
                    "enum": ["iOSAppSwift", "iOSAppObjectiveC"],
                    "description": "Name of the iOS app to run. If not provided, it uses default template for a Swift app, be sure to provide it if not a swift app is requested."
                }
            },
            "required": []
        }
    )
    def run_ios_app(self, app_name: str = "iOSAppSwift"):
        working_directory = self.base_path
        target_command = "run"
        return self._run_makefile_command(
            app_name,
            target_command,
            working_directory
        )
    
    @register_function(
        description="""Takes a screenshot from the iOS simulator and saves the file screenshot.png to the current working directory.
        Use it to check the current state of the application running inside the simulator.
        Usage example: take_screenshot_from_ios_simulator()""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {},
            "required": []
        }
    )
    def take_screenshot_from_ios_simulator(self):
        working_directory = self.base_path
        target_command = "take-screenshot"
        return self._run_makefile_command(
            target_command=target_command,
            working_directory=working_directory
        )

    @register_function(
        description="""Starts the iOS simulator.
        Use it to start the ios simulator if needed.
        Usage example: start_ios_simulator()""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {},
            "required": []
        }
    )
    def start_ios_simulator(self):
        working_directory = self.base_path
        target_command = "start-simulator"
        return self._run_makefile_command(
            target_command=target_command,
            working_directory=working_directory
        )

    @register_function(
        description="""Creates a new iOS application from a template. 
        Usage example: create_ios_app(working_directory='/tmp/kavia/codegen-1157', template_name='iOSAppSwift')""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "working_directory": {
                    "type": "string",
                    "description": "Path where the iOS app will be created. If not provided, it uses base_path."
                },
                "template_name": {
                    "type": "string",
                    "enum": ["iOSAppSwift", "iOSAppObjectiveC"],
                    "description": "Template to use for the iOS app. If not provided, it uses default template for a Swift app, be sure to provide it if not a swift app is requested."
                }
            },
            "required": []
        }
    )
    def create_ios_app(self, working_directory: str = None, template_name: str = None):
        if self.already_called_create_ios_app:
            return self._format_output('This function was already called before', 0)
        if not working_directory: working_directory = self.base_path
        if not template_name: template_name = self.default_template_name

        self._copy_file(self.default_makefile_path, working_directory)
        self._copy_template()

        self.already_called_create_ios_app = True
        return self._format_output('Created minimal template successfully!', 0)
