#!/usr/bin/env python3
"""
Example usage of the Perplexity search API.
This demonstrates how to initialize and use the PerplexitySearchAPI class.
"""

import os
import logging
import json
from pathlib import Path
from typing import Optional

from code_generation_core_agent.agents.tools.search.perplexity import PerplexitySearchAPI
from code_generation_core_agent.agents.tools.search.search_base import SearchResultType


def setup_logger():
    """Set up a basic logger for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger("perplexity_example")


def perform_search(
    api_key: str,
    query: str,
    result_type: Optional[SearchResultType] = None,
    max_results: int = 5
):
    """
    Perform a search using the Perplexity API.
    
    Args:
        api_key: Perplexity API key
        query: Search query
        result_type: Type of results to search for (optional)
        max_results: Maximum number of results to return
    """
    logger = setup_logger()
    base_path = str(Path.cwd())
    
    # Initialize the Perplexity search API
    perplexity = PerplexitySearchAPI(
        base_path=base_path,
        logger=logger,
        api_key=api_key,
        max_results=max_results
    )
    
    # Validate the API key
    if not perplexity.validate_api_key():
        logger.error("API key validation failed. Please check your API key.")
        return
    
    # Perform the search
    search_type = result_type or SearchResultType.WEB_PAGE
    logger.info(f"Searching for: '{query}' (type: {search_type.value})")
    
    response = perplexity.search(
        query=query,
        result_type=search_type,
        num_results=max_results
    )
    
    # Display the results
    print("\n" + "="*50)
    print(f"Search Query: {response.query}")
    print(f"Search Engine: {response.search_engine}")
    print(f"Total Results: {response.total_results}")
    print("="*50)
    
    # Display direct answer if available
    if response.answer:
        print("\nDirect Answer:")
        print("-"*50)
        print(response.answer)
        print("-"*50)
    
    # Display search results
    print("\nSearch Results:")
    for i, result in enumerate(response.results, 1):
        print(f"\n[{i}] {result.title} ({result.result_type.value})")
        if result.url:
            print(f"URL: {result.url}")
        if result.snippet:
            print(f"Snippet: {result.snippet[:150]}...")
        print("-"*30)
    
    # Return the response for potential further processing
    return response


def main():
    """Main function to run the example."""
    # Get API key from environment variable
    api_key = os.environ.get("PERPLEXITY_API_KEY")
    if not api_key:
        print("Please set the PERPLEXITY_API_KEY environment variable.")
        print("Example: export PERPLEXITY_API_KEY='your-api-key'")
        return
    
    # Example searches
    examples = [
        {
            "query": "What is quantum computing?",
            "result_type": SearchResultType.WEB_PAGE
        },
        {
            "query": "latest AI research breakthroughs",
            "result_type": SearchResultType.NEWS
        },
        {
            "query": "pandas in bamboo forest",
            "result_type": SearchResultType.IMAGE
        }
    ]
    
    # Run the examples
    for example in examples:
        print(f"\n\nRunning search for: {example['query']}")
        perform_search(
            api_key=api_key,
            query=example["query"],
            result_type=example["result_type"]
        )
        input("\nPress Enter to continue to next example...")
    
    # Custom query from user input
    custom_query = input("\nEnter your own search query (or press Enter to exit): ")
    if custom_query:
        perform_search(api_key=api_key, query=custom_query)


if __name__ == "__main__":
    main() 