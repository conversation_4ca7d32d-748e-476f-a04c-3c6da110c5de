"""
Interface for the base tools
"""

from abc import ABC, abstractmethod, ABCMeta

from code_generation_core_agent.agents.tools.schemas import decorators


class ToolFunction(ABC):
    """
    Descriptor of a single function that can be executed by the tool.
    """

    def __init__(self, name, schema):
        self.name = name
        self.schema = schema


class DynamicFunctionExecutor(ABC):
    """
    Function executors are classes that can execute functions directly.
    The primary use is to define functions that may be dynamically constructed.
    """

    _registered_schemas = []

    def call_dynamic_function(self, function_name, **kwargs):
        """
        Dynamically call a function.

        These functions are not defined in the class, but are created dynamically
        byt the tool that inherits this class.
        """
        pass

    def register_schema(self, schema):
        """
        Register a schema with the function executor.
        Must be in the format:
        {
            "name": "function_name",
            "schema": {
                "type": "object",
                "properties": {
                    "param1": {"type": "string"},
                    "param2": {"type": "string"}
                },
                "required": ["param1", "param2"]
            }
        }
        """
        self._registered_schemas.append(schema)

    def get_registered_schemas(self):
        return self._registered_schemas


class CombinedBaseToolMeta(ABCMeta, decorators.AutoRegisterFunctionsMeta):
    pass


class BaseToolInterface(metaclass=CombinedBaseToolMeta):
    """
    Interface for the base tools. All tools should implement this interface.
    """

    _available_functions = []

    def __init__(self, callback_functions: dict[str, callable],
                 base_path: str,
                 logger=None, llm=None, executor=None):
        """
        Initialize the tool.

        :param callback_functions: Map of all callback functions handled by this tool.
        :param base_path: Base path for the tool.
        :param logger: Logger for the tool.
        :param llm: Local Loop Manager for the tool.
        :param executor: Executor for the tool.
        """

        self.callback_functions = callback_functions

    def shutdown(self):
        """
        Shutdown the tool.
        """
        pass

    def update_base_path(self, base_path: str):
        """
        Update the base path for the tool.

        :param base_path: New base path for the tool.
        """
        self.base_path = base_path

    def get_registered_functions(self):
        """
        Get all registered functions for this tool.
        """
        return {name: func for name, func in self.__class__.__dict__.items()
                if hasattr(func, '_metadata')}

    def register_functions(self, schema):
        """
        Register a functions with the tool.
        """
        pass

    def callback(self, callback_function_name, *args, **kwargs):
        """
        Call the callback function.

        :param callback_function_name: Name of the callback function.
        :param args: Arguments to pass to the callback function.
        :param kwargs: Keyword arguments to pass to the callback function.
        """

        try:
            function = self.callback_functions[callback_function_name]

        except KeyError:
            raise ValueError(f"Callback function {callback_function_name} not found. ")
        # Call the function
        function(*args, **kwargs)

    def set_output_callback(self, callback):
        self.callback = callback

    @abstractmethod
    def get_tool_name(self):
        """
        Get the name of the tool.
        """
        return self.__class__.__name__


    def get_schema(self):
        """
        Get the schema for the tool.
        """
        pass

    def get_available_functions(self):
        """
        Get the available functions for the tool.
        """
        return self._available_functions
