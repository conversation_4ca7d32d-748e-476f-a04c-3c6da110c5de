import asyncio
from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
from code_generation_core_agent.agents.tools.shared_loop import shared_loop

class LocalExecutor(ExecutorBase):
    async def create_subprocess_shell(self, cmd, **kwargs):
        if 'stdout' not in kwargs:
            kwargs['stdout'] = asyncio.subprocess.PIPE
        if 'stderr' not in kwargs:
            kwargs['stderr'] = asyncio.subprocess.PIPE
        
        process = await asyncio.create_subprocess_shell(cmd, **kwargs)
        return process

    def run_one_shot_command(self, cmd, timeout=60) -> (str, int):
        async def _run_cmd():
            process = await self.create_subprocess_shell(cmd)
            try:
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout - 1)
                return stdout.decode('utf-8').strip(), process.returncode
            
            except Exception as e:
                try:
                    process.kill()
                    await process.wait()
                except: pass
                return f"An unexpected exception happened: {e}", 1
        
        try:
            future = shared_loop.run_coroutine(_run_cmd())
            output, return_code = future.result(timeout)
            return output, return_code
        except Exception as e:
            return f"Error executing command: {str(e)}", 1

    async def terminate_process(self, process):
        process.terminate()
        await process.wait()

    def create_task(self, task):
        return asyncio.create_task(task)

    async def wait(self, fs, timeout=None):
        return await asyncio.wait(fs, timeout=timeout)

    async def gather(self, *futures):
        return await asyncio.gather(*futures)

    def run_coroutine_threadsafe(self, coroutine, loop):
        return asyncio.run_coroutine_threadsafe(coroutine, loop)


    async def wait_for(self, coroutine, timeout):
        return await asyncio.wait_for(coroutine, timeout)