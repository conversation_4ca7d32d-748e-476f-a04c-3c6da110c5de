from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
from code_generation_core_agent.database.database_connection import connect_node_db

from typing import List, Dict, Any, Optional
import json

class GraphCrawlTools(BaseToolInterface):
    def get_tool_name(self):
        return "GraphCrawlTools"

    def __init__(self, callbacks, base_path, logger, llm=None, executor=None):
        super().__init__(callbacks, base_path, logger, llm, executor)
        self.db = self._get_db_connection()
        self.logger = logger

    def _get_db_connection(self):
        return connect_node_db()

    def _run_db_operation(self, coroutine):
        try:
            future = shared_loop.run_coroutine(coroutine)
            return future.result(timeout=30)  # Adjust timeout as needed
        except Exception as e:
            self.logger.error(f"Error in database operation: {str(e)}")
            raise

    def _format_node_info(self, node):
        """Helper method to format node information consistently."""
        return {
            "id": node.get("id"),
            "title": node.get("title"),
            "description": node.get("description"),
            "summary": node.get("summary"),
            "type": node.get("type")
        }

    @register_function(
        description="Get all nodes representing the code graph connected to a given node and their corresponding relationships, including direction.",
        parameters={
            "type": "object",
            "properties": {
                "node_id": {"type": "integer", "description": "ID of the node"}
            },
            "required": ["node_id"]
        }
    )
    def get_connected_codegraph_nodes(self, node_id):
        try:
            # Using the new function to get connected nodes and relationships
            connected_nodes_and_relationships = self._run_db_operation(
                self.db.get_connected_codegraph_nodes_and_relationships(node_id)
            )
            return {
                "status": "SUCCESS",
                "connected_nodes_and_relationships": connected_nodes_and_relationships
            }
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}

    @register_function(
        description="Get detailed contents of a specific node",
        parameters={
            "type": "object",
            "properties": {
                "node_id": {"type": "integer", "description": "ID of the node"}
            },
            "required": ["node_id"]
        }
    )
    def get_node_contents(self, node_id):
        try:
            node_data = self._run_db_operation(self.db.get_node_by_id(node_id))
            if node_data:
                #formatted_node = self._format_node_info(node_data)
                return {"status": "SUCCESS", "node_contents": node_data}
            else:
                return {"status": "ERROR", "message": "Node not found"}
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}

    @register_function(
        description="Get details of relationships between two different nodes",
        parameters={
            "type": "object",
            "properties": {
                "source_id": {"type": "integer", "description": "node_id of the source node"},
                "target_id": {"type": "integer", "description": "node_id of the target node"},
                "relationship_type": {"type": "string", "description": "Type of relationship (optional)"}
            },
            "required": ["source_id", "target_id"]
        }
    )
    def get_relationship_details(self, source_id, target_id, relationship_type=None):
        try:
            relationship = self._run_db_operation(self.db.get_relationship(source_id, target_id, relationship_type or "HAS_CHILD"))
            if relationship:
                return {"status": "SUCCESS", "relationship": relationship}
            else:
                return {"status": "ERROR", "message": "Relationship not found"}
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}

    '''@register_function(
        description="Search for nodes based on properties",
        parameters={
            "type": "object",
            "properties": {
                "node_id": {"type": "integer", "description": "ID of the node to search from"},
                "properties": {
                    "type": "object",
                    "description": "Properties to match (can include title, description, or summary)"
                }
            },
            "required": ["properties"]
        }
    )
    def search_nodes(self, properties):
        try:
            nodes = self._run_db_operation(
                self.db.get_nodes_by_property("Node", list(properties.keys())[0], list(properties.values())[0])
            )
            formatted_nodes = [self._format_node_info(node) for node in nodes]
            return {"status": "SUCCESS", "matching_nodes": formatted_nodes}
        except Exception as e:
            self.logger.error(f"Error in search_nodes: {str(e)}")
            return {"status": "ERROR", "message": f"Error searching nodes: {str(e)}"}'''