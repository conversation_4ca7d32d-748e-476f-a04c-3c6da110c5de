import inspect
import os
from functools import wraps

from code_generation_core_agent.config import config

strict_mode_enabled = config.getboolean("LLM", "strict_mode_enabled")

def register_function(**kwargs):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs_in):
            return func(*args, **kwargs_in)

        sig = inspect.signature(func)
        parameters = sig.parameters
        schema_properties = kwargs.get("parameters", {}).get("properties", {})
        schema_required = kwargs.get("parameters", {}).get("required", [])
        wrapper._validator = kwargs.get("validator")

        for param_name, param in parameters.items():
            if param_name == 'self':
                continue
            if param_name not in schema_properties:
                if param_name == 'attributes':
                    continue

                if 'attributes' in schema_properties:
                    attr_properties = schema_properties['attributes'].get("properties", {})
                    if param_name not in attr_properties:
                        raise ValueError(f"Function parameter '{param_name}' is not defined in the schema or attributes.")
                else:
                    raise ValueError(f"Function parameter '{param_name}' is not defined in the schema.")

        # Validate schema properties against function parameters
        for prop_name, prop_schema in schema_properties.items():
            if prop_name == 'attributes':
                attr_properties = prop_schema.get("properties", {})
                for attr_name in attr_properties:
                    if attr_name not in parameters and 'attributes' not in parameters:
                        raise ValueError(f"Schema attribute '{attr_name}' is not a parameter of the function.")
            elif prop_name not in parameters:
                raise ValueError(f"Schema property '{prop_name}' is not a parameter of the function.")

        # Validate required properties
        for required_prop in schema_required:
            if required_prop not in parameters:
                if required_prop == 'attributes':
                    # Check if any of the attribute properties are parameters
                    attr_properties = schema_properties['attributes'].get("properties", {})
                    if not any(attr in parameters for attr in attr_properties):
                        raise ValueError(f"Required property 'attributes' in the schema is not represented in the function parameters.")
                else:
                    raise ValueError(f"Required property '{required_prop}' in the schema is not a parameter of the function.")

        wrapper._is_registered = True

        parameters = kwargs.get("parameters", {})

        if "additionalProperties" not in parameters and len(parameters) > 0:
            parameters["additionalProperties"] = False

        wrapper._metadata = {
            "type": "function",
            "function": {
                "name": func.__name__,
                "description": kwargs.get("description", ""),
                "strict": strict_mode_enabled,
                "parameters": parameters
            }
        }
        if "claude" in os.getenv("MODEL_NAME", config.get("LLM", "model")):
            wrapper._metadata["function"].pop("strict")
        return wrapper

    return decorator


def protect_from_override(*protected):
    """
    metaclass to prevent overriding of certain attributes in a class.

    Example usage:
        class MyBaseClass(metaclass=protect("important_method")):
            def important_method(self):
                print("This is an important method that shouldn't be overridden")
    """

    class Protect(type):
        has_base = False

        def __new__(meta, name, bases, attrs):
            if meta.has_base:
                for attribute in attrs:
                    if attribute in protected:
                        raise AttributeError(f'Overriding of attribute "{attribute}" not allowed.')
            meta.has_base = True
            klass = super().__new__(meta, name, bases, attrs)
            return klass

    return Protect


class AutoRegisterFunctionsMeta(type):
    def __new__(cls, name, bases, attrs):
        new_cls = super().__new__(cls, name, bases, attrs)

        if not hasattr(new_cls, 'class_function_mapping'):
            new_cls.class_function_mapping = {}

        new_cls.class_function_mapping[name] = {}

        for attr_name, attr_value in attrs.items():
            if callable(attr_value) and getattr(attr_value, '_is_registered', False):
                new_cls.class_function_mapping[name][attr_name] = attr_value._metadata

        return new_cls
