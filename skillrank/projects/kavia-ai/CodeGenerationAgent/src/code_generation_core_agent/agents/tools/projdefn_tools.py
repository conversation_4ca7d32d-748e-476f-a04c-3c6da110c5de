from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.framework.projdefn import ProjDefn

class ProjDefnTools(BaseToolInterface):
    def get_tool_name(self):
        return "ProjDefnTools"

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.output_callback = callback_functions.get("terminal_output_callback", None)
        self.executor = executor
        self.logger = logger

    def set_output_callback(self, callback):
        self.output_callback = callback

    @register_function(
        description= "Get the list of available document knowledge keys",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
            },
            "required": []
        }            
    )
    def get_keys(self):
        projdefn = ProjDefn.getInstance()
        result = {
                "status": "SUCCESS",
                "keys": f"{projdefn.getKeys()}"
        }
        return result

    @register_function(
        description= "Get the values associated with a specified knowledge keys",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "keys": {
                    "type": "array",
                    "items": {"type": "string"},
                }
            },
            "required": ["keys"]
        }
    )
    def get_key_values(self, keys):
        projdefn = ProjDefn.getInstance()
        status = "ERROR"
        value = projdefn.getKeyValues(keys)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description= "Find relevant knowledge keys.  Returns a list of keys that can be passed to get_key_values.  The results are sorted in order of descending relevance.",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "search_terms": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of search terms.",
                },
                "and_search": {
                    "type": "boolean",
                    "description": "True for AND search, False for OR search",
                }
            },
            "required": ["search_terms", "and_search"]
        }
    )
    def find_relevant_keys(self, search_terms, and_search):
        projdefn = ProjDefn.getInstance()
        status = "ERROR"
        value = projdefn.findRelevantKeysSemantic(search_terms, and_search)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description= "Find relevant knowledge document chunks.  Returns a list of chunk specs that can be passed to get_document_chunk to get the chunk data. The results are sorted in order of descending relevance.",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "search_terms": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of search terms.",
                },
                "and_search": {
                    "type": "boolean",
                    "description": "True for AND search, False for OR search",
                }
            },
            "required": ["search_terms", "and_search"]
        }
    )
    def find_relevant_document_chunks(self, search_terms, and_search):
        projdefn = ProjDefn.getInstance()
        status = "ERROR"
        value = projdefn.findRelevantDocumentChunksSemantic(search_terms, and_search)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description= "Find relevant knowledge document images.  Returns a list of image specs that can be passed to get_document_image to get the image data.",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "search_terms": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of search terms.",
                },
                "and_search": {
                    "type": "boolean",
                    "description": "True for AND search, False for OR search",
                }
            },
            "required": ["search_terms", "and_search"]
        }
    )
    def find_relevant_document_images(self, search_terms, and_search):
        projdefn = ProjDefn.getInstance()
        status = "ERROR"
        value = projdefn.findRelevantDocumentImages(search_terms, and_search)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description= "Get the text of a specified document chunk.  This is useful to access the text directly. Example: get_document_chunk('doc__SomeDoc__pages_20_30')",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "chunk_spec": {
                    "type": "string",
                    "description": "a chunk specifier returned from find_relevant_document_chunks",
                }
            },
            "required": ["chunk_spec"]
        }
    )
    def get_document_chunk(self, chunk_spec):
        projdefn = ProjDefn.getInstance()
        status = "ERROR"
        value = projdefn.getDocumentChunk(chunk_spec)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description= "Get a specified document image.  This is useful for when it is necessary to access detail beyond what is in the image description. Example: get_document_image('doc__SomeDoc__image_ImageName)",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "image_spec": {
                    "type": "string",
                    "description": "an image specifier returned from find_relevant_document_images",
                }
            },
            "required": ["image_spec"]
        }
    )
    def get_document_image(self, image_spec):
        projdefn = ProjDefn.getInstance()
        status = "ERROR"
        value = projdefn.getDocumentImage(image_spec)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result
