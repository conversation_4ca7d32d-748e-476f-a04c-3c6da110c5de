import os
import uuid
from typing import Optional
from git import Repo, GitCommandError
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from .schemas.decorators import register_function


class GitTools(BaseToolInterface):

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None, access_token=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.logger = logger
        self.repo_cache = {}  # Dictionary to store {repo_url: local_path}
        self.access_token = access_token

    def get_tool_name(self):
        return "GitTools"

    def ensure_repo_cloned(self, repo_url):
        if repo_url not in self.repo_cache:
            local_path = self._generate_local_path(repo_url)
            self.git_clone(repo_url, local_path)
            self.repo_cache[repo_url] = local_path
        return self.repo_cache[repo_url]

    def _generate_local_path(self, repo_url):
        repo_name = repo_url.split('/')[-1].replace('.git', '')
        return os.path.join(self.base_path, repo_name)

    @register_function(
        description="Clone a Git repository to a specified destination.",
        parameters={
            "type": "object",
            "properties": {
                "repository_url": {"type": "string", "description": "URL of the Git repository to clone"},
                "destination_path": {"type": "string", "description": "Local path where the repository will be cloned"}
            },
            "required": ["repository_url", "destination_path"]
        }
    )
    def git_clone(self, repository_url, destination_path):
        try:
            if self.access_token and 'oauth2:' not in repository_url and '@' not in repository_url:
                auth_url = repository_url.replace('https://', f'https://oauth2:{self.access_token}@')
            else:
                auth_url = repository_url

            if os.path.exists(destination_path):
                return self.git_pull(destination_path)

            Repo.clone_from(auth_url, destination_path)
            return f"Cloned repository to {destination_path}."
        except GitCommandError as e:
            return f"Error cloning repository: {str(e)}"

    @register_function(
        description="Pull the latest changes from the remote repository.",
        parameters={
            "type": "object",
            "properties": {
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": ["repository_path"]
        }
    )
    def git_pull(self, repository_path):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            repo.remotes.origin.pull()
            return "Git pull successful."
        except GitCommandError as e:
            return f"Error pulling repository: {str(e)}"

    @register_function(
        description="Checkout a branch or tag in the repository.",
        parameters={
            "type": "object",
            "properties": {
                "branch_or_tag": {"type": "string", "description": "Name of the branch or tag to checkout"},
                "repository_path": {"type": "string", "description": "Path to the local Git repository"},
                "new_branch": {"type": "boolean", "description": "Whether to create a new branch"}
            },
            "required": ["branch_or_tag", "repository_path"]
        }
    )
    def git_checkout(self, branch_or_tag, repository_path, new_branch=False):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            if new_branch:
                repo.git.checkout('-b', branch_or_tag)
                return f"Checked out new branch {branch_or_tag} in {repository_path}."
            else:
                repo.git.checkout(branch_or_tag)
                return f"Checked out {branch_or_tag} in {repository_path}."
        except GitCommandError as e:
            return f"Error checking out branch/tag: {str(e)}"

    @register_function(
        description="Get the status of the Git repository.",
        parameters={
            "type": "object",
            "properties": {
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": ["repository_path"]
        }
    )
    def git_status(self, repository_path):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            return repo.git.status()
        except GitCommandError as e:
            return f"Error getting Git status: {str(e)}"

    @register_function(
        description="Add all changes in the repository to the Git index.",
        parameters={
            "type": "object",
            "properties": {
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": ["repository_path"]
        }
    )
    def git_add_all(self, repository_path):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            repo.git.add(A=True)
            return "Added all files to Git index."
        except GitCommandError as e:
            return f"Error adding files to Git index: {str(e)}"

    @register_function(
        description="Add a specific file to the Git index.",
        parameters={
            "type": "object",
            "properties": {
                "file_path": {"type": "string", "description": "Path to the file to be added"},
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": ["file_path", "repository_path"]
        }
    )
    def git_add(self, file_path, repository_path):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            repo.git.add(file_path)
            return f"Added {file_path} to Git index."
        except GitCommandError as e:
            return f"Error adding file to Git index: {str(e)}"

    @register_function(
        description="Commit changes in the Git repository.",
        parameters={
            "type": "object",
            "properties": {
                "message": {"type": "string", "description": "Commit message"},
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": ["message", "repository_path"]
        }
    )
    def git_commit(self, message, repository_path):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            repo.git.commit('-m', message)
            return f"Committed changes with message: {message}"
        except GitCommandError as e:
            return f"Error committing changes: {str(e)}"

    @register_function(
        description="Push changes to the remote repository.",
        parameters={
            "type": "object",
            "properties": {
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": ["repository_path"]
        }
    )
    def git_push(self, repository_path):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            repo.remotes.origin.push()
            return "Pushed changes to remote."
        except GitCommandError as e:
            try:
                print("Push failed. Attempting to pull with rebase...")
                repo.git.pull(rebase=True)
                repo.remotes.origin.push()
                return "Pulled changes with rebase and successfully pushed to remote."
            except GitCommandError as rebase_error:
                try:
                    print("Conflicts detected. Creating a new branch...")
                    new_branch = f"conflict-resolution-{uuid.uuid4().hex[:8]}"
                    repo.git.checkout('-b', new_branch)
                    repo.git.push('-u', 'origin', new_branch)
                    return f"Created new branch '{new_branch}' and pushed to remote due to conflicts."
                except GitCommandError as branch_error:
                    return f"Error creating and pushing new branch: {str(branch_error)}"

    @register_function(
        description="Revert changes to a specific commit or the last commit.",
        parameters={
            "type": "object",
            "properties": {
                "commit_hash": {"type": "string", "description": "Hash of the commit to revert to (optional)"},
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": []
        }
    )
    def git_revert(self, commit_hash=None, repository_path="."):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            if commit_hash:
                repo.git.revert(commit_hash)
                message = f"Reverted to commit {commit_hash}"
            else:
                repo.git.revert('HEAD')
                message = "Reverted last commit"
            return message
        except GitCommandError as e:
            return f"Error reverting changes: {str(e)}"

    @register_function(
        description="List branches in the repository.",
        parameters={
            "type": "object",
            "properties": {
                "repository_path": {"type": "string", "description": "Path to the local Git repository"},
                "all_branches": {"type": "boolean", "description": "Whether to list all branches (including remote)"}
            },
            "required": []
        }
    )
    def git_branch(self, repository_path=".", all_branches=False):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            if all_branches:
                branches = repo.git.branch('-a').split('\n')
            else:
                branches = repo.git.branch().split('\n')

            branches = [branch.strip() for branch in branches if branch.strip()]

            return "\n".join(branches)
        except GitCommandError as e:
            return f"Error listing branches: {str(e)}"

    @register_function(
        description="Merge a branch into the current branch using squash.",
        parameters={
            "type": "object",
            "properties": {
                "branch_name": {"type": "string", "description": "Name of the branch to merge"},
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": ["branch_name"]
        }
    )
    def git_merge_squash(self, branch_name, repository_path="."):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            repo.git.merge('--squash', branch_name)
            return f"Squash merged branch {branch_name}."
        except GitCommandError as e:
            return f"Error squash merging: {str(e)}"

    @register_function(
        description="Cherry-pick a specific commit into the current branch.",
        parameters={
            "type": "object",
            "properties": {
                "commit_hash": {"type": "string", "description": "Hash of the commit to cherry-pick"},
                "repository_path": {"type": "string", "description": "Path to the local Git repository"}
            },
            "required": ["commit_hash"]
        }
    )
    def git_cherry_pick(self, commit_hash, repository_path="."):
        try:
            repo = Repo(os.path.join(self.base_path, repository_path))
            repo.git.cherry_pick(commit_hash)
            return f"Cherry-picked commit {commit_hash}."
        except GitCommandError as e:
            return f"Error cherry-picking commit: {str(e)}"

    @register_function(
        description="Reads the contents of a file from a GitHub repository, optionally specifying a range of lines.",
        parameters={
            "type": "object",
            "properties": {
                "repo_url": {"type": "string", "description": "URL of the GitHub repository"},
                "file_path": {"type": "string", "description": "The path to the file within the repository."},
                "start": {"type": "integer", "description": "The line number to start reading from (1-indexed).",
                          "optional": True},
                "end": {"type": "integer", "description": "The line number to end reading at (inclusive).",
                        "optional": True}
            },
            "required": ["repo_url", "file_path"]
        }
    )
    def read_file_from_repo(self, repo_url: str, file_path: str, start: Optional[int] = None,
                            end: Optional[int] = None):
        try:
            local_repo_path = self.ensure_repo_cloned(repo_url)
            repo = Repo(local_repo_path)
            full_path = os.path.join(local_repo_path, file_path)

            with open(full_path, 'r') as file:
                lines = file.readlines()

            if start is None:
                start = 1
            if end is None or end > start + 999:
                end = start + 999

            content = ''.join(lines[start - 1:end])
            size = len(content)
            line_count = len(lines[start - 1:end])

            self.logger.info(f"Read {size} bytes from file {file_path} in repository {repo_url}")

            result = {
                "status": "SUCCESS",
                "repo_url": repo_url,
                "file_path": file_path,
                "content": content,
                "lines_read": line_count,
            }

            if line_count == 1000:
                result["message"] = "Only the first 1000 lines are returned due to length limit."

            return result

        except FileNotFoundError:
            return {"status": "ERROR", "message": f"File not found: {file_path} in repository {repo_url}"}
        except IsADirectoryError:
            return {"status": "ERROR", "message": f"Path is a directory: {file_path}. You can only read files."}
        except Exception as e:
            self.logger.error(f"Unexpected error reading file {file_path} from repository {repo_url}: {str(e)}")
            return {"status": "ERROR", "message": f"Unexpected error: {str(e)}"}
