from typing import Dict
import os
import yaml
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function

class DependencyTools(BaseToolInterface):
    def get_tool_name(self):
        return "DependencyTools"

    def __init__(self, callbacks, base_path, logger, llm=None, executor=None):
        super().__init__(callbacks, base_path, logger, llm, executor)
        self.base_path = base_path
        self.logger = logger
        self.executor = executor
        self.config_file = "supported_versions.yaml"

    def _read_yaml_file(self, file_path: str) -> dict:
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)
    
    def _read_dependency_config(self):
        try:
            script_path = os.path.dirname(os.path.abspath(__file__))
            config_file_name = self.config_file
            dependencies_path = os.path.join(script_path, config_file_name)
            return self._read_yaml_file(dependencies_path)
        except Exception as e:
            self.logger.error(f"Error reading dependency config: {str(e)}")
            return None

    @register_function(
        description="""Retrieves the required dependency versions for a specific programming language from the central configuration.
        Use it to get the required versions for specific dependencies for a given language.
        Usage example: get_dependency_versions(language='Python')""",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "language": {
                    "type": "string",
                    "enum": ["Python", "JavaScript/Node.js"],
                    "description": "Programming language of the project to get dependencies for (e.g., Python, JavaScript/Node.js)"
                }
            },
            "required": ["language"]
        }
    )
    def get_dependency_versions(self, language: str) -> Dict:
        try:
            config = self._read_dependency_config()
            
            if config is None:
                return {
                    "status": "SUCCESS",
                    "output": {},
                    "message": "No required dependency configuration found",
                    "returncode": 0
                }

            if language not in config:
                return {
                    "status": "SUCCESS",
                    "output": {},
                    "message": f"No required dependencies defined for {language}",
                    "returncode": 0
                }

            return {
                "status": "SUCCESS",
                "output": config[language],
                "returncode": 0
            }

        except Exception as e:
            self.logger.error(f"Error getting dependencies for {language}: {str(e)}")
            return {
                "status": "ERROR",
                "message": f"Failed to get dependencies: {str(e)}",
                "returncode": -1
            }
