import concurrent.futures
import json
import os

from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function

SHELL_CMD_DEFAULT_TIMEOUT = 60

MAX_DATA_SIZE = 10000  # Maximum size of data to return in a single response


class EnvironmentSummaryToolsError(Exception):
    """Custom exception for EnvironmentSummary tools errors."""
    pass


class EnvironmentSummary(BaseToolInterface):
    def get_tool_name(self):
        return 'EnvironmentSummary'

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.executor = executor
        self.logger = logger

    def _process_directory_path(self, directory):
        """
        Process the directory path and return the absolute path to the directory.

        :param directory: directory path
        :return: (result, directory)
            result: None if the directory is valid, otherwise a dictionary with error message
            directory: absolute path to the directory
        """
        result = None

        if directory in (".", "./", os.curdir, os.path.curdir):
            directory = self.base_path
            return result, directory

        # If directory doesn't start with '/', it is a relative path and should be joined with base_path
        if not directory.startswith('/'):
            directory = os.path.join(self.base_path, directory)

        if not os.path.abspath(directory).startswith(os.path.abspath(self.base_path)):
            result = {
                "status": "ERROR",
                "message": f"Directory {directory} is not a subdirectory of {self.base_path}"
            }


        if not os.path.exists(directory):
            result = {
                "status": "ERROR",
                "message": f"Directory {directory} does not exist"
            }

        return result, directory

    def _run_oneshot_command_in_executor(self, cmd, timeout) -> (bool, str):
        from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutionException
        """
        Run one shot command in executor and return the return code and output.
        If this command fails, the returned output is a formated dictionary with error message.

        :param cmd: command to run
        :param timeout: timeout for the command
        :return: (bool, str)
            False, error message if command execution failed

        """

        try:
            output, status = self.executor.run_one_shot_command(cmd, timeout=timeout)
        except DockerExecutionException as e:
            return False, f"ERROR executing command. Command execution failed with exception {e}"

        if status != 0:
            return False, f"ERROR executing command. Command returned non-zero status: {status}. Output: {output}"

        return True, output

    @register_function(
        description="Generates a summary of the file tree structure in the development environment.",
        parameters={
            "type": "object",
            "properties": {
                "directory": {
                    "type": "string",
                    "description": "directory to summarize"
                }
            },
            "required": ["directory"]
        }
    )
    def get_file_tree_summary(self, directory):
        """
        Generate a summary of the file tree structure in the development environment.
        If the file tree is too large, the summary is truncated.
        """

        max_data_size = MAX_DATA_SIZE
        dir_prefix = "File tree summary for"
        result, directory = self._process_directory_path(directory)
        if result:
            return result

        def get_max_depth(dir_path):
            max_d = 0
            for root, dirs, _ in os.walk(dir_path):
                if not any(d.startswith('.') for d in root.split(os.sep)):
                    depth_val = root[len(dir_path):].count(os.sep)
                    max_d = max(max_d, depth_val)
            return max_d

        def generate_tree(dir_path, prefix="", current_depth=0, max_depth=float('inf')):
            if not os.path.isdir(dir_path):
                yield prefix + "`- " + os.path.basename(dir_path)
                return

            contents = sorted(os.scandir(dir_path), key=lambda e: e.name)
            for i, path in enumerate(contents):
                is_last = i == len(contents) - 1
                pointer = "`- " if is_last else "|- "
                if path.is_dir() and path.name.startswith('.'):
                    yield prefix + pointer + path.name + "/ # dot directories are not listed in search"
                else:
                    yield prefix + pointer + path.name + ("/" if path.is_dir() else "")
                    if path.is_dir() and not path.name.startswith('.') and current_depth < max_depth:
                        extension = "   " if is_last else "|  "
                        yield from generate_tree(path.path, prefix + extension, current_depth + 1, max_depth)

        def get_tree_with_limit(max_depth):
            tree_generator = generate_tree(directory, max_depth=max_depth)
            tree_lines = []
            current_size = 0
            for line in tree_generator:
                new_size = current_size + len(line) + 1  # +1 for newline
                if new_size > max_data_size:
                    tree_lines.append("... (truncated due to size limit)")
                    break
                tree_lines.append(line)
                current_size = new_size
            return "\n".join(tree_lines)

        # Start with listing only top-level directories
        max_tree_depth = get_max_depth(directory)
        summary = ""
        for depth in range(max_tree_depth + 1):
            file_tree = get_tree_with_limit(depth)
            summary = f"{dir_prefix} {directory}:\n{file_tree}"
            if len(summary) > max_data_size:
                if depth == 0:
                    top_dirs = [d.name for d in os.scandir(directory) if d.is_dir() and not d.name.startswith('.')]
                    truncated_dirs = top_dirs[:10] + (["..."] if len(top_dirs) > 10 else [])
                    file_tree = "\n".join("|-- " + d for d in truncated_dirs)
                    summary = f"{dir_prefix} {directory} (truncated):\n{file_tree}"
                else:
                    file_tree = get_tree_with_limit(depth - 1)
                    summary = f"{dir_prefix} {directory}:\n{file_tree}"
                break

        if not summary:
            return {
                "status": "ERROR",
                "message": f"Failed to generate file tree summary for {directory}"
            }

        return {
            "status": "SUCCESS",
            "message": summary
        }

    @register_function(
        description="Analyzes and summarizes the code base in the development environment.",
        parameters={
            "type": "object",
            "properties": {
                "directory": {
                    "type": "string",
                    "directory": "Directory to analyze."
                }
            },
            "required": ["directory"]
        }
    )
    def get_code_summary(self, directory):

        result, directory = self._process_directory_path(directory)
        if result:
            return result

        # Generate a list of directories to exclude (hidden directories)
        exclude_dir_names = {'.git', 'node_modules'}

        exclude_dirs = []
        for root, dirs, files in os.walk(directory):
            dirs[:] = [d for d in dirs if d not in exclude_dir_names]  # Modify dirs in-place
            if (len(files) + len(dirs) > 20 or
                    os.path.basename(root).startswith('.') or
                    any(excluded in root.split(os.path.sep) for excluded in exclude_dir_names)):
                if root not in exclude_dirs:
                    included = False
                    for e in exclude_dirs:
                        if root.startswith(e):
                            included = True
                            break
                    if not included:
                        exclude_dirs.append(root)

        exclude_option = f'--exclude="**/node_modules/**" ' + ' '.join([f'--exclude="{d}"' for d in exclude_dirs])

        cmd_ctags = f'ctags -R --fields=+l -f- {exclude_option} {directory}'

        result, output = self._run_oneshot_command_in_executor(cmd_ctags, timeout=SHELL_CMD_DEFAULT_TIMEOUT)

        if not result:
            return {"status": "ERROR", "message": output}

        ctags_output = output
        tags = {}
        for line in ctags_output.split('\n'):
            if line and not line.startswith('!'):
                parts = line.split('\t')
                if len(parts) >= 4:
                    tag, file_path, pattern = parts[:3]
                    if file_path not in tags:
                        tags[file_path] = []
                    tags[file_path].append(f"{tag}: {pattern}")

        summary = "Code summary:\n"
        for file_path, file_tags in tags.items():
            summary += f"\n{file_path}:\n"
            for tag in file_tags:
                summary += f"  {tag}\n"

        return {"status": "SUCCESS", "message": summary}

    @register_function(
        description="Retrieves a summary of currently running network services in the development environment.",
        parameters={
            "type": "object",
            "properties": {}
        }
    )
    def get_running_network_services_summary(self):
        cmd = "netstat -tuln"
        result, output = self._run_oneshot_command_in_executor(cmd, timeout=SHELL_CMD_DEFAULT_TIMEOUT)

        if not result:
            return output

        network_services = output

        return {"status": "SUCCESS", "message": network_services}

    @register_function(
        description="Provides a comprehensive summary of the development environment,"
                    "including system information, installed tools, and configurations.",
        parameters={
            "type": "object",
            "properties": {
                "environment": {
                    "type": "string",
                    "description": "A string representation of the environment details."
                }
            },
            "required": ["environment"]
        }
    )
    def get_environment_summary(self, environment):

        cmd_env = "env"

        result, output = self._run_oneshot_command_in_executor(cmd_env, timeout=SHELL_CMD_DEFAULT_TIMEOUT)

        if not result:
            return output

        env_vars = output

        summary = f"""
        Environment Variables:
        {env_vars}
        """

        return {"status": "SUCCESS", "message": summary}
