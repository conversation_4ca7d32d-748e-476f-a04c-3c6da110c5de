import traceback

from code_generation_core_agent.agents.tools.base_tool import <PERSON><PERSON>ool
import copy
import json
from collections import OrderedDict
from code_generation_core_agent.agents.tools.schemas.decorators import protect_from_override, AutoRegisterFunctionsMeta


class CombinedMeta(AutoRegisterFunctionsMeta, protect_from_override('add_function_schemas')):
    pass


class AgentTool(BaseTool, metaclass=CombinedMeta):
    def __init__(self, base_path, logger, llm=None, executor=None):
        super().__init__(base_path, logger, llm, executor)

    def add_function_schemas(self, schemas, tool_name):
        modified_schemas = self._modify_function_schemas(schemas)
        super().add_function_schemas(modified_schemas, tool_name)

    @staticmethod
    def print_stack_trace():
        stack = traceback.extract_stack()
        print("Stack trace:")
        for filename, line, func, text in stack[:-1]:  # [:-1] to exclude the call to this function
            print(f"  File: {filename}, line {line}, in {func}")
            if text:
                print(f"    {text.strip()}")
        print()

    @staticmethod
    def _modify_function_schemas(schemas):
        modified_schemas = []
        for schema in schemas:
            new_schema = copy.deepcopy(schema)
            new_params = {
                "reason": {
                    "type": "string",
                    "description": "Explain the reason for this function call"
                },
                "observations": {
                    "type": "string",
                    "description": "Observations on the current state, especially if there were previous failures, your observations should be specific to the failure"
                },
                "root_cause_analysis": {
                    "type": "string",
                    "description": "Root cause analysis for previous failures and paths to pursue to address those issues. Return empty string if not applicable",
                }
            }

            # Create a new OrderedDict with new params first
            function_params = OrderedDict()
            function_params.update(new_params)
            function_params.update(new_schema['function']['parameters']['properties'])

            # Replace the original properties with the new OrderedDict
            new_schema['function']['parameters']['properties'] = function_params

            # Update required fields
            required = new_schema['function']['parameters'].get('required', [])
            new_required = ['reason', 'observations', 'root_cause_analysis'] + required
            new_schema['function']['parameters']['required'] = new_required

            modified_schemas.append(new_schema)

        return modified_schemas

    def function_executor(self, function_name, function_args):
        # Extract the new parameters
        reason = function_args.pop('reason', None)
        observations = function_args.pop('observations', None)
        root_cause_analysis = function_args.pop('root_cause_analysis', None)

        # Log the extracted information
        self.logger.info(f"Function: {function_name}")
        self.logger.info(f"Reason: {reason}")
        self.logger.info(f"Observations: {observations}")
        if root_cause_analysis:
            self.logger.info(f"Root Cause Analysis: {root_cause_analysis}")

        # Call the parent class's function_executor with the modified arguments
        result = super().function_executor(function_name, function_args)

        # You might want to log the result as well
        self.logger.info(f"Result: {json.dumps(result, indent=2)}")

        return result
