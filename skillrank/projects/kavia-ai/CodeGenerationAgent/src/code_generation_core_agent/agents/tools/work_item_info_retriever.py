import json

from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function


class WorkItemInfoRetriever(BaseToolInterface):
    def get_tool_name(self):
        return "WorkItemInfoRetriever"

    def __init__(self,  callbacks, base_path, logger, llm=None, executor=None):
        super().__init__(callbacks, base_path, logger, llm, executor)
        self.callbacks = callbacks

    def work_item_get_summary(self):
        summary = {
            "component_name": self.full_work_item.get("component_name", ""),
            "description": self.full_work_item.get("description", ""),
            "algorithms": self.work_item_get_all_algorithm_titles(),
            "state_logic": self.work_item_get_all_state_logic_titles(),
            "sequence_diagrams": self.work_item_get_all_sequence_diagram_titles(),
            "state_diagrams": self.work_item_get_all_state_diagram_titles(),
            "unit_tests": self.work_item_get_all_unit_test_titles(),
            "performance_tests": self.work_item_get_all_performance_test_titles(),
            "interfaces": [interface.get("interface_node")["properties"]["Title"] for interface in
                           self.work_item_get_interfaces()],
            "available_assets" : self.full_work_item.get("available_assets", [])
        }
        return summary

    def set_work_item(self, work_item):
        self.full_work_item = work_item

    def work_item_get_component_name(self):
        return self.full_work_item.get("component_name", "")

    def work_item_get_description(self):
        return self.full_work_item.get("description", "")

    def work_item_get_interfaces(self):
        interfaces = self.full_work_item.get("interfaces", [])
        return interfaces

    def work_item_get_design(self):
        return self.full_work_item.get("design", {})

    @staticmethod
    def _get_item_by_title(item_list, title):
        for item in item_list:
            item_data = json.loads(item)
            if item_data.get("Title") == title:
                return item_data
        return None

    @register_function(
        description="Get a specific algorithm by its title",
        parameters={
            "type": "object",
            "properties": {
                "title": {"type": "string", "description": "Title of the algorithm"}
            },
            "required": ["title"]
        }
    )
    def get_algorithm_by_title(self, title):
        return {
            "status": "SUCCESS",
            "Algorithm": self._get_item_by_title(self.full_work_item.get("Algorithm", []), title)
        }

    @register_function(
        description="Get specific state logic by its title",
        parameters={
            "type": "object",
            "properties": {
                "title": {"type": "string", "description": "Title of the state logic"}
            },
            "required": ["title"]
        }
    )
    def get_state_logic_by_title(self, title):
        return {
            "status": "SUCCESS",
            "StateLogic": self._get_item_by_title(self.full_work_item.get("StateLogic", []), title)
        }

    @register_function(
        description="Get a specific sequence diagram by its title",
        parameters={
            "type": "object",
            "properties": {
                "title": {"type": "string", "description": "Title of the sequence diagram"}
            },
            "required": ["title"]
        }
    )
    def get_sequence_diagram_by_title(self, title):
        return {
            "status": "SUCCESS",
            "Sequence": self._get_item_by_title(self.full_work_item.get("Sequence", []), title)
        }

    @register_function(
        description="Get a specific state diagram by its title",
        parameters={
            "type": "object",
            "properties": {
                "title": {"type": "string", "description": "Title of the state diagram"}
            },
            "required": ["title"]
        }
    )
    def get_state_diagram_by_title(self, title):
        return {
            "status": "SUCCESS",
            "StateDiagram": self._get_item_by_title(self.full_work_item.get("StateDiagram", []), title)
        }

    @register_function(
        description="Get a specific unit test by its title",
        parameters={
            "type": "object",
            "properties": {
                "title": {"type": "string", "description": "Title of the unit test"}
            },
            "required": ["title"]
        }
    )
    def get_unit_test_by_title(self, title):
        return {
            "status": "SUCCESS",
            "UnitTest": self._get_item_by_title(self.full_work_item.get("UnitTest", []), title)
        }

    @register_function(
        description="Get a specific performance test by its title",
        parameters={
            "type": "object",
            "properties": {
                "title": {"type": "string", "description": "Title of the performance test"}
            },
            "required": ["title"]
        }
    )
    def get_performance_test_by_title(self, title):
        return {
            "status": "SUCCESS",
            "PerformanceTest": self._get_item_by_title(self.full_work_item.get("PerformanceTest", []), title)
        }

    @staticmethod
    def _get_all_titles(item_list):
        return [json.loads(item)["Title"] for item in item_list]

    def work_item_get_all_algorithm_titles(self):
        return self._get_all_titles(self.full_work_item.get("Algorithm", []))

    def work_item_get_all_state_logic_titles(self):
        return self._get_all_titles(self.full_work_item.get("StateLogic", []))

    def work_item_get_all_sequence_diagram_titles(self):
        return self._get_all_titles(self.full_work_item.get("Sequence", []))

    def work_item_get_all_state_diagram_titles(self):
        return self._get_all_titles(self.full_work_item.get("StateDiagram", []))

    def work_item_get_all_unit_test_titles(self):
        return self._get_all_titles(self.full_work_item.get("UnitTest", []))

    def work_item_get_all_performance_test_titles(self):
        return self._get_all_titles(self.full_work_item.get("PerformanceTest", []))

    @register_function(
        description="Get a specific interface by its title",
        parameters={
            "type": "object",
            "properties": {
                "title": {"type": "string", "description": "Title of the interface"}
            },
            "required": ["title"]
        }
    )
    def get_interface_by_title(self, title):
        interfaces = self.full_work_item.get("interfaces", [])
        for interface in interfaces:
            if interface.get("interface_node", {}).get("properties", {}).get("Title") == title:
                return {
                    "status": "SUCCESS",
                    "Interface": interface
                }
        return {
            "status": "ERROR",
            "message": f"Interface with title '{title}' not found"
        }
