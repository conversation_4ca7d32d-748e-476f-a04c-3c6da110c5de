from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.framework.knowledge import Knowledge

class KnowledgeTools(BaseToolInterface):
    def get_tool_name(self):
        return "KnowledgeTools"

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.output_callback = callback_functions.get("terminal_output_callback", None)
        self.executor = executor
        self.logger = logger

    def set_output_callback(self, callback):
        self.output_callback = callback

    @register_function(
        description="Get the list of available knowledge keys. This function is useful for understanding the available knowledge keys that the knowledge base has."
                    "Example: KnowledgeTools_get_keys() ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
            },
            "required": []
        }
    )
    def get_keys(self):
        knowledge = Knowledge.getKnowledge()
        result = {
                "status": "SUCCESS",
                "keys": f"{knowledge.getKeys()}"
        }
        return result

    @register_function(
        description="Get the value associated with a specified knowledge key."
                    "Example: KnowledgeTools_get_key_value('search-terms') ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "key": {"type": "string", "description": "The knowledge key to being queried."},
            },
            "required": ["key"]
        }
    )
    def get_key_value(self, key):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.getKeyValue(key)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Get the list of available knowledge keys for the current task. This is relevent if there is workitem which specifies the task."
                    "Example: KnowledgeTools_get_task_keys() ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
            },
            "required": []
        }
    )
    def get_task_keys(self):
        knowledge = Knowledge.getKnowledge()
        result = {
                "status": "SUCCESS",
                "keys": f"{knowledge.getTaskKeys()}"
        }
        return result

    @register_function(
        description="Get the value associated with specified knowledge keys for the current task."
                    "Example: KnowledgeTools_get_key_values(['design--requirements','design--description') ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "keys": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of task knowledge keys to being queried."},
            },
            "required": ["keys"]
        }
    )
    def get_task_key_values(self, keys):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.getTaskKeyValues(keys)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Find relevant task keys"
                    "Example: KnowledgeTools_find_relevant_task_keys([<list of search terms])",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "search_terms": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of search terms.",
                },
                "and_search": {
                    "type": "boolean",
                    "description": "True for AND search (all terms must match),"
                                   "False for OR search (any term can match). Use False for broader results."
                }
            },
            "required": ["search_terms", "and_search"]
        }
    )
    def find_relevant_task_keys(self, search_terms, and_search):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.findRelevantTaskKeys(search_terms, and_search)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Find relevant files pertaining to the specified search terms. Before writing or editing any file in the codebase, it is recommended to use this function to find the most relevant files to edit."
                    "Example: KnowledgeTools_find_relevant_files([<list of search terms])",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "search_terms": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of search terms.",
                },
                "and_search": {
                    "type": "boolean",
                    "description": "True for AND search (all terms must match),"
                                   "False for OR search (any term can match). Use False for broader results."
                },
                "code_base": {
                    "type": "string",
                    "description": "Search only within this named codebase"
                }
            },
            "required": ["search_terms", "and_search"]
        }
    )
    def find_relevant_files(self, search_terms, and_search, code_base = None):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.findRelevantFiles(search_terms, and_search, code_base)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Find methods defined in the codebase(s) whose names match or contain the specified string. Use this only when you know the method name. Dont use this as a general search tool."
                    "Example: KnowledgeTools_find_methods('send_message') which might return [{'filename': '/tmp/kavia/codegen-1102/graphnode-backend-api/app/core/git_controller.py', "
                    "'code-base-name': 'graphnode-backend-api', 'method': {'name': '_send_message(self, content: str)', "
                    "'description': 'Sends a message through the web socket and updates the database with the git command output.', 'scope': 'GitController', 'scopeKind': 'class'}}]",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "method_name": {"type": "string", "description": "The method name or name fragment to search for."},
            },
            "required": ["method_name"]
        }
    )
    def find_methods(self, method_name):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.findMethods(method_name)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Find fields defined in the codebase(s) whose names match or contain the specified string."
                    "Example: KnowledgeTools_find_fields('sessionServerAppManager') which might return [{'filename': '/tmp/kavia/codegen-rialto/rialto/serverManager/service/include/ServiceContext.h', "
                    "'code-base-name': 'rialto', 'field': {'name': 'std::unique_ptr<common::ISessionServerAppManager> m_sessionServerAppManager', "
                    "'scope': 'rialto::servermanager::service::ServiceContext', 'scopeKind': 'class', 'description': 'unavailable'}}] ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "field_name": {"type": "string", "description": "The field name or name fragment to search for."},
            },
            "required": ["field_name"]
        }
    )
    def find_fields(self, field_name):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.findFields(field_name)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Get source file descriptions for multiple files. This function is useful for understanding the purpose of source files."
                    "Example: KnowledgeTools_get_source_file_descriptions(['<fully_qualified_source_file_name1>', '<fully_qualified_source_file_name2>']) ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_paths": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "A list of fully qualified paths to the source files."
                },
            },
            "required": ["file_paths"]
        }
    )
    def get_source_file_descriptions(self, file_paths):
        knowledge = Knowledge.getKnowledge()
        results = []

        for file_path in file_paths:
            status = "ERROR"
            value = knowledge.getSourceFileDescription(file_path)
            if value:
                status = "SUCCESS"
            
            results.append({
                "file_path": file_path,
                "status": status,
                "value": value
            })

        # Return a structured JSON object instead of raw list
        return {
            "status": "SUCCESS",
            "response_type": "source_file_descriptions",
            "results": results
        }

    @register_function(
        description="Get knowledge about multiple source files. This function retrieves knowledge for a list of source files."
                    " Example: KnowledgeTools_get_source_files_knowledge(['<fully_qualified_source_file_name1>', '<fully_qualified_source_file_name2>']) "
                    " This function will return the following information for each file: \n"
                    " {{ "
                    "   'is_source_file': <true or false>,\n"
                    "   'format': '<the format or language of the file>', \n"
                    "   'description': '<description of file contents>', \n"
                    "   'external_files': [<list of external files referenced>], \n"
                    "   'external_methods': [<list of external methods referenced>], \n"
                    "   'published': [<list of symbols defined in this file that are visible to other files>], \n"
                    "   'classes': [ #list of classes defined in file, if applicable\n"
                    "       {{ \n"
                    "         'name': '<class name>',\n"
                    "         'description': '<description of class>', \n"
                    "          ...\n"
                    "       }}, \n"
                    "     ], " 
                    "   'methods': [ #list of methods defined in file with full API signature, if applicable\n"
                    "       {{ \n"
                    "         'name': '<method name>', \n"
                    "         'description': '<description of method>', \n"
                    "          ...\n"
                    "       }}, \n"
                    "     ], " 
                    "   'calls': [<list of methods called in file>'], \n"
                    " }}\n",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_paths": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "A list of fully qualified paths to the source files."
                }
            },
            "required": ["file_paths"]
        }
    )
    def get_source_files_knowledge(self, file_paths):
        knowledge = Knowledge.getKnowledge()
        results = {}
        
        for file_path in file_paths:
            status = "ERROR"
            value = knowledge.getSourceFileKnowledge(file_path)
            if value:
                status = "SUCCESS"
            results[file_path] = {
                "status": status,
                "value": value
            }
        
        return results
