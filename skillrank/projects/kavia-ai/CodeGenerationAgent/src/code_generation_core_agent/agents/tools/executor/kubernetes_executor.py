import asyncio
import logging
import os
import random
import re
import string
import threading
import time
import traceback
from kubernetes import client, stream, config as k8s_config
from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
from code_generation_core_agent.agents.utilities import ApplicationType, PrintLogger
from code_generation_core_agent.config import config as system_config
from typing import Optional
from kubernetes.stream.ws_client import WSClient

DEBUG_KUBERNETES_EXECUTOR = system_config.getboolean("SYSTEM", "debug_kubernetes_executor", fallback=False)
POD_INSTANCES = {}

class KubernetesExecutor(ExecutorBase):
    def __init__(
        self,
        pod_name: str = "kavia_default_pod",
        session_dir=os.path.join(system_config.get("SYSTEM", "workspace_dir"), "kavia"),
        logger=PrintLogger(),
        platform: str = ApplicationType.COMMON.value,
    ):
        self.logger = setup_logger("KubernetesExecutor", session_dir, logging.DEBUG) if DEBUG_KUBERNETES_EXECUTOR else logger
        self.platform = platform
        self.session_dir = session_dir
        self.working_dir = system_config.get("SYSTEM", "workspace_dir")
        self.image_name = f"{pod_name}_image" if pod_name else system_config.get("SYSTEM", "default_docker_image")
        self.pod_name = pod_name
        self.pod = None
        self.client = None
        self.running = False
        self.init_kubernetes_client()
    
    def init_kubernetes_client(self):
        """Initialize the Kubernetes client for local development"""
        try:
            # Load local kubeconfig
            k8s_config.load_kube_config()
            self.client = client.CoreV1Api()
        except k8s_config.ConfigException as e:
            raise RuntimeError(f"Failed to initialize Kubernetes client: {e}")
    
    async def ensure_pod(self):
        """Ensure Kubernetes pod exists and is running"""
        if self.pod_name not in POD_INSTANCES:
            try:
                pod = await self._create_pod()
                POD_INSTANCES[self.pod_name] = pod
                self.running = True
            except client.rest.ApiException as e:
                if e.status == 409:
                    # Pod already exists, remove it
                    await self._delete_pod()
                    pod = await self._create_pod()
                    POD_INSTANCES[self.pod_name] = pod
                else:
                    raise RuntimeError(f"Failed to create pod: {e}")
    
    async def create_subprocess_shell(self, cmd, callback=None, **kwargs):
        """Create a subprocess shell in the Kubernetes pod"""
        await self.ensure_pod()
        exec_command = [
            '/bin/bash',
            '-c',
            cmd
        ]
        resp = stream.stream(
            self.client.connect_get_namespaced_pod_exec,
            self.pod_name,
            'default',
            command=exec_command,
            stderr=True,
            stdin=True,
            stdout=True,
            tty=True,
            _preload_content=False
        )
        return KubernetesSubprocess(resp, self.client, callback, self.logger)

class KubernetesSubprocess:
    """Class to represent a subprocess running inside a Kubernetes pod"""
    def __init__(self, stream, client, callback=None, logger=None):
        self.stream = stream
        self.client = client
        self._stdout = KubernetesStreamReader(stream, callback, logger)
        self._stdin = KubernetesStdinWriter(stream, logger)
        self.returncode = None
    @property
    def stdout(self):
        return self._stdout
    @property
    def stdin(self):
        return self._stdin
    async def terminate(self):
        """Terminate the subprocess in the Kubernetes pod"""
        try:
            self._stdin.write(b'\x03')  # Send CTRL-C
            self._stdin.write(b"exit\n")
            await asyncio.sleep(0.5)
            self.stream.close()
            self.returncode = -1
        except Exception as e:
            self.logger.debug(f"Error during subprocess termination: {e}")
            self.returncode = -2


class KubernetesStreamReader:
    """
    Class to read from the stdout of a Kubernetes pod subprocess.
    This class handles the reading of output from a Kubernetes exec session,
    including handling of timeouts and partial reads.
    """
    def __init__(self,
                 stream: WSClient,
                 callback: Optional[callable] = None,
                 logger: Optional[object] = None):
        """
        Initialize the Kubernetes stream reader.
        Args:
            stream: The Kubernetes websocket stream
            callback: Optional callback for handling output
            logger: Optional logger instance
        """
        self.stream = stream
        self.buffer = b''
        self._loop = asyncio.get_event_loop()
        self.callback = callback
        self.terminated = False
        self.exit_code = None
        self._buffer_lines = []
        self.logger = logger
        # Start background reading thread if callback is provided
        if self.callback:
            self.read_thread = threading.Thread(target=self._read_forever)
            self.read_thread.daemon = True  # Ensure thread terminates with main program
            self.read_thread.start()

    def _read_forever(self):
        """
        Continuously read from the stream and invoke callback with output.
        This method runs in a separate thread when a callback is provided.
        """
        while not self.terminated:
            try:
                result = self._read_without_check(timeout=1)
                if result:
                    processed_output = self._process_output(result)
                    if processed_output:
                        self.callback.on_output(processed_output)
                # Check if the stream is still alive
                if not self.stream.is_open():
                    self.terminated = True
                    self.exit_code = 0  # Default exit code when stream closes normally
                    self.callback.on_exit(self.exit_code)
                    break
            except Exception as e:
                self.logger.error(f"Error in read forever loop: {str(e)}")
                self.terminated = True
                self.exit_code = 1
                self.callback.on_exit(self.exit_code)
                break

    def _read_without_check(self, timeout=0.5):
        """
        Read data from the Kubernetes stream without sending a shell check command.
        Args:
            timeout: Time to wait for data in seconds
        Returns:
            bytes: The data read from the stream
        """
        start_time = time.time()
        result = b''
        try:
            while time.time() - start_time < timeout:
                if self.stream.is_open():
                    # Read from the stdout channel of the stream
                    if self.stream.peek_stdout():
                        chunk = self.stream.read_stdout()
                        if chunk:
                            result += chunk.encode('utf-8') if isinstance(chunk, str) else chunk
                    # Also check stderr for any error messages
                    if self.stream.peek_stderr():
                        chunk = self.stream.read_stderr()
                        if chunk:
                            result += chunk.encode('utf-8') if isinstance(chunk, str) else chunk
                    if result:
                        return result
                    time.sleep(0.1)  # Small sleep to prevent CPU spinning
                else:
                    break
            return result
        except Exception as e:
            self.logger.error(f"Error reading from stream: {str(e)}")
            return b''
    
    @staticmethod
    def _process_output(output: bytes) -> bytes:
        """
        Process the output to remove control sequences and unwanted content.
        Args:
            output: Raw output from the stream
        Returns:
            bytes: Processed output
        """
        # Remove ANSI escape sequences
        ansi_escape = re.compile(rb'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        output = ansi_escape.sub(b'', output)
        # Split into lines and filter
        lines = re.split(rb'[\r\n]', output)
        filtered_lines = []
        for line in lines:
            line = line.strip()
            if not line:
                continue
            # Filter out shell prompt lines and other unwanted content
            if b'export KUBE_CMD_TOKEN' in line:
                continue
            filtered_lines.append(line)
        return b'\n'.join(filtered_lines)
    
    def read_with_partial(self, n=-1, timeout=10):
        """
        Read from the stream with support for partial reads and timeouts.
        Args:
            n: Number of bytes to read (-1 for unlimited)
            timeout: Read timeout in seconds
        Returns:
            tuple: (processed_output, is_partial)
        """
        start_time = time.time()
        inactive_time = start_time
        inactivity_timeout = 10
        # Generate a random token for shell responsiveness check
        random_string = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
        check_command = f'\n export KUBE_CMD_TOKEN={random_string}{random_string} # testing shell response \n'
        try:
            self.stream.write_stdin(check_command)
        except Exception as e:
            self.logger.error(f"Error writing to stream: {str(e)}")
            return b'', True
        random_pattern = re.compile(f'token={random_string}{random_string}'.encode())
        partial = False
        try:
            while time.time() - start_time < timeout:
                chunk = self._read_without_check(0.2)
                if chunk:
                    self.buffer += chunk
                    inactive_time = time.time()
                    if random_pattern.search(self.buffer):
                        return self._process_output(self.buffer), partial
                    if n != -1 and len(self.buffer) >= n:
                        break
                now = time.time()
                if now - start_time >= timeout or now - inactive_time > inactivity_timeout:
                    self.logger.debug("Read timeout. Returning partial output...")
                    partial = True
                    break
            # Process the final buffer content
            if n == -1:
                result = self.buffer
                self.buffer = b''
            else:
                result = self.buffer[:n]
                self.buffer = self.buffer[n:]
            return self._process_output(result), partial
        except Exception as e:
            self.logger.error(f"Error in read_with_partial: {str(e)}\n{traceback.format_exc()}")
            return b'', True
    
    def read(self, n=-1, timeout=10):
        """
        Read from the stream, returning only the processed output.
        Args:
            n: Number of bytes to read (-1 for unlimited)
            timeout: Read timeout in seconds
        Returns:
            bytes: Processed output
        """
        output, _ = self.read_with_partial(n, timeout)
        return output

class KubernetesStdinWriter:
    """
    Class to write to the stdin of a Kubernetes pod subprocess.
    Handles writing commands and data to the pod's standard input stream.
    """
    def __init__(self, stream: WSClient, logger: Optional[object] = None):
        """
        Initialize the stdin writer.
        Args:
            stream: The Kubernetes websocket stream
            logger: Optional logger instance
        """
        self.stream = stream
        self.logger = logger
        self._write_lock = threading.Lock()
    
    def write(self, data: bytes) -> None:
        """
        Write data to the stream's stdin.
        Args:
            data: The data to write to stdin
        """
        try:
            with self._write_lock:
                if isinstance(data, bytes):
                    data = data.decode('utf-8', errors='replace')
                self.logger.debug(f"Writing to stdin: {data!r}")
                self.stream.write_stdin(data)
                # Small delay to ensure proper command processing
                time.sleep(0.1)
        except Exception as e:
            self.logger.error(f"Error writing to stdin: {str(e)}")
            raise
    
    def flush(self) -> None:
        """
        Flush the stdin stream.
        In the Kubernetes implementation, this is a no-op as the websocket
        client handles flushing automatically.
        """
        pass