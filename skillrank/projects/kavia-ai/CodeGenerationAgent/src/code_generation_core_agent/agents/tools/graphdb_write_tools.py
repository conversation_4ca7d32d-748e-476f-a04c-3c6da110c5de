from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
from code_generation_core_agent.database.database_connection import connect_node_db
import yaml

class GraphDBWriteTools(BaseToolInterface):
    def get_tool_name(self):
        return "GraphDBWriteTools"

    def __init__(self, callbacks, base_path, logger, llm=None, executor=None):
        super().__init__(callbacks, base_path, logger, llm, executor)
        self.db = self._get_db_connection()
        self.logger = logger
        self.components = []  # List to maintain component states
        self.architecture_root_id = None  # To store the ArchitectureRoot node ID
        self.project_node_id = None  # To store the project node ID

    def _get_db_connection(self):
        return connect_node_db()

    def _run_db_operation(self, coroutine):
        try:
            future = shared_loop.run_coroutine(coroutine)
            return future.result(timeout=30)  # Adjust timeout as needed
        except Exception as e:
            self.logger.error(f"Error in database operation: {str(e)}")
            raise

    '''@register_function(
        description="Create a new node in the graph database",
        parameters={
            "type": "object",
            "properties": {
                "node_label": {"type": "string", "description": "Label for the node (e.g., ArchitecturalComponent, Repository, ArchitectureRoot, etc.)"},
                "properties": {
                    "type": "object", 
                    "description": "Properties of the node. This should include all relevant properties for the node type. This should include at least a name for the node and a description field. This should also include a purpose field the describes the purpose of the item that this node represents."},
                "state": {"type": "string", "description": "State of the node (e.g., provisional_state)"}
            },
            "required": ["node_label", "properties", "state"]
        }
    )
    def create_node(self, node_label, properties, state):
        try:
            properties['state'] = state
            result = self._run_db_operation(self.db.create_node([node_label], properties))
            return {"status": "SUCCESS", "node_id": result['id'], "message": f"Node of type {node_label} created successfully"}
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}'''

    @register_function(
        description="Create a relationship between two nodes",
        parameters={
            "type": "object",
            "properties": {
                "start_node_id": {"type": "integer", "description": "ID of the start node"},
                "end_node_id": {"type": "integer", "description": "ID of the end node"},
                "relationship_type": {"type": "string", "description": "Type of the relationship"},
                "properties": {"type": "object", "description": "Properties of the relationship"}
            },
            "required": ["start_node_id", "end_node_id", "relationship_type"]
        }
    )
    def create_relationship(self, start_node_id, end_node_id, relationship_type, properties=None):
        try:
            result = self._run_db_operation(self.db.create_relationship(start_node_id, end_node_id, relationship_type, properties))
            return {"status": "SUCCESS", "message": f"Relationship of type {relationship_type} created successfully"}
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}

    @register_function(
        description="Create a new node and establish a relationship between an existing node and the newly created node. If a node with the same name and label already exists with the specified relationship, return information about the existing node.",
        parameters={
            "type": "object",
            "properties": {
                "existing_node_id": {"type": "integer", "description": "ID of the existing node to establish the relationship with"},
                "node_label": {"type": "string", "description": "Label for the new node (e.g., ArchitecturalComponent, Repository, ArchitectureRoot, etc.)"},
                "node_properties": {
                    "type": "object", 
                    "description": "Properties of the new node. This should include at least a name, description, and purpose."
                },
                "state": {"type": "string", "description": "State of the new node (e.g., provisional_state)"},
                "relationship_type": {"type": "string", "description": "Type of the relationship between the existing node and the new node"},
                "relationship_properties": {
                    "type": "object",
                    "description": "Properties of the relationship, optional"
                }
            },
            "required": ["existing_node_id", "node_label", "node_properties", "state", "relationship_type"]
        }
    )
    def create_node_and_relationship(self, existing_node_id, node_label, node_properties, state, relationship_type, relationship_properties=None):
        try:
            # Check if a node with the same name and label exists with the specified relationship
            node_name = node_properties.get('name')
            if not node_name:
                return {"status": "ERROR", "message": "Node properties must include a 'name'."}

            existing_nodes = self._run_db_operation(
                self.db.get_nodes_connected_by_relationship(existing_node_id, relationship_type, node_label)
            )

            for node in existing_nodes:
                if node['properties'].get('name') == node_name:
                    return {
                        "status": "SUCCESS",
                        "node_id": node['id'],
                        "message": f"A node of type {node_label} with name '{node_name}' already exists, so we are reusing it.",
                    }

            # If no existing node found, proceed with creation
            node_properties['state'] = state
            create_node_result = self._run_db_operation(self.db.create_node([node_label], node_properties))
            node_id = create_node_result['id']
            
            # Create the relationship between the existing node and the newly created node
            create_relationship_result = self._run_db_operation(
                self.db.create_relationship(existing_node_id, node_id, relationship_type, relationship_properties)
            )
            
            return {
                "status": "SUCCESS",
                "node_id": node_id,
                "message": f"Node of type {node_label} created successfully, and relationship of type {relationship_type} established."
            }
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}

    
    @register_function(
        description="Update a node in the graph database",
        parameters={
            "type": "object",
            "properties": {
                "node_id": {"type": "integer", "description": "ID of the node to update"},
                "properties": {"type": "object", "description": "Updated properties of the node"}
            },
            "required": ["node_id", "properties"]
        }
    )
    def update_node(self, node_id, properties):
        try:
            result = self._run_db_operation(self.db.update_node_by_id(node_id, properties))
            return {"status": "SUCCESS", "message": f"Node updated successfully"}
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}

    @register_function(
        description="Delete a node from the graph database",
        parameters={
            "type": "object",
            "properties": {
                "node_id": {"type": "integer", "description": "ID of the node to delete"}
            },
            "required": ["node_id"]
        }
    )
    def delete_node(self, node_id):
        try:
            result = self._run_db_operation(self.db.delete_node(node_id))
            return {"status": "SUCCESS", "message": f"Node deleted successfully"}
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}

    @register_function(
        description="Update a relationship in the graph database",
        parameters={
            "type": "object",
            "properties": {
                "start_node_id": {"type": "integer", "description": "ID of the start node"},
                "end_node_id": {"type": "integer", "description": "ID of the end node"},
                "relationship_type": {"type": "string", "description": "Type of the relationship"},
                "properties": {"type": "object", "description": "Updated properties of the relationship"}
            },
            "required": ["start_node_id", "end_node_id", "relationship_type", "properties"]
        }
    )
    def update_relationship(self, start_node_id, end_node_id, relationship_type, properties):
        try:
            result = self._run_db_operation(self.db.update_relationship_properties(start_node_id, end_node_id, relationship_type, properties))
            return {"status": "SUCCESS", "message": f"Relationship updated successfully"}
        except Exception as e:
            return {"status": "ERROR", "message": str(e)}
        
    def _parse_yaml_diff(self, diff):
        added_lines = []
        removed_lines = []
        for line in diff.split('\n'):
            if line.startswith('+') and not line.startswith('+++'):
                added_lines.append(line[1:])
            elif line.startswith('-') and not line.startswith('---'):
                removed_lines.append(line[1:])
        return '\n'.join(added_lines), '\n'.join(removed_lines)
        
    def create_architectural_component(self, code_graph_node_id):
        # This is a placeholder function. Implement the actual logic here.
        # For now, we'll just return some dummy data

        code_graph_node = self._run_db_operation(self.db.get_node_by_id(code_graph_node_id))

        return {
            "name": code_graph_node['properties']['name'],
            "description": code_graph_node['properties']['description'],
            "state": "provisional"
        }


    @register_function(
        description="Insert a new component into the component hierarchy and create an ArchitecturalComponent in the Architecture DB.",
        parameters={
            "type": "object",
            "properties": {
                "code_graph_parent_id": {"type": "integer", "description": "ID of the parent component in code graph"},
                "code_graph_node_id": {"type": "integer", "description": "ID of the node in the code graph"}
            },
            "required": ["code_graph_parent_id", "code_graph_node_id"]
        }
    )
    def insert_component(self, code_graph_parent_id, code_graph_node_id):
        if self.project_node_id is None or self.architecture_root_id is None:
            raise Exception("GraphDB tool is not activated. Call activate_graphdb first.")
        try:
            if code_graph_node_id == self.project_node_id:
                return {"status": "ERROR", "message": "Cannot insert project node as a component. Start with a connected node."}
            #get the component from the list with the code_graph_parent_id
            component = next((comp for comp in self.components if comp['code_graph_id'] == code_graph_parent_id), None)
            if component is None:
                #insert this under ArchitectureRoot
                parent_id = self.architecture_root_id
            else:
                parent_id = component['architectural_component_id']

            # Create properties and label for the new ArchitecturalComponent
            component_properties = self.create_architectural_component(code_graph_node_id)

            result = self.create_node_and_relationship( parent_id, "ArchitecturalComponent", component_properties, "provisional", "HAS_CHILD")

            if result['status'] == "SUCCESS":
                created_component = {
                    "architectural_component_id": result['node_id'],
                    "name": component_properties.get('name'),
                    "code_graph_id": code_graph_node_id,
                    "state": "created"
                }
                
                self.components.append(created_component)

                return {
                    "status": "SUCCESS",
                    "message": "Component inserted successfully",
                    "created_component": created_component,
                    "component_list": self.components  # Return the entire components list
                }
            else:
                return {"status": "ERROR", "message": result['message']}

        except Exception as e:
            return {"status": "ERROR", "message": str(e)}
        
    @register_function(
    description="Set the state of a component in the component hierarchy.",
    parameters={
        "type": "object",
        "properties": {
            "code_graph_id": {"type": "integer", "description": "Code graph ID of the component"},
            "state": {"type": "string", "description": "New state of the component"}
        },
        "required": ["code_graph_id", "state"]
    }
)
    def set_component_state(self, code_graph_id, state):

        if self.project_node_id is None or self.architecture_root_id is None:
            raise Exception("GraphDB tool is not activated. Call activate_graphdb first.")
        
        if code_graph_id == self.project_node_id:
            return {"status": "SUCCESS", "message": "Need not update state of the project node."}
        try:
            # Find the component in our list
            component = next((comp for comp in self.components if comp['code_graph_id'] == code_graph_id), None)
            
            if component is None:
                return {
                    "status": "ERROR",
                    "message": f"Component with code_graph_id {code_graph_id} not found in the component list"
                }

            # Update the state in our components list
            if state == 'analyzed':
                # Remove the component from the list if state is 'analyzed'
                self.components = [comp for comp in self.components if comp['code_graph_id'] != code_graph_id]
                message = f"Component with code_graph_id {code_graph_id} has been analyzed and removed from the list"
            else:
                # Update the state of the component
                component['state'] = state
                message = f"Component state updated to '{state}'"

            return {
                "status": "SUCCESS",
                "message": message,
                "updated_component": {
                    "code_graph_id": code_graph_id,
                    "architectural_component_id": component['architectural_component_id'],
                    "state": state
                },
                "components": self.components  # Return the entire updated components list
            }

        except Exception as e:
            return {"status": "ERROR", "message": str(e)}
        
    @register_function(
        description="Activate the graph database tool with the project node ID and set up the ArchitectureRoot node.",
        parameters={
            "type": "object",
            "properties": {
                "project_node_id": {"type": "integer", "description": "ID of the project node"}
            },
            "required": ["project_node_id"]
        }
    )
    def activate_graphdb(self, project_node_id):
        try:
            self.project_node_id = project_node_id

            # Get connected nodes of the project node
            connected_nodes = self._run_db_operation(
                self.db.get_connected_nodes(project_node_id)
            )

            # Look for ArchitectureRoot node
            architecture_root = next((node for node in connected_nodes if 'ArchitectureRoot' in node['labels']), None)

            if architecture_root:
                self.architecture_root_id = architecture_root['id']
            else:
                # Create ArchitectureRoot node if it doesn't exist
                create_result = self.create_node_and_relationship(
                        existing_node_id=project_node_id,
                        node_label='ArchitectureRoot',
                        node_properties={'name': 'Architecture Root'},
                        state='provisional',
                        relationship_type='CONTAINS'
                    )
                
                if create_result['status'] == 'SUCCESS':
                    self.architecture_root_id = create_result['node_id']
                else:
                    raise Exception("Failed to create ArchitectureRoot node")

            return {
                "status": "SUCCESS",
                "message": "GraphDB tool activated successfully"
            }

        except Exception as e:
            self.logger.error(f"Error in activate_graphdb: {str(e)}")
            return {"status": "ERROR", "message": str(e)}