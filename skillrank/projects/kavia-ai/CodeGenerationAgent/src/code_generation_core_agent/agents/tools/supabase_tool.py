import os
from typing import Any, Dict, List, Optional

from dotenv import load_dotenv
from supabase import Client, create_client          # supabase-py v2
from postgrest.exceptions import APIError

from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface

# --------------------------------------------------------------------------- #
#  Initialise Supabase (lazy)                                                 #
# --------------------------------------------------------------------------- #
load_dotenv()
_SUPABASE_URL = os.getenv("SUPABASE_URL")
_SUPABASE_KEY = os.getenv("SUPABASE_KEY")
_SUPABASE_DB_URL = os.getenv("SUPABASE_DB_URL")

# Global variable to store base_path
_BASE_PATH: Optional[str] = None

#  The global connection is created lazily so that credentials can come from
#  the project manifest (via connect_to_project) instead of environment vars.
#  This allows for loading credentials from .env files with different naming conventions.
_supabase: Optional[Client] = None
_setup_completed: bool = False  # Track whether setup_supbase_project has been called
if _SUPABASE_URL and _SUPABASE_KEY:
    _supabase = create_client(_SUPABASE_URL, _SUPABASE_KEY)

# Default .env file path for automatic connection
_DEFAULT_ENV_PATH = ".env"

FILTER_SCHEMA = {
    "type": "object",
    "properties": {
        "column":   {"type": "string", "description": "Column name"},
        "operator": {"type": "string",
                     "description": "PostgREST operator, e.g. eq, gt, ilike"},
        "value":    {"type": ["string", "number", "boolean"]}
    },
    "required": ["column", "operator", "value"]
}
SECRET_TABLE_SQL = """
create extension if not exists pgcrypto;

create table if not exis    ts public.secret_store (
    key text primary key,
    value bytea not null
);

-- Example RLS: only service role can read/write
alter table public.secret_store enable row level security;
create policy srole_rw on public.secret_store
    for all to authenticated
    using (auth.role() = 'service_role');
"""
# --------------------------------------------------------------------------- #
#  Internal helpers                                                           #
# --------------------------------------------------------------------------- #

def _load_env_from_file(env_file_path: str, supabase_key: str = None,
                        supabase_url: str = None, supabase_db_url: str = None) -> Dict[str, str]:
    """
    Load environment variables with the following priority:
    1. Environment variables (os.getenv) - highest priority
    2. Project manifest (.project_manifest.yaml) - second priority
    3. .env file - lowest priority
    
    Supports both REACT_APP_ and APP_ prefixes for Supabase credentials.
    Supports both KEY and ANON_KEY naming conventions.
    
    Returns a dict with the loaded environment variables.
    """
    env_vars = {}

    # If passed credentials are provided, use them directly


    # Priority 1: Environment variables (highest priority)
    # Try REACT_APP_ prefix first (with both KEY and ANON_KEY)
    react_app_url = os.getenv("REACT_APP_SUPABASE_URL")
    react_app_key = os.getenv("REACT_APP_SUPABASE_KEY") or os.getenv("REACT_APP_SUPABASE_ANON_KEY")
    react_app_db_url = os.getenv("REACT_APP_SUPABASE_DB_URL")
    if supabase_url:
        env_vars["SUPABASE_URL"] = supabase_url
        env_vars["SUPABASE_KEY"] = supabase_key
        env_vars["SUPABASE_DB_URL"] = supabase_db_url
        return env_vars

    if react_app_url and react_app_key:
        env_vars["SUPABASE_URL"] = react_app_url
        env_vars["SUPABASE_KEY"] = react_app_key
        if react_app_db_url:
            env_vars["SUPABASE_DB_URL"] = react_app_db_url
        return env_vars
    
    # Try APP_ prefix (with both KEY and ANON_KEY)
    app_url = os.getenv("APP_SUPABASE_URL")
    app_key = os.getenv("APP_SUPABASE_KEY") or os.getenv("APP_SUPABASE_ANON_KEY")
    app_db_url = os.getenv("APP_SUPABASE_DB_URL")
    
    if app_url and app_key:
        env_vars["SUPABASE_URL"] = app_url
        env_vars["SUPABASE_KEY"] = app_key
        if app_db_url:
            env_vars["SUPABASE_DB_URL"] = app_db_url
        return env_vars
    
    # Try standard names (with both KEY and ANON_KEY)
    standard_url = os.getenv("SUPABASE_URL")
    standard_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    standard_db_url = os.getenv("SUPABASE_DB_URL")

    
    if standard_url and standard_key:
        env_vars["SUPABASE_URL"] = standard_url
        env_vars["SUPABASE_KEY"] = standard_key
        if standard_db_url:
            env_vars["SUPABASE_DB_URL"] = standard_db_url
        return env_vars
    
    # Priority 2: Project manifest (.project_manifest.yaml)
    if _BASE_PATH:
        manifest_path = os.path.join(_BASE_PATH, ".project_manifest.yaml")
        if os.path.exists(manifest_path):
            try:
                from code_generation_core_agent.project_schemas import ProjectSchema
                project_schema = ProjectSchema.load_from_file(manifest_path)
                
                # Check project overview env
                if project_schema.overview and project_schema.overview.env:
                    overview_env = project_schema.overview.env
                    
                    # Try REACT_APP_ prefix
                    react_app_url = overview_env.get("REACT_APP_SUPABASE_URL")
                    react_app_key = overview_env.get("REACT_APP_SUPABASE_KEY") or overview_env.get("REACT_APP_SUPABASE_ANON_KEY")
                    react_app_db_url = overview_env.get("REACT_APP_SUPABASE_DB_URL")
                    
                    if react_app_url and react_app_key:
                        env_vars["SUPABASE_URL"] = react_app_url
                        env_vars["SUPABASE_KEY"] = react_app_key
                        if react_app_db_url:
                            env_vars["SUPABASE_DB_URL"] = react_app_db_url
                        return env_vars
                    
                    # Try APP_ prefix
                    app_url = overview_env.get("APP_SUPABASE_URL")
                    app_key = overview_env.get("APP_SUPABASE_KEY") or overview_env.get("APP_SUPABASE_ANON_KEY")
                    app_db_url = overview_env.get("APP_SUPABASE_DB_URL")
                    
                    if app_url and app_key:
                        env_vars["SUPABASE_URL"] = app_url
                        env_vars["SUPABASE_KEY"] = app_key
                        if app_db_url:
                            env_vars["SUPABASE_DB_URL"] = app_db_url
                        return env_vars
                    
                    # Try standard names
                    standard_url = overview_env.get("SUPABASE_URL")
                    standard_key = overview_env.get("SUPABASE_KEY") or overview_env.get("SUPABASE_ANON_KEY")
                    standard_db_url = overview_env.get("SUPABASE_DB_URL")
                    
                    if standard_url and standard_key:
                        env_vars["SUPABASE_URL"] = standard_url
                        env_vars["SUPABASE_KEY"] = standard_key
                        if standard_db_url:
                            env_vars["SUPABASE_DB_URL"] = standard_db_url
                        return env_vars
                
                # Check container env if overview doesn't have it
                for container in project_schema.containers:
                    if container.env:
                        container_env = container.env
                        
                        # Try REACT_APP_ prefix
                        react_app_url = container_env.get("REACT_APP_SUPABASE_URL")
                        react_app_key = container_env.get("REACT_APP_SUPABASE_KEY") or container_env.get("REACT_APP_SUPABASE_ANON_KEY")
                        react_app_db_url = container_env.get("REACT_APP_SUPABASE_DB_URL")
                        
                        if react_app_url and react_app_key:
                            env_vars["SUPABASE_URL"] = react_app_url
                            env_vars["SUPABASE_KEY"] = react_app_key
                            if react_app_db_url:
                                env_vars["SUPABASE_DB_URL"] = react_app_db_url
                            return env_vars
                        
                        # Try APP_ prefix
                        app_url = container_env.get("APP_SUPABASE_URL")
                        app_key = container_env.get("APP_SUPABASE_KEY") or container_env.get("APP_SUPABASE_ANON_KEY")
                        app_db_url = container_env.get("APP_SUPABASE_DB_URL")
                        
                        if app_url and app_key:
                            env_vars["SUPABASE_URL"] = app_url
                            env_vars["SUPABASE_KEY"] = app_key
                            if app_db_url:
                                env_vars["SUPABASE_DB_URL"] = app_db_url
                            return env_vars
                        
                        # Try standard names
                        standard_url = container_env.get("SUPABASE_URL")
                        standard_key = container_env.get("SUPABASE_KEY") or container_env.get("SUPABASE_ANON_KEY")
                        standard_db_url = container_env.get("SUPABASE_DB_URL")
                        
                        if standard_url and standard_key:
                            env_vars["SUPABASE_URL"] = standard_url
                            env_vars["SUPABASE_KEY"] = standard_key
                            if standard_db_url:
                                env_vars["SUPABASE_DB_URL"] = standard_db_url
                            return env_vars
                            
            except Exception as e:
                # If manifest loading fails, continue to next priority
                pass
    
    # Priority 3: .env file (lowest priority)
    if os.path.exists(env_file_path):
        # Load the .env file
        load_dotenv(env_file_path)
        
        # Try REACT_APP_ prefix first (with both KEY and ANON_KEY)
        react_app_url = os.getenv("REACT_APP_SUPABASE_URL")
        react_app_key = os.getenv("REACT_APP_SUPABASE_KEY") or os.getenv("REACT_APP_SUPABASE_ANON_KEY")
        react_app_db_url = os.getenv("REACT_APP_SUPABASE_DB_URL")
        
        if react_app_url and react_app_key:
            env_vars["SUPABASE_URL"] = react_app_url
            env_vars["SUPABASE_KEY"] = react_app_key
            if react_app_db_url:
                env_vars["SUPABASE_DB_URL"] = react_app_db_url
            return env_vars
        
        # Try APP_ prefix (with both KEY and ANON_KEY)
        app_url = os.getenv("APP_SUPABASE_URL")
        app_key = os.getenv("APP_SUPABASE_KEY") or os.getenv("APP_SUPABASE_ANON_KEY")
        app_db_url = os.getenv("APP_SUPABASE_DB_URL")
        
        if app_url and app_key:
            env_vars["SUPABASE_URL"] = app_url
            env_vars["SUPABASE_KEY"] = app_key
            if app_db_url:
                env_vars["SUPABASE_DB_URL"] = app_db_url
            return env_vars
        
        # Fallback to standard names (with both KEY and ANON_KEY)
        standard_url = os.getenv("SUPABASE_URL")
        standard_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        standard_db_url = os.getenv("SUPABASE_DB_URL")
        
        if standard_url and standard_key:
            env_vars["SUPABASE_URL"] = standard_url
            env_vars["SUPABASE_KEY"] = standard_key
            if standard_db_url:
                env_vars["SUPABASE_DB_URL"] = standard_db_url
            return env_vars
    
    # Final fallback: use already defined global variables if they exist
    if _SUPABASE_URL and _SUPABASE_KEY:
        env_vars["SUPABASE_URL"] = _SUPABASE_URL
        env_vars["SUPABASE_KEY"] = _SUPABASE_KEY
        if _SUPABASE_DB_URL:
            env_vars["SUPABASE_DB_URL"] = _SUPABASE_DB_URL
        return env_vars
    
    raise ValueError(
        f"No valid Supabase credentials found in environment variables, project manifest, or {env_file_path}. "
        "Expected either REACT_APP_SUPABASE_URL/KEY/ANON_KEY or APP_SUPABASE_URL/KEY/ANON_KEY or SUPABASE_URL/KEY/ANON_KEY"
    )

def supabase_connect(supabase_key: str = None, supabase_url : str = None, supabase_db_url = None ) -> Dict[str, Any]:
    """
    Automatically establish connection using default .env file or environment variables.
    Returns a status dict indicating success or failure.
    """
    global _supabase
    
    # Already connected → short-circuit
    if _supabase is not None:
        return {
            "status": "SUCCESS",
            "detail": "Supabase client already connected.",
        }
    
    # Try to load from default .env file first
    env_file_path = _DEFAULT_ENV_PATH
    env_vars = _load_env_from_file(env_file_path,
                                    supabase_key=supabase_key,
                                    supabase_url=supabase_url,
                                    supabase_db_url=supabase_db_url)
    supabase_url = env_vars["SUPABASE_URL"]
    supabase_key = env_vars["SUPABASE_KEY"]
    db_url = env_vars.get("SUPABASE_DB_URL")

    if not supabase_url or not supabase_key:
        return {
            "status": "ERROR", 
            "error": "No Supabase credentials found. Please ensure either:\n"
                    "1. A .env file exists with SUPABASE_URL and SUPABASE_KEY\n"
        }
    
    # Create client
    try:
        _supabase = create_client(supabase_url, supabase_key)
        global _setup_completed
        if db_url and not _setup_completed:
            try:
                setup_supbase_project(db_url)  # Ensure run_sql() exists
                _setup_completed = True
            except Exception as e:
                return {
                    "status":"PARTIAL_SUCCESS",
                    "detail":"Supabase client connected but, DB connection is not estabilished"
                }
        return {
            "status": "SUCCESS",
            "detail": "Supabase client connected successfully.",
        }
    except Exception as e:
        print(f"Failed to create Supabase client: {str(e)}")
        print("Env variables used:")
        print(f"SUPABASE_URL: {supabase_url}")
        print(f"SUPABASE_KEY: {supabase_key}")
        if db_url:
            print(f"SUPABASE_DB_URL: {db_url}")
        else:
            print("No SUPABASE_DB_URL provided, run_sql() will not be available.")

        return {"status": "ERROR", "error": f"Failed to create Supabase client: {str(e)}"}

def _ensure_client() -> None:
    """Ensure the global Supabase client is initialised, auto-connecting if needed."""
    global _supabase
    
    if _supabase is None:
        # Try to auto-connect
        result = supabase_connect()
        if result["status"] != "SUCCESS":
            raise RuntimeError(f"Failed to auto-connect to Supabase: {result['error']}")

def _rpc(fn: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Call a Postgres RPC and normalise the response."""
    try:
        _ensure_client()
        resp = _supabase.rpc(fn, params or {}).execute()
        data = getattr(resp, "data", resp)
        return {"status": "SUCCESS", "data": data}
    except RuntimeError as e:
        # Connection error
        return {"status": "ERROR", "error": str(e)}
    except APIError as e:
        return {"status": "ERROR", "error": str(e)}
    except Exception as e:
        return {"status": "ERROR", "error": str(e)}


def _call(builder):
    """
    Execute a query-builder chain (`supabase.table(...).insert(...)...`)
    and normalise its response.
    """
    try:
        _ensure_client()
        resp = builder.execute()           # raises APIError on failure
        return {"status": "SUCCESS", "data": resp.data}
    except RuntimeError as e:
        # Connection error
        return {"status": "ERROR", "error": str(e)}
    except APIError as e:
        return {"status": "ERROR", "error": str(e)}
    except Exception as e:
        return {"status": "ERROR", "error": str(e)}

# --------------------------------------------------------------------------- #
#  SupabaseTool implementation                                                #
# --------------------------------------------------------------------------- #
class SupabaseTool(BaseToolInterface):
    """
    Thin wrapper exposing Supabase operations to the agent framework.
    * Arbitrary SQL   → run_sql()  (via SECURITY DEFINER Postgres function)
    * Everyday DML    → uses supabase-py query-builder (.table())
    * Automatic connection management → connects automatically on first use
    """

    def __init__(self, callback_functions: dict[str, callable],
                 base_path: str,
                 logger=None, llm=None, executor=None):
        super().__init__(
            callback_functions=callback_functions,
            base_path=base_path,
            logger=logger,
            llm=llm,
            executor=executor)
        self.base_path = base_path
        
        # Store base_path globally for use in _load_env_from_file
        global _BASE_PATH
        _BASE_PATH = base_path
        
        print("---- INITIALISING SupabaseTool ---- with BASE_PATH:", base_path)
   
    @staticmethod
    def global_connect_to_project(supabase_url, supabase_key):
        global _supabase
        _supabase = create_client(supabase_url, supabase_key)

    @staticmethod
    def connect_project_internal(
        env_file_path: str,
    ) -> Dict[str, Any]:
        global _supabase

        # Already connected → short-circuit
        if _supabase is not None:
            return {
                "status": "SUCCESS",
                "detail": "Supabase client already initialised.",
            }

        # Load credentials from .env file
        try:
            env_vars = _load_env_from_file(env_file_path)
            supabase_url = env_vars["SUPABASE_URL"]
            supabase_key = env_vars["SUPABASE_KEY"]
        except (FileNotFoundError, ValueError) as e:
            return {"status": "ERROR", "error": str(e)}

        if not supabase_url or not supabase_key:
            return {
                "status": "ERROR",
                "error": "Missing Supabase credentials (url/key) in .env file.",
            }

        # Create client -------------------------------------------------------
        try:
            _supabase = create_client(supabase_url, supabase_key)
            db_url = env_vars.get("SUPABASE_DB_URL")

            global _setup_completed
            if db_url and not _setup_completed:
                setup_supbase_project(db_url)  # Ensure run_sql() exists
                _setup_completed = True
            return {
                "status": "SUCCESS",
                "detail": "Supabase client connected successfully.",
            }
        except Exception as e:  # noqa: BLE001
            return {"status": "ERROR", "error": str(e)}

    # ------------------------------------------------------------------ #
    #  Internal static helpers                                           #
    # ------------------------------------------------------------------ #
    @staticmethod
    def _quote(text: str) -> str:
        """Return a safe SQL literal (escapes single quotes)."""
        return "'" + text.replace("'", "''") + "'"

    @staticmethod
    def is_connected() -> bool:
        """Check if the Supabase client is currently connected."""
        return _supabase is not None

    @staticmethod
    def reset_connection() -> None:
        """Reset the global Supabase client connection."""
        global _supabase
        _supabase = None

    # ------------------------------------------------------------------ #
    #  BaseToolInterface implementation                                  #
    # ------------------------------------------------------------------ #
    def get_tool_name(self) -> str:
        return "SupabaseTool"

    # ------------------------------------------------------------------ #
    #  Arbitrary SQL (unchanged)                                          #
    # ------------------------------------------------------------------ #
    @register_function(
        description="Execute arbitrary SQL via the Postgres `run_sql` RPC.",
        parameters={
            "type": "object",
            "properties": {"sql": {"type": "string"}},
            "required": ["sql"],
        },
    )
    def run_sql(self, sql: str) -> Dict[str, Any]:
        sql = sql.rstrip(" ;")                    # strip trailing semicolon(s)
        return _rpc("run_sql", {"query": sql})

    # ------------------------------------------------------------------ #
    #  INFORMATION_SCHEMA helpers (still raw SQL)                         #
    # ------------------------------------------------------------------ #
    @register_function(
        description="List tables in the public schema.",
        parameters={"type": "object", "properties": {}},
    )
    def list_tables(self) -> Dict[str, Any]:
        res = self.run_sql(
            "SELECT table_name FROM information_schema.tables "
            "WHERE table_schema = 'public' ORDER BY table_name"
        )
        if res["status"] == "SUCCESS":
            rows = res["data"] or []
            res["tables"] = [r["table_name"] for r in rows]
        return res

    @register_function(
        description="Get column metadata for a table.",
        parameters={
            "type": "object",
            "properties": {"table_name": {"type": "string"}},
            "required": ["table_name"],
        },
    )
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        safe = table_name.replace('"', '""')       # basic escape
        res = self.run_sql(
            "SELECT column_name, data_type, is_nullable, column_default "
            "FROM information_schema.columns "
            "WHERE table_schema = 'public' "
            f"AND table_name = '{safe}'"
        )
        if res["status"] == "SUCCESS":
            res["columns"] = [
                {
                    "name": c["column_name"],
                    "type": c["data_type"],
                    "nullable": c["is_nullable"] == "YES",
                    "default": c["column_default"],
                }
                for c in res["data"]
            ]
        return res

    # ------------------------------------------------------------------ #
    #  DDL helpers (still raw SQL)                                         #
    # ------------------------------------------------------------------ #
    @register_function(
        description="Create a table (IF NOT EXISTS).",
        parameters={
            "type": "object",
            "properties": {
                "table_name": {"type": "string"},
                "columns": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "type": {"type": "string"},
                            "nullable": {"type": "boolean"},
                            "default": {
                                "type": ["string", "number", "boolean", "null"]
                            },
                        },
                        "required": ["name", "type"],
                    },
                },
            },
            "required": ["table_name", "columns"],
        },
    )
    def create_table(
        self, table_name: str, columns: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        ddl_parts = []
        for col in columns:
            seg = f"{col['name']} {col['type']}"
            if not col.get("nullable", True):
                seg += " NOT NULL"
            if col.get("default") is not None:
                seg += f" DEFAULT {col['default']}"
            ddl_parts.append(seg)
        return self.run_sql(
            f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(ddl_parts)})"
        )

    @register_function(
        description="Drop a table (IF EXISTS).",
        parameters={
            "type": "object",
            "properties": {"table_name": {"type": "string"}},
            "required": ["table_name"],
        },
    )
    def drop_table(self, table_name: str) -> Dict[str, Any]:
        return self.run_sql(f"DROP TABLE IF EXISTS {table_name}")

    # ------------------------------------------------------------------ #
    #  Query-builder based CRUD                                           #
    # ------------------------------------------------------------------ #
    def insert_rows(
        self,
        table_name: str,
        rows: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Insert rows (supabase-py v2 returns newly inserted rows)."""
        return _call(_supabase.table(table_name).insert(rows))

    @register_function(
        description="Run a filtered SELECT via query-builder.",
        parameters={
            "type": "object",
            "properties": {
                "table_name": {"type": "string"},
                "filters": {
                    "type": "array",
                    "items": FILTER_SCHEMA,
                    "description": "e.g. [{\"column\":\"id\",\"operator\":\"eq\",\"value\":42}]",
                },
                "limit": {"type": "integer", "minimum": 1},
            },
            "required": ["table_name"],
        },
    )
    def select_rows(
        self,
        table_name: str,
        filters: Optional[List[Dict[str, Any]]] = None,
        limit: int = 100,
    ) -> Dict[str, Any]:

        builder = _supabase.table(table_name).select("*").limit(limit)
        for f in filters or []:
            builder = builder.filter(f["column"], f["operator"], f["value"])
        return _call(builder)

    @register_function(
        description="Update rows and return the changed records.",
        parameters={
            "type": "object",
            "properties": {
                "table_name": {"type": "string"},
                "updates": {
                    "type": "object",
                    "description": "Dict of column: new_value pairs.",
                },
                "filters": {"type": "array", "items": FILTER_SCHEMA},
            },
            "required": ["table_name", "updates", "filters"],
        },
    )
    def update_rows(
        self,
        table_name: str,
        updates: Dict[str, Any],
        filters: List[Dict[str, Any]],
    ) -> Dict[str, Any]:

        builder = _supabase.table(table_name).update(updates)
        for f in filters:
            builder = builder.filter(f["column"], f["operator"], f["value"])
        return _call(builder.select("*"))

    @register_function(
        description="Delete rows and return the deleted records.",
        parameters={
            "type": "object",
            "properties": {
                "table_name": {"type": "string"},
                "filters": {"type": "array", "items": FILTER_SCHEMA},
            },
            "required": ["table_name", "filters"],
        },
    )
    def delete_rows(
        self,
        table_name: str,
        filters: List[Dict[str, Any]],
    ) -> Dict[str, Any]:

        builder = _supabase.table(table_name).delete()
        for f in filters:
            builder = builder.filter(f["column"], f["operator"], f["value"])
        return _call(builder.select("*"))
    
        
    # ------------------------------------------------------------------ #
    #  Authentication                                                    #
    # ------------------------------------------------------------------ #
    @register_function(
        description="Create a new user (email / password). Returns session.",
        parameters={
            "type": "object",
            "properties": {
                "email":    {"type": "string"},
                "password": {"type": "string", "minLength": 6}
            },
            "required": ["email", "password"],
        },
    )
    def auth_sign_up(self, email: str, password: str) -> Dict[str, Any]:
        return _auth_call("sign_up", {"email": email, "password": password})

    @register_function(
        description="Sign-in with email & password. Returns session.",
        parameters={
            "type": "object",
            "properties": {
                "email":    {"type": "string"},
                "password": {"type": "string"}
            },
            "required": ["email", "password"],
        },
    )
    def auth_sign_in(self, email: str, password: str) -> Dict[str, Any]:
        return _auth_call("sign_in_with_password",
                          {"email": email, "password": password})

    @register_function(
        description="Fetch the currently signed-in user (if any).",
        parameters={"type": "object", "properties": {}},
    )
    def auth_get_user(self) -> Dict[str, Any]:
        return _auth_call("get_user")

    @register_function(
        description="Invalidate the current session (sign-out).",
        parameters={"type": "object", "properties": {}},
    )
    def auth_sign_out(self) -> Dict[str, Any]:
        return _auth_call("sign_out")
    
    
    @register_function(
        description="Install the encrypted secret_store table (idempotent).",
        parameters={"type": "object", "properties": {}},
    )
    def create_secret_store(self) -> Dict[str, Any]:
        return self.run_sql(SECRET_TABLE_SQL)
    
    @register_function(
    description="Set a secret value (upsert, encrypted at rest).",
    parameters={
        "type": "object",
        "properties": {
            "key":   {"type": "string"},
            "value": {"type": "string"},
            "passphrase": {"type": "string",
                            "description": "Symmetric key for pgp_sym_encrypt"}
        },
        "required": ["key", "value", "passphrase"],
    },
    )
    def set_secret(self, key: str, value: str, passphrase: str) -> Dict[str, Any]:
        q = SupabaseTool._quote
        # encrypt( data bytea, key bytea, type text )
        sql = (
            "insert into secret_store(key, value) "
            f"values ( {q(key)}, "
            f"encrypt( convert_to({q(value)}, 'utf8')"
            f", convert_to({q(passphrase)}, 'utf8')"
            ", 'aes' ) ) "
            "on conflict(key) do update set "
            "value = encrypt( convert_to({v}, 'utf8'), "
            "convert_to({p}, 'utf8'), 'aes' )"
        ).format(v=q(value), p=q(passphrase))
        return _rpc('run_sql', {'query': sql})

    @register_function(
        description="Retrieve & decrypt a secret.",
        parameters={
            "type": "object",
            "properties": {
                "key": {"type": "string"},
                "passphrase": {"type": "string"}
            },
            "required": ["key", "passphrase"],
        },
    )
    def get_secret(self, key: str, passphrase: str) -> Dict[str, Any]:
        q = SupabaseTool._quote
        sql = (
            "select convert_from("
            "decrypt(value, convert_to({p}, 'utf8'), 'aes')"
            ", 'utf8') as secret "
            "from secret_store where key = {k}"
        ).format(p=q(passphrase), k=q(key))

        res = _rpc('run_sql', {'query': sql})
        if res['status'] == 'SUCCESS':
            res['secret'] = (res['data'] or [{}])[0].get('secret')
        return res

    @register_function(
        description="Delete a secret and return the deleted key.",
        parameters={
            "type": "object",
            "properties": {"key": {"type": "string"}},
            "required": ["key"],
        },
    )
    def delete_secret(self, key: str) -> Dict[str, Any]:
        builder = (
            _supabase
                .table("secret_store")
                .delete(returning="representation")   # ✅ rows will be returned
                .eq("key", key)
        )
        return _call(builder)                         # _call will .execute()
    

import textwrap
import psycopg2
from contextlib import contextmanager

@contextmanager
def pg_cursor(db_url: str) -> Any:
    """contextlib wrapper → yields an open cursor & commits on success."""
    with psycopg2.connect(db_url) as conn:
        with conn.cursor() as cur:
            yield cur
        conn.commit()
DROP_RUN_SQL = "drop function if exists public.run_sql(text);"
CREATE_SQL = textwrap.dedent(r"""
create or replace function public.run_sql(query text)
returns jsonb
language plpgsql
security definer
set search_path = public
as $$
declare
  clean text := regexp_replace(query, ';\\s*$', '');    -- strip trailing ';'
  result jsonb;
  produces_rows boolean :=
       clean ~* '^\s*(select|with)\s'                   -- SELECT / WITH …
    or clean ~* '\breturning\b';                        -- … or DML with RETURNING
begin
  if produces_rows then
      execute format(
         'with _q as (%s) select coalesce(json_agg(_q), ''[]'') from _q',
         clean
      ) into result;
      return result;
  else
      execute clean;                                    -- DDL or DML w/o rows
      return 'null'::jsonb;
  end if;
end;
$$;
""")
def install_run_sql(db_url) -> None:
    """Create or replace the helper function."""
    with pg_cursor(db_url) as cur:
        cur.execute(CREATE_SQL)
    print("✅  run_sql() installed / updated")

def clear_run_sql(db_url) -> None:
    with pg_cursor(db_url) as cur:
        cur.execute(DROP_RUN_SQL)
    print("🗑️   run_sql() dropped (if it existed)")

def install_pgcrypto(db_url) -> None:
    """
    Guarantee a fresh pgcrypto install in the *public* schema.

    • If pgcrypto exists → DROP EXTENSION pgcrypto;
    • Then (re)CREATE EXTENSION pgcrypto WITH SCHEMA public;
    • Requires a role that has CREATE EXTENSION privilege
      (e.g. supabase_admin on Supabase).
    • Aborts with exit-code 1 if the current role lacks privilege.
    """
    with pg_cursor(db_url) as cur:
        cur.execute(
            "select 1 from pg_extension where extname = 'pgcrypto' limit 1;"
        )
        already = cur.fetchone() is not None

    try:
        with pg_cursor(db_url) as cur:
            if already:
                cur.execute("drop extension pgcrypto cascade;")

            cur.execute("create extension pgcrypto with schema public;")

    except psycopg2.errors.InsufficientPrivilege as e:
        print(
            "❌  could not create/drop pgcrypto (missing privilege).\n"
            "    Re-run this script as the built-in supabase_admin role.\n"
            f"    pgerror: {e.pgerror.strip()}"
        )
        raise e

def setup_supbase_project(db_url: str) -> None:
    """Run the setup SQL for Supabase projects."""
    install_pgcrypto(db_url)   # 1️⃣ ensure encryption funcs exist
    clear_run_sql(db_url)      # 2️⃣ drop old helper
    install_run_sql(db_url)    # 3️⃣ create updated helper

# --------------------------------------------------------------------------- #
#  New internal helper – make auth results JSON-serialisable                  #
# --------------------------------------------------------------------------- #
def _auth_call(fn_name: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Wrap calls to supabase.auth.<fn_name>(...) with SUCCESS / ERROR envelope,
    converting Pydantic response objects (UserResponse, SessionResponse, …)
    to plain dicts so callers can safely subscript.
    """
    try:
        _ensure_client()
        fn = getattr(_supabase.auth, fn_name)
        resp = fn(*args, **kwargs)

        # supabase-py v2 responses inherit from BaseModel → use .model_dump()
        data = (
            resp.model_dump(mode="python", by_alias=True)
            if hasattr(resp, "model_dump")
            else resp
        )
        return {"status": "SUCCESS", "data": data}
    except RuntimeError as e:
        # Connection error
        return {"status": "ERROR", "error": str(e)}
    except Exception as e:
        return {"status": "ERROR", "error": str(e)}
