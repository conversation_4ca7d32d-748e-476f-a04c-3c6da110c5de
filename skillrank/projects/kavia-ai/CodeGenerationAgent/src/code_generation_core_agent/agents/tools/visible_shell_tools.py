import asyncio
import re
import concurrent.futures
import traceback

from code_generation_core_agent.agents.tools.output_cleaner import OutputCleaner

from .base_tool_interface import BaseToolInterface
from .shared_loop import shared_loop
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from ...config import config

SHELL_CMD_DEFAULT_TIMEOUT = config.getint("TOOLS", "shell_command_timeout")
def _preprocess_command(command):
    """
    Preprocesses the command before execution.
    Steps:
        - Remove nohup command if present. We need to manage the background processes ourselves.
    return:
        - Preprocessed command
        - is_background: True if the command is to be run in the background, False otherwise
    """
    command = command.strip()

    command = command.replace("nohup ", "")

    is_background = command.endswith('&')

    return command, is_background


class VisibleShellTools(BaseToolInterface):
    def get_tool_name(self):
        return "VisibleShellTools"

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):

        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.processes = {}
        self.next_process_id = 1
        self.cleaner = OutputCleaner()
        self.logger = logger
        self.executor = executor

        self.output_callback = callback_functions.get('terminal_output_callback', None)

    @register_function(
        description=(
                "Executes a quick, non-blocking bash shell command and captures the output.\n"
                "IMPORTANT: Use ONLY for fast commands that likely complete in the specified timeout.\n"
                "This function will timeout after "  + str(config.getint("TOOLS", "shell_command_timeout")) +" seconds.\n"
                "Examples of appropriate commands:\n"
                "  - ls, pwd, echo, cat (small files)\n"
                "  - quick database queries\n"
                "  - git status, git log (for small repos)"
                "  - Installations and initial setup.n"
                "  - mv , cp (for small files)\n"
                "DO NOT USE for any command that:\n"
                    "  - Starts a service or server\n"
                    "  - Runs continuously or in the background\n"
                    "  - Takes more than a few seconds to complete\n"
                    "  - Requires user interaction\n"
                "Examples of INAPPROPRIATE commands:\n"
                "  - npm start, top\n"
                "  - redis-cli monitor\n"
                "  - long-running scripts or data processing tasks"
        ),
        parameters={
            "type": "object",
            "properties": {
                "command": {"type": "string", "description": "The quick shell command to execute."},
                "working_directory": {"type": "string",
                                      "description": "The fully qualified path name of the working directory for the "
                                                     "command. Should be a sub-directory of the workspace."
                                      }
            },
            "required": ["command", "working_directory"]
        },
        validator="shell_tools.yaml"
    )
    def execute_immediate_return_command(self, command, working_directory):
        async def _one_shot_docker_command(timeout):
            self.logger.info(f"Executing one-shot command: {command}")
            stdout_data, return_code = await self.executor.create_subprocess_shell_one_shot(command, timeout=timeout,
                                                                                            work_directory=working_directory)

            return stdout_data, return_code

        default_timeout = SHELL_CMD_DEFAULT_TIMEOUT

        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command(default_timeout))

            # We subtract 1 second from the timeout
            # The _one_shot_docker_command will also eventually timeout, but it will return a successful result
            # We always want to return a timeout error in this case
            # The shared_loop.run_coroutine will be released 1s after the future is done
            output, returncode = future.result(timeout=default_timeout - 1)

            cleaned_output = f"{self.cleaner.clean(output)}\n"

            if self.output_callback:
                self.output_callback(cleaned_output)

            return {"status": "SUCCESS", "output": output, "returncode": returncode}
        except Exception as e:
            return {"status": "ERROR", "message": f"Command execution failed with exception {type(e).__name__} : {e}"}

    
        
    @register_function(
        description=(
            "Starts a long-running or background process in a bash shell.\n"
            "IMPORTANT: This function is designed for commands that don't complete quickly.\n"
            "USE THIS for:\n"
            "  - Starting servicesa (e.g., 'npm start', 'python manage.py runserver')\n"
            "  - Long-running monitoring commands (e.g., 'redis-cli monitor', 'tail -f log.txt')\n"
            "  - Any command that needs to run in the background or requires user interaction\n"
            "Returns a status and the PID of the shell process, which can be used with send_input() or terminate_process().\n"
            "If the status is RUNNING examine the output received thus far to see if it is sufficient prior to requesting further output.\n"
            "After starting a long-running process you MUST later cleanup using the terminate_process command.\n"
            "DO NOT USE for quick, one-shot commands that complete rapidly (use execute_quick_command instead)."
        ),
        parameters={
            "type": "object",
            "properties": {
                "command": {"type": "string", "description": "The shell command to execute."},
                "working_directory": {"type": "string",
                                      "description": "The fully qualified path name of the working directory for the "
                                                     "command. Should be a sub-directory of the workspace."
                                      }
            },
            "required": ["command", "working_directory"]
        },
        validator="shell_tools.yaml"
    )
    def start_long_running_process(self, command, working_directory):
        timeout = 180
        try:
            future = shared_loop.run_coroutine(self._async_execute_command(command, working_directory))
            try:
                result = future.result(timeout=timeout)
                return result
            except concurrent.futures.TimeoutError:
                return {"status": "TIMEOUT", "message": f"Command execution timed out after {timeout} seconds"}
            except Exception as e:
                self.logger.error(f"Error while waiting for future result: {type(e).__name__}: {str(e)}")
                return {"status": "ERROR",
                        "message": f"Error while waiting for future result: {type(e).__name__}: {str(e)}"}
        except Exception as e:
            return {"status": "ERROR", "message": f"Command execution failed: {type(e).__name__}: {str(e)}"}

    async def _async_execute_command(self, command, working_directory):
        try:
            command, is_background = _preprocess_command(command)

            process = await self.executor.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                work_directory=working_directory,
                is_background=is_background
            )

            pid = process.pid
            self.processes[pid] = {"process": process, "output": ""}

            timeout = SHELL_CMD_DEFAULT_TIMEOUT

            try:
                # Read will be executed until timeout or until there is a responsive shell
                combined_std_out_err_output, partial = process.stdout.read_with_partial(timeout=timeout)
                output = combined_std_out_err_output.decode()
                if partial:
                    status = "RUNNING"
                else:
                    status = "COMPLETED"
            except asyncio.TimeoutError:
                self.logger.warning(f"Command execution timed out for PID: {pid}")
                output = self.processes[pid]["output"]
                status = "RUNNING"  # Changed from TIMEOUT to RUNNING for npm start

            cleaned_output = f"{self.cleaner.clean(output, False)}\n"
            if self.output_callback:
                self.output_callback(cleaned_output)

            return {
                "status": status,
                "pid": pid,
                "output": cleaned_output,
                "returncode": process.returncode if status != "RUNNING" else None
            }
        except Exception as e:
            return {"status": "ERROR", "message": f"Command execution failed: {type(e).__name__}: {str(e)}"}

    @staticmethod
    async def _capture_output(process):
        output, partial = process.stdout.read_with_partial()
        return output

    @register_function(
        description="Sends input command to a shell previously started.",
        parameters={
            "type": "object",
            "properties": {
                "pid": {"type": "integer", "description": "The process ID of the shell."},
                "command": {"type": "string", "description": "The command to send."},
            },
            "required": ["pid", "command"]
        }
    )
    def send_input(self, pid, command):
        if pid not in self.processes:
            return {"status": "ERROR", "message": f"No process found with PID {pid}"}

        process = self.processes[pid]["process"]
        process.stdin.write(command.encode())
        process.stdin.flush()

        future = shared_loop.run_coroutine(self._wait_and_capture(pid))
        result = future.result()

        if self.output_callback:
            self.output_callback(result["output"])

        return result

    async def _wait_and_capture(self, pid, timeout=10):
        process = self.processes[pid]["process"]
        try:
            output = await self.executor.wait_for(self._capture_output(process), timeout=timeout)
            status = "COMPLETED" if process.returncode is not None else "RUNNING"
        except asyncio.TimeoutError:
            output = self.processes[pid]["output"]
            status = "RUNNING"

        cleaned_output = self.cleaner.clean(output.decode())
        return {"status": status, "output": cleaned_output}

    @register_function(
        description="Captures the current output of a running process.",
        parameters={
            "type": "object",
            "properties": {
                "pid": {"type": "integer", "description": "The process ID."},
            },
            "required": ["pid"]
        }
    )
    def shell_capture_output(self, pid):
        if pid not in self.processes:
            return {"status": "ERROR", "message": f"No process found with PID {pid}"}

        future = shared_loop.run_coroutine(self._wait_and_capture(pid))
        return future.result()

    @register_function(
        description="Terminates a running process.",
        parameters={
            "type": "object",
            "properties": {
                "pid": {"type": "integer", "description": "The process ID."},
            },
            "required": ["pid"]
        }
    )
    def terminate_process(self, pid):
        if pid not in self.processes:
            return {"status": "ERROR", "message": f"No process found with PID {pid}"}

        process = self.processes[pid]["process"]
        try:
            shared_loop.run_coroutine(process.terminate()).result(timeout=5)

            future = shared_loop.run_coroutine(process.wait())
            exit_code, output = future.result(timeout=5)  # Wait for up to 5 seconds for the process to terminate

            output = self.processes[pid]["output"] + output
            cleaned_output = self.cleaner.clean(output)
            if self.output_callback:
                self.output_callback(cleaned_output)

            del self.processes[pid]
            return {"status": "TERMINATED", "output": cleaned_output}
        except ProcessLookupError:
            self.logger.warning(f"Process with PID {pid} has already terminated.")
            del self.processes[pid]
            return {"status": "ALREADY_TERMINATED", "message": f"Process with PID {pid} had already terminated."}
        except asyncio.TimeoutError:
            self.logger.warning(f"Timeout while waiting for process with PID {pid} to terminate.")
            return {"status": "TERMINATION_TIMEOUT", "message": f"Timeout while terminating process with PID {pid}."}
        except Exception as e:
            # Print complete backtrace for the error
            backtrace = traceback.format_exc()
            self.logger.error(f"Error terminating process with PID {pid}: {str(e)}")
            return {"status": "ERROR", "message": f"Error terminating process: {str(e)}"}

    def __del__(self):
        for pid in list(self.processes.keys()):
            self.terminate_process(pid)
