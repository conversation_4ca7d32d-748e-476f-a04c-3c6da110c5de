import abc
import asyncio

class ExecutorBase(abc.ABC):
    @abc.abstractmethod
    async def create_subprocess_shell(self, cmd, **kwargs):
        pass

    @abc.abstractmethod
    async def terminate_process(self, process):
        pass

    @abc.abstractmethod
    def run_one_shot_command(self, cmd, timeout=60) -> (str, int):
        """
        Run one shot command and return the output and return code.
        """
        pass

    @abc.abstractmethod
    def create_task(self, task):
        pass

    @abc.abstractmethod
    def background_startup(self):
        """
        Method is used for background startup
        """
        pass

    @abc.abstractmethod
    async def wait(self, fs, timeout=None):
        pass

    @abc.abstractmethod
    async def gather(self, *futures):
        pass

    @abc.abstractmethod
    def run_coroutine_threadsafe(self, coro, loop):
        pass

    @abc.abstractmethod
    def wait_for(self, coro, timeout=None):
        pass

    @abc.abstractmethod
    def append_env_variables(self, env_variables : dict[str, str]):
        pass
    def is_running(self) -> bool:
        return False

