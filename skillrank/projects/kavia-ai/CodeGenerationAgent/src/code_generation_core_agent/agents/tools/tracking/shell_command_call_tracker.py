import os
import json
import threading
from dataclasses import dataclass, asdict
from typing import List, Dict


@dataclass
class FunctionCallDescription:
    time: int
    function_name: str
    arguments: str
    reason: str
    observations: str
    root_cause_analysis: str
    data: str = ""
    is_error: bool = False


_lock = threading.Lock()


class ShellCommandCallTracker:
    """Tracker for shell command calls."""
    _tracked_commands = ("start_long_running_process", "send_input", "start_long_running_process", "execute_immediate_return_command")

    def __init__(self, base_path):
        self.base_path = base_path
        self.history_file = os.path.join(base_path, ".function_call_history")
        self._call_history = self._load_history()
        self._pending_calls: Dict[str, FunctionCallDescription] = {}

    def _load_history(self) -> List[FunctionCallDescription]:
        with _lock:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r') as f:
                    data = json.load(f)
                    return [FunctionCallDescription(**call) for call in data]
            return []

    def _save_history(self):
        with _lock:
            with open(self.history_file, 'w') as f:
                json.dump([asdict(call) for call in self._call_history], f, indent=2)

    def _is_tracked(self, function_name: str) -> bool:
        return function_name in self._tracked_commands

    @staticmethod
    def _call_hash(function_name: str, arguments: str) -> str:
        return f"{function_name}_{arguments}"

    def trace_function_call(self, time: int, function_name: str, arguments: str, reason: str,
                            observations: str, root_cause_analysis: str):
        if not self._is_tracked(function_name):
            return

        self._pending_calls[self._call_hash(function_name, arguments)] = FunctionCallDescription(
            time, function_name, arguments, reason, observations, root_cause_analysis)

    def trace_function_return(self, function_name: str, arguments: str, is_error: bool, data: str):
        if not self._is_tracked(function_name):
            return

        call_hash = self._call_hash(function_name, arguments)
        if call_hash in self._pending_calls:
            call = self._pending_calls.pop(call_hash)
            call.data = data
            call.is_error = is_error
            self._call_history.append(call)
            self._save_history()

    def asJson(self):
        self._load_history()
        return [asdict(call) for call in self._call_history]
