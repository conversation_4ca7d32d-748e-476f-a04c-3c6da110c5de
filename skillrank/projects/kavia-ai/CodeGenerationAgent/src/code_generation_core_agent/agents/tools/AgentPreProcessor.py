import copy
import time
from collections import OrderedDict

from code_generation_core_agent.agents.tools.combined_tools import ToolPreProcessor
from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator
from code_generation_core_agent.config import config
from code_generation_core_agent.agents.tools.tracking.shell_command_call_tracker import ShellCommandCallTracker

PRINT_REFLECTION = False


class FunctionCallDescription:
    """
    Class to store the function call and its description.
    """
    function_name: str = ""
    reason: str = ""
    observations: str = ""
    root_cause_analysis: str = ""
    arguments: OrderedDict = {}


class AgentPreProcessor(ToolPreProcessor):
    """
    Pre-processor for tool calls.

    This class is responsible for:
    * Extracting information from the function call, like reason, observations, and root cause analysis.
    * Logging the extracted information.
    * post-processing the function response to truncate the output if it is too long.
    * manage memory shards
    """

    def __init__(self, callback_functions, logger, shell_command_tracker: ShellCommandCallTracker):

        super().__init__()
        self.logger = logger
        self.callback = callback_functions['function_call_callback']
        self.output_truncator = OutputTruncator(config.getint("TOOLS", "return_size_limit"), logger=logger)
        self.shell_command_call_tracker = shell_command_tracker

    def pre_process_function_call(self, function_name, **arguments):

        reason: str = arguments.pop('reason', "")
        observations: str = arguments.pop('observations', "")
        root_cause_analysis: str = arguments.pop('root_cause_analysis', "")
        current_time_ms: int = int(time.time() * 1000)

        self.shell_command_call_tracker.trace_function_call(current_time_ms,
                                                            function_name, str(arguments), reason, observations,
                                                            root_cause_analysis)
        if PRINT_REFLECTION:
            call_reason_reflection = arguments.pop('call_reason_reflection', None)
            print(f"Reflection of the call {function_name}: ", call_reason_reflection)

        f_desc = FunctionCallDescription()

        f_desc.function_name = function_name
        f_desc.reason = reason
        f_desc.observations = observations
        f_desc.arguments = arguments
        # Log the extracted information
        self.logger.info(f"Function: {function_name}")
        self.logger.info(f"Reason: {reason}")
        self.logger.info(f"Observations: {observations}")

        if root_cause_analysis:
            self.logger.info(f"Root Cause Analysis: {root_cause_analysis}")
            f_desc.root_cause_analysis = root_cause_analysis

        if self.callback:
            self.callback(f_desc)

        return arguments

    def _not_function_response(self, function_name, arguments, return_data):
        """
        Function to log the response of a function that is not a tool.
        """

        if isinstance(return_data, dict) and 'status' in return_data and return_data['status'].lower() in ['failure', 'error']:
            is_error = True
        else:
            is_error = False

        self.shell_command_call_tracker.trace_function_return(function_name, str(arguments), is_error, "")

    def post_process_function_response(self, function_name, arguments, data):

        self._not_function_response(function_name, arguments, data)

        message_types_to_truncate = ('message', 'output')
        # Check if data is a dictionary
        if not isinstance(data, dict):
            return data

        for key, value in data.items():
            if key in message_types_to_truncate:
                data[key] = self.output_truncator.truncate_output(value)

        return data

    def pre_process_function_registration(self, data):
        new_schema = copy.deepcopy(data)

        new_params = {
            "reason": {
                "type": "string",
                "description": "Explain the reason for this function call and the changes being made"

            },
            "observations": {
                "type": "string",
                "description": "Observations on the current state, especially if there were previous failures,"
                               "your observations should be specific to the failure"
            },
            "root_cause_analysis": {
                "type": "string",
                "description": "Root cause analysis for previous failures and paths t"
                               "o pursue to address those issues. Return empty string if not applicable"
            }
        }

        if PRINT_REFLECTION:
            new_params['call_reason_reflection'] = {
                "type": "string",
                "description": "Quote the section of the system prompt that affected the decision to call this function."
                               "MUST: this string can be only matching string from the  \"role\": \"system\" section!."
                               "This is a debugging variable to help to understand what triggered this function call."
                               "This should not contain the description of the task but the reflection on the system prompt most relevant to the function call."
            }

        # Add new parameters to the existing ones
        if 'parameters' in new_schema['function']:
            new_schema['function']['parameters']['properties'].update(new_params)
        else:
            new_schema['function']['parameters'] = {
                "type": "object",
                "properties": new_params
            }

        # Ensure all new parameters are required
        #if 'required' not in new_schema['function']['parameters']:
        #    new_schema['function']['parameters']['required'] = []
        #new_schema['function']['parameters']['required'].extend(new_params.keys())

        return new_schema
