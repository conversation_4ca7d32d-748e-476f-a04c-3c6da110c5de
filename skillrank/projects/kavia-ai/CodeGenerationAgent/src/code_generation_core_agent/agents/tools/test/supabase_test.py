"""
Integration test for SupabaseTool against a real Supabase project.

Run:
    python integration_test_supabase_tool.py
    pytest -q integration_test_supabase_tool.py
"""

import json
import logging
import os
import time
from importlib import import_module

import pytest
from dotenv import load_dotenv

# --------------------------------------------------------------------------- #
#  Load credentials from the environment                                     #
# --------------------------------------------------------------------------- #
load_dotenv()  # SUPABASE_URL / SUPABASE_KEY (+ optional TEST_SECRET_PASSPHRASE)
_SUPABASE_URL = os.getenv("SUPABASE_URL")
_SUPABASE_KEY = os.getenv("SUPABASE_KEY")

if not _SUPABASE_URL or not _SUPABASE_KEY:
    raise EnvironmentError(
        "Both SUPABASE_URL and SUPABASE_KEY must be set for the integration test."
    )

PASSPHRASE = os.getenv("TEST_SECRET_PASSPHRASE", "supabase_test_passphrase")

# --------------------------------------------------------------------------- #
#  Import the tool module (after env-vars are ensured)                        #
# --------------------------------------------------------------------------- #
supabase_tool_mod = import_module(
    "code_generation_core_agent.agents.tools.supabase_tool"
)
SupabaseTool = supabase_tool_mod.SupabaseTool


def _build_tool():
    """Helper to instantiate the SupabaseTool with a quiet logger."""
    logger = logging.getLogger("integration_supabase_tool")
    logger.setLevel(logging.ERROR)
    return SupabaseTool(
        callback_functions=None,
        base_path="",
        logger=logger,
        llm=None,
        executor=None,
    )


# --------------------------------------------------------------------------- #
#  Helper assertions                                                          #
# --------------------------------------------------------------------------- #
def _assert_ok(res, msg):
    assert res["status"] == "SUCCESS", f"{msg} failed → {json.dumps(res, indent=2)}"


# --------------------------------------------------------------------------- #
#  End-to-end test                                                            #
# --------------------------------------------------------------------------- #
def test_supabase_live_roundtrip():
    sb_tool = _build_tool()

    # 0) connect_to_project ---------------------------------------------------
    connect_res = sb_tool.connect_to_project(_SUPABASE_URL, _SUPABASE_KEY)
    _assert_ok(connect_res, "connect_to_project")

    # 0.1) ensure pgcrypto extension exists ----------------------------------
    ext_check = sb_tool.run_sql(
        "SELECT 1 AS ok FROM pg_extension WHERE extname = 'pgcrypto' LIMIT 1"
    )
    _assert_ok(ext_check, "pgcrypto extension query")

    if not ext_check["data"]:            # no rows → extension missing
        pytest.skip(
            "pgcrypto extension is not installed in this database – "
            "skip secret-store related assertions."
        )

    # ------------------------------------------------------------------ #
    #  A0.  VERIFY pgcrypto IS CALLABLE                                  #
    # ------------------------------------------------------------------ #
    crypto_check = sb_tool.run_sql(
        # succeeds only if pgp_sym_encrypt is reachable in search_path
        "SELECT exists ("
        "  SELECT 1 FROM pg_proc "
        "  WHERE proname = 'pgp_sym_encrypt'"
        ");"
    )
    _assert_ok(crypto_check, "pgcrypto availability query")

    if not crypto_check["data"] or not crypto_check["data"][0]["exists"]:
        pytest.skip(
            "pgcrypto (pgp_sym_encrypt) not found in this database—"
            " skipping secret-store assertions."
        )

    # ------------------------------------------------------------------ #
    #  A.  AUTHENTICATION FLOW                                           #
    # ------------------------------------------------------------------ #
    # use a unique address each run so sign-up never clashes
    epoch = int(time.time())
    email = f"<EMAIL>"
    password = "SupaSecret123!"

    #signup = sb_tool.auth_sign_up(email=email, password=password)
    #_assert_ok(signup, "auth_sign_up")

    signin = sb_tool.auth_sign_in(email=email, password=password)
    _assert_ok(signin, "auth_sign_in")

    user = sb_tool.auth_get_user()
    _assert_ok(user, "auth_get_user")
    # supabase-py v2 returns .user attr; here it's wrapped in .data
    assert user["data"]["user"]["email"] == email

    signout = sb_tool.auth_sign_out()
    _assert_ok(signout, "auth_sign_out")

    # ------------------------------------------------------------------ #
    #  B.  SECRET STORE FLOW                                             #
    # ------------------------------------------------------------------ #
    # 1) ensure table exists (idempotent)
    secret_store = sb_tool.create_secret_store()
    #_assert_ok(secret_store, "create_secret_store")

    # 2) upsert + read + delete
    secret_key = f"INTEGRATION_TEST_SECRET_{epoch}"
    secret_val = "top-secret-value"

    set_res = sb_tool.set_secret(
        key=secret_key, value=secret_val, passphrase=PASSPHRASE
    )
    _assert_ok(set_res, "set_secret")

    get_res = sb_tool.get_secret(key=secret_key, passphrase=PASSPHRASE)
    _assert_ok(get_res, "get_secret")
    assert get_res["secret"] == secret_val

    del_res = sb_tool.delete_secret(key=secret_key)
    _assert_ok(del_res, "delete_secret")

    # ------------------------------------------------------------------ #
    #  C.  CLASSIC CRUD FLOW (unchanged)                                 #
    # ------------------------------------------------------------------ #
    scratch_table = f"test_tool_{epoch}"

    before = sb_tool.list_tables()
    _assert_ok(before, "list_tables (baseline)")
    assert scratch_table not in before.get("tables", [])

    create = sb_tool.create_table(
        scratch_table,
        [
            {"name": "id", "type": "serial", "nullable": False},
            {"name": "name", "type": "text"},
            {"name": "created_at", "type": "timestamp", "default": "now()"},
        ],
    )
    _assert_ok(create, "create_table")

    after = sb_tool.list_tables()
    _assert_ok(after, "list_tables (post-create)")
    assert scratch_table in after["tables"]

    schema = sb_tool.get_table_schema(scratch_table)
    _assert_ok(schema, "get_table_schema")
    assert [c["name"] for c in schema["columns"]] == ["id", "name", "created_at"]

    insert_res = sb_tool.insert_rows(
        scratch_table, rows=[{"name": "alice"}]
    )
    _assert_ok(insert_res, "insert_rows")
    row_id = insert_res["data"][0]["id"]

    select_res = sb_tool.select_rows(
        scratch_table,
        filters=[{"column": "id", "operator": "eq", "value": row_id}],
        limit=1,
    )
    _assert_ok(select_res, "select_rows")
    assert select_res["data"][0]["name"] == "alice"

    drop = sb_tool.drop_table(scratch_table)
    _assert_ok(drop, "drop_table")


# --------------------------------------------------------------------------- #
#  Optional CLI runner                                                        #
# --------------------------------------------------------------------------- #
if __name__ == "__main__":
    try:
        test_supabase_live_roundtrip()
        print("✅  SupabaseTool live integration test PASSED")
    except AssertionError as e:
        print("❌  SupabaseTool live integration test FAILED")
        raise