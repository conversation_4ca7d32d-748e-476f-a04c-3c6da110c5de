import base64
import concurrent.futures
import os
import shlex
import json
import base64
from typing import <PERSON>ple, Optional
from PIL import Image
import io

from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator, SummarizerType
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
from code_generation_core_agent.config import config

MAX_CLI_LENGTH = 100 * 1024

# SHELL_CMD_DEFAULT_TIMEOUT
# Type: integer
# Default timeout for shell commands
SHELL_CMD_DEFAULT_TIMEOUT = config.getint("TOOLS", "shell_command_timeout")

# APPEND_DIRECTORY_TREE_TO_ERROR_RESPONSE
# Type: boolean
# Append directory tree to error response
# This is disabled by default because it can be very large
# exceed the allowed llm response size

APPEND_DIRECTORY_TREE_TO_ERROR_RESPONSE = False

class DockerFileToolsError(Exception):
    """Custom exception for DockerFileTools errors."""
    pass


class ContainerReadTool(BaseToolInterface):
    def get_tool_name(self):
        return "ContainerReadTool"

    @staticmethod
    def _find_file(base_folder, target_filename):
        for root, dirs, files in os.walk(base_folder):
            if target_filename in files:
                return os.path.join(root, target_filename)
        return None

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.output_callback = callback_functions.get("terminal_output_callback", None)
        self.codeview_callback = callback_functions.get("codeview_callback", None)
        self.agent_messsage_callback = callback_functions.get("agent_message_callback")
        self.executor = executor
        self.logger = logger
        self.summarizer = OutputTruncator(1024, summarizer_type=SummarizerType.TOOL_OUTPUT_SUMMARIZER)

    def set_output_callback(self, callback):
        self.output_callback = callback

    def _generate_directory_tree(self, startpath=None):
        if startpath is None:
            startpath = self.base_path
        tree = []
        for root, dirs, files in os.walk(startpath):
            level = root.replace(startpath, '').count(os.sep)
            indent = '|   ' * (level - 1) + '+-- '
            tree.append(f"{indent}{os.path.basename(root)}/")
            for file in files:
                sub_indent = '|   ' * level + '+-- '
                tree.append(f"{sub_indent}{file}")
        return '\n'.join(tree)

    def validate_build(self):
        """
        Validate that the edit generated working code that we can build.
        First tries to execute .init.linter.sh if it exists, otherwise falls back to .init/.init-build-cmd
        """
        linter_path = os.path.join(self.base_path, ".init/.linter.sh")
        if os.path.exists(linter_path):
            try:
                result = self._run_docker_command(f'bash {linter_path}')
                return True, ""
            except DockerFileToolsError as e:
                return False, str(e)

        # Fall back to original build validation if no linter script exists
        try:
            with open(os.path.join(self.base_path, ".init/.init-build-cmd")) as f:
                bconfig = json.load(f)
                command = bconfig["command"]
                working_directory = bconfig["working_directory"]

                self._run_docker_command(f'mkdir -p {working_directory}')
                try:
                    result = self._run_docker_command(command, work_directory=working_directory)
                except DockerFileToolsError as e:
                    return False, str(e)
                return True, ""

        except Exception as e:
            # Ignore it for now
            return True, ""

    @staticmethod
    def _generate_folder_structure(base_folder):
        def folder_to_dict(folder_path):
            # Get list of items in the folder
            items = os.listdir(folder_path)

            # If folder has more than 20 items, return just the folder name
            if len(items) > 20:
                return os.path.basename(folder_path)

            # Otherwise, build a dictionary representing the folder contents
            folder_dict = {}
            for item in items:
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path):
                    # Recursively process sub-folders
                    folder_dict[item] = folder_to_dict(item_path)
                else:
                    # Add file names as dictionary keys with None as their value
                    folder_dict[item] = None
            return folder_dict

        # Generate the folder structure dictionary
        folder_structure = folder_to_dict(base_folder)

        # Convert the dictionary to a JSON object
        folder_structure_json = json.dumps(folder_structure, indent=4)

        return folder_structure_json

    def _update_codeview(self, full_path):
        self.read_file(full_path)

    def _error_response_with_tree(self, status, message):

        directory_tree = self._generate_folder_structure(self.base_path)

        result = {"status": status, "message": message}

        if APPEND_DIRECTORY_TREE_TO_ERROR_RESPONSE:
            result["directory_tree"] = directory_tree

        return result

    def _send_output(self, message):
        if self.output_callback:
            self.output_callback(message)

    def _run_docker_command(self, cmd, work_directory=None):
        async def _one_shot_docker_command(wdir):
            stdout_data, return_code = await self.executor.create_subprocess_shell_one_shot(cmd, work_directory=wdir)
            return stdout_data, return_code

        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command(work_directory))
            output, returncode = future.result(timeout=SHELL_CMD_DEFAULT_TIMEOUT)
            if returncode != 0:
                raise DockerFileToolsError(f"Command {cmd} failed with {returncode}. Error: ```{output}```")
            return output
        except concurrent.futures.TimeoutError:
            raise DockerFileToolsError(f"Docker command execution timed out"
                                       f" after {SHELL_CMD_DEFAULT_TIMEOUT} seconds")
        except DockerFileToolsError:
            raise
        except Exception as e:
            raise DockerFileToolsError(f"Error executing Docker command. Error: {str(e)}")

    def _execute_operation(self, operation, *args, **kwargs):
        try:
            result = operation(*args, **kwargs)
            return {"status": "SUCCESS", **result}
        except DockerFileToolsError as e:
            self._send_output(f"Error: {str(e)}\n")
            return {"status": "ERROR", "message": str(e)}
        except Exception as e:
            self._send_output(f"Unexpected error: {str(e)}\n")
            return {"status": "ERROR", "message": f"An unexpected error occurred: {str(e)}"}

    @staticmethod
    def _get_full_path(base_path, file_path):
        normalized_path = os.path.normpath(file_path)
        if normalized_path.startswith('..'):
            raise ValueError("Access to parent directories is not allowed")
        return os.path.join(base_path, normalized_path)


    def read_file(self, file_path):
        try:
            self.logger.info(f"Reading file {file_path}")
            full_path = self._get_full_path(self.base_path, file_path)
            escaped_path = shlex.quote(full_path)
            cmd = f"cat {escaped_path}"

            self.logger.info(f"Executing command: {cmd}")

            output = self._run_docker_command(cmd)
            content = output
            lines = content.splitlines()
            size = len(content)
            line_count = len(lines)

            if self.codeview_callback:
                self.codeview_callback({
                    "filename": full_path,
                    "content": content,
                    "operation": "read",
                    "color_info": {
                        "ranges": [(0, len(content))]  # Entire content is marked as read
                    }
                })

            self.logger.info(f"Read {size} bytes from file {file_path}. send output")
            self._send_output(f"Reading {size} bytes from file {file_path}\n")
            self.logger.info(f"Returning content")

            result = {
                "status": "SUCCESS",
                "path": full_path,
                "content": content,
                "lines_read": line_count,
                "is_full_content_loaded": True
            }

            return result

        except ValueError as e:
            return self._error_response_with_tree("ERROR", str(e))
        except Exception as e:
            import traceback
            traceback.print_exc()
            if "No such file or directory" in str(e):
                guess = self._find_file(self.base_path, os.path.basename(full_path))
                if guess is not None:
                    return {"status": "ERROR", "message": f"File not found: {full_path}. Do you mean {guess}?"}
                else:
                    return self._error_response_with_tree("ERROR", f"File not found: {full_path}")
            elif "Is a directory" in str(e):
                return self._error_response_with_tree("ERROR",
                                                      f"Path is a directory: {full_path}. You can only read files")
            else:
                self._send_output(f"Error reading file {file_path}: {str(e)}\n")
                return self._error_response_with_tree("ERROR", f"Error reading file {file_path}: {str(e)}")

    @register_function(
        description="Reads the contents of one or multiple files. "
                    "Use this when the file is not already pre-loaded in the prompt. "
                    "Specify as many files you wish. "
                    "Example: ContainerReadTool_read_files(file_paths=['/tmp/file1.txt', '/tmp/file2.txt'])",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_paths": {"type": "array", "items": {"type": "string"},
                               "description": "List of fully qualified file paths."},
            },
            "required": ["file_paths"]
        }
    )
    def read_files(self, file_paths):
        if not file_paths:
            return self._error_response_with_tree("ERROR", "No file paths provided")

        results = []
        has_errors = False

        for path in file_paths:
            result = self.read_file(path)
            results.append(result)
            if result["status"] == "ERROR":
                has_errors = True

        return {
            "status": "SUCCESS" if not has_errors else "PARTIAL_SUCCESS",
            "results": results
        }
    
    @staticmethod
    def is_image_file(file_path: str) -> bool:
        """
        Checks if the given file path points to a valid image file.
        Args:
            file_path (str): The path to the file to check.
        Returns:
            bool: True if the file is an image, False otherwise.
        """
        try:
            with Image.open(file_path) as img:
                return img.format in ['JPEG', 'PNG', 'GIF', 'BMP', 'TIFF']
        except Exception:
            return False



    @register_function(
        description=("Reads the contents of a document file and returns the text. "
                     "This function call handles conversion of PDF and other files to text."
                     "Use this when the file is not already pre-loaded in the prompt. "
                     "Specify the relative file path to the file. "
                     "Example: ContainerReadTool_read_document(file_path='test.pdf')"),
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "Relative path to the file from base_directory."
                },
                "start_line": {
                    "type": "integer",
                    "description": "Starting line number to read from the file. Default is 0.",
                }
            },
            "required": ["file_path"]
        }
    )
    def read_document(self, file_path, start_line=0):
        """
        Reads the contents of a document file and returns the text.
        Handles conversion of PDF, Excel, and other document formats to text.

        Args:
            file_path (str): Relative path to the document file
            start_line (int): Starting line number to read from (default: 0)

        Returns:
            dict: Structured response with status, path, content, and metadata
        """
        MAX_DOCUMENT_LINES = 10000  # Global constant for maximum lines to return

        try:
            self.logger.info(f"Reading document {file_path}")
            full_path = self._get_full_path(self.base_path, file_path)

            # Check if file exists
            if not os.path.exists(full_path):
                guess = self._find_file(self.base_path, os.path.basename(full_path))
                if guess is not None:
                    return {"status": "ERROR", "message": f"File not found: {full_path}. Do you mean {guess}?"}
                else:
                    return self._error_response_with_tree("ERROR", f"File not found: {full_path}")

            # Get file extension
            _, ext = os.path.splitext(file_path.lower())

            content = ""

            # Handle different file formats using Python libraries
            try:
                if ext == '.pdf':
                    # Try pypdf (modern fork of PyPDF2) first
                    try:
                        import pypdf
                        with open(full_path, 'rb') as file:
                            pdf_reader = pypdf.PdfReader(file)
                            text_parts = []
                            for page_num in range(len(pdf_reader.pages)):
                                page = pdf_reader.pages[page_num]
                                text_parts.append(page.extract_text())
                            content = '\n'.join(text_parts)
                    except Exception as e:
                        # Fallback to pdfplumber for better table extraction
                        import pdfplumber
                        text_parts = []
                        with pdfplumber.open(full_path) as pdf:
                            for page in pdf.pages:
                                page_text = page.extract_text()
                                if page_text:
                                    text_parts.append(page_text)
                        content = '\n'.join(text_parts)

                elif ext in ['.xlsx', '.xls']:
                    # Handle Excel files - convert to CSV format
                    import pandas as pd

                    if ext == '.xlsx':
                        excel_file = pd.ExcelFile(full_path, engine='openpyxl')
                    else:
                        excel_file = pd.ExcelFile(full_path, engine='xlrd')

                    output_parts = []
                    for sheet_name in excel_file.sheet_names:
                        df = pd.read_excel(excel_file, sheet_name=sheet_name)
                        output_parts.append(f'### Sheet: {sheet_name}')
                        output_parts.append(df.to_csv(index=False))
                        output_parts.append('')

                    content = '\n'.join(output_parts)

                elif ext == '.csv':
                    # For CSV files, just read them directly
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as file:
                        content = file.read()

                elif ext == '.docx':
                    # Handle Word documents
                    import docx
                    doc = docx.Document(full_path)
                    paragraphs = []
                    for paragraph in doc.paragraphs:
                        if paragraph.text.strip():
                            paragraphs.append(paragraph.text)
                    content = '\n'.join(paragraphs)

                elif ext == '.doc':
                    # For older .doc files, try python-docx2txt
                    try:
                        import doc2docx
                        import tempfile
                        import docx

                        # Convert .doc to .docx first
                        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp:
                            tmp_path = tmp.name

                        doc2docx.convert(full_path, tmp_path)
                        doc = docx.Document(tmp_path)
                        paragraphs = []
                        for paragraph in doc.paragraphs:
                            if paragraph.text.strip():
                                paragraphs.append(paragraph.text)
                        content = '\n'.join(paragraphs)
                        os.unlink(tmp_path)
                    except Exception:
                        # Fallback to textract if available
                        import textract
                        content = textract.process(full_path).decode('utf-8', errors='ignore')

                elif ext in ['.txt', '.md', '.log', '.json', '.xml', '.yaml', '.yml']:
                    # Plain text files - just read them
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as file:
                        content = file.read()

                elif ext == '.rtf':
                    # Handle RTF files
                    from striprtf.striprtf import rtf_to_text
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as file:
                        rtf_content = file.read()
                    content = rtf_to_text(rtf_content)

                elif ext == '.odt':
                    # Handle OpenDocument Text files
                    from odf import text, teletype
                    from odf.opendocument import load

                    doc = load(full_path)
                    paragraphs = []
                    for element in doc.getElementsByType(text.P):
                        paragraphs.append(teletype.extractText(element))
                    content = '\n'.join(paragraphs)

                elif ext == '.html' or ext == '.htm':
                    # Handle HTML files
                    from bs4 import BeautifulSoup
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as file:
                        html_content = file.read()
                    soup = BeautifulSoup(html_content, 'html.parser')
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()
                    content = soup.get_text()
                    # Clean up whitespace
                    lines = (line.strip() for line in content.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    content = '\n'.join(chunk for chunk in chunks if chunk)

                else:
                    # For unknown file types, try to read as text
                    try:
                        with open(full_path, 'r', encoding='utf-8', errors='ignore') as file:
                            content = file.read()
                    except Exception:
                        return {"status": "ERROR",
                                "message": f"Unsupported file format: {ext}. Unable to read as text."}

            except ImportError as e:
                missing_lib = str(e).split("'")[1] if "'" in str(e) else "required library"
                return {"status": "ERROR",
                        "message": f"Missing Python library for {ext} files: {missing_lib}. Please install required dependencies."}
            except Exception as e:
                return {"status": "ERROR",
                        "message": f"Error reading {ext} file: {str(e)}"}

            # Split content into lines and apply limits
            lines = content.splitlines()
            total_lines = len(lines)

            # Apply start_line filtering
            if start_line > 0:
                if start_line >= total_lines:
                    return {"status": "ERROR",
                            "message": f"start_line {start_line} exceeds total lines {total_lines}"}
                lines = lines[start_line:]

            # Apply maximum line limit
            truncated = False
            if len(lines) > MAX_DOCUMENT_LINES:
                lines = lines[:MAX_DOCUMENT_LINES]
                truncated = True

            content = '\n'.join(lines)
            lines_read = len(lines)
            size = len(content)

            # Send to codeview callback if available
            if self.codeview_callback:
                self.codeview_callback({
                    "filename": full_path,
                    "content": content,
                    "operation": "read",
                    "color_info": {
                        "ranges": [(0, len(content))]
                    }
                })

            self.logger.info(f"Read {size} bytes from document {file_path}")
            self._send_output(
                f"Reading {size} bytes from document {file_path} (format: {ext}, lines: {lines_read}/{total_lines})\n")

            result = {
                "status": "SUCCESS",
                "path": full_path,
                "content": content,
                "lines_read": lines_read,
                "total_lines": total_lines,
                "format": ext,
                "is_full_content_loaded": start_line == 0 and not truncated,
                "truncated": truncated
            }

            if truncated:
                result[
                    "message"] = f"Content truncated to {MAX_DOCUMENT_LINES} lines. Total document has {total_lines} lines."

            return result

        except ValueError as e:
            return self._error_response_with_tree("ERROR", str(e))
        except Exception as e:
            import traceback
            traceback.print_exc()
            self._send_output(f"Error reading document {file_path}: {str(e)}\n")
            return self._error_response_with_tree("ERROR", f"Error reading document {file_path}: {str(e)}")

    @register_function(
        description=(
            "Reads an image file, resizes it to a smaller dimension, and returns the base64-encoded bytes as text. "
            "Use this to allow LLMs to process image content"
            "Specify the relative file path to the image and optionally the maximum size for resizing. "
            "The image will be resized to fit within the specified maximum width or height while maintaining the aspect ratio. "
            "Do not use a max_width larger than 256"
            "Example: ContainerReadTool_read_image_file(file_path='images/sample.png', max_width=128)"
        ),
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "Relative path to the image file from base_directory."
                },
                "max_width": {
                    "type": "integer",
                    "description": "Maximum width for resizing the image (default: 128).",
                    "default": 128,
                    "minimum": 1
                }
            },
            "required": ["file_path"]
        }
    )
    def read_image_file(self, file_path: str, max_width: int = 128) -> dict:
        """
        Reads an image file, resizes it to a smaller dimension, and returns the base64-encoded bytes.
        Args:
            file_path (str): Relative path to the image file within the container.
            max_width (int): Maximum width or height for resizing (default: 128).
        Returns:
            dict: Structured response with status, path, format, original and resized size, and base64 content.
        """
        try:
            max_size = max_width
            # Resolve and validate the full file path
            full_path = self._get_full_path(self.base_path, file_path)
            if not self.is_image_file(full_path):
                return self._error_response_with_tree("ERROR", f"File is not a supported image: {file_path}")

            # Open the image using Pillow
            with Image.open(full_path) as img:
                original_size = img.size
                img_format = img.format

                # Calculate new size while preserving aspect ratio
                ratio = min(max_size / original_size[0], max_size / original_size[1], 1.0)
                new_size = (int(original_size[0] * ratio), int(original_size[1] * ratio))
                if new_size != original_size:
                    img = img.resize(new_size, Image.LANCZOS)

                # Save the resized image to a bytes buffer
                buffer = io.BytesIO()
                save_format = img_format if img_format else "PNG"
                content_type = f"image/{save_format.lower()}"
                img.save(buffer, format=save_format)
                buffer.seek(0)
                image_bytes = buffer.read()

                # Encode the image bytes as base64
                base64_bytes = base64.b64encode(image_bytes).decode("utf-8")  

                content = f"data:{content_type};base64,{base64_bytes}"              

            # Prepare the response
            result =  {"status": "SUCCESS",  "image_url": {"url": content, "detail": "auto"}}
            self._send_output(f"Read and resized image {file_path} from {original_size} to {new_size}\n")
            return result

        except ValueError as e:
            return self._error_response_with_tree("ERROR", str(e))
        except FileNotFoundError:
            return self._error_response_with_tree("ERROR", f"File not found: {file_path}")
        except Exception as e:
            self.logger.error(f"Error reading image file {file_path}: {str(e)}")
            return self._error_response_with_tree("ERROR", f"Error reading image file {file_path}: {str(e)}")

    def append_to_file(self, file_path, content):
        escaped_path = shlex.quote(file_path)

        # Convert content to bytes if it's not already
        if isinstance(content, str):
            content = content.encode('utf-8')

        # Calculate the max content length that can be safely encoded and sent
        max_content_length = MAX_CLI_LENGTH // 4 * 3  # Account for base64 expansion

        # Append content in chunks
        for i in range(0, len(content), max_content_length):
            chunk = content[i:i + max_content_length]
            encoded_chunk = base64.b64encode(chunk).decode('utf-8')

            cmd = f"printf '%s' '{encoded_chunk}' | base64 -d >> {escaped_path}"
            self._run_docker_command(cmd)

        size = len(content)
        self._send_output(f"Appending {size} bytes to file {file_path}\n")
        return {"status": "SUCCESS", "message": f"Content was successfully appended to {file_path}."}

    def list_directory(self, directory):

        def operation():
            escaped_dir = shlex.quote(directory)
            cmd = f"ls -1 {escaped_dir}"
            output = self._run_docker_command(cmd)
            items = output.splitlines()
            return {"items": items}

        return self._execute_operation(operation)

    def create_directory(self, directory):
        def operation():
            escaped_dir = shlex.quote(directory)
            cmd = f"mkdir -p {escaped_dir}"
            self._run_docker_command(cmd)
            self._send_output(f"Creating directory {directory}\n")
            return {"message": f"Directory {directory} was successfully created."}

        return self._execute_operation(operation)

    def delete_file(self, file_path):
        protected_dirs = ["/", "/bin", "/boot", "/dev", "/etc", "/home", "/lib",
                          "/lib64", "/media", "/mnt", "/opt",
                          "/proc", "/root", "/run", "/sbin", "/srv", "/sys", "/tmp", "/usr", "/var",
                          "/Applications", "/System", "/Users", "/Volumes", "/Library"]
        normalized_file_path = os.path.normpath(file_path)

        if normalized_file_path in protected_dirs:
            return {"status": "ERROR", "message": f"Cannot delete system directory {file_path}"}

        def operation():
            escaped_path = shlex.quote(file_path)

            cmd = f"rm -rf {escaped_path}"
            self._run_docker_command(cmd)
            self._send_output(f"Deleting file {file_path}\n")
            return {"message": f"File {file_path} was successfully deleted."}

        return self._execute_operation(operation)

    def rename_file(self, old_path, new_path):
        def operation():
            escaped_old = shlex.quote(old_path)
            escaped_new = shlex.quote(new_path)
            cmd = f"mv {escaped_old} {escaped_new}"
            self._run_docker_command(cmd)
            self._send_output(f"Renaming file/directory from {old_path} to {new_path}\n")
            return {"message": f"File/directory renamed from {old_path} to {new_path}."}

        return self._execute_operation(operation)

    def copy_file(self, source_path, destination_path):
        def operation():
            escaped_source = shlex.quote(source_path)
            escaped_dest = shlex.quote(destination_path)
            cmd = f"cp {escaped_source} {escaped_dest}"
            self._run_docker_command(cmd)
            self._send_output(f"Copying file from {source_path} to {destination_path}\n")
            return {"message": f"File copied from {source_path} to {destination_path}."}

        return self._execute_operation(operation)

    def get_file_info(self, file_path):
        def operation():
            escaped_path = shlex.quote(file_path)
            cmd = f"stat -c '%s %Y %X %W' {escaped_path}"
            output = self._run_docker_command(cmd)
            size, modified, accessed, created = map(int, output.split())
            info = {
                "size": size,
                "created": created,
                "modified": modified,
                "accessed": accessed,
            }
            self._send_output(f"Getting file info for {file_path}\n")
            return {"info": info}

        return self._execute_operation(operation)

    def search_files(self, directory, pattern, recursive=True):
        def operation():
            escaped_dir = shlex.quote(directory)
            escaped_pattern = shlex.quote(pattern)

            # Construct the find command based on recursiveness
            if recursive:
                cmd = f"find {escaped_dir}"
            else:
                cmd = f"find {escaped_dir} -maxdepth 1"

            # Use find with -name option to match the pattern directly
            cmd += f" -type f -name {escaped_pattern} -printf '%P\\n'"

            output = self._run_docker_command(cmd)
            matches = output.splitlines()

            # Prepend the directory path to each match
            matches = [os.path.join(directory, match) for match in matches]
            self._send_output(f"Searching for files in {directory} with pattern {pattern} (recursive: {recursive})\n")
            return {"matches": matches}

        return self._execute_operation(operation)
