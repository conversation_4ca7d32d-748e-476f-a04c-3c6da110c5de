import os
import time
import requests
from urllib.parse import urlparse
from pathlib import Path
from typing import Dict, Optional, Union, BinaryIO
import logging


class Downloader:
    """
    Downloader class that handles URL redirection, cookies,
    authentication, retry logic, and various other requirements for downloading content.
    """
    
    def __init__(
        self,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: int = 5,
        user_agent: Optional[str] = None,
        verify_ssl: bool = True,
        chunk_size: int = 8192,
        session: Optional[requests.Session] = None,
        log_level: int = logging.INFO,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize the downloader with configurable parameters.
        
        Args:
            timeout: Request timeout in seconds.
            max_retries: Maximum number of retry attempts for failed downloads.
            retry_delay: Delay between retries in seconds.
            user_agent: Custom User-Agent string to use for requests.
            verify_ssl: Whether to verify SSL certificates.
            chunk_size: Size of chunks when streaming downloads.
            session: Optional requests.Session object to use.
            log_level: Logging level to use.
        """
        # Setup logging

        if logger is not None:
            self.logger = logger
        else:
            self.logger = logging.getLogger("Downloader")
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        self.session = session if session else requests.Session()
        
        default_user_agent = "Kavia Downloader/1.0"
        self.headers = {
            "User-Agent": user_agent if user_agent else default_user_agent,
            "Accept": "*/*",
            "Connection": "keep-alive"
        }
        
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.verify_ssl = verify_ssl
        self.chunk_size = chunk_size

    def set_cookies(self, cookies: Dict[str, str]) -> None:
        """
        Set cookies to be used for requests.
        
        Args:
            cookies: Dictionary of cookies to set.
        """
        for key, value in cookies.items():
            self.session.cookies.set(key, value)
        self.logger.debug(f"Set {len(cookies)} cookies")

    def set_headers(self, headers: Dict[str, str]) -> None:
        """
        Set custom headers to be used for requests.
        
        Args:
            headers: Dictionary of headers to set.
        """
        self.headers.update(headers)
        self.logger.debug(f"Updated headers: {headers}")

    def set_auth(self, username: str, password: str) -> None:
        """
        Set basic HTTP authentication.
        
        Args:
            username: Username for authentication.
            password: Password for authentication.
        """
        self.session.auth = (username, password)
        self.logger.debug("Set basic authentication")

    def set_proxy(self, proxy_url: str) -> None:
        """
        Set a proxy for requests.
        
        Args:
            proxy_url: URL of the proxy server.
        """
        self.session.proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        self.logger.debug(f"Set proxy: {proxy_url}")

    def _get_filename_from_url(self, url: str, response: requests.Response) -> str:
        """
        Extract filename from URL or Content-Disposition header.
        
        Args:
            url: URL of the download.
            response: Response object from request.
            
        Returns:
            Extracted filename or a default one.
        """
        # Try to extract from Content-Disposition header
        if "Content-Disposition" in response.headers:
            content_disposition = response.headers["Content-Disposition"]
            filename_start = content_disposition.find("filename=")
            if filename_start != -1:
                filename_start += 9  # Length of "filename="
                filename_end = content_disposition.find(";", filename_start)
                if filename_end == -1:
                    filename_end = len(content_disposition)
                
                filename = content_disposition[filename_start:filename_end].strip('"\'')
                return filename
        
        # Extract from URL
        parsed_url = urlparse(url)
        path = parsed_url.path
        if path and "/" in path:
            filename = path.split("/")[-1]
            if filename:
                return filename
        
        # Default filename with timestamp if nothing else works
        timestamp = int(time.time())
        return f"download_{timestamp}"

    def download(
        self, 
        url: str, 
        destination: Optional[Union[str, Path, BinaryIO]] = None,
        show_progress: bool = True,
        filename: Optional[str] = None
    ) -> str:
        """
        Download a file from a URL to a destination.
        
        Args:
            url: URL to download from.
            destination: Path to save the file to. If None, uses current directory.
                         If a file-like object, writes directly to it.
            show_progress: Whether to display download progress.
            filename: Optional custom filename to use.
            
        Returns:
            Path to the downloaded file or the number of bytes written to a file-like object.
        """
        self.logger.info(f"Starting download from {url}")
        
        file_obj = None
        close_file = False
        file_path = None
        
        try:
            retry_count = 0
            while retry_count <= self.max_retries:
                try:
                    # Make initial HEAD request to check if resource exists and get metadata
                    # but handle sites that don't support HEAD requests

                    content_length = 0
                    response = None
                    
                    try:
                        self.logger.debug(f"Making HEAD request to {url}")
                        head_response = self.session.head(
                            url,
                            headers=self.headers,
                            timeout=self.timeout,
                            allow_redirects=True,
                            verify=self.verify_ssl
                        )
                        head_response.raise_for_status()
                        content_length = int(head_response.headers.get('Content-Length', 0))
                    except (requests.RequestException, IOError) as head_error:
                        self.logger.warning(f"HEAD request failed: {str(head_error)}. Skipping HEAD and proceeding with GET.")
                    
                    self.logger.debug(f"Making GET request to {url}")
                    response = self.session.get(
                        url,
                        headers=self.headers,
                        timeout=self.timeout,
                        stream=True,
                        allow_redirects=True,
                        verify=self.verify_ssl
                    )
                    response.raise_for_status()
                    
                    # Get content length if available and not already retrieved from HEAD
                    if content_length == 0:
                        content_length = int(response.headers.get('Content-Length', 0))
                    
                    # Determine output file path or use file-like object
                    if isinstance(destination, (str, Path)):
                        dest_path = Path(destination)
                        if dest_path.is_dir():
                            if filename:
                                out_filename = filename
                            else:
                                out_filename = self._get_filename_from_url(url, response)
                            file_path = dest_path / out_filename
                        else:
                            # Destination is a specific file path
                            file_path = dest_path
                            # Create parent directories if they don't exist
                            file_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        self.logger.info(f"Downloading to {file_path}")
                        file_obj = open(file_path, 'wb')
                        close_file = True
                    elif hasattr(destination, 'write') and callable(destination.write):
                        # It's a file-like object
                        file_obj = destination
                        self.logger.info(f"Downloading to provided file-like object")
                    else:
                        # Default to current directory with filename from URL
                        if filename:
                            out_filename = filename
                        else:
                            out_filename = self._get_filename_from_url(url, response)
                        file_path = Path(os.getcwd()) / out_filename
                        self.logger.info(f"Downloading to current directory: {file_path}")
                        file_obj = open(file_path, 'wb')
                        close_file = True
                    
                    # Download the file in chunks
                    downloaded_size = 0
                    start_time = time.time()
                    last_update_time = start_time
                    
                    for chunk in response.iter_content(chunk_size=self.chunk_size):
                        if chunk:
                            file_obj.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # Show progress if requested
                            if show_progress and time.time() - last_update_time > 0.5:  # Update every 0.5 seconds
                                if content_length:
                                    percent = (downloaded_size / content_length) * 100
                                    elapsed = time.time() - start_time
                                    speed = downloaded_size / (elapsed if elapsed > 0 else 1)
                                    
                                    # Calculate ETA
                                    if speed > 0:
                                        eta = (content_length - downloaded_size) / speed
                                    else:
                                        eta = 0
                                    
                                    self.logger.info(
                                        f"Progress: {percent:.1f}% | "
                                        f"{downloaded_size / 1024 / 1024:.1f}MB of {content_length / 1024 / 1024:.1f}MB | "
                                        f"Speed: {speed / 1024 / 1024:.1f}MB/s | "
                                        f"ETA: {eta:.0f}s"
                                    )
                                else:
                                    self.logger.info(f"Downloaded: {downloaded_size / 1024 / 1024:.1f}MB")
                                
                                last_update_time = time.time()
                    
                    elapsed = time.time() - start_time
                    self.logger.info(
                        f"Download completed: {downloaded_size / 1024 / 1024:.1f}MB in {elapsed:.1f}s "
                        f"({downloaded_size / (elapsed if elapsed > 0 else 1) / 1024 / 1024:.1f}MB/s)"
                    )
                    
                    # Success! Break out of retry loop
                    break
                    
                except (requests.RequestException, IOError) as e:
                    retry_count += 1
                    if retry_count <= self.max_retries:
                        self.logger.warning(f"Download failed: {str(e)}. Retrying ({retry_count}/{self.max_retries}) in {self.retry_delay}s")
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(f"Download failed after {self.max_retries} retries: {str(e)}")
            
            if file_path:
                return str(file_path)
            else:
                return f"Downloaded {downloaded_size} bytes to file object"
                
        finally:
            # Clean up
            if close_file and file_obj:
                file_obj.close()

    def download_multiple(
        self, 
        urls: list, 
        destination_dir: Union[str, Path],
        show_progress: bool = True
    ) -> list:
        """
        Download multiple files from a list of URLs.
        
        Args:
            urls: List of URLs to download.
            destination_dir: Directory to save the files to.
            show_progress: Whether to display download progress.
            
        Returns:
            List of paths to the downloaded files.
        """
        dest_path = Path(destination_dir)
        dest_path.mkdir(parents=True, exist_ok=True)
        
        downloaded_files = []
        for url in urls:
            try:
                file_path = self.download(url, dest_path, show_progress)
                downloaded_files.append(file_path)
            except Exception as e:
                self.logger.error(f"Failed to download {url}: {str(e)}")
        
        return downloaded_files

    def get_response_info(self, url: str) -> Dict:
        """
        Get detailed information about a URL without downloading the content.
        
        Args:
            url: URL to inspect.
            
        Returns:
            Dictionary with response information.
        """
        try:
            response = self.session.head(
                url,
                headers=self.headers,
                timeout=self.timeout,
                allow_redirects=True,
                verify=self.verify_ssl
            )
            response.raise_for_status()
        except requests.RequestException:
            # If HEAD request fails, try with GET but don't download the full content
            self.logger.warning(f"HEAD request failed, falling back to GET for {url}")
            response = self.session.get(
                url,
                headers=self.headers,
                timeout=self.timeout,
                stream=True,
                allow_redirects=True,
                verify=self.verify_ssl
            )
            response.raise_for_status()
            # Don't download the content
            response.close()
        
        info = {
            "url": response.url,
            "status_code": response.status_code,
            "content_type": response.headers.get("Content-Type"),
            "content_length": response.headers.get("Content-Length"),
            "last_modified": response.headers.get("Last-Modified"),
            "redirect_history": [r.url for r in response.history],
            "headers": dict(response.headers)
        }
        
        return info


# Example usage
if __name__ == "__main__":
    downloader = Downloader()
    downloaded_file = downloader.download("https://picsum.photos/536/354")
    
    # Advanced usage with custom settings
    downloader = Downloader(
        timeout=60,
        max_retries=5,
        retry_delay=10,
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        verify_ssl=True
    )