import atexit
import base64
import io
import os
import queue
import threading
from abc import abstractmethod

import html2text
from PIL import Image

from browsergym.core import OpenEndedTask
from browsergym.core.env import BrowserEnv
from browsergym.utils.obs import flatten_axtree_to_str, flatten_dom_to_str
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface, DynamicFunctionExecutor
from code_generation_core_agent.agents.tools.browsershim.schema import ShimHighLevelActionSet
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.search import PerplexitySearchAPI
from code_generation_core_agent.config import config


DEBUG_MODE = False

CONFIG_BROWSER_FLATTEN_AXTREE = True

MAX_CONTENT_SIZE = 100000

#Proxy configuration
# Name: PROXY_CONFIG
# Description: Proxy configuration passed for chromium in playwright
# Type: dict
# Default: {"server": "http://localhost:8088"}

# The proxy server is started up by the docker container
# For more information see the docker/Dockerfile
# and docker_executor.py

PROXY_CONFIG = {
    "server": "http://localhost:8088"
}

# We use a single global browser environment for all instances of the browser tool
# This is to avoid the overhead of starting up a new browser environment for each tool instance

globalBrowserEnvironment = None
globalBrowserThread = None

# Functions that are excluded from browser gym
BROWSER_GYM_EXCLUDED_FUNCTIONS = (
    "send_msg_to_user",  # We don't want to send messages to the user. There is a separate tool for that.
    "upload_file",  # No file upload in browser gym

    # Navigation functions disabled to reduce complexity
    # Re-enable if needed
    "noop",
    "drag_and_drop",
    "go_forward",
    "dblclick",
    # End of navigation functions
)


class PostProcessingTool:
    @staticmethod
    def getHints(text):
        hints = ["accessibility_tree has the following format: [<bid>] <html object type> <html object text>"]
        # Check if cookies or cookie text is in the text and return the hint
        if "cookies" in text.lower() or "cookie" in text.lower():
            hints.append(
                "If accept cookies button is present, click on it and accept all cookies, otherwise ignore this hint.")
        return hints


class FunctionExecutor:
    """
    Interface for generic function execution.
    Used for testing and debugging.
    """

    @abstractmethod
    def process_action(self, action):
        pass


def build_html_2_text_converter():
    """
    Create an HTML to text converter.
    """
    html_text_converter = html2text.HTML2Text()
    html_text_converter.ignore_links = False
    html_text_converter.ignore_images = True
    html_text_converter.images_to_alt = True
    html_text_converter.body_width = 0
    return html_text_converter


class ThreadedBrowserEnv(threading.Thread):
    def __init__(self, task_kwargs, pw_chromium_kwargs):
        threading.Thread.__init__(self)
        self.task_kwargs = task_kwargs
        self.pw_chromium_kwargs = pw_chromium_kwargs
        self.command_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.browser_env = None
        self.running = True
        self.lock = threading.Lock()
        self.viewport = self.get_viewport_from_config()
        self.timeout = config.getint("TOOLS", "browser_reset_timeout")

    @staticmethod
    def get_viewport_from_config():
        viewport_string = config.get("TOOLS", "browser_viewport")
        viewport = {
            'width': int(viewport_string.split('x')[0]),
            'height': int(viewport_string.split('x')[1])
        }
        return viewport

    def run(self):
        try:
            global globalBrowserEnvironment

            if not globalBrowserEnvironment:
                globalBrowserEnvironment = BrowserEnv(
                    task_entrypoint=OpenEndedTask,
                    task_kwargs=self.task_kwargs,
                    pw_chromium_kwargs=self.pw_chromium_kwargs,
                    resizeable_window=False,
                    viewport=self.get_viewport_from_config()
                )
                obs, info = globalBrowserEnvironment.reset()

            self.browser_env = globalBrowserEnvironment

            while self.running:
                try:
                    command, args = self.command_queue.get(timeout=1)
                    if command == 'reset':
                        result = self.browser_env.reset()
                    elif command == 'step':
                        result = self.browser_env.step(args)
                    elif command == 'close':
                        self.running = False
                        result = None
                    else:
                        result = (None, None, None, None, {'error': 'Unknown command'})
                    self.result_queue.put(result)
                except queue.Empty:
                    continue

        except Exception as e:
            # Stack trace
            import traceback
            traceback.print_exc()
            self.result_queue.put((None, None, None, None, {'error': str(e)}))

    def reset(self):
        with self.lock:
            if self.running:
                self.command_queue.put(('reset', None))
                return self.result_queue.get(timeout=self.timeout)  # Use configurable timeout
            else:
                raise Exception("Browser thread not running")

    def step(self, action):
        with self.lock:
            if self.running:
                self.command_queue.put(('step', action))
                return self.result_queue.get(timeout=self.timeout)  # Use configurable timeout
            else:
                raise Exception("Browser thread not running")

    def close(self):
        # Don't join from within the thread itself - this causes deadlock
        # The join should be called from the main thread that created this thread, shutdown_browser() does this
        with self.lock:
            if self.running:  # Only send close command if still running
                self.command_queue.put(('close', None))


def shutdown_browser():
    global globalBrowserThread, globalBrowserEnvironment
    if globalBrowserThread:
        try:
            globalBrowserThread.close()
            # Join the thread from the caller thread context
            globalBrowserThread.join(timeout=10)  # Add timeout to prevent hanging
        except Exception as e:
            print(f"Error during browser shutdown: {e}")
        finally:
            globalBrowserThread = None
            globalBrowserEnvironment = None


atexit.register(shutdown_browser)


class BrowserFunctionExecutor(FunctionExecutor):
    """
    Function executor for the browser shim tool.
    """
    current_screen_shot = None

    def __del__(self):
        self.close()

    def get_current_screen_shot(self):
        return self.current_screen_shot

    def __init__(self, callback: callable = None, chromium_kwargs=None):

        self.viewport = ThreadedBrowserEnv.get_viewport_from_config()

        self.screenshot_width = config.getint("TOOLS", "browser_screenshot_width")
        ratio =  self.viewport['width'] / self.viewport['height']
        self.screenshot_height = int(self.screenshot_width / ratio)

        if chromium_kwargs is None:
            # test if the proxy port is open and valid
            # if not, use the default chromium kwargs
            # This is a workaround for the proxy server not being available
            # in the docker container

            import socket
            try:
                # Parse http://<address>:<port> from the proxy server

                address, port = PROXY_CONFIG['server'].split('://')[1].split(':')
                sock = socket.create_connection((address, int(port)), timeout=5)
                sock.close()
                self.chromium_kwargs = {
                    'proxy': PROXY_CONFIG
                }
            except (socket.timeout, ConnectionRefusedError):
                # Proxy server is not available, use default chromium kwargs
                print("Proxy server is not available. Using default chromium kwargs.")
                self.chromium_kwargs = {
                }

        else:
            self.chromium_kwargs = chromium_kwargs

        self.callback = callback
        self.html_text_converter = build_html_2_text_converter()

        global globalBrowserThread

        try:
            if not globalBrowserThread:
                globalBrowserThread = ThreadedBrowserEnv(
                    task_kwargs={"start_url": "about:blank"},
                    pw_chromium_kwargs=self.chromium_kwargs
                )
                globalBrowserThread.start()

            self.browser_env = globalBrowserThread
        except Exception as e:
            print(f"Error initializing the browser environment: {str(e)}")
            self.browser_env = None

    @staticmethod
    def resize_and_encode_to_base64png(img, new_width=None, new_height=None):
        """
        Optionally resizes a numpy array image and converts it to a base64 encoded PNG string.
        Uses a resampling method that is compatible across different versions of PIL/Pillow.

        Args:
            img: A numpy array representing an image (height, width, rgb).
            new_width: The desired width of the output image. If None, original width is kept.
            new_height: The desired height of the output image. If None, original height is kept.

        Returns:
            A base64 encoded string of the image in PNG format.
        """
        # Convert numpy array to PIL Image
        pil_img = Image.fromarray(img.astype('uint8'), 'RGB')

        # Resize the image if new dimensions are provided
        if new_width is not None and new_height is not None:
            # Use BICUBIC resampling, which is available in all versions
            pil_img = pil_img.resize((new_width, new_height), Image.BICUBIC)

        # Save the image to a bytes buffer
        buffered = io.BytesIO()
        pil_img.save(buffered, format="PNG")
        img_str = buffered.getvalue()

        # Encode the image string to base64
        base64_encoded = base64.b64encode(img_str)

        return base64_encoded.decode('utf-8')

    def _observation_to_status(self, observation: dict) -> dict:
        status = 'SUCCESS'
        message = 'Action completed successfully.'

        html_text = self.html_text_converter.handle(flatten_dom_to_str(observation['dom_object']))

        cur_axtree_txt = flatten_axtree_to_str(
            observation['axtree_object'],
            extra_properties=observation[
                'extra_element_properties'
            ],
            filter_visible_only=True,
            filter_with_bid_only=True,
            with_visible=True,
            with_clickable=True,
        )

        if DEBUG_MODE:
            print("Last browser action: ", observation['last_action'])
            print("Error from browser: ", observation['last_action_error'])

        print("Last action error: ", observation['last_action_error'])
        print("message: ", message)
        self.current_screen_shot = self.resize_and_encode_to_base64png(observation['screenshot'], self.screenshot_width, self.screenshot_height)

        content = f"data:image/png;base64,{self.current_screen_shot}"

        result = {
            'status': status,
            'message': message,
            'web_page_text': html_text,
            'accessibility_tree': cur_axtree_txt,
            "image_url": {"url": content, "detail": "auto"},
            'focused_element_bid': observation['focused_element_bid'],
            'last_browser_action': observation['last_action'],
            'hints': PostProcessingTool().getHints(html_text + cur_axtree_txt),
        }

        if observation['last_action_error']:
            result['last_browser_action'] = observation['last_action_error']
            result['status'] = 'ERROR'



        if self.callback:
            self.callback(self.current_screen_shot)

        return result


    def process_action(self, action) -> dict:
        """
        Process the action and return the result.
        """
        # Remove the function prefix at the beginning of the action string
        if self.browser_env == None:
            return {
                'status': 'ERROR',
                'message': 'Browser is down. Environment cant run a browser.'
            }
        else:
            obs, reward, terminated, truncated, info = self.browser_env.step(action)
        return self._observation_to_status(obs)

    def close(self):
        pass


class WebBrowser(BaseToolInterface, DynamicFunctionExecutor):
    """ Browser shim tool for OpenDevin browser"""

    def shutdown(self):
        super().shutdown()
        self.browser_function_executor.close()

    def __del__(self):
        self.shutdown()
        
    def call_dynamic_function(self, function_name, **kwargs):
        if DEBUG_MODE:
            print(f"Called {function_name} with parameters: {kwargs}")

        param_strings = []
        for param_name, param_value in kwargs.items():
            if isinstance(param_value, str):
                param_strings.append(f"{param_name}='{param_value}'")
            elif isinstance(param_value, list):
                param_strings.append(f"{param_name}={param_value}")
            else:
                param_strings.append(f"{param_name}={repr(param_value)}")

        action_string = f"{function_name}({', '.join(param_strings)})"

        # Print debugging information
        if DEBUG_MODE:
            print(f"Called {function_name} with parameters: {kwargs}")
            print(f"Name of the function: {function_name}")
            print(f"Action string: {action_string}")

        # Call the process_action method with the constructed action_string
        return self.browser_function_executor.process_action(action_string)

    


    def get_tool_name(self):
        return "WebBrowser"

    def _dump_action_list(self):
        for action in self.all_functions:
            print(f"Action: {action}")

    def __init__(self, callbacks, base_path, logger,
                 llm=None, executor=None,
                 browser_function_executor= BrowserFunctionExecutor):

        super().__init__(callbacks, base_path, logger, llm, executor)
        self.viewport = ThreadedBrowserEnv.get_viewport_from_config()
        self.logger = logger

        self.screenshot_width = config.getint("TOOLS", "browser_screenshot_width")
        #  Screenshot height is calculated based on the aspect ratio of the viewport

        ratio =  self.viewport['width'] / self.viewport['height']
        self.screenshot_height = int(self.screenshot_width / ratio)

        self.base_path = base_path

        if executor is not None:
            self.browser_function_executor = executor

        self.callback = callbacks.get('browser_output_callback', None)
        self.browser_function_executor = browser_function_executor(self.callback)
        action_subsets = ['chat', 'bid', 'nav']

        # Get the high-level action set
        self._action_space = ShimHighLevelActionSet(
            subsets=action_subsets,
            strict=False,
            multiaction=True,
            function_prefix="",
        )

        self.all_functions = self._action_space.describe()

        self._action_set = {}

        self._create_dynamic_functions()

        if DEBUG_MODE:
            self._dump_action_list()

    @property
    def action_space(self):
        return self._action_space

    def _create_dynamic_functions(self):
        for action_dict in self._action_space.describe():
            function_info = action_dict['function']
            # Exclude functions that we don't want to expose to the llm
            if function_info['name'] in BROWSER_GYM_EXCLUDED_FUNCTIONS:
                continue

            function_name = function_info['name']
            function_description = function_info['description']
            parameters = function_info['parameters']['properties']
            required_params = function_info['parameters'].get('required', [])

            description_extra = ("\nThis function is only for headless browser. Browser window will be"
                                  " never shown to the user.")
            metadata = {
                "type": "function",
                "function": {
                    "name": function_name,
                    "description": function_description + description_extra,
                    "parameters": {
                        "type": "object",
                        "properties": parameters,
                        "required": required_params
                    }
                }
            }

            self.register_schema(metadata)
