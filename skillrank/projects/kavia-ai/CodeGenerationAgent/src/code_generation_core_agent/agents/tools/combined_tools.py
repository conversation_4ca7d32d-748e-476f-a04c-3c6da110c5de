import copy
from abc import ABC

from code_generation_core_agent.agents.tools.base_tool import <PERSON><PERSON>ool
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface, DynamicFunctionExecutor
from code_generation_core_agent.agents.tools.validator.pre_check import PreCheckLoader

DEBUG_MODE = False


class ToolPreProcessor(ABC):
    """
    Interface for pre- and post-processing tool.

    A ToolPreProcessor can extend the function map, pre-process function calls
     and post-process function responses.
    """

    def __init__(self):
        pass

    def pre_process_function_call(self, function_name, **arguments):
        """
        Pre-process function call data
        Return the processed arguments
        """
        pass

    def post_process_function_response(self, function_name, arguments, data):
        """
        Post-process the data.
        """
        pass

    def pre_process_function_registration(self, data):
        """
        Pre-process function registration data

        @param data: dictionary containing the function registration datas
        """
        pass


class CombinedTools(BaseTool):
    # Map to store the actual functions being called
    function_namespace_mapping = {}
    # Map to the functions seen by the LLM
    function_processed_mapping = {}

    @staticmethod
    def _get_unknown_function_error(function_name):
        """
        Get the unknown function error.

        :param function_name: Name of the function.
        """
        return {
            "status": "ERROR",
            "message": f"Unknown function: {function_name}",
            "error_type": "UnknownFunction",
            "function_name": function_name
        }

    def __init__(self, tool_set: list[BaseToolInterface],
                 pre_processing_tools: list[ToolPreProcessor],
                 base_path, logger=None, llm=None, executor=None):
        """
        Initialize the combined tools.

        :param tool_set: List of tools to combine.
        :param processing_tools: List of processing tools.
        :param base_path: Base path for the tool.
        :param logger: Logger for the tool.
        :param llm: Local Loop Manager for the tool.
        :param executor: Executor for the tool.
        """
        super().__init__(base_path, logger, llm, executor)
        self.tool_set = {}

        for tool in tool_set:
            # Check that tool name has no _ in it
            if "_" in tool.get_tool_name():
                raise ValueError(f"Tool name should not contain _. Offending tool: {tool.get_tool_name()}")

            self.tool_set[tool.get_tool_name()] = tool

        self.processing_tools = pre_processing_tools
        self._init_functions()
        self.pre_check_loaders = {}

    def _load_validator(self, validator_file):
        if validator_file not in self.pre_check_loaders:
            loader = PreCheckLoader(validator_file)
            loader.load_yaml(self.base_path)
            self.pre_check_loaders[validator_file] = loader
        return self.pre_check_loaders[validator_file]

    def validate_function(self, tool_direct_function_name, **kwargs):
        """
        Validate a function registered in the tool set.
        Not that if a function is not registered yet, it will return True.
        Return True if the function validation passed is valid or we don't keep track of this function,
               raise an exception otherwise.
        """

        if "_" not in tool_direct_function_name:
            raise ValueError(
                f"Function name should contain _ to separate tool name. Offending function: {tool_direct_function_name}")

        tool_name, function_name = tool_direct_function_name.split("_", 1)

        try:
            tool_instance = self.tool_set[tool_name]
        except KeyError:
            # This function may be just not registered yet.
            return True

        functionn = getattr(tool_instance, function_name, None)

        if functionn is None:
            # Don't validate function that is not registered
            return True

        if hasattr(functionn, '_validator'):
            validator_file = functionn._validator

            if validator_file is None:
                return True

            loader = self._load_validator(validator_file)
            result, error_message, backtrace = loader.execute_pre_checks(function_name, kwargs)
            if not result:
                raise ValueError(error_message)

        return True

    def _generate_and_register_function_schemas(self):
        """
        Generate and register function schemas for all tools.
        """
        pass

    def _init_decorated_functions(self):
        """
        Initialize the decorated functions for the combined tool.
        """
        for tool_name, tool in self.tool_set.items():
            functions_for_registration = []
            registered_functions = tool.get_registered_functions()
            # Print _metadata for each function
            for func_name, func in registered_functions.items():
                processed_metadata = copy.deepcopy(func._metadata)

                for processing_tool in self.processing_tools:
                    processed_metadata = processing_tool.pre_process_function_registration(processed_metadata)
                    processed_metadata["function"]["name"] = tool_name + "_" + func_name
                functions_for_registration.append(processed_metadata)

            self.add_function_schemas(functions_for_registration, tool_name)

    def _init_dynamic_functions(self):
        """
        Initialize the dynamic functions for the combined tool.
        """
        for tool_name, tool in self.tool_set.items():
            functions_for_registration = []

            if isinstance(tool, DynamicFunctionExecutor):
                schemas = tool.get_registered_schemas()
                for schema in schemas:
                    function_name = schema["function"]["name"]
                    processed_metadata = copy.deepcopy(schema)
                    for processing_tool in self.processing_tools:
                        processed_metadata = processing_tool.pre_process_function_registration(processed_metadata)
                        processed_metadata["function"]["name"] = tool_name + "_" + function_name

                    if processed_metadata:
                        functions_for_registration.append(processed_metadata)

            if functions_for_registration:
                self.add_function_schemas(functions_for_registration, tool_name)

    def _init_functions(self):
        """
        Initialize the functions for the combined tool.
        """
        self._init_decorated_functions()
        self._init_dynamic_functions()

    def update_base_path(self, new_base_path):
            """Update base path for all combined tools that support it"""
            
            # Update base path for any tool instances in tool_set if they exist
            if hasattr(self, 'tool_set'):
                for tool_name, tool_instance in self.tool_set.items():
                    if hasattr(tool_instance, 'update_base_path'):
                        tool_instance.update_base_path(new_base_path)
                        self.logger.debug(f"Updated base path for {tool_name} to {new_base_path}")

    def _preprocess_argument(self, function_name, **args):
        for processing_tool in self.processing_tools:
            args = processing_tool.pre_process_function_call(function_name, **args)

            if not isinstance(args, dict):
                raise ValueError("Pre-process function call should return a dictionary")
        return args

    def _call_function_internal(self, tool_instance, function_name, **arguments):

        # Check if tool instance is a dynamic function executor and the function is
        # one of the dynamic functions. If not try to call it directly

        if isinstance(tool_instance, DynamicFunctionExecutor):
            schemas = tool_instance.get_registered_schemas()
            for schema in schemas:
                if schema["function"]["name"] == function_name:
                    return tool_instance.call_dynamic_function(function_name, **arguments)

            if hasattr(tool_instance, function_name):
                return getattr(tool_instance, function_name)(**arguments)
            else:
                return self._get_unknown_function_error(function_name)

        return getattr(tool_instance, function_name)(**arguments)

    def call_function(self, tool_direct_function_name, **kwargs):
        """
        Call a function in the tool set.

        :param tool_direct_function_name: Name of the function to call.
        :param kwargs: Keyword arguments to pass to the function.
        """
        # Split _ to get the tool name

        if "_" not in tool_direct_function_name:
            self.logger.error(
                f"Function name should contain _ to separate tool name. Offending function: {tool_direct_function_name}")
            return self._get_unknown_function_error(tool_direct_function_name)

        if "." in tool_direct_function_name:
            self.logger.error(f"Function name should not contain . Offending function: {tool_direct_function_name}")
            return self._get_unknown_function_error(tool_direct_function_name)

        tool_name = tool_direct_function_name.split("_")[0]

        function_name = "_".join(tool_direct_function_name.split("_")[1:])

        try:
            tool_instance = self.tool_set[tool_name]
        except KeyError:
            return {"status": "ERROR", "message": f"Tool {tool_name} not found. Incorrect function name."}

        # Exclude attributes from kwargs we add them later
        attributes = {k: v for k, v in kwargs.items() if k != "attributes"}

        # If "attributes" exists in kwargs, update our attributes dictionary with its contents
        if "attributes" in kwargs and isinstance(kwargs["attributes"], dict):
            attributes.update(kwargs["attributes"])

        arguments = self._preprocess_argument(function_name, **attributes)

        if DEBUG_MODE:
            print(f'Calling function {function_name} in tool {tool_instance}'
                  f' instance {tool_instance} and class {tool_instance.__class__.__name__}')

        #target = self.function_namespace_mapping[tool_name][tool_instance.__class__.__name__]

        # call function_name on tool_instance with args and kwargs
        result = self._call_function_internal(tool_instance, function_name, **arguments)

        processed_result = self._post_process_result(function_name, arguments, result)

        return processed_result

    def shutdown(self):
        """
        Shutdown the combined tool.
        """
        for tool in self.tool_set.values():
            tool.shutdown()

    def get_tool(self, tool_name: str):
        """
        Get ta tool
        """
        for tool in self.tool_set.values():
            if tool.get_tool_name() == tool_name:
                return tool
        return None

    def _post_process_result(self, function_name, arguments, result):
        """
        Post-process the result.

        :param result: Result to post-process.
        """
        for processing_tool in self.processing_tools:
            result = processing_tool.post_process_function_response(function_name, arguments, result)
        return result
