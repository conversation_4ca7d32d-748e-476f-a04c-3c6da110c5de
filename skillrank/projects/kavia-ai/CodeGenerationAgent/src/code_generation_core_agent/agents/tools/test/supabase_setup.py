#!/usr/bin/env python3
"""
bootstrap_run_sql.py

Initialises helper objects in your Supabase Postgres:

• Ensures the pgcrypto extension exists (needed for pgp_sym_encrypt / decrypt)
• Clears & re-creates  public.run_sql(query text)

When run as a script it performs:  check/install pgcrypto → clear → install
and prints status lines.

Requirements:
    pip install psycopg2-binary python-dotenv
    export SUPABASE_DB_URL=postgres://<supabase_admin>:<pwd>@.../postgres
"""

import os
import sys
import textwrap
import psycopg2
from contextlib import contextmanager
from dotenv import load_dotenv

# --------------------------------------------------------------------------- #
#  Load env vars & connect                                                    #
# --------------------------------------------------------------------------- #
load_dotenv()
PG_URL = os.getenv("SUPABASE_DB_URL")
if not PG_URL:
    sys.exit("❌  SUPABASE_DB_URL is not set; aborting.")

@contextmanager
def pg_cursor():
    """Yield a cursor; print DB errors nicely and exit."""
    conn = None
    try:
        conn = psycopg2.connect(PG_URL)
        with conn.cursor() as cur:
            yield cur
        conn.commit()
    except psycopg2.Error as e:
        if conn:
            conn.rollback()
        print(f"❌  PostgreSQL error:\n    {e.pgerror or str(e).strip()}")
        # traceback.print_exc()          # uncomment for full stack
        sys.exit(1)
    finally:
        if conn:
            conn.close()

# ── SQL payloads ─────────────────────────────────────────────────────────────
DROP_RUN_SQL = "drop function if exists public.run_sql(text);"

CREATE_RUN_SQL = textwrap.dedent(r"""
create or replace function public.run_sql(query text)
returns jsonb
language plpgsql
security definer
set search_path = public         
as $$
declare
  clean text := regexp_replace(query, ';\\s*$', '');    -- strip trailing ';'
  result jsonb;
  produces_rows boolean :=
       clean ~* '^\s*(select|with)\s'                   -- SELECT / WITH …
    or clean ~* '\\breturning\\b';                      -- … or DML with RETURNING
begin
  if produces_rows then
      execute format(
         'with _q as (%s) select coalesce(json_agg(_q), ''[]'') from _q',
         clean
      ) into result;
      return result;
  else
      execute clean;                                    -- DDL or DML w/o rows
      return 'null'::jsonb;
  end if;
end;
$$;
""")

# --------------------------------------------------------------------------- #
#  Helpers                                                                    #
# --------------------------------------------------------------------------- #
def pgcrypto_present() -> bool:
    """Return True iff pgcrypto extension is installed (any schema)."""
    with pg_cursor() as cur:
        cur.execute(
            "select 1 from pg_extension where extname = 'pgcrypto' limit 1;"
        )
        return cur.fetchone() is not None

def install_pgcrypto() -> None:
    """
    Guarantee a fresh pgcrypto install in the *public* schema.

    • If pgcrypto exists → DROP EXTENSION pgcrypto;
    • Then (re)CREATE EXTENSION pgcrypto WITH SCHEMA public;
    • Requires a role that has CREATE EXTENSION privilege
      (e.g. supabase_admin on Supabase).
    • Aborts with exit-code 1 if the current role lacks privilege.
    """
    with pg_cursor() as cur:
        cur.execute(
            "select 1 from pg_extension where extname = 'pgcrypto' limit 1;"
        )
        already = cur.fetchone() is not None

    try:
        with pg_cursor() as cur:
            if already:
                print("🔄  pgcrypto already present – dropping first …")
                cur.execute("drop extension pgcrypto cascade;")

            print("⬆️   installing pgcrypto …")
            cur.execute("create extension pgcrypto with schema public;")

        print("✅  pgcrypto installed (fresh)")
    except psycopg2.errors.InsufficientPrivilege as e:
        print(
            "❌  could not create/drop pgcrypto (missing privilege).\n"
            "    Re-run this script as the built-in supabase_admin role.\n"
            f"    pgerror: {e.pgerror.strip()}"
        )
        sys.exit(1)

def run_sql_present() -> bool:
    """Return True iff public.run_sql exists in pg_proc."""
    with pg_cursor() as cur:
        cur.execute("""
            select 1
            from pg_proc p
            join pg_namespace n on n.oid = p.pronamespace
            where p.proname = 'run_sql' and n.nspname = 'public'
            limit 1;
        """)
        return cur.fetchone() is not None

def verify_run_sql() -> bool:
    """
    Call run_sql() with a trivial query; returns True on success.
    Any execution error will be surfaced by pg_cursor() as sys.exit(1),
    so returning False is only possible if the function isn't found.
    """
    if not run_sql_present():
        return False
    with pg_cursor() as cur:
        cur.execute("select public.run_sql('select 1 as ok');")
        return cur.fetchone() is not None

def clear_run_sql() -> None:
    with pg_cursor() as cur:
        cur.execute(DROP_RUN_SQL)
    print("🗑️   run_sql() dropped (if it existed)")

def install_run_sql() -> None:
    with pg_cursor() as cur:
        cur.execute(CREATE_RUN_SQL)

    # ── verification step ────────────────────────────────────────────────────
    if verify_run_sql():
        print("✅  run_sql() installed / updated & verified operational")
    else:
        print("❌  run_sql() install appears to have failed.")
        sys.exit(1)

# ── main orchestration ───────────────────────────────────────────────────────
if __name__ == "__main__":
    try:
        install_pgcrypto()   # 1️⃣ ensure encryption funcs exist
        clear_run_sql()      # 2️⃣ drop old helper
        install_run_sql()    # 3️⃣ create + verify new helper
    except Exception as e:
        print(f"❌  {type(e).__name__}: {e}")
        # traceback.print_exc()
        sys.exit(1)