# tool.py

from code_generation_core_agent.agents.tools.search.perplexity import PerplexitySearchAPI
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from .downloader import Downloader
import os
from dotenv import load_dotenv
from firecrawl import FirecrawlApp

load_dotenv()
PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY")
FIRECRAWL_API_KEY = os.getenv("FIRECRAWL_API_KEY")
SEARCH_MAX_RESULTS = 10


class FireCrawl:
    def __init__(self, logger):
        self.logger = logger

    def generate_markdown(self, url, output_format="markdown"):
        """
        Generate structured markup for a given webpage URL using FireCrawl.

        Args:
            url (str): The URL of the webpage to process
            output_format (str): The desired output format (html or markdown)

        Returns:
            dict: A dictionary containing the status and generated markup
        """
        try:
            app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)
            scrape_result = app.scrape_url(url=url, formats=[output_format])
            #print (scrape_result.markdown)
            return scrape_result.markdown
        except Exception as e:
            self.logger.error(f"Error generating markup for {url}: {str(e)}")
            raise e

class SearchAndDownloadTool(BaseToolInterface):
    def get_tool_name(self):
        return "SearchAndDownloadTool"

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.output_callback = callback_functions.get("terminal_output_callback", None)
        self.codeview_callback = callback_functions.get("codeview_callback", None)
        self.agent_messsage_callback = callback_functions.get("agent_message_callback")
        self.executor = executor
        self.logger = logger
        self.llm = llm

        self.downloader = Downloader(logger=self.logger)

    @register_function(
        description="Performs web search using a search engine. Returns a list of search results which inclide title, url, and snippet.",
        parameters={
            "type": "object",
            "properties": {
                "search_string": {
                    "type": "string",
                    "description": "Search string to search the web."
                }
            },
            "required": ["search_string"]
        }
    )
    def web_search(self, search_string: str) -> dict:
        perplexity = PerplexitySearchAPI(
            logger=self.logger,
            api_key=PERPLEXITY_API_KEY,
            max_results=SEARCH_MAX_RESULTS
        )

        response = perplexity.search(
            query=search_string,
            num_results=SEARCH_MAX_RESULTS
        )

        return {
            'status': 'SUCCESS',
            'message': str(response.to_dict())
        }

    def _get_full_path(self, base_path, file_path):
        normalized_path = os.path.normpath(file_path)
        if normalized_path.startswith('..'):
            raise ValueError("Access to parent directories is not allowed")
        return os.path.join(base_path, normalized_path)
            
    @register_function(
        description="Download a file from the given URL and save it to the target path."
        "Target path should be relative to base_directory.\n"
        "Example: download_asset(url='https://example.com/file.txt', target_path='relative/path/to/save/file.txt')",
        parameters={
            "type": "object",
            "properties": {
                "url": {
                    "type": "string",
                    "description": "The URL of the file to download."
                },
                "target_path": {
                    "type": "string",
                    "description": "The absolute path where the downloaded file will be saved."
                }
            },
            "required": ["url", "target_path"]
        }
    )

    def download_asset(self, url, target_path):
        """
        Download a file from the given URL and save it to the target path.
        """
        self.logger.info(f"Reading file {target_path}")
        full_path = self._get_full_path(self.base_path, target_path)

        try:
            out_path = self.downloader.download(url, full_path)
            return  {
                'status': 'SUCCESS',
                'message': f"File downloaded from {url} to {out_path}",
            }
        except Exception as e:
            return {
                'status': 'ERROR',
                'message': f"Error downloading file from {url}: {str(e)}",
            }
    @register_function(
        description="Generate markup for a webpage URL using FireCrawl. Returns the structured markup of the webpage content."
                    "example: generate_webpage_markup(url='https://example.com', output_format='markdown')",
        parameters={
            "type": "object",
            "properties": {
                "url": {
                    "type": "string",
                    "description": "The URL of the webpage to generate markup for"
                },
                "output_format": {
                    "type": "string",
                    "description": "Desired output format (e.g., 'html', 'markdown')",
                    "enum": ["html", "markdown"]
                }
            },
            "required": ["url"]
        }
    )
    def generate_webpage_markdown(self, url: str, output_format: str = "markdown") -> dict:
        """
        Generate structured markup for a given webpage URL using FireCrawl.

        Args:
            url (str): The URL of the webpage to process
            output_format (str): The desired output format (html or markdown)

        Returns:
            dict: A dictionary containing the status and generated markup
        """
        try:
            crawler = FireCrawl(logger=self.logger)
            markup = crawler.generate_markdown(url, output_format=output_format)

            return {
                'status': 'SUCCESS',
                'message': 'Markup generated successfully',
                'markup': markup
            }
        except Exception as e:
            return {
                'status': 'ERROR',
                'message': f"Error generating markup for {url}: {str(e)}"
            }