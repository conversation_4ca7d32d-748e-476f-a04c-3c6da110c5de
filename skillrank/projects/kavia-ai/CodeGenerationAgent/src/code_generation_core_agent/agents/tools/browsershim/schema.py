from inspect import Signature, Parameter
from typing import List, Dict, Any, Union, Optional, Literal

from browsergym.core.action.highlevel import *

TEMPLATE = """
function_schema = [
    {
        "type": "function",
        "function": {
            "name": str,
            "description": str,
            "parameters": {
                "type": "object",
                "properties": {
                    str: {
                        "type": str,
                        "description": str,
                        "items": {
                            "type": str
                        }
                    }
                },
                "required": list[str]
            }
        }
    }
]

"""


@dataclass
class ShimHighLevelAction:
    signature: Signature
    description: str
    examples: list[str]


class ShimHighLevelActionSet(AbstractActionSet):
    """
    ShimHighLevelActionSet is based on the HighLevelActionSet class from browsergym, however,
    it is designed to create function descriptors compatible with the Code Generation Agent.

    function_schema = [
    {
        "type": "function",
        "function": {
            "name": str,
            "description": str,
            "parameters": {
                "type": "object",
                "properties": {
                    str: {
                        "type": str,
                        "description": str,
                        "items": {
                            "type": str
                        }
                    }
                },
                "required": list[str]
            }
        }
    }
    """

    ActionSubset = HighLevelActionSet.ActionSubset

    def __init__(
            self,
            subsets: Optional[ActionSubset | list[ActionSubset]] = [
                "chat",
                "bid",
                "nav",
                "tab",
            ],
            custom_actions: Optional[list[callable]] = None,
            multiaction: bool = True,
            demo_mode: Literal["off", "default", "all_blue", "only_visible_elements"] = "off",
            strict: bool = False,
            function_prefix: str = "browser_",
    ):
        super().__init__(strict)
        self.multiaction = multiaction
        self.demo_mode = demo_mode
        self.function_prefix = function_prefix
        if not subsets:
            raise ValueError(f"'action_subsets' is empty.")

        if isinstance(subsets, str):
            subsets = [subsets]

        allowed_actions = [noop]  # the noop action is always allowed

        # add actions from specified action sets
        if subsets:
            for subset in subsets:
                match subset:
                    case "chat":
                        allowed_actions.extend(ACTION_SUBSETS["chat"])
                    case "infeas":
                        allowed_actions.extend(ACTION_SUBSETS["infeas"])
                    case "bid":
                        allowed_actions.extend(ACTION_SUBSETS["bid"])
                    case "coord":
                        allowed_actions.extend(ACTION_SUBSETS["coord"])
                    case "nav":
                        allowed_actions.extend(ACTION_SUBSETS["nav"])
                    case "tab":
                        allowed_actions.extend(ACTION_SUBSETS["tab"])
                    case "custom":
                        if not custom_actions:
                            raise ValueError(
                                "'custom' is in 'action_subsets' but 'custom_actions' is empty."
                            )
                        allowed_actions.extend(custom_actions)
                    case _:
                        raise ValueError(f"Unknown high-level action subspace: {subset}")

        # like set() but preserves order
        allowed_actions = list(dict.fromkeys(allowed_actions).keys())

        # parse the actions and build the action space
        self.action_set: dict[str, ShimHighLevelAction] = {}
        self.python_includes = ""

        # include playwright imports
        self.python_includes += f"""\
import playwright.sync_api
from typing import Literal


"""
        # include demo_mode flag
        self.python_includes += f"""\
demo_mode={repr(demo_mode)}


"""

        # include utility functions
        for _, func in inspect.getmembers(utils, inspect.isfunction):
            self.python_includes += f"""\
{inspect.getsource(func)}


"""

        # parse and include action functions
        for func in allowed_actions:
            # include action function definition in the code
            self.python_includes += f"""\
{inspect.getsource(func)}


"""

            # extract action signature
            sig = inspect.signature(func)

            # parse docstring
            description, examples = action_docstring_parser.parse_string(func.__doc__)

            # reconstruct action description
            description = " ".join(description)

            # reconstruct action examples
            examples = [
                self.function_prefix + function_name + "(" + ", ".join([repr(arg) for arg in function_args]) + ")"
                for function_name, function_args in examples
            ]

            if func.__name__ in self.action_set:
                raise ValueError(f"Duplicated action '{func.__name__}'")

            self.action_set[func.__name__] = ShimHighLevelAction(
                signature=sig,
                description=description,
                examples=examples,
            )

    def get_function_schema(self) -> List[Dict[str, Any]]:
        function_schema = []

        for action_name, action in self.action_set.items():
            parameters = {
                "type": "object",
                "properties": {},
                "required": []
            }

            for param_name, param in action.signature.parameters.items():
                param_info = {
                    "type": self._get_param_type(param.annotation),
                    "description": f"Parameter {param_name}"
                }

                if param.annotation is Parameter.empty:
                    param_info["type"] = "any"
                elif getattr(param.annotation, "__origin__", None) in (list, List):
                    param_info["items"] = {"type": self._get_param_type(param.annotation.__args__[0])}

                parameters["properties"][param_name] = param_info

                if param.default is Parameter.empty:
                    parameters["required"].append(param_name)

            function_schema.append({
                "type": "function",
                "function": {
                    "name": self.function_prefix + action_name,
                    "description": action.description,
                    "parameters": parameters
                }
            })

        return function_schema

    @staticmethod
    def _get_param_type(annotation):
        if annotation is Parameter.empty:
            return "any"
        elif annotation == str:
            return "string"
        elif annotation == int:
            return "integer"
        elif annotation == float:
            return "number"
        elif annotation == bool:
            return "boolean"
        elif getattr(annotation, "__origin__", None) in (list, List):
            return "array"
        else:
            return "object"

    def example_action(self, abstract: bool, max_examples: int = 3) -> str:
        """
        Returns an example action as a string.
        """
        if abstract:
            if self.multiaction:
                return """\
One or several actions, separated by new lines."""
            else:
                return """\
One single action to be executed. You can only use one action at a time."""
        else:
            picked_examples = []

            # use fill and click examples if action is present
            for action_name in ["fill", "click", "mouse_click", "keyboard_type"]:
                if action_name in self.action_set:
                    picked_examples.extend(self.action_set[action_name].examples)

            # last resort, use all action examples
            if not picked_examples:
                for _, action in self.action_set.items():
                    picked_examples += action.examples

            # shuffle examples
            rng = random.Random(1)
            rng.shuffle(picked_examples)

            if self.multiaction:
                return "\n".join(picked_examples[:max_examples])
            else:
                return picked_examples[0]

    def describe(self) -> List[Dict[str, Any]]:
        """
        Returns a description of this action space in the specified format.

        Returns:
            List[Dict[str, Any]]: A list of function schemas.
        """
        function_schema = []

        for action_name, action in self.action_set.items():
            parameters = {
                "type": "object",
                "properties": {},
                "required": []
            }

            for param_name, param in action.signature.parameters.items():
                param_info = {
                    "type": self._get_param_type(param.annotation),
                    "description": f"Parameter {param_name}"
                }

                if param.annotation is Parameter.empty:
                    param_info["type"] = "any"
                elif getattr(param.annotation, "__origin__", None) in (list, List):
                    param_info["items"] = {"type": self._get_param_type(param.annotation.__args__[0])}

                parameters["properties"][param_name] = param_info

                if param.default is Parameter.empty:
                    parameters["required"].append(param_name)

            # Combine description and examples
            full_description = "Function is part of the browser tool. Helps with searching and browsing the web." + action.description
            if action.examples:
                full_description += " Examples: " + "\n".join(action.examples)

                function_schema.append({
                    "type": "function",
                    "function": {
                        "name": self.function_prefix + action_name,
                        "description": full_description,
                        "parameters": parameters
                    }
                })

        return function_schema

    def to_python_code(self, action):
        """
        Converts the given high-level action string to browsergym-compatible python code.

        Args:
            action: the high-level action to parse.

        Returns:
            Executable python code that performs the action in a browsergym environment.
        """
        highlevel_code = action

        # do the actual parsing and convert each high-level action to
        # the corresponding python function call
        if self.strict:
            function_calls = highlevel_action_parser.parse_string(highlevel_code, parse_all=True)
        else:
            function_calls = highlevel_action_parser.search_string(
                highlevel_code
            )  # allow for multiple matches, skip anything in-between
            function_calls = sum(function_calls)  # unpack multiple matches

        if not function_calls:
            raise ValueError("Received an empty action.")
        elif len(function_calls) > 1 and not self.multiaction:
            raise ValueError("Received a multi-action, only single-actions are allowed.")

        python_code = ""

        # function definitions
        python_code += self.python_includes

        # function calls
        for function_name, function_args in function_calls:
            if function_name not in self.action_set:
                raise NameError(f"Invalid action type '{function_name}'.")
            python_code += (
                    function_name + "(" + ", ".join([repr(arg) for arg in function_args]) + ")\n"
            )

        # return the constructed python code
        return python_code
