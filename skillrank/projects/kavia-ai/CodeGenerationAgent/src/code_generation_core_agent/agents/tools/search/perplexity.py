import logging
import time
import json
from typing import Dict, List, Optional, Any
import os

import litellm
from litellm import completion

from code_generation_core_agent.agents.framework.cost_tracker import CostTracer
from code_generation_core_agent.agents.tools.search.search_base import BaseSearchAPI, SearchResponse, SearchResult


class PerplexitySearchAPI(BaseSearchAPI):
    """
    Implementation of the Perplexity Search API using litellm.
    https://docs.perplexity.ai/
    """

    def __init__(
            self,
            logger: Optional[logging.Logger] = None,
            api_key: Optional[str] = None,
            timeout: int = 30,
            max_results: int = 10,
            safe_search: bool = True,
            model: str = "sonar",  # Default model according to docs
    ):
        """
        Initialize the Perplexity Search API using litellm.

        Args:
            base_path: Base path for the tool
            logger: Logger instance
            api_key: Perplexity API key (required)
            timeout: Timeout for API requests in seconds
            max_results: Maximum number of results to return
            safe_search: Whether to enable safe search filtering
        """
        super().__init__(logger, api_key, timeout, max_results, safe_search)
        self.model = f"perplexity/{model}"  # litellm requires "perplexity/" prefix
        self.cost_tracer = CostTracer()

        if api_key:
            os.environ["PERPLEXITYAI_API_KEY"] = api_key
        else:
            self.logger.warning("Perplexity API key not provided. API calls will fail.")

    def validate_api_key(self) -> bool:
        """
        Validate that the Perplexity API key is valid and working.

        Returns:
            True if the API key is valid, False otherwise
        """
        if not self.api_key:
            return False

        try:
            # Simple query to check if the API key works
            completion(
                model=self.model,
                messages=[{"role": "user", "content": "What is today's date?"}],
                max_tokens=10
            )
            return True
        except Exception as e:
            self.logger.error(f"API key validation failed: {str(e)}")
            return False

    def search(
            self,
            query: str,
            num_results: Optional[int] = None,
            **kwargs
    ) -> SearchResponse:
        """
        Perform a search with the Perplexity API using litellm.

        Args:
            query: Search query string
            num_results: Number of results to return (max sources to include)
            **kwargs: Additional parameters:
                - temperature: Temperature for LLM generation (default: 0.2)
                - max_tokens: Maximum tokens to generate (default: 1024)
                - top_p: Nucleus sampling threshold (default: 0.9)
                - search_recency_filter: Filter by time period (e.g., 'day', 'week')
                - search_domain_filter: List of domains to filter results by

        Returns:
            SearchResponse object containing the search results
        """
        try:
            num_results = num_results or self.max_results
            temperature = kwargs.get("temperature", 0.2)
            max_tokens = kwargs.get("max_tokens", 1024)
            top_p = kwargs.get("top_p", 0.9)

            # Additional search parameters
            search_recency_filter = kwargs.get("search_recency_filter")
            search_domain_filter = kwargs.get("search_domain_filter")

            # Prepare the litellm completion parameters
            # Map any additional params that litellm supports
            extra_params = {}
            if search_recency_filter:
                extra_params["search_recency_filter"] = search_recency_filter
            if search_domain_filter:
                extra_params["search_domain_filter"] = search_domain_filter

            # Enable web search options
            extra_params["web_search_options"] = {"search_context_size": "high"}

            # Make the API request using litellm
            start_time = time.time()
            response = completion(
                model=self.model,
                messages=[{"role": "user", "content": query}],
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p,
                timeout=self.timeout,
                **extra_params
            )
            duration = time.time() - start_time

            self.logger.info(f"Perplexity API request via litellm took {duration:.2f} seconds")

            try:
                self.cost_tracer.add_cost_from_completion_response(
                    source="AiSearch",
                    completion_response=response
                )
                self.logger.info(f"Added cost for Perplexity search query: {query[:50]}...")
            except Exception as e:
                self.logger.warning(f"Failed to track cost: {str(e)}")

            # Parse the response
            return self.parse_response(
                response,
                query=query,
                max_results=num_results
            )
        except Exception as e:
            return self.handle_error(e)

    def parse_response(
            self,
            response: Any,
            query: str,
            max_results: Optional[int] = None,
    ) -> SearchResponse:
        """
        Parse the response from litellm.

        Args:
            response: Response from litellm completion
            query: Original search query
            max_results: Maximum number of results to include

        Returns:
            Parsed SearchResponse object
        """
        results = []
        answer = None
        search_engine = "Perplexity"
        total_results = 0
        if max_results is None:
            max_results = self.max_results

        try:
            # Extract the direct answer
            if hasattr(response, "choices") and response.choices:
                choice = response.choices[0]
                if hasattr(choice, "message") and hasattr(choice.message, "content"):
                    answer = choice.message.content

            # Extract citations if available
            citations = []
            # Try to extract citations from multiple possible locations in the response
            if hasattr(response, "citations"):
                citations = response.citations
            elif hasattr(response, "_response_ms") and isinstance(response._response_ms, dict):
                if "citations" in response._response_ms:
                    citations = response._response_ms["citations"]
                elif "content" in response._response_ms and "web_search_results" in response._response_ms["content"]:
                    # Handle nested structure in response content
                    for result in response._response_ms["content"]["web_search_results"]:
                        if "url" in result:
                            citations.append(result["url"])

            # If we can access the raw response, extract citations from there
            if hasattr(response, "model_response") and response.model_response:
                raw_response = response.model_response
                if isinstance(raw_response, dict):
                    if "citations" in raw_response:
                        citations = raw_response["citations"]
                    elif "choices" in raw_response and raw_response["choices"]:
                        # Try to extract from choices
                        for choice in raw_response["choices"]:
                            if "message" in choice and "tool_calls" in choice["message"]:
                                for tool_call in choice["message"]["tool_calls"]:
                                    if "function" in tool_call and "arguments" in tool_call["function"]:
                                        try:
                                            args = json.loads(tool_call["function"]["arguments"])
                                            if "citations" in args:
                                                citations.extend(args["citations"])
                                        except:
                                            self.logger.warning("Failed to parse tool call arguments")

            self.logger.info(f"Found {len(citations) if citations else 0} citations")

            # Process each citation URL into a search result
            for url in citations[:max_results]:
                # Create a basic result with the URL
                result = SearchResult(
                    title="",  # No title with perplexity
                    url=url,
                    snippet="",  # No snippet available
                    metadata={"source_url": url}
                )
                results.append(result)
                total_results += 1

        except Exception as e:
            self.logger.error(f"Error parsing Perplexity response: {e}", exc_info=True)
            return SearchResponse(
                results=[],
                query=query,
                total_results=0,
                search_engine=search_engine,
                error=f"Failed to parse response: {str(e)}"
            )

        if not results and answer:
            results.append(
                SearchResult(
                    title="Perplexity Answer",
                    url=None,  # No URL for the direct answer
                    snippet=answer[:300] + "..." if len(answer) > 300 else answer,
                    metadata={"full_answer": answer}
                )
            )
            total_results = 1

        return SearchResponse(
            results=results,
            query=query,
            total_results=total_results,
            search_engine=search_engine,
            answer=answer
        )

    def build_search_url(self, query: str, **params) -> str:
        """
        Build the URL for a search request. For litellm, this returns
        a string representation of the model being used.

        Args:
            query: Search query
            **params: Additional parameters

        Returns:
            A string representation of the model being used
        """
        return f"litellm using {self.model}"