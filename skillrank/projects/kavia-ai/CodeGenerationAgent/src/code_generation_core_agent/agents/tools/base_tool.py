import importlib
import inspect
import logging
from collections import deque
import json

import code_generation_core_agent.agents.tools.schemas.decorators as decorators

def create_combined_tool(tools_to_use, base_path, logger, llm=None, executor=None):
    """
    Create a combined tool by combining multiple tools.
    """
    class CombinedTool(*tools_to_use):
        def __init__(self, base_path, logger, llm=None, executor=None):
            super().__init__(base_path, logger, llm, executor)
            
        def update_base_path(self, new_base_path):
            """Update base path for all combined tools that support it"""
            
            # Update base path for any tool instances in tool_set if they exist
            if hasattr(self, 'tool_set'):
                for tool_name, tool_instance in self.tool_set.items():
                    if hasattr(tool_instance, 'update_base_path'):
                        tool_instance.update_base_path(new_base_path)
                        logger.debug(f"Updated base path for {tool_name} to {new_base_path}")

    return CombinedTool(base_path, logger, llm, executor)

class BaseTool(metaclass=decorators.AutoRegisterFunctionsMeta):
    """Base class for all tools ."""

    def __init__(self, base_path, logger=None, llm=None, executor=None):
        self._function_schemas = []
        self.function_mapping = {}
        self._function_schemas_by_tool = {}
        self.project_base_path = base_path  # Store original project base path
        self.base_path = base_path
        if logger is None:
            self.logger = logging.getLogger(self.__class__.__name__)
        else:
            self.logger = logger

        self.recent_calls = deque(maxlen=15)  # Store last 15 calls
        self.max_repetitions = 3
        self.loop_window = 15
        self.llm = llm
        self.executor = executor
        self._construct_function_mapping()

    def update_base_path(self, new_base_path: str):
        """
        Update the base path for container-specific operations
        
        Args:
            new_base_path: New base path to use for operations
        """
        old_base_path = self.base_path
        self.base_path = new_base_path
        self.logger.debug(f"Updated {self.__class__.__name__} base_path from {old_base_path} to: {new_base_path}")
        
        # Call hook for subclasses to handle base path changes
        self._on_base_path_changed(old_base_path, new_base_path)

    def _on_base_path_changed(self, old_base_path: str, new_base_path: str):
        """
        Hook for subclasses to handle base path changes
        Override this method in subclasses if they need to perform
        additional actions when the base path changes
        
        Args:
            old_base_path: Previous base path
            new_base_path: New base path
        """
        # Default implementation does nothing
        pass

    def get_current_base_path(self) -> str:
        """Get the current effective base path"""
        return self.base_path

    def get_project_base_path(self) -> str:
        """Get the original project base path"""
        return self.project_base_path
    
    def reset_to_project_base_path(self):
        """Reset base path to the original project base path"""
        self.update_base_path(self.project_base_path)




    def _construct_function_mapping(self):
        """
        Construct function mapping by combining all function mappings from the class hierarchy.
        This ensures that when we combine multiple tools, all functions are available and registered
        exactly once.
        """

        # Get the method resolution order
        mro = self.__class__.__mro__

        # Initialize the function mapping if it doesn't exist
        if not hasattr(self, 'function_mapping'):
            self.function_mapping = {}

        for current_class in mro:
            if current_class in (BaseTool, object):
                break

            if (hasattr(current_class, 'class_function_mapping') and current_class.__name__
                    in current_class.class_function_mapping):

                new_functions = current_class.class_function_mapping[current_class.__name__]
                # Add the new functions to the existing function mapping
                self.function_mapping.update(new_functions)

    def set_parameter(self, name, value):
        """Set a parameter for the tool."""
        setattr(self, name, value)

    def set_parameters(self, **kwargs):
        """Set multiple parameters at once."""
        for name, value in kwargs.items():
            self.set_parameter(name, value)

    def add_function_schemas(self, schemas: list, tool_name: str):
        for schema in schemas:
            function_name = schema["function"]["name"]
            # Check if the function schema already exists
            if any(existing_schema["function"]["name"] == function_name for existing_schema in self._function_schemas):
                continue  # Skip adding the duplicate schema

            self._function_schemas.append(schema)
            if tool_name not in self._function_schemas_by_tool:
                self._function_schemas_by_tool[tool_name] = []

            self._function_schemas_by_tool[tool_name].append(schema)

    def add_function_schemas_to_existing(self, schemas : list, tool_names : list[str]) -> list:
        existing_functions = []

        missing_functions_map = {}
        missing_schemas = self.get_function_schemas(tool_names)

        def get_function_name_from(schema):
            return schema["function"]["name"]

        for schema in schemas:
            function_name = get_function_name_from(schema)
            existing_functions.append(function_name)

        for schema in missing_schemas:
            function_name = get_function_name_from(schema)
            missing_functions_map[function_name] = schema

        for missing_function_name in missing_functions_map.keys():
            if missing_function_name not in existing_functions:
                schemas.append(missing_functions_map[missing_function_name])

        return schemas

    def get_function_schemas(self, tool_names):
        schemas = []

        for tool_name in tool_names:
            schemas.extend(self._function_schemas_by_tool.get(tool_name, []))
        return schemas

    @property
    def function_schemas(self):
        return self._function_schemas

    def validate_function(self, tool_direct_function_name, **kwargs):
        """
        Validate a function.
        Implemented this method in the child class to validate the function.

        Return True if the function is valid, raise an exception otherwise.
        """
        print("Dummy function to be implemented in the child class")
        return True

    def call_function_old(self, tool_direct_function_name, **function_args):
        """Call a function by name with the given arguments."""
        if tool_direct_function_name not in self.function_mapping:
            unknown_function_error = {
                "status": "ERROR",
                "message": f"Unknown function: {tool_direct_function_name}",
                "error_type": "UnknownFunction",
                "function_name": tool_direct_function_name
            }
            self.logger.error(f"Unknown function: {json.dumps(unknown_function_error)}")
            return unknown_function_error

        function = getattr(self, self.function_mapping[tool_direct_function_name])
        return function(**function_args)

    def function_validator(self, function_name, function_args):

        self.validate_function(function_name, **function_args)


    def function_executor(self, function_name, function_args):
        """Execute a function from the function_mapping with improved loop detection and enhanced logging."""
        # Dump all available functions
        self.logger.info(f"Executing function: {function_name} with arguments: {json.dumps(function_args)}")

        try:
            # Remove 'function_name' from the function_args if it exists.
            function_args.pop('function_name', None)

            result = self.call_function(function_name, **function_args)

            self.logger.info(f"Function {function_name} executed successfully. Output: {json.dumps(result)}")

            # Create a hash of the function call including input and output
            call_hash = self._create_call_hash(function_name, function_args, result)
            self.recent_calls.append(call_hash)

            # Check for loops
            if self._detect_loop():
                loop_error = {
                    "status": "ERROR",
                    "message": f"Loop detected: Similar function calls repeated {self.max_repetitions} times in the last {self.loop_window} calls. Please use the review_progress_and_plan function to analyze the situation and determine the next steps.",
                    "suggestion": "Call review_progress_and_plan function"
                }
                self.logger.warning(f"Loop detected: {json.dumps(loop_error)}")
                self.recent_calls.clear()  # Clear the stored hashes
                return loop_error

            return result
        except TypeError as e:
            import traceback
            print(traceback.format_exc())
            trace = str(traceback.format_exc())
            error_message = str(e)
            error_result = {
                "status": "ERROR",
                "message": f"Invalid argument passed to {function_name}: {error_message}",
                "error_type": "InvalidArgument",
                "function_name": function_name,
                "provided_arguments": function_args
            }
            self.logger.error(f"TypeError in function execution: {json.dumps(error_result)}")
            self.logger.error(f"Trace: {trace}. Funtion: {function_name} Args: {function_args}")
            return error_result
        except Exception as e:

            error_message = str(e)
            # print stack trace
            import traceback
            tool_exception :str = str(traceback.format_exc())
            error_result = {
                "status": "ERROR",
                "message": f"Error in function {function_name}: {error_message}",
                "error_type": "FunctionError",
                "function_name": function_name,
                "provided_arguments": function_args
            }
            self.logger.error(f"Error in function execution: {tool_exception} {json.dumps(error_result)}")
            return error_result

    @staticmethod
    def _create_call_hash(function_name, function_args, result):
        """Create a hash of the function call including input and output."""
        call_data = {
            "function": function_name,
            "args": json.dumps(function_args, sort_keys=True),
            "result": json.dumps(result, sort_keys=True)
        }
        return json.dumps(call_data, sort_keys=True)

    def _detect_loop(self):
        """Detect if a loop is occurring in the recent function calls."""
        if len(self.recent_calls) < self.loop_window:
            return False

        # Count occurrences of each unique call
        call_counts = {}
        for call in self.recent_calls:
            call_counts[call] = call_counts.get(call, 0) + 1

        # Check if any call appears at least max_repetitions times
        return any(count >= self.max_repetitions for count in call_counts.values())


class ToolRegistry:
    """A registry to keep track of all available tools."""

    def __init__(self):
        self.tools = {}
        self.executors = {}

    def register_tool(self, tool_name, tool_class, executor):
        """Register a new tool."""
        if not issubclass(tool_class, BaseTool):
            raise TypeError(f"{tool_class.__name__} must be a subclass of BaseTool")
        self.tools[tool_name] = tool_class
        self.executors[tool_name] = executor

    def get_tool(self, tool_name):
        """Get a tool by name."""
        return self.tools.get(tool_name)

    def get_executor(self, tool_name):
        """Get an executor by tool name."""
        return self.executors.get(tool_name)

    def list_tools(self):
        """List all registered tools."""
        return list(self.tools.keys())



def load_tools_from_module(module_name, tool_registry):
    """Load all tool classes from a module and register them."""
    module = importlib.import_module(module_name)
    for name, obj in inspect.getmembers(module):
        if inspect.isclass(obj) and issubclass(obj, BaseTool) and obj != BaseTool:
            tool_registry.register_tool(name, obj)
