import json
import os
import shlex
import tempfile

import tempfile
import os
import json
import shlex
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JavaScriptValidationError(Exception):
    pass


def validate_js(self, filepath):
    node_script = """
        const fs = require('fs');
        const acorn = require('acorn');
        const walk = require('acorn-walk');

        function analyzeCode(code) {
            const issues = [];
            let ast;

            try {
                ast = acorn.parse(code, { 
                    ecmaVersion: 'latest',
                    sourceType: 'module',
                    locations: true
                });
            } catch (error) {
                return [{
                    type: 'SyntaxError',
                    message: error.message,
                    line: error.loc.line,
                    column: error.loc.column
                }];
            }

            // ... (rest of the analysis code remains the same)

            return issues;
        }

        try {
            const file_path = process.argv[2];
            const code = fs.readFileSync(file_path, 'utf-8');
            const issues = analyzeCode(code);
            console.log(JSON.stringify({ success: true, issues }));
        } catch (error) {
            console.log(JSON.stringify({ success: false, error: error.message }));
        }
    """

    temp_script_path = None
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as temp_script:
            temp_script.write(node_script)
            temp_script_path = temp_script.name

        escaped_filepath = shlex.quote(filepath)
        escaped_temp_script = shlex.quote(temp_script_path)
        cmd = f"node {escaped_temp_script} {escaped_filepath}"

        try:
            output = self._run_docker_command(cmd)
        except subprocess.CalledProcessError as e:
            logger.error(f"Error executing Node.js script: {e}")
            raise JavaScriptValidationError("Failed to execute JavaScript validation script")

        try:
            result = json.loads(output)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON output: {e}")
            raise JavaScriptValidationError("Failed to parse JavaScript validation result")

        if not result.get('success', False):
            logger.error(f"JavaScript validation failed: {result.get('error', 'Unknown error')}")
            raise JavaScriptValidationError(f"JavaScript validation failed: {result.get('error', 'Unknown error')}")

        issues = result.get('issues', [])
        if not issues:
            return True, None
        else:
            error_messages = []
            for issue in issues:
                if 'line' in issue and 'column' in issue:
                    error_messages.append(
                        f"{issue['type']} at line {issue['line']}, column {issue['column']}: {issue['message']}")
                else:
                    error_messages.append(f"{issue['type']}: {issue['message']}")
            return False, "\n".join(error_messages)

    except Exception as e:
        logger.exception("Unexpected error during JavaScript validation")
        raise JavaScriptValidationError(f"Unexpected error during JavaScript validation: {str(e)}")

    finally:
        if temp_script_path and os.path.exists(temp_script_path):
            try:
                os.unlink(temp_script_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary script file: {e}")


def validate_file_data(self, filepath):
    _, file_extension = os.path.splitext(filepath)
    if file_extension.lower() == '.js':
        try:
            is_valid, error_message = self.validate_js(filepath)
            if not is_valid:
                logger.warning(f"JavaScript validation failed for {filepath}: {error_message}")
                print("Validation error: ", error_message)
                return False, error_message
        except JavaScriptValidationError as e:
            print("Validation error: ", str(e))
            logger.error(f"JavaScript validation error for {filepath}: {str(e)}")
            return False, str(e)
    return True, None

