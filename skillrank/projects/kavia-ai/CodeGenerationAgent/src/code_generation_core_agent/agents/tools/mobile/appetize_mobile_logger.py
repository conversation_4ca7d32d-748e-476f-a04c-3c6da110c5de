from code_generation_core_agent.agents.setup_logger import setup_logger

class AppetizeSessionLogger:
    """Sync Appetize logs"""

    def __init__(self, execution_base_path: str):
        """
        Initialize the Appetize session logger

        Args:
            execution_base_path: Base path for the Appetize logger
        """
        self.logger = setup_logger("AppetizeLogs", execution_base_path)
        self.logger.info("Appetize session logger initialized.")

    def sync_logs(self, logs):
        for log in logs:
            self.logger.info(log)