# Pre-checks configuration
include:
  - common.yaml

checks:
  - name: safe_working_directory
    code: |
      def is_safe_working_directory(working_dir, base_path, command):
          import os
          allowed_commands = [
              "mkdir", "ls", "cat", "echo", 
              "pwd", "date", "touch", "cp", "mv",
              "chmod", "chown",
              "grep", "find", "sort", "uniq",
              "head", "tail", "wc", "diff",
              "tar", "gzip", "gunzip", "zip", "unzip",
              "sed", "awk", "cut", "tr",
              "df", "du", "free", "top",
              "ps", "kill", "which", "whereis",
              "uname", "hostname", "whoami",
              "tee", "basename", "dirname", "git",
              "curl", "wget", "scp", "rsync",
              "cd", "npx", "npm", "node", "yarn", "python", "pip", "apt-get",
              "emulator", "adb", "gradle", "gradlew",
              "java", "flutter", "bash", "python3", "pip3", "pipenv", "pipenv3",
              # Test runners and build tools
              "pytest", "unittest", "nose2", "coverage",  # Python
              "jest", "mocha", "karma", "jasmine",       # JavaScript
              "mvn", "maven",                            # Java Maven
              "go", "gotestsum", "gotest",              # Go
              "cargo", "rustc",                          # Rust
              "rspec", "rake", "bundle",                 # Ruby
              "dotnet", "nunit", "xunit", "mstest",     # .NET
              "composer", "phpunit", "artisan",          # PHP
              "swift", "xcodebuild",                     # Swift
              "cmake", "ctest", "make",                  # C/C++
              "sbt", "scalac", "scala"                   # Scala
          ]
          
          command_start = command.strip().split()[0].lower()
          if command_start in allowed_commands:
              return True
          
          return os.path.realpath(working_dir) != os.path.realpath(base_path)
      
      result = is_safe_working_directory(value, base_path, command)
    error_message: "Argument '{arg_name}' is set to '{value}'. working_directory must be different from {base_path}
     unless the command is in the list of allowed safe commands."

  - name: prevent_dangerous_rm
    code: |
      def is_safe_command(command):
          import re
          dangerous_patterns = [
              r"\brm\s+-[^-]*rf\b",  # Matches rm with -rf flags in any order
              r"\bsudo\s+rm\s+-[^-]*rf\b"  # Matches sudo rm with -rf flags in any order
          ]
          return not any(re.search(pattern, command, re.IGNORECASE) for pattern in dangerous_patterns)
      
      result = is_safe_command(command)
    error_message: "The command '{value}' contains a dangerous 'rm -rf' or 'sudo rm -rf' pattern, which is not allowed. 
    Consider reusing the current directory tree."

functions:
  execute_immediate_return_command:
    command:
      - name: length
        min: 1
        max: 3000
      - name: prevent_dangerous_rm

    working_directory:
      - name: safe_working_directory
        base_path: '{base_path}'

  start_long_running_process:
    command:
      - name: length
        min: 1
        max: 3000
      - name: prevent_dangerous_rm

    working_directory:
      - name: safe_working_directory
        base_path: '{base_path}'

