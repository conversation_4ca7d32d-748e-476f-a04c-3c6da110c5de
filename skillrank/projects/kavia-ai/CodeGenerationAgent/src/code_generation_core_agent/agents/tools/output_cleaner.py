import re
from ...config import config

class OutputCleaner:
    def __init__(self):
        self.ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        self.repeating_chars = re.compile(r'(.)\1{3,}')

    def clean(self, text, abridge=True):
        text = self.ansi_escape.sub('', text)
        text = self.repeating_chars.sub(r'\1\1\1', text)
        text = text.strip()
        if abridge:
            verbosity_limit = config.getint("TOOLS", "return_size_limit")
            text_len = len(text)
            if text_len > verbosity_limit:
                separator = '\n\n...\n\n'
                verbosity_limit = (verbosity_limit - len(separator)) // 2
                print("Warning: truncating long shell command output to fit verbosity limit.")
                text = text[:verbosity_limit] + separator + text[(text_len - verbosity_limit):]
        return text