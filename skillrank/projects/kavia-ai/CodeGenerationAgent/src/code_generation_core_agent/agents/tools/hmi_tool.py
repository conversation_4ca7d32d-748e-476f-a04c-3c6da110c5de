# This is a Human-to-Machine Interface (HMI) agent.
import json
import os.path

from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function

import threading

# mutex for protecting the Question list

global_lock = threading.Lock()


class HMITool(BaseToolInterface):
    def get_tool_name(self):
        return "HMITool"

    def _load_questions(self):
        if os.path.exists(self.question_file_path):
            with open(self.question_file_path, 'r') as f:
                self.questions = json.load(f)
                # Get the highest question ID
                # Convert the keys to integers
                if self.questions:
                    self.next_question_id = max(int(key) for key in self.questions.keys()) + 1
                else:
                    self.next_question_id = 1

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        """
        Initializes the HMI Agent.
        """
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.questions = {}
        self.next_question_id = 1
        self.user_query_callback = callback_functions.get("user_query_callback", None)
        self.output_callback = None
        self.question_file_path = os.path.join(base_path, "user_input.json")
        self._load_questions()
        if self.questions and self.user_query_callback:
            self.user_query_callback(self.questions)

        self.logger = logger


    def set_user_input(self, user_input):
        """
        Returns true if the user input was ingested successfully.
        """
        # Check if the user input is a dictionary that can be converted

        global global_lock
        with global_lock:
            if not isinstance(user_input, dict):
                return False

            for key in user_input:
                if not all(field in user_input[key] for field in ["text", "status", "answer"]):
                    return False

            for key in user_input:
                if key in self.questions:
                    self.questions[key]["answer"] = user_input[key]["answer"]
                    self.questions[key]["status"] = user_input[key]["status"]

            self._process_questions()
            return True
    def set_parameters(self, **kwargs):
        super().set_parameters(**kwargs)

        if 'user_query_callback' in kwargs:
            self.user_query_callback = kwargs['user_query_callback']
            if self.questions:
                self.user_query_callback(self.questions)

    def set_output_callback(self, callback):
        self.output_callback = callback

    def _process_questions(self):
        with open(self.question_file_path, 'w') as f:
            json.dump(self.questions, f, indent=4)

        if self.user_query_callback:
            self.user_query_callback(self.questions)

    def _send_output(self, message):
        if self.output_callback:
            self.output_callback(message)

    @register_function(
        description="Adds a new question to the list and assigns it a unique ID. "
                    "Any interaction with the user MUST be done via this function at all times."
                    "Use this function if you need user confirmation or input for a specific task."
                    "Example: HMITool_ask_question_from_user('What is your OPENAI_API_KEY?')",
        parameters= {
        "type": "object",
        "properties": {
            "question_text": {"type": "string", "description": "The text of the question to add."},
        },
        "required": ["question_text"]
        }
    )
    def ask_question_from_user(self, question_text):
        global global_lock
        with global_lock:

            # Check if question already exists
            for question_id, question in self.questions.items():
                if question["text"] == question_text:
                    return {"status": "ERROR",
                            "message": f"Question already exists with ID {question_id}",
                            "questions": self.questions
                            }

            question_id = self.next_question_id
            self.next_question_id += 1
            self.questions[question_id] = {
                "text": question_text,
                "status": "pending",
                "answer": ""
            }

            self._process_questions()

            self._send_output(f"Added question (ID: {question_id}): {question_text}\n")
            return {"status": "SUCCESS",
                    "question_id": question_id,
                    "questions": self.questions
                    }

    @register_function(
        description = "Marks a specific question as answered.",
        parameters =  {
        "type": "object",
        "properties": {
            "question_id": {"type": "integer",
                            "description": "The unique ID of the question to mark as answered. A question is answered if there is non-empty valid answer. Example: hmi_mark_question_answered(1)"},
        },
        "required": ["question_id"]
        }
    )
    def mark_question_answered(self, question_id):
        global global_lock
        with global_lock:
            if question_id not in self.questions:
                return {"status": "ERROR", "message": f"No question found with ID {question_id}"}

            self.questions[question_id]["status"] = "answered"
            self._process_questions()
            self._send_output(f"Marked question (ID: {question_id}) as answered\n")
        return {"status": "SUCCESS", "question_id": question_id}

    @register_function(
        description = "Marks a response to a question as incomplete and provides a reason. Example: hmi_mark_response_incomplete(1, 'The response is missing a required field.')",
        parameters = {
        "type": "object",
        "properties": {
            "question_id": {"type": "integer",
                            "description": "The unique ID of the question with an incomplete response."},
            "rejection_reason": {"type": "string",
                                 "description": "The reason why the response is considered incomplete."},
        },
        "required": ["question_id", "rejection_reason"]
        }
    )
    def mark_response_incomplete(self, question_id, rejection_reason):
        global global_lock
        with global_lock:
            if question_id not in self.questions:
                return {"status": "ERROR", "message": f"No question found with ID {question_id}"}

            self.questions[question_id]["status"] = "incomplete"
            self.questions[question_id]["reason"] = rejection_reason
            self._process_questions()
            self._send_output(f"Marked response for question (ID: {question_id}) as incomplete. Reason: {rejection_reason}\n")

            return {"status": "SUCCESS", "question_id": question_id}

    @register_function(
        description = "Removes a specific question from the list.",
        parameters = {
        "type": "object",
        "properties": {
            "question_id": {"type": "integer",
                            "description": "The unique ID of the question to remove. Duplicated old questions can be removed using this. Example: hmi_remove_question(1)"},
        },
        "required": ["question_id"]
        }
    )
    def remove_question(self, question_id):
        with global_lock:
            if question_id not in self.questions:
                return {"status": "ERROR", "message": f"No question found with ID {question_id}"}

            del self.questions[question_id]
            self._process_questions()
            self._send_output(f"Removed question (ID: {question_id})\n")
            return {"status": "SUCCESS", "question_id": question_id}

    @register_function(
        description = "Get all previous questions and their statuses. Takes no parameters. Example: hmi_get_all_questions(True)",
        parameters = {
        "type": "object",
        "properties": {
            "can_continue": {"type": "boolean",
                            "description": "Indicate if LLM can continue without answering all questions."},
        },
        "required": [ "can_continue" ]
        }
    )
    def get_all_questions(self, can_continue):
        """
        Get all previous questions and their statuses.
        :param can_continue: A boolean value indicating whether the code generation can continue.
        """
        with global_lock:
            self._send_output("Listing current questions\n")
            return {"status": "SUCCESS", "questions": self.questions}

    def __del__(self):
        # Clean up any resources if needed
        pass