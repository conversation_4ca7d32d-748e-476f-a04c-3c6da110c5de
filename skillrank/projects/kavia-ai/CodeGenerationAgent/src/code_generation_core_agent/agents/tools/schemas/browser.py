# Browser schema

function_schema = [
            {
                "type": "function",
                "function": {
                    "name": "initialize_browser",
                    "description": "Initialize a headless Chrome browser.",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_navigate_to_url",
                    "description": "Navigate to a specified URL.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "The URL to navigate to.",
                            }
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_click_element",
                    "description": "Click on a specified element.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "element_id": {
                                "type": "string",
                                "description": "The ID of the element to click.",
                            }
                        },
                        "required": ["element_id"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_scroll_page",
                    "description": "Scroll the page by a specified amount.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "x": {
                                "type": "integer",
                                "description": "The amount to scroll horizontally.",
                            },
                            "y": {
                                "type": "integer",
                                "description": "The amount to scroll vertically.",
                            }
                        },
                        "required": ["x", "y"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_capture_screenshot",
                    "description": "Capture a screenshot of the current page.",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_send_keys",
                    "description": "Send keyboard input to the active element or a specified element.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "keys": {
                                "type": "string",
                                "description": "The keys to send.",
                            },
                            "element_id": {
                                "type": "string",
                                "description": "The ID of the element to send keys to (optional).",
                            }
                        },
                        "required": ["keys"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_send_key_event",
                    "description": "Send a specific key event.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "key": {
                                "type": "string",
                                "description": "The key to send (e.g., 'ENTER', 'TAB', 'ARROW_DOWN').",
                            }
                        },
                        "required": ["key"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_get_input_fields",
                    "description": "Get a list of all input fields on the page.",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_move_mouse",
                    "description": "Move the mouse to specified coordinates.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "x": {
                                "type": "integer",
                                "description": "The x-coordinate to move the mouse to.",
                            },
                            "y": {
                                "type": "integer",
                                "description": "The y-coordinate to move the mouse to.",
                            }
                        },
                        "required": ["x", "y"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_hover_element",
                    "description": "Hover the mouse over a specified element.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "element_id": {
                                "type": "string",
                                "description": "The ID of the element to hover over.",
                            }
                        },
                        "required": ["element_id"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_enter_text",
                    "description": "Enter text into a specified input field or the first visible input field.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "The text to enter into the input field.",
                            },
                            "element_id": {
                                "type": "string",
                                "description": "The ID of the input element (optional). If not provided, the first visible input field will be used.",
                            }
                        },
                        "required": ["text"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_get_visible_accessible_elements",
                    "description": "Get all visible and accessible elements on the current page.",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "browser_get_page_dimensions",
                    "description": "Get the dimensions of the current page and viewport.",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            },
        ]

