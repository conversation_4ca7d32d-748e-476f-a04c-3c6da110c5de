import json
import time
import os
import psutil

from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator, SummarizerType, parse_json_with_cleanup
from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType
from logging import Logger
from pathlib import Path
from typing import List, Dict, Any, Optional, Optional
import threading


from code_generation_core_agent.config import config

class ProjectSetupState:
    """Singleton to track and manage project setup state."""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ProjectSetupState, cls).__new__(cls)
                cls._instance.universal_init_invoked = False
        return cls._instance

    def set_universal_init_invoked(self):
        with self.__class__._lock:
            self.universal_init_invoked = True

    def is_universal_init_invoked(self) -> bool:
        return self.universal_init_invoked

RUN_IN_EXECUTOR = True

SUPPORTED_FRAMEWORKS_CONFIGS = {
    "angular": {
        "parameters": "typescript=true",
        "template_class": "AngularTemplate"
    },
    "astro": {
        "parameters": "typescript=true",
        "template_class": "AstroTemplate"
    },
    "nextjs": {
        "parameters": "",
        "template_class": "NextJSTemplate"
    },
    "qwik": {
        "parameters": "",
        "template_class": "QwikTemplate"
    },
    "nuxt": {
        "parameters": "typescript=true",
        "template_class": "NuxtTemplate"
    },
    "react": {
        "parameters": "typescript=true,styling_solution=styled-components",
        "template_class": "ReactTemplate"
    },
    "reactnative": {
        "parameters": "",
        "template_class": "ReactNativeTemplate"
    },
    "slidev": {
        "parameters": "",
        "template_class": "SlidevTemplate"
    },
    "remix": {
        "parameters": "typescript=true,styling_solution=tailwind",
        "template_class": "RemixTemplate"
    },
    "remotion": {
        "parameters": "",
        "template_class": "RemotionTemplate"
    },
    "svelte": {
        "parameters": "",
        "template_class": "SvelteTemplate"
    },
    "typescript": {
        "parameters": "",
        "template_class": "TypeScriptTemplate"
    },
    "vite": {
        "parameters": "typescript=true",
        "template_class": "ViteTemplate"
    },
    "vue": {
        "parameters": "typescript=true",
        "template_class": "VueTemplate"
    },
    "flutter": {
        "parameters": "",
        "template_class": "FlutterTemplate"
    },
    "android": {
        "parameters": "",
        "template_class": "AndroidTemplate"
    },
    "express": {
        "parameters": "typescript=false",
        "template_class": "ExpressTemplate"
    },
    "django": {
        "parameters": "",
        "template_class": "DjangoTemplate"
    },
    "dotnet": {
        "parameters": "",
        "template_class": "DotNetTemplate"
    },
    "fastapi": {
        "parameters": "",
        "template_class": "FastAPITemplate"
    },
    "flask": {
        "parameters": "",
        "template_class": "FlaskTemplate"
    },
    "springboot": {
        "parameters": "",
        "template_class": "SpringBootTemplate"
    },
    "kotlin": {
        "parameters": "",
        "template_class": "KotlinTemplate"
    },
    "lightningjs":{
        "parameters": "",
        "template_class": "LightningjsTemplate"
    },
    "mongodb": {
        "parameters": "database_name=myapp,database_user=appuser,database_password=dbuser123",
        "template_class": "MongoDBTemplate"
    },
    "mysql": {
        "parameters": "database_name=myapp,database_user=appuser,database_password=dbuser123",
        "template_class": "MySQLTemplate"
    },
    "postgresql": {
        "parameters": "database_name=myapp,database_user=appuser,database_password=dbuser123",
        "template_class": "PostgreSQLTemplate"
    },
    "sqlite":{
        "parameters": "database_name=myapp.db",
        "template_class": "SQLiteTemplate"
    }
    # Add new languages here with their specific parameters and template classes
    # "python": {
    #     "parameters": "use_poetry=true,include_tests=true",
    #     "template_class": "PythonTemplate"
    # },
}


# -- Frameworks data retrieval -----------------------------------------------------------------

class FrameworkData:
    """
    Class to store framework configuration data loaded from .init directory files.
    """
    run_relative_file_path = '.init/.init-run-tool'
    build_relative_file_path = '.init/.init-build-cmd'
    dependencies_relative_file_path = '.init/.init-install-dependencies'
    manifest_relative_path = '../.project_manifest.yaml'
    generate_openapi_relative_path = '.init/.init-generate-openapi'

    def __init__(self, project_path: str, logger: Logger, container_name: str = ''):
        self.project_path = Path(project_path)
        self.logger = logger
        self.container_name = container_name

        self.run_command: Optional[str] = None
        self.build_command: Optional[str] = None
        self.install_dependencies_command: Optional[str] = None
        self.working_directory: Optional[str] = None
        self.generate_openapi_command: Optional[str] = None
        
        self._load_all_configs()


    def _load_config(self, file_path: str) -> Optional[dict]:
        """Load and parse a JSON configuration file."""
        config_path = self.project_path / file_path
        
        try:
            if not config_path.exists():
                self.logger.warning(f"Project not initialized, no {file_path} file found at {config_path}")
                return None
            
            with open(config_path, 'r') as file:
                return json.load(file)
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing JSON from {file_path}: {e}")
            return None

        except Exception as e:
            self.logger.error(f"Unexpected error reading {file_path}: {e}")
            return None
    
    def _load_all_configs(self):
        """Load all configuration files and populate instance attributes."""

        # Load run configuration
        if not self.run_command or not self.working_directory: # This can be loaded from manifest
            run_config = self._load_config(FrameworkData.run_relative_file_path)
            if run_config:
                self.run_command = run_config.get("command")
                working_dir = run_config.get("working_directory")
                if working_dir != '':
                    self.working_directory = working_dir
                if not self.run_command:
                    self.logger.warning(f"No run command found in {FrameworkData.run_relative_file_path} file")
        
        # Load build configuration
        if not self.build_command or not self.working_directory: # This can be loaded from manifest
            build_config = self._load_config(FrameworkData.build_relative_file_path)
            if build_config:
                self.build_command = build_config.get("command")
                working_dir = run_config.get("working_directory")
                if working_dir != '':
                    self.working_directory = working_dir
                if not self.build_command:
                    self.logger.warning(f"No build command found in {FrameworkData.build_relative_file_path} file")
        
        # Load install dependencies configuration
        if not self.install_dependencies_command or not self.working_directory: # This can be loaded from manifest
            deps_config = self._load_config(FrameworkData.dependencies_relative_file_path)
            if deps_config:
                self.install_dependencies_command = deps_config.get("command")
                working_dir = run_config.get("working_directory")
                if working_dir != '':
                    self.working_directory = working_dir
                if not self.install_dependencies_command:
                    self.logger.warning(f"No install command found in {FrameworkData.dependencies_relative_file_path} file")

        # Load openapi generation configuration
        if not self.generate_openapi_command or not self.working_directory:
            openapi_config = self._load_config(FrameworkData.generate_openapi_relative_path)
            if openapi_config:
                self.generate_openapi_command = openapi_config.get("command")
                working_dir = openapi_config.get("working_directory")
                if working_dir != '':
                    self.working_directory = working_dir
                if not self.generate_openapi_command:
                    self.logger.warning(f"No openapi generation command found in {FrameworkData.generate_openapi_relative_path}")
    
    def reload(self):
        """Reload all configurations from disk."""
        self._load_all_configs()


# -- Backward compatibility wrapper functions --------------------------------------------------

def get_framework_run_info(project_path: str, logger: Logger, container_name = '') -> tuple[Optional[str], Optional[str]]:
    """Get the run command and working directory for a project."""
    fw_data = FrameworkData(project_path, logger, container_name)
    return fw_data.run_command, fw_data.working_directory

def run_command_template_to_command(run_command_template: str, port: str | int, host: str, framework: str, executor):
    """
    Convert a run command template to a command with port and host.
    """
    # Case 1: It has placeholders
    if "<port>" in run_command_template and "<host>" in run_command_template:
        return run_command_template.replace("<port>", str(port)).replace("<host>", host)
    
    elif "<port>" in run_command_template:
        return run_command_template.replace("<port>", str(port))
    
    elif "<host>" in run_command_template:
        return run_command_template.replace("<host>", host)

    # Case 2: It already has flags suggesting host/port configuration
    if any(flag in run_command_template for flag in ["--", ":", "="]):
        return run_command_template

    # Case 3: Use universalinit
    command = (
        f"uniinit "
        f"--get-run-command "
        f"--type {framework} "
    ).strip()

    output, return_code = executor.run_one_shot_command(command)
    if return_code != 0:
        raise RuntimeError(f"Error obtaining command: {output}")

    output = json.loads(output)
    return output["run_command"].replace("<port>", str(port)).replace("<host>", host)

def get_framework_build_info(project_path: str, logger: Logger, container_name = '') -> tuple[Optional[str], Optional[str]]:
    """Get the build command and working directory for a project."""
    fw_data = FrameworkData(project_path, logger, container_name)
    return fw_data.build_command, fw_data.working_directory

def get_framework_install_dependencies_info(project_path: str, logger: Logger, container_name = '') -> tuple[Optional[str], Optional[str]]:
    """Get the install dependencies command and working directory for a project."""
    fw_data = FrameworkData(project_path, logger, container_name)
    return fw_data.install_dependencies_command, fw_data.working_directory

def get_generate_openapi_info(project_path: str, logger: Logger, container_name = '') -> tuple[Optional[str], Optional[str]]:
    """Get the generate openapi command for a project"""
    fw_data = FrameworkData(project_path, logger, container_name)
    return fw_data.generate_openapi_command, fw_data.working_directory


# -- Universalinit startup ---------------------------------------------------------------------

def _write_initialization_files(logger, output: str, project_root_path: str, executor: ExecutorBase) -> bool:
    """
    Extracts JSON configuration from command output and writes initialization files using executor commands.

    Args:
        logger: Logger instance
        output: String containing command output with JSON after [OUTPUT] marker
        project_root_path: Path to the project root directory (parent of workspace)
        executor: ExecutorBase instance to run commands

    Returns:
        bool: True if files were created successfully, False otherwise
    """
    try:
        # Extract JSON part after [OUTPUT]
        json_start = output.find('[OUTPUT]')
        if json_start == -1:
            logger.error("No [OUTPUT] marker found in command output")
            return False

        json_content = output[json_start + len('[OUTPUT]'):].strip()
        config = json.loads(json_content)

        if not config.get('template_config'):
            logger.error("No template configuration found in output JSON")
            return False

        template_config = config['template_config']

        parent_dir = Path(project_root_path).parent

        files_to_create = {
            '.init/.init-env': template_config.get('env_config', {}).get('environment_initialized', ''),
            '.init/.init-files': '\n'.join(template_config.get('init_files', [])),
            '.init/.init-minimal': template_config.get('init_minimal', ''),
            FrameworkData.run_relative_file_path: json.dumps(template_config.get('run_tool', {}), indent=4),
            '.init/.init-style': template_config.get('init_style', ''),
            '.init/.init-test-tool': json.dumps(template_config.get('test_tool', {}), indent=4),
            FrameworkData.build_relative_file_path: json.dumps(template_config.get('build_cmd', {}), indent=4),
            FrameworkData.dependencies_relative_file_path: json.dumps(template_config.get('install_dependencies', {}), indent=4),
            '.init/.linter.sh': template_config.get('linter_script', ''),
            FrameworkData.generate_openapi_relative_path: json.dumps(template_config.get('openapi_generation', {}), indent=4)
        }

        for filename, content in files_to_create.items():
            output, return_code = executor.run_one_shot_command(f"mkdir -p {parent_dir}/.init")
            if return_code != 0:
                logger.error(f"Error creating {parent_dir}/.init directory: {output}")
                return False

            file_path = parent_dir / filename
            
            # Create file using echo and redirection
            cmd = f"echo '{content}' > {file_path}"
            output, return_code = executor.run_one_shot_command(cmd)

            if return_code != 0:
                logger.error(f"Error writing {filename}: {output}")
                return False

            # Make .init/linter.sh executable if needed
            if filename == '.init/.linter.sh':
                chmod_cmd = f"chmod 755 {file_path}"
                output, return_code = executor.run_one_shot_command(chmod_cmd)

                if return_code != 0:
                    logger.error(f"Error making {filename} executable: {output}")
                    return False

        return True

    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON from output: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error in _write_initialization_files: {e}")
        return False

def validate_and_normalize_framework(item: Dict[str, Any], platform: str) -> Optional[str]:
    """
    Validate and normalize framework based on item configuration and platform.
    This function extracts the common logic used in framework validation.
    
    Args:
        item: Dictionary containing component/container information
        platform: Platform type (e.g., 'web', 'backend', 'generic')
        
    Returns:
        Normalized framework name if supported, None otherwise
    """
    try:
        language = item.get("language", "unknown").lower()
        framework = item.get("framework", language).lower()

        # Apply the same logic as _initialize_component
        if framework in ["default", "not supported"]:
            framework = language

        # Check if framework is supported in SUPPORTED_FRAMEWORKS_CONFIGS
        if framework in SUPPORTED_FRAMEWORKS_CONFIGS:
            return framework
        else:
            return None
            
    except Exception as e:
        return None

def _initialize_component(item: Dict[str, Any], project_root_path: str,
                          executor: ExecutorBase,
                          logger, framework, platform) -> Optional[str]:
    """
    Initialize a single component based on its language using the executor.
    
    Args:
        item: Dictionary containing component information
        project_root_path: Path to the project root
        executor: Executor instance to run commands
        logger: Logger instance
        
    Returns:
        Component name if initialization was successful, None otherwise
    """
    try:
        name = item.get("container_name") or item.get("component_name")

        # Use shared validation function
        normalized_framework = validate_and_normalize_framework(item, platform)
        if normalized_framework is None:
            logger.error(f"Unsupported framework: {framework}")
            return None
            
        # Get parameters
        params = SUPPORTED_FRAMEWORKS_CONFIGS[normalized_framework]["parameters"]
        param_string = f"--parameters {params}" if params else ""
        name = name.replace(" ", "_")

        project_output_path = Path(project_root_path, name)

        # Build the command
        cmd = (
            f"mkdir -p {project_root_path} && "
            f"cd {project_root_path} && "
            f"uniinit "
            f"--name {name} "
            f"--type {normalized_framework} "
            f"--author 'John Doe' "
            f"--output {project_output_path} "
            f"{param_string}"
        ).strip()

        output, return_code = executor.run_one_shot_command("pip freeze | grep universalinit")
        logger.info(f"Initializing project {name} - universalinit version: {output.split('==')[-1].strip()} - Framework: {normalized_framework} - cmd: {cmd}")

        # Execute the command
        output, return_code = executor.run_one_shot_command(cmd)
        ProjectSetupState().set_universal_init_invoked()

        if return_code == 0:
            logger.info(f"Uniinit output: {output}")
            if _write_initialization_files(logger, output, project_output_path, executor):
                logger.info(f"Project {name} initialization successful")
                return name
            else:
                logger.error(f"Project {name} initialization failed while creating init files")
                return None
        else:
            logger.error(f"Project {name} initialization failed: {output}")
            return None
            
    except Exception as e:
        logger.error(f"Error initializing component {item.get('component_name')}: {e}")
        return None

def _setup_project_with_executor(components: List[Dict[str, Any]], project_root_path: str, 
                                executor: ExecutorBase, logger, framework, platform) -> List[str]:
    """
    Sets up projects using the executor and CLI tool.
    """
    # Initialize each component and filter out failed initializations (None results)
    results = [_initialize_component(item, project_root_path, executor, logger, framework, platform) 
              for item in components]
    
    return [r for r in results if r is not None]

def setup_project(work_item: str, project_root_path: str, executor: ExecutorBase,
                  logger, framework, platform) -> List[Dict[str, Any]]:
    """
    Sets up one or more projects based on the work item description.
    Uses either direct initialization or executor-based initialization based on RUN_IN_EXECUTOR flag.
    """
    wait_for_fs : bool = config.getboolean("SYSTEM", "universal_init_wait_for_fs")


    # Wait for filesystem lock if enabled
    if wait_for_fs:
        wait_for_filesystem_lock(project_root_path, logger)

    # Check if .init/.init-build-cmd is present in the project directory and return if it is
    if Path(project_root_path, FrameworkData.build_relative_file_path).exists():
        logger.info(f"universalsetup: project already initialized at {project_root_path}")
        return [work_item.get("component_name") or work_item.get("container_name")]

    try:
        components = [work_item]
        return _setup_project_with_executor(components, project_root_path,
                                            executor, logger, framework=framework, platform=platform)


    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"Error in setup_project: {e}")
        return []


# -- Universalinit state management helpers ----------------------------------------------------

def wait_for_project_setup(project_root_path: str, timeout: int = 300, logger = None, post_process_status_file = "post_process_status.lock"):
    """
    Waits for project setup to complete by checking for the post_process_status.lock file.
    
    Args:
        project_root_path: Path to the project root directory
        timeout: Maximum time to wait in seconds (default: 300 seconds)
        logger: Optional logger instance
        
    Returns:
        True if setup completed successfully
        
    Raises:
        TimeoutError: If the setup process doesn't complete within the timeout period
        RuntimeError: If the setup process fails
    """
    
    if not ProjectSetupState().is_universal_init_invoked():
        logger.info("Project setup not invoked, skipping wait for setup completion")
        return

    # First, read the .init/.init-build-cmd file to get the working directory
    init_build_cmd_path = Path(project_root_path) / FrameworkData.build_relative_file_path
    if not init_build_cmd_path.exists():
        error_msg = f"Cannot find {FrameworkData.build_relative_file_path} file in {project_root_path}"
        if logger:
            logger.error(error_msg)
        return
    
    try:
        build_config = json.loads(init_build_cmd_path.read_text())
        working_directory = build_config.get("working_directory")
        
        if not working_directory:
            error_msg = f"Working directory not found in {FrameworkData.build_relative_file_path} file"
            if logger:
                logger.error(error_msg)
            raise ValueError(error_msg)
            
        # Check for lock file in the working directory
        lock_file_path = Path(working_directory) / post_process_status_file
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if lock_file_path.exists():
                status = lock_file_path.read_text().strip()
                
                if status == "SUCCESS":
                    if logger:
                        logger.info("Project setup completed successfully")
                    return True
                elif status == "FAILED":
                    error_msg = "Project setup failed according to lock file"
                    if logger:
                        logger.error(error_msg)
                    raise RuntimeError(error_msg)
                    
                # If status is "RUNNING" or something else, keep waiting
            
            # Sleep before checking again
            time.sleep(1)
            
        # If we get here, we've timed out
        error_msg = f"Timeout waiting for project setup to complete after {timeout} seconds"
        if logger:
            logger.error(error_msg)
        raise TimeoutError(error_msg)
        
    except json.JSONDecodeError:
        error_msg = f"Invalid JSON in {FrameworkData.build_relative_file_path} file at {init_build_cmd_path}"
        if logger:
            logger.error(error_msg)
        raise ValueError(error_msg)


def wait_for_filesystem_lock(project_root_path: str, logger: Logger = None) -> None:
    """
    Wait for filesystem lock to be released by checking for .filesystem.lock files.
    
    Args:
        project_root_path: Path to the project root directory
        logger: Optional logger instance for logging
        
    This function:
    1. Checks the project_root_path for .filesystem.lock file
    2. If not found, checks parent directories until found
    3. If found, checks if PID in the lock file exists on the system
    4. If PID exists, waits until PID no longer exists or file is deleted
    """
    lock_filename = ".filesystem.lock"
    
    # Start from the project root path and check upwards
    current_path = Path(project_root_path).resolve()
    
    # Find the lock file by checking current path and parents
    lock_file_path = None
    while current_path != current_path.parent:  # Stop at root directory
        potential_lock_file = current_path / lock_filename
        if potential_lock_file.exists():
            lock_file_path = potential_lock_file
            if logger:
                logger.info(f"Found filesystem lock file at: {lock_file_path}")
            break
        current_path = current_path.parent
    
    # If no lock file found, continue without waiting
    if lock_file_path is None:
        if logger:
            logger.info("No filesystem lock file found, continuing without wait")
        return
    
    # Read the PID from the lock file
    try:
        pid_content = lock_file_path.read_text().strip()
        if not pid_content:
            if logger:
                logger.warning(f"Filesystem lock file {lock_file_path} is empty")
            return
            
        pid = int(pid_content)
        if logger:
            logger.info(f"Found PID {pid} in filesystem lock file")
            
    except (ValueError, IOError) as e:
        if logger:
            logger.warning(f"Error reading PID from filesystem lock file {lock_file_path}: {e}")
        return
    
    # Check if the PID exists on the system
    try:
        if not psutil.pid_exists(pid):
            if logger:
                logger.info(f"PID {pid} from lock file does not exist on system, continuing")
            return
    except Exception as e:
        if logger:
            logger.warning(f"Error checking if PID {pid} exists: {e}")
        return
    
    # PID exists, wait for it to terminate or file to be deleted
    if logger:
        logger.info(f"Waiting for PID {pid} to terminate or lock file to be deleted")
    
    while True:
        try:
            # Check if PID still exists
            if not psutil.pid_exists(pid):
                if logger:
                    logger.info(f"PID {pid} has terminated")
                break
                
            # Check if lock file still exists
            if not lock_file_path.exists():
                if logger:
                    logger.info(f"Filesystem lock file {lock_file_path} has been deleted")
                break
                
            # Sleep before checking again
            time.sleep(1)
            
        except Exception as e:
            if logger:
                logger.warning(f"Error while waiting for filesystem lock: {e}")
            break
    
    if logger:
        logger.info("Filesystem lock wait completed")

