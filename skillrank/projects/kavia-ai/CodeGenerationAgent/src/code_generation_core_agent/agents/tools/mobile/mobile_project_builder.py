from pathlib import Path
import json
import traceback
import yaml
from typing import Op<PERSON>, Union

from code_generation_core_agent.agents.tools.android_emulator_tools import AndroidEmulatorTool
from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase

def mobile_project_builder(app_type: str, android_emulator_tools: AndroidEmulatorTool, 
                    project_root_path: str, executor: ExecutorBase, logger, restart = False) -> str:
    """
    Build the project based on the android platform needed.
    Args:
        app_type: The type of application to build
        android_emulator_tools: Tools for Android emulation
        project_root_path: Root path of the project
        executor: executor instance
        logger: Logger instance
    Returns:
        str: Appetize service response public url if successful, Error message with stacktrace if failed
    """
    try:
        project_path = None
        
        # Check for the project initialization
        init_file = Path(project_root_path) / ".init/.init-build-cmd"
        if not init_file.exists():
            logger.warning(f"Init file not found at {init_file}, falling back to project manifest")
        else:
            project_path = _get_project_path_from_init_file(init_file, executor, logger)
        
        if project_path is None:
            # Check for the project manifest if init file is not available
            project_manifest_file = Path(project_root_path).parent / ".project_manifest.yaml"
            if not project_manifest_file.exists():
                error_msg = f"Project manifest file not found at {project_manifest_file}"
                logger.error(error_msg)
                return error_msg
            project_path = _get_project_path_from_manifest(project_manifest_file, project_root_path, logger)

        # Determine APK path based on app type
        app_type = app_type.lower()
        if app_type in ["android", "kotlin"]:
            apk = project_path / "app-debug.apk"
        elif app_type == "flutter":
            apk = project_path / "app-release.apk"
        else:
            unsupported_msg = f"Unsupported app_type: {app_type}"
            logger.error(unsupported_msg)
            return unsupported_msg
        
        # Get the public key if available
        appetize_public_key_result = _check_for_public_key(apk, logger)
        if appetize_public_key_result and appetize_public_key_result.startswith("Error"):
            return appetize_public_key_result
        
        # Determine if we need to build
        needs_build = not apk.exists() or appetize_public_key_result is not None or restart
        
        # Build if necessary
        if needs_build:
            build_action = "update" if apk.exists() else "create"
            logger.info(f"Building application ({build_action})")
            first_run = True if build_action == "create" else False
            build_result = _build_application(project_path, app_type, apk, android_emulator_tools, logger, first_run)
            if build_result.startswith("Error"):
                return build_result
        
        # Handle APK deployment based on state
        if appetize_public_key_result:
            logger.info("Updating APK in emulator service")
            return _update_apk_in_emulator_service(apk, appetize_public_key_result, android_emulator_tools, logger)
        else:
            logger.info("Uploading APK to emulator service")
            return _upload_apk_to_emulator_service(apk, android_emulator_tools, logger)
            
    except Exception as e:
        error_msg = f"Error in mobile_project_builder: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
        logger.error(error_msg)
        return error_msg


def _build_application(project_path: Path, app_type: str, apk_path: Path, 
                      android_emulator_tools: AndroidEmulatorTool, logger, first_run) -> str:
    """
    Build the application and check if APK was created successfully.
    
    Args:
        project_path: Path to the project
        app_type: Type of application to build
        apk_path: Expected path of the APK file
        android_emulator_tools: Tools for Android emulation
        logger: Logger instance
    
    Returns:
        str: "Success" if build succeeded, error message with stacktrace if failed
    """
    try:
        build_result = android_emulator_tools.build_application(
            project_path=project_path,
            app_type=app_type,
            output_path=project_path,
            first_run=first_run
        )
        
        # Check if build_result is an error message
        if isinstance(build_result, str) and build_result.startswith("Error"):
            logger.error(f"Build failed: {build_result}")
            return build_result
        
        if not build_result:
            error_msg = "Failed to build application - build_application returned False"
            logger.error(error_msg)
            return f"Error: {error_msg}"
            
        # Check if the build created the APK
        if not apk_path.exists():
            error_msg = f"Build completed but APK not found at {apk_path}"
            logger.error(error_msg)
            return f"Error: {error_msg}"
            
        logger.info("Build successful")
        return "Success"
    except Exception as e:
        error_msg = f"Error building application: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"Error: {error_msg}"

def _check_for_public_key(apk_path: Path, logger) -> Optional[str]:
    """
    Check if the APK is already uploaded to the emulator service so a public key exists.
    
    Args:
        apk_path: Path to the APK file
    Returns:
        str: If the APK is already uploaded, return the public key; if no key found, return None; if error, return error message
    """
    try:
        emulator_init_file = apk_path.parent / '.emulator-service-init'
        
        # If the file doesn't exist, this is the first run - not an error condition
        if not emulator_init_file.exists():
            logger.info("No emulator initialization file found. This appears to be the first build.")
            return None
        
        try:
            with open(emulator_init_file, 'r') as f:
                emulator_data = json.load(f)
            
            public_key = emulator_data.get("public_key")
            if public_key:
                logger.info(f"APK already uploaded, public key: {public_key}")
                return public_key
            else:
                logger.info("APK not uploaded, no public key found")
                return None
        except json.JSONDecodeError as e:
            # This is a legitimate error - the file exists but is malformed
            error_msg = f"Failed to read emulator initialization data: {str(e)}"
            logger.error(error_msg)
            return f"Error: {error_msg}"
    except Exception as e:
        error_msg = f"Error checking for public key: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"Error: {error_msg}"

def _upload_apk_to_emulator_service(apk_path: Path, android_emulator_tools: AndroidEmulatorTool, 
                                    logger) -> str:
    """
    Upload APK to emulation service using direct initialization without executor.
    
    Args:
        apk_path: Path to the APK file
        android_emulator_tools: Tools for Android emulation
        logger: Logger for recording process information
    Returns:
        str: Appetize service response string if successful, error message with stacktrace if failed
    """
    try:
        upload_result = android_emulator_tools.upload_application(apk_path)
        
        # Check if upload_result is an error message
        if isinstance(upload_result, str) and upload_result.startswith("Error"):
            logger.error(f"Upload failed: {upload_result}")
            return upload_result
        
        if not upload_result:
            error_msg = "Failed to upload APK - upload_application returned None"
            logger.error(error_msg)
            return f"Error: {error_msg}"
    except Exception as e:
        error_msg = f"Failed to upload APK: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"Error: {error_msg}"
        
    project_root_path = apk_path.parent
    emulator_init_file = project_root_path / '.emulator-service-init'

    try:
        with open(emulator_init_file, 'w') as f:
            json.dump(upload_result, f)
    except Exception as e:
        error_msg = f"Failed to save emulator initialization data: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"Error: {error_msg}"
    
    return upload_result["app_url"]

def _update_apk_in_emulator_service(apk_path: Path, public_key: str, android_emulator_tools: AndroidEmulatorTool,
                                    logger) -> str:
    """
    Update APK in emulation service.
    
    Args:
        apk_path: Path to the APK file
        public_key: Public key for the existing app
        android_emulator_tools: Tools for Android emulation
        logger: Logger for recording process information
    Returns:
        str: Appetize service response string if successful, error message with stacktrace if failed
    """
    logger.info(f"Updating APK {str(apk_path)} in emulator service")
    
    try:
        upload_result = android_emulator_tools.update_application(apk_path, public_key)
        
        # Check if upload_result is an error message
        if isinstance(upload_result, str) and upload_result.startswith("Error"):
            logger.error(f"Update failed: {upload_result}")
            return upload_result
        
        if not upload_result:
            error_msg = "Failed to update APK - update_application returned None"
            logger.error(error_msg)
            return f"Error: {error_msg}"
    except Exception as e:
        error_msg = f"Failed to update APK: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"Error: {error_msg}"
    
    return upload_result

def _get_project_path_from_init_file(init_file: Path, executor: ExecutorBase, logger) -> Optional[Path]:
    """
    Extracts the project path from the initialization file.
    
    Args:
        init_file: Path to the initialization file
        executor: The executor for running commands
        logger: Logger instance
        
    Returns:
        Path: Project path if successful
        None: If failed
    """
    try:
        cmd = f"cat {str(init_file)}"
        output, return_code = executor.run_one_shot_command(cmd)
        if return_code != 0:
            error_msg = f"Error reading build configuration: {output}"
            logger.error(error_msg)
            return None
        
        try:
            config = json.loads(output)                
            return Path(config["working_directory"])
        except json.JSONDecodeError as e:
            error_msg = f"Invalid build configuration format: {str(e)}"
            logger.error(error_msg)
            return None
    except Exception as e:
        error_msg = f"Error extracting project path from init file: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
        logger.error(error_msg)
        return None

def _get_project_path_from_manifest(manifest_file: Path, project_root_path: str, logger) -> Optional[Path]:
    """
    Extracts the project path from the project manifest YAML file.
    Looks for container entries of type 'mobile' and uses their 'container_root' value.
    
    Args:
        manifest_file: Path to the project manifest YAML file
        project_root_path: The root path of the project
        logger: Logger instance
        
    Returns:
        Path: Project path if successful
        None: If failed
    """
    try:
        with open(manifest_file, 'r') as f:
            try:
                manifest_data = yaml.safe_load(f)
            except yaml.YAMLError as e:
                error_msg = f"Failed to parse project manifest YAML: {str(e)}"
                logger.error(error_msg)
                return None

        containers = manifest_data.get('containers', [])
        if not containers:
            error_msg = "Invalid project manifest: no containers found"
            logger.error(error_msg)
            return None

        # Find the mobile container
        mobile_container = None
        for container in containers:
            if container.get('container_type') == 'mobile':
                mobile_container = container
                break
                
        if not mobile_container:
            error_msg = "No mobile container found in project manifest"
            logger.error(error_msg)
            return None

        container_root = mobile_container.get('container_root')
        if not container_root:
            error_msg = "Mobile container does not have a container_root specified"
            logger.error(error_msg)
            return None
            
        project_dir = Path(project_root_path).parent
        project_path = project_dir / container_root
        
        logger.info(f"Using project path from manifest: {project_path}")
        return project_path
        
    except Exception as e:
        error_msg = f"Error extracting project path from manifest: {str(e)}\nStacktrace:\n{traceback.format_exc()}"
        logger.error(error_msg)
        return None