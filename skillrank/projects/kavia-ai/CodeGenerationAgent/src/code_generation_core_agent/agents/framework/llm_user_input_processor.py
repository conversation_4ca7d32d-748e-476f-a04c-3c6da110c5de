import json


from .llm_truncate import OutputTruncator, SummarizerType

MAX_USER_INPUT_SIZE = 3000


class InputSummary:
    def __init__(self, json_data):
        self.interrupt_task = json_data.get('interrupt_task', False)
        self.interrupt_reason = json_data.get('interrupt_reason', '')
        self.cleaned_user_input = json_data.get('cleaned_user_input',
                                                json_data.get('cleaned_user_input', json_data))
        self.answer_question = json_data.get('answer_question', False)

    def __str__(self):
        return f"InputSummary(interrupt_task={self.interrupt_task}, interrupt_reason={self.interrupt_reason}, cleaned_user_input={self.cleaned_user_input}, answer_questioin={self.answer_question})"


class InputData:
    def __init__(self, user_input, current_task, attachments=None, extra=None):
        self.user_input = user_input
        self.current_task = current_task
        self.attachments = attachments if attachments else []
        self.extra=extra

    def __str__(self):
        return json.dumps({
            "user_input": self.user_input,
            "current_task": self.current_task,
            "attachments": self.attachments,
            "extra": self.extra
        })


class LLMUserInputProcessor(OutputTruncator):
    def __init__(self, logger):
        super().__init__(max_size=MAX_USER_INPUT_SIZE,
                         summarizer_type=SummarizerType.USER_INPUT_SUMMARIZER, logger=logger)

    def summarize_user_input(self, data : InputData):
        result = super().truncate_output(str(data), allways_truncate=True)
        # Convert the result to a JSON
        try:
            json_result = json.loads(result) if isinstance(result, str) else result
            return InputSummary(json_result)
        except Exception as e:
            print(f"Error converting the result to JSON: {e}")
            return None
