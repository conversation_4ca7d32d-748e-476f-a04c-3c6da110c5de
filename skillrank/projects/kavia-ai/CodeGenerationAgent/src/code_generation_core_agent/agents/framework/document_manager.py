import json
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function

from .knowledge import Knowledge

class DocMgr(BaseToolInterface):
    def __init__(self, callback_functions, base_path, logger, llm, executor):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.logger = logger

    def get_tool_name(self) -> str:
        return "DocumentManager"

    '''
    Process the work report made by the DocumentationAgent to extract the
    information necessary to inform the knowledge system of the creation or
    modification of a document
    '''
    def review_work(self, request_details:dict, report:dict, tool_call_log:list):
        knowledge = Knowledge.getKnowledge()
        sources = []
        self.logger.info('DocMgr review_work')
        result = report.get('result')
        if result:
            if isinstance(result,str):
                try:
                    result = json.loads(result)
                except json.JSONDecodeError:
                    result = None
            if result:
                file_operations = result.get('file_operations')
                for tool_call in tool_call_log:
                    function = tool_call['name']
                    if function == 'ContainerFileTools_read_files':
                        args = tool_call['args']
                        cmd_files = args['file_paths']
                        for file in cmd_files:
                            if 'kavia_docs' in file and file.lower().endswith('.md'):
                                # skip - don't cite our own generated documents as sources
                                continue
                            if not file in sources:
                                sources.append(file)
                if file_operations:
                    files = []
                    for fileop in file_operations:
                        op = fileop.get('operation_type')
                        file_path = fileop.get('file_path')
                        if op and file_path:
                            if op in ['WRITE','EDIT'] and file_path.endswith('.md') and file_path not in files:
                                files.append(file_path)
                                description = fileop.get('explanation')
                                cited_sources = fileop.get('sources')
                                used_sources = []
                                if cited_sources:
                                    for source in cited_sources:
                                        if source in sources:
                                            used_sources.append(source)
                                else:
                                    used_sources = sources
                                knowledge.documentCreated( file_path, description, used_sources)
        return

    @register_function(
        description="Get the list of documents.  Returns a list of the fully qualified filenames of all documents created by DocumentationAgent."
                    "Example: DocumentManager_get_documents() ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
            },
            "required": []
        }
    )
    def get_documents(self):
        knowledge = Knowledge.getKnowledge()
        result = {
                "status": "SUCCESS",
                "documents": f"{knowledge.getDocuments()}"
        }
        return result

    @register_function(
        description="Get the list of sources for a document.  Returns all codebase files that were used in the past as information sources for this document."
                    "Example: DocumentManager_get_document_sources('<document_fullyqualified_pathnam>') ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_path": {"type": "string", "description": "The fully qualified path to the document."},
            },
            "required": ["file_path"]
        }
    )
    def get_document_sources(self, file_path):
        knowledge = Knowledge.getKnowledge()
        result = {
                "status": "SUCCESS",
                "sources": f"{knowledge.getDocumentSources(file_path)}"
        }
        return result


    @register_function(
        description="Get a summary of changes in document sources since the document was last updated.  The summary will provide a broad sense of the changes."
                    "If the summary for a file is just 'Yes' this means diff information is not available but the file has been modified more recently than the document that references it."
                    "Example: DocumentManager_get_summary_of_changes_to_document_sources('<document_fullyqualified_pathnam>') ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_path": {"type": "string", "description": "The fully qualified path to the document."},
            },
            "required": ["file_path"]
        }
    )
    def get_summary_of_changes_to_document_sources(self, file_path):
        knowledge = Knowledge.getKnowledge()
        result = {
                "status": "SUCCESS",
                "sources": f"{knowledge.getSummaryOfChangesToDocumentSources(file_path)}"
        }
        return result

    @register_function(
        description="Get the list of changes in document sources since the document was last updated.  This will include actual code diffs of the changes."
                    "If the changes for a file are just 'Yes' this means diff information is not available but the file has been modified more recently than the document that references it."
                    "Example: DocumentManager_get_changes_to_document_sources('<document_fullyqualified_pathnam>') ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_path": {"type": "string", "description": "The fully qualified path to the document."},
            },
            "required": ["file_path"]
        }
    )
    def get_changes_to_document_sources(self, file_path):
        knowledge = Knowledge.getKnowledge()
        result = {
                "status": "SUCCESS",
                "sources": f"{knowledge.getChangesToDocumentSources(file_path)}"
        }
        return result
