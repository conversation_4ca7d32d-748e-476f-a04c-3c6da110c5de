from enum import Enum
from typing import List

from code_generation_core_agent.agents.framework.memories import Memories, MemoryBase
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools


class WatchedFunctions(Enum):
    WRITE_FILE = "write_file"
    READ_FILE = "read_file"


def validate_mehthod(class_obj, method_name):
    """
    If function is not found in the class, raise an exception.
    """
    assert hasattr(class_obj, method_name), f"Method {method_name} not found in class {class_obj.__class__.__name__}"


class MemoryShards:
    """
    Memory Shards is a class to store the memory of the agent.
    """

    def __init__(self):
        self.memories = Memories()

    def _on_write_file(self, **arguments):
        validate_mehthod(ContainerFileTools, "write_file")
        reason = arguments.pop('reason', None)
        observations = arguments.pop('observations', None)
        root_cause_analysis = arguments.pop('root_cause_analysis', None)
        key = arguments.pop('file_path', "")
        if not key:
            return

        # Construct a JSON from reason, observations, and root_cause_analysis if they are not None

        memory_item = {"function_name": WatchedFunctions.WRITE_FILE.value}
        if reason is not None:
            memory_item["reason"] = reason
        if observations is not None:
            memory_item["observations"] = observations
        if root_cause_analysis:
            memory_item["root_cause_analysis"] = root_cause_analysis

        # Convert memory_item to a string
        self.memories.save_memory(key=key, memory=str(memory_item), is_fuzzy=False)

    def notice_function_call(self, function_name, arguments):
        if function_name == "write_file":
            self._on_write_file(**arguments)

    def get_function_call_memories(self, function_name : str, arguments : dict) -> List[MemoryBase]:
        if function_name == "write_file" or function_name == "read_file":
            file_path = arguments.get("file_path", "")
            return self.memories.get_memory(file_path, is_fuzzy=False)