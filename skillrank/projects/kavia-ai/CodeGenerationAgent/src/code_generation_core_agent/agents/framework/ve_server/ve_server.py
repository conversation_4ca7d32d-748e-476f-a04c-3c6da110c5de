import asyncio
import uvicorn
import tempfile
import threading
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import httpx
from fastapi import Request, Response
from starlette.websockets import WebSocket, WebSocketDisconnect
from pathlib import Path
import websockets
import json
import os
import re
import shutil

from code_generation_core_agent.config import config
from code_generation_core_agent.agents.tools.shared_loop import shared_loop

class VisualEditPayload(BaseModel):
    id: str
    values: dict
    changes: dict
    parent: dict

class VisualEditServer:
    def __init__(self, agent_path, base_path, platform, framework, executor, tools:dict, logger, app_port= 3000, host='0.0.0.0', port=4000):
        self.app = None
        self.agent_path = agent_path
        self.base_path = base_path
        self.platform = platform
        self.framework = framework
        self.executor = executor
        self.tools = tools
        self.logger = logger
        self.llm = None
        self.filelister = None
        self.src_base_path = None
        self.app_port = app_port
        self.host = host
        self.port = port
        self._server = None
        self._task = None
        self._loop = None
        self._thread = None
        self.work_path = os.path.join(base_path,'.ve')
        self.logger.info(f"VisualEditServer created with base path {base_path}")
        self._get_src_base_path(base_path)

    def _get_src_base_path(self,base_path):
        src_base_path = base_path
        filename = os.path.join(base_path,'.init/.init-run-tool')
        try:
            contents = []
            with open(filename, 'r', encoding='utf-8', errors='ignore') as file:
                contents = file.read()
                values = json.loads(contents)
                src_base_path = values.get('working_directory',base_path)
                self.logger.info(f"VisualEditServer: parse src_base_path {src_base_path} from {filename}")
        except Exception as e:
            self.logger.error(f"VisualEditServer: error getting working_directory: {str(e)}")
            pass
        self.src_base_path = src_base_path

    def _create_visual_edit_app(self):
        self.logger.info("VisualEditServer: creating FastAPI app")
        app = FastAPI()

        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # tighten in production!
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        @app.post("/save-edit")
        async def save_edit(payload: VisualEditPayload):
            useAIEdit = False
            id_str = payload.id
            changes = payload.changes
            temp_file = None

            self.logger.info(f"ve save: changes: {changes}")
            try:
                relative_file, line_str = id_str.split('::')
                relative_file = relative_file.lstrip('/')
                line = int(line_str)
            except Exception as e:
                useAIEdit = True

            if useAIEdit:
                result = await self.doAIEdit(dict(payload))
                if result:
                    return {"success": True, "message": "Edit block processed"}
                else:
                    return {"success": False, "error": 'edit failed'}
            else:
                full_file_path = os.path.join(self.src_base_path, relative_file)
                self.logger.info(f"Editing file: {full_file_path} (line {line})")

                input_data = {
                    'file': full_file_path,
                    'line': line,
                    'changes': changes
                }
                temp_file = tempfile.NamedTemporaryFile(delete=False, dir=self.work_path)
                temp_input_file_name = temp_file.name
                with open(temp_input_file_name, 'w') as f:
                    json.dump(input_data, f)

                try:
                    cmd = f"node run-transform.js {temp_input_file_name}"
                    timeout = config.getint("TOOLS", "shell_command_timeout")
                    async def _one_shot_docker_command():
                        stdout_data, returncode = await self.executor.create_subprocess_shell_one_shot(cmd, work_directory=self.work_path)
                        return stdout_data, returncode
                    try:
                        future = shared_loop.run_coroutine(_one_shot_docker_command())
                        output, returncode = future.result(timeout=timeout)
                    except Exception as e:
                        self.logger.error(f"VisualEditServer: error executing transform: {str(e)}")
                        pass

                    if returncode != 0:
                        self.logger.error(f"Transform failed:\n{output}")
                        return {"success": False, "error": output}

                    stdout = output
                    self.logger.debug(f"VisualEditServer: Raw transform output:\n{stdout}")

                    match = re.search(r'<<<EDIT_BLOCK_START>>>(.*?)<<<EDIT_BLOCK_END>>>', stdout, re.DOTALL)
                    if match:
                        edit_block = match.group(1).strip()
                        self.logger.debug(f"VisualEditServer: Extracted edit block:\n{edit_block}")
                        # Pass to main app's handler
                        self.tools.tool_set['ContainerFileTools'].edit_file(edit_block)
                        return {"success": True, "message": "Edit block processed"}
                    else:
                        self.logger.error("VisualEditServer: No edit block found in output.")
                        return {"success": True, "message": "No changes detected"}

                except Exception as e:
                    self.logger.error(f"VisualEditServer: Server error: {e}")
                    return {"success": False, "error": str(e)}

                finally:
                    if os.path.exists(temp_input_file_name):
                        os.remove(temp_input_file_name)
                    if temp_file:
                        temp_file.close()

        @app.api_route("/{full_path:path}", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"])
        async def proxy_request(request: Request, full_path: str):
            proxied_url = f"http://localhost:{self.app_port}/{full_path}"
            query_params = str(request.query_params)

            def adjust_path(full_path: str) -> str:
                path = Path(full_path)
                if not path.suffix:
                    for suffix in ['.js','.jsx','.ts','.tsx']:
                        test_path = full_path+suffix
                        if os.path.exists(os.path.join(self.src_base_path,test_path)):
                            full_path = test_path
                            break
                return full_path

            def should_proxy(full_path: str) -> bool:
                path = Path(full_path)
                try:
                    return (
                        'preview.html' not in full_path
                    )
                except Exception:
                    return True

            full_path = adjust_path(full_path)

            if not should_proxy(full_path):
                status_code = 200
                serve_content = ''
                content_type = "text/html"
                if full_path.endswith('preview.html'):
                    current_file = os.path.abspath(__file__)
                    current_dir = os.path.dirname(current_file)
                    serve_file_path = os.path.join(current_dir,'preview.html')
                    with open(serve_file_path, 'r', encoding='utf-8', errors='ignore') as file:
                        serve_content = file.read()

                rsp_headers = {}
                rsp_headers['content-type'] = content_type
                rsp_headers['content-encoding'] = 'utf-8'
                rsp_headers['content-length'] = str(len(serve_content))
                return Response(
                    content=serve_content,
                    status_code=status_code,
                    headers=rsp_headers,
                    media_type=content_type
                )

            async with httpx.AsyncClient() as client:
                body = await request.body()

                req_headers = {}
                for key, value in request.headers.items():
                    if key in ['host', 'referer']:
                        value = value.replace(f':{self.port}',f':{self.app_port}')
                    req_headers[key] = value
                try:
                    self.logger.debug(f"WEB-PROXY: forward: req:{request.method}, url:{proxied_url}, headers:{req_headers}, parms:{query_params}")
                    if request.method == 'GET' and request.headers.get('sec-fetch-dest','') != 'style':
                        if query_params:
                            if query_params.endswith('='):
                                query_params= query_params[:len(query_params)-1]
                            proxied_url = f"{proxied_url}?{query_params}"
                        proxy_response = await client.get(proxied_url,
                                                        headers=req_headers,
                                                        timeout=self.timeout)
                    else:
                        proxy_response = await client.request(
                            method=request.method,
                            url=proxied_url,
                            headers=req_headers,
                            content=body,
                            params=query_params,
                            timeout=self.timeout
                        )
                except Exception as e:
                    self.logger.error(f"Error in proxy_request: {repr(e)}")
                    return Response(status_code=500)

                if proxy_response.status_code == 500:
                    self.logger.error("Error 500 from upstream")

                content_type = proxy_response.headers.get("content-type", "")

                raw_content = await proxy_response.aread()

                rsp_headers = {}
                for key, value in proxy_response.headers.items():
                    if key in ['content-encoding']:
                        value = proxy_response.encoding
                    if key in ['content-length']:
                        value = str(len(raw_content))
                    if key == 'x-frame-options' and value == 'DENY':
                        # omit to allow preview to show error details
                        self.logger.error("removing x-frame-options=DENY")
                        continue
                    rsp_headers[key] = value

                self.logger.debug(f"WEB-PROXY: rsp: req:{request.method}, url:{proxied_url}, content-type:{content_type}, headers:{rsp_headers}")
                return Response(
                    content=raw_content,
                    status_code=proxy_response.status_code,
                    headers=rsp_headers,
                    media_type=content_type
                )


        @app.websocket("/{path:path}")
        async def websocket_proxy(websocket: WebSocket, path: str):
            await self._proxy_websocket(websocket, path)

        return app

    async def _proxy_websocket(self, websocket: WebSocket, path: str):
        token = websocket.query_params.get("token")
        upstream_url = f"ws://localhost:{self.app_port}/{path}"
        if token:
            upstream_url = f"{upstream_url}?token={token}"

        try:
            # Forward subprotocols
            subprotocols = websocket.headers.get("sec-websocket-protocol")
            if subprotocols:
                subprotocols = [proto.strip() for proto in subprotocols.split(',')]
            else:
                subprotocols = None

            additional_headers = []
            for key, value in websocket.headers.raw:
                if key == b'host':
                    url= f'localhost:{self.app_port}'
                    additional_headers.append( ('host',url.encode('utf-8')) )
                elif key == b'origin':
                    url= f'http://localhost:{self.app_port}'
                    additional_headers.append( (b'origin', url.encode('utf-8')) )
                elif key == b'sec-websocket-extensions' and value == b'permessage-deflate':
                    # don't pass on
                    continue
                else:
                    additional_headers.append( (key, value))
            async with websockets.connect(upstream_url, additional_headers=additional_headers, subprotocols=subprotocols, compression=None) as upstream_ws:

                websocket._headers._list[8] = (b'sec-websocket-extensions', '')
                await websocket.accept(subprotocol=upstream_ws.subprotocol)

                async def client_to_upstream():
                    try:
                        while True:
                            data = await websocket.receive_text()
                            await upstream_ws.send(data)
                    except WebSocketDisconnect:
                        await upstream_ws.close()
                    except Exception:
                        pass

                async def upstream_to_client():
                    try:
                        async for message in upstream_ws:
                            await websocket.send_text(message)
                    except Exception:
                        pass

                await asyncio.gather(client_to_upstream(), upstream_to_client())
        except Exception as e:
            self.logger.error(f"WebSocket proxy error for path {path}: {e}")

    async def _transform_code(self, file_path, source_code):
        try:
            temp_input_file = tempfile.NamedTemporaryFile(delete=False, dir=self.work_path, suffix=".js")
            temp_input_file.write(source_code.encode())
            temp_input_file.close()

            cmd = f"node run-transform-source.js {temp_input_file.name}"
            timeout = config.getint("TOOLS", "shell_command_timeout")

            async def _one_shot_docker_command():
                stdout_data, returncode = await self.executor.create_subprocess_shell_one_shot(cmd, work_directory=self.work_path)
                return stdout_data, returncode

            future = shared_loop.run_coroutine(_one_shot_docker_command())
            output, returncode = future.result(timeout=timeout)

            os.remove(temp_input_file.name)

            if returncode != 0:
                self.logger.warning(f"Code transform failed: {output}")
                return source_code  # fallback to original

            return output
        except Exception as e:
            self.logger.error(f"Transform exception: {str(e)}")
            return source_code

    def _run_server_loop(self):
        asyncio.set_event_loop(self._loop)
        self._loop.run_forever()

    def _match_files(self,search_term:str):
        matches = []
        cmd = f'grep -F -l -r --exclude-dir=node_modules --exclude-dir=coverage --exclude-dir=out --exclude=package-lock.json "{search_term}" *'
        timeout = config.getint("TOOLS", "shell_command_timeout")
        async def _one_shot_docker_command():
            stdout_data, returncode = await self.executor.create_subprocess_shell_one_shot(cmd, work_directory=self.src_base_path)
            return stdout_data, returncode
        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command())
            output, returncode = future.result(timeout=timeout)
        except Exception as e:
            self.logger.error(f"VisualEditServer: error executing search: {str(e)}")
            pass
        if returncode == 0:
            matches.extend(output.strip().splitlines())
        return matches

    def _getImportantFiles(self, payload:dict) -> dict:
        include_files = []
        include_files_weights = {}
        items = payload.get('values')
        def _get_path(value:str) -> str:
            from urllib.parse import urlparse
            try:
                result = urlparse(value)
                if result.scheme in ('http', 'https') and result.netloc:
                    return result.path
                else:
                    return value
            except Exception:
                return value
        def _parse_style_string(style_str):
            result = {}
            for item in style_str.split(';'):
                if ':' in item:
                    key, value = item.split(':', 1)
                    result[key.strip()] = value.strip()
            return result
        def _reduce_class(s: str) -> str:
            parts = s.split(" ", 1)
            return parts[1] if len(parts) > 1 else ""
        def _update_weight(file,addition):
            weight = include_files_weights.get(file,1)
            weight += addition
            include_files_weights[file] = weight
        def _process_dict(items:dict):
            if items:
                for key, value in items.items():
                    if value:
                        if key in ['text','attributes','style','alt','class','src',
                                   'backgroundImage','background-color','filter','href']:
                            if isinstance(value,dict):
                                _process_dict(value)
                            elif isinstance(value,str):
                                if key == 'style':
                                    _process_dict( _parse_style_string(value))
                                elif key == 'backgroundImage':
                                    value = _get_path(value)
                                matches = self._match_files(value)
                                if key == 'class':
                                    while not len(matches):
                                        value = _reduce_class(value)
                                        if not value:
                                            break
                                        matches = self._match_files(value)
                                    if len(matches) > 10:
                                        continue
                                if matches:
                                    weight = 1000 / len(matches)
                                    for match in matches:
                                        if match not in include_files:
                                            include_files.append(match)
                                        _update_weight(match,weight)
        _process_dict(items)

        self.logger.info(f"important files for ve save: {include_files}")

        self.filelister.include_files = include_files
        important_files = self.filelister.list_as_json(self.src_base_path, sort_key=lambda item : 1.0/include_files_weights.get(item,1))
        return important_files

    async def doAIEdit(self, payload:dict ) -> bool:
        from code_generation_core_agent.llm.llm_interface import LLMInterface
        from code_generation_core_agent.agents.micro.file_lister import FileLister
        result = False
        if not self.llm:
            OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
            self.llm = LLMInterface(llm_api_key=OPENAI_API_KEY,
                            session_dir=self.agent_path,
                            instance_name="ve_server",
                            chat_worker=None)
        if not self.filelister:
            include_files = []
            self.filelister = FileLister(exclude_dirs=['node_modules','build','coverage','out'],
                                         exclude_files=['package-lock.json','eslint.config.ts','tsconfig.json','tsconfig.app.json',
                                                        'tsconfig.node.json','tsconfig.vitest.json','vite.config.ts','vitest.config.ts',
                                                        'preview.html'],
                                         exclude_extensions=['.log','.lock'],
                                         include_files=include_files,
                                         print_contents_threshold=100000
                                         )
        if self.llm:
            try:
                def aie_function_executor(function_name, function_args):
                    if function_name == 'ContainerFileTools_edit_file':
                        print(f'args: {function_args}')
                        changes = function_args.get('changes')
                        if changes:
                            if not '<<<<<<< SEARCH' in changes:
                                changes = changes.replace("\n```\n","\n<<<<<<< SEARCH\n",1)
                            else:
                                changes = changes.replace("\n```","",1)
                            if not '>>>>>>> REPLACE' in changes:
                                changes = changes.replace("\n```\n","\n>>>>>>> REPLACE\n",1)
                            changes = changes.replace("```","")
                            function_args['changes'] = changes
                        return self.tools.tool_set['ContainerFileTools'].edit_file(changes)
                    return self.tools.function_executor(function_name, function_args)

                edit_schema = {
                                "type": "function",
                                "function": {
                                    "name": "ContainerFileTools_edit_file",
                                    "description=": "Edit files using a unified diff-like format for precise, context-aware changes. " \
                                        "Usage: ContainerFileTools_edit_file(changes='<file path>\n```\n<<<<<<< SEARCH\n<original content>\n=======\n<new content>\n>>>>>>> REPLACE\n```') "\
                                        "SEARCH and REPLACE are required and SEARCH content must be present in the file.""",
                                    "parameters": {
                                        "type": "object",
                                        "strict": True,
                                        "properties": {
                                            "changes": {
                                                "type": "string",
                                                "description": """ String ins SEARCH/REPLACE format.
# *SEARCH/REPLACE block* Rules:

Every *SEARCH/REPLACE block* must use this format:
1. The *FULL* and absolute file path alone on a line, verbatim. No bold asterisks, no quotes around it, no escaping of characters, etc.
2. The start of search block: <<<<<<< SEARCH
3. A contiguous chunk of lines to search for in the existing source code
4. The dividing line: =======
5. The lines to replace into the source code
6. The end of the replace block: >>>>>>> REPLACE

Use absolute file path, as shown to you by the user.

If the SEARCH section doesn't exactly match existing file content, the edit will fail and damage the user's system.
Consider the preloaded file content as a source of actual truth for current file content.

Every *SEARCH* section must *EXACTLY MATCH* the existing file content, character for character, including all comments, doc strings, etc.
The *SEARCH* section is always a subset, or multiple subsets of an existing file content. This *SEARCH* section MUST be is based on:

a.) The preloaded file content in the prompt, if any.
b.) The file content loaded using a previous *ContainerFileTools_read_files* tool call.

Never use the *SEARCH* section to search for a line that is not in the file content, or I will be fired.

If the file contains code or other data wrapped/escaped in json/xml/quotes or other containers, you need to propose edits to the literal contents of the file, including the container markup.

*SEARCH/REPLACE* blocks will replace *all* matching occurrences.
Include enough lines to make the SEARCH blocks uniquely match the lines to change.

IMPORTANT: Each SEARCH/REPLACE block must have exactly one "=======" separator line.
If you need to make multiple changes to a file, use multiple SEARCH/REPLACE blocks.

Keep *SEARCH/REPLACE* blocks concise.
Break large *SEARCH/REPLACE* blocks into a series of smaller blocks that each change a small portion of the file.
Include just the changing lines, and a few surrounding lines if needed for uniqueness.
Do not include long runs of unchanging lines in *SEARCH/REPLACE* blocks.

To move code within a file, use 2 *SEARCH/REPLACE* blocks: 1 to delete it from its current location, 1 to insert it in the new location.

Pay attention to which filenames the user wants you to edit, especially if they are asking you to create a new file.

If you want to put code in a new file, use a *SEARCH/REPLACE block* with:
- A new file path, including dir name if needed
- An empty `SEARCH` section
- The new file's contents in the `REPLACE` section

To rename files which have been added to the chat, use shell commands at the end of your response
                    """
                                        }
                                    },
                                    "required": ["changes"]
                                }
                            }
                        }

                payload.pop('id')
                model_name = config.get("LLM", "model")
                schemas = self.tools.get_function_schemas(["KnowledgeTools", "ContainerFileTools"])
                schemas.append(edit_schema)
                validator = self.tools.function_validator
                executor = aie_function_executor
                important_files = self._getImportantFiles(payload)
                system_prompt = 'You are an expert software engineer specializing in web application user interface coding.'
                user_prompt = f'The user has altered a style attribute of a user interface element of a {self.platform} app you are working on. ' \
                            f'The application is being implemented using the {self.framework} framework. ' \
                            'The user is supplying certain information about the UI element whose style attribute is being modified. ' \
                            'Your task is to examine the supplied information about the UI element and its parents, and the requried ' \
                            'style attribute modification and from this formulate an edit operation to perform on the app project source ' \
                            'code that will correctly produce the required change. ' \
                            'For things like image and video source urls pay careful attention to how the original value is specified in the ' \
                            'source code.  For example, if an image or video url is being changed and the original value is specified in a certain ' \
                            'source file, make your change there.  Do not simply add an inline src attribute to the HTML element if the original value ' \
                            'is specified elsewhere. Be sure to format the url as a local resource where appropriate.\n ' \
                            'You will be provided with important project files which you ' \
                            'will examine first instead of reading files.  The important files are listed in order of decreasing importance. If you need further information you can use the KnowledgeTools_find_relevant_files and ' \
                            'ContainerFileTools_read_files tools to determine how and where to make the required source code change.\n ' \
                            'Before reading a file, check if its contents are already listed in the important project files.\n ' \
                            'Do NOT invoke the ContainerFileTools_read_files tool for files whose contents are listed with important project files.\n ' \
                            'You will use the ContainerFileTools_edit_file tool to perform the source code change. Take care that the filename ' \
                            'in your edit_file command is absolute by combining the folder given by \'base_directory\' with the relative filename. ' \
                            'If you are unable to determine where or how to make the code change report FAILURE in your final resposne.\n ' \
                            'Before your final response with status SUCCESS, you MUST execute a ContainerFileTools_edit_file command to make the required changes.\n ' \
                            f'Here are important project files: {important_files}.\n ' \
                            f'Here is the information provided by the user: {payload}.\n ' \
                            'Please format your final response as a JSON object with the following structure:\n' \
                            '{{' \
                            '    "status": <SUCCESS or FAILURE>\n' \
                            '    "file_modified": <filename>' \
                            '    "description": <description of change or error>' \
                            '}}\n'
                completion = await self.llm.llm_interaction_wrapper(
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt},
                    ],
                    user_prompt=None,
                    system_prompt=None,
                    model=model_name,
                    response_format={"type": "json_object"},
                    function_schemas= schemas,
                    function_executor=executor,
                    function_validator=validator
                )
                response = json.loads(completion.choices[0].message.content.strip())
                status = response.get('status','FAILURE')
                if status == 'SUCCESS':
                    result = True

            except Exception as e:
                self.logger.error(f"Error: doAIEdit failed with exception: {str(e)}")
                pass

        return result

    def get_preview_url(self, preview_url:str ) -> str:
        adjusted_url = preview_url.replace(f'{self.app_port}',f'{self.port}')
        return adjusted_url

    def start(self):
        self.logger.info("VisualEditServer: start")
        self.timeout = config.getint("TOOLS", "shell_command_timeout")
        self.app = self._create_visual_edit_app()
        app_config = uvicorn.Config(self.app, host=self.host, port=self.port, loop='asyncio')
        self._server = uvicorn.Server(app_config)
        self._loop = asyncio.new_event_loop()
        self._task = self._loop.create_task(self._server.serve())
        self._thread = threading.Thread(target=self._run_server_loop, daemon=True)
        self._thread.start()
        self.logger.info(f"VisualEditServer started on {self.host}:{self.port}")

    async def stop(self):
        if self._server and self._server.should_exit is False:
            self.logger.info("Stopping edit server...")
            self._server.should_exit = True
            await self._task  # wait for shutdown
            self.logger.info("Edit server stopped.")
