from dataclasses import dataclass
from collections import defaultdict
from enum import Enum
from typing import Dict, List, Set, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re


class MemoryException(Exception):
    pass


class MemoryType(Enum):
    EXACT = 1
    FUZZY = 2


class MemoryBase:
    def __init__(self, content: str, timestamp: int, strength: float, memory_type: MemoryType):
        self.content = content
        self.timestamp = timestamp
        self.strength = strength
        self.type = memory_type

    def __str__(self):
        # Return as json string
        return str({'content': self.content, 'weight': int(self.strength * 100)})

@dataclass
class ExactMemory(MemoryBase):
    def __init__(self, content: str, timestamp: int, strength: float):
        super().__init__(content, timestamp, strength, MemoryType.EXACT)


@dataclass
class FuzzyMemory(MemoryBase):
    key: str
    keywords: Set[str]

    def __init__(self, key: str, content: str, timestamp: int, strength: float, keywords: Set[str]):
        super().__init__(content, timestamp, strength, MemoryType.FUZZY)
        self.key = key
        self.keywords = keywords


class Memories:
    def __init__(self, fade_rate: float = 0.1, drop_threshold: float = 0.1):
        self.exact_memories: Dict[str, List[ExactMemory]] = {}
        self.fuzzy_memories: Dict[str, List[FuzzyMemory]] = {}
        self.keyword_to_keys: Dict[str, Set[str]] = defaultdict(set)
        self.fade_rate = fade_rate
        self.drop_threshold = drop_threshold
        self.vectorizer = TfidfVectorizer()
        self.memory_count = 0
        self.vectorizer_fitted = False
        self.all_keys_vector = None

    def save_memory(self, key: str, memory: str, is_fuzzy: bool = False) -> None:
        strength = 1.0
        self.memory_count += 1

        if is_fuzzy:
            keywords = set(self._extract_keywords(key))
            fuzzy_memory = FuzzyMemory(key, memory, self.memory_count, strength, keywords)
            self.fuzzy_memories.setdefault(key, []).append(fuzzy_memory)

            for keyword in keywords:
                self.keyword_to_keys[keyword].add(key)

            self.vectorizer_fitted = False  # Reset vectorizer flag
        else:
            self.exact_memories.setdefault(key, []).append(ExactMemory(memory, self.memory_count, strength))

        self._apply_fading_effect(key, MemoryType.FUZZY if is_fuzzy else MemoryType.EXACT)

    def get_memory(self, key: str, is_fuzzy: bool = False) -> List[MemoryBase]:
        if is_fuzzy:
            return self._fuzzy_lookup(key)
        else:
            return self._exact_lookup(key)

    def _exact_lookup(self, key: str) -> List[ExactMemory]:
        if key in self.exact_memories:
            return self.exact_memories[key]
        return []

    def _fuzzy_lookup(self, query: str, similarity_threshold: float = 0.1) -> List[FuzzyMemory]:
        if not self.fuzzy_memories:
            return []

        all_keys = list(self.fuzzy_memories.keys())

        if not all_keys:
            return []

        if not self.vectorizer_fitted:
            self.vectorizer.fit(all_keys)
            self.all_keys_vector = self.vectorizer.transform(all_keys)
            self.vectorizer_fitted = True

        query_vector = self.vectorizer.transform([query])

        similarities = cosine_similarity(query_vector, self.all_keys_vector)[0]

        most_similar_index = similarities.argmax()
        most_similar_key = all_keys[most_similar_index]

        if similarities[most_similar_index] > 0.0 and similarities[most_similar_index] >= similarity_threshold:
            return self.fuzzy_memories[most_similar_key]
        else:
            return []

    def _apply_fading_effect(self, key: str, memory_type: MemoryType) -> None:
        memories = self.exact_memories[key] if memory_type == MemoryType.EXACT else self.fuzzy_memories[key]
        memories_to_keep = []

        for mem in memories:
            mem.strength *= (1 - self.fade_rate)
            if mem.strength >= self.drop_threshold:
                memories_to_keep.append(mem)

        if memory_type == MemoryType.EXACT:
            self.exact_memories[key] = memories_to_keep

        else:
            self.fuzzy_memories[key] = memories_to_keep

        if not memories_to_keep:
            if memory_type == MemoryType.EXACT:
                del self.exact_memories[key]
            else:
                del self.fuzzy_memories[key]
                for keyword in self.keyword_to_keys:
                    self.keyword_to_keys[keyword].discard(key)

    @staticmethod
    def _extract_keywords(text: str) -> List[str]:
        stopwords = {'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from', 'has', 'he', 'in', 'is', 'it',
                     'its', 'of', 'on', 'that', 'the', 'to', 'was', 'were', 'will', 'with'}

        words = re.findall(r'\w+', text.lower())
        return list(set([word for word in words if word not in stopwords]))