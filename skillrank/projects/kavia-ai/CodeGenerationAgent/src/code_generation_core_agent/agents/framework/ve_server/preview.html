<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Kavia Preview</title>
  <link rel="icon" href="data:,">
  <style>
    body {
      font-family: sans-serif;
      margin: 0;
      padding: 0;
    }

    iframe {
      width: 100vw;
      height: 100vh;
      border: none;
    }

    #editor-toggle {
      position: fixed;
      bottom: 10px;
      left: 10px;
      z-index: 10001;
      padding: 0.5rem 1rem;
      background: #ccc;
      color: #000;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .editor-mode-active {
      pointer-events: none;
    }

    .edit-mode-active iframe {
      pointer-events: auto;
    }

    .editor-overlay {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10000;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      border: 2px dashed #f90;
    }

    #text-style-controls {
      display: flex;
      gap: 8px;
      margin-top: 1em;
    }

    #text-style-controls button {
      padding: 6px 10px;
      border: 1px solid #ccc;
      background: white;
      cursor: pointer;
      font-weight: bold;
    }

    #text-style-controls button.active {
      background-color: #ddd;
      border-color: #888;
      box-shadow: inset 1px 1px 3px rgba(0,0,0,0.2);
    }
  </style>
</head>
<body>
  <iframe id="app-frame" src="http://localhost:4000"></iframe>
  <button id="editor-toggle">Enable Edit Mode</button>

  <script>
    const toggleBtn = document.getElementById('editor-toggle');
    const iframe = document.getElementById('app-frame');
    pathname = window.location.pathname.replace(/\/preview\.html$/, '')
    iframe.src = `${window.location.protocol}//${window.location.hostname}:4000${pathname}`;

    let overlayActive = false;
    let overlayElement = null;

    function rgbToHex(rgb) {
      if ( rgb.startsWith('#') ) return rgb;
      const match = rgb.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
      if (!match) return '#000000';
      return (
        '#' +
        [1, 2, 3]
          .map(i => parseInt(match[i]).toString(16).padStart(2, '0'))
          .join('')
      );
    }

    function hexToRgba(hex, alpha) {
      hex = hex.replace(/^#/, '');

      if (hex.length !== 6) {
        return `rgba(0,0,0,1)`;
      }

      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);

      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    function enableEditOverlay() {
      document.body.classList.add('edit-mode-active');
      overlayElement = document.createElement('div');
      overlayElement.className = 'editor-overlay';
      document.body.appendChild(overlayElement);
      toggleBtn.textContent = 'Disable Edit Mode';
      overlayActive = true;

      iframe.contentWindow.postMessage({ type: 'enable-edit-mode' }, '*');
    }

    function disableEditOverlay() {
      document.body.classList.remove('edit-mode-active');
      if (overlayElement) {
        document.body.removeChild(overlayElement);
        overlayElement = null;
      }
      toggleBtn.textContent = 'Enable Edit Mode';
      overlayActive = false;

      iframe.contentWindow.postMessage({ type: 'disable-edit-mode' }, '*');
      clearHighlight();
      sidePanel = document.getElementById('editor-panel');
      if (sidePanel) sidePanel.remove()
    }

    toggleBtn.addEventListener('click', () => {
      if (overlayActive) {
        disableEditOverlay();
      } else {
        enableEditOverlay();
      }
    });

    // Inject required code into iframe
    iframe.addEventListener('load', () => {
      const scriptContent = `
        (function () {
          //let idCounter = 1;
          let highlightEl = null;
          let currEl = null;
          let currElDisabled = false;
          let currHoverRules = null;
          let editMode = false;

          function generateEditorId(el) {
            const path = [];
            while (el && el !== document.body) {
              if ( el.parentNode ) {
                const siblings = Array.from(el.parentNode.children);
                const index = siblings.indexOf(el);
                path.unshift(index);
              }
              el = el.parentNode;
            }
            return 'editor-' + path.join('-'); // e.g., editor-1-2-0
          }

          function assignIds(root = document.body) {
            const walk = (el) => {
              if (!(el instanceof HTMLElement)) return;
              if (!el.hasAttribute('data-editor-id')) {
                el.setAttribute('data-editor-id', generateEditorId(el));
              }
              Array.from(el.children).forEach(walk);
            };
            walk(root);
          }       

          function watchForNewElements() {
            const observer = new MutationObserver((mutations) => {
              mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                  if (node instanceof HTMLElement) {
                    if (!node.hasAttribute('data-editor-id')) {
                      //node.setAttribute('data-editor-id', 'el-' + idCounter++);
                      node.setAttribute('data-editor-id', generateEditorId(node));
                    }
                    node.querySelectorAll('*').forEach((child) => {
                      if (child instanceof HTMLElement && !child.hasAttribute('data-editor-id')) {
                        //child.setAttribute('data-editor-id', 'el-' + idCounter++);
                        child.setAttribute('data-editor-id', generateEditorId(child));
                      }
                    });
                  }
                });
              });
            });

            observer.observe(document.body, { childList: true, subtree: true });
          }

          function removeHover(el) {
            const hoverRules = [];
            const sheets = [...document.styleSheets];

            for (const sheet of sheets) {
              let rules;
              try {
                rules = sheet.cssRules;
              } catch (e) {
                continue; // Skip cross-origin stylesheets
              }

              for (const rule of rules) {
                if (rule.selectorText && rule.selectorText.includes(':hover')) {
                  try {
                    const hoverSelector = rule.selectorText;
                    if (el.matches(hoverSelector.replace(/:hover/, ''))) {
                      hoverRules.push({
                        selector: hoverSelector,
                        style: rule.style.cssText,
                        sheet,
                        index: Array.from(rules).indexOf(rule)
                      });
                      // Override hover style by disabling it
                      rule.style.cssText = ''; // OR comment this line out if you only want to override
                    }
                  } catch (e) {
                    continue;
                  }
                }
              }
            }

            return hoverRules;
          }

          function restoreHover(el, hoverRules) {
            for (const { selector, style, sheet, index } of hoverRules) {
              try {
                const rule = sheet.cssRules[index];
                if (rule && rule.selectorText === selector) {
                  rule.style.cssText = style;
                }
              } catch (e) {
                continue;
              }
            }
          }

          function onMouseMove(event) {
            if (!editMode) return;

            const el = document.elementFromPoint(event.clientX, event.clientY);
            if (!el || !(el instanceof HTMLElement)) return;

            if ( currEl != el) {
              resetPointerEvents();
              if ( currEl ) {
                if (currElDisabled) {
                  currEl.setAttribute('disabled',"");
                  currEl.disabled = true;
                }
                if ( currHoverRules ) {
                  restoreHover( currEl, currHoverRules );
                  currHoverRules = null;
                }
              }
              currEl = el;
              currElDisabled = currEl.hasAttribute('disabled');
              if (currElDisabled) {
                currEl.removeAttribute('disabled');
                currEl.disabled = false;
              }
              currHoverRules = removeHover(currEl);
            }
            if (!highlightEl) {
              highlightEl = document.createElement('div');
              highlightEl.id = '__editor-highlight';
              highlightEl.style.position = 'absolute';
              highlightEl.style.pointerEvents = 'none';
              highlightEl.style.border = '2px dashed red';
              highlightEl.style.zIndex = '99999';
              document.body.appendChild(highlightEl);
            }

            const rect = el.getBoundingClientRect();
            highlightEl.style.left = rect.left + window.scrollX + 'px';
            highlightEl.style.top = rect.top + window.scrollY + 'px';
            highlightEl.style.width = rect.width + 'px';
            highlightEl.style.height = rect.height + 'px';
          }

          function getDirectText(el) {
            let text = '';
            el.childNodes.forEach(node => {
              if (node.nodeType === Node.TEXT_NODE) {
                text += node.textContent;
              }
            });
            return text.trim();
          }

            let lastSelected = [];

            function selectElement(element) {
              // Disable pointer events on current selection to allow next layer selection
              if (element) {
                pvalue = null;
                if ( element.style.pointerEvents ) {
                   pvalue = element.style.pointerEvents;
                }
                element.style.pointerEvents = 'none';
                lastSelected.push({element: element, pvalue: pvalue});
              }
            }

            function resetPointerEvents() {
              while (lastSelected.length) {
                item = lastSelected.pop();
                el = item.element;
                pvalue = item.pvalue;
                if ( el ) {
                  if ( pvalue ) {
                    el.style.pointerEvents = pvalue;
                  } else {
                    el.style.pointerEvents=null;
                  }
                }
              }
            }

            function onClick(event) {
            if (!editMode) return;

            const el = event.target.closest('[data-editor-id]');
            if (!el) {
              resetPointerEvents();
              return;
            }

            function getFontWeightCategory(fontWeight) {
              if (typeof fontWeight === 'string') {
                fontWeight = fontWeight.toLowerCase();
                if (fontWeight === 'bold') return 'bold';
                if (fontWeight === 'normal') return 'normal';
                if (!isNaN(fontWeight)) fontWeight = parseInt(fontWeight, 10);
              }

              if (typeof fontWeight === 'number') {
                return fontWeight >= 600 ? 'bold' : 'normal';
              }

              // fallback
              return 'normal';
            }

            function buildElementData(element) {
              if (!element || element.tagName === 'IFRAME' && element.id === 'app-frame') {
                return null;
              }

              const styles = getComputedStyle(element);
              const parentEl = element.parentElement?.closest('[data-editor-id]');

              values = {
                editorId: element.getAttribute('data-editor-id'),
                tagName: element.tagName,
                attributes: Object.fromEntries(Array.from(element.attributes).map(attr => [attr.name, attr.value])),
                textContent: getDirectText(element),
                styles: {
                  color: styles.color,
                  fontFamily: styles.fontFamily || 'inherit',
                  fontSize: styles.fontSize || 'inherit',
                  fontWeight: getFontWeightCategory(styles.fontWeight),
                  fontStyle: styles.fontStyle || 'normal',
                  textDecoration: styles.textDecoration.includes('underline') ? 'underline' : 'none',
                  textAlign: styles.textAlign || 'left',
                  backgroundColor: styles.backgroundColor,
                  width: element.style.width,
                  height: element.style.height,
                },
                parent: buildElementData(parentEl)
              };
              if ( styles.backgroundImage !== "none") {
                val = styles.backgroundImage;
                val = val.trim();
                if ( val.startsWith('url("') ) {
                  val= val.slice(5,-1);
                }
                if ( val.endsWith('"')) {
                  val= val.slice(0,val.length-1);
                }
                values.styles.backgroundImage = val;
                values.styles.backgroundRepeat = styles.backgroundRepeat;
                values.styles.backgroundSize = styles.backgroundSize;
                values.styles.backgroundPosition = styles.backgroundPosition;
              }
              return values;
            }

            const data = buildElementData(el);

            selectElement(el);

            window.parent.postMessage({ type: 'element-clicked', data }, '*');
            event.preventDefault();
            event.stopPropagation();
          }

          function enableEdit() {
            editMode = true;
            document.addEventListener('mousemove', onMouseMove, true);
            document.addEventListener('click', onClick, true);
          }

          function disableEdit() {
            editMode = false;
            document.removeEventListener('mousemove', onMouseMove, true);
            document.removeEventListener('click', onClick, true);
            if (highlightEl) {
              highlightEl.remove();
              highlightEl = null;
            }
            if ( currEl ) {
              if (currElDisabled) {
                currEl.setAttribute('disabled',"");
                currEl.disabled = true;
              }
              currEl = null;
              currElDisabled = false;
            }
            resetPointerEvents();
          }

          function adjustFontSize(element, deltaPx) {
            const computedStyle = window.getComputedStyle(element);
            const currentSize = computedStyle.fontSize;

            const numericValue = parseFloat(currentSize);

            const newSize = numericValue + deltaPx;
            if ( newSize > 0 ) {
              const remInPx = parseFloat(getComputedStyle(document.documentElement).fontSize);
              element.style.fontSize = \`\${newSize/remInPx}rem\`;
            }
          }

          window.addEventListener('message', (event) => {
            if (!event.data || typeof event.data.type !== 'string') return;

            if (event.data.type === 'enable-edit-mode') {
              enableEdit();
            } else if (event.data.type === 'disable-edit-mode') {
              disableEdit();
            } else if (event.data?.type === 'update-attribute') {
              const { editorId, attrName, value } = event.data;
              const selector = '[data-editor-id="' + editorId + '"]';
              const target = document.querySelector(selector);
              if (!target) return;

              if (attrName === 'textContent') {
                target.textContent = value;
              } else if (attrName.startsWith('style.')) {
                const styleProp = attrName.split('.')[1];
                if (styleProp === 'fontSize') {
                   adjustFontSize(target,value);
                } else {
                  target.style[styleProp] = value;
                }
              } else {
                target.setAttribute(attrName, value);
                if ( target.tagName === 'SOURCE' ) {
                   if ( target.parentElement.tagName === 'VIDEO' ) {
                      target.parentElement.load();
                   }
                }
              }
            }              
          });

          if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
              assignIds();
              watchForNewElements();
            });
          } else {
            setTimeout(() => {
              assignIds();
              watchForNewElements();
            }, 100); // Delay to allow hydration to complete
          }
        })();
      `;

      const scriptEl = document.createElement('script');
      scriptEl.type = 'text/javascript';
      scriptEl.textContent = scriptContent;

      try {
        iframe.contentDocument.head.appendChild(scriptEl);
      } catch (err) {
        console.warn('Failed to inject script into iframe:', err);
      }
    });

    // Highlight element in iframe
    function highlightElementInIframe(event) {
      if (!overlayActive) return;

      const iframeRect = iframe.getBoundingClientRect();
      const x = event.clientX - iframeRect.left;
      const y = event.clientY - iframeRect.top;

      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
      const el = iframeDoc.elementFromPoint(x, y);

      if (!el || !(el instanceof HTMLElement)) return;

      let existing = iframeDoc.getElementById('__editor-highlight');
      if (!existing) {
        existing = iframeDoc.createElement('div');
        existing.id = '__editor-highlight';
        existing.style.position = 'absolute';
        existing.style.pointerEvents = 'none';
        existing.style.border = '2px dashed red';
        existing.style.zIndex = '99999';
        iframeDoc.body.appendChild(existing);
      }

      const rect = el.getBoundingClientRect();
      existing.style.left = `${rect.left + iframe.contentWindow.scrollX}px`;
      existing.style.top = `${rect.top + iframe.contentWindow.scrollY}px`;
      existing.style.width = `${rect.width}px`;
      existing.style.height = `${rect.height}px`;
    }

    function clearHighlight() {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
      const el = iframeDoc.getElementById('__editor-highlight');
      if (el) el.remove();
    }

    // Mousemove event only triggers highlighting when overlayActive
    window.addEventListener('mousemove', highlightElementInIframe);

    // Handle messages from iframe (element clicked)
    window.addEventListener('message', (event) => {
      if (!overlayActive) return;
      const { type, data } = event.data;

      if (type === 'element-clicked') {
        createSidePanel(event.data.data);
      }
    });

    function getBackendUrl(backendPort = 4000) {
      return `${window.location.protocol}//${window.location.hostname}:${backendPort}`;
    }

    function handleCancel(data) {
      const iframe = document.getElementById('app-frame');
      iframe.src = iframe.src;
      disableEditOverlay();
    }

    function handleSave(data) {
      const saveButton = document.getElementById('save-button');
      const orgSaveHTML = saveButton.innerHTML;
      const orgSaveColor = saveButton.style.color;
      const orgSaveBg = saveButton.style.backgroundColor;

      // Show busy state
      saveButton.innerHTML = 'Working <span class="spinner"></span>';
      saveButton.style.color = 'white';
      saveButton.style.backgroundColor = 'grey';
      saveButton.disabled = true;

      const text = document.getElementById('el-text').value;
      const color = rgbToHex(document.getElementById('text-color').value);
      const fontFamily = getSelectedFontFamily();
      const boldToggle = document.getElementById('bold-btn').classList.contains('active');
      const italicToggle = document.getElementById('italic-btn').classList.contains('active');
      const underlineToggle = document.getElementById('underline-btn').classList.contains('active');
      const textAlign = document.getElementById('text-align').value;
      const bgToggle = document.getElementById('bg-toggle').checked;
      const bgColor = rgbToHex(document.getElementById('bg-color').value);
      const classValue = document.getElementById('el-class').value;

      const values = {};
      values.text = data.textContent;
      values.fontFamily = data.styles.fontFamily || 'inherit';
      values.fontWeight = data.styles.fontWeight || 'normal';
      values.fontStyle = data.styles.fontStyle || 'normal';
      values.fontUnderline = data.styles.textDecoration === 'underline' ? 'underline' : 'none';
      values.textAlign = data.styles.textAlign || 'left';
      values.attributes = data.attributes;
      delete values.attributes["data-editor-id"];
      values.style = data.styles;
      if ( !bgToggle ) {
        delete values.style.backgroundColor
      }

      const iframe = document.getElementById('app-frame');
      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
      const el = iframeDoc.querySelector(`[data-editor-id="${data.editorId}"]`);


      const changes = {};
      const styleChanges = {};

      if ( data.tagName === 'IMG' || data.styles.backgroundImage) {
        newurl = document.getElementById('img-src-input').value;
        if ( data.tagName === 'IMG') {
          if (data.attributes.src !== newurl ) {
            changes.src = newurl;
          }

          orgimgwidth = data.styles.width || 'unset'
          newimgwidth = getSelectedValue('img-width');
          if ( orgimgwidth !== newimgwidth ) {
            styleChanges.width = newimgwidth;
          }

          orgimgheight = data.styles.height || 'unset'
          newimgheight = getSelectedValue('img-height');
          if ( orgimgheight !== newimgheight ) {
            styleChanges.height = newimgheight;
          }
        } else {
          newurl = `url("${newurl}")`;
          if (data.styles.backgroundImage !== newurl) {
            styleChanges.backgroundImage = newurl;
          }

          orgbgsize = data.styles.backgroundSize || 'unset';
          newbgsize = getSelectedValue('img-size');
          if ( orgbgsize !== newbgsize ) {
            styleChanges.backgroundSize = newbgsize;
          }

          orgbgpos = mapSelectorValue('img-position', data.styles.backgroundPosition || 'unset');
          newbgpos = getSelectedValue('img-position');
          if ( orgbgpos !== newbgpos ) {
            styleChanges.backgroundPosition = newbgpos;
          }

          orgbgrep= data.styles.backgroundRepeat || 'unset';
          newbgrep= getSelectedValue('img-repeat');
          if ( orgbgrep !== newbgrep ) {
            styleChanges.backgroundRepeat = newbgrep;
          }
        }
      }

      if ( data.tagName === 'VIDEO' ) {
        newurl = document.getElementById('vid-src-input').value;
        if ( data.attributes.src !== newurl ) {
          changes.src = newurl;
        }
      }

      if (text !== data.textContent) {
        changes.text = text;
      }

      if (classValue !== (data.attributes.class || '')) {
        changes.class = classValue;
      }

      let originalColor = data.styles.color || '';
      let originalClrAlpha = 1;
      let clrAlpha = 1;
      const clrMatch = originalColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
      if (clrMatch) {
        const alpha = clrMatch[4] !== undefined ? parseFloat(clrMatch[4]) : 1;
        originalClrAlpha = alpha;
        clrAlpha = document.getElementById('txt-alpha').value;
      }
      if ( originalColor ) originalColor = rgbToHex(originalColor);
      if ((color !== originalColor || (clrAlpha != originalClrAlpha))) {
        styleChanges.color = hexToRgba(color,clrAlpha);
      }

      if (fontFamily && fontFamily !== (data.styles.fontFamily || '').trim()) {
        styleChanges.fontFamily = fontFamily;
      }

      if ( (values.fontWeight === 'bold') !== boldToggle) {
        styleChanges.fontWeight = boldToggle ? 'bold' : 'normal';
      }

      if ( (values.fontStyle === 'italic') !== italicToggle) {
        styleChanges.fontStyle = italicToggle ? 'italic' : 'normal';
      }

      if ( (values.fontUnderline === 'underline') !== underlineToggle) {
        styleChanges.textDecoration = underlineToggle ? 'underline' : 'none';
      }

      if ( values.textAlign !== textAlign ) {
        styleChanges.textAlign = textAlign;
      }

      let originalBg = data.styles.backgroundColor || '';
      let originalBgAlpha = 1;
      let bgIsTransparent = false;
      let bgIsOpaque = true;
      let bgAlpha = 1;
      const bgMatch = originalBg.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
      if (bgMatch) {
        const alpha = bgMatch[4] !== undefined ? parseFloat(bgMatch[4]) : 1;
        originalBgAlpha = alpha;
        bgAlpha = document.getElementById('bg-alpha').value;
      }
      if ( originalBg ) originalBg = rgbToHex(originalBg);
      if (bgToggle && (bgColor !== originalBg || (bgAlpha!= originalBgAlpha))) {
        styleChanges.backgroundColor = hexToRgba(bgColor,bgAlpha);
      }

      if (Object.keys(styleChanges).length > 0) {
        changes.style = styleChanges;
      }

      if (el.style.fontSize) {
        const remInPx = parseFloat(getComputedStyle(document.documentElement).fontSize);
        fontSizeOrg = parseFloat(data.styles.fontSize) / remInPx;
        if ( el.style.fontSize !== fontSizeOrg ) {
          styleChanges.fontSize = el.style.fontSize;
        }
      }

      values.tagName = el.tagName;

      const payload = {
        id: data.editorId,
        values,
        changes,
        parent: extractParentChain(data.parent),
      };

      console.log('Saving changes:', JSON.stringify(payload));

      fetch(`${getBackendUrl()}/save-edit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })
        .then(res => res.json())
        .then(result => {
          if (result.success) {
            console.log('Changes saved.');
          } else {
            alert('Failed to save changes.');
          }
        })
        .catch(err => {
          console.error('Error saving:', err);
          alert('Error saving changes.');
        })
        .finally(() => {
          saveButton.innerHTML = orgSaveHTML;
          saveButton.style.color = orgSaveColor;
          saveButton.style.backgroundColor = orgSaveBg;
          saveButton.disabled = false;
        });
    }

    // Recursively build parent chain
    function extractParentChain(node) {
      if (!node) return {};

      return {
        tagName: node.tagName,
        class: node.attributes.class || '',
        parent: extractParentChain(node.parent),
      };
    }

    function getSelectedFontFamily() {
      const fontFamilySelect = document.getElementById('font-family-select');
      const fontFamilyCustom = document.getElementById('font-family-custom');
      const selected = fontFamilySelect.value;
      if (selected === '__custom__') {
        return fontFamilyCustom.value.trim();
      }
      return selected;
    }

    function createSidePanel(data) {
      // Remove old panel if any
      const oldPanel = document.getElementById('editor-panel');
      if (oldPanel) oldPanel.remove();

      const panel = document.createElement('div');
      panel.id = 'editor-panel';
      panel.style = `
        position: fixed;
        top: 0;
        right: 0;
        width: 300px;
        height: 100%;
        background: white;
        border-left: 1px solid #ccc;
        box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        padding: 16px;
        font-family: sans-serif;
        z-index: 999999;
        overflow-y: auto;
        box-sizing: border-box;
      `;

      // Parse rgba to check if alpha = 0
      let txtColor = data.styles.color || '';
      let txtAlpha = 1;
      const txtMatch = txtColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
      if (txtMatch) {
        const alpha = txtMatch[4] !== undefined ? parseFloat(txtMatch[4]) : 1;
        txtAlpha = alpha;
      }

      // Parse bg rgba to check if alpha = 0
      let bgIsTransparent = false;
      let bgIsOpaque = true;
      let bgColor = data.styles.backgroundColor || '';
      let bgAlpha = 1;
      const bgMatch = bgColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
      if (bgMatch) {
        const alpha = bgMatch[4] !== undefined ? parseFloat(bgMatch[4]) : 1;
        bgIsTransparent = alpha === 0;
        bgIsOpaque = alpha === 1;
        bgAlpha = alpha;
      }
      data.useBackground = !bgIsTransparent;

      const fontFamily = data.styles.fontFamily || 'inherit';
      const fontWeight = data.styles.fontWeight || 'normal';
      const fontStyle = data.styles.fontStyle || 'normal';
      const fontUnderline = data.styles.textDecoration === 'underline' ? 'underline' : 'none';
      const textAlign = data.styles.textAlign || 'left';

      imgSection = ''
      if (data.tagName === 'IMG' || data.styles.backgroundImage ) {
        imgSection = `
        <div class="control-group" id="img-src-control">
          <label for="img-src-input">Image Source</label>
          <input type="text" id="img-src-input" value="..." placeholder="Enter image URL" />
          <div id="img-preview-container">
            <img id="img-preview" src="..." style="max-width: 100%; height: auto; margin-top: 8px;" />
          </div>
        </div>`
      }
      let imgSize = data.styles.backgroundSize || 'unset';
      let imgPosition = data.styles.backgroundPosition || 'unset';
      const imgRepeat = data.styles.backgroundRepeat || 'unset';
      let imgWidth = data.styles.width || 'unset';
      let imgHeight = data.styles.height || 'unset';
      imgProperties = ''
      if (data.styles.backgroundImage ) {
        imgProperties = `
        <div style="display: flex; gap: 8px; width: 100%;">
        <label style="margin-top: 1em; width: 40%">Size:</label>
        <select id="img-size" style="margin-top: 1em; width: 60%;">
          <option value="auto">auto</option>
          <option value="cover">cover</option>
          <option value="contain">contain</option>
          <option value="initial">initial</option>
          <option value="inherit">inherit</option>
          <option value="__custom__">Other...</option>
          <option value="unset">unset</option>
        </select>
        </div>
        <input type="text" id="img-size-other" placeholder="Size in % or unit" style="display: none; width: 100%;" />
        <div style="display: flex; gap: 8px; width: 100%;">
        <label style="margin-top: 1em; width: 40%">Position:</label>
        <select id="img-position" style="margin-top: 1em; width: 60%;">
          <option value="left top">left top</option>
          <option value="center top">center top</option>
          <option value="right top">right top</option>
          <option value="left center">left center</option>
          <option value="center center">center center</option>
          <option value="right center">right center</option>
          <option value="left bottom">left bottom</option>
          <option value="center bottom">center bottom</option>
          <option value="right bottom">right bottom</option>
          <option value="initial">initial</option>
          <option value="inherit">inherit</option>
          <option value="__custom__">Other...</option>
          <option value="unset">unset</option>
        </select>
        </div>
        <input type="text" id="img-position-other" placeholder="Position in % or unit" style="display: none; width: 100%;" />
        <div style="display: flex; gap: 8px; width: 100%;">
        <label style="margin-top: 1em; width: 40%">Repeat:</label>
        <select id="img-repeat" style="margin-top: 1em; width: 60%;">
          <option value="no-repeat" ${imgRepeat === 'no-repeat' ? 'selected' : ''}>no-repeat</option>
          <option value="repeat-x" ${imgRepeat === 'repeat-x' ? 'selected' : ''}>repeat-x</option>
          <option value="repeat-y" ${imgRepeat === 'repeat-y' ? 'selected' : ''}>repeat-y</option>
          <option value="repeat" ${imgRepeat === 'repeat' ? 'selected' : ''}>repeat</option>
          <option value="space" ${imgRepeat === 'space' ? 'selected' : ''}>space</option>
          <option value="round" ${imgRepeat === 'round' ? 'selected' : ''}>round</option>
          <option value="initial" ${imgRepeat === 'initial' ? 'selected' : ''}>initial</option>
          <option value="inherit" ${imgRepeat === 'inherit' ? 'selected' : ''}>inherit</option>
          <option value="unset" ${imgRepeat === 'unset' ? 'selected' : ''}>unset</option>
        </select>
        </div>
        `
      }
      else if (data.tagName === 'IMG') {
        imgProperties = `
        <div style="display: flex; gap: 8px; width: 100%;">
        <label style="margin-top: 1em; width: 40%">Width:</label>
        <select id="img-width" style="margin-top: 1em; width: 60%;">
          <option value="auto">auto</option>
          <option value="initial">initial</option>
          <option value="inherit">inherit</option>
          <option value="__custom__">Other...</option>
          <option value="unset">unset</option>
        </select>
        </div>
        <input type="text" id="img-width-other" placeholder="Width in % or unit" style="display: none; width: 100%;" />
        <div style="display: flex; gap: 8px; width: 100%;">
        <label style="margin-top: 1em; width: 40%">Height:</label>
        <select id="img-height" style="margin-top: 1em; width: 60%;">
          <option value="auto">auto</option>
          <option value="initial">initial</option>
          <option value="inherit">inherit</option>
          <option value="__custom__">Other...</option>
          <option value="unset">unset</option>
        </select>
        </div>
        <input type="text" id="img-height-other" placeholder="Height in % or unit" style="display: none; width: 100%;" />
        `
      }

      videoSection = ''
      if (data.tagName === 'VIDEO' ) {
        videoSection = `
        <div class="control-group" id="vid-src-control">
          <label for="vid-src-input">Video Source</label>
          <input type="text" id="vid-src-input" value="..." placeholder="Enter video URL" />
          <div id="vid-preview-container">
            <video id="vid-preview" src="..." style="max-width: 100%; height: auto; margin-top: 8px;" autoplay="" muted="" loop="" />
          </div>
        </div>`
      }

      panel.innerHTML = `
        <button id="close-panel" style="
          position: absolute;
          top: 8px;
          left: 12px;
          font-size: 20px;
          background: none;
          border: none;
          cursor: pointer;
        ">&times;</button>

        <h2 style="margin-top: 0; text-align:center;">Editor Panel</h2>

      ${imgSection}
      ${imgProperties}

      ${videoSection}

        <label style="display: block; margin-top: 1em;">Text:</label>
        <textarea id="el-text" style="width: 100%; height: 60px;">${data.textContent}</textarea>

        <label style="display: block; margin-top: 1em;">Color:</label>
        <div style="display: flex; gap: 8px; width: 100%;">
          <input
            type="color"
            id="text-color"
            value="${rgbToHex(data.styles.color || 'rgb(0, 0, 0)')}"
            style="width: 40%; min-width: 60px;"
          />
          <input
            type="text"
            id="text-color-hex"
            value="${rgbToHex(data.styles.color || 'rgb(0, 0, 0)')}"
            style="width: 60%;"
            maxlength="7"
            pattern="^#([0-9A-Fa-f]{6})$"
            placeholder="#000000"
          />
        </div>
        <label style="display: block; margin-top: 1em;">Color Alpha</label>
        <input type="text" id="txt-alpha" value="${txtAlpha || ''}" style="width: 20%;" />

        <label style="display: block; margin-top: 1em;">Font Family:</label>
        <select id="font-family-select" style="width: 100%; margin-bottom: 0.5em;">
          <option value="Arial, sans-serif">Arial</option>
          <option value="Helvetica, sans-serif">Helvetica</option>
          <option value="Verdana, sans-serif">Verdana</option>
          <option value="Tahoma, sans-serif">Tahoma</option>
          <option value="'Trebuchet MS', sans-serif">Trebuchet MS</option>
          <option value="'Segoe UI', sans-serif">Segoe UI</option>
          <option value="Georgia, serif">Georgia</option>
          <option value="'Times New Roman', serif">Times New Roman</option>
          <option value="Palatino, 'Palatino Linotype', serif">Palatino</option>
          <option value="'Garamond', serif">Garamond</option>
          <option value="'Courier New', monospace">Courier New</option>
          <option value="'Lucida Console', monospace">Lucida Console</option>
          <option value="Monaco, monospace">Monaco</option>
          <option value="'Impact', fantasy">Impact</option>
          <option value="'Comic Sans MS', cursive, sans-serif">Comic Sans MS</option>
          <option value="__custom__">Other...</option>
        </select>

        <input type="text" id="font-family-custom" placeholder="Enter custom font family" style="display: none; width: 100%;" />

        <label style="display: block; margin-top: 1em;">Text Style:</label>
        <div id="text-style-controls" style="display: flex; gap: 0.5em; margin-top: 1em;">
          <button id="bold-btn" class="${fontWeight === 'bold' ? "active" : ""} style="font-weight: bold;">B</button>
          <button id="italic-btn" class="${fontStyle === 'italic' ? "active" : ""} style="font-style: italic;">I</button>
          <button id="underline-btn" class="${fontUnderline === 'underline' ? "active" : ""} style="text-decoration: underline;">U</button>
        </div>

        <label style="display: block; margin-top: 1em;">Text Size:</label>
        <div id="text-size-controls" style="display: flex; gap: 0.5em; margin-top: 1em;">
          <button id="text-smaller-btn" style="font-size: 10pt;">A</button>
          <button id="text-larger-btn"  style="font-size: 16pt;">A</button>
        </div>

        <label style="display: block; margin-top: 1em;">Text Alignment:</label>
        <select id="text-align" style="width: 100%;">
          <option value="left" ${textAlign === 'left' ? 'selected' : ''}>Left</option>
          <option value="start" ${textAlign === 'start' ? 'selected' : ''}>Start</option>
          <option value="center" ${textAlign === 'center' ? 'selected' : ''}>Center</option>
          <option value="right" ${textAlign === 'right' ? 'selected' : ''}>Right</option>
          <option value="justify" ${textAlign === 'justify' ? 'selected' : ''}>Justify</option>
        </select>

        <label style="display: block; margin-top: 1em;">
          <input type="checkbox" id="bg-toggle" ${bgIsTransparent ? '' : 'checked'} />
          Apply Background Color
        </label>
        <div style="display: flex; gap: 8px; width: 100%;">
          <input
            type="color"
            id="bg-color"
            value="${rgbToHex(data.styles.backgroundColor) || '#ffffff'}" ${rgbToHex(data.styles.backgroundColor) ? '' : 'disabled'}
            style="width: 40%; min-width: 60px; margin-top: 0.5em;"
          />
          <input
            type="text"
            id="bg-color-hex"
            value="${rgbToHex(data.styles.backgroundColor || 'rgb(0, 0, 0)')}"
            style="width: 60%;"
            maxlength="7"
            pattern="^#([0-9A-Fa-f]{6})$"
            placeholder="#000000"
          />
        </div>
        <label style="display: block; margin-top: 1em;">Background Color Alpha</label>
        <input type="text" id="bg-alpha" value="${bgAlpha || ''}" style="width: 20%;" />

        <label style="display: block; margin-top: 1em;">CSS class</label>
        <input type="text" id="el-class" value="${data.attributes.class || ''}" style="width: 100%;" />

        <style>
          .spinner {
            display: inline-block;
            width: 14px;
            height: 14px;
            border: 2px solid white;
            border-top: 2px solid transparent;
            border-radius: 50%;
            margin-left: 8px;
            animation: spin 0.6s linear infinite;
            vertical-align: middle;
          }

          @keyframes spin {
            to { transform: rotate(360deg); }
          }
        </style>

        <div style="display: flex; justify-content: space-evenly; width: 100%;">
        <button id="save-button" style="
          margin-top: 1.5em;
          width: 40%;
          padding: 0.5em;
          background: #eee;
          border: 1px solid #ccc;
          cursor: pointer;
        ">Save</button>

        <button id="cancel-button" style="
          margin-top: 1.5em;
          width: 40%;
          padding: 0.5em;
          background: #eee;
          border: 1px solid #ccc;
          cursor: pointer;
        ">Cancel</button>
        </div>
      `;

      document.body.appendChild(panel);

      const fontFamilySelect = document.getElementById('font-family-select');
      const fontFamilyCustom = document.getElementById('font-family-custom');
      const currentFontFamily = (data.styles.fontFamily || '').replace(/["']/g, '').trim();

      let matched = false;
      Array.from(fontFamilySelect.options).forEach(option => {
        if (currentFontFamily.startsWith(option.value.split(',')[0])) {
          fontFamilySelect.value = option.value;
          matched = true;
        }
      });

      if (!matched && currentFontFamily) {
        fontFamilySelect.value = '__custom__';
        fontFamilyCustom.style.display = 'block';
        fontFamilyCustom.value = currentFontFamily;
      }

      // Handlers
      document.getElementById('close-panel').onclick = () => panel.remove();

      document.getElementById('bg-toggle').onchange = (e) => {
        document.getElementById('bg-color').disabled = !e.target.checked;
      };

      document.getElementById('save-button').addEventListener('click', () => handleSave(data));

      document.getElementById('cancel-button').addEventListener('click', () => handleCancel(data));

      function toggleStyle(buttonId, styleProp, toggleValue) {
        const btn = document.getElementById(buttonId);
        const el = iframe.contentDocument.querySelector(`[data-editor-id="${data.editorId}"]`);
        if (!btn || !el) return;

        const isActive = btn.classList.toggle('active');
        if (styleProp === 'textDecoration') {
          el.style[styleProp] = isActive ? toggleValue : 'none';
        } else {
          el.style[styleProp] = isActive ? toggleValue : 'normal';
        }

        // Send update to live edit
        window.frames[0].postMessage({
          type: 'update-attribute',
          editorId: data.editorId,
          attrName: `style.${styleProp}`,
          value: el.style[styleProp]
        }, '*');
      }

      function sendUpdate(attrName, value) {
        window.frames[0].postMessage({
          type: 'update-attribute',
          editorId: data.editorId,
          attrName,
          value,
        }, '*');
      }

      document.getElementById('el-text').addEventListener('input', (e) => {
        sendUpdate('textContent', e.target.value);
      });

      document.getElementById('el-class').addEventListener('input', (e) => {
        sendUpdate('class', e.target.value);
      });

      document.getElementById('text-color').addEventListener('input', (e) => {
        alpha = document.getElementById('txt-alpha').value;
        newvalue = hexToRgba(e.target.value,alpha);
        document.getElementById('text-color-hex').value = e.target.value;
        sendUpdate('style.color', newvalue);
      });

      document.getElementById('text-color-hex').addEventListener('input', (e) => {
        const val = e.target.value;
        if (/^#[0-9A-Fa-f]{6}$/.test(val)) {
          alpha = document.getElementById('txt-alpha').value;
          newvalue = hexToRgba(e.target.value,alpha);
          document.getElementById('text-color').value = e.target.value;
          sendUpdate('style.color', newvalue);
        }
      });

      document.getElementById('txt-alpha').addEventListener('input', (e) => {
        rgbhex = document.getElementById('text-color').value;
        alpha = document.getElementById('txt-alpha').value;
        newvalue = hexToRgba(rgbhex,alpha);
        sendUpdate('style.color', newvalue);
      });

      fontFamilySelect.addEventListener('change', () => {
        const isCustom = fontFamilySelect.value === '__custom__';
        fontFamilyCustom.style.display = isCustom ? 'block' : 'none';

        if (!isCustom) {
          sendUpdate('style.fontFamily', fontFamilySelect.value);
        } else {
          // still send custom value if already typed
          const customValue = fontFamilyCustom.value.trim();
          if (customValue) {
            sendUpdate('style.fontFamily', customValue);
          }
        }
      });

      fontFamilyCustom.addEventListener('input', (e) => {
        const value = e.target.value.trim();
        if (fontFamilySelect.value === '__custom__' && value) {
          sendUpdate('style.fontFamily', value);
        }
      });

      document.getElementById('bold-btn').addEventListener('click', () => {
        toggleStyle('bold-btn', 'fontWeight', 'bold');
      });

      document.getElementById('italic-btn').addEventListener('click', () => {
        toggleStyle('italic-btn', 'fontStyle', 'italic');
      });

      document.getElementById('underline-btn').addEventListener('click', () => {
        toggleStyle('underline-btn', 'textDecoration', 'underline');
      });

      document.getElementById('text-smaller-btn').addEventListener('click', () => {
        sendUpdate('style.fontSize',-1);
      });

      document.getElementById('text-larger-btn').addEventListener('click', () => {
        sendUpdate('style.fontSize',+1);
      });

      document.getElementById('text-align').addEventListener('change', (e) => {
        sendUpdate('style.textAlign', e.target.value);
      });

      document.getElementById('bg-toggle').addEventListener('change', (e) => {
        const bgColorInput = document.getElementById('bg-color');
        bgColorInput.disabled = !e.target.checked;
        const value = e.target.checked ? bgColorInput.value : 'transparent';
        sendUpdate('style.backgroundColor', value);
      });

      document.getElementById('bg-color').addEventListener('input', (e) => {
        if (!document.getElementById('bg-toggle').checked) return;
        alpha = document.getElementById('bg-alpha').value;
        newvalue = hexToRgba(e.target.value,alpha);
        document.getElementById('bg-color-hex').value = e.target.value;
        sendUpdate('style.backgroundColor', newvalue);
      });

      document.getElementById('bg-color-hex').addEventListener('input', (e) => {
        const val = e.target.value;
        if (/^#[0-9A-Fa-f]{6}$/.test(val)) {
          document.getElementById('bg-color').value = e.target.value;
          if (!document.getElementById('bg-toggle').checked) return;
          alpha = document.getElementById('bg-alpha').value;
          newvalue = hexToRgba(e.target.value,alpha);
          sendUpdate('style.backgroundColor', newvalue);
        }
      });

      document.getElementById('bg-alpha').addEventListener('input', (e) => {
        rgbhex = document.getElementById('bg-color').value;
        alpha = document.getElementById('bg-alpha').value;
        newvalue = hexToRgba(rgbhex,alpha);
        sendUpdate('style.backgroundColor', newvalue);
      });

      if (data.tagName === 'IMG' || data.styles.backgroundImage) {
        if (data.tagName == 'IMG') {
          imgUrl = data.attributes.src;
        } else {
          imgUrl = data.styles.backgroundImage;
        }
        imgSrc = document.getElementById('img-src-input');
        imgSrc.value = imgUrl;
        imgPreview = document.getElementById('img-preview');
        const match = imgUrl.match(/url\(["']?(.*?)["']?\)/);
        url = match ? match[1] : imgUrl;
        imgPreview.src = url;
        imgSrc.addEventListener('change', (e) => {
          if ( data.tagName == 'IMG') {
            sendUpdate('src', e.target.value);
          } else {
            url = `url("${e.target.value}")`;
            sendUpdate('style.backgroundImage', url);
          }
          imgPreview = document.getElementById('img-preview');
          imgPreview.src = e.target.value;
        });
        if ( data.styles.backgroundImage) {
          setupSelector('img-size', 'img-size-other', imgSize);
          document.getElementById('img-size').addEventListener('change', (e) => {
            new_value = e.target.value;
            if ( new_value === '__custom__') new_value = document.getElementById('img-size-other').value;
            sendUpdate('style.backgroundSize', new_value);
            setupSelector('img-size', 'img-size-other', new_value);
          });
          document.getElementById('img-size-other').addEventListener('change', (e) => {
            new_value = e.target.value;
            imgSize = e.target.value;
            sendUpdate('style.backgroundSize', new_value);
          });

          setupSelector('img-position', 'img-position-other', imgPosition);
          document.getElementById('img-position').addEventListener('change', (e) => {
            new_value = e.target.value;
            if ( new_value === '__custom__') new_value = document.getElementById('img-position-other').value;
            sendUpdate('style.backgroundPosition', new_value);
            setupSelector('img-position', 'img-position-other', new_value);
          });
          document.getElementById('img-position-other').addEventListener('change', (e) => {
            new_value = e.target.value;
            imgPosition = e.target.value;
            sendUpdate('style.backgroundPosition', new_value);
          });

          document.getElementById('img-repeat').addEventListener('change', (e) => {
            sendUpdate('style.backgroundRepeat', e.target.value);
          });
        }
        if ( data.tagName === 'IMG') {
          setupSelector('img-width', 'img-width-other', imgWidth);
          document.getElementById('img-width').addEventListener('change', (e) => {
            new_value = e.target.value;
            if ( new_value === '__custom__') new_value = document.getElementById('img-width-other').value;
            sendUpdate('style.width', new_value);
            setupSelector('img-width', 'img-width-other', new_value);
          });
          document.getElementById('img-width-other').addEventListener('change', (e) => {
            new_value = e.target.value;
            imgWidth = e.target.value;
            sendUpdate('style.width', new_value);
          });

          setupSelector('img-height', 'img-height-other', imgHeight);
          document.getElementById('img-height').addEventListener('change', (e) => {
            new_value = e.target.value;
            if ( new_value === '__custom__') new_value = document.getElementById('img-height-other').value;
            sendUpdate('style.height', new_value);
            setupSelector('img-height', 'img-height-other', new_value);
          });
          document.getElementById('img-height-other').addEventListener('change', (e) => {
            new_value = e.target.value;
            imgHeight = e.target.value;
            sendUpdate('style.height', new_value);
          });
        }
      }

      if (data.tagName === 'VIDEO') {
        if ( data.attributes.src ) {
          vidUrl = data.attributes.src;
        } else {
          const el = iframe.contentDocument.querySelector(`[data-editor-id="${data.editorId}"]`);
          Array.from(el.children).forEach(child => {
            if (child.tagName === 'SOURCE') {
              vidUrl = child.src;
              data.attributes.src = vidUrl;
            }
          });
        }
        vidSrc = document.getElementById('vid-src-input');
        vidSrc.value = vidUrl;
        vidPreview = document.getElementById('vid-preview');
        const match = vidUrl.match(/url\(["']?(.*?)["']?\)/);
        url = match ? match[1] : vidUrl;
        vidPreview.src = url;
        vidSrc.addEventListener('change', (e) => {
          if ( data.tagName == 'VIDEO') {
            if ( data.attributes.src ) {
              sendUpdate('src', e.target.value);
            } else {
              const el = iframe.contentDocument.querySelector(`[data-editor-id="${data.editorId}"]`);
              Array.from(el.children).forEach(child => {
                if (child.tagName === 'SOURCE') {
                  id_saved = data.editorId;
                  data.editorId = child.getAttribute('data-editor-id');
                  if ( data.editorId ) {
                    if (data.editorId) {
                      sendUpdate('src', e.target.value);
                    }
                  }
                  data.editorId = id_saved;
                }
              });
            }
          }
          vidPreview = document.getElementById('vid-preview');
          vidPreview.src = e.target.value;
        });
      }
    }

    function mapSelectorValue(selector_id, current_value) {
      mapped_value = current_value;
      if ( selector_id === 'img-position') {
        parts = current_value.split(" ");
        if (parts.length == 2) {
          for (i in [0,1]) {
            if ( parts[i] === '0%' ) parts[i] = 'left';
            if ( parts[i] === '50%' ) parts[i] = 'center';
            if ( parts[i] === '100%' ) parts[i] = 'right';
          }
          mapped_value = parts[0]+' '+parts[1];
        }
      }
      return mapped_value;
    }

    function setupSelector(selector_id, custom_id, current_value) {
      const select = document.getElementById(selector_id);
      const custom = document.getElementById(custom_id);

      current_value = mapSelectorValue(selector_id, current_value);

      let matched = false;
      Array.from(select.options).forEach(option => {
        if (current_value === option.value) {
          select.value = option.value;
          matched = true;
          custom.style.display = 'none';
        }
      });
      if (!matched) {
        select.value = '__custom__';
        custom.style.display = 'block';
        custom.value = current_value;
      }
    }

    function getSelectedValue(selector_id) {
      const select = document.getElementById(selector_id);
      selected_value = select.value;
      if ( selected_value == '__custom__' ) {
        custom_id = selector_id+'-other';
        const custom = document.getElementById(custom_id);
        selected_value = custom.value;
      } else {
        selected_value = mapSelectorValue(selector_id, selected_value);
      }
      return selected_value;
    }

</script>

  <div id="side-panel" style="
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    width: 300px;
    height: 100vh;
    background: #fff;
    border-left: 1px solid #ccc;
    padding: 1rem;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    z-index: 10002;
    overflow-y: auto;
  ">
    <h3>Element Info</h3>
    <div id="panel-content"></div>
  </div>

</body>
</html>