import logging
from enum import Enum

import litellm
import re
from code_generation_core_agent.agents.framework.cost_tracker import CostTracer
from code_generation_core_agent.config import config
import json
DEBUG = False

MAX_STR_SIZE = 200000
class InvalidModelError(Exception):
    """Exception raised for invalid model configuration."""
    pass

class SummarizerType(Enum):
    VALIDATION_COMPLETED_SUMMARIZER = "validation_summarizer"
    """
    Summarizer type for determining if a validation process is complete.
    Returns a boolean result indicating completion status.
    """

    TOOL_OUTPUT_SUMMARIZER = "tool_output_summarizer"
    """
    Summarizer type for condensing tool output while preserving critical information.
    Focuses on maintaining warning and error messages, success messages, and other crucial details.
    """

    LLM_INTERACTION_SUMMARIZER = "llm_interaction_summarizer"
    """
    Summarizer type for summarizing LLM interactions with a focus on algorithmic progress.
    Produces a structured JSON summary tracking function calls, important data, and failure patterns.
    """

    USER_INPUT_SUMMARIZER = "user_input_summarizer"
    """
    Summarizer type for analyzing user input in the context of ongoing code and test generation tasks.
    Determines if the current task should be interrupted and cleans up the user input for further processing.
    """

    LLM_TASK_MANAGER_SUMMARIZER = "llm_task_manager_summarizer"
    """
    Summarizer type for summarizing responses and scheduling tasks based on a chat with the human.
    Specialized in LLM generating code and tests.
    """

    LLM_CHAT_ENABLED_WORKER_SUMMARIZER = "llm_chat_enabled_worker_summarizer"
    """
    Summarizer type for managing interactive chat sessions in a ChatEnabledWorker.
    Processes message history, work items, and user interactions to provide contextual responses.
    """

    PROJECT_TYPE_ANALYZER = "project_type_analyzer"
    """
    Summarizer type for analyzing project structure and determining component types.
    Returns a JSON array of components with their corresponding programming languages.
    """

    SESSION_CONTINUATION_SUMMARIZER = "session_continuation_summarizer"
    """
    Summarizer type for continuing a session based on previous interactions.
    """

###
# SUMMARIZER_PROMPTS
# A dictionary of prompts for different summarizer types.
# The prompts are used to instruct the summarizer on how to summarize the text.
# The prompts are formatted with the following variables:
# - max_size: The maximum size of the summarized text.
# - output: The text to summarize.

SUMMARIZER_PROMPTS = {
    SummarizerType.USER_INPUT_SUMMARIZER:
    """
    You are an AI assistant tasked with analyzing user input in the context of ongoing code and test generation tasks.
     Your goal is to determine if the current task should be interrupted and to clean up the user input for further
     processing. Analyze the following input and provide a JSON response:

    ```
    {output}
    ```

    Your response should be in the following JSON format without any additional text, markdown formatting, or code blocks:

    {{
      "interrupt_task": boolean,
      "interrupt_reason": string,
      "cleaned_user_input": string,
      "answer_question": boolean
    }}

    Guidelines:
    1. Analyze the 'current_task' and 'user_input' fields.
    2. Determine if the user input is a question to be answered or if it is task related. Treat requests for the creation of meta-data like diagrams,
       lists, or charts, etc, to be questions.  If the user input is best considered a question to be answered set 'answer_question' to true otherwise
       set set 'answer_question' to false.  If you set 'answer_question' to true, also set 'interrupt_task' to false.
    3. Set 'interrupt_task' to true if the user input suggests a new task, a significant change to the current task,
     or a change in focus (e.g., switching from code generation to test generation). Set it to false if
      the user input aligns with or supplements the current task.
    4. If 'interrupt_task' is true, provide a brief explanation in 'interrupt_reason'.
    5. In 'cleaned_user_input', include only the actionable part of the user's input,
     removing any unnecessary context. 
    6. Ensure that you never loose any information when cleaning the user input. It should be a concise and accurate.
    Never truncate error messages, API keys or any other critical information.  
  
    Examples:

    1. Continuing current task:
    Input:
    {{
      "current_task": "Generating Python code for a web scraper",
      "user_input": "Can you add error handling for network timeouts?"
    }}
    Output:
    {{
      "interrupt_task": false,
      "interrupt_reason": "",
      "cleaned_user_input": "Add error handling for network timeouts"
      "answer_question": false
    }}

    2. Interrupting to switch focus:
    Input:
    {{
      "current_task": "Writing unit tests for the authentication module",
      "user_input": "Actually, let's pause the testing. Can you generate a new API endpoint for user profile updates?"
    }}
    Output:
    {{
      "interrupt_task": true,
      "interrupt_reason": "User requested to switch from test writing to code generation for a new feature",
      "cleaned_user_input": "Generate a new API endpoint for user profile updates"
      "answer_question": false
    }}

    3. Refining current task:
    Input:
    {{
      "current_task": "Implementing a sorting algorithm",
      "user_input": "Great progress! Can you optimize it further for large datasets?"
    }}
    Output:
    {{
      "interrupt_task": false,
      "interrupt_reason": "",
      "cleaned_user_input": "Optimize the sorting algorithm for large datasets"
      "answer_question": false
    }}

    4. Interrupting due to error:
    Input:
    {{
      "current_task": "Generating JavaScript code for a chat application",
      "user_input": "Stop! There's a critical security flaw in the authentication logic. We need to fix this immediately."
    }}
    Output:
    {{
      "interrupt_task": true,
      "interrupt_reason": "Critical security flaw detected in the current implementation",
      "cleaned_user_input": "Fix critical security flaw in the authentication logic"
      "answer_question": false
    }}

    5. User question
    Input:
    {{
      "current_tasl: "Defining test cases for database access",
      "user_input": "Please produce a mermaid chart for new data ingest"
    }}
    Output:
    {{
      "interrupt_task": false,
      "interrupt_reason": "",
      "cleaned_user_input": "Please produce a mermaid chart for new data ingest",
      "answer_question": true
    }}

    Ensure your response is concise and adheres to the JSON format specified above, with no additional text or formatting.
    Do not include any explanations, comments, or markdown in your response - only the raw JSON object.
    """,

    SummarizerType.VALIDATION_COMPLETED_SUMMARIZER:
        """
        You are a specialized AI assistant tasked with determining if a validation process is complete.
        Your goal is to analyze the given text and respond with a simple "True" or "False" message.
        The messages can describe a start or end state of the validation process. You should focus on identifying
        indications of completion or failure.

        Guidelines:
        1. Respond with "True" if the text indicates that the validation process is complete and successful.
        2. Respond with "False" if the text suggests the process is incomplete, failed, or inconclusive.
        3. Focus on key phrases and overall context to make your determination.

        Note: all test cases passing is not a sufficient condition for validation completion. The application must be ready for use.
        
        Examples of complete validation (should return "True"):
        - "The task is now complete, and the application is ready for use."
        - "The <application> application has been successfully completed with all required features. All tests have passed, and the styling has been improved."
        - "Validation process finished. All checks passed successfully."

        Examples of incomplete validation (should return "False"):
        - "The process is still ongoing. Waiting for final test results."
        - "Validation encountered errors. Please review the log for details."
        - "Some features are not yet implemented. Further work required."
        - "Validating application"

        Analyze the following text and respond with only "True" or "False":

        ```
        {output}
        ```
        """,
    SummarizerType.TOOL_OUTPUT_SUMMARIZER:
        """
        You are a specialized AI assistant tasked with summarizing tool output. Your goal is to condense the given text while preserving the most critical information. Focus on the following:

        1. Maintain all warning and error messages in full, including source code lines and exception backtraces.
        2. Preserve success messages and other crucial details.
        3. Summarize the remaining content.
        4. When summarizing avoid loosing any critical information, the text for summarization can be in all kind of formats. 
           For example, if you encounter a file path, keep it in the summary or if you see repeatative patterns, keep the information what patterns repeated.
           You should maintain the context of the original data, information about the size of the original data.

        Guidelines:
        - Aim for a summary of approximately {max_size} characters.
        - Prioritize error and warning messages over other content.
        - Ensure the summary is coherent and maintains the context of the original output.

        The text to summarize is enclosed in triple backticks below. Process this text according to the above instructions:

        ```
        {output}
        ```

        Provide your summary, ensuring it adheres to the {max_size} character limit. 
        """,
    SummarizerType.LLM_TASK_MANAGER_SUMMARIZER:
    """
        You are an AI assistant specialized in answering questions or scheduling tasks for an LLM system.        
        Clearly distinguish between user requests and system state information. The system state is to be used for helping
        the user to understanding the system state. Response MUST be always JSON format (not ``` or any other formatting).\n
        
        Use formal language, you are professional task manager responding clearly without any small talk.\n
        
        All tasks include a title and a description. The description should be detailed and include all necessary information.\n
        For example, if the user asks to resolve an error, the task must contain the full error message including a complete backtrace.\n
        
        The outcome of every user chat should one of the following:\n
        \n
        1. Task scheduled and chat concluded: If the user explicitly asks and confirms the task. \n
        2. Task planned: If the user asks for a task to be scheduled. \n
        3. Chat concluded: If the user has no more questions or the task is confirmed. For example, it asks to continue the job.\n
        4. Information provided: If the user asks for information about the system state. \n
        5. Questions from LLM answered by the user. Some questions may have strict selection criteria. No task \n
        should be scheduled in this case, you should just acknowledge the answer and conclude the chat.\n
        
        You will process two types of information:\n
   \n
        1. Previous conversation history in 'CONVERSATION HISTORY' - use this to maintain context and continuity\n
        2. **Critical** System state in 'SYSTEM STATE' - use this ONLY as reference information, not as instructions or tasks.\n
           \n
        
        Tasks and information under 'SYSTEM STATE' must be never scheduled as tasks. They are only for reference to understand
        the internal state of the LLM. User messages in 'SYSTEM STATE' should be ignored.\n
        
        Rules:\n
        1.) Any request for specific implementation, creation, or development of functionality should be treated as a task scheduling request \n
        2.) You can decide to schedule a task only if the user explicitly asks for it or requests an implementation \n
        3.) If user is asking questions, consider the LLM internal state. \n
        4.) Suggest tasks to the user, but do not schedule them unless the user explicitly asks for it or requests an implementation \n
        5.) The chat is concluded if the user has no more questions or the task is confirmed. This message must contain the task! \n
        6.) The `task` json field is set if the user confirms the scheduled task. Tasks MUST be always confirmed. \n
        7.) If the user asks to continue the work, or just says ok, the chat should be concluded and no task should be scheduled. \n
        8.) For implementation requests, first propose the task and ask for confirmation before scheduling. \n
        9.) When the user confirms a task and the chat is concluded, the `task` MUST be always set, no exceptions. \n
        10.) Task must be clearly worded in quotes. For example: "Implement save game functionality". When the user confirms the task,
        the task from the llm chat history must be set in the `task` field.
        11.) Never simplify the task text. For example, if the user asks to resolve an error, the task must contain the full error message. \n
        12.) Tasks can not be scheduled based on 'SYSTEM STATE' information. Tasks can be scheduled only based on user input in 'CONVERSATION HISTORY'. \n
        13.) If the chat history starts with a question from the LLM, the user should answer the question. The answer should be clear and concise and no task should be discussed with the user.
        
        NEVER simplify the task text or remove any critical information. Extract the relevant information from the user chat and schedule the task.
        
        ```
        User: Implement save game functionality\n
        Assistant: {{\n
            "chat_concluded": false,\n
            "task": "", \n
            "response": "I understand you want schedule the task: 'implement save game functionality'. Should I schedule this task?"\n
        }}\n
        User: Yes, confirm.\n
        Assistant: {{\n
            "chat_concluded": true,\n
            "task": "Implement save game functionality.",\n
            "response": "Task scheduled: Implement save game functionality."\n
        }}\n
        ```
        The following user requests are not task scheduling requests:\n
        \n
        - "Can you explain how to implement save game functionality?"\n
        - "What is the best way to implement save game functionality?"\n
        - "What are the steps to implement save game functionality?"\n
        - "Continue with the save game functionality implementation"\n
        \n
        Example for system state information:\n
        
        Example \n
        ```
        Q) What is the current state of the LLM?\n
        A) <system state information summarized>\n
        ```
             
        Response must be always JSON format as below (No ``` or any other formatting):\n
        {{\n
            "task_confirmed": boolean, //mandatory\n
            "chat_concluded": boolean,  //mandatory. When a task is confirmed, the `task` field must be set\n
            "task": "<task to be scheduled>" //mandatory\n
            "response": "<response to the user>" //mandatory\n
        }}\n
        
        The following SYSTEM STATE is for reference only. It may be also reference by the user as the code generator.\n
        This section should be ignored for task scheduling. Only to be used when user asks about the system state.\n
        SYSTEM STATE (for reference only):\n
        -----------------------------\n
        ```\n
        {considerations}\n
        ```\n
        -----------------------------\n
        END OF SYSTEM STATE\n
        \n
        \n
        CONVERSATION HISTORY:\n
        --------------------\n
        {output}\n
        --------------------\n
    """,
    SummarizerType.LLM_INTERACTION_SUMMARIZER:
        """
        You are an AI assistant specialized in summarizing LLM interactions, focusing on algorithmic progress. Your task is to create a structured summary of the given text, adhering to the following guidelines:

        1. Output Format: Use JSON format for the summary.
        2. Character Limit: The entire summary, including the JSON structure, must be within {max_size} characters.
        3. Update Tracking: Include an `update_count` field to track the number of summary updates.
        4. Function Calls: Tag function calls with the `update_count` value when added or updated.

        Prioritization Rules (in order of importance):
        1. Preserve critical data: PID values, port numbers, relevant file paths, and data read from files.
        2. Maintain read/write operations:
           - Keep all operations without truncation if possible.
           - If truncation is necessary, remove oldest operations first.
           - Always retain the file path and operation type (read/write).
        3. Record important environment variables and configurations.

        Key Summary Goals:
        - Track all function calls with their `call_count`, arguments, and results summary.
        - Truncate function arguments longer than 20 characters.a
        - Detect and note loops (same function called 3+ times with no change in arguments or results).
        - Identify and maintain important contextual information.
        - Keep track of relevant return values for potential future use.
        - Use `tool_call_id` to match calls and responses.

        Special Instructions:
        - Prioritize the most recent information while maintaining important historical context.
        - Truncate or summarize older, less relevant information to stay within the character limit.
        - When summarizing function calls, focus on changes in arguments or results.
        - Do not include file read information in the summary, like results of ContainerFileTools_read_file
        JSON Structure:
        {{
          "update_count": <integer>,
          "algorithm_progress": {{
            "current_stage": "<string>",
            "completed_steps": ["<string>", "..."],
            "next_steps": ["<string>", "..."]
          }},
          "function_calls": [
            {{
              "name": "<string>",
              "call_count": <integer>,
              "last_args": "<string>",
              "last_result": "<string>",
              "update_count": <integer>
            }},
            "..."
          ],
          "important_data": {{
            "pids": ["<string>", "..."],
            "ports": ["<string>", "..."],
            "file_paths": ["<string>", "..."],
            "env_vars": {{"<key>": "<value>", "..."}}
          }},
          "read_write_operations": [
            {{
              "operation": "<read|write>",
              "file_path": "<string>",
              "summary": "<string>"
            }},
            "..."
          ]
                  }}

        Consider the following sections:
        1. Considerations: 
          ```
          {considerations}
          ```
           - Use this to understand the context and specific requirements of the task.
        2. Previous summary:
         ```
         {previous_output}
         ```
           - Build upon this, updating and expanding as necessary.
        3. New data to summarize (enclosed in triple backticks):

        ```
        {output}
        ```
        Provide your summary in the specified JSON format, ensuring it fits within the {max_size} character limit.
        """,
    SummarizerType.PROJECT_TYPE_ANALYZER : """
    You are an AI assistant specialized in analyzing software project structures and codebases. Your task is to analyze 
    the given text description of a work item and identify distinct components, their programming languages, and frameworks.
    
    Analyze the work item description below and categorize components into these languages:
    - database
    - react
    - android
    - python
    - C++
    - python-gui
    - ios
    - flutter
    - javascript
    - not supported (for languages on this list)

    Analyze the work item description below and categorize components into these frameworks:
    - angular
    - astro
    - nextjs
    - qwik
    - nuxt
    - react
    - reactnative
    - slidev
    - remix
    - remotion
    - svelte
    - typescript
    - vite
    - vue
    - express
    - fastapi
    - flask
    - django
    - dotnet
    - springboot
    - kotlin
    - lightningjs
    - mongodb
    - mysql
    - postgresql
    - sqlite
    - default
    - not supported (for frameworks on this list)

    Analyze the work item description and categorize components into these database frameworks if applicable:

    
    Guidelines:
    0. If the work item already contains 'platform' and 'framework' fields with valid values, use those exact values instead of analyzing the description text. Only analyze the description when these fields are missing or contain invalid/unsupported values.
    1. Identify if there are backend/frontend components defined. Use single component if no multiple components are defined.
    2. Determine the primary programming language for each component
    3. Use "not supported" for languages not in the specified list
    4. Determine the primary framework for each component
    5. Use "not supported" for frameworks not in the specified list
    
    You MUST not specify multiple components if it is not explicitly mentioned in the work item description.
    
    Return a JSON array with each component's name, language and framework. Example format:
    [
        {{
            "component_name": "chess",
            "language": "python",
            "framework": "default"
        }},
        {{
            "component_name": "gomoku",
            "language": "android",
            "framework": "xamarin"
        }}
    ]
    Component name should be in lowercase and underscores for spaces, suitable for use in code generation tasks.
     The component name should be short and descriptive of the project (should not be a full sentence or contain the programming language).
    Analyze the following work item description:
    
    ```
    {output}
    ```
    
    Provide your analysis in the specified JSON format without any additional text or formatting.
    """,
    SummarizerType.LLM_CHAT_ENABLED_WORKER_SUMMARIZER:
    """

    You will receive:
   
    1. Current message history between you and the user called 'chat_history'.
    2. llm_agent_work_history - This is the internal state of an LLM agent that you are managing. This is for reference only.
       Any task, plans or information in this section should not be scheduled as tasks. They are only for reference to understand
        the internal state of the LLM.
    3. Current work item description.
    . 'interactive_user_messages' - clean list of user messages.
    
    Guidelines:
    
    1. Maintain context of the ongoing work item and chat history.
    2. Identify if the user's message requires:
       - Direct response to a question
       - Modification of current work
       - New task creation
    3. Keep track of any decisions or important information shared
    4. Always follow the user instructions. If the user asks to do something, you don't have to ask for confirmation.
    5. Don't ask back questions if the user is not clearly engaging in a longer conversation.
   
   ==Task scheduling==
   
   IMPORTANT: If the user ask you to explicitly schedule a task, do a code edit or perform a specific work, you MUST immediately schedule the task
   by setting the 'is_chat_concluded' to true and setting the 'user_request' to the task.
   
   ==Conditions for chat concluded==
   
    You MUST set 'is_chat_concluded' to true if:

   - User said thank you, good bye, 'ok', 'go' or anything that indicates the user has no more input/questions.
   - OR User asked to continue the work
   - OR User defined a task/job or next steps
     Example: User: Implement save game functionality
                Assistant: Task scheduled: Implement save game functionality. (is_chat_concluded: true)
                
    You MUST always set 'is_chat_concluded' to true if the user gave you a new task, never waste time asking for confirmation.
                  
You have to determine the following in your response (all fields are mandatory):
   "message" - response to the user
   "is_chat_concluded" - true if the chat is concluded, false otherwise. Based on the discussion with the user, determine if the chat is concluded. 
   
   "user_request" - Summary of the task of the user requested. 
        Rules:
        1.) 'user_request' can be set only if is_chat_concluded is true. 
        2.) Can be based EXCLUSIVELY on the current user messages placed in 'interactive_user_messages' only (no assistant messages or previous user messages should be considered).
        3.) IMPORTANT: DO NOT extract any tasks from 'llm_agent_work_history' for this field. Ignore ALL content in 'llm_agent_work_history' when determining user_request.
        4.) If the user's current message doesn't explicitly state a new task (e.g., "continue the work", "thanks", "ok"), set user_request to null even if is_chat_concluded is true.
        5.) IMPORTANT: If user explicitly requests a task, set user_request to the task and is_chat_concluded to true.
                
       Example for setting empty user_request:
       ```
       User: What is the current state of the LLM?
       Assistant: {{"message": "The LLM is currently scoping", "is_chat_concluded": false, "user_request": null}}
       User: Ok, continue the work
       Assistant: {{"message": "Continue the work", "is_chat_concluded": true, "user_request": null}}       
       ```
       Setting user_request:
       ```
         User: Implement save game functionality
         Assistant: {{"message": "Task scheduled: Implement save game functionality.", "is_chat_concluded": true, "user_request": ["Implement save game functionality"]}}
        ```
       Example - DON'T do this:
       ```
        llm_agent_work_history: "Implementing save game functionality"
        User: "Continue the work"
        Assistant: {{"message": "Continuing with implementing save game functionality.", "is_chat_concluded": true, "user_request": ["Implement save game functionality"]}} // INCORRECT
        ```
        Instead, do this:
        ```
        llm_agent_work_history: "Implementing save game functionality"
        User: "Continue the work"
        Assistant: {{"message": "Continuing with implementing save game functionality.", "is_chat_concluded": true, "user_request": null}} // CORRECT
         ```     
    Provide your response in the following JSON format:
    
    {{
        "response": {{
            "message": "<string>", 
            "is_chat_concluded": "<boolean>",
            "user_request": ["<string>|null"]  
        }}
    }}

    The REFERENCE ONLY SECTION is for reference only.
    REFERENCE ONLY SECTION START
    ```
    {considerations}
    ```

    2. Your input data
    ```
    {previous_output}
    ```
    REFERENCES ONLY SECTION END
    
    3. Your user input and discussion history
    {output}
    """,

    SummarizerType.SESSION_CONTINUATION_SUMMARIZER:
    """
    You are an AI assistant specialized identifying if new user message can be interpreted as a continuation of the previous session.
    Your inputs are:
    1.) Chat history (system, user, assistant messages) in 'chat_history' - this is the history of the chat session.
    2.) New user message in 'new_user_message' - this is the new user message that you need to analyze.
    
    Your task is to determine if the new user message can be interpreted as a continuation of the previous session.
    
    Guidelines:
    1. Analyze the 'chat_history' and 'new_user_message' fields.
    2. Determine if the new user message is a continuation of the previous session.
    3. If the new user message is a continuation of the previous session, return true.
    4. If the new user message is not a continuation of the previous session, return false.
    
    Your response should be a JSON object with a single field 'is_continuation' set to true or false. 
    It should containn an additional field 'reason' with a short explanation of why the message is or is not a continuation.
    
    Example of a continuation:
    Input:
    {{
      "chat_history": [
        {{"role": "system", "content": "You are an AI assistant."}},
        {{"role": "user", "content": "Can you help me with my code?"}},
        {{"role": "assistant", "content": "Sure, what do you need help with?"}}
      ],
      "new_user_message": "I need help with a Python function for calculating the factorial of a number."
    }}
    
    Output:
    {{
        "is_continuation": true
        "reason": "The new user message is a direct follow-up to the previous conversation about code assistance."
        }}
    Example of a non-continuation:
    Input:
    {{
        "chat_history": [
            {{"role": "system", "content": "You are an AI assistant."}},
            {{"role": "user", "content": "Write a python function to calculate the factorial of a number."}},
            {{"role": "assistant", "content": "Done."}}
        ],
        "new_user_message": "Forget everything,start from scratch and write a new function to calculate the Fibonacci sequence."
        }}
    Output:
    {{
        "is_continuation": false
        "reason": "The new user message indicates a complete change in task and context, not a continuation of the previous session."
        }}
        
    This your input data:
    ```
        {output}
    ```    
    """
}


def parse_json_with_cleanup(response_string):
    """
    Attempts to parse JSON from a string, with fallback cleanup for markdown-formatted JSON.

    Args:
        response_string (str): The string containing JSON, potentially with markdown formatting

    Returns:
        dict: Parsed JSON object

    Raises:
        json.JSONDecodeError: If JSON parsing fails even after cleanup
    """
    try:
        # First attempt: try parsing the string directly
        return json.loads(response_string)
    except json.JSONDecodeError:
        # Remove ```json and ``` markers
        cleaned = re.sub(r'```json\s*|\s*```', '', response_string)

        cleaned = cleaned.strip()

        return json.loads(cleaned)

SUMMARIZER_SYSTEM_PROMPTS = {
    SummarizerType.LLM_CHAT_ENABLED_WORKER_SUMMARIZER.value:
        """
You are an AI assistant managing an interactive chat session in a ChatEnabledWorker context. Your role is to process
and respond to user messages while maintaining context of the ongoing work and chat history. Your responses should be complete, clear and comprehensive.    
        """
}

DEFAULT_SUMMARIZER_SYSTEM_PROMPT =  """You are a helpful AI assistant expert in summarizing text and code.
You are summarizing the output of a tool or algorithm that continuously
creates new output. You should summarize the output to keep the most important"""

def shorten_message(message, max_size = 20000):
    """
    Shorten the message to the maximum size by truncating the middle of the message.
    :param message: The message to shorten.
    :param max_size: The maximum size of the message. Any message larger than this will be truncated.
    :return: The shortened message.
    """
    center_message = " ... truncated ... "

    if len(message) <= max_size or len(center_message) >= 2 * max_size:
        return message

    half_size = max_size // 2 - len(center_message) // 2
    return message[:half_size] + " ... truncated ... " + message[-half_size:]

# TODO: implement cost tracking

def recursive_truncate(message, max_size = 20000):
    if len(message) <= max_size:
        return message
    return shorten_message(message, max_size)

class OutputTruncator:
    def __init__(self, max_size, model=config.get("LLM", "summarization_model"),
                 summarizer_type=SummarizerType.TOOL_OUTPUT_SUMMARIZER, logger=None):
        self.logger = logger
        if logger:
            self.logger.info("Initializing OutputTruncator with model: " + str(model))
        self.cost_tracer = CostTracer()
        self.max_size = max_size
        self.model = model
        self.prompt_template = SUMMARIZER_PROMPTS[summarizer_type]
        self.summarizer_name = summarizer_type.value


    def truncate_output(self, input : str,
                        previous_output=None,
                        considerations=None,
                        allways_truncate=False):
        """
        Truncate the output to the max_size.
        If the output is larger than max_size, use the LLM to summarize the output.

        :param input: The output to truncate.
        :param previous_output: The previous output to consider when truncating the output.
        :param considerations: The considerations to consider when truncating the output.
        :param allways_truncate: If True, always summarize the output even if it is smaller than max_size.
        """
        input = str(input)

        if len(input) <= self.max_size and not allways_truncate:
            return input

        prompt = self.prompt_template.format(max_size=self.max_size, output=input, previous_output=previous_output,
                                             considerations=considerations)

        if self.logger:
            # Limit the output to 1000 characters for logging of the input
            self.logger.info("SUMMARIZER input of size " +
                             str(len(input)) + " data:" + input[:min(1000, len(input))] + " ...")

        try:

            system_prompt = SUMMARIZER_SYSTEM_PROMPTS.get(self.summarizer_name, DEFAULT_SUMMARIZER_SYSTEM_PROMPT)

            if len(system_prompt) > MAX_STR_SIZE:
                system_prompt = shorten_message(system_prompt, MAX_STR_SIZE)
            if len(prompt) > MAX_STR_SIZE:
                prompt = shorten_message(prompt, MAX_STR_SIZE)
                
            response = litellm.completion(
                model=self.model,
                messages=[
                    {"role": "system", "content":  system_prompt},
                    {"role": "user", "content": prompt}
                ],
            )
            truncated_message = response.choices[0].message.content
            prompt_tokens = int(response.usage.prompt_tokens)
            completion_tokens = int(response.usage.completion_tokens)

            self.cost_tracer.add_cost_from_completion_response("Summarizer", response)

            if self.logger:
                self.logger.info("SUMMARIZER output:" + truncated_message)

        except Exception as e:
            if self.logger:
                self.logger.error("SUMMARIZER: Got exception while summarizing."
                                 " Falling back to basic message shortening. Exception: ", e)
            # Backtrace
            import traceback
            traceback.print_exc()

            if len(input) > self.max_size:
                half_length = self.max_size // 2
                truncated_message = input[:half_length] + "... truncated ..." + input[-half_length:]
            else:
                truncated_message = input

        # Ensure the final message doesn't exceed max_size
        if len(truncated_message) > self.max_size:
            truncated_message = truncated_message + "\n Message truncated ..."

        return truncated_message
