import json
import os
import threading
from enum import Enum

from code_generation_core_agent.agents.micro.agent_system import MicroAgentFactory
from code_generation_core_agent.agents.tools.combined_tools import CombinedTools
from code_generation_core_agent.config import config
from code_generation_core_agent.llm.llm_interface import LLMInterface


class MicroAgentType(Enum):
    # Micro agent responsible for initial setup
    INITIAL_SETUP_MICRO_AGENT = "InitialSetupAgent"
    # All micro agents responsible for code generation
    CODER_MICRO_AGENTS = "CodeGenMicroAgents"
    # Asset extractor micro agent
    # Micro agents responsible for building the final container
    CONTAINER_BUILDER_MICRO_AGENT = "ContainerBuilderAgent"


class MicroAgentRegistry:
    """
    MicroAgentRegistry class is responsible for managing the micro agents and their capabilities.
    Dependending on the agent type it will return the capabilities of the micro agents and the agents of the given type
    """
    chat_workers = {}
    micro_agents = {}

    def get_chat_workers(self):
        return self.chat_workers

    def get_agent(self, agent_type):
        """
        Get an agent by name, initializing it lazily if it doesn't exist yet
        """
        with self._lock:
            # Return the agent if it's already initialized
            if agent_type in self.micro_agents:
                return self.micro_agents[agent_type]

            # Otherwise, initialize the agent on demand
            try:
                from code_generation_core_agent.chat.chat_enabled_worker import ChatEnabledWorker

                chat_worker = ChatEnabledWorker([], agent_type)

                micro_llm = LLMInterface(llm_api_key=None, session_dir=self.execution_base_path,
                                         instance_name=f"{agent_type}", chat_worker=chat_worker)

                self.chat_workers[agent_type] = chat_worker

                try:
                    custom_model_name = config.get('LLM_MODEL_MAPPING', agent_type)
                except Exception:
                    custom_model_name = self.model_name

                self.logger.info(f"Lazy initializing agent {agent_type} with model {custom_model_name}")
                agent = self.agent_factory.create(
                    agent_type,
                    micro_llm,
                    self.execution_base_path,
                    self.logger,
                    custom_model_name,
                    self.agent_tools,
                    tools_to_use=self.micro_agent_config['micro_agents'][agent_type]['tools'],
                    setup_steps=self.micro_agent_config['micro_agents'][agent_type].get('setup_steps', []),
                    reporter=self.reporter
                )
                self.micro_agents[agent_type] = agent
                chat_worker.set_micro_agent(agent, self)

                return agent
            except Exception as e:
                import traceback
                traceback.print_exc()
                self.logger.error(f"Error lazily initializing {agent_type}: {str(e)}")
                return None

    def _extract_micro_agents_and_capabilities(self, agent_name):
        micro_agents = self.micro_agent_config['task_agents'].get(agent_name, {}).get('micro_agents', [])
        capabilities = {}
        for agent in micro_agents:
            agent_config = self.micro_agent_config['micro_agents'].get(agent, {})
            capabilities[agent] = agent_config.get('capabilities', [])
        return capabilities

    def configure_micro_agents_for_agent(self, agent_name):
        self.capabilities = self._extract_micro_agents_and_capabilities(agent_name)

        for key, value in self.capabilities.items():
            if key == MicroAgentType.INITIAL_SETUP_MICRO_AGENT.value:
                self.capabilities_mapping.setdefault(MicroAgentType.INITIAL_SETUP_MICRO_AGENT.value, {})[key] = value
            elif key == MicroAgentType.CONTAINER_BUILDER_MICRO_AGENT.value:
                self.capabilities_mapping.setdefault(MicroAgentType.CONTAINER_BUILDER_MICRO_AGENT.value, {})[key] = value
            else:
                self.capabilities_mapping.setdefault(MicroAgentType.CODER_MICRO_AGENTS.value, {})[key] = value

    def interrupt_all_agents(self, interrupt_reason: str):
        with self._lock:
            for key, agent in self.micro_agents.items():
                # Note: InitialSetupAgent should not be interrupted
                if key != MicroAgentType.INITIAL_SETUP_MICRO_AGENT.value:
                    interrupted = agent.interrupt_llm(interrupt_reason)
                    if interrupted:
                        self.logger.info(f"Interrupted agent {key}: {interrupted}")

    def get_capabilities(self, micro_agent_type: MicroAgentType):
        return self.capabilities_mapping.get(micro_agent_type.value, {})

    def get_agents(self, micro_agent_type: MicroAgentType) -> dict:
        """
        Get a dictionary of agents of the given type, lazily initializing them as needed
        """
        agents = {}
        for agent_key in self.capabilities_mapping.get(micro_agent_type.value, {}):
            agent = self.get_agent(agent_key)
            if agent:
                agents[agent_key] = agent

        return agents
    


    def get_micro_agent_names(self):
        return list(self.capabilities.keys())

    def __init__(self, agent_name, micro_agent_config, logger,
                 prompts_base_path: str, model_name: str,
                 execution_base_path: str,
                 reporter,
                 agent_tools):

        self.micro_agent_config = micro_agent_config
        self.model_name: str = model_name
        self.execution_base_path: str = execution_base_path
        self.logger = logger
        self.capabilities_mapping = {}
        self.agents = {}
        self.capabilities = {}
        self.prompts_base_path = prompts_base_path

        self.agent_factory: MicroAgentFactory = MicroAgentFactory(self.micro_agent_config, self.prompts_base_path)
        self.configure_micro_agents_for_agent(agent_name)
        self._lock = threading.Lock()
        self.work_item = None
        self.reporter = reporter
        self.agent_tools = agent_tools
        self.operation_mode = "container"



    def is_valid_micro_agent(self, agent_name: str):
        """
        Check if the agent name is valid based on configured capabilities
        """
        return agent_name in self.capabilities.keys()

    async def generate_welcome(self, agent_name, work_item, llm):
        system_prompt = "You are an expert software engineer."
        self.work_item = work_item

        if agent_name == "CodeGeneration":
            user_prompt = f"Your task is to formulate a welcome message to the user who is developing this project: {work_item.get('component_name')}.\n" \
                          f"The project description is {work_item.get('description')}.  Formulate a brief, business like, welcome message that shows that you understand the task " \
                          "and you are ready to assist the user in completing it. " \
                          "Please format your final response as a JSON object with the following structure:\n" \
                          "{{ \n" \
                          "   'welcome_message': '<welcome message to the user>', \n" \
                          "}}\n"
        elif agent_name == "CodeMaintenance":
            user_prompt = f"Your task is to formulate a welcome message to the user who is working with this codebase: {work_item.get('component_name')}.\n" \
                          f"The codebase description is {work_item.get('description')}.  Formulate a brief, business like, welcome message that shows that you understand the task " \
                          "and you are ready to assist the user by answering questions about the codebase or making modifications to it. " \
                          "Please format your final response as a JSON object with the following structure:\n" \
                          "{{ \n" \
                          "   'welcome_message': '<welcome message to the user>', \n" \
                          "}}\n"
        else:
            raise ValueError(f"Invalid agent name: {agent_name}")

        schemas = None
        validator = None
        executor = None

        completion = await llm.llm_interaction_wrapper(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            user_prompt=None,
            system_prompt=None,
            model=self.model_name,
            response_format={"type": "json_object"},
            function_schemas=schemas,
            function_executor=executor,
            function_validator=validator
        )
        # Print type of completion
        response = json.loads(completion.choices[0].message.content)

        if 'welcome_message' in response:
            welcome_message = response['welcome_message']
        else:
            welcome_message = "Welcome!  Please let me know how I can help?"

        return welcome_message
    
    def finalize(self):
        """
        Finalize the registry, cleaning up resources if necessary
        """
        self.micro_agents.clear()
        self.chat_workers.clear()
        self.agent_factory.finalize()
        self.logger.info("MicroAgentRegistry finalized.")
