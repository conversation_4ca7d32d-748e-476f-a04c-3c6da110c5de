import os
import base64
import shlex

from code_generation_core_agent.config import config
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
from code_generation_core_agent.agents.framework.cost_tracker import CostTracer

from .knowledge import Knowledge
from .knowledge import KnowledgeReporter
from .knowledge import KnowledgeHelpers
from .knowledge import KnowledgeCodeBase

class Reporter(KnowledgeReporter):
    def __init__(self, agent):
        self.agent = agent

    def send_agent_message(self, message):
        self.agent.logger.info(message)

    def cost_update_callback(self, all_costs, total_cost ):
        self.agent.reporter.cost_update_callback( all_costs, total_cost)

class Helpers(KnowledgeHelpers):
    def __init__(self, agent):
        self.agent = agent
        self.base_path = agent.base_path
        self._fastpath = False
        self._check_fast_path_available()

    def _check_fast_path_available(self):
        import random
        import string
        characters = string.ascii_letters + string.digits
        str = ''.join(random.choice(characters) for _ in range(20))
        filename = os.path.join(self.base_path,'.fptest')
        with open(filename, 'w') as file:
            file.write(str)
        contents = self.read_file(filename)
        if contents.__eq__(str):
            self._fastpath = True
        os.remove(filename)

    def execute_cmd(self, cmd):
            allow_fastpath = True
            no_fast_cmds = { 'ctags' }
            for no_fast in no_fast_cmds:
                if no_fast in cmd:
                    allow_fastpath = False

            if allow_fastpath and self._fastpath:
                import subprocess
                try:
                    result = subprocess.run(cmd, capture_output=True, shell=True, text=True)
                    return result.stdout, result.returncode
                except Exception as e:
                    print(f"Error: cmd {cmd} had exception {e}")
                return None, None

            timeout = config.getint("TOOLS", "shell_command_timeout")
            async def _one_shot_docker_command():
                stdout_data, returncode = await self.agent.executor.create_subprocess_shell_one_shot(cmd)
                return stdout_data, returncode
            try:
                future = shared_loop.run_coroutine(_one_shot_docker_command())
                output, returncode = future.result(timeout=timeout)
                return output, returncode
            except Exception as e:
                print(f"Error: cmd {cmd} had exception {e}")
            return None, None

    def check_file_exists(self, filename):
        exists = False
        if self._fastpath:
            if os.path.exists(filename):
                exists = True
        else:
            cmd = f'ls -al {filename}'
            output, returncode = self._execute_cmd(cmd)
            if returncode == 0:
                exists = True
        return exists

    def read_file(self, filename):
        contents = None
        if self._fastpath:
            try:
                with open(filename, 'r', encoding='utf-8', errors='ignore') as file:
                    contents = file.read()
            except (FileNotFoundError, UnicodeDecodeError):
                pass
        else:
            cmd = f'cat {filename}'
            output, returncode = self.execute_cmd(cmd)
            if returncode == 0:
                contents = output
        return contents
    
    def write_file(self, filename, content):
        escaped_path = shlex.quote(filename)
        encoded_new_content = base64.b64encode(content.encode('utf-8')).decode('utf-8')
        cmd = f"mkdir -p $(dirname {escaped_path}) && echo '{encoded_new_content}' | base64 -d > {escaped_path}"
        output, returncode = self.execute_cmd(cmd)
        return returncode

    def list_directory(self, directory):
        list = []
        if self._fastpath:
            try:
                list = os.listdir(directory)
            except FileNotFoundError:
                pass
            except NotADirectoryError:
                pass
        else:
            cmd = f'ls -1 {directory}'
            output, returncode = self.execute_cmd(cmd)
            if returncode == 0:
                list = output.splitlines()
        return list

class Knowledge_Helper:
    def __init__(self, agent, id: str = "default"):
        timeout = config.getint("TOOLS", "shell_command_timeout")
        model =  config.get("LLM", "summarization_model")
        file_types = ''.join(config.get("KNOWLEDGE","ingestible_filetypes").split()).split(',')
        num_ingest_threads = int(config.get("KNOWLEDGE","knowledge_ingest_threads"))

        configuration = {
            "base_path" : agent.base_path,
            "model" : model,
            "timeout": timeout,
            "file_types": file_types,
            "cost_tracer" : CostTracer(),
            "reporter" : Reporter(agent),
            "helpers" : Helpers(agent),
            "num_ingest_threads" : num_ingest_threads
        }

        if hasattr(agent, 'codebases') and agent.codebases:
            # Handle both string (backward compatibility) and list cases
            container_list = agent.codebases
            if isinstance(container_list, str):
                # Legacy string handling - split by spaces
                container_list = container_list.split()
                
            if len(container_list) > 0:
                kcb = []
                for container_name in container_list:
                    # Find the workspace path for this container name
                    codebase_path = None
                    if hasattr(agent, 'container_registry') and container_name in agent.container_registry:
                        workspace = agent.container_registry[container_name].get('workspace')
                        if workspace:
                            codebase_path = os.path.join(agent.base_path, workspace)
                        
                    kcb.append(KnowledgeCodeBase(codebase_path, container_name))
                
                configuration["code_bases"] = kcb
                
        self.knowledge = Knowledge.getKnowledge(configuration, id)

