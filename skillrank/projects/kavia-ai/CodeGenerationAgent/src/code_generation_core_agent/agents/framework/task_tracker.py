import os
import fcntl

from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function


class TaskTracker(BaseToolInterface):
    """
    Minimal Task tracker with only read and write operations.
    The LLM is fully responsible for interpreting and constructing the task data.
    """

    def get_tool_name(self) -> str:
        return "TaskTracker"

    def _load_raw_data_from_file(self) -> str:
        """Load raw data from save file if it exists, otherwise return empty string"""
        try:
            with open(self.save_location, 'r') as f:
                fcntl.flock(f.fileno(), fcntl.LOCK_SH)
                try:
                    return f.read()
                finally:
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)
        except FileNotFoundError:
            return "{}"

    def _save_raw_data_to_file(self, raw_data: str) -> None:
        """Save the raw data to file with proper locking"""
        # Ensure directory exists
        os.makedirs(os.path.dirname(self.save_location), exist_ok=True)

        with open(self.save_location, 'w') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            try:
                f.write(raw_data)
            finally:
                fcntl.flock(f.fileno(), fcntl.LOCK_UN)

    def __init__(self, callback_functions, base_path, logger, save_location: str,
                 task_description: str, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.save_location = save_location

        # Initialize file with empty structure if it doesn't exist
        if not os.path.exists(self.save_location):
            initial_data = f"""{{
  "task_description": "{task_description}",
  "tasks": {{}},
  "next_task_id": 1
}}"""
            self._save_raw_data_to_file(initial_data)

    @register_function(
        description="""
        Get the full task list data.
        This is the read operation.

        The returned data will follow this structure:
        {
          "task_description": "Overall task description text",
          "tasks": {
            "Task1": {
              "description": "Main task description",
              "task_description": "More detailed description for the main task",
              "status": "to-do|in-progress|completed",
              "details": "Additional details for the task",
              "subtasks": {
                "Task1SubTask1": {
                  "description": "Description of the subtask",
                  "status": "to-do|in-progress|completed",
                  "micro_agent": "List of comma separated micro-agents for this task",
                  "details": "Additional details for the subtask"
                },
                "Task1SubTask2": { ... }
              }
            },
            "Task2": { ... }
          }
        }
        """,
        parameters={
            "type": "object",
            "strict": True,
            "properties": {},
            "additionalProperties": False
        }
    )
    def read(self) -> dict:
        """
        Read and return the entire task list data.
        """
        raw_data = self._load_raw_data_from_file()

        return {
            "status": "SUCCESS",
            "tasks_data": raw_data
        }

    @register_function(
        description="""
        Replace the entire task list with the provided data.
        This is the write operation.
        The LLM must maintain the proper structure of tasks and subtasks.
        Always get the current task list first with read() before updating.

        The data must follow this structure:
        {
          "task_description": "Overall task description text",
          "tasks": {
            "Task1": {
              "description": "Main task description",
              "task_description": "More detailed description for the main task",
              "status": "to-do|in-progress|completed",
              "details": "Additional details for the task",
              "subtasks": {
                "Task1SubTask1": {
                  "description": "Description of the subtask",
                  "status": "to-do|in-progress|completed",
                  "micro_agent": "List of comma separated micro-agents for this task",
                  "details": "Additional details for the subtask"
                },
                "Task1SubTask2": { ... }
              }
            },
            "Task2": { ... }
          }
        }

        - Task IDs must follow the format "TaskX" where X is a number
        - Subtask IDs must follow the format "TaskXSubTaskY" where X matches the parent task number and Y is a subtask number
        - Status values must be one of: "to-do", "in-progress", or "completed"
        - The next_task_id should be set to the next task number to use
        """,
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "tasks_data": {"type": "string",
                               "description": "Complete data containing the entire task list structure following the specified format"}
            },
            "required": ["tasks_data"]
        }
    )
    def write(self, tasks_data: str) -> dict:
        """
        Update the entire task list with the provided data.
        The LLM is responsible for ensuring the data has the correct structure.
        """
        # Save the raw data without any validation
        self._save_raw_data_to_file(tasks_data)

        return {
            "status": "SUCCESS",
            "message": "Task list updated successfully"
        }