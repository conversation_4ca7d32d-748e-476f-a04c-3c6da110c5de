import os
import signal
import threading
import time
from abc import ABCMeta, abstractmethod
from dataclasses import field, dataclass
from typing import Dict, <PERSON><PERSON>, Tu<PERSON>, Any
import litellm


from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.config import config


def print_cache_stats_table(cache_stats, logger):
    """Simple ASCII table for cache statistics based on litellm fields"""
    if not cache_stats:
        logger.info("No cache statistics available.")
        return

    logger.info("\n" + "=" * 100)
    logger.info("PROMPT CACHE STATISTICS (Provider-level caching)")
    logger.info("=" * 100)
    logger.info(f"{'Model':<30} {'Requests':>10} {'Cache Reads':>12} {'Cache Writes':>13} {'Hit%':>8}")
    logger.info("-" * 100)

    for model, stats in cache_stats.items():
        hit_percentage = (stats['cache_hits'] / stats['total_requests'] * 100) if stats['total_requests'] > 0 else 0
        logger.info(f"{model:<30} "
              f"{stats['total_requests']:>10} "
              f"{stats['cache_read_tokens']:>11,} "
              f"{stats['cache_write_tokens']:>12,} "
              f"{hit_percentage:>7.1f}%")

    logger.info("-" * 100)


class CircuitBreakerCallback(metaclass=ABCMeta):

    @abstractmethod
    def trigger_warning(self, limit : int):
        pass

    @abstractmethod
    def trigger_pause(self, limit : int):
        pass

    @abstractmethod
    def trigger_error(self, limit : int):
        pass

class CircuitBreaker:
    def __init__(self, warning_limit=9, pause_limit=9.5, break_limit=10):
        self.warning_limit = warning_limit
        self.pause_limit = pause_limit
        self.break_limit = break_limit
        self.callbacks : set[CircuitBreakerCallback] = set()

    def add_callback(self, callback):
        self.callbacks.add(callback)

    def trigger_warning(self, limit):
        for cb in self.callbacks:
            cb.trigger_warning(limit=self.warning_limit)

    def trigger_pause(self, limit):
        for cb in self.callbacks:
            cb.trigger_pause(limit=self.pause_limit)

    def trigger_error(self, limit):
        # Run in a thread: sleep for 3 sec and kill this process
        import threading

        # In 3 sec we exit the process with a kill signal, no turning back after this point
        graceful_thread = threading.Thread(target = (lambda: time.sleep(3) or os.kill(os.getpid(), signal.SIGINT)) , daemon=True)

        # We give 60 sec for the process to exit gracefully, if not we kill it with a kill signal
        force_kill_thread = threading.Thread(target = (lambda: time.sleep(60) or os.kill(os.getpid(), signal.SIGKILL)) , daemon=True)

        graceful_thread.start()
        force_kill_thread.start()

        for cb in self.callbacks:
            cb.trigger_error(limit=self.break_limit)

        print("***** COST LIMIT REACHED! EXITING! ******")
        # Lock here the execution. No more processing after this
        while True:
            time.sleep(1)

@dataclass
class CacheStats:
    """Cache statistics based on litellm usage fields"""
    total_requests: int = 0
    cache_hits: int = 0  # requests that had cache_read_tokens > 0
    cache_read_tokens: int = 0  # from cache_read_input_tokens
    cache_write_tokens: int = 0  # from cache_creation_input_tokens

class CacheTracker:
    """Tracks prompt cache metrics from litellm usage objects"""

    def __init__(self):
        self._lock = threading.Lock()
        self._stats = {}

    def process_response(self, response: Any) -> Dict[str, Any]:
        """Process litellm response and extract cache metrics from usage object"""
        model = getattr(response, 'model', 'unknown')
        usage = getattr(response, 'usage', None)

        if not usage:
            return {'model': model, 'cache_hit': False}

        cache_read_tokens = getattr(usage, 'cache_read_input_tokens', 0)
        cache_write_tokens = getattr(usage, 'cache_creation_input_tokens', 0)

        # Check OpenAI format first (they use different field names)
        if hasattr(usage, 'prompt_tokens_details') and usage.prompt_tokens_details:
            prompt_details = usage.prompt_tokens_details
            # OpenAI uses 'cached_tokens' for cache hits
            openai_cached = getattr(prompt_details, 'cached_tokens', 0)
            if openai_cached and openai_cached > 0:
                cache_read_tokens = openai_cached

        # For OpenAI, cache creation is often tracked differently
        # They may not expose cache_creation_input_tokens directly

        is_cache_hit = cache_read_tokens > 0

        with self._lock:
            stats = self._stats.setdefault(model, CacheStats())
            stats.total_requests += 1
            stats.cache_read_tokens += cache_read_tokens
            stats.cache_write_tokens += cache_write_tokens

            if is_cache_hit:
                stats.cache_hits += 1

        return {
            'model': model,
            'cache_hit': is_cache_hit,
            'cache_read_tokens': cache_read_tokens,
            'cache_write_tokens': cache_write_tokens
        }

    def get_all_stats(self) -> Dict[str, Dict[str, int]]:
        """Get cache statistics for all models"""
        with self._lock:
            return {
                model: {
                    'total_requests': stats.total_requests,
                    'cache_hits': stats.cache_hits,
                    'cache_read_tokens': stats.cache_read_tokens,
                    'cache_write_tokens': stats.cache_write_tokens
                }
                for model, stats in self._stats.items()
            }

    def reset(self):
        """Reset all statistics"""
        with self._lock:
            self._stats.clear()


class CostTracer:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(CostTracer, cls).__new__(cls)
                    cls._instance._costs = {}
                    cls._instance._cost_lock = threading.Lock()
                    cls._instance._cache_tracker = CacheTracker()
                    cls._instance.logger = setup_logger("CostTracer", "/tmp")
                    cls._instance.circuit_breaker = CircuitBreaker(
                        warning_limit=config.getfloat('LLM', 'cost_warning_limit', fallback=8.0),
                        pause_limit=config.getfloat('LLM', 'cost_pause_limit', fallback=9.0),
                        break_limit=config.getfloat('LLM', 'cost_error_limit', fallback=11.0)
                    )
                    cls._instance.cost_warning_dispatched = False
                    cls._instance.cost_pause_dispatched = False

        return cls._instance

    def extend_circuit_breaker_limits(self, scale_factor: float):
        """
        Extend the circuit breaker limits by a factor.
        The factor must be a positive number greater than 1.0 and less than or equal to 5.0.
        """

        if scale_factor <= 1.0 or scale_factor >5.0:
            raise ValueError("Factor must be a positive number greater than 1.0 and less than or equal to 5.0")

        with self._lock:
            self.circuit_breaker.warning_limit = config.getfloat('LLM', 'cost_warning_limit', fallback=8.0) * scale_factor
            self.circuit_breaker.pause_limit = config.getfloat('LLM', 'cost_pause_limit', fallback=9.0) * scale_factor
            self.circuit_breaker.break_limit = config.getfloat('LLM', 'cost_error_limit', fallback=11.0) * scale_factor

            # Reset the dispatched flags
            self.cost_warning_dispatched = False
            self.cost_pause_dispatched = False


            self.logger.info(f"Extended circuit breaker limits by {scale_factor}. New limits: "
                             f"warning={self.circuit_breaker.warning_limit}, "
                             f"pause={self.circuit_breaker.pause_limit}, "
                             f"break={self.circuit_breaker.break_limit}")

    def register_cost_threshold_callback(self, callback : CircuitBreakerCallback):
        """
        Register a callback to be notified when the cost threshold is reached.
        There are three callbacks:
        - warning: when the cost is close to the warning limit
        - pause: when the cost is close to the pause limit
        - error: when the cost is close to the error limit
        """
        self.circuit_breaker.add_callback(callback)

    def set_log_dir(self, log_dir):
        with self._lock:
            self._instance.logger = setup_logger("CostTracer", log_dir)

    def add_cost(self, source: str, cost: float):
        """Add cost to a specific source"""
        with self._cost_lock:
            if source in self._costs:
                self._costs[source] += cost
            else:
                self._costs[source] = cost

    def get_cost(self, source: str) -> float:
        """Get cost for a specific source"""
        with self._cost_lock:
            return self._costs.get(source, 0.0)

    def get_total_cost(self) -> float:
        """Get the total cost across all sources"""
        with self._cost_lock:
            return sum(self._costs.values())

    def get_all_costs(self) -> Dict[str, float]:
        """Get all costs by source"""
        with self._cost_lock:
            return dict(self._costs)

    def reset_costs(self):
        """Reset all costs"""
        with self._cost_lock:
            self._costs.clear()

    @staticmethod
    def get_provider(model: str) -> Optional[str]:
        """Get the provider for a model using litellm.get_llm_provider"""
        try:
            _, provider, _, _ = litellm.get_llm_provider(model=model)
            return provider
        except Exception:
            return None


    def add_cost_by_tokens(self, source: str, model: str, prompt_tokens: int, completion_tokens: int):
        """
        Add cost based on token usage, using litellm's cost calculation

        Args:
            source: The source to attribute cost to
            model: Model name (e.g., 'gpt-4', 'claude-3-5-sonnet-20240620')
            prompt_tokens: Number of tokens in the prompt
            completion_tokens: Number of tokens in the completion
        """
        try:
            # Use LiteLLM's cost_per_token function to calculate costs
            prompt_cost, completion_cost = litellm.cost_per_token(
                model=model,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens
            )

            # Calculate total cost
            total_cost = prompt_cost + completion_cost
            # Add to our tracker
            self.add_cost(source, total_cost)

            return total_cost
        except Exception as e:
            raise ValueError(f"Error calculating cost for model '{model}': {str(e)}")

    def _notify_circuit_breaker(self, cost):
        """
        Notify the circuit breaker about the cost.
        :param cost:  - cost in USD
        :return:e
        """
        if cost >= self.circuit_breaker.break_limit:
            self.logger.info(f"Cost break limit reached: {cost} USD. Terminating process.")
            self.circuit_breaker.trigger_error(self.circuit_breaker.break_limit)
        elif cost >= self.circuit_breaker.pause_limit and not self.cost_pause_dispatched:
            self.logger.info(f"Cost pause limit reached: {cost} USD. Pausing operations.")
            self.circuit_breaker.trigger_pause(self.circuit_breaker.pause_limit)
            self.cost_pause_dispatched = True
        elif cost >= self.circuit_breaker.warning_limit and not self.cost_warning_dispatched:
            self.logger.info(f"Cost warning limit reached: {cost} USD. Notifying.")
            self.circuit_breaker.trigger_warning(self.circuit_breaker.warning_limit)
            self.cost_warning_dispatched = True
        else:
            pass

    def add_cost_from_completion_response(self, source: str, completion_response):
        """
        Extract and add cost from a litellm completion response

        Args:
            source: The source to attribute cost to
            completion_response: Response from litellm.completion()
        """
        try:
            from litellm import completion_cost
            cost = completion_cost(completion_response)
            self._cache_tracker.process_response(completion_response)

            # Format the cache stats for better readability
            cache_stats = self._cache_tracker.get_all_stats()
            print_cache_stats_table(cache_stats, self.logger)

            if cost > 0:
                all_cost = self.get_total_cost()
                if int(all_cost + cost) != int(all_cost):
                    self.logger.info("[ Cost Breakdown ]")
                    for key, value in self._costs.items():
                        self.logger.info(f"{key}: {value} $")
                    self.logger.info("******************")

                self.add_cost(source, cost)
            else:
                usage = completion_response.usage
                cost = self.add_cost_by_tokens(
                    source=source,
                    model=completion_response.model,
                    prompt_tokens=usage.prompt_tokens,
                    completion_tokens=usage.completion_tokens
                )

            total_cost = self.get_total_cost()
            self.logger.info(f"Added cost {cost}$ to source '{source}' for model '{completion_response.model}' total cost {total_cost}", extra={
                "cost": cost,
                "source": source,
                "model": completion_response.model,
                "total_cost": total_cost,
            })
            self._notify_circuit_breaker(total_cost)

            return cost
        except Exception as e:
            raise ValueError(f"Error extracting cost from completion response: {str(e)}")

    @staticmethod
    def get_model_pricing(model: str) -> dict[str, float]:
        """
        Get pricing information for a model using litellm

        Args:
            model: Model name

        Returns:
            Tuple of (input_cost_per_token, output_cost_per_token)
        """
        try:
            input_cost, output_cost = litellm.cost_per_token(
                model=model,
                prompt_tokens=1000,
                completion_tokens=1000
            )

            return {'input_token_cost': input_cost / 1000, 'output_token_cost' :output_cost / 1000 }
        except Exception as e:
            raise ValueError(f"Error getting pricing for model '{model}': {str(e)}")

# Usage example:
# cost_tracer = CostTracer()
# cost_tracer.add_cost("AgentA", 10.5)
# cost_tracer.add_cost("AgentB", 5.2)
# total_cost = cost_tracer.get_total_cost()
# all_costs = cost_tracer.get_all_costs()