#!/usr/bin/env python3
"""
User Agent Module

This module contains classes for user agent orchestration in the code generation system.
It provides a meta-assistant that uses an LLM to drive a software engineering agent.
"""
from datetime import datetime
import os
import time
import json
import logging
import asyncio
from typing import Any, Dict, Optional, List
from pathlib import Path
import jinja2

from code_generation_core_agent.agents.framework.knowledge import Knowledge
from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader
from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType
from code_generation_core_agent.chat.chat_enabled_worker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>orker
from code_generation_core_agent.llm.chat_interface import ChatInterface
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.task_execution_agent import (
    TaskExecutionAgent,
    TaskExecutionControl,
    TaskExecutionReporter,
)
from code_generation_core_agent.version import VERSION


# Apply nest_asyncio to allow nested event loops
import nest_asyncio
nest_asyncio.apply()


def stop_cga(swe_agent: Optional[TaskExecutionAgent] = None) -> None:
    """
    Stop the Code Generation Agent (CGA) gracefully.
    
    This function is intended to be called when the user agent decides to terminate the session.
    It releases knowledge resources and shuts down the browser if applicable.
    """
    from code_generation_core_agent.agents.tools.web_browser import shutdown_browser
    shutdown_browser()
    Knowledge.releaseKnowledge("default")
    
    # Finally stop the agent
    if swe_agent:
        swe_agent.stop()   


def answer_task_execution_agent(user_agent, swe_agent, swe_agent_message: str, logger: logging.Logger) -> str:
    """
    Generate a response to the Task Execution Agent.
    This global function can be used by various components that need to interact with the Task Execution Agent.
    
    Args:
        swe_agent_message (str): Message from the Task Execution Agent
        logger (logging.Logger): Logger instance to use for logging
        user_agent: User agent instance to generate the response
        swe_agent: Optional software engineering agent that can be stopped if needed
            
    Returns:
        str: Response to the Task Execution Agent
    """
    logger.info(f"Task Execution Agent: {swe_agent_message}")
    response = user_agent.generate_answer(swe_agent_message)
    response_msg = response.get("content", "").strip()
    
    # Check if the user agent wants to terminate the session
    if response_msg.endswith("GOODBYE!"):
        logger.info("Session terminated by user agent.")
        stop_cga(swe_agent)
    else:
        logger.info(f"User Task Agent: {response_msg}")
        
    return response


class UserAgent:
    """
    A meta-assistant that uses an LLM to drive a software engineering agent.
    
    This class acts as a simulated user that interacts with the software engineering agent,
    providing guidance and responding to queries from the agent.
    
    Attributes:
        workspace (str): The workspace directory path
        model (str): The LLM model to use
        task (str): The task description
        system_prompt (str): The system prompt for the LLM
        user_agent (LLMInterface): The LLM interface for the user agent
        messages (List[Dict]): The message history
        response_format (Optional[Dict]): Format specification for LLM responses
    """

    def __init__(self, workspace: str, model: str, task: Optional[str] = None, task_guidelines: Optional[str] = None, prompts_dir: Optional[str] = None, system_prompt: Optional[str] = None, instance_name: Optional[str] = None):
        """
        Initialize a UserAgent.
        
        Args:
            workspace (str): The workspace directory path
            model (str): The LLM model to use
            task (str): The task description
            task_guidelines (Optional[str]): Guidelines for the task
            prompts_dir (Optional[str]): Directory containing prompt templates
            system_prompt (Optional[str]): Custom system prompt to override template loading
            instance_name (Optional[str]): The instance name for the LLMInterface (default: 'user_agent')
        """
        self.workspace = workspace
        self.model = model
        self.task = task
        self.task_guidelines = task_guidelines
        
        # Load system prompt from template only if not provided as parameter
        if system_prompt is None:
            template_path = Path(prompts_dir) / "user_agent_prompt.j2"
            self.system_prompt = self._load_system_prompt(template_path, task, task_guidelines)
        else:
            self.system_prompt = system_prompt
        
        # Initialize LLM interface
        self.user_agent = LLMInterface(
            llm_api_key=None,
            session_dir=workspace,
            instance_name=instance_name or "user_agent",
        )
        
        # Initialize message history with system prompt
        self.messages = [
            {
                "role": "system",
                "created_at": datetime.now().isoformat(),
                "content": self.system_prompt,
            }
        ]
        self.response_format = None
        
    def _load_system_prompt(self, template_path: Path, task: str, task_guidelines: Optional[str] = None) -> str:
        """
        Load and render the system prompt template.
        
        Args:
            template_path (Path): Path to the Jinja template file
            task (str): The task description to inject into the template
            
        Returns:
            str: The rendered system prompt
            
        Raises:
            FileNotFoundError: If the template file cannot be found
            jinja2.exceptions.TemplateError: If there's an error rendering the template
        """
        try:
            template_dir = template_path.parent
            template_file = template_path.name
            
            logging.info(f"Loading system prompt template from {template_path}")
            
            # Set up Jinja environment
            env = jinja2.Environment(
                loader=jinja2.FileSystemLoader(template_dir),
                trim_blocks=True,
                lstrip_blocks=True
            )
            
            # Load and render template
            template = env.get_template(template_file)
            system_prompt = template.render(task=task, task_guidelines=task_guidelines)
            
            return system_prompt
            
        except FileNotFoundError:
            logging.error(f"System prompt template not found at {template_path}")
            raise
        except jinja2.exceptions.TemplateError as e:
            logging.error(f"Error rendering system prompt template: {e}")
            raise

    def send_message(self, 
                     messages: List[Dict],
                     user_prompt: Optional[str] = None,
                     system_prompt: Optional[str] = None,
                     response_format: Optional[Dict] = None) -> Any:
        """
        Send a message to the LLM and get a response.
        
        Args:
            messages (List[Dict]): Message history
            user_prompt (Optional[str]): User prompt to include
            system_prompt (Optional[str]): System prompt to include
            response_format (Optional[Dict]): Format specification for LLM response
            
        Returns:
            Any: The LLM completion object
        """
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(
            self.user_agent.llm_interaction_wrapper(
                messages=self.messages,
                user_prompt=user_prompt,
                system_prompt=system_prompt,
                model=self.model,
                response_format=response_format,
            )
        )

    def generate_answer(self, swe_agent_message: str) -> str:
        """
        Generate a response to a message from the software engineering agent.
        
        Args:
            swe_agent_message (str): The message from the SWE agent
            
        Returns:
            str: The generated response
        """
        # TODO: to handle  yes/no questions on errors, we need improve responses format to JSON to be able to handle a retry mechanism

        # Construct user message
        user_prompt = f"Answer to the swe agent: {swe_agent_message}"
        message = {
            "role": "user",
            "created_at": datetime.now().isoformat(),
            "content": user_prompt,
        }
        self.messages.append(message)

        # Get completion from LLM
        user_completion = self.send_message(
            messages=self.messages,
            user_prompt=None,
            system_prompt=None,
            response_format=self.response_format,
        )

        # Process and record response 
        user_agent_response = user_completion.choices[0].message.content.strip()
        logging.debug(f"Response from user agent: {user_agent_response}")
        
        # Add assistant message to history
        message = {
            "role": "assistant",
            "created_at": datetime.now().isoformat(),
            "content": user_agent_response,
        }
        self.messages.append(message)

        return message


class UserTaskControl(TaskExecutionControl):
    """
    Simple task control implementation for user agent orchestration.
    
    This class provides a basic implementation of TaskExecutionControl interface
    with empty methods since the user agent doesn't need complex task control.
    """

    def pause(self) -> None:
        """Pause task execution (no-op implementation)."""
        pass

    def resume(self) -> None:
        """Resume task execution (no-op implementation)."""
        pass

    def stop(self) -> None:
        """Stop task execution (no-op implementation)."""
        pass

    def check_status(self) -> None:
        """Check current execution status (no-op implementation)."""
        pass

    def reset(self) -> None:
        """Reset execution state (no-op implementation)."""
        pass


class UserTaskReporter(TaskExecutionReporter):
    """
    Task reporter that logs progress and handles user interaction.
    
    This class implements the TaskExecutionReporter interface to provide reporting
    capabilities for the user agent system. It logs information to both console
    and file, and handles interaction with the user agent.
    
    Attributes:
        logger (Logger): Logger instance for reporting
        swe_agent (TaskExecutionAgent): Software engineering agent instance
        user_agent (UserAgent): User agent instance
    """

    def __init__(
        self,
        swe_agent: TaskExecutionAgent,
        user_agent: UserAgent,
        log_dir: str,
        log_level: int = logging.INFO,
    ):
        """
        Initialize a UserTaskReporter.
        
        Args:
            swe_agent (TaskExecutionAgent): Software engineering agent instance
            user_agent (UserAgent): User agent instance
            log_dir (str): Directory to store log files
            log_level (int): Logging level (default: logging.INFO)
        """
        self.logger = setup_logger("UserTaskAgent", log_dir, log_level)
        self.swe_agent = swe_agent
        self.user_agent = user_agent

    def progress_callback(
        self, status: str, total_tasks: str, latest_result: str, request_context: str
    ) -> None:
        """
        Record task progress.
        
        Args:
            status (str): Current status of the task
            total_tasks (str): Description of remaining tasks
            latest_result (str): Latest result from task execution
            request_context (str): Additional context for the request
        """
        self.logger.info(f"Progress: {status} - latest result: {latest_result}")
        if total_tasks:
            self.logger.info(f"Tasks remaining: {total_tasks}")

    def terminal_output_callback(self, output: str) -> None:
        """
        Handle terminal output (no-op implementation).
        
        Args:
            output (str): Terminal output
        """
        # Terminal output is ignored in this implementation
        pass

    def browser_output_callback(self, image: str) -> None:
        """
        Handle browser output (no-op implementation).
        
        Args:
            image (str): Base64-encoded image data
        """
        # Browser output is ignored in this implementation
        pass

    def cost_update_callback(self, agent_costs: Dict[str, float], total_cost: float) -> None:
        """
        Handle cost updates (no-op implementation).
        
        Args:
            agent_costs (Dict[str, float]): Cost breakdown by agent
            total_cost (float): Total cost
        """
        # Cost updates are ignored in this implementation
        pass

    def send_code_panel_update(
        self, stream_id: str, content: str, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Send code panel update (no-op implementation).
        
        Args:
            stream_id (str): Stream identifier
            content (str): Content to display
            metadata (Optional[Dict[str, Any]]): Additional metadata
        """
        # Code panel updates are ignored in this implementation
        pass

    def function_call_callback(self, call_description: Any) -> None:
        """
        Handle function call notifications (no-op implementation).
        
        Args:
            call_description (Any): Description of the function call
        """
        # Function call notifications are ignored in this implementation
        pass

    def codeview_callback(self, code: str) -> None:
        """
        Handle code view updates (no-op implementation).
        
        Args:
            code (str): Code to display
        """
        # Code view updates are ignored in this implementation
        pass

    def codeview_update_callback(self, code: str) -> None:
        """
        Handle code view updates (no-op implementation).
        
        Args:
            code (str): Code to display
        """
        # Code view updates are ignored in this implementation
        pass

    def app_state_callback(self, url, state=None):
        pass

    def send_code_update(self, stream_id: str, content: str, 
                        metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Send code update to parent class.
        
        Args:
            stream_id (str): Stream identifier
            content (str): Content to display
            metadata (Optional[Dict[str, Any]]): Additional metadata
        """
        return super().send_code_update(stream_id, content, metadata)

    def task_start_callback(self, task_description: str) -> None:
        """
        Handle task start notification.
        
        Args:
            task_description (str): Description of the starting task
        """
        self.logger.info(f"\n=== Starting task: {task_description} ===\n")

    def send_agent_message(self, message: str) -> None:
        """
        Send message to agent (no-op implementation).
        
        Args:
            message (str): Message to send
        """
        # Agent messages are ignored in this implementation
        pass

    def user_query_callback(self, questions: Dict) -> str:
        """
        Handle query to the user.
        
        Args:
            questions (Dict): Questions for the user agent
            
        Returns:
            str: Response from the user agent
        """
        questions_str = json.dumps(questions)
        return self.user_agent.generate_answer(questions_str)
    
    def get_user_input(self, prompt: str) -> str:
        """
        Get input from the user agent in non-streaming mode.
        
        Args:
            prompt (str): Prompt to present to the user agent
            
        Returns:
            str: Response from the user agent
        """
        return answer_task_execution_agent(self.user_agent, self.swe_agent, prompt, self.logger)

    def universal_init_status_callback(self, container_name, status, message=None, error=None):
        """
        Report the status of universal initialization for a container/component.
        
        Args:
            container_name (str): Name of the container or component being initialized
            status (str): Status of initialization ('starting', 'success', 'failed', 'completed')
            message (str, optional): Status message or description
            error (str, optional): Error message if initialization failed
        """
        pass


class UserAgentOrchestrator:
    """
    Orchestrates the user agent, SWE agent, and task execution process.
    
    This class handles the coordination between different components of the
    user agent system and provides the main entry point for tasks.
    
    Attributes:
        config_path (str): Path to micro agents configuration file
        model (str): LLM model to use
        prompts_dir (str): Directory containing prompt templates
        workspace (str): Base workspace directory
        budget (Optional[float]): Maximum cost budget for execution
        log_level (int): Logging level
        enable_streaming (bool): Whether to enable streaming responses
        metrics_path (Optional[str]): Path to save metrics

        metrics_path (Optional[str]): Path to save metrics

    """
    
    def __init__(
        self,
        config_path: str,
        model: str,
        prompts_dir: str,
        workspace: str,
        budget: Optional[float] = None,
        logging_level: int = logging.INFO,
        enable_streaming: bool = False,
        metrics_path: Optional[str] = None
    ):
        """
        Initialize a UserAgentOrchestrator.
        
        Args:
            config_path (str): Path to micro agents configuration file
            model (str): LLM model to use
            prompts_dir (str): Directory containing prompt templates
            workspace (str): Base workspace directory
            budget (Optional[float]): Maximum cost budget for execution
            logging_level (int): Logging level (default: logging.INFO)
            enable_streaming (bool): Whether to enable streaming responses (default: False)
            metrics_path (Optional[str]): Path to save metrics (default: None)
            metrics_path (Optional[str]): Path to save metrics (default: None)
        """
        logging.info("Starting Templates Builder Agent...")
        self.config_path = config_path
        self.model = model
        self.prompts_dir = prompts_dir
        self.workspace = workspace
        self.budget = budget
        self.log_level = logging_level
        self.enable_streaming = enable_streaming
        self.metrics_path = metrics_path

    def _load_config(self, task_definition: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        platform_loader = PlatformConfigLoader(self.config_path)
        
        # Extract platform and framework from task definition if available
        if task_definition:
            selected_platform = task_definition.get("platform") or ApplicationType.GENERIC.value
            selected_framework = task_definition.get("framework") or FrameworkType.DEFAULT.value
        else:
            selected_platform = ApplicationType.GENERIC.value
            selected_framework = FrameworkType.DEFAULT.value
            
        micro_agent_config = platform_loader.get_platform_config(
            ApplicationType.from_string(selected_platform),
            FrameworkType.from_string(selected_framework)
            )
        return micro_agent_config

    async def run(self, task_definition: Dict[str, Any]) -> None:
        """
        Run the specified task.
        
        Args:
            task_definition (Dict[str, Any]): task object containing task definition
                
        The task_definition should contain the following keys:
            - name: task name
            - component_name: Component name
            - description: Task description (or overview: Task description as alternative)
            - interfaces: Interface definitions
            - algorithms: Algorithm definitions
            - pseudocode: Pseudocode implementations
            - platform: Platform type (optional, defaults to GENERIC)
            - framework: Framework type (optional, defaults to DEFAULT)
        """
        # Extract task name and prepare workspace
        task_name = task_definition["name"]
        logging.info(f"Running task {task_name}...")

        # Load configuration with task definition to extract platform and framework
        config = self._load_config(task_definition)
        
        # Extract platform and framework for TaskExecutionAgent
        selected_platform = task_definition.get("platform") or ApplicationType.GENERIC.value
        selected_framework = task_definition.get("framework") or FrameworkType.DEFAULT.value

        # Initialize chat interface if streaming is enabled
        chat_interface = None
        chat_worker = None

        if self.enable_streaming:
            logging.info("Streaming mode enabled")
            chat_interface = ChatInterface()                        
            chat_worker = ChatEnabledWorker([], name="TaskExecutionAgent")
            chat_interface.add_chat_user(chat_worker)            

        # Initialize LLM interface for SWE agent
        llm = LLMInterface(
            llm_api_key=None,
            session_dir=self.workspace,
            instance_name="swe_agent",
            chat_worker=chat_worker,
        )

        # Create user agent instance
        user_agent = UserAgent(
            workspace=self.workspace, 
            model=self.model, 
            task=task_definition.get("overview") or task_definition["description"],
            task_guidelines=task_definition.get("additional_system_instructions"),
            prompts_dir=self.prompts_dir
        )

        # Create and initialize TaskExecutionAgent with extracted platform and framework
        agent = TaskExecutionAgent(
            llm=llm,
            execution_base_path=self.workspace,
            micro_agents_config=config,
            model_name=self.model,
            prompts_base_path=self.prompts_dir,
            platform=selected_platform,
            framework=selected_framework
        )

        # Create reporter and set it on the agent
        reporter = UserTaskReporter(
            swe_agent=agent,
            user_agent=user_agent,
            log_dir=self.workspace,
            log_level=self.log_level,
        )
        agent.set_status_reporter(reporter)
        
        if self.enable_streaming:
            # Set up chat interface handlers
            self._setup_chat_interface_handlers(user_agent, agent, chat_interface)

        # Create task control
        control = UserTaskControl()
        
        # Track execution time and costs
        start_time = time.time()

        task = task_definition

        logging.info(f"Prepared task definition: {task}")

        # Process the work item
        await agent.process_work_item(
            agent_name="CodeGeneration",
            work_item=task,
            use_retriever=True,
            control=control,
            status_reporter=reporter,
            chat_interface=chat_interface,
            budget=self.budget,
        )

        # Report metrics
        elapsed_time = time.time() - start_time
        total_cost = agent.cost_tracer.get_total_cost()
        all_costs = agent.cost_tracer.get_all_costs()
        metrics = {
                "app_name": task_name,
                "elapsed_time": elapsed_time,
                "total_cost": total_cost,
                "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "project_link": self.workspace,
                "cga_version": VERSION,
                "model": self.model,
                "streaming": self.enable_streaming
            }
        logging.info(f"Metrics: {metrics}")

        # Only save metrics if enabled
        if self.metrics_path:

           # Read existing metrics if file exists
            existing_metrics = []
            if os.path.exists(self.metrics_path):
                try:
                    with open(self.metrics_path, "r") as f:
                        existing_metrics = json.load(f)
                except json.JSONDecodeError:
                    existing_metrics = []
           
            if not isinstance(existing_metrics, list):
                existing_metrics = [existing_metrics]
            existing_metrics.append(metrics)
            
            # Ensure metrics directory exists
            os.makedirs(os.path.dirname(self.metrics_path), exist_ok=True)
            
            with open(self.metrics_path, "w") as f:
                json.dump(existing_metrics, f, indent=4)

        
        # Log execution metrics
        reporter.logger.info(
            f"UserTaskAgent elapsed time: {elapsed_time:.2f} seconds"
        )
        reporter.logger.info(f"UserTaskAgent all costs: {all_costs}")
        reporter.logger.info(f"UserTaskAgent total cost: ${total_cost:.4f}")
        stop_cga(agent)
        stop_cga(agent)

    def _setup_chat_interface_handlers(self, user_agent, swe_agent, chat_interface: ChatInterface) -> None:
        """
        Set up event handlers for the chat interface to process streaming events.
        
        Args:
            chat_interface (ChatInterface): The chat interface instance to set up
        """
        # Handler for new messages
        def on_message_added(message):
            logging.info(f"Message added: {message.id} ({message.msg_type.value}) - {message.status.value}")
        
        # Handler for streaming chunks
        def on_stream_chunk(message, chunk):
            # Only log first 100 chars to avoid excessive logging
            chunk_preview = chunk[:100] + ("..." if len(chunk) > 100 else "")
            logging.debug(f"Stream chunk received for {message.id}: {chunk_preview}")
        
        # Handler for message resolution
        def on_message_resolved(message):
            logging.info(f"Message resolved: {message.id}")
        
        # Handler for messages needing response
        def on_needs_response(message):
            logging.info(f"Message needs response: {message.id} - {message.content}")
            try:
                # Get response from the agent with a timeout to avoid hanging
                response = answer_task_execution_agent(
                    user_agent,
                    swe_agent,
                    message.content, 
                    swe_agent.reporter.logger
                )                
                # Send the response back via the chat interface
                chat_interface.process_input(response, parent_id=message.id)
                
            except Exception as e:
                logging.error(f"Error processing response: {str(e)}")
                import traceback
                logging.error(traceback.format_exc())
                # Make sure to resolve the message even on error to prevent hanging
                chat_interface.resolve_message(message.id, f"Error: {str(e)}")

        # Register all event handlers
        chat_interface.add_callback("message_added", on_message_added)
        chat_interface.add_callback("stream_chunk", on_stream_chunk)
        chat_interface.add_callback("message_resolved", on_message_resolved)
        chat_interface.add_callback("needs_response", on_needs_response)
