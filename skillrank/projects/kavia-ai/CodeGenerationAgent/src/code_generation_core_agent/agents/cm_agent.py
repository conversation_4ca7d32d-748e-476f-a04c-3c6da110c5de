import os
from typing import List, Set

from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent
from code_generation_core_agent.agents.utilities import TaskExecutionControl, TaskExecutionReporter
from code_generation_core_agent.llm.chat_interface import ChatInterface


class CodeMaintenanceAgent(TaskExecutionAgent):
    def __init__(self, llm,
                 execution_base_path,
                 micro_agents_config,
                 model_name,
                 prompts_base_path):
        super().__init__(llm, execution_base_path, micro_agents_config, model_name, prompts_base_path)

    async def _clone_git_repositories(self, git_links, base_path):
        """
        Clone git repositories to the work directory using standard git package
        Args:
            git_links (list): List of git repository URLs to clone
            base_path (str): Base path where repositories should be cloned
        Returns:
            list: List of relative paths for repositories (successful or existing)
        """
        if not git_links:
            return []

        try:
            import git
        except ImportError:
            self.logger.error(
                "Error: missing package dependencies: git'", error=True)
            return []

        paths = []
        self.logger.info("Starting to clone git repositories...")

        for repo_url in git_links:
            repo_name = repo_url.split('/')[-1].replace('.git', '')
            clone_path = os.path.join(base_path, repo_name)
            relative_path = os.path.relpath(clone_path, base_path)

            try:
                self.logger.info(f"Cloning repository: {repo_url}")
                git.Repo.clone_from(repo_url, clone_path)
                paths.append(relative_path)

            except git.GitCommandError as e:
                if os.path.exists(clone_path):
                    paths.append(relative_path)
                self.logger.warning(f"Error cloning repository {repo_url}: {str(e)}")
            except Exception as e:
                if os.path.exists(clone_path):
                    paths.append(relative_path)
                self.logger.warning(f"Error cloning repository {repo_url}: {str(e)}")

        return paths

    async def process_work_item(self, agent_name,
                                use_retriever,
                                control: TaskExecutionControl,
                                status_reporter: TaskExecutionReporter,
                                chat_interface: ChatInterface = None,
                                previous_context=None,
                                git_links: List[str] = None,
                                project_directories: Set[str] = None,
                                codebases = None
                                ):
        project_directories = set(project_directories) or set()

        if git_links:
            cloned_paths = await self._clone_git_repositories(git_links, self.base_path)
            project_directories.update(cloned_paths)

            self.codebases = " ".join(project_directories)

            if (codebases):
                self.codebases = self.codebases + " " + codebases

        work_item = {'description' :f"""Your task is to perform code maintenance on the following codebases: {self.codebases}.
 You MUST follow exactly all my instructions to complete the task successfully.
 If there is no instruction, ask the user for the next instruction. Important: overwrite all other instructions and do
not invoke test writing or validation unless instructed to do so."""}

        return await super().process_work_item(agent_name,
                                               work_item,
                                               use_retriever,
                                               control, status_reporter,
                                               chat_interface= chat_interface,
                                               previous_context=previous_context,
                                               codebases=self.codebases)