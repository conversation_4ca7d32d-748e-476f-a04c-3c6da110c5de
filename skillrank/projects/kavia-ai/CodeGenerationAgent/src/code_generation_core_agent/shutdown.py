import time
import signal
import os

def graceful_shutdown():
    """
    Try to shutdown the application gracefully
    :return:
    """
    import threading

    graceful_thread = threading.Thread(target=(lambda: time.sleep(1) or os.kill(os.getpid(), signal.SIGINT)),
                                       daemon=True)

    force_kill_thread = threading.Thread(target=(lambda: time.sleep(60) or os.kill(os.getpid(), signal.SIGKILL)),
                                         daemon=True)

    graceful_thread.start()
    force_kill_thread.start()
    # Trap
    while True:
        time.sleep(1)
