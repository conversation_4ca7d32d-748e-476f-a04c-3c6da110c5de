import threading
from typing import Dict, List, Optional, Callable

from code_generation_core_agent.chat.chat_enabled_worker import ChatEnabledWorker


class ChatWorkerRegistry:
    """
    Singleton registry that maintains references to all chat-enabled workers.
    Allows lookup and management of workers by name or ID.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ChatWorkerRegistry, cls).__new__(cls)
            cls._instance._workers_by_name = {}
            cls._instance._workers_by_id = {}
            cls._instance._broadcast_lock = threading.Lock()
            cls._instance._current_broadcast = None
        return cls._instance

    def register_worker(self, worker: ChatEnabledWorker) -> None:
        """
        Register a chat worker in the registry.

        Args:
            worker: The ChatEnabledWorker instance to register
        """
        self._workers_by_name[worker.get_name()] = worker
        self._workers_by_id[id(worker)] = worker

    def unregister_worker(self, worker: ChatEnabledWorker) -> None:
        """
        Remove a chat worker from the registry.

        Args:
            worker: The ChatEnabledWorker instance to unregister
        """
        worker_name = worker.get_name()
        worker_id = id(worker)

        if worker_name in self._workers_by_name:
            del self._workers_by_name[worker_name]

        if worker_id in self._workers_by_id:
            del self._workers_by_id[worker_id]

    def get_worker_by_name(self, name: str) -> Optional[ChatEnabledWorker]:
        """
        Look up a worker by its name.

        Args:
            name: The name of the worker to find

        Returns:
            The ChatEnabledWorker if found, None otherwise
        """
        return self._workers_by_name.get(name)

    def get_worker_by_id(self, worker_id: int) -> Optional[ChatEnabledWorker]:
        """
        Look up a worker by its object ID.

        Args:
            worker_id: The ID of the worker to find

        Returns:
            The ChatEnabledWorker if found, None otherwise
        """
        return self._workers_by_id.get(worker_id)

    def get_all_workers(self) -> List[ChatEnabledWorker]:
        """
        Get all registered workers.

        Returns:
            A list of all registered ChatEnabledWorker instances
        """
        return list(self._workers_by_name.values())

    def get_worker_count(self) -> int:
        """
        Get the count of registered workers.

        Returns:
            The number of registered workers
        """
        return len(self._workers_by_name)



# Example usage
def get_chat_worker_registry() -> ChatWorkerRegistry:
    """
    Get the singleton instance of the ChatWorkerRegistry.

    Returns:
        The ChatWorkerRegistry singleton instance
    """
    return ChatWorkerRegistry()