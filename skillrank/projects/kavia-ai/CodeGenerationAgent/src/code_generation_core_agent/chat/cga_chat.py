import asyncio
import json
import queue
import re
import threading
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from typing import Dict, <PERSON><PERSON>, Any, List

from litellm import completion

from code_generation_core_agent.chat.cga_user_service import CGAUserService, UserMessage, CGAChatInputType
from code_generation_core_agent.llm.chat_interface import ChatInterface, MessageStatus, ChatMessage, MessageType
from code_generation_core_agent.llm.chat_member import Chat<PERSON>essageCallback


def route_user_message(message: str) -> str:
    """
    Routes a user message to either TaskExecutionAgent or CodeWritingAgent based on message content.

    Args:
        message (str): The user's message

    Returns:
        str: Either "TaskExecutionAgent" or "CodeWritingAgent"
    """
    # Define the system and user prompts
    return "TaskExecutionAgent"

    prompt = [
        {
            "role": "system",
            "content": """You are a message routing system for a code assistant platform.

Your job is to determine whether a user message should be routed to:
1. TaskExecutionAgent - for questions about code development, validation, verification, tests, status, etc.
2. CodeWritingAgent - for requests that explicitly ask to make changes to code (add files, edit files, implement features, etc.)

You must respond with ONLY ONE of these two exact strings: "TaskExecutionAgent" or "CodeWritingAgent".
Do not include any explanations, punctuation, or additional text."""
        },
        {
            "role": "user",
            "content": message
        }
    ]

    try:
        response = completion(
            model="gpt-4o-mini",
            messages=prompt,
            max_tokens=10,
            temperature=0.1
        )

        result = response.choices[0].message.content.strip()

        if result in ["TaskExecutionAgent", "CodeWritingAgent"]:
            return result
        else:
            if any(kw in message.lower() for kw in ["change", "edit", "add", "create", "modify", "implement", "write"]):
                return "CodeWritingAgent"
            else:
                return "TaskExecutionAgent"

    except Exception as e:
        # In case of an error, provide a simple fallback
        print(f"Error in route_user_message: {e}")
        return "TaskExecutionAgent"  # Default to orchestrator in case of errors


@dataclass
class SystemChatRequest:
    """Represents a request to the simulated chat system"""
    message_id: str
    content: str
    response_queue: asyncio.Queue
    parent_id: str
    attachments: List[Dict] = None 
    extra: Dict = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        return {
            'message_id': self.message_id,
            'content': self.content,
            'parent_id': self.parent_id,
            'attachments': self.attachments or [],
            'extra': self.extra or {}
        }


class CGAChatSystem:
    def __init__(self, chat_interface: ChatInterface, disable_task_creation: bool = False):
        self.chat_interface = chat_interface
        self.request_queue = queue.Queue()
        self.running = False
        self.worker_thread = None
        self.chat_interface.add_callback("message_added", self._handle_new_message)
        self.start()
        self._response_events: Dict[str, Tuple[threading.Event, Dict]] = {}
        self._response_events_lock = threading.Lock()
        self.cga_service = CGAUserService()
        self.cga_service.reset()
        self.disable_task_creation = disable_task_creation
        self.agent_message_ids = {"CodeWritingAgent": set(),
                                  "TaskExecutionAgent": set()}
        # These are direct questions to the user and the responses should not be parsed
        # and directed to particular agents!
        self.direct_questions_to_user = set()

    def _handle_new_message(self, message: ChatMessage):
        """Handle incoming new messages"""
        if message.msg_type.value == MessageType.USER.value:
            response_queue = asyncio.Queue()
            request = SystemChatRequest(
                message_id=message.id,
                content=message.content,
                response_queue=response_queue,
                parent_id=message.parent_id,
                attachments=message.attachments,
                extra=message.extra
            )
            self.request_queue.put(request)

    def send_stop_message(self):
        """Send a stop message to the chat system so that it will stop waiting for user input"""
        response_queue = asyncio.Queue()
        request = SystemChatRequest(
            message_id="0",
            content="Stop",
            response_queue=response_queue,
            parent_id="0",
            attachments=None
        )
        self.request_queue.put(request)

    def _handle_task(self, request: SystemChatRequest):
        # TODO: Implement task handling
        pass

    def _process_queue(self):
        """Main processing loop for the chat loop"""
        while self.running:
            try:
                request = self.request_queue.get()

                if request is None:
                    break
                if request.content.startswith("/task"):
                    self._handle_task(request)
                    continue
                if request.content.startswith("/code"):
                    self._handle_code_request(request)
                    continue

                if request.content.startswith("/q"):
                    self._handle_questions(request)
                    continue

                if self.disable_task_creation:
                    print("DEBUG: Disabling task creation")
                    print("DEBUG: Full request:", request)
                    self._handle_generic_message(request)
                else:
                    asyncio.run(self._generate_response(request))

            except Exception as e:
                import traceback
                traceback.print_exc()
                print(f"Error processing request: {e}")

    def start(self):
        """Start the simulated chat system thread"""
        if not self.running:
            self.running = True
            self.worker_thread = threading.Thread(target=self._process_queue)
            self.worker_thread.daemon = True
            self.worker_thread.start()

    def _send_message_to_user(self, message: str,
                              parent_id: str = None,
                              needs_response: bool = False,
                              blocking: bool = False,
                              error: bool = False,
                              message_type : MessageType = None, 
                              extra = None) -> Tuple[str, str]:
        """Send an LLM message and optionally wait for response

        :param message: The message to send
        :param parent_id: The parent message ID
        :param needs_response: Whether the message needs a response
        :param block: Whether to block until the message is sent and responded to
        :return: The message ID
        """
        status = MessageStatus.PENDING if needs_response else MessageStatus.COMPLETED
        if error:
            m_type = MessageType.ERROR
            status = MessageStatus.COMPLETED
        elif message_type is not None:
            m_type = message_type
        else:
            m_type = MessageType.LLM

        if m_type is None:
            m_type = MessageType


        result = self.chat_interface.llm_interface.send_message(message,
                                                                status=status,
                                                                message_type=m_type,
                                                                parent_id=parent_id, extra=extra)
        self.direct_questions_to_user.add(result.id)

        if needs_response:
            self.chat_interface.llm_interface.need_response(result.id)
        elif parent_id:
            self.chat_interface.resolve_message(parent_id)

        if blocking:
            event = threading.Event()
            with self._response_events_lock:
                self._response_events[result.id] = (event, {})
            event.wait()
            with self._response_events_lock:
                _, response = self._response_events.pop(result.id)

            return result.id, {"content":response['content'], 
                               "attachments": response['attachments'],
                               "extra": response.get('extra', {})}

        self.chat_interface.resolve_message(result.id, "Resolved user message")

        return result.id, ""

    def interrupt_all_blocking_requests(self, interrupt_reason="interrupted"):
        """Interrupt all blocking requests.
        Place into response events a message with
        the content "interrupted" and set the event.
        """
        with self._response_events_lock:
            for message_id, (event, _) in self._response_events.items():
                event.set()
                self._response_events[message_id] = (event, {"content": interrupt_reason, "attachments": []})


    def _message_history(self, message_id: str):
        """Collect all parent messages and return them in reverse order"""
        msg = self.chat_interface.get_message(message_id)
        parents = []

        while message_id:
            parent = self.chat_interface.get_message(message_id)
            if parent:
                parents.append(f"{parent.msg_type.value}: {parent.content}")
                message_id = parent.parent_id
            else:
                break

        # Reverse the order of messages and join them with the delimiter
        return "\n-----\n".join(reversed(parents))

    def parse_json_with_cleanup(self, response_string):
        """
        Attempts to parse JSON from a string, with fallback cleanup for markdown-formatted JSON.

        Args:
            response_string (str): The string containing JSON, potentially with markdown formatting

        Returns:
            dict: Parsed JSON object

        Raises:
            json.JSONDecodeError: If JSON parsing fails even after cleanup
        """
        try:
            # First attempt: try parsing the string directly
            return json.loads(response_string)
        except json.JSONDecodeError:
            # Remove ```json and ``` markers
            cleaned = re.sub(r'```json\s*|\s*```', '', response_string)

            cleaned = cleaned.strip()

            return json.loads(cleaned)

    async def _generate_response(self, request: SystemChatRequest):
        """Generate response and signal completion if needed"""
        print("Generating response")
        try:
            message_history = self._message_history(request.message_id)

            cga_response = self.cga_service.generate_response_summary(message_history)

            try:
                cga_json = self.parse_json_with_cleanup(cga_response)
                concluded = False
                if cga_json["chat_concluded"] == True:
                    concluded = True

                user_message = cga_json['response']

                if cga_json['task']:
                    cga_service = CGAUserService()
                    task = UserMessage(message_id=request.message_id, content=cga_json['task'],
                                       input_type=CGAChatInputType.TASK)

                    cga_service.chat_user_input_available(task)

                if concluded:
                    message_id = request.parent_id

                    with self._response_events_lock:
                        parent_id = request.parent_id

                        while parent_id or request.parent_id != parent_id:
                            if parent_id in self._response_events:
                                event, _ = self._response_events[parent_id]
                                self._response_events[parent_id] = (event, request.to_dict())
                                event.set()
                                break
                            parent = self.chat_interface.get_message(parent_id)
                            if parent:
                                parent_id = parent.parent_id
                            else:
                                break

                self.chat_interface.llm_interface.send_message(user_message,
                                                               parent_id=request.message_id,
                                                               status=MessageStatus.COMPLETED if concluded else MessageStatus.NEEDS_RESPONSE,
                                                               message_type=MessageType.LLM)
                self.chat_interface.resolve_message(request.message_id, "Resolved user message")

            except Exception as e:
                print(f"Error converting response to JSON: {e}")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"Error generating response: {e}")

    def _handle_questions(self, request):
        question_message = request.content.split("/q")[1].strip()
        question_requests = UserMessage(message_id=request.message_id, content=question_message,
                                        input_type=CGAChatInputType.QUESTION)
        self.cga_service.chat_user_input_available(question_requests)
        pass

    def _handle_generic_message(self, request):
        code_requests = UserMessage(message_id=request.message_id, content=request.content,
                                    input_type=CGAChatInputType.TASK, attachments=request.attachments, extra=request.extra) 

        self.cga_service.chat_user_input_available(code_requests)

        with self._response_events_lock:
            parent_id = request.parent_id
            print("Parent ID:", parent_id, "Request ID:", request.message_id)
            while parent_id or request.parent_id != parent_id:
                if parent_id in self._response_events:
                    event, _ = self._response_events[parent_id]
                    self._response_events[parent_id] = (event, request.to_dict())
                    event.set()
                    break
                parent = self.chat_interface.get_message(parent_id)
                if parent:
                    parent_id = parent.parent_id
                else:
                    break
        self.chat_interface.resolve_message(request.message_id, "Resolved user message")

    def _handle_code_request(self, request):
        code_message = request.content.split("/code")[1].strip()
        code_requests = UserMessage(message_id=request.message_id, content=code_message,
                                    input_type=CGAChatInputType.CODE)
        print("Code request:", code_message)
        print("Calling chat_user_input_available")
        self.cga_service.chat_user_input_available(code_requests)
        pass

    def _handle_direct_agent_chat(self, request, agent_name):

        self.agent_message_ids[agent_name].update(request.message_id)

        class ChatMessageAgentCallback(ChatMessageCallback):
            def __init__(self, chat_interface, agent_message_ids):
                super().__init__()
                self.chat_interface = chat_interface
                self.agent_message_ids = agent_message_ids

            def on_member_message(self, user_id: str, parent_id: str, message: str, chat_concluded: bool):
                """
                on_member_message is called when a message is received from the chat.
                args:
                    user_id: str
                        The user id of the sender
                    parent_id: str
                        The id of the parent message
                    message: str
                        The message
                """
                out_message = self.chat_interface.llm_interface.send_message(message,
                                                                             parent_id=request.message_id,
                                                                             status = MessageStatus.COMPLETED if chat_concluded else MessageStatus.NEEDS_RESPONSE,
                                                                             message_type=MessageType.LLM,
                                                                             attachments=request.attachments,
                                                                             extra=request.extra
                                                                             )

                self.agent_message_ids[agent_name].add(out_message.id)
                self.chat_interface.resolve_message(request.message_id, "Resolved user message")

        agent_message = request.content

        parent_id = request.parent_id
        content = agent_message

        chat_interface = self.chat_interface.find_user(agent_name)

        if chat_interface:
            # Create a ChatMessageCallback implementation in place
            chat_interface.set_message_callback(ChatMessageAgentCallback(self.chat_interface, self.agent_message_ids))
            chat_interface.send_message(parent_id, request.message_id, content, attachments=request.attachments, extra = request.extra)
        else:
            print("Chat interface not found:",agent_name)
        pass
