import asyncio
import threading
from enum import Enum
from typing import List, Any, Dict
from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator, SummarizerType, \
    parse_json_with_cleanup
import json

from code_generation_core_agent.llm.chat_member import <PERSON><PERSON><PERSON>ember, ChatMessageCallback


class ChatContextProvider:
    """
    ChatContextProvider is a base class for a provider that can provide chat context.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_chat_context(self) -> Any:
        """
        get_chat_context is called to get the chat LLM context.
        """
        return ""
    
    def set_action(self, action: str):
        """
        set_action is called to set the action for the chat.
        """
        pass


class StatusListener:
    """
    StatusListener is a base class for a listener that can listen to the status of a worker.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def on_status_change(self, status: str):
        """
        on_status_change is called when the status of the worker changes.
        """
        pass

    def on_message(self, message: str):
        """
        on_message is called when a message is received from the worker.
        """
        pass


class UserInputReader:
    """
    UserInputReader is a class that reads user input from a particular source.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def read_input(self):
        """
        read_input is called to read the user input.
        """
        pass


class CLIUserInputReader(UserInputReader):
    """
    CLIUserInputReader is a class that reads user input from the console.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def read_input(self):
        """
        read_input is called to read the user input.
        """
        return input("Enter a message: ")


class CurrentMode(Enum):
    """
    Chat or bypass mode. In bypass mode, there will be no interruption of the worker.
    """
    CHAT = "chat"
    BYPASS = "bypass"
    CHAT_IN_PROGRESS = "chat_in_progress"


class ChatEnabledWorker(ChatMember):
    """
    ChatEnabledWorker is a base class for a worker that performs background task
    but can be interrupted by user input. When interrupted, the worker switches
    to a chat mode where it can respond to the user's input.
    """

    def set_message_callback(self, callback: ChatMessageCallback):
        super().set_message_callback(callback)
        self.callback = callback

    summarizer = OutputTruncator(max_size=4096, summarizer_type=SummarizerType.LLM_CHAT_ENABLED_WORKER_SUMMARIZER)

    def send_message(self, parent_id: str, message_id: str, message: str, attachments: List[Dict] = None):

        super().send_message(parent_id, message_id, message, attachments)
        # Our parent ID is the message ID that we got
        self.parent_id = message_id

        # If there is no parent ID set, we know this is a root message, a new discussion
        # the user just started.
        if not parent_id:
            self.chat_thread_id = message_id
        # From bypass mode switch to chat mode
        self.enable_chat_mode()
        # Get the chat context and start the chat mode
        if (self.context_provider):
            chat_context = self.context_provider.get_chat_context()
            self.enable_chat_mode()
            self.send_chat_message(message, chat_context, "", attachments)

    def __init__(self, status_listeners: List[StatusListener], name="ChatEnabledWorker"):
        super().__init__(name)
        self.agent_registry = None
        self.status_listeners = status_listeners
        self.name = name
        self.current_mode = CurrentMode.BYPASS
        self.chat_lock = threading.Lock()
        self.lock = threading.Lock()
        self.chat_thread_event = threading.Event()
        self.chat_last_result = None
        self.context_provider = None
        self.callback = None
        self.parent_id = ""
        # Chat thread ID is the first parent ID of the chat
        self.chat_thread_id = ""
        self.chat_messages = []
        self.llm_chat_history = []
        # This can be none or set to a micro agent
        self.micro_agent = None
        # Track the background process_request task
        self.background_task = None
        self.background_thread = None
        # Register the worker in the registry
        from code_generation_core_agent.chat.chat_worker_registry import get_chat_worker_registry

        get_chat_worker_registry().register_worker(self)

    def set_context_provider(self, context_provider: ChatContextProvider):
        """
        set_context_provider sets the chat context provider.
        """
        self.context_provider = context_provider

    def send_user_input(self, user_input):
        self.send_message(user_input)

    def get_name(self):
        """
        get_name is called to get the name of the worker.
        """
        return self.name

    def read_user_input(self) -> str:
        """
        Read user input using the default CLI input reader.
        Override this method to implement custom input reading.
        
        Returns:
            str: The user input string or empty string if no input available
        """
        try:
            reader = CLIUserInputReader()

            return reader.read_input()
        except Exception as e:
            print(f"Error reading user input: {str(e)}")
            for listener in self.status_listeners:
                listener.on_message(f"Error reading user input: {str(e)}")
            return ""

    def enable_chat_mode(self):
        """
        enable_chat_mode is called to enable the chat mode.
        """
        acquired = self.chat_lock.acquire(blocking=False)
        if acquired:
            self.current_mode = CurrentMode.CHAT
            self.chat_lock.release()


    def send_chat_message(self, message: str,
                          llm_chat_history: List[Any],
                          work_item: str,
                          attachments: List[Dict] = None):
        """
        enter_chat_mode is called to enter the chat mode.
        This function returns an empty string in bypass mode and will loop until the user input is received.
        
        Chat mode has the following input values:
        - message: the message to send to the worker
        - LLM chat history: the chat history of the LLM.
        - Work item: the work item that the worker is currently working on.
        """
        self.chat_lock.acquire()

        chat_summary = None

        # We have two ways to get the chat llm context. The first is to pass it as an argument
        # and the second is to use the context provider. If both are not available, we will use an empty context.
        # This allows to trigger a chat session multiple ways and chat with LLMs that are currently working.
        if llm_chat_history is None and self.context_provider:
            self.llm_chat_history = self.context_provider.get_chat_context()
            print("Got chat context from provider. Size: ", len(self.llm_chat_history))

        self.current_mode = CurrentMode.CHAT_IN_PROGRESS

            # Create message data with attachments if present
        message_data = {
            'user': message
        }
        if attachments:
            message_data['attachments'] = attachments

        self.chat_messages.append(message_data)

        chat_context = {
            "llm_agent_work_history": str(llm_chat_history),
            "interactive_user_messages": [m for m in self.chat_messages if m.get('user')],
        }

        try:

            while True:

                # Use the summarizer to process the chat context and get response
                response = self.summarizer.truncate_output(
                    input=json.dumps(self.chat_messages),
                    previous_output=json.dumps(chat_context),
                    considerations=work_item,
                    allways_truncate=True,
                )

                try:
                    # Parse the summarizer response using the helper function
                    result = parse_json_with_cleanup(response)

                    # Get the response message and handle any actions
                    response_data = result.get("response", {})
                    response_message = response_data.get("message", "")
                    is_chat_concluded = response_data.get("is_chat_concluded", False)
                    action = response_data.get("user_request", None)

                    result = response_message

                    if action:
                        result = result + f"\n\n Direct request from the user: {action}"

                    self.callback.on_member_message(self.user_id,
                                                self.parent_id,
                                                result, is_chat_concluded)

                    if action:
                        result = self.process_action_response(response_message, action, is_chat_concluded)
            
                    self.chat_messages[-1]['assistant'] = response_message

                    if is_chat_concluded:
                        self.chat_messages = []
                        self.current_mode = CurrentMode.BYPASS

                    break

                except json.JSONDecodeError as e:
                    print(f"CHAT Error processing chat: {str(e)}")

            self.chat_last_result = chat_summary
            self.chat_thread_event.set()
            self.llm_chat_history = None
            return chat_summary
        finally:
            self.chat_lock.release()

    def process_action_response(self, response_message: str, action: str, is_chat_concluded: bool) -> str:
        """
        Process the action response based on whether there's a callback or micro agent.

        Args:
            response_message: The message from the response
            action: The action to be processed
            is_chat_concluded: Whether the chat is concluded

        Returns:
            str: The result of processing the action
        """
        result = response_message

        # The logic is the following. For micro-agents we interrupt the micro-agent, create
        # a new thread and start to inject the user input.
        # For the main orchestrator (not a micro-agent) we inject the action directly.

        if self.context_provider and not self.micro_agent:
            self.context_provider.set_action(action)
        elif self.micro_agent:

            from code_generation_core_agent.agents.framework.micro_agent_registry import MicroAgentType

            # Check if we have an active background task and it's still running
            if self.background_thread and self.background_thread.is_alive():
                # Background thread exists and is still running, inject the action
                result = self.micro_agent.inject_action(str(action))
            else:
                # TODO: chat interruption broken
                #if self.micro_agent.work_in_progress():
                    # If the orchestrator is using the agent we will interrupt it and force it to exit
                    # gracefully
                #    self.micro_agent.interrupt_llm("Chat interrupted", 0)

                # Define a function to run in a separate thread
                def run_micro_agent_in_thread():
                    # Create a new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        # Run the coroutine in this thread's event loop
                        result = loop.run_until_complete(
                            self.micro_agent.process_request(
                                str(action),
                                work_item=self.agent_registry.work_item,
                                capabilities=self.agent_registry.get_capabilities(
                                    MicroAgentType.CODER_MICRO_AGENTS)
                            )
                        )
                    finally:
                        loop.close()

                # Create and start the background thread
                self.background_thread = threading.Thread(target=run_micro_agent_in_thread)
                self.background_thread.daemon = True
                self.background_thread.start()

                result = "Processing request in background thread..."
        return result

    def set_micro_agent(self, agent, agent_registry):
        self.micro_agent = agent
        self.agent_registry = agent_registry




# Add a main function test the chat enabled worker.
# The input messages should contain code generation tasks, tool calls and other information.

WORK_ITEM = """
 TicTacToe game in python.
"""

# A long history of codce genertaion messages
LLM_CHAT_HISTORY = [
    {
        "role": "user",
        "content": "Let's create a TicTacToe game in Python"
    },
    {
        "role": "assistant",
        "content": "I'll help you create a TicTacToe game. First, let's design the basic structure and game board."
    },
    {
        "role": "assistant",
        "content": "Here's the initial implementation of the game board:\n```python\nclass TicTacToe:\n    def __init__(self):\n        self.board = [[' ' for _ in range(3)] for _ in range(3)]\n        self.current_player = 'X'\n```"
    },
    {
        "role": "user",
        "content": "Can you add the move validation logic?"
    },
    {
        "role": "assistant",
        "content": "I'll add move validation:\n```python\n    def is_valid_move(self, row, col):\n        return 0 <= row < 3 and 0 <= col < 3 and self.board[row][col] == ' '\n```"
    },
    {
        "role": "assistant",
        "content": "Added win checking logic:\n```python\n    def check_winner(self):\n        # Check rows, columns and diagonals\n        for i in range(3):\n            if self.board[i][0] == self.board[i][1] == self.board[i][2] != ' ':\n                return True\n```"
    },
    {
        "role": "user",
        "content": "Can we add a display method for the board?"
    },
    {
        "role": "assistant",
        "content": "Here's the display method:\n```python\n    def display_board(self):\n        for row in self.board:\n            print('|'.join(row))\n            print('-' * 5)\n```"
    }
]


class ConsoleStatusListener(StatusListener):
    """
    A simple console-based status listener that prints messages to stdout.
    """

    def on_status_change(self, status: str):
        print(f"Status: {status}")

    def on_message(self, message: str):
        print(message)


def main():
    # Create a console status listener
    listener = ConsoleStatusListener()

    # Initialize the worker with the listener
    worker = ChatEnabledWorker(status_listeners=[listener])

    # Initial message to start the conversation

    initial_message = "What features should we add to the game?"

    print("Entering chat mode")
    # Enter chat mode with proper context
    chat_summary = worker.send_chat_message(initial_message, LLM_CHAT_HISTORY, WORK_ITEM)

    if chat_summary:
        listener.on_message("\nChat session completed.")


if __name__ == "__main__":
    main()
