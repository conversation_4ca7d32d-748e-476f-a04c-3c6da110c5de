import enum
import threading
from dataclasses import dataclass, field
from typing import Optional, Callable, Dict

from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator, SummarizerType

class CGAChatInputType(enum.Enum):
    TASK = "task"
    QUESTION = "question"
    INSTRUCTION = "instruction"
    CODE = "code"


@dataclass
class UserMessage:
    message_id: str
    content: str
    input_type: CGAChatInputType
    attachments: Optional[list] = None
    extra: Dict = field(default_factory=dict)


class CGAUserService:
    _instance: Optional['CGAUserService'] = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False

        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.agent_state = None
            self._initialized = True
            # User instructions are derived from the user's input
            self.user_instructions = []
            self.usr_instr_av_callbacks = []
            self.summarizer = OutputTruncator(5000,
                                              model="gpt-4o",
                                              summarizer_type=SummarizerType.LLM_TASK_MANAGER_SUMMARIZER)

    def generate_response_summary(self, input):
        return self.summarizer.truncate_output(input, considerations=self.agent_state, allways_truncate=True)

    def get_last_user_instruction(self):
        """
        Get the last user instruction.
        """
        return self.user_instructions[-1] if self.user_instructions else None

    def set_agent_state(self, state: str):
        """
        Set the agent state.
        """
        with self._lock:
            self.agent_state = state

    def get_agent_state(self) -> str:
        """
        Get the agent state.
        """
        with self._lock:
            return self.agent_state

    def register_user_instruction_available_callback(self, callback: Callable[[UserMessage], None]):
        """
        Register a callback for when a user instruction is available.
        """
        self.usr_instr_av_callbacks.append(callback)

    def remove_user_instruction_available_callback(self, callback: Callable[[UserMessage], None]):
        """
        Remove a callback for when a user instruction is available.
        """
        self.usr_instr_av_callbacks.remove(callback)

    def chat_user_input_available(self, user_input: UserMessage):
        """
        Process the user input.
        """
        self.user_instructions.append(user_input)
        for callback in self.usr_instr_av_callbacks:
            callback(user_input)

    def clear_user_instructions(self):
        """
        Clear the user instructions.
        """
        self.user_instructions.clear()
        self.usr_instr_av_callbacks.clear()

    def reset(self):
        """
        Reset the user service.
        """
        self.agent_state = None
        self.user_instructions.clear()
        self.usr_instr_av_callbacks.clear()

