{"figma_components": [{"frames": [{"id": "0:3", "name": "Gomoku online", "type": "FRAME", "imageUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/def583fd-db90-4ec4-9211-6ee1070c55ae"}, {"id": "0:130", "name": "Frame 1000000804", "type": "FRAME", "imageUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/3749a6e3-dbd0-4ea3-be25-cb8dbeac13bb"}, {"id": "0:133", "name": "Frame 1000000804", "type": "FRAME", "imageUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/67b7d772-8a1f-4e87-93b8-82ec15c8eae5"}], "fileKey": "OxInGVnd8qN43PHmDpZx4S", "document": {"id": "0:0", "name": "Document", "type": "DOCUMENT", "scrollBehavior": "SCROLLS", "children": [{"id": "0:1", "name": "Page 1", "type": "CANVAS", "scrollBehavior": "SCROLLS", "children": [{"id": "0:3", "name": "Gomoku online", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "0:4", "name": "Group 25379", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:5", "name": "Group 25377", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:6", "name": "Line 20", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -913.694135158559, "y": -645.9999389648438, "width": 4.336168399277085e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -913.6941528320312, "y": -645.9999389648438, "width": 2.00006103515625, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:7", "name": "Line 23", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -759.1263250999652, "y": -645.9999389648438, "width": 4.336168399277085e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -759.1263427734375, "y": -645.9999389648438, "width": 2.00006103515625, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:8", "name": "Line 26", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -602.252240627309, "y": -645.9999389648438, "width": 4.336168399277085e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -602.2522583007812, "y": -645.9999389648438, "width": 2.00006103515625, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:9", "name": "Line 29", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -445.3781561546527, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -445.3781433105469, "y": -645.9999389648438, "width": 2.000030517578125, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:10", "name": "Line 32", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -290.81034609605894, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -290.8103332519531, "y": -645.9999389648438, "width": 2.000030517578125, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:11", "name": "Line 35", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -133.9362616234027, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -133.93626403808594, "y": -645.9999389648438, "width": 2.0000457763671875, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:12", "name": "Line 38", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 20.63154843519106, "y": -645.9999389648438, "width": 4.3361683939480145e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": 20.631547927856445, "y": -645.9999389648438, "width": 2.0000438690185547, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:13", "name": "Line 21", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -860.6334051780902, "y": -645.9999389648438, "width": 4.336168399277085e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -860.6334228515625, "y": -645.9999389648438, "width": 2.00006103515625, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:14", "name": "Line 24", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -706.0665716819965, "y": -645.9999389648438, "width": 4.336168399277085e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -706.0665893554688, "y": -645.9999389648438, "width": 2.00006103515625, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:15", "name": "Line 27", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -549.191449611684, "y": -645.9999389648438, "width": 4.336168399277085e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -549.1914672851562, "y": -645.9999389648438, "width": 2.00006103515625, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:16", "name": "Line 30", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -392.3173651390277, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -392.3173522949219, "y": -645.9999389648438, "width": 2.000030517578125, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:17", "name": "Line 33", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -237.74955508043394, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -237.7495574951172, "y": -645.9999389648438, "width": 2.0000457763671875, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:18", "name": "Line 36", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -80.87547060777769, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -80.87547302246094, "y": -645.9999389648438, "width": 2.0000457763671875, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:19", "name": "Line 39", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 73.69136288831606, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": 73.69136047363281, "y": -645.9999389648438, "width": 2.0000457763671875, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:20", "name": "Line 22", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -809.8798651390277, "y": -645.9999389648438, "width": 4.336168399277085e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -809.8798828125, "y": -645.9999389648438, "width": 2.00006103515625, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:21", "name": "Line 25", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -655.313031642934, "y": -645.9999389648438, "width": 4.336168399277085e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -655.3130493164062, "y": -645.9999389648438, "width": 2.00006103515625, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:22", "name": "Line 28", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -498.43794009019956, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -498.43792724609375, "y": -645.9999389648438, "width": 2.000030517578125, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:23", "name": "Line 31", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -341.5638250999652, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -341.5638122558594, "y": -645.9999389648438, "width": 2.000030517578125, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:24", "name": "Line 34", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -186.99601504137144, "y": -645.9999389648438, "width": 4.336168393592743e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -186.9960174560547, "y": -645.9999389648438, "width": 2.0000457763671875, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:25", "name": "Line 37", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -30.12193056871519, "y": -645.9999389648438, "width": 4.3361683939480145e-05, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -30.121931076049805, "y": -645.9999389648438, "width": 2.0000438690185547, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": -913.6941528320312, "y": -645.9999389648438, "width": 987.3855590820312, "height": 991.9999389648438}, "absoluteRenderBounds": {"x": -913.6941528320312, "y": -645.9999389648438, "width": 989.3855590820312, "height": 991.9999389648438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:26", "name": "Group 25378", "type": "GROUP", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062848, "children": [{"id": "0:27", "name": "Line 20", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062742, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -643.6930188522812, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -643.6929931640625, "width": 991.9999389648438, "height": 2.00006103515625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:28", "name": "Line 23", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -489.1255750046249, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -489.1255798339844, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:29", "name": "Line 26", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -332.2511853561874, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -332.2511901855469, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:30", "name": "Line 29", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -175.37679570774992, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -175.37680053710938, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:31", "name": "Line 32", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -20.809290824937428, "width": 991.9999389648438, "height": 8.672337492754423e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -20.809289932250977, "width": 991.9999389648438, "height": 2.0000858306884766}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:32", "name": "Line 35", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": 136.06509882350008, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": 136.06509399414062, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:33", "name": "Line 38", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": 290.63254267115633, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": 290.6325378417969, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:34", "name": "Line 21", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -590.6325330124374, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -590.6325073242188, "width": 991.9999389648438, "height": 2.00006103515625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:35", "name": "Line 24", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -436.0651196823593, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -436.06512451171875, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:36", "name": "Line 27", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -279.1907300339218, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -279.19073486328125, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:37", "name": "Line 30", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -122.31630986790618, "width": 991.9999389648438, "height": 8.672337493464966e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -122.3163070678711, "width": 991.9999389648438, "height": 2.0000839233398438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:38", "name": "Line 33", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": 32.25113397975007, "width": 991.9999389648438, "height": 8.672337492754423e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": 32.25113296508789, "width": 991.9999389648438, "height": 2.0000877380371094}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:39", "name": "Line 36", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": 189.12552362818758, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": 189.12551879882812, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:40", "name": "Line 39", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": 343.69296747584383, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": 343.6929626464844, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:41", "name": "Line 22", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -539.8790540085312, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -539.8790283203125, "width": 991.9999389648438, "height": 2.00006103515625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:42", "name": "Line 25", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -385.31164067845305, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -385.3116455078125, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:43", "name": "Line 28", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -228.43725103001555, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -228.437255859375, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:44", "name": "Line 31", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": -71.56283086399993, "width": 991.9999389648438, "height": 8.672337493464966e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": -71.56282806396484, "width": 991.9999389648438, "height": 2.0000839233398438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:45", "name": "Line 34", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": 83.00461298365632, "width": 991.9999389648438, "height": 8.672337493464966e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": 83.0046157836914, "width": 991.9999389648438, "height": 2.0000839233398438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:46", "name": "Line 37", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963705062706, "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5416447520256042, "g": 0.26321831345558167, "b": 0.7272623777389526, "a": 1.0}}], "strokeWeight": 2.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": -915.9999389648438, "y": 239.87900263209383, "width": 991.9999389648438, "height": 8.67233749204388e-05}, "absoluteRenderBounds": {"x": -915.9999389648438, "y": 239.87899780273438, "width": 991.9999389648438, "height": 2.000091552734375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": -915.9999821248587, "y": -643.6929754906008, "width": 991.9999821248587, "height": 987.3860907249758}, "absoluteRenderBounds": {"x": -915.9999821248587, "y": -643.6929931640625, "width": 991.9999821248587, "height": 989.3860473632812}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": -916.0, "y": -646.0, "width": 992.0, "height": 992.0}, "absoluteRenderBounds": {"x": -916.0, "y": -646.0, "width": 992.0, "height": 992.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:47", "name": "Rectangle 32 (<PERSON><PERSON>)", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1921568661928177, "g": 0.03921568766236305, "b": 0.29411765933036804, "a": 1.0}}], "fillOverrideTable": {"1": null}, "strokes": [], "strokeWeight": 50.0, "strokeAlign": "INSIDE", "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": -960.0, "y": -690.0, "width": 1920.5, "height": 1080.0}, "absoluteRenderBounds": {"x": -960.0, "y": -690.0, "width": 1920.0, "height": 1080.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.5}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 4.0}, "radius": 10.0, "showShadowBehindNode": false}], "interactions": []}, {"id": "0:48", "name": "Group 25391", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:49", "name": "Player 1", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 273.0, "y": -182.0, "width": 159.0, "height": 43.0}, "absoluteRenderBounds": {"x": 279.0, "y": -172.0, "width": 147.0, "height": 15.0}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "Player 1", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 24.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 30.719999313354492, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "0:50", "name": "Computer", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 613.0, "y": -182.0, "width": 179.0, "height": 43.0}, "absoluteRenderBounds": {"x": 623.0, "y": -172.0, "width": 159.0, "height": 15.0}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "Computer", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 24.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 30.719999313354492, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "0:51", "name": "0:1", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 151.0, "y": -419.0, "width": 751.0, "height": 237.0}, "absoluteRenderBounds": {"x": 299.0, "y": -338.0, "width": 455.0, "height": 125.0}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "0:1", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 200.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 40.0, "lineHeightPx": 256.0, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [11, 11, 10], "styleOverrideTable": {"11": {"letterSpacing": 40.0}, "10": {"fontSize": 200.0, "letterSpacing": 40.0, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}]}}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 151.0, "y": -419.0, "width": 751.0, "height": 280.0}, "absoluteRenderBounds": {"x": 151.0, "y": -419.0, "width": 751.0, "height": 280.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:52", "name": "Group 25385", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:53", "name": "Group 25384", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:54", "name": "Group 25381", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:55", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 749.9916381835938, "y": -527.3147583007812, "width": 60.251834869384766, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 749.9916381835938, "y": -527.3147583007812, "width": 60.2518310546875, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:56", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 629.487548828125, "y": -527.3147583007812, "width": 90.37773895263672, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 629.487548828125, "y": -527.3147583007812, "width": 90.37774658203125, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:57", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 569.2388305664062, "y": -527.3147583007812, "width": 30.125917434692383, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 569.2388305664062, "y": -527.3147583007812, "width": 30.12591552734375, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:58", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 478.85797119140625, "y": -527.3147583007812, "width": 60.251834869384766, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 478.85797119140625, "y": -527.3147583007812, "width": 60.2518310546875, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:59", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 358.3572082519531, "y": -527.3147583007812, "width": 90.37773895263672, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 358.3572082519531, "y": -527.3147583007812, "width": 90.37774658203125, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:60", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 252.9145965576172, "y": -527.3147583007812, "width": 75.31478881835938, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 252.9145965576172, "y": -527.3147583007812, "width": 75.31480407714844, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 252.9145965576172, "y": -527.3147583007812, "width": 557.3289794921875, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 252.9145965576172, "y": -527.3147583007812, "width": 557.3289794921875, "height": 75.31478881835938}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:61", "name": "Group 25382", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:62", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 742.167724609375, "y": -618.9927368164062, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 742.167724609375, "y": -618.9927368164062, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:63", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 646.8582763671875, "y": -618.9927368164062, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 646.8582763671875, "y": -618.9927368164062, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:64", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 551.5488891601562, "y": -618.9927368164062, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 551.5488891601562, "y": -618.9927368164062, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:65", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 442.62445068359375, "y": -618.9927368164062, "width": 81.69324493408203, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 442.62445068359375, "y": -618.9927368164062, "width": 81.6932373046875, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:66", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 347.3161315917969, "y": -618.9927368164062, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 347.3161315917969, "y": -618.9927368164062, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:67", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 252.0078125, "y": -618.9927368164062, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 252.0078125, "y": -618.9927368164062, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 252.0078125, "y": -618.9927368164062, "width": 558.2376098632812, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 252.0078125, "y": -618.9927368164062, "width": 558.2376098632812, "height": 68.07770538330078}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 252.0078125, "y": -618.9927368164062, "width": 558.2376098632812, "height": 166.9927520751953}, "absoluteRenderBounds": {"x": 252.0078125, "y": -618.9927368164062, "width": 558.2376098632812, "height": 166.99276733398438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:68", "name": "Group 25383", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:69", "name": "Group 25381", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:70", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 740.9835815429688, "y": -536.322021484375, "width": 60.251834869384766, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 740.9835815429688, "y": -536.322021484375, "width": 60.2518310546875, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:71", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 620.4794921875, "y": -536.322021484375, "width": 90.37773895263672, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 620.4794921875, "y": -536.322021484375, "width": 90.37774658203125, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:72", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 560.2307739257812, "y": -536.322021484375, "width": 30.125917434692383, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 560.2307739257812, "y": -536.322021484375, "width": 30.12591552734375, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:73", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 469.84991455078125, "y": -536.322021484375, "width": 60.251834869384766, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 469.84991455078125, "y": -536.322021484375, "width": 60.2518310546875, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:74", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 349.3491516113281, "y": -536.322021484375, "width": 90.37773895263672, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 349.3491516113281, "y": -536.322021484375, "width": 90.37774658203125, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:75", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 243.90667724609375, "y": -536.322021484375, "width": 75.31478881835938, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 243.90667724609375, "y": -536.322021484375, "width": 75.31478881835938, "height": 75.31478881835938}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 243.90667724609375, "y": -536.322021484375, "width": 557.3287353515625, "height": 75.31478881835938}, "absoluteRenderBounds": {"x": 243.90667724609375, "y": -536.322021484375, "width": 557.3287353515625, "height": 75.31478881835938}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:76", "name": "Group 25382", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:77", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 733.1597900390625, "y": -628.0, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 733.1597900390625, "y": -628.0, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:78", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 637.850341796875, "y": -628.0, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 637.850341796875, "y": -628.0, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:79", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 542.5408935546875, "y": -628.0, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 542.5408935546875, "y": -628.0, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:80", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 433.61651611328125, "y": -628.0, "width": 81.69324493408203, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 433.61651611328125, "y": -628.0, "width": 81.6932373046875, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:81", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 338.3081970214844, "y": -628.0, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 338.3081970214844, "y": -628.0, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}, {"id": "0:82", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 243.0, "y": -628.0, "width": 68.07770538330078, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 243.0, "y": -628.0, "width": 68.07769775390625, "height": 68.07769775390625}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 243.0, "y": -628.0, "width": 558.2374877929688, "height": 68.07770538330078}, "absoluteRenderBounds": {"x": 243.0, "y": -628.0, "width": 558.2374877929688, "height": 68.07770538330078}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 243.0, "y": -628.0, "width": 558.2374877929688, "height": 166.99276733398438}, "absoluteRenderBounds": {"x": 243.0, "y": -628.0, "width": 558.2374877929688, "height": 166.99276733398438}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 243.0, "y": -628.0, "width": 567.2454223632812, "height": 176.0}, "absoluteRenderBounds": {"x": 243.0, "y": -628.0, "width": 567.2454223632812, "height": 176.00003051757812}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:83", "name": "Group 25392", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:84", "name": "Group 25388", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:85", "name": "Rectangle 21", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "cornerRadius": 10.0, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 184.0, "y": -55.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 184.0, "y": -55.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:86", "name": "easy", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 230.0, "y": -43.0, "width": 117.0, "height": 37.0}, "absoluteRenderBounds": {"x": 239.75, "y": -30.75, "width": 97.5, "height": 18.75}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "easy", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 30.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 38.39999771118164, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 10.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 184.0, "y": -55.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 184.0, "y": -55.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:87", "name": "Group 25389", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:88", "name": "Rectangle 33", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "cornerRadius": 10.0, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 422.0, "y": -55.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 422.0, "y": -55.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:89", "name": "Medium", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 447.0, "y": -43.0, "width": 159.0, "height": 37.0}, "absoluteRenderBounds": {"x": 455.25, "y": -30.75, "width": 142.5, "height": 18.75}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "Medium", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 30.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 38.39999771118164, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 10.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 422.0, "y": -55.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 422.0, "y": -55.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:90", "name": "Group 25390", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:91", "name": "Rectangle 34", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "cornerRadius": 10.0, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 660.0, "y": -55.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 660.0, "y": -55.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:92", "name": "hard", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 685.0, "y": -43.0, "width": 159.0, "height": 37.0}, "absoluteRenderBounds": {"x": 715.75, "y": -30.75, "width": 97.5, "height": 18.75}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "hard", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 30.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 38.39999771118164, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 10.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 660.0, "y": -55.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 660.0, "y": -55.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 10.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 184.0, "y": -55.0, "width": 686.0, "height": 68.0}, "absoluteRenderBounds": {"x": 184.0, "y": -55.0, "width": 686.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:93", "name": "Group 25398", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:94", "name": "Group 25388", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:95", "name": "Rectangle 21", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "cornerRadius": 10.0, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 184.0, "y": 40.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 184.0, "y": 40.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:96", "name": "20:20", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 219.0, "y": 52.0, "width": 139.0, "height": 37.0}, "absoluteRenderBounds": {"x": 232.25, "y": 64.25, "width": 112.5, "height": 18.75}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "20:20", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 30.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 38.39999771118164, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 10.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 184.0, "y": 40.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 184.0, "y": 40.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:97", "name": "Group 25389", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:98", "name": "Rectangle 33", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "cornerRadius": 10.0, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 422.0, "y": 40.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 422.0, "y": 40.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:99", "name": "15:15", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 447.0, "y": 52.0, "width": 159.0, "height": 37.0}, "absoluteRenderBounds": {"x": 474.0, "y": 64.25, "width": 105.0, "height": 18.75}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "15:15", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 30.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 38.39999771118164, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 10.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 422.0, "y": 40.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 422.0, "y": 40.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:100", "name": "Group 25390", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:101", "name": "Rectangle 34", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "cornerRadius": 10.0, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 660.0, "y": 40.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 660.0, "y": 40.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:102", "name": "10:10", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 685.0, "y": 52.0, "width": 159.0, "height": 37.0}, "absoluteRenderBounds": {"x": 712.0, "y": 64.25, "width": 105.0, "height": 18.75}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "characters": "10:10", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "fontSize": 30.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 38.39999771118164, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 10.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 660.0, "y": 40.0, "width": 210.0, "height": 68.0}, "absoluteRenderBounds": {"x": 660.0, "y": 40.0, "width": 210.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 10.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 184.0, "y": 40.0, "width": 686.0, "height": 68.0}, "absoluteRenderBounds": {"x": 184.0, "y": 40.0, "width": 686.0, "height": 68.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:103", "name": "Group 25393", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:104", "name": "Rectangle 35", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -437.0, "y": -115.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -437.0, "y": -115.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:105", "name": "Rectangle 51", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -429.0, "y": -107.0, "width": 22.0, "height": 22.0}, "absoluteRenderBounds": {"x": -429.0, "y": -107.0, "width": 22.0, "height": 22.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": -437.0, "y": -115.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -437.0, "y": -115.0, "width": 38.0, "height": 38.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:106", "name": "Rectangle 40", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -334.0, "y": -167.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -334.0, "y": -167.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:107", "name": "Rectangle 44", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -230.0, "y": -220.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -230.0, "y": -220.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:108", "name": "Rectangle 42", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -230.0, "y": -115.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -230.0, "y": -115.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:109", "name": "Rectangle 48", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -489.0, "y": -220.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -489.0, "y": -220.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:110", "name": "Rectangle 36", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -385.0, "y": -115.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -385.0, "y": -115.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:111", "name": "Rectangle 39", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -334.0, "y": -115.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -334.0, "y": -115.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:112", "name": "Rectangle 41", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -282.0, "y": -115.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -282.0, "y": -115.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:113", "name": "Rectangle 49", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -334.0, "y": -64.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -334.0, "y": -64.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:114", "name": "Rectangle 45", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -282.0, "y": -220.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -282.0, "y": -220.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:115", "name": "Rectangle 43", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -282.0, "y": -166.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -282.0, "y": -166.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:116", "name": "Rectangle 47", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -436.0, "y": -166.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -436.0, "y": -166.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:117", "name": "Group 25394", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:118", "name": "Rectangle 37", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -385.0, "y": -166.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -385.0, "y": -166.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:119", "name": "Rectangle 52", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -377.0, "y": -158.0, "width": 22.0, "height": 22.0}, "absoluteRenderBounds": {"x": -377.0, "y": -158.0, "width": 22.0, "height": 22.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": -385.0, "y": -166.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -385.0, "y": -166.0, "width": 38.0, "height": 38.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:120", "name": "Group 25395", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:121", "name": "Rectangle 38", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -334.0, "y": -220.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -334.0, "y": -220.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:122", "name": "Rectangle 53", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -326.0, "y": -212.0, "width": 22.0, "height": 22.0}, "absoluteRenderBounds": {"x": -326.0, "y": -212.0, "width": 22.0, "height": 22.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": -334.0, "y": -220.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -334.0, "y": -220.0, "width": 38.0, "height": 38.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:123", "name": "Group 25396", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:124", "name": "Rectangle 46", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -282.0, "y": -272.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -282.0, "y": -272.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:125", "name": "Rectangle 54", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -274.0, "y": -264.0, "width": 22.0, "height": 22.0}, "absoluteRenderBounds": {"x": -274.0, "y": -264.0, "width": 22.0, "height": 22.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": -282.0, "y": -272.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -282.0, "y": -272.0, "width": 38.0, "height": 38.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:126", "name": "Group 25397", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:127", "name": "Rectangle 50", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -230.0, "y": -324.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -230.0, "y": -324.0, "width": 38.0, "height": 38.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:128", "name": "Rectangle 55", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37254902720451355, "g": 0.14901961386203766, "b": 0.5215686559677124, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": -222.0, "y": -316.0, "width": 22.0, "height": 22.0}, "absoluteRenderBounds": {"x": -222.0, "y": -316.0, "width": 22.0, "height": 22.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": -230.0, "y": -324.0, "width": 38.0, "height": 38.0}, "absoluteRenderBounds": {"x": -230.0, "y": -324.0, "width": 38.0, "height": 38.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:129", "name": "Group 25391", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "0:130", "name": "Frame 1000000804", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "0:131", "name": "new Game", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 344.5, "y": 195.0, "width": 365.0, "height": 69.0}, "absoluteRenderBounds": {"x": 351.5, "y": 217.25, "width": 351.0, "height": 33.75}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "new Game", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 54.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 69.1199951171875, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "cornerRadius": 20.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 10.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 10.0, "paddingRight": 10.0, "paddingTop": 10.0, "paddingBottom": 14.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 287.0, "y": 180.0, "width": 480.0, "height": 103.0}, "absoluteRenderBounds": {"x": 287.0, "y": 180.0, "width": 480.0, "height": 103.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 20.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 287.0, "y": 180.0, "width": 480.0, "height": 103.0}, "absoluteRenderBounds": {"x": 287.0, "y": 180.0, "width": 480.0, "height": 103.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37244024872779846, "g": 0.15046118199825287, "b": 0.5204264521598816, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.37244024872779846, "g": 0.15046118199825287, "b": 0.5204264521598816, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.37244024872779846, "g": 0.15046118199825287, "b": 0.5204264521598816, "a": 1.0}, "absoluteBoundingBox": {"x": -960.0, "y": -690.0, "width": 1920.0, "height": 1080.0}, "absoluteRenderBounds": {"x": -960.0, "y": -690.0, "width": 1920.0, "height": 1080.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "0:132", "name": "New Game CTA", "type": "COMPONENT", "scrollBehavior": "SCROLLS", "children": [{"id": "0:133", "name": "Frame 1000000804", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "0:134", "name": "new Game", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 347.5, "y": 553.0, "width": 365.0, "height": 69.0}, "absoluteRenderBounds": {"x": 354.5, "y": 575.25, "width": 351.0, "height": 33.75}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "new Game", "style": {"fontFamily": "Silkscreen", "fontPostScriptName": "Silkscreen-Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 54.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 69.1199951171875, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 4, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}}], "strokes": [], "cornerRadius": 20.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 1.0, "g": 0.44999998807907104, "b": 0.0, "a": 1.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 10.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 10.0, "paddingRight": 10.0, "paddingTop": 10.0, "paddingBottom": 14.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 290.0, "y": 538.0, "width": 480.0, "height": 103.0}, "absoluteRenderBounds": {"x": 290.0, "y": 538.0, "width": 480.0, "height": 103.0}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 290.0, "y": 538.0, "width": 480.0, "height": 103.0}, "absoluteRenderBounds": {"x": 290.0, "y": 538.0, "width": 480.0, "height": 103.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "backgroundColor": {"r": 0.11764705926179886, "g": 0.11764705926179886, "b": 0.11764705926179886, "a": 1.0}, "prototypeStartNodeID": null, "flowStartingPoints": [], "prototypeDevice": {"type": "NONE", "rotation": "NONE"}}]}}]}