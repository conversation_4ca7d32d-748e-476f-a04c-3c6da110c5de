"""
Simple Container Tab Helper - Just adds tabs to existing QTabWidget
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QLineEdit, QComboBox, QTextEdit, QHBoxLayout, QPushButton, QDialog
)
from code_generation_core_agent.agents.utilities import FrameworkType
from code_generation_core_agent.project_schemas import ContainerType, Container, Route
import json


def create_container_tab(container : Container):
    """
    Create a simple container tab widget

    Args:
        container_type: ContainerType enum
        container_name: Name of the container
        framework: Framework name

    Returns:
        QWidget: The tab widget
    """
    tab = QWidget()
    layout = QVBoxLayout(tab)

    tab.setProperty("container_type", container.container_type)
    tab.setProperty("container_name", container.container_name)
    tab.setProperty("framework", container.framework)
    tab.setProperty("container", container)

    container_type = container.container_type
    framework = FrameworkType.map_to_known_labels(container.framework) 

    # Container Name
    name_field = QVBoxLayout()
    name_field.addWidget(QLabel("Container Name:"))
    name_input = QLineEdit(container.container_name)
    name_field.addWidget(name_input)
    name_widget = QWidget()
    name_widget.setLayout(name_field)
    layout.addWidget(name_widget)

    # Framework and ENV button in horizontal layout
    framework_env_layout = QHBoxLayout()
    
    # Framework
    framework_field = QVBoxLayout()
    framework_field.addWidget(QLabel("Framework:"))
    framework_combo = QComboBox()

    # Add framework options based on container type
    if container_type == ContainerType.FRONTEND:
        frameworks = get_container_frameworks(FrameworkType.get_frontend_frameworks())
    elif container_type == ContainerType.BACKEND:
        frameworks = get_container_frameworks(FrameworkType.get_backend_frameworks())
    elif container_type == ContainerType.MOBILE:
        frameworks = get_container_frameworks(FrameworkType.get_mobile_frameworks())
    elif container_type == ContainerType.DATABASE:
        frameworks = get_container_frameworks(FrameworkType.get_database_frameworks())
    else:
        frameworks = ["Default"]

    framework_combo.addItems(frameworks)
    if framework in frameworks:
        framework_combo.setCurrentText(framework)

    framework_field.addWidget(framework_combo)
    framework_widget = QWidget()
    framework_widget.setLayout(framework_field)
    framework_env_layout.addWidget(framework_widget)
    
    # ENV Button
    env_field = QVBoxLayout()
    env_field.addWidget(QLabel("Environment:"))
    env_button = QPushButton("ENV Variables")
    env_button.setEnabled(container.container_name and container.framework and container.framework != "None")
    env_button.clicked.connect(lambda: open_env_dialog(container, tab, env_button))
    env_field.addWidget(env_button)
    env_widget = QWidget()
    env_widget.setLayout(env_field)
    framework_env_layout.addWidget(env_widget)
    
    # Add the horizontal layout to main layout
    framework_env_container = QWidget()
    framework_env_container.setLayout(framework_env_layout)
    layout.addWidget(framework_env_container)

    # Description
    desc_field = QVBoxLayout()
    desc_field.addWidget(QLabel("Description:"))
    desc_input = QTextEdit()
    desc_input.setMaximumHeight(80)
    desc_input.setPlainText(container.description)
    desc_field.addWidget(desc_input)
    desc_widget = QWidget()
    desc_widget.setLayout(desc_field)
    layout.addWidget(desc_widget)

    # Interfaces
    interfaces_field = QVBoxLayout()
    interfaces_field.addWidget(QLabel("Interfaces:"))
    interfaces_input = QTextEdit()
    interfaces_input.setPlainText(container.interfaces)
    interfaces_input.setMaximumHeight(80)
    interfaces_field.addWidget(interfaces_input)
    interfaces_widget = QWidget()
    interfaces_widget.setLayout(interfaces_field)
    layout.addWidget(interfaces_widget)

    # Route Path and Endpoint (for first route)
    route_layout = QHBoxLayout()
    
    # Route Path
    route_path_field = QVBoxLayout()
    route_path_field.addWidget(QLabel("Route Path:"))
    route_path_input = QLineEdit()
    # Get the path from the first route if it exists
    if container.routes and len(container.routes) > 0:
        route_path_input.setText(container.routes[0].path)
    route_path_field.addWidget(route_path_input)
    route_path_widget = QWidget()
    route_path_widget.setLayout(route_path_field)
    route_layout.addWidget(route_path_widget)
    
    # Route Endpoint
    route_endpoint_field = QVBoxLayout()
    route_endpoint_field.addWidget(QLabel("Route Endpoint:"))
    route_endpoint_input = QLineEdit()
    # Get the endpoint from the first route if it exists
    if container.routes and len(container.routes) > 0:
        route_endpoint_input.setText(container.routes[0].endpoint)
    route_endpoint_field.addWidget(route_endpoint_input)
    route_endpoint_widget = QWidget()
    route_endpoint_widget.setLayout(route_endpoint_field)
    route_layout.addWidget(route_endpoint_widget)
    
    # Add route layout to main layout
    route_container = QWidget()
    route_container.setLayout(route_layout)
    layout.addWidget(route_container)

    # Container Details
    container_details_field = QVBoxLayout()
    container_details_field.addWidget(QLabel("Container Details:"))
    container_details_input = QTextEdit()
    container_details_input.setPlainText(json.dumps(container.container_details))
    container_details_input.setMaximumHeight(80)
    container_details_field.addWidget(container_details_input)
    container_details_widget = QWidget()
    container_details_widget.setLayout(container_details_field)
    layout.addWidget(container_details_widget)

    return tab


def open_env_dialog(container, parent_widget, env_button):
    """Open the environment variables dialog for a container"""
    # Find the main GUI window
    main_window = parent_widget
    while main_window and not hasattr(main_window, 'open_container_env_dialog'):
        main_window = main_window.parent()
    
    if main_window and hasattr(main_window, 'open_container_env_dialog'):
        main_window.open_container_env_dialog(container)
        # Update the ENV button state
        env_button.setEnabled(container.container_name and container.framework and container.framework != "None")
    else:
        # Fallback if main window not found
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(parent_widget, "Info", "Environment variables dialog not available.")


def add_container_tabs_to_tabwidget(tab_widget, containers):
    """
    Add container tabs to existing QTabWidget

    Args:
        tab_widget: QTabWidget to add tabs to
        containers: List of containers from project schema
    """
    while tab_widget.count() > 1:
        tab_widget.removeTab(1)

    # Add new tabs for each container
    for container in containers:
        # More lenient condition - show container if it has a name and framework is not "None"
        if container.container_name and container.framework and container.framework != "None":
            print(f"Creating tab for container: {container.container_name} with framework: {container.framework}")
            tab = create_container_tab(container)
            tab_name = f"{container.container_type.value.capitalize()}"

            tab_widget.addTab(tab, tab_name)

            tab_widget.setCurrentWidget(tab)
            connect_tab_change_handlers(tab_widget)
        else:
            print(f"Skipping container: {container.container_name} with framework: {container.framework}")



def get_active_tab_container(tab_widget) -> Container | None:
    """
    Get the container type of the currently active tab

    Args:
        tab_widget: QTabWidget

    Returns:
        ContainerType or None: The container type of the active tab
    """
    current_tab = tab_widget.currentWidget()
    if current_tab:
        return current_tab.property("container")
    return None


def sync_current_tab_to_container_data(tab_widget):
    """
    Synchronize the currently active tab's form fields with its stored container data.
    Uses the container reference already stored on the tab widget.

    Args:
        tab_widget: QTabWidget containing container tabs

    Returns:
        bool: True if sync was successful, False if no active container tab
    """
    current_container = get_active_tab_container(tab_widget)

    if not current_container:
        return False

    current_tab = tab_widget.currentWidget()
    if not current_tab:
        return False

    name_widget = current_tab.findChild(QLineEdit)
    if name_widget:
        name_widget.setText(current_container.container_name)

    # Update framework dropdown
    framework_combos = current_tab.findChildren(QComboBox)
    for combo in framework_combos:
        parent_label = combo.parent().findChild(QLabel)
        if parent_label and "Framework" in parent_label.text():
            if current_container.framework in [combo.itemText(i) for i in range(combo.count())]:
                combo.setCurrentText(current_container.framework)
            break

    # Update description and interfaces text areas
    text_edits = current_tab.findChildren(QTextEdit)
    for text_edit in text_edits:
        parent_label = text_edit.parent().findChild(QLabel)
        if parent_label:
            label_text = parent_label.text()
            if "Description" in label_text:
                text_edit.setPlainText(current_container.description)
            elif "Interfaces" in label_text:
                text_edit.setPlainText(current_container.interfaces)
            elif "Container Details" in label_text:
                text_edit.setPlainText(json.dumps(current_container.container_details, indent=2))

    # Update route fields
    line_edits = current_tab.findChildren(QLineEdit)
    for line_edit in line_edits:
        parent_label = line_edit.parent().findChild(QLabel)
        if parent_label:
            label_text = parent_label.text()
            if "Route Path" in label_text:
                # Set route path from first route if it exists
                if current_container.routes and len(current_container.routes) > 0:
                    line_edit.setText(current_container.routes[0].path)
                else:
                    line_edit.setText("")
            elif "Route Endpoint" in label_text:
                # Set route endpoint from first route if it exists
                if current_container.routes and len(current_container.routes) > 0:
                    line_edit.setText(current_container.routes[0].endpoint)
                else:
                    line_edit.setText("")

    print(
        f"Synced tab fields for container: {current_container.container_name} ({current_container.container_type.value})")
    return True


def sync_container_from_current_tab(tab_widget):
    """
    Update the stored container object with the current form field values.
    Call this when form fields have been changed by the user.

    Args:
        tab_widget: QTabWidget containing container tabs

    Returns:
        Container: Updated container object, or None if no active container tab
    """
    current_container = get_active_tab_container(tab_widget)

    if not current_container:
        return None

    current_tab = tab_widget.currentWidget()
    if not current_tab:
        return None

    # Get updated values from form fields
    updated_name = current_container.container_name
    updated_framework = current_container.framework
    updated_description = current_container.description
    updated_interfaces = current_container.interfaces

    # Extract container name from form
    name_widget = current_tab.findChild(QLineEdit)
    if name_widget:
        updated_name = name_widget.text()

    # Extract framework from dropdown
    framework_combos = current_tab.findChildren(QComboBox)
    for combo in framework_combos:
        parent_label = combo.parent().findChild(QLabel)
        if parent_label and "Framework" in parent_label.text():
            updated_framework = combo.currentText()
            break

    # Extract description and interfaces from text areas
    text_edits = current_tab.findChildren(QTextEdit)
    for text_edit in text_edits:
        parent_label = text_edit.parent().findChild(QLabel)
        if parent_label:
            label_text = parent_label.text()
            if "Description" in label_text:
                updated_description = text_edit.toPlainText()
            elif "Interfaces" in label_text:
                updated_interfaces = text_edit.toPlainText()
            elif "Container Details" in label_text:
                try:
                    current_container.container_details = json.loads(text_edit.toPlainText())
                except json.JSONDecodeError:
                    print("Invalid JSON in container details field, skipping update.")
                    current_container.container_details = {}

    # Extract route data from line edits
    line_edits = current_tab.findChildren(QLineEdit)
    route_path = ""
    route_endpoint = ""
    for line_edit in line_edits:
        parent_label = line_edit.parent().findChild(QLabel)
        if parent_label:
            label_text = parent_label.text()
            if "Route Path" in label_text:
                route_path = line_edit.text()
            elif "Route Endpoint" in label_text:
                route_endpoint = line_edit.text()

    # Update or create the first route
    if route_path or route_endpoint:
        # Ensure routes list exists
        if not current_container.routes:
            current_container.routes = []
        
        # Create or update the first route
        if len(current_container.routes) == 0:
            current_container.routes.append(Route(path=route_path, endpoint=route_endpoint))
        else:
            current_container.routes[0].path = route_path
            current_container.routes[0].endpoint = route_endpoint

    # Update the container object with new values
    current_container.container_name = updated_name
    current_container.framework = updated_framework
    current_container.description = updated_description
    current_container.interfaces = updated_interfaces

    # Store the updated container back on the tab
    current_tab.setProperty("container", current_container)

    print(
        f"Updated container data from form: {current_container.container_name} ({current_container.container_type.value})")
    return current_container


def connect_tab_change_handlers(tab_widget):
    """
    Connect change handlers to form fields in the current tab to automatically
    sync container data when fields are modified.

    Args:
        tab_widget: QTabWidget containing container tabs
    """
    current_tab = tab_widget.currentWidget()
    if not current_tab:
        return

    name_widget = current_tab.findChild(QLineEdit)
    if name_widget:
        name_widget.textChanged.connect(lambda: sync_container_from_current_tab(tab_widget))

    framework_combos = current_tab.findChildren(QComboBox)
    for combo in framework_combos:
        parent_label = combo.parent().findChild(QLabel)
        if parent_label and "Framework" in parent_label.text():
            combo.currentTextChanged.connect(lambda: sync_container_from_current_tab(tab_widget))
            break

    text_edits = current_tab.findChildren(QTextEdit)
    for text_edit in text_edits:
        text_edit.textChanged.connect(lambda: sync_container_from_current_tab(tab_widget))

    # Connect route field change handlers
    line_edits = current_tab.findChildren(QLineEdit)
    for line_edit in line_edits:
        parent_label = line_edit.parent().findChild(QLabel)
        if parent_label:
            label_text = parent_label.text()
            if "Route Path" in label_text or "Route Endpoint" in label_text:
                line_edit.textChanged.connect(lambda: sync_container_from_current_tab(tab_widget))


def sync_project_schema_to_overview_fields(overview_fields, project_schema):
    """
    Sync project schema data TO overview UI fields (schema -> GUI)

    Args:
        overview_fields: Dictionary of overview UI field references
        project_schema: ProjectSchema object to read from
    """
    if not project_schema:
        return

    overview = project_schema.overview

    # Update basic fields
    overview_fields["project_name"].setPlainText(overview.project_name)
    overview_fields["description"].setPlainText(overview.description)

    # Update framework dropdowns from containers
    frontends = project_schema.get_containers_by_type(ContainerType.FRONTEND)
    if frontends:
        overview_fields["frontend_framework"].setCurrentText(frontends[0].framework)
    else:
        overview_fields["frontend_framework"].setCurrentText("Not Selected")

    backends = project_schema.get_containers_by_type(ContainerType.BACKEND)
    if backends:
        overview_fields["backend_framework"].setCurrentText(backends[0].framework)
    else:
        overview_fields["backend_framework"].setCurrentText("Not Selected")

    databases = project_schema.get_containers_by_type(ContainerType.DATABASE)
    if databases:
        overview_fields["database_framework"].setCurrentText(databases[0].framework)
    else:
        overview_fields["database_framework"].setCurrentText("Not Selected")


def connect_overview_change_handlers(overview_fields, project_schema, manifest_path):
    """
    Connect change handlers to overview fields to sync back to project schema (GUI -> schema)
    Similar pattern to connect_tab_change_handlers

    Args:
        overview_fields: Dictionary of overview UI field references
        project_schema: ProjectSchema object to update
        manifest_path: Path to save manifest when changes occur
    """

    def sync_to_schema():
        if not project_schema:
            return

        # Update overview data FROM UI TO schema
        project_schema.overview.project_name = overview_fields["project_name"].toPlainText()
        project_schema.overview.description = overview_fields["description"].toPlainText()

        # Update container frameworks
        frontends = project_schema.get_containers_by_type(ContainerType.FRONTEND)
        if frontends:
            frontends[0].framework = overview_fields["frontend_framework"].currentText()

        backends = project_schema.get_containers_by_type(ContainerType.BACKEND)
        if backends:
            backends[0].framework = overview_fields["backend_framework"].currentText()

        databases = project_schema.get_containers_by_type(ContainerType.DATABASE)
        if databases:
            databases[0].framework = overview_fields["database_framework"].currentText()

        # Save to manifest
        project_schema.save_to_manifest(manifest_path)

    # Connect the change signals
    overview_fields["project_name"].textChanged.connect(sync_to_schema)
    overview_fields["description"].textChanged.connect(sync_to_schema)
    overview_fields["frontend_framework"].currentTextChanged.connect(sync_to_schema)
    overview_fields["backend_framework"].currentTextChanged.connect(sync_to_schema)
    overview_fields["database_framework"].currentTextChanged.connect(sync_to_schema)


def get_container_frameworks(get_frameworks_method):
    return [FrameworkType.map_to_known_labels(framework.value) for framework in get_frameworks_method]