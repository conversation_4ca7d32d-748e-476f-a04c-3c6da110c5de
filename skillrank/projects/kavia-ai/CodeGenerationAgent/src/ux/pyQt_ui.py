import base64
import html
import sys
import os
import datetime
from collections import deque
from enum import Enum
from queue import Queue
from typing import List, Optional, Dict, Union, Any
from pathlib import Path

from PyQt6.QtWebEngineWidgets import QWebEngineView
from rich.console import Console

from code_generation_core_agent.agents.preview.preview_manager import PreviewManager
from code_generation_core_agent.agents.project_welcome_page import create_project_welcome_page
from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader
from code_generation_core_agent.agents.tools.AgentPreProcessor import FunctionCallDescription
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType
from code_generation_core_agent.chat.chat_enabled_worker import Chat<PERSON><PERSON>bled<PERSON>orker
from code_generation_core_agent.config import config
from dotenv import load_dotenv
from PyQt6.QtGui import QIcon, QDesktopServices
from PyQt6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout,
                             QLineEdit, Q<PERSON>ush<PERSON>utton, QProgressBar,
                             QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView,
                             QTabWidget, QListWidget, QComboBox, QDialog, QFormLayout, QDialogButtonBox, QFileDialog,
                             QListWidgetItem, QMessageBox, QAbstractItemView)
from PyQt6.QtCore import QObject, pyqtSignal, pyqtSlot, QByteArray, QThread, QUrl
from PyQt6.QtGui import  QPixmap, QImage, QFont, QTextCharFormat, QColor
from PyQt6.QtWidgets import QTextEdit, QLabel, QScrollArea, QFrame, QVBoxLayout, QApplication
from PyQt6.QtCore import Qt, QEvent, QPoint, QTimer
from PyQt6.QtGui import QTextCursor, QMouseEvent

import yaml, json
import asyncio
import re

import threading
import signal
import pydevd


from code_generation_core_agent.agents.code_generation_agent import CodeGenerationReporter
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent, TaskExecutionReporter
from code_generation_core_agent.llm.chat_api import start_flask_server
from code_generation_core_agent.llm.chat_interface import ChatInterface
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.code_generation_agent import CodeGenerationControl as AbstractCodeGenerationControl
from code_generation_core_agent.agents.prompt_helper import prompt_prepare
from code_generation_core_agent.agents.prompt_helper import prompt_prepare_image
from code_generation_core_agent.agents.setup_logger import setup_logger

# Must precede any llm module imports

from langtrace_python_sdk import langtrace, with_langtrace_root_span

from code_generation_core_agent.project_schemas import dataclass_to_llm_schema, ProjectSchema, \
    convert_work_item_to_container_config, get_container_empty_work_item, \
    ProjectOverview, ContainerType, CodeGenerationStatus, Container, get_platform_from_container_type
from ux.demo_workitems import PREDEFINED_WORK_ITEMS, PREDEFINED_WORK_ITEMS_FOR_FRAMEWORKS
from ux.gui_helpers import add_container_tabs_to_tabwidget, get_active_tab_container, \
    connect_overview_change_handlers, sync_project_schema_to_overview_fields

SUPPORTED_CONTAINER_LIST=['frontend', 'mobile', 'backend', 'database']

AGENT_CONFIGS = {
    "DocumentCreation": {
        "prompts_path": "src/code_generation_core_agent/agents/micro/prompts"
    },
    "CodeMaintenance": {
        "prompts_path": "src/code_generation_core_agent/agents/micro/prompts"
    },
    "CodeGeneration": {
        "prompts_path": "src/code_generation_core_agent/agents/micro/prompts"
    },
    "DocumentCreation": {
        "prompts_path": "src/code_generation_core_agent/agents/micro/prompts"
    }
}

class BackendListFields(Enum):
    ACTIONS = 0
    FOLDER = 1
    TCP_PORTS = 2
    STARTUP_COMMAND = 3
    INSTALL_COMMANDS = 4
    CI_LINT_COMMAND = 5

# Check if langtrace configuration exists in environment variables
if os.getenv('LANGTRACE_API_HOST') and os.getenv('LANGTRACE_API_KEY'):
    langtrace.init(
        api_host=os.getenv('LANGTRACE_API_HOST'),
        api_key=os.getenv('LANGTRACE_API_KEY')
    )

DATABSE_CONNECTION_ = None
db_lock_ = threading.Lock()
console = Console()

def signal_handler(sig, frame):
    QApplication.quit()


class ProcessWorker(QObject):
    finished_signal = pyqtSignal()
    progress_signal = pyqtSignal(str)

    def __init__(self, agent, agent_type, work_item, control,
                 reset_context, reporter, chat, container_type):
        super().__init__()
        self.agent = agent
        self.agent_type = agent_type
        self.work_item = work_item
        self.control = control
        self.container_type = container_type
        if 'global_context_history' not in globals():
            globals()['global_context_history'] = {}

        global_context_history = globals()['global_context_history']

        if (reset_context):
            global_context_history[container_type] = ""

        context_history = global_context_history.get(container_type, "")
        self.previous_context = context_history

        self.reporter = reporter
        self.chat = chat
        self.git_links = work_item.get('github_links')


    def run(self):
        try:
            threading.current_thread().name = f"{self.agent_type}Worker"
            try:
                pydevd.settrace(suspend=False)  # only for debugging, setting breakpoints.
            except ConnectionRefusedError:
                print("Could not connect to the debugger. Continuing without debugging.")

            self.previous_context = asyncio.run(self.agent.process_work_item(
                self.agent_type,
                self.work_item,
                use_retriever=False,
                control=self.control,
                status_reporter=self.reporter,
                previous_context=self.previous_context,
                chat_interface=self.chat,
                git_links = self.git_links
            ))

            global_context_history = globals()['global_context_history']
            global_context_history[self.container_type] = self.previous_context
        except Exception as e:
            self.progress_signal.emit(f"Error in {self.agent_type}: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            self.finished_signal.emit()

    def send_user_input(self, user_input):
        self.agent.set_user_input(user_input)


class PersistentToolTip(QFrame):
    def __init__(self, text, parent=None):
        super().__init__(parent, Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint)
        layout = QVBoxLayout(self)
        self.scroll_area = QScrollArea(self)
        self.content = QLabel(text)
        self.content.setWordWrap(True)
        self.content.setTextFormat(Qt.TextFormat.RichText)
        self.scroll_area.setWidget(self.content)
        self.scroll_area.setWidgetResizable(True)
        layout.setContentsMargins(3, 3, 3, 3)
        layout.addWidget(self.scroll_area)
        self.setLayout(layout)
        self.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border: 1px solid #555;
                border-radius: 5px;
            }
            QLabel {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: Arial, sans-serif;
            }
            QScrollBar:vertical {
                border: none;
                background: #2b2b2b;
                width: 10px;
                margin: 0px 0px 0px 0px;
            }
            QScrollBar::handle:vertical {
                background: #555;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        self.adjustSize()

    def adjustSize(self):
        content_height = self.content.sizeHint().height()
        scroll_area_height = min(content_height + 10, 400)
        self.scroll_area.setFixedSize(500, scroll_area_height)
        super().adjustSize()

class UserInputBox(QTextEdit):
    def __init__(self, parent=None):
        super().__init__(parent)

    def canInsertFromMimeData(self, source ):
        if not source.hasUrls():
            if source.hasText():
                return True
            super().canInsertFromMimeData(source)
        urls = source.urls()
        for url in urls:
            file_path = url.toString()
            if file_path.lower().endswith(('.png', '.jpg', '.jpeg' )):
                return True
        return False

    def insertFromMimeData(self, source ):
        if not source.hasUrls():
            if source.hasText():
                plain_text = source.text()
                self.insertPlainText(plain_text)
                return
            return super().insertFromMimeData(source)
        urls = source.urls()
        urlString = urls[0].toString()
        file_type = None
        file_path = None
        if urlString.lower().endswith(('.jpg', '.jpeg' )):
            file_type = '.jpeg'
        if urlString.lower().endswith('.png'):
            file_type = '.png'
        if file_type:
            if urlString.lower().startswith("http"):
                file_path =  prompt_prepare_image(urlString)
            else:
                file_path = urls[0].toLocalFile()
        if file_path:
            self.append(f"<img src=\"{file_path}\" width=\"40\" height=\"40\" />")

class CodeViewEditBox(QTextEdit):
    def __init__(self, parent=None):
        super().__init__(parent)

    def show_code(self, filename=None, content=None, operation = "read", color_info=None):
        if content is None and isinstance(filename, (str, dict)):
            # Handle legacy format for backward compatibility
            if isinstance(filename, dict):
                content = filename.get('content', '')
                filename = filename.get('filename', '')
                color_info = filename.get('color_info', None)
            else:
                content = filename
                filename = None
                color_info = None

        if content:
            cursor = self.textCursor()

            # Set up text formats
            bold_format = QTextCharFormat()
            bold_format.setFontWeight(QFont.Weight.Bold)
            bold_format.setFontUnderline(True)
            bold_format.setForeground(QColor(0xe5, 0xc0, 0x7b))

            normal_format = QTextCharFormat()
            normal_format.setFontWeight(QFont.Weight.Normal)

            # Update content if filename is present and not a hidden file
            if filename:
                basename = os.path.basename(filename)
                if not basename.startswith('.'):
                    self.clear()
                    cursor.insertText(f"{filename}:\n\n", bold_format)

                    # If we have color information, apply it
                    if color_info and isinstance(color_info, dict):
                        ranges = color_info.get('ranges', '')
                        last_pos = 0
                        for block in ranges:
                            start = block[0]
                            end = block[1]

                            # Insert any text before this block with normal formatting
                            if start > last_pos:
                                cursor.insertText(content[last_pos:start], normal_format)

                            # Create a format for this block based on operation type
                            block_format = QTextCharFormat(normal_format)
                            if operation == 'read':
                                block_format.setForeground(QColor('#e0e0e0'))  # White
                            elif operation == 'write':
                                block_format.setForeground(QColor('#98c379'))  # Green
                            elif operation == 'edit':
                                block_format.setForeground(QColor('#61afef'))  # Blue

                            # Insert the colored block
                            cursor.insertText(content[start:end], block_format)
                            last_pos = end

                        # Insert any remaining text with normal formatting
                        if last_pos < len(content):
                            cursor.insertText(content[last_pos:], normal_format)
                    else:
                        # If no color info, insert all content with normal formatting
                        cursor.insertText(content, normal_format)

class FunctionCallEditBox(QTextEdit):
    def __init__(self, parent=None, max_tooltip_data=500):
        super().__init__(parent)
        self.tooltip_data = deque(maxlen=max_tooltip_data)
        self.setMouseTracking(True)
        self.current_tooltip = None
        self.hover_timer = QTimer(self)
        self.hover_timer.setSingleShot(True)
        self.hover_timer.timeout.connect(self.show_tooltip)
        self.current_pos = QPoint()
        self.call_counter = 0
        self.hover_word = None
        self.installEventFilter(self)
        self.max_tooltip_data = max_tooltip_data

    def eventFilter(self, obj, event):
        if event.type() == QEvent.Type.ToolTip:
            self.handle_hover(event.pos())
            return True
        return super().eventFilter(obj, event)

    def handle_hover(self, pos):
        cursor = self.cursorForPosition(pos)
        cursor.select(QTextCursor.SelectionType.WordUnderCursor)
        if cursor.hasSelection():
            word = cursor.selectedText()
            if word != self.hover_word:
                self.hover_word = word
                self.current_pos = pos
                self.hover_timer.start(200)
        else:
            self.hover_word = None

    def mouseMoveEvent(self, event: QMouseEvent):
        super().mouseMoveEvent(event)
        self.handle_hover(event.pos())

    def show_tooltip(self):
        if self.hover_word:
            tooltip_text = self.get_tooltip_for_function(self.hover_word, self.current_pos)
            if tooltip_text:
                if self.current_tooltip:
                    self.current_tooltip.close()
                self.current_tooltip = PersistentToolTip(tooltip_text)
                global_pos = self.mapToGlobal(self.current_pos)
                self.current_tooltip.move(global_pos + QPoint(10, 10))
                self.current_tooltip.show()

    def hide_tooltip(self):
        self.hover_timer.stop()
        if self.current_tooltip:
            self.current_tooltip.close()
            self.current_tooltip = None

    def get_tooltip_for_function(self, function_name, pos):
        cursor = self.cursorForPosition(pos)
        for call in reversed(self.tooltip_data):
            if call['function_name'] == function_name:
                if call['start_pos'] <= cursor.position() <= call['end_pos']:
                    return self.format_function_call(call)
        return None

    @staticmethod
    def format_function_call(call):
        formatted = f"<h3 style='color: #61afef;'>Function: {call['function_name']}</h3>"
        formatted += f"<p><strong>Reason:</strong> {call['reason']}</p>"
        formatted += f"<p><strong>Observations:</strong> {call['observations']}</p>"
        formatted += f"<p><strong>Root Cause Analysis:</strong> {call['root_cause_analysis']}</p>"
        formatted += "<h4>Arguments:</h4>"

        for name, value in call['arguments'].items():
            formatted += f"<p><strong>{name}:</strong></p>"
            if isinstance(value, str) and ('\n' in value or '  ' in value):
                escaped_value = html.escape(value)
                formatted += (f"<pre style='background-color: #1e1e1e; padding: 10px;"
                              f" border-radius: 5px; white-space: pre-wrap; word-wrap: break-word;'>"
                              f"<code style='color: #d4d4d4; font-family: Consolas,"
                              f" Monaco, \"Courier New\", monospace;'>{escaped_value}</code></pre>")
            else:
                formatted += f"<p>{value}</p>"

        return formatted

    def append_function_call(self, function_name, reason, observations, root_cause_analysis, arguments):
        start_pos = self.textCursor().position()
        self.call_counter += 1
        call_id = f"{function_name}_{self.call_counter}"

        new_call = {
            'id': call_id,
            'function_name': function_name,
            'reason': reason,
            'observations': observations,
            'root_cause_analysis': root_cause_analysis,
            'arguments': arguments,
            'start_pos': start_pos,
            'end_pos': start_pos
        }

        self.tooltip_data.append(new_call)

    def update_last_call_end_pos(self):
        if self.tooltip_data:
            self.tooltip_data[-1]['end_pos'] = self.textCursor().position()

class pyQTReporter(CodeGenerationReporter, TaskExecutionReporter):
    user_input_requested = pyqtSignal(str)
    user_input_received = pyqtSignal(str)

    def __init__(self, gui):
        self.gui = gui
        self.input_queue = Queue()



    def task_start_callback(self, task_description):
        self.gui.update_status_signal.emit(task_description)

    def progress_callback(self, status, total_tasks, latest_result, request_context):
        self.gui.store_progress_signal.emit(status, total_tasks, latest_result, request_context)

    def terminal_output_callback(self, output):
        self.gui.update_terminal_signal.emit(output)

    def browser_output_callback(self, image):
        self.gui.update_browser_signal.emit(image)

    def cost_update_callback(self, agent_costs, total_cost):
        self.gui.update_cost_signal.emit(agent_costs, total_cost)

    def user_query_callback(self, questions):
        self.gui.update_questions_signal.emit(questions)

    def function_call_callback(self, call_description: FunctionCallDescription):
        self.gui.update_function_call_signal.emit(call_description)

    def codeview_callback(self, code_data):
        try:
            if isinstance(code_data, dict):
                # Pass the entire dictionary to handle color_info
                self.gui.show_code_signal.emit(code_data)
        except Exception as e:
            print(f"Error in codeview_callback: {str(e)}")

    def send_agent_message(self, message):
        self.gui.display_chat_message_signal.emit("Agent", message)

    def task_start_callback(self, task_description):
        self.gui.update_status_signal.emit(task_description)

    async def get_user_input(self, prompt):
        # Use the GUI's method to request user input
        self.gui.request_user_input_signal.emit(prompt)
        # Use asyncio to wait for the queue in a non-blocking way
        loop = asyncio.get_event_loop()
        result =  await loop.run_in_executor(None, self.input_queue.get)
        return result

    def set_user_input(self, user_input):
            self.input_queue.put(user_input)

    def send_code_panel_update(self, stream_id, content, metadata=None):
        """Send streaming code updates to the UI.

        Accumulate content until a new file is signaled. When a new file begins
        (via a metadata dictionary with type "start"), flush (print) the accumulated
        content and reset the buffer.
        """
        if not hasattr(self, 'logger'):
            session_dir = self.gui.generate_session_dir()
            self.logger = setup_logger("PyQt", session_dir, config_dir=None)
        # Initialize the accumulator if it doesn't already exist.
        if not hasattr(self, '_accumulated_code'):
            self._accumulated_code = ""

        # Handle metadata: if a new file is starting, flush the previous content.
        if metadata:
            msg_type = metadata.get('type', 'content')
            if msg_type == "start":
                # Print the header for the new file.
                file_path = metadata.get('file_path', 'unknown')
                self.logger.info(f"Starting file: {file_path}")
                #print(f"Starting file: {file_path}")

            if msg_type == "end":
                # If we have any accumulated content, output it.
                if self._accumulated_code:
                    file_path = metadata.get('file_path', 'unknown')
                    self.logger.info(f"Accumulated content for {file_path}:")
                    self.logger.info(self._accumulated_code)
                    # Reset the accumulator for the new file.
                    self._accumulated_code = ""
                    self.logger.info(f"Ending file: {file_path}")
                else:
                    self.logger.info(f"Ending file with no content.")
                return

        # Append new content to the accumulator.
        if content:
            self._accumulated_code += content

    def app_state_callback(self, url, state=None, container=None):
        """Update the status for a specific container and refresh the preview display."""
        if container:
            if container not in self.gui.preview_statuses:
                self.gui.preview_statuses[container] = {}
            self.gui.preview_statuses[container]["url"] = url
            self.gui.preview_statuses[container]["state"] = state
        self.gui.update_preview_display()

    def universal_init_status_callback(self, container_name, status, message=None, error=None):
        """
        Report the status of universal initialization for a container/component.
        
        Args:
            container_name (str): Name of the container or component being initialized
            status (str): Status of initialization ('starting', 'success', 'failed', 'completed')
            message (str, optional): Status message or description
            error (str, optional): Error message if initialization failed
        """
        self.gui.update_status_signal.emit(f"{container_name}:{status} - {message} {error}")

class CodeGenerationControl(AbstractCodeGenerationControl):
    def __init__(self):
        self.is_paused = False
        self.should_stop = False
        self.pause_condition = threading.Condition()

    def pause(self):
        self.is_paused = True

    def resume(self):
        with self.pause_condition:
            self.is_paused = False
            self.pause_condition.notify_all()

    def stop(self):
        self.should_stop = True
        self.resume()  # In case it's paused

    def check_status(self):
        if self.should_stop:
            raise StopIteration("Code generation stopped")
        with self.pause_condition:
            while self.is_paused:
                self.pause_condition.wait()

    def reset(self):
        self.is_paused = False
        self.should_stop = False


class ThirdPartyServicesWidget(QWidget):
    """A compound widget to list / edit 3rd‑party services."""

    # Add signal for when services change
    services_changed = pyqtSignal()

    def __init__(self, parent=None):  # noqa: D401
        super().__init__(parent)

        self._list = QListWidget()
        self._list.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)

        # add / remove controls ---------------------------------------------
        add_edit = QLineEdit()
        add_edit.setPlaceholderText("Enter service name (e.g., Supabase, Firebase, Stripe)")
        add_btn = QPushButton("Add")
        rm_btn = QPushButton("Remove Selected")

        add_btn.clicked.connect(lambda: self._add_service(add_edit))
        rm_btn.clicked.connect(self._remove_selected)

        # Connect Enter key to add service
        add_edit.returnPressed.connect(lambda: self._add_service(add_edit))

        ctl = QHBoxLayout()
        ctl.addWidget(add_edit)
        ctl.addWidget(add_btn)
        ctl.addWidget(rm_btn)
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("3rd‑Party Services:"))
        layout.addWidget(self._list)
        layout.addLayout(ctl)

    # ------------------------------------------------------------------ #
    #  Public API                                                        #
    # ------------------------------------------------------------------ #
    def services(self) -> list[str]:
        """Return the current list of services as plain strings."""
        return [self._list.item(i).text() for i in range(self._list.count())]

    def set_services(self, items: list[str] | None):  # noqa: D401
        self._list.clear()
        for it in (items or []):
            self._list.addItem(str(it))
        # Emit signal after setting services
        self.services_changed.emit()

    # ------------------------------------------------------------------ #
    #  Internals                                                         #
    # ------------------------------------------------------------------ #
    def _add_service(self, edit: QLineEdit) -> None:  # noqa: D401
        txt = edit.text().strip()
        if txt:
            # Check if service already exists
            existing_services = self.services()
            if txt not in existing_services:
                self._list.addItem(txt)
                edit.clear()
                # Emit signal when service is added
                self.services_changed.emit()

    def _remove_selected(self) -> None:  # noqa: D401
        selected_items = self._list.selectedItems()
        if selected_items:
            for item in selected_items:
                self._list.takeItem(self._list.row(item))
            # Emit signal when services are removed
            self.services_changed.emit()

class CodeGenerationGUI(QMainWindow):
    update_terminal_signal = pyqtSignal(str)
    update_function_call_signal = pyqtSignal(FunctionCallDescription)
    show_code_signal = pyqtSignal(dict)
    update_browser_signal = pyqtSignal(object)
    update_cost_signal = pyqtSignal(dict, float)
    store_progress_signal = pyqtSignal(str, str, str, str)
    update_status_signal = pyqtSignal(str)
    update_questions_signal = pyqtSignal(dict)
    display_chat_message_signal = pyqtSignal(str, str)
    request_user_input_signal = pyqtSignal(str)
    user_input_received = pyqtSignal(str)


    def __init__(self, chat: ChatInterface):
        super().__init__()
        self.is_user_input_requested = False
        self.chat = chat
        self.default_work_items = PREDEFINED_WORK_ITEMS_FOR_FRAMEWORKS
        self.default_work_item = self.default_work_items[ApplicationType.GENERIC]
        self.figma_components = ""

        # Set default work items for each container type
        self.default_frontend_work_item = self.default_work_items[FrameworkType.REACT]
        self.default_mobile_work_item = self.default_work_items[FrameworkType.FLUTTER]
        self.default_backend_work_item = self.default_work_items[FrameworkType.FASTAPI]
        self.default_database_work_item = self.default_work_items[ApplicationType.BACKEND]
        self.project_schema: Optional[ProjectSchema] = None

        self.manifest_path = os.path.join(CodeGenerationGUI.generate_session_dir(), ".project_manifest.yaml")

        # Container-specific mappings for available frameworks
        self.container_framework_mappings = {
            'frontend': FrameworkType.get_frontend_frameworks(),
            'mobile': FrameworkType.get_mobile_frameworks(),
            'backend': FrameworkType.get_backend_frameworks(),
            'database': FrameworkType.get_database_frameworks(),
        }

        # Default platform types for containers
        self.container_platform_defaults = {
            'frontend': ApplicationType.WEB,
            'mobile': ApplicationType.MOBILE,
            'backend': ApplicationType.BACKEND,
            'database': ApplicationType.DATABASE
        }

        # keep track of which containers have already been started
        self.started_containers = set()
        # stash per‐container contexts
        self.container_contexts = {}
        # stash the last selected container
        self.current_container_type = None

        self.preview_statuses = {}  # name -> {"url": ..., "state": ..., "error": ...}

        self.progress_updates = []
        self.is_paused = False
        self.initUI()
        self.worker = None
        self.thread = None
        self.steps_data = {}
        self.tasks_data = {}
        self.control = CodeGenerationControl()
        self.previous_contexts = []
        self.past_steps = []  # Add this line to store past steps
        self.last_context = None  # Add this line to store the last context
        self.selected_task = None  # Add this line to store the selected task
        # whenever the user switches containers, update the button label

        self.reporter = pyQTReporter(self)

        # Connect signals to slots
        self.update_terminal_signal.connect(self.update_terminal)
        self.update_function_call_signal.connect(self.update_function_call)
        self.show_code_signal.connect(self.show_code)
        self.update_browser_signal.connect(self.update_browser)
        self.store_progress_signal.connect(self.store_progress_update)
        self.update_cost_signal.connect(self.update_cost_display)
        self.update_status_signal.connect(self.update_status)
        self.update_questions_signal.connect(self.update_questions)
        self.display_chat_message_signal.connect(self.display_chat_message)
        self.request_user_input_signal.connect(self.request_user_input)
        # holds metadata for each container tab, keyed by container name
        self.container_contexts = {}



    def mousePressEvent(self, event):
        super().mousePressEvent(event)
        if self.function_call_output.current_tooltip:
            tooltip_rect = self.function_call_output.current_tooltip.geometry()
            if not tooltip_rect.contains(event.globalPosition().toPoint()):
                self.function_call_output.hide_tooltip()

    def update_questions(self, questions):
        for question_id, question_data in questions.items():
            if question_data['status'] == 'pending':
                self.add_question(question_id, question_data['text'])
            elif question_data['status'] == 'answered':
                self.remove_question(question_id)

    def update_preview_display(self):
        """Display all container previews and their statuses in the UI."""
        # You may want to add a QListWidget named self.previewListWidget in your UI
        if not hasattr(self, "previewListWidget"):
            # Dynamically add a preview list widget if not present
            self.previewListWidget = QListWidget()
            # Add to your layout as appropriate, e.g.:
            # self.left_layout.addWidget(self.previewListWidget)
            # Or place it in a suitable tab/section

        self.previewListWidget.clear()
        for name, info in self.preview_statuses.items():
            url = info.get("url", "N/A")
            state = info.get("state", "unknown")
            error = info.get("error")
            item_text = f"{name}: {state} - {url if url else 'No URL'}"
            if error:
                item_text += f" (Error: {error})"
            self.previewListWidget.addItem(item_text)

    @pyqtSlot(str)
    def request_user_input(self, prompt):
        if prompt:
            self.display_chat_message("Agent", prompt)
        self.chat_input.setPlaceholderText(prompt)
        # This signals that we are blocked and waiting for user input
        self.is_user_input_requested = True

    def send_chat_message(self):
        message = self.chat_input.toMarkdown()
        prepared_message = prompt_prepare(message,"text")
        message = prompt_prepare(message,"html")
        if message:
            self.display_chat_message("User", message)
            self.chat_input.clear()
            self.chat_input.setPlaceholderText("Type your message here...")
            # This logic works as follows:
            # If user input is requested by the LLM (see callback), we send the input to the LLM
            # if not, we send the input to the worker that will process and possibly interrupt the task
            if self.is_user_input_requested:
                self.reporter.set_user_input(prepared_message)
                self.is_user_input_requested = False
            else:
                if self.worker:
                    self.worker.send_user_input(prepared_message)
                else:
                    self.status_area.append("Code generation worker not running. Cannot send user input.")
    def populate_model_selector(self):
        """Populate the model selector with available models"""
        if self.worker and hasattr(self.worker, 'agent'):
            available_models = self.worker.agent.get_available_models()
            current_model = self.worker.agent.get_selected_model()

            self.model_selector.clear()
            self.model_selector.addItems(available_models)

            # Set current model if it exists
            if current_model:
                index = self.model_selector.findText(current_model)
                if index >= 0:
                    self.model_selector.setCurrentIndex(index)

    def on_model_selected(self, index):
        """Handle model selection change"""
        if self.worker and hasattr(self.worker, 'agent'):
            selected_model = self.model_selector.currentText()
            self.worker.agent.set_model(selected_model)

    def initUI(self):
        self.setWindowTitle('Task Execution Test GUI')
        self.setGeometry(100, 100, 1600, 900)

        central_widget = QWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(central_widget)
        self.setCentralWidget(scroll_area)

        main_layout = QHBoxLayout(central_widget)

        # Left column
        left_column = QWidget()
        left_layout = QVBoxLayout(left_column)

        # Create tab widget for input
        self.input_tab_widget = QTabWidget()
        self.input_tab_widget.addTab(self.create_code_generation_tab(), "Code Generation")
        self.input_tab_widget.addTab(self.create_document_creation_tab(), "Document Creation")
        self.input_tab_widget.addTab(self.create_code_maintenance_tab(), "Code Maintenance")
        left_layout.addWidget(self.input_tab_widget)

        # Web User interface

        # Add minimize button for input panel
        self.minimize_input_button = QPushButton('Minimize Input Panel')
        self.minimize_input_button.clicked.connect(self.toggle_input_panel)
        left_layout.addWidget(self.minimize_input_button)

        # In the button_layout section of initUI method
        button_layout = QHBoxLayout()

        # Add model selector
        model_selector_layout = QHBoxLayout()
        model_selector_label = QLabel('Model:')
        self.model_selector = QComboBox()
        model_selector_layout.addWidget(model_selector_label)
        model_selector_layout.addWidget(self.model_selector)
        button_layout.addLayout(model_selector_layout)

        # Connect model selector signal
        self.model_selector.currentIndexChanged.connect(self.on_model_selected)

        # Existing buttons
        self.generate_button = QPushButton('Start Process')
        self.generate_button.clicked.connect(self.toggle_generation)
        button_layout.addWidget(self.generate_button)

        self.stop_stream_button = QPushButton('Stop Streaming')
        self.stop_stream_button.clicked.connect(self.stop_streaming)
        self.stop_stream_button.setEnabled(False)
        button_layout.addWidget(self.stop_stream_button)
        self.display_button = QPushButton('Display')
        self.display_button.clicked.connect(lambda: QDesktopServices.openUrl(QUrl('http://localhost:8089/vnc.html?autoconnect=true&resize=scale')))
        self.display_button.setEnabled(False)
        button_layout.addWidget(self.display_button)

        self.preview_manager = PreviewManager(CodeGenerationGUI.generate_session_dir())

        left_layout.addLayout(button_layout)

        # Add a button to start preview in single container mode

        self.preview_button = QPushButton('Preview')

        self.preview_button.clicked.connect(self.trigger_preview_for_current_container)
        button_layout.addWidget(self.preview_button)

        # --- Preview Buttons Section (to be dynamically filled) ---
        self.preview_buttons_layout = QHBoxLayout()
        left_layout.addLayout(self.preview_buttons_layout)
        # keep a dict of buttons if you need to reference them later
        self.preview_buttons: Dict[str, QPushButton] = {}

        self._populate_preview_buttons()

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)
        self.progress_bar.hide()
        left_layout.addWidget(self.progress_bar)


        # Right column
        right_column = QWidget()
        right_layout = QVBoxLayout(right_column)
        self.tab_widget = QTabWidget()

        web_interface_widget = QWidget()
        web_interface_layout = QVBoxLayout(web_interface_widget)
        self.web_view = QWebEngineView()
        self.web_view.setUrl(QUrl("http://localhost:5003"))
        web_interface_layout.addWidget(self.web_view)
        web_interface_layout.setContentsMargins(0, 0, 0, 0)
        web_interface_widget.setLayout(web_interface_layout)
        #self.tab_widget.addTab(web_interface_widget, "User Interface")
        left_layout.addWidget(web_interface_widget)

        # Terminal panel
        terminal_widget = QWidget()
        terminal_layout = QVBoxLayout(terminal_widget)
        self.terminal_output = QTextEdit()
        self.terminal_output.setReadOnly(True)
        self.terminal_output.setStyleSheet("background-color: black; color: white; font-family: Courier;")
        terminal_layout.addWidget(self.terminal_output)
        self.tab_widget.addTab(terminal_widget, "Terminal")


        # Browser panel
        browser_widget = QWidget()
        browser_layout = QVBoxLayout(browser_widget)
        self.browser_display = QLabel()
        self.browser_display.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.browser_display.setMinimumSize(800, 600)
        self.browser_display.setStyleSheet("background-color: white;")
        browser_layout.addWidget(self.browser_display)
        self.tab_widget.addTab(browser_widget, "Browser")

        # Function description panel
        function_call_widget = QWidget()
        function_call_layout = QVBoxLayout(function_call_widget)
        self.function_call_output = FunctionCallEditBox()
        self.function_call_output.setReadOnly(True)
        self.function_call_output.setStyleSheet("background-color: black; color: white; font-family: Courier;")
        function_call_layout.addWidget(self.function_call_output)
        self.tab_widget.addTab(function_call_widget, "Function calls")

        # Code view panel
        codeview_widget = QWidget()
        codeview_layout = QVBoxLayout(codeview_widget)
        self.codeview_output = CodeViewEditBox()
        self.codeview_output.setReadOnly(True)
        self.codeview_output.setStyleSheet("background-color: black; color: white; font-family: Courier;")
        codeview_layout.addWidget(self.codeview_output)
        self.tab_widget.addTab(codeview_widget, "Code")

        # Status panel
        self.status_area = QTextEdit()
        self.status_area.setReadOnly(True)
        self.tab_widget.addTab(self.status_area, "Status")

        # Past Steps Tab
        past_steps_widget = QWidget()
        past_steps_layout = QHBoxLayout(past_steps_widget)
        self.past_steps_list = QListWidget()
        self.past_steps_detail = QTextEdit()
        self.past_steps_detail.setReadOnly(True)
        past_steps_layout.addWidget(self.past_steps_list, 1)
        past_steps_layout.addWidget(self.past_steps_detail, 2)
        self.tab_widget.addTab(past_steps_widget, "Past Steps")

        # Task Plan Tab
        task_plan_widget = QWidget()
        task_plan_layout = QHBoxLayout(task_plan_widget)
        self.task_list = QListWidget()
        self.task_detail = QTextEdit()
        self.task_detail.setReadOnly(True)
        task_plan_layout.addWidget(self.task_list, 1)
        task_plan_layout.addWidget(self.task_detail, 2)
        self.tab_widget.addTab(task_plan_widget, "Task Plan")

        # Last Results Tab
        self.last_results_area = QTextEdit()
        self.last_results_area.setReadOnly(True)
        self.tab_widget.addTab(self.last_results_area, "Last Results")

        # LLM Costs Tab
        cost_widget = QWidget()
        cost_layout = QVBoxLayout(cost_widget)
        self.cost_display = QTableWidget()
        self.cost_display.setColumnCount(2)
        self.cost_display.setHorizontalHeaderLabels(["Agent", "Cost"])
        self.cost_display.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        cost_layout.addWidget(self.cost_display)
        self.tab_widget.addTab(cost_widget, "LLM Costs")

        # Set Terminal as the default tab
        self.tab_widget.setCurrentIndex(0)
        right_layout.addWidget(self.tab_widget)

        main_layout.addWidget(left_column, 1)
        main_layout.addWidget(right_column, 1)

        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)
        # Set the initial state of the input panel
        self.input_panel_minimized = False

    def toggle_input_panel(self):
        if self.input_panel_minimized:
            self.input_tab_widget.show()
            self.minimize_input_button.setText('Minimize Input Panel')
        else:
            self.input_tab_widget.hide()
            self.minimize_input_button.setText('Maximize Input Panel')
        self.input_panel_minimized = not self.input_panel_minimized

    def update_preview_buttons(self, container_names: List[str]):
        # remove old buttons
        for i in reversed(range(self.preview_buttons_layout.count())):
            w = self.preview_buttons_layout.itemAt(i).widget()
            if w:
                w.setParent(None)

        self.preview_buttons.clear()
        # create a new button for each container
        for name in container_names:
            btn = QPushButton(f"Preview {name}")
            btn.clicked.connect(lambda _, c=name: self.trigger_preview(c))
            self.preview_buttons_layout.addWidget(btn)
            self.preview_buttons[name] = btn

    def _populate_preview_buttons(self):
        """
        Populate the preview buttons based on the containers from the manifest file.
        """
        session_dir = self.generate_session_dir()
        # check if the session directory has a manifest file
        if os.path.exists(self.manifest_path):

            with open(self.manifest_path, 'r') as f:
                manifest_data = yaml.safe_load(f)

                containers = manifest_data.get("containers", [])
                container_names = [container.get("container_name") for container in containers if container.get("container_name")]

                self.update_preview_buttons(container_names)

    def trigger_preview_for_current_container(self):
        project_work_item = getattr(self.worker, 'work_item', {})
        containers = project_work_item.get('containers', {})
        container_name = containers[0].get('container_name')
        return self.trigger_preview(container_name)

    def trigger_preview(self, container_name):
        """
        Trigger the preview for the specified container.
        """

        container = self.preview_manager.get_container_by_name(container_name)
        if not container:
            QMessageBox.warning(self, "Preview Error", f"No such container registered: {container_name}")
            return
        result = self.preview_manager.run_container(container.container_name, run_dependencies=False, restart = True)
        self.status_area.append(f"Triggered preview for {container.container_name}: {result.get('message', '')}")

    def open_selected_preview(self):
        selected_items = self.previewListWidget.selectedItems()
        if not selected_items:
            return
        selected_text = selected_items[0].text()
        container = selected_text.split(":")[0]
        url = self.preview_statuses.get(container, {}).get("url")
        if url and url != "N/A":
            QDesktopServices.openUrl(QUrl(url))


    def get_platform_index_in_combobox(self, platform_type: ApplicationType, combo: QComboBox) -> int:
        for i in range(combo.count()):
            if combo.itemData(i) == platform_type:
                return i
        return 0

    def create_platform_dropdown(self, label: str, on_change_callback=None) -> QWidget:
        dropdown = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel(label))
        platform_selector = QComboBox()
        for platform_type in ApplicationType:
            if platform_type is not ApplicationType.GENERIC:
                platform_selector.addItem(platform_type.name, platform_type)
        if platform_selector.count() > 0:
            platform_selector.setCurrentIndex(
                self.get_platform_index_in_combobox(
                    ApplicationType.GENERIC, platform_selector))
        if on_change_callback:
            platform_selector.currentIndexChanged.connect(on_change_callback)
        layout.addWidget(platform_selector)
        dropdown.setLayout(layout)
        return dropdown

    def create_framework_dropdown(self, label: str, on_change_callback=None) -> QWidget:
        dropdown = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel(label))
        framework_selector = QComboBox()
        layout.addWidget(framework_selector)
        dropdown.setLayout(layout)
        framework_selector.currentIndexChanged.connect(on_change_callback)
        return dropdown

    def update_default_value_input_field(self, widget: QWidget, new_value: str):
        if type(new_value) != str: return

        input_box = widget.findChild(UserInputBox)
        if input_box:
            input_box.setPlainText(new_value)
            return

        line_edit = widget.findChild(QLineEdit)
        if line_edit:
            line_edit.setText(new_value)
            return



    def update_framework_options(self, platform_type: ApplicationType):
        framework_combo = self.framework.findChild(QComboBox)
        framework_combo.blockSignals(True)
        framework_combo.clear()
        frameworks = FrameworkType.get_frameworks_for_platform(platform_type)
        for framework in frameworks:
            framework_combo.addItem(framework.value, framework)
        if framework_combo.count() > 0:
            framework_combo.setCurrentIndex(0)
        framework_combo.blockSignals(False)

    def create_container_tabs(self):
        container_tab_widget = QTabWidget()
        overview_tab = self.create_overview_tab()

        # Only add the overview tab initially
        container_tab_widget.addTab(overview_tab, "Overview")

        # Store references for later use
        self._container_tab_widgets = {
            'overview': overview_tab,
        }

        return container_tab_widget


    def create_overview_tab(self):
        tab = QWidget()
        main_layout = QVBoxLayout(tab)
        main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # # Add predefined work items
        # self.predefined_projects = QComboBox()
        # self.predefined_projects.addItem("Select pre-defined project")

        # # Use PREDEFINED_WORK_ITEMS_FOR_FRAMEWORKS instead of PREDEFINED_WORK_ITEMS
        # for framework_type, work_item in PREDEFINED_WORK_ITEMS_FOR_FRAMEWORKS.items():
        #     display_name = work_item.get('component_name', framework_type.value)
        #     self.predefined_projects.addItem(display_name, framework_type)  # Store framework_type as data

        # predefined_field = self.create_input_field("Pre-defined Project:", widget=self.predefined_projects)

        # Project Name
        self.overview_project_name_box = UserInputBox()
        project_name_field = self.create_input_field("Project Name:", widget=self.overview_project_name_box)

        # Project Description
        self.overview_description_box = UserInputBox()
        desc_field = self.create_input_field("Project Description:", widget=self.overview_description_box)

        # Frontend Framework
        self.frontend_framework_combo = QComboBox()
        self.frontend_framework_combo.addItems(self._get_frameworks_list(FrameworkType.get_frontend_frameworks))
        frontend_field = self.create_input_field("Frontend Framework:", widget=self.frontend_framework_combo)

        # Mobile Framework
        self.mobile_framework_combo = QComboBox()
        self.mobile_framework_combo.addItems(self._get_frameworks_list(FrameworkType.get_mobile_frameworks))
        mobile_field = self.create_input_field("Mobile Framework:", widget=self.mobile_framework_combo)

        # Backend Framework
        self.backend_framework_combo = QComboBox()
        self.backend_framework_combo.addItems(self._get_frameworks_list(FrameworkType.get_backend_frameworks))
        backend_field = self.create_input_field("Backend Framework:", widget=self.backend_framework_combo)

        # Database Framework
        self.database_framework_combo = QComboBox()
        self.database_framework_combo.addItems(self._get_frameworks_list(FrameworkType.get_database_frameworks))
        database_field = self.create_input_field("Database Framework:", widget=self.database_framework_combo)

        self.third_party_widget = ThirdPartyServicesWidget()
        third_party_field = self.create_input_field(None, widget=self.third_party_widget)

        # Add all fields to the main layout
        for field in [project_name_field, desc_field, frontend_field, mobile_field, backend_field, database_field, third_party_field]:
            main_layout.addWidget(field)

        # Button layout for Create and Generate Project buttons
        button_layout = QHBoxLayout()

        # Create Button - this will trigger tab reorganization
        self.create_project_button = QPushButton("Create")
        self.create_project_button.clicked.connect(self.handle_create_project)
        button_layout.addWidget(self.create_project_button)

        # Generate Project Button
        self.generate_project_button = QPushButton("Generate Project")
        self.generate_project_button.clicked.connect(self.handle_generate_project)
        self.generate_project_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.generate_project_button)

        main_layout.addLayout(button_layout)

        # Store references for later updates
        self.overview_fields = {
            "project_name": self.overview_project_name_box,
            "description": self.overview_description_box,
            "frontend_framework": self.frontend_framework_combo,
            "backend_framework": self.backend_framework_combo,
            "database_framework": self.database_framework_combo
        }
        tab.setLayout(main_layout)
        return tab
    
    def _get_frameworks_list(self, get_frameworks_method):
            frameworks = [FrameworkType.map_to_known_labels(framework.value) for framework in get_frameworks_method()]
            frameworks.insert(0, "Not selected")
            frameworks.append("None")
            return frameworks

    def handle_create_project(self):
        """Handle project creation - simplified version"""
        overview_data = {
            "project_name": self.overview_project_name_box.toPlainText(),
            "description": self.overview_description_box.toPlainText(),
            "frontend_framework": self.frontend_framework_combo.currentText(),
            "mobile_framework": self.mobile_framework_combo.currentText(),
            "backend_framework": self.backend_framework_combo.currentText(),
            "database_framework": self.database_framework_combo.currentText(),
            "third_party_services": self.third_party_widget.services()  # Add this line
        }

        # Create project schema - this may modify/enhance the overview_data
        project_schema = asyncio.run(create_project_welcome_page(overview_data))

        
        # Set workspace for the containers
        for container in project_schema.containers:
            name: str = container.container_name
            name.replace(" ", "_")
            container.workspace = name.replace(" ", "_") + "_workspace"
            container.container_root = f"{container.workspace}/{name.replace(' ', '_')}"

        # Mandatory port configuration. Without this the preview manager will crash
        # All services need to have a port
        # Set backend port
        bcs = project_schema.get_containers_by_type(ContainerType.BACKEND)

        if (bcs):
            bcs[0].port = config.get("CONTAINER", "default_backend_port")
            bcs[0].apiSpec = "openapi.json"  # Set default API spec for backend

        # Set database port
        dtbs = project_schema.get_containers_by_type(ContainerType.DATABASE)

        if (dtbs):
            dtbs[0].port = config.get("CONTAINER", "default_database_port")

        # Set frontend port
        fcs = project_schema.get_containers_by_type(ContainerType.FRONTEND)
        if (fcs):
            fcs[0].port = config.get("CONTAINER", "default_frontend_port")

        
        if "Supabase" in project_schema.overview.third_party_services:
            # Set Supabase environment variables
            env_vars = {
                'SUPABASE_URL': os.environ.get('SUPABASE_URL', ''),
                'SUPABASE_KEY': os.environ.get('SUPABASE_KEY', ''),
                'SITE_URL': os.getenv("HOST", "0.0.0.0")
            }
            project_schema.overview.env.update(env_vars)

        # Saving the manifest after we did some config update
        project_schema.save_to_manifest(self.manifest_path)
        services = project_schema.overview.third_party_services
        self.third_party_widget.set_services(services)
        # Store schema and let IT take preference - sync schema data back to UI
        self.project_schema = project_schema
        sync_project_schema_to_overview_fields(self.overview_fields, project_schema)
        add_container_tabs_to_tabwidget(self.container_tabs, project_schema.containers)

        # Keep in focus the main tab
        self.container_tabs.setCurrentIndex(0)

        # maintain container names list for preview buttons
        container_names = [container.container_name for container in project_schema.containers]
        self.update_preview_buttons(container_names)

        # Now connect change handlers for future edits
        connect_overview_change_handlers(self.overview_fields, project_schema, self.manifest_path)


    def handle_generate_project(self):
        """Handle project-wide code generation from the overview panel"""
        # Validate required fields
        project_name = self.overview_project_name_box.toPlainText().strip()
        if not project_name:
            QMessageBox.warning(self, "Validation Error", "Project name is required.")
            return

        # Check if any generation is already running
        if self.worker is not None:
            QMessageBox.warning(self, "Generation in Progress",
                            "Code generation is already running. Please wait for it to complete.")
            return

        # Start project-wide generation using existing start_process method
        self.start_process(is_project_generation=True)



    def update_start_button_text(self):
        """Update start button text based on current container state"""
        current_container : Container = self.get_current_container()
        if current_container:
            container_type = current_container.container_type.value
            status = current_container.private.code_generation_status

            if status == CodeGenerationStatus.IN_PROGRESS:
                self.generate_button.setText('Stop Session')
            elif status == CodeGenerationStatus.SUCCESS:
                self.generate_button.setText('Restart Session')
            else:
                self.generate_button.setText('Start Process')
        else:
            self.generate_button.setText('Start Process')

    def create_project_work_item(self):
        """Create a comprehensive work item for project-wide generation (multi-container format)"""
        # Get overview data

        # active_container : Container = get_active_tab_container(self.tab_widget)

        overview_data = {
            "project_name": self.project_schema.overview.project_name,
            "description": self.project_schema.overview.description,

        }

        frontends = self.project_schema.get_containers_by_type(ContainerType.FRONTEND)
        if frontends:
            overview_data["frontend_framework"] = frontends[0].framework

        backends = self.project_schema.get_containers_by_type(ContainerType.BACKEND)
        if backends:
            overview_data["backend_framework"] = backends[0].framework

        databases = self.project_schema.get_containers_by_type(ContainerType.DATABASE)
        if databases:
            overview_data["database_framework"] = databases[0].framework

        mobiles = self.project_schema.get_containers_by_type(ContainerType.MOBILE)
        if mobiles:
            overview_data["mobile_framework"] = mobiles[0].framework

        services = self.third_party_widget.services()

        third_party_services = []
        for service in services:
            if service == "Supabase":
                entry = {
                    "service_name": "Supabase",
                    "project_name": overview_data['project_name'],
                    }
                third_party_services.append(entry)

        # Add back to the project schema the chanages in the GUI
        self.project_schema.overview.third_party_services = services


        # Only add env vars that are actually set
        env_vars = {
            'APP_SUPABASE_URL': os.environ.get('APP_SUPABASE_URL'),
            'APP_SUPABASE_KEY': os.environ.get('APP_SUPABASE_KEY'),
            'APP_SUPABASE_DB_URL': os.environ.get('APP_SUPABASE_DB_URL')
        }

        # Filter out None values (missing env vars)
        env_vars = {k: v for k, v in env_vars.items() if v is not None}

        self.project_schema.overview.env.update(env_vars)

        # Also add to all containers
        for container in self.project_schema.containers:
            container.env.update(self.project_schema.overview.env)

        self.project_schema.save_to_manifest(self.manifest_path)

        all_containers = self.get_containers_in_work_item_format(self.project_schema.containers)

        is_multi_container = len(all_containers) > 1

        # Create the comprehensive work item (MULTI-CONTAINER FORMAT)
        project_work_item = {
            'project_type': 'multi_container' if is_multi_container else 'single_container',
            'project_name': overview_data['project_name'],
            'description': overview_data['description'],
            'containers': all_containers,
            'overview': overview_data,
            '3rd_party_services': third_party_services,
            'figma_components': self.figma_components,
            'manifest_path': self.manifest_path,
            #'backend_implementations': self.backend_implementations if hasattr(self, 'backend_implementations') else [],
        }

        return project_work_item

    def create_project_work_item_from_manifest(self, manifest_path):
        """Create a comprehensive work item populated from the manifest."""
        # Load manifest file
        with open(manifest_path, 'r') as f:
            manifest = yaml.safe_load(f)

        # Overview mapping
        overview_data = {
            'project_name': manifest.get('appName', ''),
            'description': manifest.get('description', ''),
            'frontend_framework': '',
            'mobile_framework': '',
            'backend_framework': '',
            'database_framework': ''
        }
        # Populate framework fields from containers
        for c in manifest.get('containers', []):
            ctype = c.get('container_type', '')
            framework = c.get('framework', '')
            overview_data[f'{ctype}_framework'] = framework

        # Containers list (use as-is from manifest)
        containers = manifest.get('containers', [])

        is_multi_container = len(containers) > 1

        # Build the work item
        project_work_item = {
            'project_type': 'multi_container' if is_multi_container else 'single_container',
            'project_name': overview_data['project_name'],
            'description': overview_data['description'],
            'containers': containers,
            'overview': overview_data,
            'manifest_path': manifest_path,
        }
        return project_work_item


    def is_container_configured(self, container_type):
        """Check if a container has meaningful configuration"""
        if container_type == 'frontend':
            return self.frontend_framework_combo.currentText() not in ["Not Selected", "None"]
        elif container_type == 'backend':
            return self.backend_framework_combo.currentText() not in ["Not Selected", "None"]
        elif container_type == 'database':
            return self.database_framework_combo.currentText() not in ["Not Selected", "None"]
        elif container_type == 'mobile':
            return self.mobile_framework_combo.currentText() not in ["Not Selected", "None"]
        return False



    def create_container_specific_tab(self, container_type):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Get default work item for this container type
        default_work_item = self.get_container_default_work_item(container_type)

        # Create the fields with default values
        container_name = self.create_input_field(
            f"{container_type} Container Name:",
            default_text = default_work_item.container_name
        )

        platform = self.create_container_platform_dropdown(
            container_type,
            f"{container_type} Platform Type:",
            lambda: self.platform_changed_container(container_type)
        )

        framework = self.create_container_framework_dropdown(
            container_type,
            f"{container_type} Framework:",
            lambda: self.framework_changed_container(container_type)
        )

        description = self.create_input_field(
            f"{container_type} Description:",
            widget=UserInputBox(),
            default_text=default_work_item.description
        )

        interfaces = self.create_input_field(
            f"{container_type} Interfaces:",
                widget=UserInputBox(),
            default_text=default_work_item.interfaces
        )

        if getattr(default_work_item, "container_details", None):
            container_details = self.create_input_field(
                f"{container_type} Container:",
                widget=UserInputBox(),
                default_text=json.dumps(dataclass_to_llm_schema(default_work_item.container_details))
            )
        else:
            container_details = None

        # Add fields to layout
        for widget in [container_name, platform, framework, description, interfaces, container_details]:
            layout.addWidget(widget)

        # Store references to container-specific fields
        setattr(self, f"{container_type.lower()}_fields", {
            'container_name': container_name,
            'platform': platform,
            'framework': framework,
            'description': description,
            'interfaces': interfaces,
            'container_details': container_details
        })



        return tab, default_work_item

    def get_container_default_work_item(self, container_type):
        """Get the default work item for a specific container type as proper dataclass."""
        container_type = container_type.lower()

        # Get the old dictionary format
        if container_type == 'frontend':
            old_work_item = self.default_frontend_work_item
        elif container_type == 'backend':
            old_work_item = self.default_backend_work_item
        elif container_type == 'database':
            old_work_item = self.default_database_work_item
        elif container_type == 'mobile':
            old_work_item = self.default_mobile_work_item
        else:
            old_work_item = self.default_work_items[ApplicationType.GENERIC]

        # Convert to new dataclass format
        return convert_work_item_to_container_config(old_work_item, container_type)

    def create_container_platform_dropdown(self, container_type, label, on_change_callback):
        """Create a platform dropdown specific to a container type"""
        dropdown = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel(label))
        platform_selector = QComboBox()

        # Add only relevant platform types for this container
        default_platform = self.container_platform_defaults[container_type.lower()]
        platform_selector.addItem(default_platform.name, default_platform)

        if on_change_callback:
            platform_selector.currentIndexChanged.connect(on_change_callback)

        layout.addWidget(platform_selector)
        dropdown.setLayout(layout)
        return dropdown

    def create_container_framework_dropdown(self, container_type, label, on_change_callback):
        """Create a framework dropdown specific to a container type"""
        dropdown = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel(label))
        framework_selector = QComboBox()

        # Add frameworks specific to this container type
        available_frameworks = self.container_framework_mappings[container_type.lower()]
        for framework in available_frameworks:
            framework_selector.addItem(framework.value, framework)

        if on_change_callback:
            framework_selector.currentIndexChanged.connect(on_change_callback)

        layout.addWidget(framework_selector)
        dropdown.setLayout(layout)
        return dropdown

    def framework_changed_container(self, container_type):
        """Handle framework change for a specific container"""
        container_type = container_type.lower()
        ctx = self.container_contexts.get(container_type, {})

        fields = getattr(self, f"{container_type}_fields")
        framework_combo = fields['framework'].findChild(QComboBox)
        new_framework = framework_combo.currentData()

        if not ctx.get('_initialized', False):
            new_work_item = self.default_work_items.get(new_framework,
                                                self.get_container_default_work_item(container_type))

        else:
            # else we've got real welcome-page data in ctx, so just update its framework
            new_work_item = ctx

        # 3) update the UI _and_ re-stash
        self.set_container_data(container_type, new_work_item)
        #self.set_container_work_item(container_type, new_work_item)
        self.container_contexts[container_type] = ctx


    def create_code_generation_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Add container tabs
        self.container_tabs = self.create_container_tabs()
        self.container_tabs.currentChanged.connect(self.update_start_button_text)
        layout.addWidget(self.container_tabs)

        return tab

    def get_containers_in_work_item_format(self, containers: List['Container']) -> List[dict[str, Any]]:
        """
        Convert a list of containers to the list format expected by work items.
        """
        result = []
        for container in containers:
            result.append(container.to_work_item_format())
        return result

    def set_container_data(self, container_type, data):
        fields = getattr(self, f"{container_type}_fields")

        # Update the fields with the provided data
        self.update_default_value_input_field(fields['container_name'],
                                            data.get('container_name', '') or data.get('component_name', ''))

        platform_combo = fields['platform'].findChild(QComboBox)
        framework_combo = fields['framework'].findChild(QComboBox)

        # Set platform and framework
        if 'platform' in data:
            platform_type = ApplicationType.from_string(data['platform'])
            index = self.get_platform_index_in_combobox(platform_type, platform_combo)
            platform_combo.setCurrentIndex(index)

        if 'framework' in data:
            framework_combo.setCurrentText(data['framework'])

        # Update description and interfaces
        self.update_default_value_input_field(fields['description'],
                                            data.get('description', ''))
        self.update_default_value_input_field(fields['interfaces'],
                                            data.get('interfaces', ''))
        self.update_default_value_input_field(fields['container_details'],
                                            json.dumps(data.get('container_details', {})))

    def create_document_creation_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)


        return tab


    def create_code_maintenance_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        return tab


    def create_question_area(self):
        question_widget = QWidget()
        question_layout = QVBoxLayout(question_widget)

        self.question_tabs = QTabWidget()
        question_layout.addWidget(self.question_tabs)

        self.submit_button = QPushButton("Submit this answer")
        self.submit_button.clicked.connect(self.submit_answers)
        question_layout.addWidget(self.submit_button)

        self.questions = {}  # Store questions and answers

        return question_widget

    def add_question(self, question_id, question_text):
        if question_id not in self.questions or self.questions[question_id]['status'] == 'removed':
            self.questions[question_id] = {
                "text": question_text,
                "status": "pending",
                "answer": ""
            }

            tab = QWidget()
            tab_layout = QVBoxLayout(tab)

            question_display = QTextEdit()
            question_display.setPlainText(question_text)
            question_display.setReadOnly(True)
            tab_layout.addWidget(question_display)

            answer_input = QTextEdit()
            answer_input.setPlaceholderText("Enter your answer here...")
            tab_layout.addWidget(answer_input)

            self.question_tabs.addTab(tab, f"Question {question_id}")

            # Store references to the widgets
            self.questions[question_id]["widgets"] = {
                "tab": tab,
                "question_display": question_display,
                "answer_input": answer_input
            }

    def remove_question(self, question_id):
        if question_id in self.questions and self.questions[question_id]['status'] != 'removed':
            # Remove the tab
            tab_index = self.question_tabs.indexOf(self.questions[question_id]["widgets"]["tab"])
            if tab_index != -1:
                self.question_tabs.removeTab(tab_index)

            self.questions[question_id]['status'] = 'removed'

    def submit_answers(self):
        answers = {}
        for question_id, question_data in self.questions.items():
            answer = question_data["widgets"]["answer_input"].toMarkdown()
            if answer.strip():  # Only include non-empty answers
                answers[question_id] = {
                    "text": question_data["text"],
                    "status": "answered",
                    "answer": answer
                }

        if answers:
            self.worker.send_user_input(answers)

    def toggle_generation(self):
        """Handle start/stop of container-specific or project-wide code generation"""
        if self.worker is None:
            # constants for readability
            CODE_GEN_TAB = 0
            OVERVIEW_SUBTAB = 0
            # 1) If we're on Code-Generation *and* its Overview sub-tab, block here
            if (self.input_tab_widget.currentIndex() == CODE_GEN_TAB and
                self.container_tabs.currentIndex() == OVERVIEW_SUBTAB):
                QMessageBox.information(
                    self, "Project Generation",
                    "For project-wide generation, please use the 'Generate Project' button in the overview tab."
                )
                return

            # 2) Otherwise, start the right agent:
            if self.input_tab_widget.currentIndex() == CODE_GEN_TAB:
                # Code Generation (but *not* Overview): must pick a container
                current_container = self.get_current_container()

                if current_container:
                    self.start_process(container_type=current_container.container_type)
                else:
                    QMessageBox.warning(self, "Error", "Please select a container tab first.")
            else:
                # Doc-creation or Code-maintenance (no containers): fire off TaskExecutionAgent directly
                self.start_process()
        else:
            self.stop_process()

    def stop_process(self):
        """Stop the current container's or project's process"""
        if self.worker:
            self.control.stop()
            try:
                self.worker.agent.stop()
            except Exception as e:
                print(f"Error stopping agent {e}")

        # Handle different generation types
        if self.current_container_type == "project":
            self.generate_project_button.setText('Generate Project')
            self.generate_project_button.setEnabled(True)
            self.status_area.append("Project generation stopped.")
        else:
            self.generate_button.setText('Restart Session')
            self.status_area.append("Process stopped.")

        self.stop_stream_button.setEnabled(False)
        self.progress_bar.hide()

        # Restore the input panel
        self.input_tab_widget.setEnabled(True)
        if self.input_panel_minimized:
            self.toggle_input_panel()



    def update_past_steps(self):
        self.past_steps_list.clear()
        self.steps_data.clear()
        for step in self.past_steps:
            self.past_steps_list.addItem(step['title'])
            self.steps_data[step['title']] = step['context']
        self.past_steps_list.itemClicked.connect(self.show_step_detail)

    def update_task_list(self, tasks):
        self.task_list.clear()
        self.tasks_data.clear()
        for task in tasks:
            if task.get('task', ''):
                self.task_list.addItem(task['task'])
                self.tasks_data[task['task']] = task
            if task.get('step', ''):
                self.task_list.addItem(task['step'])
                self.tasks_data[task['step']] = task
        self.task_list.itemClicked.connect(self.show_task_detail)

    @staticmethod
    def create_input_field(label, widget=None, default_text="", validator=None):
        container = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel(label))
        if widget is None:
            widget = QLineEdit()
            if validator:
                widget.setValidator(validator)
        if isinstance(widget, QLineEdit):
            widget.setText(default_text)
        elif isinstance(widget, QTextEdit):
            widget.setFixedHeight(40)
            widget.append( prompt_prepare(f"{default_text}"))
        layout.addWidget(widget)
        container.setLayout(layout)
        return container

    def show_step_detail(self, item):
        step_title = item.text()
        step_request_context = self.steps_data.get(step_title, {})

        step_data = step_request_context.get('last_step', {})

        detail_text = f"Step: {step_title}\n\n"
        detail_text += f"Agent: {step_data.get('micro_agent', 'N/A')}\n"
        detail_text += f"Action: {step_data.get('action', 'N/A')}\n\n"
        detail_text += f"Details:\n{json.dumps(step_data.get('request_details', 'No details available.'), indent=4)}\n\n"

        if 'last_result' in step_request_context:
            detail_text += f"Result:\n{step_request_context['last_result']}\n"

        self.past_steps_detail.setPlainText(detail_text)

        self.selected_task = step_title
        self.last_context = json.dumps(step_request_context)

        # Add a "Restart from here" button only if code generation has stopped
        if self.worker is None:
            self.generate_button.setText('Restart Code Generation')

    def restart_from_step(self, step_title):
        try:
            step_data = self.steps_data[step_title]
            self.start_code_generation(previous_context=json.dumps(step_data))
        except KeyError:
            print(f"Step not found: {step_title}")

    def show_task_detail(self, item):
        task_title = item.text()
        task_data = self.tasks_data.get(task_title, {})

        detail_text = f"Task: {task_title}\n\n"
        detail_text += f"Status: {task_data.get('status', 'N/A')}\n"
        detail_text += f"Agent: {task_data.get('micro_agent', 'N/A')}\n"
        detail_text += f"File to Modify: {task_data.get('file_to_modify', 'N/A')}\n\n"
        detail_text += f"Details:\n{task_data.get('details', 'No details available.')}\n"

        if 'estimated_complexity' in task_data:
            detail_text += f"\nEstimated Complexity: {task_data['estimated_complexity']}\n"

        self.task_detail.setPlainText(detail_text)

    @pyqtSlot(str)
    def update_terminal(self, output):
        cursor = self.terminal_output.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        cursor.insertText(output)
        self.terminal_output.setTextCursor(cursor)
        self.terminal_output.ensureCursorVisible()

    @pyqtSlot(dict)
    def show_code(self, code_data):
        if isinstance(code_data, dict):
            self.codeview_output.show_code(code_data.get('filename'), code_data.get('content'), code_data.get('operation'), code_data.get('color_info'))
        self.codeview_output.update()

    @pyqtSlot(FunctionCallDescription)
    def update_function_call(self, output: FunctionCallDescription):
        scrollbar = self.function_call_output.verticalScrollBar()
        current_scroll = scrollbar.value()
        max_scroll = scrollbar.maximum()

        # Check if we're at the bottom before updating
        at_bottom = current_scroll == max_scroll

        cursor = self.function_call_output.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)

        if not self.function_call_output.toPlainText().strip():
            separator = ""
        else:
            separator = '<hr style="border: 0; height: 1px; background: #555; margin: 10px 0;">'

        # Extract the first 40 characters of the first argument
        first_arg_preview = ""
        if output.arguments:
            first_arg = next(iter(output.arguments.values()))
            if isinstance(first_arg, str):
                first_arg_preview = first_arg[:40]
                if len(first_arg) > 40:
                    first_arg_preview += "..."
            else:
                first_arg_preview = str(first_arg)[:40]
                if len(str(first_arg)) > 40:
                    first_arg_preview += "..."
            if len(output.arguments) > 1:
                first_arg_preview += ", ..."

        function_display = f"{output.function_name} ({first_arg_preview})" if first_arg_preview else output.function_name

        html_template = """
        {separator}
        <div style="margin-bottom: 10px; border: 1px solid #555; padding: 10px; border-radius: 5px; background-color: #1e1e1e;">
            <h4 style="color: #e0e0e0; margin: 0 0 10px 0;">Call: <span style="color: #61afef;">{function_name}</span></h3>
            <p style="margin: 0 0 10px 0;"><strong style="color: #e0e0e0;">Reason:</strong><span style="color: #98c379;">{reason}</span></p>
            <p style="margin: 0 0 10px 0;"><strong style="color: #e0e0e0;">Observations:</strong><span style="color: #e5c07b;">{observations}</span></p>
        </div>
        """

        formatted_html = html_template.format(
            separator=separator,
            function_name=function_display,
            reason=output.reason if output.reason else "N/A",
            observations=output.observations if output.observations else "N/A",
        )

        # Store function call information for tooltip
        self.function_call_output.append_function_call(
            output.function_name,
            output.reason,
            output.observations,
            output.root_cause_analysis,
            output.arguments
        )

        cursor.insertHtml(formatted_html)
        self.function_call_output.setTextCursor(cursor)
        #self.function_call_output.ensureCursorVisible()

        self.function_call_output.update_last_call_end_pos()

        if at_bottom:
            self.function_call_output.verticalScrollBar().setValue(
                self.function_call_output.verticalScrollBar().maximum()
            )
        else:
            self.function_call_output.verticalScrollBar().setValue(current_scroll)

        self.function_call_output.update()

    @pyqtSlot(dict, float)
    def update_cost_display(self, agent_costs, total_cost):
        self.cost_display.setRowCount(len(agent_costs) + 1)  # +1 for total cost

        for row, (agent, cost) in enumerate(agent_costs.items()):
            self.cost_display.setItem(row, 0, QTableWidgetItem(agent))
            self.cost_display.setItem(row, 1, QTableWidgetItem(f"${cost:.6f}"))

        # Add total cost as the last row
        total_row = len(agent_costs)
        self.cost_display.setItem(total_row, 0, QTableWidgetItem("Total"))
        self.cost_display.setItem(total_row, 1, QTableWidgetItem(f"${total_cost:.6f}"))

        # Adjust column widths
        self.cost_display.resizeColumnsToContents()

        # Ensure the widget is wide enough to show all content
        total_width = sum(self.cost_display.columnWidth(i) for i in range(self.cost_display.columnCount()))
        self.cost_display.setMinimumWidth(total_width)

    @pyqtSlot(object)
    def update_browser(self, image_data):
        try:
            if isinstance(image_data, str):
                # Assume it's a base64 encoded string
                image_bytes = base64.b64decode(image_data)
            elif isinstance(image_data, bytes):
                # It's already in bytes format
                image_bytes = image_data
            else:
                raise ValueError(f"Unsupported image data type: {type(image_data)}")

            qimage = QImage.fromData(QByteArray(image_bytes))

            if qimage.isNull():
                raise ValueError("Failed to create valid QImage")

            pixmap = QPixmap.fromImage(qimage)

            self.browser_display.setPixmap(pixmap.scaled(
                self.browser_display.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))
        except Exception as e:
            error_message = f"Error updating browser display: {str(e)}\n"
            error_message += f"Error type: {type(e)}\n"
            error_message += f"Image data type: {type(image_data)}\n"
            print(error_message)

            self.browser_display.setText(error_message)

    def emit_update_terminal(self, output):
        self.update_terminal_signal.emit(output)

    def emit_update_cost(self, agent_costs, total_cost):
        self.update_cost_signal.emit(agent_costs, total_cost)

    def emit_update_browser(self, image):
        self.update_browser_signal.emit(image)
    def _init_agent(self, agent_type: str,
                    llm, session_dir: str, micro_agent_config: dict,
                    model_name: str, prompts_base_path: str, container_type : str,
                    reset_context=False,
                    work_item=None):

        if 'global_context_history' not in globals():
            globals()['global_context_history'] = {}

        global_context_history = globals()['global_context_history']

        context_history = global_context_history.get(container_type, "")

        project_type = work_item.get("project_type", "multi_container")
        platform_from_work_item = ApplicationType.GENERIC.value
        framework_from_work_item = FrameworkType.DEFAULT.value

        if project_type == "single_container":
            container = work_item.get("containers", [{}])[0]
            # For single container, use the platform and framework from the container
            platform_from_work_item = container.get("platform", container.get("container_type", ApplicationType.GENERIC.value))
            framework_from_work_item = container.get("framework", FrameworkType.DEFAULT.value)

        """Initialize the TaskExecutionAgent and ProcessWorker"""
        # Add backend info to the agent initialization log
        agent = TaskExecutionAgent(llm,
                                    execution_base_path=session_dir,
                                    micro_agents_config=micro_agent_config,
                                    model_name=model_name,
                                    prompts_base_path=prompts_base_path,
                                    platform=platform_from_work_item.lower(),
                                    framework=framework_from_work_item.lower(),
                                    )
        self.control.reset()
        self.worker = ProcessWorker(agent,
                                        agent_type=agent_type,
                                        control=self.control,
                                        reporter=self.reporter,
                                        work_item=work_item,
                                        reset_context=False,
                                        chat=self.chat,
                                        container_type=container_type
                                        )

    def _rotat_logs(self, session_dir):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

        forced_log_dir = config.get("SYSTEM","config_dir")
        if forced_log_dir:
            # Export last dir from session dir
            last_dir = os.path.basename(os.path.normpath(session_dir))
            log_dir = os.path.join(forced_log_dir, last_dir, "logs")
        else:
            log_dir = session_dir + "/logs"

        self.make_dir_p(log_dir)
        for f in os.listdir(log_dir):
            if f.endswith(".log"):
                os.rename(os.path.join(log_dir, f), os.path.join(log_dir, f"{f}-{timestamp}"))

    def start_process(self, container_type = None, is_project_generation=False):
        current_tab_index = self.input_tab_widget.currentIndex()
        if (self.project_schema):
            self.project_schema.save_to_manifest(self.manifest_path)

        if is_project_generation:  # Project generation from overview panel
            # Project-wide generation - use multi-container format
            work_item = self.create_project_work_item()
            agent_type = "CodeGeneration"

            # Update UI for project generation
            self.generate_project_button.setEnabled(False)
            self.generate_project_button.setText("Generating Project...")

        elif current_tab_index == 0:  # Code Generation tab
            # Check if we're on overview tab within container tabs
            current_container_tab_index = self.container_tabs.currentIndex()

            if current_container_tab_index == 0:  # Overview tab
                # This should redirect to project generation
                QMessageBox.information(self, "Info", "Use 'Generate Project' button for project-wide generation.")
                return

            current_container = self.get_current_container()

            if not current_container:
                QMessageBox.warning(self, "Error", "Please select a container tab first.")
                return

            containers = self.get_containers_in_work_item_format([current_container])

            # Create the work item for the current container (ORIGINAL FORMAT)
            work_item = {
                "containers"  : containers,
                "description": current_container.description,
                "interfaces": current_container.interfaces,
                'figma_components': self.figma_components,
                'container_type': current_container.container_type.value,
            }

            agent_type = "CodeGeneration"

        elif current_tab_index == 1:  # Document Creation
            work_item = {
                "description": "Create or update documentation for a project",
            }
            agent_type = "DocumentCreation"

        elif current_tab_index == 2:  # Code Maintenance
            agent_type = "CodeMaintenance"
            # check to make sure that manifest file exists in the file system
            if self.manifest_path and not os.path.exists(self.manifest_path):
                self.manifest_path = None

            if self.manifest_path:
                work_item = self.create_project_work_item_from_manifest(self.manifest_path)
                work_item['description'] = "Create or update code or documentation for a project based on manifest"
                work_item['manifest_path'] = self.manifest_path
            else:
                work_item = {
                    "description": "Create or update code or documentation for a project",
                }
        else:
            agent_type = "CodeMaintenance"
            if self.manifest_path:
                work_item = self.create_project_work_item_from_manifest(self.manifest_path)
                work_item['description'] = "Create or update code or documentation for a project based on manifest"
                work_item['manifest_path'] = self.manifest_path
            else:
                work_item = {
                    "description": "Create or update code or documentation for a project",
                }
        # Common UI updates (only if not already updated for project generation)
        if not is_project_generation:
            self.generate_button.setEnabled(False)

        self.stop_stream_button.setEnabled(True)
        self.progress_bar.show()
        self.input_tab_widget.setEnabled(False)
        self.display_button.setEnabled(True)
        self.status_area.clear()
        self.last_results_area.clear()
        self.clear_past_steps()
        self.clear_task_list()

        OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

        if os.getenv("MODEL_NAME"):
            console.print("⚠️ [bold yellow]Warning:[/bold yellow] MODEL_NAME environment variable is deprecated."
                        " Use the LLM 'model' key in the config.ini file instead.", style="yellow")

        model_name = os.getenv("MODEL_NAME", config.get("LLM", "model"))

        # Use session root for project generation, container-specific for others
        #session_dir = self.generate_session_dir(container)
        session_dir = self.generate_session_dir()

        self._rotat_logs(session_dir)

        chat_worker = ChatEnabledWorker([], name="TaskExecutionAgent")
        self.chat.add_chat_user(chat_worker)

        llm = LLMInterface(llm_api_key=OPENAI_API_KEY,
                        session_dir=session_dir,
                        instance_name="TaskExecutionAgent",
                        chat_worker=chat_worker)

        if agent_type not in AGENT_CONFIGS:
            raise ValueError(f"Unsupported agent type: {agent_type}")

        agents_config_info = AGENT_CONFIGS[agent_type]
        prompts_base_path = agents_config_info["prompts_path"]

        if self.manifest_path:
            work_item['manifest_path'] = self.manifest_path

        # Load platform configuration
        micro_base_path = os.path.dirname(prompts_base_path)
        platform_loader = PlatformConfigLoader(micro_base_path)

        # For project generation, use generic platform/framework
        if is_project_generation:
            selected_platform = ApplicationType.GENERIC.value
            selected_framework = FrameworkType.DEFAULT.value
        else:
            # For single container, use container-specific platform/framework
            single_container = work_item.get("containers", [{}])[0]
            selected_platform = single_container.get("platform", single_container.get("container_type", ApplicationType.GENERIC.value))
            selected_framework = single_container.get("framework", FrameworkType.DEFAULT.value)

        micro_agent_config = platform_loader.get_platform_config(
            ApplicationType.from_string(selected_platform),
            FrameworkType.from_string(selected_framework)
            )

        self._init_agent(agent_type, llm, session_dir, micro_agent_config,
                        model_name, prompts_base_path, container_type,
                        work_item=work_item)

        self.worker.finished_signal.connect(self.process_finished)
        self.worker.progress_signal.connect(self.update_progress)

        self.thread = QThread()
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.finished_signal.connect(self.thread.quit)
        self.worker.finished_signal.connect(self.worker.deleteLater)
        self.thread.finished.connect(self.thread.deleteLater)

        self.thread.start()

        self.populate_model_selector()

        # Record which container/project we're working on
        if is_project_generation:
            self.current_container_type = "project"
            self.status_area.append("Starting project-wide code generation...")
        else:
            if self.get_current_container():
                self.current_container_type = self.get_current_container().container_type
                if self.current_container_type:
                    self.started_containers.add(self.current_container_type)

        # Update button states
        if is_project_generation:
            pass  # Already updated above
        else:
            self.generate_button.setText('Stop Session')
            self.generate_button.setEnabled(True)

        self.stop_stream_button.setEnabled(True)
        self.stop_stream_button.setText("Stop Streaming")

        # Minimize the input panel after starting the process
        if not self.input_panel_minimized:
            self.toggle_input_panel()

    def get_current_container(self) -> Container :
        """Get the currently selected container type"""
        return get_active_tab_container(self.container_tabs)

    def update_progress(self, message):
        self.status_area.clear()  # Clear previous status
        self.status_area.setText(message)

    def process_finished(self):
        """Handle process completion and save container context"""

        # Update UI based on generation type
        if self.current_container_type == "project":
            # Project generation completed
            self.generate_project_button.setEnabled(True)
            self.generate_project_button.setText('Generate Project')
            self.status_area.append("Project generation completed!")

            # Update preview buttons for all generated containers
            container_names = []
            project_work_item = getattr(self.worker, 'work_item', {})
            containers = project_work_item.get('containers', {})
            for container in containers:
                if container.get('container_name'):
                    container_names.append(container['container_name'])
            if container_names:
                self.update_preview_buttons(container_names)

        else:
            # Container-specific generation completed
            self.generate_button.setEnabled(True)
            self.generate_button.setText('Restart Session')
            self.status_area.append(f"Process completed for {self.current_container_type} container!")

        # Common cleanup
        self.stop_stream_button.setEnabled(False)
        self.progress_bar.hide()
        self.input_tab_widget.setEnabled(True)

        # Clean up thread
        if self.thread and self.thread.isRunning():
            self.thread.quit()
            self.thread.wait(1000)

        # Clear references
        self.worker = None
        self.current_container_type = None
        self.thread = None

        # Restore input panel if minimized
        if self.input_panel_minimized:
            self.toggle_input_panel()

    @pyqtSlot(str, str, str, str)
    def store_progress_update(self, status, total_tasks, latest_result, request_context):
        try:
            context = json.loads(request_context) if request_context else {}
            result = json.loads(latest_result) if latest_result else {}

            # Add the current step to past_steps
            if 'last_step' in context:
                last_step = context['last_step']
                if isinstance(last_step, str):
                    try:
                        last_step = json.loads(last_step)
                    except json.JSONDecodeError:
                        print(f"Error decoding last_step JSON: {last_step}")
                        last_step = {}

                if isinstance(last_step, dict):
                    step_title = last_step.get('action', 'Unknown Action')
                    agent = last_step.get('micro_agent', 'Unknown Agent')
                    action = last_step.get('action', 'Unknown Action')
                    details = json.dumps(last_step.get('request_details', {}), indent=4)

                    self.past_steps.append({
                        'title': step_title,
                        'micro_agent': agent,
                        'action': action,
                        'details': details,
                        'context': context
                    })

            self.progress_updates.append({
                'status': status,
                'total_tasks': json.loads(total_tasks) if total_tasks else [],
                'latest_result': result,
                'request_context': context
            })
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON in store_progress_update: {e}")
            # Handle the error by storing raw strings
            self.progress_updates.append({
                'status': status,
                'total_tasks': total_tasks,
                'latest_result': latest_result,
                'request_context': request_context
            })

    @pyqtSlot(str)
    def update_status(self, message):
        """Update status with container information"""
        current_container = self.get_current_container()

        if not current_container: #Project mode
            # Project-wide generation
            message = f"[PROJECT] {message}"

            # Show all configured containers
            if hasattr(self, 'worker') and self.worker:
                work_item = getattr(self.worker, 'work_item', {})
                containers = work_item.get('containers', {})
                if containers:
                    message += f"\n\nConfigured Containers: {len(containers)}"
                    for container in containers:
                        message += f"\n - {container.get('container_type')}: {container.get('container_name')}"
                        message += f" {container.get('framework')}"

        else:
            # Single container generation
            message = f"[{current_container.container_type}] {message}"

        self.status_area.setText(message)

    @pyqtSlot()
    def stop_streaming(self):
        """
        User has pressed "Stop Streaming" — forward to
        TaskExecutionAgent.stop_streaming(), and disable the button.
        """
        if self.worker and hasattr(self.worker, 'agent'):
            try:
                self.worker.agent.stop_streaming()
                self.status_area.append("Streaming stopped by user.")
            except Exception as e:
                self.status_area.append(f"Error stopping stream: {e}")
        self.stop_stream_button.setEnabled(False)

    def update_display(self):
        if self.progress_updates:
            latest_update = self.progress_updates[-1]
            self.status_area.append(latest_update['status'])
            self.update_past_steps()
            self.update_task_list(latest_update['total_tasks'])
            self.last_results_area.setPlainText(json.dumps(latest_update['latest_result'], indent=2))
            self.progress_updates.clear()

    def generation_finished(self):
        self.generate_button.setEnabled(True)
        self.generate_button.setText('Restart Code Generation')
        self.stop_stream_button.setEnabled(False)
        self.progress_bar.hide()
        self.status_area.append("Code generation completed!")
        self.worker = None

    def clear_past_steps(self):
        self.past_steps_list.clear()
        self.past_steps_detail.clear()
        self.steps_data.clear()

    def clear_task_list(self):
        self.task_list.clear()
        self.task_detail.clear()
        self.tasks_data.clear()


    def send_user_input(self):
        user_input = self.user_input.toMarkdown()
        user_input = prompt_prepare(user_input,"text")
        if user_input and self.worker:
            self.status_area.append(f"User input sent: {user_input}")
            self.worker.send_user_input(user_input)
            self.user_input.clear()
        elif not self.worker:
            self.status_area.append("Code generation has not started. Cannot send user input.")

    def closeEvent(self, event):
        if self.thread:
            if self.control:
                self.control.resume()  # Ensure the thread isn't paused
            self.control.stop()  # Stop the thread
            self.thread.join(timeout=1.0)  # Give the thread a chance to finish
        super().closeEvent(event)

    def create_chat_panel(self):
        chat_group = QGroupBox("Chat with Agent (deprecated)")
        chat_layout = QVBoxLayout()

        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setAcceptRichText(True)
        self.chat_display.setStyleSheet("background-color: white; color: black; font-family: Arial;")
        self.chat_display.setMinimumHeight(300)
        chat_layout.addWidget(self.chat_display)
        chat_group.setLayout(chat_layout)
        return chat_group

    def eventFilter(self, source, event):
        if (source is self.chat_input and
            event.type() == QEvent.Type.KeyPress and
            event.key() == Qt.Key.Key_Return and
            not event.modifiers() & Qt.KeyboardModifier.ShiftModifier):
            self.send_chat_message()
            return True
        return super().eventFilter(source, event)

    @staticmethod
    def convert_llm_output_to_html(text):
        # Convert **bold** to <strong>bold</strong>
        text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)

        # Convert *italic* to <em>italic</em>
        text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)

        # Convert numbered lists
        text = re.sub(r'^\d+\.\s', r'<br>• ', text, flags=re.MULTILINE)

        # Convert URLs to clickable links
        text = re.sub(r'(https?://\S+)', r'<a href="\1">\1</a>', text)

        # Convert newlines to <br> tags
        text = text.replace('\n', '<br>')

        return text

    def display_chat_message(self, sender, message):
        self.chat_display.moveCursor(QTextCursor.MoveOperation.End)
        # Convert the message to HTML
        html_message = self.convert_llm_output_to_html(message)

        # Insert the formatted message
        self.chat_display.insertHtml(f"<p><strong>{sender}:</strong> {html_message}</p>")
        self.chat_display.insertHtml("<br>")  # Add some space after each message
        self.chat_display.ensureCursorVisible()


    def display_prompt(self, prompt):
        self.chat_display.append(f"<b>Agent:</b> {prompt}")
        self.chat_input.setPlaceholderText(prompt)


    @staticmethod
    def generate_session_dir() -> str:
        ident = os.getenv("SESSION_ROOT", "1157")

        session_root = os.path.join(
            config.get("SYSTEM", "workspace_dir"),
            "kavia",
            f"codegen-{ident}"
        )

        # Check if directory exists
        if not os.path.exists(session_root):
            Path(session_root).mkdir(parents=True, exist_ok=True)

        return session_root

    @staticmethod
    def generate_container_dir(container_type):
        session_root = CodeGenerationGUI.generate_session_dir()

        container_dir = os.path.join(session_root, container_type)

        uploads_dir = os.path.join(container_dir, "chat_uploads")

        Path(container_dir).mkdir(parents=True, exist_ok=True)
        Path(uploads_dir).mkdir(parents=True, exist_ok=True)

        return container_dir


    @staticmethod
    def make_dir_p(path):

        Path(path).mkdir(parents=True, exist_ok=True)

    def update_preview_button_status(self, is_enabled):
        """Update the Preview button status based on URL availability"""
        self.preview_button.setEnabled(is_enabled)
        if is_enabled:
            # Get the URL from the preview manager
            preview_url = self.preview_manager.get_preview_url()
            # Update tooltip to show the URL
            if preview_url:
                self.preview_button.setToolTip(f"Open preview: {preview_url}")
        else:
            self.preview_button.setToolTip("Preview not available yet")

    def open_container_env_dialog(self, container):
        """Open environment variables dialog for a container"""
        dialog = EnvironmentVariablesDialog(container, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Update the container's env variables
            container.env = dialog.get_environment_variables()
            # Save to manifest if we have a project schema
            if self.project_schema:
                self.project_schema.save_to_manifest(self.manifest_path)

class CollapsibleSection(QWidget):
    def __init__(self, title, content):
        super().__init__()
        self.title = title
        self.content = content
        self.is_expanded = True
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        self.toggle_button = QPushButton(self.title + " ▼")
        self.toggle_button.clicked.connect(self.toggle_content)
        layout.addWidget(self.toggle_button)
        layout.addWidget(self.content)

    def toggle_content(self):
        self.is_expanded = not self.is_expanded
        self.content.setVisible(self.is_expanded)
        self.toggle_button.setText(self.title + (" ▼" if self.is_expanded else " ▶"))


class EnvironmentVariablesDialog(QDialog):
    """Dialog for managing environment variables for a container"""
    
    def __init__(self, container, parent=None):
        super().__init__(parent)
        self.container = container
        self.env_vars = container.env.copy() if container.env else {}
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle(f"Environment Variables - {self.container.container_name}")
        self.setModal(True)
        self.resize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel(f"Environment Variables for {self.container.container_name}")
        header_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(header_label)
        
        # Environment variables table
        self.env_table = QTableWidget()
        self.env_table.setColumnCount(2)
        self.env_table.setHorizontalHeaderLabels(["Variable Name", "Value"])
        self.env_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.env_table.setAlternatingRowColors(True)
        layout.addWidget(self.env_table)
        
        # Buttons for managing env vars
        button_layout = QHBoxLayout()
        
        add_button = QPushButton("Add Variable")
        add_button.clicked.connect(self.add_variable)
        button_layout.addWidget(add_button)
        
        remove_button = QPushButton("Remove Selected")
        remove_button.clicked.connect(self.remove_variable)
        button_layout.addWidget(remove_button)
        
        button_layout.addStretch()
        
        # Import/Export buttons
        import_button = QPushButton("Import from .env")
        import_button.clicked.connect(self.import_from_env)
        button_layout.addWidget(import_button)
        
        export_button = QPushButton("Export to .env")
        export_button.clicked.connect(self.export_to_env)
        button_layout.addWidget(export_button)
        
        layout.addLayout(button_layout)
        
        # Dialog buttons
        dialog_buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        dialog_buttons.accepted.connect(self.accept)
        dialog_buttons.rejected.connect(self.reject)
        layout.addWidget(dialog_buttons)
        
        self.populate_table()
        
    def populate_table(self):
        """Populate the table with current environment variables"""
        self.env_table.setRowCount(len(self.env_vars))
        
        for row, (key, value) in enumerate(self.env_vars.items()):
            key_item = QTableWidgetItem(key)
            value_item = QTableWidgetItem(value)
            
            self.env_table.setItem(row, 0, key_item)
            self.env_table.setItem(row, 1, value_item)
            
    def add_variable(self):
        """Add a new environment variable"""
        row = self.env_table.rowCount()
        self.env_table.insertRow(row)
        
        key_item = QTableWidgetItem("")
        value_item = QTableWidgetItem("")
        
        self.env_table.setItem(row, 0, key_item)
        self.env_table.setItem(row, 1, value_item)
        
        # Focus on the new row for editing
        self.env_table.setCurrentCell(row, 0)
        self.env_table.editItem(key_item)
        
    def remove_variable(self):
        """Remove selected environment variables"""
        selected_rows = set()
        for item in self.env_table.selectedItems():
            selected_rows.add(item.row())
            
        # Remove rows in reverse order to avoid index issues
        for row in sorted(selected_rows, reverse=True):
            self.env_table.removeRow(row)
            
    def import_from_env(self):
        """Import environment variables from .env file"""
        if not self.container.container_root:
            QMessageBox.warning(self, "Import Error", "Container root path not set. Cannot import .env file.")
            return
            
        # Try to find .env file in container root
        env_file_path = os.path.join(
            CodeGenerationGUI.generate_session_dir(),
            self.container.container_root,
            ".env"
        )
        
        if not os.path.exists(env_file_path):
            QMessageBox.information(self, "Import Info", f"No .env file found at:\n{env_file_path}")
            return
            
        try:
            from dotenv import dotenv_values
            env_vars = dotenv_values(env_file_path)
            env_vars = dict(env_vars)
            
            # Add imported variables to the table
            for key, value in env_vars.items():
                row = self.env_table.rowCount()
                self.env_table.insertRow(row)
                
                key_item = QTableWidgetItem(key)
                value_item = QTableWidgetItem(value)
                
                self.env_table.setItem(row, 0, key_item)
                self.env_table.setItem(row, 1, value_item)
                
            QMessageBox.information(self, "Import Success", f"Imported {len(env_vars)} environment variables from .env file.")
            
        except Exception as e:
            QMessageBox.critical(self, "Import Error", f"Failed to import .env file:\n{str(e)}")
            
    def export_to_env(self):
        """Export environment variables to .env file"""
        if not self.container.container_root:
            QMessageBox.warning(self, "Export Error", "Container root path not set. Cannot export to .env file.")
            return
            
        # Collect variables from table
        env_vars = {}
        for row in range(self.env_table.rowCount()):
            key_item = self.env_table.item(row, 0)
            value_item = self.env_table.item(row, 1)
            
            if key_item and key_item.text().strip():
                key = key_item.text().strip()
                value = value_item.text() if value_item else ""
                env_vars[key] = value
                
        if not env_vars:
            QMessageBox.information(self, "Export Info", "No environment variables to export.")
            return
            
        # Create .env file
        env_file_path = os.path.join(
            CodeGenerationGUI.generate_session_dir(),
            self.container.container_root,
            ".env"
        )
        
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(env_file_path), exist_ok=True)
            
            with open(env_file_path, 'w', encoding='utf-8') as f:
                for key, value in env_vars.items():
                    f.write(f"{key}={value}\n")
                    
            QMessageBox.information(self, "Export Success", f"Exported {len(env_vars)} environment variables to:\n{env_file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export .env file:\n{str(e)}")
            
    def get_environment_variables(self):
        """Get the environment variables from the table"""
        env_vars = {}
        for row in range(self.env_table.rowCount()):
            key_item = self.env_table.item(row, 0)
            value_item = self.env_table.item(row, 1)
            
            if key_item and key_item.text().strip():
                key = key_item.text().strip()
                value = value_item.text() if value_item else ""
                env_vars[key] = value
                
        return env_vars
        
    def accept(self):
        """Save environment variables to container and close dialog"""
        self.env_vars = self.get_environment_variables()
        self.container.env = self.env_vars
        super().accept()

def main():
    signal.signal(signal.SIGINT, signal_handler)

    chat = ChatInterface()

    # Start Flask server in background thread
    flask_thread = start_flask_server(chat)


    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon("src/ux/kavia_rounded.png"))

    gui = CodeGenerationGUI(chat)
    gui.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
