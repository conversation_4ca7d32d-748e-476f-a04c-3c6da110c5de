import json
import os

from code_generation_core_agent.agents.utilities import FrameworkType, ApplicationType


def merge_figma_design(work_items, figma_data, name):
    return {**work_items[name], "figma_components": figma_data["figma_components"]} if "figma_components" in figma_data else work_items[name]


base_work_items = {
    "ColorWeaver": {
        "component_name": "ColorWeaver - Color Mixing Puzzle Game",
        "description": "React-based falling block puzzle game focusing on color theory and combinations."
                       " Built with React and TypeScript, running on port 3000 with a WebGL renderer for smooth animations."
                       " Functionality includes: 1. Generating and managing 2x2 pieces with random color triangles"
                       "2. Handling piece movement and rotation with collision detection"
                       "3. Processing color combinations and merging rules 4. Managing game state including score, "
                       "level, and chain reactions 5. Implementing particle effects for matched colors "
                       "6. Handling progressive difficulty scaling 7. Managing preview queue of upcoming pieces "
                       "8. Processing and displaying chain reaction animations",
        "interfaces": "Modern GUI with main game grid (10x20), piece preview panel, "
                      "score display, level indicator, color combination guide, and statistics tracker. "
                      "Keyboard controls for movement (arrows), rotation (up), and hard drop (space). "
                      "Touch controls for mobile with swipe and tap gestures.",
        "algorithms": [
            "Random color distribution for piece generation",
            "Collision detection for piece movement and rotation",
            "Color mixing logic (RGB color space calculations)",
            "Chain reaction detection and processing",
            "Score calculation with multipliers",
            "Progressive difficulty scaling based on score/level",
            "Particle system for visual effects",
            "Grid collapse and piece settling physics"
        ],
        "pseudocode": """
generatePiece():
    create 2x2 grid
    for each cell:
        assign random primary color
    return piece

checkColorMix(x, y):
    if 2x2 grid complete:
        calculate resulting color
        trigger particle effects
        update score
        collapse grid

updateGameState():
    move current piece down
    check collisions
    if piece locked:
        check color combinations
        process chain reactions
        generate new piece
        update difficulty
"""
    },

    "RetroTetris": {
        "component_name": "React App for Retro CRT Tetris",
        "description": "React app for playing Tetris with an authentic CRT TV aesthetic. "
                       "The app is written in JavaScript and uses React. The app is deployed locally on port 3000. "
                       "Functionality includes: 1. Standard Tetris controls (Left/Right/Up/Down arrows, Spacebar)"
                       " 2. Classic piece movements and rotations 3. Score tracking and level progression 4. Next piece preview"
                       " 5. Game Over detection 6. Authentic CRT effects including scan lines, screen curvature, and slight flicker",
        "interfaces": "GUI interface styled as a vintage CRT TV display with game grid, score display, and next piece preview",
        "algorithms": "Piece generation and rotation logic, collision detection, line clearing and scoring calculation,"
                      " progressive difficulty scaling, implementing standard Tetris controls and movements",
        "pseudocode": ""
    },

    "RecipeSearch": {
        "component_name": "React App for Recipe Search",
        "description": "Modern looking react app for searching and viewing recipes from dummyJSON API (https://dummyjson.com/recipes). "
                       "The app is written in JavaScript and uses React. The app is deployed locally on port 3000."
                       " Functionality includes:"
                       " 1. Fetching recipes from API "
                       "2. Displaying list of recipes bellow the search window "
                       "3. Searching recipes 4. Viewing recipe details"
                       " 5. Showing recipe images"
                       " 6. Display ingredients and instructions in a popup window when a recipe is clicked",
        "interfaces": "GUI interface to search and view recipes",
        "algorithms": "Fetching recipes from API, searching recipes, displaying recipe details, filtering recipe list",
        "pseudocode": ""
    },

    "ExpenseTracker": {
        "component_name": "React App for Expense Tracking",
        "description": "React app for tracking personal or business expenses. The app is written in JavaScript and uses React. It utilizes local storage for data persistence and Chart.js for visualizations. The app is deployed locally on port 3000. Functionality includes: 1. Adding new expenses 2. Categorizing expenses 3. Setting and tracking budgets 4. Generating reports and visualizations 5. Exporting data 6. Customizing app settings through a detailed configuration menu",
        "interfaces": "GUI interface for expense management, detailed settings menu, data visualization dashboard",
        "algorithms": "Expense categorization, budget calculations, data aggregation for reports",
        "pseudocode": """
1. Initialize app state and load saved data
2. Render main interface with navigation menu
3. While app is running:
    a. If user selects 'Add Expense':
        - Display expense entry form
        - Validate and save new expense
        - Update relevant data and visualizations
    b. If user selects 'View Expenses':
        - Display list of expenses with filter and sort options
        - Allow editing or deletion of expenses
    c. If user selects 'Budgets':
        - Display current budgets and spending progress
        - Allow setting or modifying budget categories and amounts
    d. If user selects 'Reports':
        - Generate and display various financial reports and charts
        - Offer options to customize report parameters
    e. If user selects 'Settings':
        - Display detailed configuration menu
        - Allow customization of:
            * Currency settings
            * Date format
            * Category management
            * Notification preferences
            * Data backup and sync options
            * UI theme and layout
    f. If user selects 'Export Data':
        - Offer options to export data in various formats (CSV, PDF, etc.)
4. Continuously save app state and user preferences
"""
    },

    "ChessGame": {
        "component_name": "React App for Chess",
        "description": "React app for playing chess against an AI opponent. The app is written in JavaScript and uses React. Use a modern UI library (material UI) for the interface. Use an existing chess library chessboard.js for layout. Implements the game logic and AI moves. The app is deployed locally on port 3000. Functionality includes: 1. Creating a new game session 2. Setting AI difficulty level 3. Allowing user to make moves 4. Automatically making moves when it's the AI's turn 5. Getting the game state 6. Displaying the result when the game ends",
        "interfaces": "GUI interface to play the game, difficulty setting controls",
        "algorithms": "Chess game logic, AI move generation based on difficulty",
        "pseudocode": """
1. Initialize chessboard
2. Set up difficulty controls
3. While game is not over:
    a. If it's player's turn:
        - Wait for player move
        - Validate move
        - Make move if valid
    b. If it's AI's turn:
        - Generate AI move based on current difficulty
        - Make AI move
    c. Update board state
    d. Check for game end conditions (checkmate, stalemate, etc.)
4. Display game result
"""
    },

"gomoku" : {
    "component_name": "React App for Retro Gomoku",
    "description": "React app for playing Gomoku (Five in a Row) in a retro-themed setting. "
                   "The app is written in JavaScript and uses React. The app is deployed locally on port 3000. "
                   "Functionality includes:"
                   "1. Creating a new game session "
                   "2. Human player is the first to move"
                   "3. Allowing user to place squares."
                   "4. Computer placing dots using Monte Carlo Tree Search with threat-space analysis "
                   "5. Maintaining and displaying theboard state "
                   "6. Celebrating with funny AI messages when someone wins "
                   "7. Game play features a retro CRT screen aesthetic "
                   "8. First AI move is center-board placement for optimal positioning"
                   "9. Support board sizes 10x10, 15x15 and 20x20",

    "interfaces": "GUI interface to play the game on a retro board design",
    "algorithms": "1. Monte Carlo Tree Search (MCTS) with UCT selection for main move evaluation "
                  "2. Threat-space analysis for rapid pruning of poor moves "
                  "3. Pattern matching for quick win/block detection "
                  "4. Random first-move selection "
                  "5. Five-in-a-row win condition validation (horizontal, vertical, diagonal) "
                  "6. Move validation and board state management",
    "pseudocode": ""
},

    "MinimalARM64OS": {
        "component_name": "Bare Metal ARM64 OS with Basic Shell",
        "description": "A minimal operating system for ARM64 architecture (aarch64) that boots to a basic shell with echo functionality. The OS is written in C11 standard and uses fixed-width integer types (stdint.h) for consistent behavior. It utilizes QEMU for testing and provides bare minimum functionality: 1. Basic boot sequence 2. UART-based I/O 3. Minimal interrupt handling 4. Simple shell with echo command 5. Fixed memory layout 6. Hardware abstraction for UART. The system uses aarch64-none-elf-gcc for compilation and is designed for QEMU aarch64 testing.",
        "interfaces": "Serial console interface via UART for input/output, basic shell prompt, hardware abstraction layer for UART operations, exception vector table interface",
        "algorithms": "Boot sequence initialization, UART communication protocol, interrupt handling for keyboard input, character buffer management, basic command parsing",
        "pseudocode": """
1. Initialize system state:
    a. Set up stack pointer
    b. Configure exception vector table
    c. Initialize UART hardware
    d. Enable minimal interrupts

2. While system is running:
    a. If boot sequence:
        - Initialize stack pointer (0x80000)
        - Set up exception vectors
            * Configure minimal handlers for:
                - Synchronous exceptions
                - IRQ (UART)
                - FIQ (unused)
                - SError
        - Initialize UART
            * Set baud rate
            * Enable TX/RX
        - Jump to kernel_main

    b. If in kernel_main:
        - Display boot message
        - Enter command loop:
            * Show prompt
            * Wait for input
            * If command is 'echo':
                - Read following chars
                - Print them back
            * If unknown command:
                - Display error

    c. If UART interrupt:
        - Read available character
        - Store in input buffer
        - Set input ready flag

    d. If exception occurs:
        - Handle minimal required response
        - Return to main loop if possible
        - Halt if fatal

3. Hardware abstraction functions:
    - UART_init()
    - UART_putc(char)
    - UART_getc()
    - Handle_interrupt()

4. Memory layout enforced:
    - 0x00000000: Boot code
    - 0x00001000: Kernel code
    - 0x00080000: Stack (down)
"""
    },
    "TicTacToe": {
               "component_name": "React App for Tic Tac Toe",
                "description": "React app for playing tic tac toe with the computer. "
                               "Game background image: https://img.goodfon.com/original/1920x1080/c/73/video-game-art-rise-of-the-ronin-playstation-swordman-ship-t.jpg "
                               "The app is deployed locally on port 3000"
                               "Key features include game session creation, random first-move selection, user move input, automated computer moves, game state tracking, and a victory celebration.",
                "interfaces": "GUI interface to play the game, difficulty setting controls, game state display, reset button, undo button, timer display",
                "algorithms": "Randomly deciding who has the first move, making moves using mini-max algorithm when it is computer's turn, getting the game state, getting the game winner",
                "pseudocode": "",
    },
    "TicTacToeTestCases": {
                "test_cases": [
                  {
                    "Tags": "TicTacToe, GameInitialization, BasicFunctionality",
                    "Description": "Verify that the game board initializes correctly with an empty 3x3 grid",
                    "Category": "GameInitialization",
                    "PreConditions": "Tic Tac Toe application is open",
                    "Type": "FunctionalTestCase",
                    "Steps": "1. Launch the Tic Tac Toe game\n2. Observe the initial game board",
                    "TestLevel": "Unit",
                    "ExpectedResult": "The game should display an empty 3x3 grid with no X or O markers",
                    "CanBeAutomated": "true",
                    "Title": "Game Board Initialization",
                    "Priority": "High"
                  },
                  {
                    "Tags": "TicTacToe, GameLogic, FirstMoveSelection",
                    "Description": "Verify that the first move is randomly assigned to either the user or computer",
                    "Category": "GameLogic",
                    "PreConditions": "Application is ready to start a new game",
                    "Type": "FunctionalTestCase",
                    "Steps": "1. Start multiple new games (at least 10)\n2. Record which player makes the first move in each game",
                    "TestLevel": "Unit",
                    "ExpectedResult": "Both the user and computer should make the first move in a roughly equal number of games",
                    "CanBeAutomated": "true",
                    "Title": "Random First Move Selection",
                    "Priority": "Medium"
                  },
                  {
                    "Tags": "TicTacToe, GamePlay, ComputerAI",
                    "Description": "Verify that the computer makes intelligent moves using the minimax algorithm",
                    "Category": "GamePlay",
                    "PreConditions": "Game is initialized and on highest difficulty setting",
                    "Type": "FunctionalTestCase",
                    "Steps": "1. Create a scenario where there is an obvious optimal move for the computer\n2. Observe if the computer takes that optimal move",
                    "TestLevel": "System",
                    "ExpectedResult": "The computer should consistently choose the optimal move to either win or block the player from winning",
                    "CanBeAutomated": "true",
                    "Title": "Computer AI Move Intelligence",
                    "Priority": "High"
                  },
                  {
                    "Tags": "TicTacToe, GameLogic, WinDetection",
                    "Description": "Verify that the game correctly detects all win conditions (horizontal, vertical, diagonal)",
                    "Category": "GameLogic",
                    "PreConditions": "Game is in progress",
                    "Type": "FunctionalTestCase",
                    "Steps": "1. Create winning scenarios for all possible win patterns (3 horizontal rows, 3 vertical columns, 2 diagonals)\n2. Observe the game's response to each winning pattern",
                    "TestLevel": "System",
                    "ExpectedResult": "The game should correctly detect the win for all 8 possible winning patterns and display a victory message",
                    "CanBeAutomated": "true",
                    "Title": "Win Condition Detection",
                    "Priority": "High"
                  },
                  {
                    "Tags": "TicTacToe, GameControls, ResetAndUndo",
                    "Description": "Verify that the reset and undo buttons function correctly",
                    "Category": "GameControls",
                    "PreConditions": "Game is in progress with multiple moves made",
                    "Type": "FunctionalTestCase",
                    "Steps": "1. Make several moves on the board\n2. Test the undo button to verify it reverts the last move\n3. Continue playing\n4. Test the reset button to verify it clears the board",
                    "TestLevel": "Unit",
                    "ExpectedResult": "The undo button should revert the last player and computer moves. The reset button should clear the board completely",
                    "CanBeAutomated": "true",
                    "Title": "Reset and Undo Button Functionality",
                    "Priority": "High"
                  },
                  {
                    "Tags": "TicTacToe, Celebration, VictoryAnimation",
                    "Description": "Verify that a victory celebration occurs when the player wins the game",
                    "Category": "GameLogic",
                    "PreConditions": "Game is in progress and player is close to winning",
                    "Type": "FunctionalTestCase",
                    "Steps": "1. Set up a scenario where the player needs one more move to win\n2. Complete the winning move\n3. Observe the game's victory celebration response",
                    "TestLevel": "System",
                    "ExpectedResult": "A visually engaging victory celebration animation or effect should display when the player wins",
                    "CanBeAutomated": "true",
                    "Title": "Victory Celebration Functionality",
                    "Priority": "Medium"
                  }
                ]
         },

    "TicTacToeFrontend": {
        "component_name": "React App for Tic Tac Toe Frontend",
        "containers": [
            {
            "container_name": "tictactoe-backend",
            "container_type": "git",
            "repository": "**************:Kavia-ai/tictactoe-backend.git",
            "branch": "main"
            },
        ],
        "description": "React app for playing tic tac toe with the computer. "
                       "The app is written in JavaScript and uses React and Material UI. The app is deployed locally on port 3000"
                       "Compared to any reference design always prefer WCAG 2.2 standards for accessibility. It uses the backend API for game logic and state management."
                       "Key features include game session creation, random first-move selection, user move input, automated computer moves, game state tracking, and a victory celebration.",
        "interfaces": "GUI interface to play the game, difficulty setting controls, game state display, reset button, undo button, timer display",
        "algorithms": "",
        "pseudocode": ""
    },
    "NumberGuess": {
        "component_name": "React App for Number Guessing Game",
        "description": "React app for playing a number guessing game against the computer. "
                       "The app is written in JavaScript and uses React and Material UI. The app is deployed locally on port 3000 "
                       "Key features include game session creation, number range selection, hint system, guess tracking, attempt counter, and a victory celebration.",
        "interfaces": "GUI interface to play the game, difficulty setting controls (adjusts number range), guess input field, hint display, attempt counter, reset button, give up button, score display",
        "algorithms": "Generating a random target number within the selected range, comparing user guesses with target number, providing intelligent hints based on guess history, calculating score based on attempts used",
        "pseudocode": ""
    },
    "MemoryCardFlutter": {
        "component_name": "Flutter App for Memory Card Game",
        "description": "Flutter app for playing a memory card matching game. "
                    "The app is written in Dart and uses Flutter 3. The app will run on the available emulator. "
                    "Key features include game session creation, card shuffling, move counting, timer tracking, difficulty levels, and victory celebration.",
        "interfaces": "GUI interface to play the game, difficulty setting controls (grid size 4x4, 6x6), move counter display, timer display, reset button, best scores display",
        "algorithms": "Card shuffling algorithm, pair matching validation, score calculation based on moves and time, game state management, difficulty level adjustment",
        "pseudocode": ""
    },
    "SimpleCalculator": {
        "component_name": "Flutter App for Simple Calculator",
        "description": "Flutter app for performing basic arithmetic calculations. "
                    "Key features include number input, operator selection, result display, and error handling.",
        "interfaces": "GUI interface with number buttons, operator buttons, result display, clear button, error message display",
        "algorithms": "Arithmetic operation execution based on user input, error handling for invalid expressions, result display formatting",
        "pseudocode": ""
    },
    "FlutterJumpGame": {
        "component_name": "Flutter Game - FlutterJump",
        "description": "Flutter-based mobile game where the player controls a character that jumps between platforms to reach higher levels. Built using Flutter and Dart, the game features smooth animations, score tracking, and basic physics for jump mechanics.",
        "interfaces": "The app provides a clean user interface using Flutter widgets, including a home screen, game screen, pause menu, and game-over screen. Touch controls are used for interaction.",
        "algorithms": "1. Game loop and frame updates using Flutter’s animation controller\n2. Collision detection for platform landing\n3. Score and difficulty scaling as the player ascends\n4. Simple physics for gravity and jumping",
        "pseudocode": ""
    },
    "TicTacToeiOSSwift": {
        "component_name": "iOS Tic Tac Toe Game Application",
        "description": "Native iOS application for playing Tic Tac Toe built with Swift and SwiftUI. "
                        "The app follows iOS Human Interface Guidelines and supports iPhone layouts. "
                        "Functionality includes: "
                        "1. Local multiplayer mode for two players",
        "interfaces": "Native iOS GUI with main game board"
                      "Supports standard iOS gestures like tap for moves",
        "algorithms": [
            "Game state validation and win condition checking",
        ],
        "pseudocode": """
        The following is a suggestion don't completely follow it if you can make simplifications and still address the requirements
        // Main Game Structure
        class TicTacToeGame {
            // Game State
            var board = [["", "", ""],
                        ["", "", ""],
                        ["", "", ""]]
            var currentPlayer = "X"
            var gameActive = true
            
            // Game Logic Functions
            func makeMove(row, column) {
                if board[row][column] is empty and gameActive {
                    place currentPlayer at board[row][column]
                    if checkWinCondition() {
                        declare currentPlayer as winner
                        end game
                    } else if boardIsFull() {
                        declare draw
                        end game
                    } else {
                        switch currentPlayer between "X" and "O"
                    }
                }
            }
            
            func checkWinCondition() {
                check all rows for three matching symbols
                check all columns for three matching symbols
                check both diagonals for three matching symbols
                return true if any condition is met
            }
            
            func resetGame() {
                clear all board positions
                set currentPlayer to "X"
                set gameActive to true
            }
        }
        // UI Components
        class GameViewController {
            // UI Elements
            gameBoard: 3x3 grid of buttons
            statusLabel: displays current player or game result
            resetButton: starts new game
            
            // Event Handlers
            func handleCellTap(at position) {
                if tap is detected on valid cell {
                    get row and column from tapped position
                    call game.makeMove(row, column)
                    update UI to reflect move
                    update status label
                }
            }
            
            func handleResetButtonTap() {
                call game.resetGame()
                reset all UI elements to initial state
            }
            
            // UI Setup
            func setupGameBoard() {
                create 3x3 grid layout
                setup tap gesture recognizers for each cell
                apply iOS standard styling
                ensure proper spacing and alignment
                setup status label and reset button
            }
        }
        // App Lifecycle
        class AppDelegate {
            func applicationDidLaunch() {
                setup window and root view controller
                initialize game controller
                show game boardF
            }
        }
        // Flow:
        1. App launches
        2. Game board appears with empty cells
        3. Players alternate tapping cells to place X or O
        4. After each move:
        - Check for win condition
        - Check for draw condition
        - Switch players if game continues
        5. When game ends:
        - Display result
        - Allow reset for new game
            """
        },
    "AndroidTicTacToe": {
        "component_name": "Android Tic Tac Toe",
        "description": "Android app for playing tic tac toe with the computer. "
                    "The app is written in Java and uses Gradle for building and testing. " 
                    "Key features include game session creation, random first-move selection, user move input, automated computer moves, game state tracking, and a victory celebration.",
        "interfaces": "GUI interface to play the game, game state display, reset button",
        "algorithms": "Randomly deciding who has the first move, making moves using mini-max algorithm when it is computer's turn, getting the game state, getting the game winner",
        "pseudocode": ""
    },
    "Movielist": {
        "component_name": "React App for Movie Recommendations",
        "description": "React app for discovering and tracking movies based on emotional states ('moods'). "
                      "The app is written in JavaScript and uses React with a dark theme UI. The app features a sidebar navigation system. "
                      "Key features include mood-based recommendations, recently watched tracking, movie details view, and personalized suggestions.",
        "interfaces": "Dark mode UI with sidebar navigation, search bar, mood selection buttons, movie card grid display,"
                      " movie poster thumbnails, mood tags per movie, user profile section, 'Add Mood' button, friend activity feed, movie details modal",
        "algorithms": "Mood-based movie matching algorithm, personalized recommendation engine based on watch history,"
                      " similarity scoring between movies, social graph processing for friend recommendations, genre and mood correlation analysis",
        "pseudocode": ""
    },
    "ConversionApp": {
        "component_name": "C/C++ GUI App for Unit Conversion",
        "description": "C/C++ console application for converting units of measurement. "
                       "The app is written in C/C++ and uses a UI for unit conversions. "
                       "Functionality includes: 1. Converting between various units (length, weight, volume, etc.)"
                       " 2. Supporting both metric and imperial units 3. Handling user input and validation 4. Displaying conversion results 5. Providing a user-friendly interface for input and output"
                       " Application should use a GUI library like Qt for the interface.",
        "interfaces": "GUI interface styled like an old-school calculator with input fields, dropdowns for unit selection, conversion buttons, and result display",
        "algorithms": "Unit conversion logic for various measurement types, input validation for user entries, error handling for invalid conversions",
        "pseudocode": ""
    },
"RemarkableViewer": {
        "component_name": "Qt6 Application for reMarkable 2 Integration",
        "description": "Qt6 desktop application for interacting with reMarkable 2 tablet running v3+ firmware. "
                      "The application is written in Python using PyQt6 and integrates with reMarkable's local USB access API"
                      "Key features include document browsing, notebook preview, document upload/download, "
                      "and real-time document status tracking. No PDF support is needed",
        "interfaces": "GUI interface with document tree view, preview panel for notebook files, "
                     "toolbar for common actions (sync, upload, download, delete), "
                     "status bar for sync/connection status, "
                     "authentication dialog for cloud connection, "
                     "document properties panel, "
                     "search functionality for documents, "
                     "drag-and-drop support for file upload",
        "algorithms": "Notebook preview rendering "
                     "remarkable notebook format parsing (.rm files), "
                     "document tree management and filtering, "
                     "thumbnail generation for previews, "
                     "background task handling for API operations, "
                     "caching mechanism for downloaded files",
        "pseudocode": ""
    },
    "PacmanGame": {
        "component_name": "React App for Classic Pacman Game",
        "description": "React app implementation of the classic Pacman arcade game. "
                       "The app is written in JavaScript and uses React with HTML5 Canvas for rendering. "
                       "Key features include single-level maze navigation, ghost AI behavior, pellet collection, "
                       "power pellet mechanics, score tracking, and game over conditions.",
        "interfaces": "GUI interface with game canvas display, score counter, lives remaining indicator, "
                      "keyboard controls for Pacman movement (arrow keys), start game button, "
                      "game over screen with final score, high score display. No sound support is needed, "
                      "focus on visual aspect, accurate iconography.",
        "algorithms": "Pacman movement and collision detection, ghost AI pathfinding, "
                      "pellet collection tracking, power pellet state management, "
                      "score calculation, wall collision detection, "
                      "game state management (playing, paused, game over)",
        "pseudocode": ""
    },
    "GitHubExplorer": {
        "component_name": "React App for GitHub Repository Explorer",
        "description": "React app for exploring GitHub repositories and users. "
                       "GitHub Explorer is a streamlined, multi-page React application designed to provide developers "
                       "and users with a professional interface for exploring GitHub's ecosystem through its Public API. "
                       "The application emphasizes simplicity in both functionality and implementation, utilizing only "
                       "React JS and CSS to deliver a powerful yet lightweight experience."
                       "Key Features:\n"
                        "Home Page:\n"
                        "Intuitive search interface with real-time feedback"
                        "Curated showcase of trending developers and repositories"
                        "Persistent search history management"
                        "Comprehensive statistics dashboard\n"
                        "User Profile Interface:\n"
                        "Detailed GitHub user profile visualization"
                        "Interactive statistics and metrics display"
                        "Dynamic activity tracking calendar"
                        "Comprehensive organization affiliation overview\n"
                        "Repository Management:"
                        "Responsive grid-based repository presentation"
                        "Sophisticated filtering and search capabilities"
                        "Multi-parameter sorting system (stars, forks, recent updates)"
                        "Visual representation of programming language distribution\n",
        "interfaces": "GUI interface with search bar, search history display, trending showcase, ",
        "algorithms": "GitHub API integration, search query processing, data visualization, ",
        "pseudocode": ""
    },
    "TicTacToeBackend": {
        "component_name": "Python Flask API for TicTacToe Backend",
        "description": "Python-based RESTful API backend for TicTacToe game using Flask and SQLite. "
                       "The backend is written in Python 3.9+ and provides endpoints for game state management. "
                       "The API runs locally on port 5010. "
                       "Functionality includes: 1. Generating optimal AI moves using minimax algorithm "
                       "2. Processing player moves and board state 3. Determining game outcomes (win/loss/draw) "
                       "4. Storing game results in a SQLite database 5. Providing game statistics and history "
                       "6. Supporting multiple difficulty levels for the AI opponent",
        "interfaces": "RESTful API with JSON endpoints for game state management, move processing, and game statistics",
        "algorithms": "Minimax algorithm with alpha-beta pruning for optimal AI moves, "
                     "adaptive difficulty scaling based on selected level, "
                     "win condition detection for 3x3 grid, "
                     "board state validation and move legality checking, "
                     "SQLite database integration for game persistence",
        "pseudocode": ""
    },

    "VuePong": {
        "component_name": "Vue.js App for Classic Pong Game",
        "description": "Modern Vue.js implementation of the classic Pong arcade game with multiple play modes. "
                    "The app is built with Vue 3 using the Composition API and leverages Vue's reactivity system for game state management. "
                    "Deployed locally on port 5173 with Vite for fast development. "
                    "Functionality includes: "
                    "1. Single-player mode against AI with adjustable difficulty levels (Easy, Medium, Hard) "
                    "2. Two-player local multiplayer mode with split controls (W/S for Player 1, Arrow keys for Player 2) "
                    "3. Real-time paddle and ball physics with realistic bounce angles "
                    "4. Score tracking with game point/match systems "
                    "5. Visual and gameplay customization options (paddle size, ball speed, court appearance) "
                    "6. Pause/resume functionality with elegant game state management "
                    "7. Vue-themed visuals including prominent Vue logo integration in the UI "
                    "8. Responsive design that works across different screen sizes",
        "interfaces": "Clean, modern UI with Vue-inspired color scheme featuring: "
                    "Main menu with game mode selection, options, and high scores "
                    "Game screen with animated paddles, ball, score display, and Vue logo watermark "
                    "Difficulty selection slider for single-player mode "
                    "Settings panel for customizing game parameters "
                    "Pause overlay with resume/quit options "
                    "Victory screen with score summary and return-to-menu option "
                    "Visual indicators for active player/controls",
        "algorithms": [
            "Collision detection and response for paddles, ball, and walls",
            "Dynamic AI opponent behavior based on selected difficulty",
            "Progressive AI difficulty scaling with player skill",
            "Physics-based ball movement with velocity and angle calculations",
            "Input handling for keyboard controls with debounce protection",
            "Game state management with Vue's reactive system",
            "Score tracking and win condition validation",
            "Paddle movement with acceleration/deceleration physics"
        ],
        "pseudocode": """
            // Game Initialization
            function initGame(gameMode, difficulty):
                reset scores to 0
                reset paddles to center positions
                if gameMode is 'singlePlayer':
                    set AI difficulty based on selection
                setupControls(gameMode)
                launchBall()

            // Main Game Loop
            function gameLoop(timestamp):
                calculateDeltaTime()
                
                // Update positions
                updateBallPosition()
                if gameMode is 'singlePlayer':
                    updateAIPaddle(difficulty)
                else:
                    updatePlayer2Paddle()
                updatePlayer1Paddle()
                
                // Check collisions
                if ballHitsPaddle():
                    calculateReflection()
                    increaseBallSpeed()
                
                if ballHitsWall():
                    bounceBall()
                
                if ballPassesPaddle():
                    scorePoint()
                    if gameOver():
                        showVictoryScreen()
                    else:
                        resetBall()
                
                renderGame()
                requestAnimationFrame(gameLoop)

            // AI Behavior
            function updateAIPaddle(difficulty):
                switch difficulty:
                    case 'easy':
                        // Simple follower with delay and limited movement
                        move towards ball with 60% speed and 500ms reaction delay
                    case 'medium':
                        // Predictive movement with occasional mistakes
                        predict ball trajectory with 85% accuracy
                        move towards predicted position with 80% speed
                    case 'hard':
                        // Advanced prediction with near-perfect positioning
                        accurately predict ball trajectory
                        move strategically to optimize return angle
                        adapt to player patterns
    """
    },
    "AstroPortifolioBuilder": {
        "component_name": "Astro App for Portfolio Builder",
        "description": "Astro app for creating and managing personal developer portfolios or professional websites. The app is written in JavaScript and uses Astro's content-focused architecture. The app is deployed locally on port 4321. Functionality includes: 1. Markdown-based content editing for portfolio sections (about, projects, skills) 2. Theme customization with predefined color schemes and layouts 3. Project showcase with filterable gallery and detailed case studies 4. Responsive image optimization using Astro's built-in image processing 5. Social media link integration with icon customization 6. Single command deployment to GitHub Pages, Netlify, or Vercel",
        "interfaces": "Clean, minimal UI with customizable theming and the Astro logo in the footer next to 'Built with Astro' text. Admin interface consists of a simple sidebar navigation with content section tabs, drag-and-drop content reordering, and live preview panel. Public-facing portfolio view includes responsive hero section, animated skill bars, project cards with hover effects, and contact form. Mobile-friendly design automatically adapts layout for smaller screens with appropriate touch targets. Simple markdown editor for content with basic formatting controls.",
        "algorithms": [
            "Static site generation from markdown content files",
            "Optimal asset loading with automatic image optimization",
            "Content collection organization for portfolio sections",
            "Minimal JavaScript approach using Astro islands only where needed",
            "Theming system with CSS variable injection",
            "Build-time SEO optimization for portfolio discoverability"
        ],
        "pseudocode": "// Site Generation Process\ninitializePortfolio() {\n  // Load user configuration\n  const config = loadUserConfig('./portfolio.config.js')\n  \n  // Process content collections\n  const sections = loadMarkdownContent('./content/')\n  \n  // Generate static pages\n  generatePages([\n    {route: '/', template: 'home', data: sections.home},\n    {route: '/about', template: 'about', data: sections.about},\n    {route: '/projects', template: 'projects', data: sections.projects},\n    {route: '/contact', template: 'contact', data: config.contactInfo}\n  ])\n  \n  // Apply selected theme\n  const themeVariables = loadTheme(config.theme)\n  injectCSSVariables(themeVariables)\n  \n  // Optimize assets for production\n  if (isProduction) {\n    optimizeImages('./public/images/')\n    minifyCSS('./public/styles/')\n  }\n}"
    },
    "ViteInteractiveMusicStudio": {
        "component_name": "Vite App for Simple Beat Maker",
        "description": "Vite app for creating simple music beats in the browser. Written in JavaScript and uses Vite. Deployed locally on port 5173. Functionality includes: 1. Grid-based beat pattern creator 2. Basic drum kit sounds (kick, snare, hi-hat) 3. Playback controls (play, pause, stop) 4. Tempo adjustment 5. Project saving to local storage 6. Clear interface with prominent Vite logo and 'Built with Vite' text in the footer",
        "interfaces": "Clean, minimal GUI with a central grid for creating beat patterns. Each row represents a different drum sound, and columns represent time divisions. Clicking cells toggles sounds on/off. Contains play/pause button, tempo slider, and save button. The Vite logo appears in the top right corner of the application.",
        "algorithms": [
            "Web Audio API for sound playback",
            "Beat timing using JavaScript intervals",
            "Pattern storage and retrieval",
            "Simple sound triggering",
            "Local storage for saving projects"
        ],
        "pseudocode": "initApp():\n    initialize audio context\n    load basic sound samples\n    set up default empty pattern grid\n    display UI\n\ntoggleCell(row, column):\n    if cell is active:\n        deactivate cell\n    else:\n        activate cell\n    update visual state\n\nplayBeat():\n    set current position to 0\n    start interval based on tempo\n    for each tick:\n        play sounds for active cells at current position\n        highlight current column\n        advance position\n\nsaveProject():\n    store current pattern in local storage\n    display confirmation message"
    },
    "CalculatorNextJS": {
        "component_name": "NextJS App for Basic Calculator",
        "description": "NextJS app for performing simple arithmetic calculations. The app is written in JavaScript and uses NextJS without any external APIs. The app is deployed locally on port 3000. Functionality includes: 1. Basic operations (addition, subtraction, multiplication, division) 2. Clear button to reset calculations 3. Decimal point support 4. Simple calculation history list. The interface prominently displays the NextJS logo with 'Built with NextJS' text in the corner.",
        "interfaces": "Clean, minimal GUI designed as a calculator with a display at the top showing current input and result. Button grid below includes numbers 0-9, operators (+, -, *, /), decimal point, equals, and clear buttons. Small calculation history panel shows the last 5 calculations. NextJS logo appears in the bottom right corner of the calculator. Dark/light theme toggle in the top corner adapts the calculator appearance.",
        "algorithms": [
            "Basic arithmetic operation handling",
            "Input validation and formatting",
            "Calculation history management with local state"
        ],
        "pseudocode": ""
    },
    "TaskManagementNextJS": {
        "component_name": "NextJS App for Task Management",
        "description": "NextJS app for managing personal and team tasks. The app is written in JavaScript and uses NextJS with React. The app is deployed locally on port 3000. Functionality includes: 1. Creating and organizing tasks with due dates 2. Assigning tasks to team members 3. Setting task priorities (Low, Medium, High) 4. Tracking task completion status 5. Simple dashboard with task overview. The interface includes the NextJS logo in the navigation bar and 'Built with NextJS' text in the footer.",
        "interfaces": "Clean, minimal GUI with light/dark mode toggle. Home dashboard displays task cards organized by status (To Do, In Progress, Completed). Task creation form includes fields for title, description, due date, priority, and assignee. Task cards show title, priority indicator, due date, and assignee avatar. Mobile-friendly design adjusts layout for smaller screens. NextJS logo appears in the top navigation bar and loading states.",
        "algorithms": [
            "Task filtering and sorting by various properties (date, priority, assignee)",
            "Basic search functionality for finding specific tasks",
            "Task completion tracking with progress indicators",
            "Simple data persistence using NextJS API routes",
            "User authentication and basic authorization"
        ],
        "pseudocode": "// Task Management Core\ninitializeTaskApp() {\n  // Load initial tasks\n  const tasks = fetchTasks()\n  \n  // Set up UI components\n  renderDashboard(tasks)\n  \n  // Handle task creation\n  onTaskFormSubmit((taskData) => {\n    const newTask = createTask(taskData)\n    addTaskToList(newTask)\n    updateTaskCount()\n  })\n  \n  // Handle task status changes\n  onTaskStatusChange((taskId, newStatus) => {\n    updateTaskStatus(taskId, newStatus)\n    moveTaskCard(taskId, newStatus)\n    updateCompletionStats()\n  })\n}"
    },
    "SvelteMemoryGame": {
        "component_name": "Svelte App for Memory Match Game",
        "description": "Svelte app for a classic memory matching card game, written in JavaScript and uses Svelte. The application is deployed locally on port 5000 and runs entirely in the browser. Key features include:\n1. Interactive grid of cards that can be flipped with smooth animations\n2. Randomized card placement at the start of each game\n3. Move counter to track player performance\n4. Timer to measure how long it takes to complete the game\n5. Different difficulty levels (4x4, 6x6 grids)\n6. Celebration animation when all matches are found\n7. Svelte logo prominently displayed in the game header with \"Created with Svelte\" text in the footer",
        "interfaces": "The UI features a playful, colorful design with card-flipping animations:\n- Game header with Svelte logo and game title\n- Central game board displaying a grid of face-down cards\n- Score panel showing moves taken and elapsed time\n- Restart button to reset the game\n- Difficulty selector (Easy/Medium/Hard)\n- Game completion modal with results and replay option\n\nInteraction methods include mouse clicks or touch taps to flip cards. The application is fully responsive and adapts to both desktop and mobile screens. Each card has a satisfying flip animation implemented using Svelte transitions. The Svelte logo appears in the top header with a subtle spinning animation on game completion, and \"Created with Svelte\" text appears in the footer.",
        "algorithms": "1. Fisher-Yates shuffle algorithm to randomize card positions\n2. Card matching logic to check if two flipped cards match\n3. Game state management using Svelte stores\n4. Timer implementation using reactive declarations\n5. Card flip animation using Svelte transition directives",
        "pseudocode": "// Main Game component\ncomponent MemoryGame {\n  // Game state variables\n  let cards = [];\n  let flippedCards = [];\n  let matchedPairs = 0;\n  let moves = 0;\n  let gameStarted = false;\n  let gameCompleted = false;\n  let timer = 0;\n  let difficulty = 'easy';\n  \n  // Initialize game\n  function initGame(difficultyLevel) {\n    difficulty = difficultyLevel;\n    const gridSize = getGridSize(difficulty);\n    const totalPairs = (gridSize * gridSize) / 2;\n    \n    // Create card pairs\n    cards = createCardPairs(totalPairs);\n    \n    // Shuffle cards using Fisher-Yates algorithm\n    cards = shuffleCards(cards);\n    \n    // Reset game state\n    flippedCards = [];\n    matchedPairs = 0;\n    moves = 0;\n    gameStarted = false;\n    gameCompleted = false;\n    timer = 0;\n  }\n  \n  // Handle card click\n  function handleCardClick(cardId) {\n    // Start timer on first move\n    if (!gameStarted) {\n      gameStarted = true;\n      startTimer();\n    }\n    \n    // Don't allow more than 2 cards flipped or clicking on matched cards\n    const card = cards.find(c => c.id === cardId);\n    if (flippedCards.length >= 2 || card.matched || flippedCards.includes(cardId)) {\n      return;\n    }\n    \n    // Flip card\n    flippedCards = [...flippedCards, cardId];\n    \n    // Check for match if two cards are flipped\n    if (flippedCards.length === 2) {\n      moves++;\n      const [firstCardId, secondCardId] = flippedCards;\n      const firstCard = cards.find(c => c.id === firstCardId);\n      const secondCard = cards.find(c => c.id === secondCardId);\n      \n      if (firstCard.value === secondCard.value) {\n        // Cards match\n        cards = cards.map(c => \n          c.id === firstCardId || c.id === secondCardId \n            ? {...c, matched: true} \n            : c\n        );\n        matchedPairs++;\n        flippedCards = [];\n        \n        // Check if game completed\n        if (matchedPairs === cards.length / 2) {\n          gameCompleted = true;\n          stopTimer();\n          showCelebration();\n        }\n      } else {\n        // Cards don't match, flip back after delay\n        setTimeout(() => {\n          flippedCards = [];\n        }, 1000);\n      }\n    }\n  }\n  \n  // Start timer\n  function startTimer() {\n    const timerInterval = setInterval(() => {\n      timer++;\n    }, 1000);\n    \n    return () => clearInterval(timerInterval); // Cleanup\n  }\n}"
        },
    "TypescriptQuizBuilder": {
        "component_name": "TypeScript App for Interactive Quiz Builder",
        "description": "TypeScript app for creating, taking, and tracking progress on custom quizzes. Written in JavaScript and uses TypeScript for type safety without any external dependencies. Deployed locally on port 5000 through a simple HTTP server. Features include:\n\n1. Quiz creation interface with support for multiple question types (multiple choice, true/false, short answer)\n2. Question editor with ability to add, edit, and delete questions within a quiz\n3. Quiz-taking mode that presents questions and collects user answers\n4. Immediate feedback on quiz completion with detailed score breakdown\n5. Local storage persistence for saving quizzes, user answers, and progress\n6. History of previous quiz attempts with performance tracking\n\nThe interface includes the TypeScript logo in the header section and displays 'Built with TypeScript' in the application footer.",
        "interfaces": "The user interface features a clean, minimal design that works across devices without external UI libraries:\n\n- Header with TypeScript logo and application title\n- Simple navigation tabs for 'Create Quiz', 'My Quizzes', and 'Quiz History'\n- Form-based question editor with:\n  * Question text input field\n  * Question type selector (multiple choice, true/false, short answer)\n  * Dynamic options fields for multiple choice questions\n  * Correct answer marking functionality\n  * Add/Save/Delete question buttons\n- Quiz-taking interface with:\n  * One question displayed at a time with navigation buttons\n  * Answer selection/input area specific to question type\n  * Submit button to finalize answers\n  * Progress indicator showing current question/total questions\n- Results screen showing:\n  * Overall score (number correct and percentage)\n  * Question-by-question breakdown of correct/incorrect answers\n  * Option to review incorrect answers\n  * Button to retake quiz or return to main menu\n- Responsive layout using CSS Grid and Flexbox (no external frameworks)",
        "algorithms": "1. Form validation and type checking for question creation\n2. Answer validation based on question type (exact match for short answer, index match for multiple choice)\n3. Score calculation algorithm that processes user responses against correct answers\n4. Local storage serialization and deserialization with type safety checks\n5. Quiz state management for tracking current question, user selections, and navigation\n6. Performance analytics calculation for tracking improvement across multiple attempts",
        "pseudocode": "// Core types for the quiz application\n\ntype QuestionType = 'multiple-choice' | 'true-false' | 'short-answer';\n\ninterface Question {\n  id: string;\n  type: QuestionType;\n  text: string;\n  points: number;\n}\n\ninterface MultipleChoiceQuestion extends Question {\n  type: 'multiple-choice';\n  options: string[];\n  correctOptionIndex: number;\n}\n\ninterface TrueFalseQuestion extends Question {\n  type: 'true-false';\n  correctAnswer: boolean;\n}\n\ninterface ShortAnswerQuestion extends Question {\n  type: 'short-answer';\n  correctAnswer: string;\n}\n\ntype AnyQuestion = MultipleChoiceQuestion | TrueFalseQuestion | ShortAnswerQuestion;\n\ninterface Quiz {\n  id: string;\n  title: string;\n  description: string;\n  questions: AnyQuestion[];\n}\n\ninterface UserAnswer {\n  questionId: string;\n  answer: string | number | boolean; // Type depends on question type\n}\n\ninterface QuizAttempt {\n  id: string;\n  quizId: string;\n  date: Date;\n  answers: UserAnswer[];\n  score: number;\n  maxScore: number;\n}\n\nclass QuizManager {\n  private quizzes: Quiz[] = [];\n  private attempts: QuizAttempt[] = [];\n  private currentQuiz: Quiz | null = null;\n  private currentAnswers: UserAnswer[] = [];\n  \n  constructor() {\n    this.loadFromStorage();\n  }\n  \n  // Quiz Creation Methods\n  \n  createQuiz(title: string, description: string): Quiz {\n    const newQuiz: Quiz = {\n      id: this.generateId(),\n      title,\n      description,\n      questions: []\n    };\n    \n    this.quizzes.push(newQuiz);\n    this.currentQuiz = newQuiz;\n    this.saveToStorage();\n    return newQuiz;\n  }\n  \n  addQuestion(question: Omit<AnyQuestion, 'id'>): void {\n    if (!this.currentQuiz) throw new Error('No active quiz');\n    \n    const newQuestion = {\n      ...question,\n      id: this.generateId()\n    } as AnyQuestion;\n    \n    this.currentQuiz.questions.push(newQuestion);\n    this.saveToStorage();\n  }\n  \n  editQuestion(questionId: string, updatedQuestion: Omit<AnyQuestion, 'id'>): void {\n    if (!this.currentQuiz) throw new Error('No active quiz');\n    \n    const index = this.currentQuiz.questions.findIndex(q => q.id === questionId);\n    if (index === -1) throw new Error('Question not found');\n    \n    this.currentQuiz.questions[index] = {\n      ...updatedQuestion,\n      id: questionId\n    } as AnyQuestion;\n    \n    this.saveToStorage();\n  }\n  \n  deleteQuestion(questionId: string): void {\n    if (!this.currentQuiz) throw new Error('No active quiz');\n    this.currentQuiz.questions = this.currentQuiz.questions.filter(q => q.id !== questionId);\n    this.saveToStorage();\n  }\n  \n  // Quiz Taking Methods\n  \n  startQuiz(quizId: string): void {\n    const quiz = this.findQuiz(quizId);\n    if (!quiz) throw new Error('Quiz not found');\n    if (quiz.questions.length === 0) throw new Error('Quiz has no questions');\n    \n    this.currentQuiz = quiz;\n    this.currentAnswers = [];\n  }\n  \n  getCurrentQuestion(index: number): AnyQuestion | null {\n    if (!this.currentQuiz) return null;\n    return this.currentQuiz.questions[index] || null;\n  }\n  \n  submitAnswer(questionId: string, answer: string | number | boolean): void {\n    if (!this.currentQuiz) throw new Error('No active quiz');\n    \n    // Remove previous answer to this question if it exists\n    this.currentAnswers = this.currentAnswers.filter(a => a.questionId !== questionId);\n    \n    // Add the new answer\n    this.currentAnswers.push({\n      questionId,\n      answer\n    });\n  }\n  \n  submitQuiz(): QuizAttempt {\n    if (!this.currentQuiz) throw new Error('No active quiz');\n    \n    const score = this.calculateScore();\n    const maxScore = this.calculateMaxScore();\n    \n    const attempt: QuizAttempt = {\n      id: this.generateId(),\n      quizId: this.currentQuiz.id,\n      date: new Date(),\n      answers: [...this.currentAnswers],\n      score,\n      maxScore\n    };\n    \n    this.attempts.push(attempt);\n    this.saveToStorage();\n    \n    return attempt;\n  }\n  \n  // Score Calculation\n  \n  private calculateScore(): number {\n    if (!this.currentQuiz) return 0;\n    \n    return this.currentQuiz.questions.reduce((score, question) => {\n      const userAnswer = this.currentAnswers.find(a => a.questionId === question.id);\n      if (!userAnswer) return score; // No answer provided\n      \n      const isCorrect = this.checkAnswer(question, userAnswer.answer);\n      return isCorrect ? score + question.points : score;\n    }, 0);\n  }\n  \n  private calculateMaxScore(): number {\n    if (!this.currentQuiz) return 0;\n    return this.currentQuiz.questions.reduce((total, q) => total + q.points, 0);\n  }\n  \n  private checkAnswer(question: AnyQuestion, answer: string | number | boolean): boolean {\n    switch (question.type) {\n      case 'multiple-choice':\n        return (answer as number) === (question as MultipleChoiceQuestion).correctOptionIndex;\n      \n      case 'true-false':\n        return (answer as boolean) === (question as TrueFalseQuestion).correctAnswer;\n      \n      case 'short-answer':\n        return this.normalizeAnswer(answer as string) === \n               this.normalizeAnswer((question as ShortAnswerQuestion).correctAnswer);\n      \n      default:\n        return false;\n    }\n  }\n  \n  private normalizeAnswer(text: string): string {\n    return text.trim().toLowerCase();\n  }\n  \n  // Report Generation\n  \n  getQuizReport(attemptId: string): {\n    quiz: Quiz,\n    attempt: QuizAttempt,\n    questionResults: {question: AnyQuestion, userAnswer: any, correct: boolean}[]\n  } | null {\n    const attempt = this.attempts.find(a => a.id === attemptId);\n    if (!attempt) return null;\n    \n    const quiz = this.findQuiz(attempt.quizId);\n    if (!quiz) return null;\n    \n    const questionResults = quiz.questions.map(question => {\n      const userAnswer = attempt.answers.find(a => a.questionId === question.id);\n      const correct = userAnswer ? this.checkAnswer(question, userAnswer.answer) : false;\n      \n      return {\n        question,\n        userAnswer: userAnswer?.answer,\n        correct\n      };\n    });\n    \n    return {\n      quiz,\n      attempt,\n      questionResults\n    };\n  }\n  \n  // Utility Methods\n  \n  private findQuiz(id: string): Quiz | undefined {\n    return this.quizzes.find(quiz => quiz.id === id);\n  }\n  \n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substring(2);\n  }\n  \n  private saveToStorage(): void {\n    localStorage.setItem('quizzes', JSON.stringify(this.quizzes));\n    localStorage.setItem('attempts', JSON.stringify(this.attempts));\n  }\n  \n  private loadFromStorage(): void {\n    const storedQuizzes = localStorage.getItem('quizzes');\n    if (storedQuizzes) {\n      this.quizzes = JSON.parse(storedQuizzes);\n    }\n    \n    const storedAttempts = localStorage.getItem('attempts');\n    if (storedAttempts) {\n      this.attempts = JSON.parse(storedAttempts);\n    }\n  }\n}"
    },
    "QwikPomodoroApp": {
        "component_name": "Qwik App for Pomodoro Focus Timer",
        "description": "Qwik app for productivity enhancement using the Pomodoro technique, written in TypeScript using the Qwik framework. Deployed locally on port 5173. The application features:\n\n1. Zero-delay initial load leveraging Qwik's resumability model\n2. Configurable work sessions (25 minutes) and break sessions (5 minutes)\n3. Visual circular progress indicator showing remaining time\n4. Session counter tracking completed work intervals\n5. Audio notifications for session transitions\n6. Simple task input field for focus tracking\n7. Responsive design for desktop and mobile devices\n8. Branded interface with Qwik lightning bolt logo in header and 'Powered by Qwik' text in footer",
        "interfaces": "The interface provides a clean, distraction-free productivity environment with:\n\n- Qwik lightning bolt logo prominently displayed in the top-right corner\n- Large, central timer display showing minutes:seconds remaining\n- Circular progress indicator that visually depletes as time passes\n- Clear control buttons: Start, Pause, and Reset\n- Visual distinction between work mode (deep red) and break mode (calming blue)\n- Session type indicator showing 'Focus Time' or 'Break Time'\n- Session counter displaying number of completed work intervals\n- Simple settings panel for customizing session durations\n- Task input field with placeholder 'What are you working on?'\n- 'Powered by Qwik' branding in the footer\n\nThe design emphasizes readability and focus with minimal distractions.",
        "algorithms": [
            "1. Fine-grained reactive timer using Qwik's useStore and useVisibleTask$",
            "2. Session state management with Qwik's reactive primitives",
            "3. Progress calculation using useComputed$ for the visual timer indicator",
            "4. Audio notification system triggered on session completion",
            "5. Responsive UI state handling using Qwik's component architecture"
        ],
        "pseudocode": "component$ PomodoroTimer() {\n  // State management using Qwik's reactive store\n  const state = useStore({\n    minutes: 25,\n    seconds: 0,\n    isRunning: false,\n    isWorkSession: true,\n    completedSessions: 0,\n    workDuration: 25,\n    breakDuration: 5,\n    currentTask: ''\n  });\n\n  // Timer implementation with Qwik's useVisibleTask$\n  useVisibleTask$(({ cleanup }) => {\n    if (!state.isRunning) return;\n    \n    const timer = setInterval(() => {\n      // Decrement timer and handle session transitions\n      if (state.seconds === 0) {\n        if (state.minutes === 0) {\n          // Session completed logic\n          playNotification();\n          toggleSessionType();\n        } else {\n          state.minutes--;\n          state.seconds = 59;\n        }\n      } else {\n        state.seconds--;\n      }\n    }, 1000);\n    \n    cleanup(() => clearInterval(timer));\n  });\n\n  // Calculate progress with Qwik's useComputed$\n  const progressPercentage = useComputed$(() => {\n    const totalSeconds = state.isWorkSession ? \n      state.workDuration * 60 : \n      state.breakDuration * 60;\n    const remainingSeconds = (state.minutes * 60) + state.seconds;\n    return ((totalSeconds - remainingSeconds) / totalSeconds) * 100;\n  });\n\n  return (\n    <div class={state.isWorkSession ? 'work-mode' : 'break-mode'}>\n      {/* Main timer UI components */}\n      <TimerDisplay minutes={state.minutes} seconds={state.seconds} progress={progressPercentage.value} />\n      <SessionControls state={state} />\n      <SessionCounter count={state.completedSessions} />\n      <TaskInput value={state.currentTask} />\n    </div>\n  );\n}"
        },
    "AngularTaskTracker": {
        "component_name": "Angular App for Personal Task Tracker",
        "description": "Angular app for managing daily tasks and tracking personal productivity. Written in TypeScript and uses Angular framework. Key features include:\n1. Task creation with title, completed/pending status and description. \n2. Simple progress tracking with completed vs. pending task statistics\n3. Local storage integration for persisting tasks between sessions\n\"Created with Angular\" text in the footer.",
        "interfaces": "The interface uses a clean, minimalist design with Angular Material components for consistency. User controls include:\n- Top header with app title and a button to add new tasks\n- Left panel with task list\n- Right panel showing selected task details and edit options\n- Bottom footer with \"Created with Angular\" branding\n- Floating add button for quick task creation.",
        "algorithms": "1. Statistics Calculation: Simple aggregation of task data to display completion rates\n2. Data Persistence: Local storage read/write operations with JSON serialization\n3. Reactive Form Handling: Uses Angular's reactive forms pattern for efficient form validation and data binding"
    },
    "AngularTicTacToe": {
        "component_name": "Angular App for Tic Tac Toe",
        "description": "Angular app for a classic game of Tic Tac Toe. Written in TypeScript using the core Angular framework. Deployed locally on port 4200. Key features include:\n1. Interactive 3x3 game board for two players (X and O)\n2. Turn indicator showing which player's turn it is\n3. Automatic win detection and game over state\n4. Reset button to start a new game\n5. Score tracking for multiple rounds\nThe Angular logo is displayed in the header with \"Created with Angular\" text in the footer.",
        "interfaces": "The interface features a clean, centered game board with a minimalist design. The Angular logo appears in the top-right corner of the page. User controls include:\n- 3x3 grid of clickable cells for gameplay\n- Player turn indicator above the board (\"X's Turn\" or \"O's Turn\")\n- Game status display for showing win/draw messages\n- Score counter showing wins for each player\n- Reset button to start a new game\n- Footer with \"Created with Angular\" text\nThe interface is responsive and works on both desktop and mobile devices with touch support.",
        "algorithms": "1. Game State Management: Tracks the current state of the board using a 3x3 matrix\n2. Win Condition Checking: Evaluates rows, columns, and diagonals after each move to detect wins\n3. Turn Alternation: Simple toggling between players X and O\n4. Input Validation: Prevents moves on already occupied cells\n5. Game Reset: Clears the board while maintaining the score count"
    },
    "AngularCounter": {
        "component_name": "Angular App for Simple Counter",
        "description": "Angular app for a basic interactive counter. Written in TypeScript using the core Angular framework. Deployed locally on port 4200. Key features include:\n1. Increment and decrement buttons to change the counter value\n2. Reset button to return the counter to zero\n3. Visual color feedback that changes as the counter reaches certain thresholds\nThe Angular logo is displayed in the header with \"Created with Angular\" text in the footer.",
        "interfaces": "The interface consists of a single centered card with a minimalist design. The Angular logo appears in the top-right corner of the page. User controls include:\n- Large counter display showing the current value\n- Three buttons: increment (+), decrement (-), and reset\n- Simple visual feedback with color changes based on value (red for negative, green for positive, blue for zero)\n- Footer with \"Created with Angular\" text\nThe interface is responsive and works on both desktop and mobile devices.",
        "algorithms": "1. Counter Logic: Basic increment/decrement operations on a numeric value\n2. Visual Feedback: Simple conditional logic to apply different styles based on counter value\n3. Component State Management: Standard Angular property binding to maintain and display counter state"
    },
    "NuxtNoteTaking": {
        "component_name": "Nuxt App for Note Taking",
        "description": "Nuxt app for creating and managing simple notes. Deployed locally on port 3000. Features include:\n1. Create plain text notes\n2. View list of all notes\n3. Edit existing notes\n4. Delete unwanted notes\nThe application displays the Nuxt logo in the header with 'Created with Nuxt' text in the footer.",
        "interfaces": "The user interface includes:\n- Header with Nuxt logo and 'New Note' button\n- Sidebar with list of note titles\n- Main content area for viewing and editing notes\n- Simple formatting toolbar\n- Footer with 'Created with Nuxt' branding\nThe design is clean and minimalist with focus on readability.",
        "algorithms": "1. Note Storage: Saves notes to browser's local storage\n2. List Management: Sorts notes by creation or modification date\n3. Text Formatting: Basic text styling capabilities",
        "pseudocode": "// Notes Component\ndefineComponent({\n  setup() {\n    const notes = ref([])\n    const currentNote = ref(null)\n    \n    // Load notes from storage\n    onMounted(() => {\n      notes.value = loadNotesFromStorage()\n    })\n    \n    // Create new note\n    function createNote() {\n      const newNote = {\n        id: generateId(),\n        title: 'Untitled Note',\n        content: '',\n        date: new Date()\n      }\n      notes.value.push(newNote)\n      currentNote.value = newNote\n      saveNotesToStorage(notes.value)\n    }\n    \n    // Save note changes\n    function saveNote() {\n      saveNotesToStorage(notes.value)\n    }\n    \n    return {\n      notes,\n      currentNote,\n      createNote,\n      saveNote\n    }\n  }\n})"
    },
    "SlidevAIPresentation": {
        "component_name": "Slidev App for AI Business Performance Analytics",
        "description": "Slidev app for showcasing how AI implementation boosts company performance metrics, written in JavaScript and utilizing the Slidev framework. Deployed locally on port 3030 and accessible via web browser. The presentation includes:\n\n1. Interactive data visualizations comparing pre-AI and post-AI implementation metrics across various industries\n2. Animated transition slides that demonstrate ROI calculation methodologies for AI investments\n3. Case study galleries with expandable details for companies that successfully integrated AI\n4. Pre-configured interactive Q&A slide templates for structured audience discussion\n5. AI adoption roadmap template with customizable milestones and KPIs\n6. PDF export functionality with embedded analytics and reference links\n7. Speaker notes section with AI-powered timing suggestions based on content complexity\n\nThe presentation prominently displays the Slidev logo in the bottom corner of each slide and includes a \"Created with Slidev\" watermark on the title and ending slides.",
        "interfaces": "The presentation interface features a clean, corporate-focused design with a blue and white color scheme that emphasizes data clarity. The UI includes:\n\n- Main slide view with progress indicator showing current slide position\n- Navigation controls including keyboard shortcuts (arrow keys, space bar), touch swipe support for mobile/tablet viewing, and clickable navigation arrows\n- Presentation overview mode activated by pressing 'o' to see all slides as thumbnails\n- Drawer menu accessible from the bottom-right corner containing slide outline and theme options\n- Fullscreen toggle button with 'f' keyboard shortcut\n- Presenter view (accessed by pressing 'p') showing current slide, next slide, speaker notes, and timer\n- Built-in Slidev chart components displaying key performance metrics with click-to-reveal additional details\n- Static AI ROI comparison tables with pre-calculated scenarios for different company sizes and industries\n- Case study carousel with navigation dots and expanding information panels\n- The Slidev logo appears in the bottom-right corner of each slide with a subtle animation when transitioning between slides\n- Reference slide at the end with key sources and recommended reading, featuring Slidev branding integrated into the design",
        "algorithms": "1. Slide Transition Management: Controls the flow and animation between slides based on content type and narrative progression\n2. Data Visualization Rendering: Translates company performance metrics into appropriate chart types (bar charts, line graphs, scatter plots) based on data characteristics\n3. ROI Calculation Framework: Implements formulas for calculating and displaying return on investment for AI implementation across different business models\n4. Discussion Point Management: Organizes and displays key discussion topics and takeaways on dedicated Q&A slides throughout the presentation\n5. Case Study Filtering: Organizes and presents company success stories based on industry, company size, and AI technology type",
        "pseudocode": ""
    },
    "RemixDailyPlanner": {
        "component_name": "Remix App for Daily Task Planner",
        "description": "Remix app for managing daily tasks without external dependencies, written in TypeScript and using Remix. The application allows users to add, edit, and track their tasks in a simple interface. Deployed locally on port 3000. Key features include:\n      1. Task creation with title, description, and priority level\n      2. Local storage persistence for offline access\n      3. Simple drag-and-drop functionality for organizing tasks\n      4. Dark and light mode for better readability\n      5. Task filtering by priority and completion status\n      6. \"Created with Remix\" branding displayed in the footer and header",
        "interfaces": "The UI consists of a single-page layout with a task input section and a list of tasks displayed below. Users can interact with tasks using:\n      - A simple input field for adding new tasks\n      - A draggable task list for reordering items\n      - A checkbox for marking tasks as complete\n      - A filter dropdown to sort tasks by priority or status\n      - The Remix logo prominently displayed in the header and settings menu",
        "algorithms": 
            "Local storage-based task persistence, Drag-and-drop task reordering, Task filtering using array sorting and filtering functions, State management with Remix loaders and actions, Offline-first approach using browser cache for storing tasks",
        "pseudocode": ""
    },
    "RemixTicTacToe": {
        "component_name": "Remix App for Tic Tac Toe",
        "description": "Remix app for a classic game of Tic Tac Toe. Writen using the Remix framework. Key features include:\n1. Interactive 3x3 game board for two players (X and O)\n2. Turn indicator showing which player's turn it is\n3. Automatic win detection and game over state\n4. Reset button to start a new game\n5. Score tracking for multiple rounds\nThe Remix logo is displayed in the header with \"Created with Remix\" text in the footer.",
        "interfaces": "The interface features a clean, centered game board with a minimalist design. The Remix logo appears in the top-right corner of the page. User controls include:\n- 3x3 grid of clickable cells for gameplay\n- Player turn indicator above the board (\"X's Turn\" or \"O's Turn\")\n- Game status display for showing win/draw messages\n- Score counter showing wins for each player\n- Reset button to start a new game\n- Footer with \"Created with Remix\" text\nThe interface is responsive and works on both desktop and mobile devices with touch support.",
        "algorithms": "1. Game State Management: Tracks the current state of the board using a 3x3 matrix\n2. Win Condition Checking: Evaluates rows, columns, and diagonals after each move to detect wins\n3. Turn Alternation: Simple toggling between players X and O\n4. Input Validation: Prevents moves on already occupied cells\n5. Game Reset: Clears the board while maintaining the score count"
    },
    "RemotionCountDown": {
        "component_name": "Dynamic Countdown Video Generator with Remotion",
        "description": "A simple countdown video generator built with Remotion, The application allows users to generate a custom countdown animation with dynamic text, background colors, and smooth transitions.",
        "interfaces": "A simple, user-friendly UI with an editor panel to customize countdown duration, fonts, colors, and animations. Users can see changes instantly with a live preview and export their video with a single click as an MP4. The interface is mobile-friendly, ensuring a smooth experience on any screen size.",
        "algorithms" : "1. Frame-by-frame countdown generation with smooth transitions\n2. Dynamic text rendering with customizable fonts and colors\n 3. Optimized animation timing for consistent playback speed\n4. Automatic video encoding for efficient MP4 export\n5. Real-time preview rendering with minimal performance overhead\n6. Responsive scaling to ensure correct proportions on all screen sizes",
        "pseudocode": ""
    },
    "RemotionQuoteToVideo": {
        "component_name": "Remotion App for Social Media Quote Generator",
        "description": "Remotion app for creating animated quote videos for social media. This lightweight application leverages Remotion to transform text quotes into shareable video clips with minimal user input. The app generates MP4 videos optimized for social platforms. Features include:\n\n1. Simple text input for quote and attribution\n2. Selection from 5 pre-designed animation styles (fade, slide, typewriter, reveal, bounce)\n3. Color theme customization with preset palettes or custom color picker\n4. One-click export for common social media dimensions (square, portrait, landscape)\n5. Prominent display of Remotion logo and 'Created with Remotion' caption in the lower third of the output video",
        "interfaces": "The application features a minimal, user-friendly single-page interface with a clean design. The top header displays the application name 'Social Media Quote Generator' prominently.\n\nThe main workspace is divided into two sections: the input panel on the left and the live preview on the right. The input panel contains:\n- A text area for entering the quote\n- A smaller text input for attribution\n- A dropdown menu for selecting animation style\n- Color theme selection buttons with 6 preset options and a custom picker\n- Video dimension selector with thumbnail previews\n\nThe preview panel shows a real-time render of how the final video will appear, with controls for play/pause and a simple timeline scrubber. The Remotion logo appears in the bottom right corner of the interface.\n\nA prominent 'Generate Video' button at the bottom of the input panel initiates the rendering process, with a progress indicator showing render status.",
        "algorithms": "1. Text Layout Engine: Automatically calculates optimal text size, line breaks, and positioning based on quote length and selected output dimensions\n\n2. Animation Sequencer: Applies selected animation style to text elements with appropriate timing and easing functions\n\n3. Color Harmony Generator: Creates visually pleasing color combinations for text, background, and accent elements",
        "pseudocode": ""
    },
    "AdventureWebsite": {
        "component_name": "Adventure Website in Next.js",
        "description": "Create a professional looking website landing page. Informational mostly featuring images. Company is called 10,200’ and provides adventure experiences from Leadville, CO, the highest elevation city in the US. The website should have a professional footer that links to an about page for the company which mentions that the company is the premier adventure sports provider (use your imagination to promote the company further). The footer should also provide links for contact, book adventures and post comments.",
        "interfaces": "The website features a clean, modern design with a focus on adventure experiences. The header includes the company logo and navigation links to different sections of the site. The main content area showcases images of various adventures offered by the company, along with descriptions and booking options. The footer includes links to the about page, contact information, and social media profiles.",
        "algorithms": "The website uses responsive design principles to ensure it looks great on all devices. It includes a simple booking form that validates user input and submits the data to the server. The images are optimized for fast loading times, and the layout adapts to different screen sizes using CSS Grid and Flexbox.",
        "pseudocode": ""
    },
    "ExpressEventBackend": {
        "component_name": "Express App for Simple Event Management",
        "description": "Express app for basic event management and registration. Written in JavaScript and uses the Express framework, deployed locally on port 8000 with SQLite as the database backend. The app provides a straightforward RESTful API with the following functionality:\n\n1. Create, read, update, and delete events with details like title, date, location, and description\n2. Register attendees for events with basic contact information\n3. List upcoming events with optional filtering by date and category\n4. Basic reporting on event attendance and registration status",
        "interfaces": "Will serve a Swagger document as the only UI interface, accessible at the /docs endpoint. The API focuses on providing essential REST endpoints for simple event management operations.",
        "algorithms": "1. SQLite database integration using Sequelize with basic models for events and attendees\n2. CRUD operations implementation following REST principles\n3. Joi for request and response validation\n4. Simple query filtering for event listing and searches\n",
        "pseudocode": ""
    },
    "DjangoEventBackend": {
        "component_name": "Django App for Simple Event Management",
        "description": "Django app for basic event management and registration. Written in Python and uses Django framework, deployed locally on port 8000 with SQLite as the database backend. The app provides a straightforward RESTful API with the following functionality:\n\n1. Create, read, update, and delete events with details like title, date, location, and description\n2. Register attendees for events with basic contact information\n3. List upcoming events with optional filtering by date and category\n4. Basic reporting on event attendance and registration status",
        "interfaces": "Will serve a Swagger document as the only UI interface, accessible at the /docs endpoint. The API focuses on providing essential REST endpoints for simple event management operations.",
        "algorithms": "1. SQLite database integration using SQLAlchemy with basic models for events and attendees\n2. CRUD operations implementation following REST principles\n3. Pydantic models for request and response validation\n4. Simple query filtering for event listing and searches\n5. Basic dependency injection for database session management",
        "pseudocode": ""
    },
    "DotNetCoreEventBackend": {
        "component_name": ".NET Core API for Simple Event Management",
        "description": "ASP.NET Core Web API for basic event management and registration. Written in C# and uses ASP.NET Core framework, deployed locally on port 5000 with SQLite as the database backend. The app provides a straightforward RESTful API with the following functionality:\n\n1. Create, read, update, and delete events with details like title, date, location, and description\n2. Register attendees for events with basic contact information\n3. List upcoming events with optional filtering by date and category\n4. Basic reporting on event attendance and registration status",
        "interfaces": "Will serve a Swagger document as the only UI interface, accessible at the /swagger endpoint. The API focuses on providing essential REST endpoints for simple event management operations.",
        "algorithms": "1. SQLite database integration using Entity Framework Core with basic models for events and attendees\n2. CRUD operations implementation following REST principles with ApiController\n3. Data Transfer Objects (DTOs) for request and response validation\n4. LINQ queries for event listing and filtering operations\n5. Dependency injection for database context and service layer management",
        "pseudocode": ""
    },
    "FastAPIEventBackend": {
        "component_name": "FastAPI App for Simple Event Management",
        "description": "FastAPI app for basic event management and registration. Written in Python and uses FastAPI framework, deployed locally on port 8000 with SQLite as the database backend. The app provides a straightforward RESTful API with the following functionality:\n\n1. Create, read, update, and delete events with details like title, date, location, and description\n2. Register attendees for events with basic contact information\n3. List upcoming events with optional filtering by date and category\n4. Basic reporting on event attendance and registration status",
        "interfaces": "Will serve a Swagger document as the only UI interface, accessible at the /docs endpoint. The API focuses on providing essential REST endpoints for simple event management operations.",
        "algorithms": "1. SQLite database integration using SQLAlchemy with basic models for events and attendees\n2. CRUD operations implementation following REST principles\n3. Pydantic models for request and response validation\n4. Simple query filtering for event listing and searches\n5. Basic dependency injection for database session management",
        "pseudocode": ""
    },
    "FlaskEventBackend": {
        "component_name": "Flask App for Simple Event Management",
        "description": "Flask app for basic event management and registration. Written in Python and uses the Flask framework with SQLite as the database backend. The app provides a straightforward RESTful API with the following functionality:\n\n1. Create, read, update, and delete events with details.\n2. Register attendees for events with basic contact information\n3. Basic reporting on event attendance and registration status",
        "interfaces": "The API focuses on providing essential REST endpoints for simple event management operations.",
        "algorithms": "1. SQLite database integration using SQLAlchemy with basic models for events and attendees\n2. CRUD operations implementation following REST principles",
        "pseudocode": ""
    },
    "SpringBootEventBackend": {
        "component_name": "SpringBoot App for Simple Event Management",
        "description": "SpringBoot app for basic event management and registration. Written in Java and uses SpringBoot framework, deployed locally on port 8000 with SQLite as the database backend. The app provides a straightforward RESTful API with the following functionality:\n\n1. Create, read, update, and delete events with details like title, date, location, and description\n2. Register attendees for events with basic contact information\n3. List upcoming events with optional filtering by date and category\n4. Basic reporting on event attendance and registration status",
        "interfaces": "Will serve a Swagger document as the only UI interface, accessible at the /docs endpoint. The API focuses on providing essential REST endpoints for simple event management operations.",
        "algorithms": "1. SQLite database integration using Spring Data JPA with basic models for events and attendees\n2. CRUD operations implementation following REST principles\n3. Spring Boot validation annotations for request and response validation\n4. Simple query filtering for event listing and searches\n5. Basic dependency injection for database session management",
        "pseudocode": ""
    },
    "TemplateForStoreApp": {
        "component_name": "Design Template for a frontend only Store App",
        "description": "A template design for a store app that provides a clean and modern user interface that looks like what you would see in a small ETSI like store. The app does not include actual functionality but serves as a design reference. The template includes:\n\n1. A header with the store logo and navigation links\n2. A product grid layout showcasing various items with images, names, and prices\n3. A shopping cart icon in the header with a dropdown showing selected items\n4. A footer with links to contact information, privacy policy, and terms of service\n5. Responsive design for mobile and desktop views. The template is build using React and styled-components for CSS-in-JS styling.",
        "interfaces": "The template features a clean, modern design with a focus on usability. The header includes the store logo and navigation links to different sections of the site. The main content area showcases a grid layout of products with images, names, and prices. The footer includes links to contact information, privacy policy, and terms of service. Ignore the current interface in the current template.",
        "algorithms": "",
        "pseudocode": ""
    },
    "JazzFestGrid": {
        "component_name": "Jazz Festival Grid",
        "description": "A simple React App for a jazz festival grid (which include Jazzfest events and events at other venues). The App displays a grid by day,time and venue with performances shown in the grid. You may also list artists, their performance times, and stages. The layout is responsive and adjusts to different screen sizes.",
        "interfaces": "The grid layout is designed to be clean and easy to read. You can also browse by artist, venue, genre and day. You could also search across the database. The app is responsive and adjusts to different screen sizes. The UI is fun and Jazzy with some cool background. The UI is built using React and styled-components for CSS-in-JS styling.",
        "algorithms": "",
        "pseudocode": ""
    },
    "KotlinTodoApp": {
        "component_name": "Kotlin Android App for Todo List",
        "description": "Native Android application for managing daily tasks built with Kotlin. "
                    "The app follows Material Design guidelines and uses modern Android development practices. "
                    "Key features include: 1. Creating and managing tasks with title and description "
                    "2. Setting task priorities (Low, Medium, High) and due dates "
                    "3. Marking tasks as complete "
                    "4. Filtering and sorting tasks by status, priority, or due date "
                    "5. Simple statistics dashboard showing completion rates "
                    "6. Local data persistence using Room database",
        "interfaces": "Material Design UI with a main task list screen showing task cards, floating action button "
                    "for adding new tasks, swipe gestures for quick actions (swipe right to complete, left to delete), "
                    "bottom navigation for switching between All/Active/Completed task views, and a simple settings screen "
                    "for app preferences. Supports both light and dark themes following system settings.",
        "algorithms": "1. Local data persistence with Room database and repository pattern "
                    "2. MVVM architecture with ViewModels and LiveData for state management "
                    "3. Task sorting and filtering using Kotlin collection operations "
                    "4. Date handling and reminder scheduling for due dates "
                    "5. Statistics calculation for task completion metrics",
        "pseudocode": ""
    },
    "LightningJSMovieBrowser": {
        "component_name": "LightningJS Movie Browser",
        "description": "A Moview Browser App that lists the top 10 movies and they allows the users to browse by genre. It will also allow users to search movies by title, Genre, actors, release year or a combination. Will use tmdb API to get the movies. The app will be built using LightningJS and will be deployed on a local server. ",
        "interfaces": "A Ntflix style UI with a main movie list screen showing movie posters, a search bar for filtering movies, ",
        "algorithms": "",
        "pseudocode": ""
    },
     "GenericProject":  {
        "component_name": "Simple tic tac toe",
        "description": "A tic tac toe game",
        "interfaces": "A 3X3 board",
        "algorithms": "",
        "pseudocode": ""
     },
     "EmptyProject": {
        "component_name": "Database",
        "description": "No tables for now, just initialize the database",
        "interfaces": "",
        "algorithms": "",
        "pseudocode": ""
     },
     "ColorCraft": {
        "App Name": "ColorCraft",
        "component_name": "ColorCraft",
        "description": "Build the primary container for a simple web application that allows users to select and customize colors with various interactive features.",
        "container": {
            "name": "ColorCraft Frontend Container",
            "features": [
                {
                    "name": "RGB Slider",
                    "description": "Interactive sliders to adjust Red, Green, and Blue color values",
                    "isEnabled": True
                },
                {
                    "name": "HEX Input",
                    "description": "Direct HEX color code input with real-time preview",
                    "isEnabled": True
                },
                {
                    "name": "Color History",
                    "description": "Save and recall recently selected colors",
                    "isEnabled": True
                },
                {
                    "name": "Color Copy",
                    "description": "Easy copy functionality for selected color codes",
                    "isEnabled": True
                }
            ],
            "techStack": {
                "frontend": [
                    "React JS"
                ],
                "backend": [
                    "None"
                ],
                "language": [
                    "JavaScript (ES6+)"
                ]
            },
            "colors": {
                "primary": "#3498db",
                "secondary": "#2ecc71",
                "accent": "#e74c3c"
            },
            "theme": "light",
            "layoutDescription": "A clean, minimalist interface with a large color display area, color selection sliders/inputs for RGB/HEX, and a preview section showing selected colors."
        },
    },
    "MongoDB_Database": {
        "component_name": "MongoDB Document Database Server",
        "description": "MongoDB NoSQL document database deployed locally.",
        "interfaces": "MongoDB shell (mongosh)"
    },
    "MySQL_Database": {
        "component_name": "MySQL Database Server", 
        "description": "MySQL relational database management system deployed locally.",
        "interfaces": "MySQL command-line client (mysql)"
    },
    "PostgreSQL_Database": {
        "component_name": "PostgreSQL Database Server",
        "description": "PostgreSQL relational database management system deployed locally.",
        "interfaces": "SQL command-line interface (psql)"
    },
    "SQLite_Database": {
        "component_name": "SQLite Embedded Database Engine",
        "description": "SQLite serverless embedded database engine with file-based storage.",
        "interfaces": "SQLite command-line interface (sqlite3), direct API calls, shell script"
    },
}

PREDEFINED_WORK_ITEMS = base_work_items

PREDEFINED_WORK_ITEMS_FOR_FRAMEWORKS = {
            ApplicationType.WEB: PREDEFINED_WORK_ITEMS["GitHubExplorer"],
            ApplicationType.MOBILE: PREDEFINED_WORK_ITEMS["FlutterJumpGame"],
            ApplicationType.BACKEND: PREDEFINED_WORK_ITEMS["FastAPIEventBackend"],
            ApplicationType.GENERIC:PREDEFINED_WORK_ITEMS["GenericProject"],
            FrameworkType.EXPRESS: PREDEFINED_WORK_ITEMS["ExpressEventBackend"],
            FrameworkType.DJANGO: PREDEFINED_WORK_ITEMS["DjangoEventBackend"],
            FrameworkType.DOTNET: PREDEFINED_WORK_ITEMS["DotNetCoreEventBackend"],
            FrameworkType.ANDROID: PREDEFINED_WORK_ITEMS["AndroidTicTacToe"],
            FrameworkType.FLUTTER: PREDEFINED_WORK_ITEMS["FlutterJumpGame"],
            FrameworkType.IOS: PREDEFINED_WORK_ITEMS["TicTacToeiOSSwift"],
            FrameworkType.FASTAPI: PREDEFINED_WORK_ITEMS["FastAPIEventBackend"],
            FrameworkType.FLASK: PREDEFINED_WORK_ITEMS["FlaskEventBackend"],
            FrameworkType.SPRINGBOOT: PREDEFINED_WORK_ITEMS["SpringBootEventBackend"],
            FrameworkType.QWIK: PREDEFINED_WORK_ITEMS["QwikPomodoroApp"],
            FrameworkType.REACT: PREDEFINED_WORK_ITEMS["ColorCraft"],
            FrameworkType.SLIDEV: PREDEFINED_WORK_ITEMS["SlidevAIPresentation"],
            FrameworkType.TYPESCRIPT: PREDEFINED_WORK_ITEMS["TypescriptQuizBuilder"],
            FrameworkType.ASTRO: PREDEFINED_WORK_ITEMS["AstroPortifolioBuilder"],
            FrameworkType.SVELTE: PREDEFINED_WORK_ITEMS["SvelteMemoryGame"],
            FrameworkType.VITE: PREDEFINED_WORK_ITEMS["ViteInteractiveMusicStudio"],
            FrameworkType.VUE: PREDEFINED_WORK_ITEMS["VuePong"],
            FrameworkType.NEXTJS: PREDEFINED_WORK_ITEMS["CalculatorNextJS"],
            FrameworkType.ANGULAR: PREDEFINED_WORK_ITEMS["AngularTicTacToe"],
            FrameworkType.NUXT: PREDEFINED_WORK_ITEMS["NuxtNoteTaking"],
            FrameworkType.REMIX: PREDEFINED_WORK_ITEMS["RemixDailyPlanner"],
            FrameworkType.REMOTION: PREDEFINED_WORK_ITEMS["RemotionCountDown"],
            FrameworkType.KOTLIN: PREDEFINED_WORK_ITEMS["KotlinTodoApp"],
            FrameworkType.LIGHTNINGJS: PREDEFINED_WORK_ITEMS["LightningJSMovieBrowser"],
            FrameworkType.MONGODB: PREDEFINED_WORK_ITEMS["MongoDB_Database"],
            FrameworkType.MYSQL: PREDEFINED_WORK_ITEMS["MySQL_Database"],
            FrameworkType.POSTGRESQL: PREDEFINED_WORK_ITEMS["PostgreSQL_Database"],
            FrameworkType.SQLITE: PREDEFINED_WORK_ITEMS["SQLite_Database"],            
        }
