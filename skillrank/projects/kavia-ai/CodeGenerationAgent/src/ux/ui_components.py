"""
Simple Container Tab Helper - Just adds tabs to existing QTabWidget
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QLineEdit, QComboBox, QTextEdit
)
from code_generation_core_agent.project_schemas import ContainerType, Container
from typing import Optional


def setup_auto_update_signals(tab):
    """
    Set up automatic update signals for a tab's UI elements

    Args:
        tab: QWidget tab containing the UI elements
    """

    def update_container():
        """Update the container from current UI values"""
        container = tab.property("container")
        if not container:
            return

        # Update container name
        for widget in tab.findChildren(QLineEdit):
            parent_label = widget.parent().findChild(QLabel)
            if parent_label and parent_label.text() == "Container Name:":
                container.container_name = widget.text()
                break

        # Update framework
        for widget in tab.findChildren(QComboBox):
            parent_label = widget.parent().findChild(QLabel)
            if parent_label and parent_label.text() == "Framework:":
                container.framework = widget.currentText()
                break

        # Update description and interfaces
        for widget in tab.findChildren(QTextEdit):
            parent_label = widget.parent().findChild(QLabel)
            if parent_label:
                if parent_label.text() == "Description:":
                    container.description = widget.toPlainText()
                elif parent_label.text() == "Interfaces:":
                    container.interfaces = widget.toPlainText()

    # Connect signals to the update function
    for widget in tab.findChildren(QLineEdit):
        widget.textChanged.connect(update_container)

    for widget in tab.findChildren(QComboBox):
        widget.currentTextChanged.connect(update_container)

    for widget in tab.findChildren(QTextEdit):
        widget.textChanged.connect(update_container)


def enable_auto_update_for_tab(tab_widget, tab_index: int):
    """
    Enable automatic updates for a specific tab

    Args:
        tab_widget: QTabWidget
        tab_index: Index of the tab
    """
    tab = tab_widget.widget(tab_index)
    if tab:
        setup_auto_update_signals(tab)


def disable_auto_update_for_tab(tab_widget, tab_index: int):
    """
    Disable automatic updates for a specific tab by disconnecting signals

    Args:
        tab_widget: QTabWidget
        tab_index: Index of the tab
    """
    tab = tab_widget.widget(tab_index)
    if not tab:
        return

    # Disconnect all signals for the tab's widgets
    for widget in tab.findChildren(QLineEdit):
        widget.textChanged.disconnect()

    for widget in tab.findChildren(QComboBox):
        widget.currentTextChanged.disconnect()

    for widget in tab.findChildren(QTextEdit):
        widget.textChanged.disconnect()


def get_active_tab_container(tab_widget) -> Optional[Container]:
    """
    Get the container object of the currently active tab

    Args:
        tab_widget: QTabWidget

    Returns:
        Container object or None: The container object of the active tab
    """
    current_tab = tab_widget.currentWidget()
    if current_tab:
        return current_tab.property("container")
    return None


def get_active_tab_container_type(tab_widget) -> Optional[ContainerType]:
    """
    Get the container type of the currently active tab

    Args:
        tab_widget: QTabWidget

    Returns:
        ContainerType or None: The container type of the active tab
    """
    container = get_active_tab_container(tab_widget)
    return container.container_type if container else None


def get_tab_container(tab_widget, tab_index: int) -> Optional[Container]:
    """
    Get container object for a specific tab by index

    Args:
        tab_widget: QTabWidget
        tab_index: Index of the tab

    Returns:
        Container object or None: The container object of the specified tab
    """
    tab = tab_widget.widget(tab_index)
    if tab:
        return tab.property("container")
    return None


def get_all_tab_containers(tab_widget) -> list[Container]:
    """
    Get all container objects from all tabs

    Args:
        tab_widget: QTabWidget

    Returns:
        List[Container]: List of container objects
    """
    containers = []
    for i in range(tab_widget.count()):
        container = get_tab_container(tab_widget, i)
        if container:
            containers.append(container)
    return containers


def update_container_from_tab(tab_widget, tab_index: int) -> bool:
    """
    Update the container object with current values from the tab's UI elements

    Args:
        tab_widget: QTabWidget
        tab_index: Index of the tab to read from

    Returns:
        bool: True if successful, False if tab/container not found
    """
    container = get_tab_container(tab_widget, tab_index)
    if not container:
        return False

    tab = tab_widget.widget(tab_index)
    if not tab:
        return False

    # Find and read the UI elements
    for widget in tab.findChildren(QLineEdit):
        parent_label = widget.parent().findChild(QLabel)
        if parent_label and parent_label.text() == "Container Name:":
            container.container_name = widget.text()

    for widget in tab.findChildren(QComboBox):
        parent_label = widget.parent().findChild(QLabel)
        if parent_label and parent_label.text() == "Framework:":
            container.framework = widget.currentText()

    for widget in tab.findChildren(QTextEdit):
        parent_label = widget.parent().findChild(QLabel)
        if parent_label:
            if parent_label.text() == "Description:":
                container.description = widget.toPlainText()
            elif parent_label.text() == "Interfaces:":
                container.interfaces = widget.toPlainText()

    return True


def update_active_container_from_tab(tab_widget) -> bool:
    """
    Update the active tab's container object with current UI values

    Args:
        tab_widget: QTabWidget

    Returns:
        bool: True if successful, False if no active tab
    """
    current_index = tab_widget.currentIndex()
    return update_container_from_tab(tab_widget, current_index)


def update_tab_from_container_changes(tab_widget, tab_index: int):
    """
    Update tab UI elements when the underlying container object changes

    Args:
        tab_widget: QTabWidget
        tab_index: Index of the tab to update
    """
    container = get_tab_container(tab_widget, tab_index)
    if not container:
        return

    tab = tab_widget.widget(tab_index)
    if not tab:
        return

    # Find and update the UI elements
    # This is a simple approach - you might want to store references to specific widgets
    for widget in tab.findChildren(QLineEdit):
        parent_label = widget.parent().findChild(QLabel)
        if parent_label and parent_label.text() == "Container Name:":
            widget.setText(container.container_name)

    for widget in tab.findChildren(QComboBox):
        parent_label = widget.parent().findChild(QLabel)
        if parent_label and parent_label.text() == "Framework:":
            if container.framework in [widget.itemText(i) for i in range(widget.count())]:
                widget.setCurrentText(container.framework)


    for widget in tab.findChildren(QTextEdit):
        parent_label = widget.parent().findChild(QLabel)
        if parent_label:
            if parent_label.text() == "Description:":
                widget.setPlainText(container.description)
            elif parent_label.text() == "Interfaces:":
                widget.setPlainText(container.interfaces)