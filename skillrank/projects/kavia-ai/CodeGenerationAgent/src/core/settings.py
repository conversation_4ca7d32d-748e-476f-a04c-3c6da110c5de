from pydantic_settings import BaseSettings ,PydanticBaseSettingsSource
from pydantic import Field
from typing import List, Tuple, Type

class Settings(BaseSettings):

    # Neo4j Database Credentials
    NEO4J_CONNECTION_URI: str = Field(env="NEO4J_CONNECTION_URI", description="Neo4j connection URI")
    NEO4J_USER: str = Field(env="NEO4J_USER", description="User for Neo4j database")
    NEO4J_PASSWORD: str = Field(env="NEO4J_PASSWORD", description="Password for Neo4j database")


    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> <PERSON><PERSON>[PydanticBaseSettingsSource, ...]:
        return dotenv_settings,env_settings ,init_settings, file_secret_settings
 
    class Config:
        validate_assignment = True
        case_sensitive = True
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Ignore extra variables
       
