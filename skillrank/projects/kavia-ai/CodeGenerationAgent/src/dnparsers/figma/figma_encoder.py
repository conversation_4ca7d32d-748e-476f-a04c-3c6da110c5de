import json
from enum import Enum
from dataclasses import is_dataclass, asdict
from typing import Any
from dnparsers.figma.models import SerializableEnum, JsonSerializable


class FigmaEncoder(json.JSONEncoder):

    def default(self, obj: Any) -> Any:
        if isinstance(obj, Enum):
            return obj.value

        if isinstance(obj, JsonSerializable):
            return obj.for_json()

        if is_dataclass(obj):
            return asdict(obj)

        return super().default(obj)


def save_component_json(filepath: str, component_data: dict) -> None:
    """Save component data to JSON file using custom encoder."""
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(component_data, f, cls=FigmaEncoder, indent=2)