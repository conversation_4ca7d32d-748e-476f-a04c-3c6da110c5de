<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Figma Document Report</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Figma Document Report</h1>

        <section class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Document Overview</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">Metadata</h3>
                    {% for key, value in metadata.items() %}
                    <div class="mb-2">
                        <div class="text-sm font-medium text-gray-500">{{ key }}</div>
                        <div class="text-sm text-gray-900">{{ value }}</div>
                    </div>
                    {% endfor %}
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">Statistics</h3>
                    {% for key, value in statistics.items() %}
                    <div class="mb-2">
                        <div class="text-sm font-medium text-gray-500">{{ key }}</div>
                        <div class="text-sm text-gray-900">{{ value }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Color Palette</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for color in colors %}
                <div class="flex items-center p-3 bg-gray-50 rounded">
                    <div class="w-10 h-10 rounded mr-3" style="background-color: {{ color.hex }}"></div>
                    <div>
                        <div class="font-mono text-sm">{{ color.hex }}</div>
                        <div class="text-xs text-gray-500">RGB({{ color.rgb.r }}, {{ color.rgb.g }}, {{ color.rgb.b }})</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Text Content</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Content</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Font</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Size</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for text in text_content %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">{{ text.name }}</td>
                            <td class="px-6 py-4 text-sm">{{ text.content }}</td>
                            <td class="px-6 py-4 text-sm">{{ text.style.font_family }}</td>
                            <td class="px-6 py-4 text-sm">{{ text.style.font_size }}px</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Interactive Elements</h2>
            <div class="space-y-4">
                {% for element in interactive_elements %}
                <div class="bg-gray-50 p-4 rounded">
                    <h3 class="font-medium text-gray-900">{{ element.name }} ({{ element.type }})</h3>
                    <div class="mt-2 space-y-2">
                        {% for interaction in element.interactions %}
                        <div class="text-sm">
                            <span class="font-medium">Trigger:</span> {{ interaction.trigger.type }}<br>
                            <span class="font-medium">Actions:</span>
                            <ul class="list-disc list-inside ml-4">
                                {% for action in interaction.actions %}
                                <li>{{ action.type }}{% if action.destination %} → {{ action.destination }}{% endif %}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Component Tree</h2>
            <div class="overflow-x-auto">
                <pre class="text-sm text-gray-700">{{ component_tree | tojson(indent=2) }}</pre>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Validation Results</h2>
            <ul class="list-disc list-inside">
                {% if validation_issues %}
                {% for issue in validation_issues %}
                <li class="text-red-600">{{ issue }}</li>
                {% endfor %}
                {% else %}
                <li class="text-green-600">No validation issues found</li>
                {% endif %}
            </ul>
        </section>
    </div>
</body>
</html>