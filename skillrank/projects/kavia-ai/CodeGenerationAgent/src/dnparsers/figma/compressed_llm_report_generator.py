import argparse
import json
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

from dnparsers.figma.figma_parser import parse_figma_file
from dnparsers.figma.models import FigmaDocument, Node, Component, Paint, Effect, TextStyle, Color, EffectType, Bounds, \
    Padding
from dnparsers.figma.utils import generate_html_report

@dataclass
class CompressedReport:
    """Container for compressed report data with metadata."""
    schema: Dict[str, Any]     # Schema documentation for LLMs
    tokens: Dict[str, int]     # Mapping of repeated strings to IDs
    styles: Dict[str, int]     # Mapping of repeated styles to IDs
    colors: Dict[str, int]     # Mapping of repeated colors to IDs
    data: Dict[str, Any]       # The compressed report data

class CompressedLLMReportGenerator:
    """Generates structured JSON reports optimized for LLM code generation."""

    def __init__(self, document: FigmaDocument):
        self.document = document
        self.token_map: Dict[str, int] = {}
        self.next_token_id = 1
        self.style_map: Dict[str, int] = {}
        self.next_style_id = 1
        self.color_map: Dict[str, int] = {}
        self.next_color_id = 1
        self.schema = self._generate_schema()

    def _tokenize(self, value: str) -> int:
        """Convert string to token ID."""
        if value not in self.token_map:
            self.token_map[value] = self.next_token_id
            self.next_token_id += 1
        return self.token_map[value]

    def _tokenize_dict(self, value: Union[Dict, str]) -> Union[Dict[str, int], int]:
        """Convert dictionary values to tokens while preserving structure."""
        if isinstance(value, dict):
            return {k: self._tokenize(str(v)) for k, v in value.items()}
        return self._tokenize(str(value))

    def _tokenize_style(self, style: Dict) -> int:
        """Convert style dictionary to token ID."""
        style_str = json.dumps(style, sort_keys=True)
        if style_str not in self.style_map:
            self.style_map[style_str] = self.next_style_id
            self.next_style_id += 1
        return self.style_map[style_str]

    def _compress_node(self, node: Node) -> Dict[str, Any]:
        """Compress node data."""
        compressed = {
            'i': self._tokenize(node.id),
            'n': self._tokenize(node.name),
            't': self._tokenize(node.type),
            'v': node.visible
        }

        if node.fills:
            compressed['f'] = [self._compress_paint(f) for f in node.fills]
        if node.strokes:
            compressed['s'] = [self._compress_paint(s) for s in node.strokes]
        if node.effects:
            compressed['e'] = [self._compress_effect(e) for e in node.effects]
        if node.layout:
            compressed['l'] = self._compress_layout(node.layout)
        if node.text_style:
            compressed['ts'] = self._compress_text_style(node.text_style)
        if node.characters is not None:
            compressed['c'] = self._tokenize(node.characters)
        if node.interactions:
            compressed['int'] = [self._compress_interaction(i) for i in node.interactions]
        if node.children:
            compressed['ch'] = [self._compress_node(c) for c in node.children]

        return compressed

    def _compress_paint(self, paint: Paint) -> Dict[str, Any]:
        """Compress paint data."""
        compressed = {'t': self._tokenize(paint.type.value)}
        if paint.color:
            compressed['c'] = self._tokenize(paint.color.to_hex())
        if paint.opacity is not None:
            compressed['o'] = paint.opacity
        return compressed

    def _compress_effect(self, effect: Effect) -> Dict[str, Any]:
        """Compress effect data."""
        compressed = {'t': self._tokenize(effect.type.value)}
        if effect.color:
            compressed['c'] = self._tokenize(effect.color.to_hex())
        if effect.radius is not None:
            compressed['r'] = effect.radius
        return compressed

    def _compress_layout(self, layout: Any) -> Dict[str, Any]:
        """Compress layout data."""
        compressed = {}
        if hasattr(layout, 'mode') and layout.mode:
            compressed['m'] = self._tokenize(str(layout.mode))
        if hasattr(layout, 'spacing'):
            compressed['s'] = layout.spacing
        if hasattr(layout, 'alignment') and layout.alignment:
            compressed['a'] = self._tokenize_dict(layout.alignment)
        if hasattr(layout, 'padding') and layout.padding:
            compressed['p'] = [
                layout.padding.top,
                layout.padding.right,
                layout.padding.bottom,
                layout.padding.left
            ]
        if hasattr(layout, 'position') and layout.position:
            compressed['pos'] = {
                'x': layout.position.x,
                'y': layout.position.y,
                'w': layout.position.width,
                'h': layout.position.height
            }
        return compressed

    def _compress_text_style(self, style: TextStyle) -> Dict[str, Any]:
        """Compress text style data."""
        style_dict = {
            'ff': style.font_family,
            'fs': style.font_size,
            'fw': style.font_weight,
            'ls': style.letter_spacing,
            'lh': style.line_height_px,
            'ta': style.text_align_horizontal
        }
        return self._tokenize_style(style_dict)

    def _compress_interaction(self, interaction: Any) -> Dict[str, Any]:
        """Compress interaction data."""
        if not interaction.trigger:
            return {}
        return {
            't': self._tokenize(str(interaction.trigger.get('type', ''))),
            'a': [self._compress_action(a) for a in interaction.actions]
        }

    def _compress_action(self, action: Any) -> Dict[str, Any]:
        """Compress action data."""
        compressed = {'t': self._tokenize(str(action.type))}
        if action.destination_id:
            compressed['d'] = self._tokenize(str(action.destination_id))
        if action.navigation:
            # Handle both string and dictionary navigation data
            compressed['n'] = self._tokenize_dict(action.navigation)
        return compressed

    def generate_report(self) -> CompressedReport:
        """Generate a compressed report that maintains all information."""
        components = [
            {
                'id': self._tokenize(comp.id),
                'fk': self._tokenize(comp.file_key),
                'h': self._compress_node(comp.hierarchy)
            }
            for comp in self.document.components
        ]

        # Safely handle metadata string conversion
        metadata = {k: self._tokenize(str(v)) for k, v in self.document.metadata.items()}

        compressed_data = {
            'v': '1.0',
            'm': metadata,
            'c': components,
            's': {
                k: [self._tokenize_style(style) if isinstance(style, dict)
                   else self._tokenize(str(style))
                   for style in v]
                for k, v in self.document.styles.items()
            },
            'a': {k: [self._tokenize(str(asset)) for asset in v]
                 for k, v in self.document.assets.items()},
            'i': [self._compress_interaction(interaction)
                 for interaction in self.document.interactions]
        }

        return CompressedReport(
            schema=self.schema,
            tokens=self.token_map,
            styles=self.style_map,
            colors=self.color_map,
            data=compressed_data
        )

    def _generate_schema(self) -> Dict[str, Any]:
        """Generate schema documentation."""
        return {
            "version": "1.0",
            "description": "Compressed Figma document format optimized for LLM processing"
        }


def load_json_file(file_path: str) -> str:
    """Load JSON from file with proper path handling and return as string."""
    path = Path(file_path)
    if not path.is_absolute() and not path.exists():
        script_dir = Path(__file__).parent.absolute()
        path = script_dir / path
    if not path.exists():
        path = Path.cwd() / file_path
    if not path.exists():
        raise FileNotFoundError(f"Could not find file: {file_path}")

    print(f"Loading file from: {path}")
    with open(path, 'r', encoding='utf-8') as f:
        return f.read()


def main() -> int:
    """Main function to run the Figma report generator."""
    parser = argparse.ArgumentParser(
        description='Generate HTML and JSON reports from Figma JSON data'
    )
    parser.add_argument('input_file', help='Path to Figma JSON file')
    parser.add_argument('--html-output', default='report.html',
                        help='Path for output HTML report (default: report.html)')
    parser.add_argument('--json-output', default='report.json',
                        help='Path for output JSON report (default: report.json)')
    parser.add_argument('--format', choices=['html', 'json', 'both'],
                        default='both', help='Output format(s) to generate (default: both)')

    args = parser.parse_args()

    try:
        # Load the raw JSON string
        json_str = load_json_file(args.input_file)

        # Parse the document using the string
        document = parse_figma_file(json_str)

        if args.format in ['html', 'both']:
            generate_html_report(json_str, args.html_output)
            print(f"HTML report generated: {args.html_output}")

        if args.format in ['json', 'both']:
            report_generator = CompressedLLMReportGenerator(document)
            json_report = report_generator.generate_report()

            with open(args.json_output, 'w', encoding='utf-8') as f:
                json.dump({
                    'schema': json_report.schema,
                    'tokens': json_report.tokens,
                    'styles': json_report.styles,
                    'colors': json_report.colors,
                    'data': json_report.data
                }, f, indent=2)
            print(f"JSON report generated: {args.json_output}")

        return 0

    except FileNotFoundError as e:
        print(f"Error: {str(e)}")
        return 1
    except json.JSONDecodeError:
        print("Error: Invalid JSON data in input file")
        return 1
    except Exception as e:
        print(f"Error generating reports: {str(e)}")
        raise


if __name__ == '__main__':
    import sys
    sys.exit(main())