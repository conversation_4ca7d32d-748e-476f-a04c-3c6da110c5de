import argparse
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Set

from dnparsers.figma.figma_parser import parse_figma_file
from dnparsers.figma.models import FigmaDocument, Node, Component, Paint, Effect, TextStyle, Color, EffectType, Bounds, \
    Padding
from dnparsers.figma.utils import generate_html_report


class LLMReportGenerator:
    """Generates structured JSON reports optimized for LLM code generation."""

    def __init__(self, document: FigmaDocument):
        self.document = document

    def _convert_bounds_to_dict(self, bounds: Optional[Bounds]) -> Optional[Dict[str, float]]:
        """Convert Bounds object to dictionary."""
        if not bounds:
            return None
        return {
            "x": bounds.x,
            "y": bounds.y,
            "width": bounds.width,
            "height": bounds.height
        }

    def _convert_padding_to_dict(self, padding: Optional[Padding]) -> Optional[Dict[str, float]]:
        """Convert Padding object to dictionary."""
        if not padding:
            return None
        return {
            "top": padding.top,
            "right": padding.right,
            "bottom": padding.bottom,
            "left": padding.left
        }

    def _generate_component_structure(self, node: Node) -> Dict[str, Any]:
        """
        Generate a structured representation of a component's hierarchy.

        Args:
            node: Root node of the component hierarchy

        Returns:
            Dictionary containing the component's structure
        """
        structure = {
            "id": node.id,
            "type": node.type,
            "name": node.name,
            "properties": {
                "visible": node.visible,
                "position": self._convert_bounds_to_dict(
                    node.layout.position if node.layout else None
                ),
                "padding": self._convert_padding_to_dict(
                    node.layout.padding if node.layout else None
                ),
                "layout": {
                    "mode": node.layout.mode if node.layout else None,
                    "spacing": node.layout.spacing if node.layout else None,
                    "alignment": node.layout.alignment if node.layout else None
                } if node.layout else None
            },
            "styles": {
                "fills": [{"type": fill.type.value, "color": fill.color.to_hex() if fill.color else None}
                          for fill in node.fills] if node.fills else [],
                "strokes": [{"type": stroke.type.value, "color": stroke.color.to_hex() if stroke.color else None}
                            for stroke in node.strokes] if node.strokes else [],
                "effects": [{"type": effect.type.value, "radius": effect.radius}
                            for effect in node.effects] if node.effects else []
            },
            "text": {
                "content": node.characters,
                "style": {
                    "fontFamily": node.text_style.font_family if node.text_style else None,
                    "fontSize": node.text_style.font_size if node.text_style else None,
                    "fontWeight": node.text_style.font_weight if node.text_style else None,
                    "letterSpacing": node.text_style.letter_spacing if node.text_style else None,
                    "lineHeight": node.text_style.line_height_px if node.text_style else None,
                    "textAlign": node.text_style.text_align_horizontal if node.text_style else None
                }
            } if node.characters else None,
            "children": [
                self._generate_component_structure(child)
                for child in node.children
            ] if node.children else []
        }

        # Remove None values for cleaner output
        return {k: v for k, v in structure.items() if v is not None}


    def generate_report(self) -> Dict[str, Any]:
        """Generate a comprehensive JSON report structured for LLM consumption."""
        return {
            "version": "1.0",
            "metadata": self._generate_metadata(),
            "designSystem": self._extract_design_system(),
            "components": self._process_components(),
            "layout": self._extract_layout_patterns(),
            "interactions": self._process_interactions(),
            "accessibility": self._extract_accessibility_info(),
            "codeGeneration": self._generate_code_hints()
        }

    def _extract_component_styles(self, component: Component) -> Dict[str, Any]:
        """Extract styles from a component."""
        return {
            "fills": [{"type": fill.type.value, "color": fill.color.to_hex() if fill.color else None}
                      for fill in component.styles.get("fills", [])],
            "effects": [{"type": effect.type.value, "radius": effect.radius}
                        for effect in component.styles.get("effects", [])]
        }

    def _detect_component_variants(self, component: Component) -> List[Dict[str, Any]]:
        """Detect variants of a component based on naming patterns."""
        variants = []
        # Basic variant detection based on naming conventions
        name = component.hierarchy.name.lower()
        if "variant" in name or "state" in name:
            variants.append({
                "type": "state",
                "name": name.split("/")[-1],
                "condition": "default"
            })
        return variants

    def _analyze_dependencies(self, component: Component) -> List[Dict[str, Any]]:
        """Analyze component dependencies."""
        dependencies = []
        # Add standard React dependencies
        dependencies.append({"name": "react", "type": "core"})
        # Check for specific features that might need additional dependencies
        if component.hierarchy.interactions:
            dependencies.append({"name": "react-dom", "type": "core"})
        return dependencies

    def _process_components(self) -> List[Dict[str, Any]]:
        """Process components with code generation context."""
        components = []
        for component in self.document.components:
            component_data = {
                "id": component.id,
                "name": component.hierarchy.name,
                "type": "functional",
                "props": self._extract_component_props(component),
                "structure": self._generate_component_structure(component.hierarchy),
                "styling": self._extract_component_styles(component),
                "variants": self._detect_component_variants(component),
                "dependencies": self._analyze_dependencies(component)
            }
            components.append(component_data)
        return components

    def _extract_component_props(self, component: Component) -> List[Dict[str, Any]]:
        """Extract likely component props based on usage patterns."""
        props = []
        text_nodes = self._find_text_nodes(component.hierarchy)
        for node in text_nodes:
            if node.characters and not self._is_static_content(node.characters):
                props.append({
                    "name": self._generate_prop_name(node.name),
                    "type": "string",
                    "description": f"Text content for {node.name}",
                    "required": False
                })

        if component.hierarchy.interactions:
            for interaction in component.hierarchy.interactions:
                props.append({
                    "name": f"on{interaction.trigger['type'].capitalize()}",
                    "type": "function",
                    "description": f"Callback for {interaction.trigger['type']} event",
                    "required": False
                })
        return props

    def _generate_metadata(self) -> Dict[str, Any]:
        """Extract and structure metadata for LLM context."""
        return {
            **self.document.metadata,
            "componentCount": len(self.document.components),
            "totalInteractions": len(self.document.interactions),
            "assetTypes": list(self.document.assets.keys()),
            "framework": "react",
            "styleSystem": "tailwind"
        }

    def _extract_design_system(self) -> Dict[str, Any]:
        """Extract design system tokens and patterns."""
        colors = set()
        fonts = set()
        text_styles = []
        spacing_values = set()
        border_radii = set()

        def process_node(node: Node) -> None:
            for paint in node.fills + node.strokes:
                if paint.color:
                    colors.add(paint.color.to_hex())

            if node.text_style:
                fonts.add(node.text_style.font_family)
                text_styles.append({
                    "fontFamily": node.text_style.font_family,
                    "fontSize": node.text_style.font_size,
                    "fontWeight": node.text_style.font_weight,
                    "lineHeight": node.text_style.line_height_px,
                    "letterSpacing": node.text_style.letter_spacing
                })

            if node.layout and node.layout.spacing:
                spacing_values.add(node.layout.spacing)

            if "cornerRadius" in node.properties:
                border_radii.add(node.properties["cornerRadius"])

            for child in node.children:
                process_node(child)

        for component in self.document.components:
            process_node(component.hierarchy)

        return {
            "colors": sorted(list(colors)),
            "typography": {
                "fonts": sorted(list(fonts)),
                "styles": self._deduplicate_styles(text_styles)
            },
            "spacing": sorted(list(spacing_values)),
            "shadows": self._extract_shadow_styles(),
            "borderRadius": sorted(list(border_radii)) if border_radii else [0, 4, 8, 16, 24]
        }

    def _extract_layout_patterns(self) -> Dict[str, Any]:
        """Extract common layout patterns for consistent code generation."""
        patterns = {
            "flex": [],
            "grid": [],
            "positioning": []
        }

        def analyze_layout(node: Node) -> None:
            if node.layout:
                if node.layout.mode == "HORIZONTAL" or node.layout.mode == "VERTICAL":
                    patterns["flex"].append({
                        "direction": node.layout.mode.lower(),
                        "spacing": node.layout.spacing,
                        "alignment": node.layout.alignment,
                        "wrap": "nowrap"
                    })

            for child in node.children:
                analyze_layout(child)

        for component in self.document.components:
            analyze_layout(component.hierarchy)

        return patterns

    def _process_interactions(self) -> List[Dict[str, Any]]:
        """Process interactions with code generation context."""
        return [{
            "sourceId": interaction["nodeId"],
            "sourceName": interaction["nodeName"],
            "events": [{
                "trigger": event["trigger"],
                "actions": event["actions"],
                "suggestedImplementation": {
                    "type": "handler",
                    "name": f"handle{event['trigger'].get('type', 'Event').capitalize()}"
                }
            } for event in interaction["interactions"]]
        } for interaction in self.document.interactions]

    def _generate_code_hints(self) -> Dict[str, Any]:
        """Generate hints for code generation."""
        return {
            "componentOrganization": {
                "suggested_file_structure": [
                    "components/",
                    "components/atoms/",
                    "components/molecules/",
                    "components/organisms/",
                    "styles/",
                    "utils/"
                ],
                "component_hierarchy": {
                    "atoms": [],
                    "molecules": [],
                    "organisms": []
                }
            },
            "stateManagement": {
                "local": self._detect_local_state_needs(),
                "global": []
            },
            "styling": {
                "tailwindClasses": {
                    "layout": ["flex", "flex-col", "grid", "gap-4"],
                    "spacing": ["p-4", "m-2", "space-x-4"],
                    "typography": ["text-sm", "font-medium", "leading-6"],
                }
            }
        }

    def _extract_shadow_styles(self) -> List[Dict[str, Any]]:
        """Extract shadow styles from effects."""
        shadows = set()

        def process_node_shadows(node: Node) -> None:
            for effect in node.effects:
                if effect.type in [EffectType.DROP_SHADOW, EffectType.INNER_SHADOW]:
                    shadow_def = {
                        "type": effect.type.value,
                        "color": effect.color.to_rgba() if effect.color else "rgba(0,0,0,0.1)",
                        "offset": {
                            "x": effect.offset.x if effect.offset else 0,
                            "y": effect.offset.y if effect.offset else 0
                        },
                        "radius": effect.radius,
                        "spread": effect.spread
                    }
                    shadows.add(json.dumps(shadow_def, sort_keys=True))

            for child in node.children:
                process_node_shadows(child)

        for component in self.document.components:
            process_node_shadows(component.hierarchy)

        return [json.loads(s) for s in shadows]

    def _extract_accessibility_info(self) -> Dict[str, Any]:
        """Extract accessibility-related information."""
        return {
            "roles": self._suggest_semantic_roles(),
            "landmarks": self._extract_landmarks(),
            "focus": {
                "order": [],
                "indicators": []
            }
        }

    def _suggest_semantic_roles(self) -> Dict[str, List[str]]:
        """Suggest semantic roles for components."""
        roles = {}
        for component in self.document.components:
            if "nav" in component.hierarchy.name.lower():
                roles[component.id] = "navigation"
            elif "button" in component.hierarchy.name.lower():
                roles[component.id] = "button"
        return roles

    def _extract_landmarks(self) -> List[Dict[str, str]]:
        """Extract landmark information."""
        landmarks = []
        for component in self.document.components:
            if any(role in component.hierarchy.name.lower()
                   for role in ["header", "nav", "main", "footer"]):
                landmarks.append({
                    "id": component.id,
                    "role": next(role for role in ["header", "nav", "main", "footer"]
                                 if role in component.hierarchy.name.lower())
                })
        return landmarks

    # Helper methods
    def _deduplicate_styles(self, styles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate styles while preserving order."""
        seen = set()
        return [x for x in styles if not (str(x) in seen or seen.add(str(x)))]

    def _find_text_nodes(self, node: Node) -> List[Node]:
        """Find all text nodes in the hierarchy."""
        text_nodes = []
        if node.characters is not None:
            text_nodes.append(node)
        for child in node.children:
            text_nodes.extend(self._find_text_nodes(child))
        return text_nodes

    def _is_static_content(self, text: str) -> bool:
        """Determine if text content appears to be static."""
        placeholder_indicators = ["{", "}", "$", "placeholder", "lorem ipsum"]
        return not any(indicator in text.lower() for indicator in placeholder_indicators)

    def _generate_prop_name(self, node_name: str) -> str:
        """Generate a proper React prop name from a node name."""
        words = node_name.replace("-", " ").replace("_", " ").split()
        return words[0].lower() + "".join(word.capitalize() for word in words[1:])

    def _detect_local_state_needs(self) -> List[Dict[str, Any]]:
        """Detect components that likely need local state."""
        local_state_needs = []
        for component in self.document.components:
            if component.hierarchy.interactions:
                local_state_needs.append({
                    "componentId": component.id,
                    "componentName": component.hierarchy.name,
                    "stateType": "boolean",
                    "suggestedName": "is" + component.hierarchy.name.replace(" ", ""),
                    "reason": "Component has interactions that may require state tracking"
                })
        return local_state_needs


def generate_reports(input_file: str, html_output: str, json_output: str) -> None:
    """Generate both HTML and JSON reports from Figma data."""
    try:
        json_data = load_json_file(input_file)
        document = parse_figma_file(json_data)

        generate_html_report(json_data, html_output)

        report_generator = LLMReportGenerator(document)
        json_report = report_generator.generate_report()

        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump(json_report, f, indent=2)
        print(f"JSON report generated: {json_output}")

    except FileNotFoundError as e:
        print(f"Error: {str(e)}")
    except json.JSONDecodeError:
        print("Error: Invalid JSON data in input file")
    except Exception as e:
        print(f"Error generating reports: {str(e)}")
        raise


def load_json_file(file_path: str) -> str:
    """Load JSON from file with proper path handling."""
    path = Path(file_path)
    if not path.is_absolute() and not path.exists():
        script_dir = Path(__file__).parent.absolute()
        path = script_dir / path
    if not path.exists():
        path = Path.cwd() / file_path
    if not path.exists():
        raise FileNotFoundError(f"Could not find file: {file_path}")

    print(f"Loading file from: {path}")
    with open(path, 'r', encoding='utf-8') as f:
        return f.read()


def main() -> int:
    """Main function to run the Figma report generator."""
    parser = argparse.ArgumentParser(
        description='Generate HTML and JSON reports from Figma JSON data'
    )
    parser.add_argument('input_file', help='Path to Figma JSON file')
    parser.add_argument('--html-output', default='report.html',
                        help='Path for output HTML report (default: report.html)')
    parser.add_argument('--json-output', default='report.json',
                        help='Path for output JSON report (default: report.json)')
    parser.add_argument('--format', choices=['html', 'json', 'both'],
                        default='both', help='Output format(s) to generate (default: both)')

    args = parser.parse_args()

    try:
        # Determine which reports to generate
        generate_html = args.format in ['html', 'both']
        generate_json = args.format in ['json', 'both']

        # Load input data
        json_data = load_json_file(args.input_file)
        document = parse_figma_file(json_data)

        # Generate requested reports
        if generate_html:
            generate_html_report(json_data, args.html_output)

        if generate_json:
            report_generator = LLMReportGenerator(document)
            json_report = report_generator.generate_report()

            with open(args.json_output, 'w', encoding='utf-8') as f:
                json.dump(json_report, f, indent=2)
            print(f"JSON report generated: {args.json_output}")

    except FileNotFoundError as e:
        print(f"Error: {str(e)}")
        return 1
    except json.JSONDecodeError:
        print("Error: Invalid JSON data in input file")
        return 1
    except Exception as e:
        print(f"Error generating reports: {str(e)}")
        raise

    return 0


if __name__ == '__main__':
    import sys

    main()