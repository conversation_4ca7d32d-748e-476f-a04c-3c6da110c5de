#!/bin/bash

# Check for errors. Without this the CI may produce false negatives!
set -e

python_command="python3"
python_version=$("$python_command" -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
if [ -x "/usr/bin/python3.11" ]; then
    python_command="/usr/bin/python3.11"
elif [ -x "/opt/homebrew/bin/python3.11" ]; then  # Common Homebrew path on Mac
    python_command="/opt/homebrew/bin/python3.11"
elif [ "$(printf '%s\n' "3.11" "$python_version" | sort -V | head -n1)" = "3.11" ]; then
    echo "Python $python_version found"
else
    echo "Python 3.11 or higher not found"
    exit 1
fi

echo "Python command: " $python_command
if "$python_command" -m venv venv ; then
    echo "Virtual environment created successfully"
else
    echo "Failed to create virtual environment, it could mean that you are missing ensurepip package, try:"
    echo "sudo apt install --reinstall python3.11-venv python3.11-distutils"
    exit 1
fi
source venv/bin/activate
pip install --upgrade -r requirements.txt

playwright install chromium
# Append or create .env file with PYTHONPATH
PROJECT_ROOT=$(pwd)
ENV_FILE=".env"
DEPENDENCY_PATH=$PROJECT_ROOT/src:$PROJECT_ROOT

PYTHONPATH_ENTRY="PYTHONPATH=$DEPENDENCY_PATH"

if [ -f "$ENV_FILE" ]; then
    if grep -q "^PYTHONPATH=" "$ENV_FILE"; then
        sed -i.bak "s|^PYTHONPATH=.*|$PYTHONPATH_ENTRY|" "$ENV_FILE" && rm "$ENV_FILE.bak"
        echo "Updated PYTHONPATH in existing .env file"
    else
        echo "$PYTHONPATH_ENTRY" >> "$ENV_FILE"
        echo "Appended PYTHONPATH to existing .env file"
    fi
else
    echo "$PYTHONPATH_ENTRY" > "$ENV_FILE"
    echo "Created new .env file with PYTHONPATH"
fi

echo "Setup complete. Activate the virtual environment with 'source venv/bin/activate'"
echo "PYTHONPATH has been set in .env file"

echo "Activate env"
source venv/bin/activate
pip install nest_asyncio
echo "Pull container"
PYTHONPATH=$PWD/src python -c "from src.code_generation_core_agent.agents.utilities import pull_current_container; pull_current_container()"

export PYTHONPATH=$DEPENDENCY_PATH
