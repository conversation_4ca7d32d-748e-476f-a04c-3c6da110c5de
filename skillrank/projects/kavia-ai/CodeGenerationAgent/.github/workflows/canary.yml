name: Nightly Canary Promotion
permissions:
  contents: write
  packages: read

on:
  schedule:
    - cron: '0 0 * * *'  # Fixed cron syntax
  workflow_dispatch:

jobs:
  promote-to-canary:
    runs-on: largerunner
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        ssh-key: ${{ secrets.SECRET_TOKEN }}

    - name: Set up Git
      run: |
        git config user.name github-actions
        git config user.email <EMAIL>
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install toml package
      run: pip install toml
    
    - name: Generate new version
      id: version
      run: |
        # Get the current version from pyproject.toml
        current_version=$(python -c "
        import toml
        with open('pyproject.toml', 'r') as f:
            config = toml.load(f)
        print(config['tool']['poetry']['version'])
        ")
        
        # Increment the patch version
        IFS='.' read -ra VERSION_PARTS <<< "$current_version"
        new_patch=$((VERSION_PARTS[2] + 1))
        new_version="${VERSION_PARTS[0]}.${VERSION_PARTS[1]}.$new_patch"
        
        echo "new_version=$new_version" >> $GITHUB_OUTPUT
    
    - name: Execute Promotion Steps
      run: |
        set -x
        # 1. Merge main to canary
        git fetch origin
        git remote update
        git checkout canary
        git reset --hard origin/canary
        git merge origin/main --no-ff --no-edit || {
          echo "Merge conflicts detected. Aborting."
          git merge --abort
          exit 1
        }
        
        # 2. Update version and push to canary
        # Update version.py
        mkdir -p src/code_generation_core_agent
        echo "# This file is auto-generated by GitHub Actions" > src/code_generation_core_agent/version.py
        echo "# Do not modify manually" >> src/code_generation_core_agent/version.py
        echo "" >> src/code_generation_core_agent/version.py
        echo "VERSION = \"${{ steps.version.outputs.new_version }}\"" >> src/code_generation_core_agent/version.py
        
        # Update pyproject.toml
        python -c "
        import toml
        with open('pyproject.toml', 'r') as f:
            config = toml.load(f)
        config['tool']['poetry']['version'] = '${{ steps.version.outputs.new_version }}'
        with open('pyproject.toml', 'w') as f:
            toml.dump(config, f)
        "
        
        # Commit and push changes to canary
        git add src/code_generation_core_agent/version.py pyproject.toml
        git commit -m "chore: update version to ${{ steps.version.outputs.new_version }}" || {
          echo "No changes to commit"
          exit 1
        }
        git push origin canary
    
    - name: Create Git Tag
      run: |
        git tag v${{ steps.version.outputs.new_version }}
        git push origin v${{ steps.version.outputs.new_version }}

    - name: Notify Slack – New Package
      if: ${{ success() }}
      run: |
        curl -X POST \
          -H 'Content-type: application/json' \
          --data "{\"text\":\"A new code generation package is available ${{ steps.version.outputs.new_version }} on branch \`${{ github.ref_name }}\`\"}" \
          ${{ secrets.CODE_AGENT_HOOK }}

  merge-canary-to-main:
    needs: [promote-to-canary, deploy]
    runs-on: largerunner
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        ssh-key: ${{ secrets.SECRET_TOKEN }}
    
    - name: Set up Git
      run: |
        git config user.name github-actions
        git config user.email <EMAIL>
    
    - name: Merge canary to main
      run: |
        git fetch origin
        git checkout main
        git reset --hard origin/main
        git merge origin/canary --no-ff --no-edit || {
          echo "Merge conflicts detected. Aborting."
          git merge --abort
          exit 1
        }
        git push origin main
       
  deploy:
    needs: promote-to-canary
    uses: ./.github/workflows/deploy.yml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
