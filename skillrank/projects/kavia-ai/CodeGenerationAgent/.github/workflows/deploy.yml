# .github/workflows/deploy-package.yml
name: Deploy Package
on:
  workflow_call:
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      AWS_REGION:
        required: true

jobs:
  package-and-deploy:
    runs-on: largerunner
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: canary
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install build tools
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    
    - name: Build the package
      run: |
        python -m build --outdir dist
    
    - name: Extract version from pyproject.toml
      id: get_version
      run: |
        VERSION=$(grep -Po '(?<=version = ")[^"]*' pyproject.toml)
        echo "VERSION=$VERSION" >> $GITHUB_ENV
    
    - name: Configure AWS CLI
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}
    
   
    - name: Login to AWS CodeArtifact
      run: |
        aws codeartifact login \
          --tool twine \
          --repository kavia \
          --domain kavia \
          --domain-owner 058264095463
    
    - name: Upload package to AWS CodeArtifact
      run: |
        twine upload --repository codeartifact dist/*
