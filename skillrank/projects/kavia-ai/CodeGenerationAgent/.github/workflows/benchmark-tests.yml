name: Nightly Benchmark
on:
  workflow_call:
  workflow_dispatch:

jobs:

  benchmark:
    runs-on: linux-arm64
    timeout-minutes: 720  # 12 hours total
    permissions:
      contents: read
      packages: read

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v3
      with:
        python-version: "3.11"
    - name: Create .env file
      run: |
        echo "ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY }}" >> .env
        echo "PERPLEXITY_API_KEY=${{ secrets.PERPLEXITY_API_KEY }}" >> .env
        echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID_BEDROCK }}" >> .env
        echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY_BEDROCK }}" >> .env
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
        echo "NEO4J_PASSWORD=${{ secrets.NEO4J_PASSWORD }}" >> .env
        echo "NEO4J_USER=neo4j" >> .env
        echo "NEO4J_CONNECTION_URI=neo4j+s://3ab082c5.databases.neo4j.io" >> .env
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: |
          venv
          ~/.cache/pip
        key: venv-${{ runner.os }}-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          venv-${{ runner.os }}-

    - name: Set up Docker
      uses: docker/setup-buildx-action@v2

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: |
        python -m pip install --upgrade pip
        pip install gdown

    - name: Create base directory
      run: |
        mkdir -p /var/www/html/protected/cga/${{ github.run_id }}
        chmod -R 777 /var/www/html/protected/cga/${{ github.run_id }}

    - name: Run benchmarks
      id: benchmarks
      run: |
        BENCHMARKS=(
          "test_complete_codegen_pomodoro_timer"
          "test_complete_codegen_event_countdown_timer"
          "test_complete_codegen_expense_tracker"
          "test_complete_codegen_meme_generator"
        )
        
        for benchmark in "${BENCHMARKS[@]}"; do
          echo "Running benchmark: $benchmark"
          
          # Create benchmark-specific directory
          benchmark_dir="/var/www/html/protected/cga/${{ github.run_id }}/${benchmark}"
          mkdir -p "${benchmark_dir}"
          
          timeout 1h bash -c "source setup.sh && PYTHONPATH=$PWD/src bash run_benchmark.sh -t $benchmark" || true
          
          # Copy HTML and PNG files to benchmark directory
          if ls /tmp/*.html 2>/dev/null; then
            cp /tmp/*.html "${benchmark_dir}/"
          fi
          
          # Collect PNG files
          mkdir -p "collected_images_${benchmark}"
          if ls /tmp/*.png 2>/dev/null; then
            cp /tmp/*.png "collected_images_${benchmark}/"
            cp /tmp/*.png "${benchmark_dir}/"
            
            # Create montage for this benchmark
            cd "collected_images_${benchmark}"
            montage *.png -mode Concatenate -tile 4x4 "montage_${benchmark}.jpg" || true
            cd ..
          fi
          
          # Clean node_modules
          find /tmp/kavia -type d -name "node_modules" -exec rm -rf {} +
          
          # Create tar archive
          tar czf "test_complete_${benchmark}.tgz" -C /tmp kavia || true
          
          # Cleanup
          rm -f /tmp/*.png
          rm -f /tmp/*.html
          rm -rf /tmp/kavia
          find "${benchmark_dir}" -type f -exec chmod 644 {} +
          find "${benchmark_dir}" -type d -exec chmod 755 {} +
        done
      env:
        DOCKER_HOST: unix:///var/run/docker.sock

    - name: Copy montage and archive files
      run: |
        # Copy montage files to base CGA directory
        benchmark_dir="/var/www/html/protected/cga/${{ github.run_id }}"

        cp collected_images_*/montage_*.jpg  "${benchmark_dir}/"
        # Copy archive files to base CGA directory
        cp test_complete_*.tgz  "${benchmark_dir}/"

    - name: Upload benchmark archives
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-archives
        path: test_complete_*.tgz
        retention-days: 14

    - name: Upload montages
      uses: actions/upload-artifact@v3
      with:
        path: collected_images_*/montage_*.jpg
        name: benchmark-montages
        retention-days: 14
