name: Create env file
on:
  workflow_call:

jobs:
  create-env:
    runs-on: largerunner
    steps:
      - name: Create .env file
        run: |
          echo "ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY }}" >> .env
          echo "PERPLEXITY_API_KEY=${{ secrets.PERPLEXITY_API_KEY }}" >> .env
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> .env
          echo "AWS_REGION=${{ secrets.AWS_REGION }}" >> .env
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> .env
          echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
          echo "NEO4J_PASSWORD=${{ secrets.NEO4J_PASSWORD }}" >> .env
          echo "NEO4J_USER=neo4j" >> .env
          echo "NEO4J_CONNECTION_URI=neo4j+s://3ab082c5.databases.neo4j.io" >> .env
