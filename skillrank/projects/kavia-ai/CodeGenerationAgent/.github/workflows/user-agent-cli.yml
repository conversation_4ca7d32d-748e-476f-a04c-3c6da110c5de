name: Templates CLI Workflow

on:
  schedule:
    - cron: '0 0 * * 0'
  workflow_dispatch:

jobs:
  run-cli:
    runs-on: largerunner
    timeout-minutes: 60
    permissions:
      contents: read
      packages: read
    env:
      TEMPORARY_WORKSPACE: ${{ github.workspace }}/workspace
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v3
      with:
        python-version: "3.11"
    
    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -

    - name: Configure Poetry
      run: |
        poetry config virtualenvs.create true
        poetry config virtualenvs.in-project true

    - name: Cache Poetry dependencies
      uses: actions/cache@v3
      id: cache-poetry
      with:
        path: |
          .venv
          ~/.cache/pypoetry
        key: ${{ runner.os }}-poetry-${{ hashFiles('**/poetry.lock', 'pyproject.toml') }}
    
    - name: Create .env file
      run: |
        echo "ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY }}" >> .env
        echo "PERPLEXITY_API_KEY=${{ secrets.PERPLEXITY_API_KEY }}" >> .env
        echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> .env
        echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> .env
        echo "AWS_DEFAULT_REGION=${{ secrets.AWS_REGION }}" >> .env
        echo "S3_METRICS_BUCKET=${{ secrets.S3_METRICS_BUCKET }}" >> .env
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
        echo "NEO4J_PASSWORD=${{ secrets.NEO4J_PASSWORD }}" >> .env
        echo "NEO4J_USER=neo4j" >> .env
        echo "NEO4J_CONNECTION_URI=neo4j+s://3ab082c5.databases.neo4j.io" >> .env
        echo "PYTHONPATH=${{ github.workspace }}/src:${{ github.workspace }}" >> .env

    
    - name: Set up test environment
      run: |
        mkdir -p workspace
        sudo mkdir -p /tmp/kavia
        sudo chmod 777 /tmp/kavia
    
    - name: Set up Docker
      uses: docker/setup-buildx-action@v2
    
    - name: Install dependencies
      run: |
        poetry install
        source setup.sh
    

    
    - name: Pull Docker container
      run: |
        # Pull the current container (like python-app.yml)
        PYTHONPATH=$PWD/src poetry run python -c "from src.code_generation_core_agent.agents.utilities import pull_current_container; pull_current_container()"
    
    - name: Run Templates CLI
      run: |
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitHub Actions"
        
        PYTHONPATH=$PWD/src poetry run python tools/templates-cli/templates_cli.py \
          --tasks-path=test/data/templates/sample_tasks.json \
          --instructions-path=test/data/templates/sample_instructions.j2 \
          --override \
          --budget=2 \
          --model=gpt-4.1 \
          --metrics-path=metrics.json
        
     
      env:
        PYTHONPATH: ${{ github.workspace }}/src
        DOCKER_HOST: unix:///var/run/docker.sock
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        S3_METRICS_BUCKET: ${{ secrets.S3_METRICS_BUCKET }}
        PERPLEXITY_API_KEY: ${{ secrets.PERPLEXITY_API_KEY }}
        NEO4J_PASSWORD: ${{ secrets.NEO4J_PASSWORD }}
        NEO4J_USER: neo4j
        NEO4J_CONNECTION_URI: neo4j+s://3ab082c5.databases.neo4j.io

    - name: Upload metrics to S3
      if: always()
      run: |
        echo "=== Starting S3 metrics upload ==="
        
        # Look for the main metrics file
        METRICS_FILE="/tmp/templates/metrics.json"
        
        if [ -f "$METRICS_FILE" ] && [ -s "$METRICS_FILE" ]; then
          echo "✅ Found metrics file: $METRICS_FILE"
          
          export AWS_ACCESS_KEY_ID="${{ secrets.AWS_ACCESS_KEY_ID }}"
          export AWS_SECRET_ACCESS_KEY="${{ secrets.AWS_SECRET_ACCESS_KEY }}"
          export AWS_DEFAULT_REGION="${{ secrets.AWS_REGION }}"
          export S3_METRICS_BUCKET="${{ secrets.S3_METRICS_BUCKET }}"
          
          
          echo "📤 Uploading metrics file..."
          PYTHONPATH=$PWD/src poetry run python tools/templates-cli/upload_metrics_to_s3.py \
            --metrics-path "$METRICS_FILE" \
            --bucket "${{ secrets.S3_METRICS_BUCKET }}" \
            --prefix "" \
            --region "${{ secrets.AWS_REGION }}"
          
          if [ $? -eq 0 ]; then
            echo "✅ Successfully uploaded metrics to S3"
          else
            echo "❌ Failed to upload metrics to S3"
            exit 1
          fi
        else
          echo "⚠️  No metrics.json file found or file is empty, skipping S3 upload"
        fi