name: Release Branch Workflow

on:
  pull_request_target:
    types: [opened, synchronize, reopened, closed]
    branches: 
      - 'release/**'  # This matches any branch under release/
  workflow_dispatch:

jobs:
  # Run on PR creation/update
  verify-commit-type:
    if: github.event_name == 'pull_request_target' && github.event.pull_request.merged != true
    runs-on: largerunner
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        ref: ${{ github.event.pull_request.head.sha }}
    - name: Check for merge commits
      run: |
        # Check if there are any merge commits in the PR
        if git log --merges ${{ github.event.pull_request.base.sha }}..${{ github.event.pull_request.head.sha }} | grep -q "Merge"; then
          echo "Error: Merge commits are not allowed in release branches"
          exit 1
        fi

  # Run tests on PR
  test:
    if: github.event_name == 'pull_request_target' && github.event.pull_request.merged != true
    runs-on: largerunner
    timeout-minutes: 60
    permissions:
      contents: read
      packages: read
    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}
    - name: Set up Python 3.11
      uses: actions/setup-python@v3
      with:
        python-version: "3.11"
    - name: Create .env file
      run: |
        echo "ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY }}" >> .env
        echo "PERPLEXITY_API_KEY=${{ secrets.PERPLEXITY_API_KEY }}" >> .env
        echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID_BEDROCK }}" >> .env
        echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY_BEDROCK }}" >> .env
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
        echo "NEO4J_PASSWORD=${{ secrets.NEO4J_PASSWORD }}" >> .env
        echo "NEO4J_USER=neo4j" >> .env
        echo "NEO4J_CONNECTION_URI=neo4j+s://3ab082c5.databases.neo4j.io" >> .env

    - name: Set up test environment
      run: |
        sudo mkdir -p /tmp/kavia
        sudo chmod 777 /tmp/kavia

    - name: Set up Docker
      uses: docker/setup-buildx-action@v2
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
    - name: Run tests
      run: |
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitHub Actions"
        source setup.sh
        PYTHONPATH=$PWD/src pytest test/all_tests.py
      env:
        DOCKER_HOST: unix:///var/run/docker.sock

  package-and-deploy:
    if: |
      (github.event_name == 'pull_request_target' && github.event.pull_request.merged == true) ||
      (github.event_name == 'workflow_dispatch')
    runs-on: largerunner
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install build tools
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build the package
      run: |
        python -m build --outdir dist

    - name: Extract version from pyproject.toml
      id: get_version
      run: |
        VERSION=$(grep -Po '(?<=version = ")[^"]*' pyproject.toml)
        echo "VERSION=$VERSION" >> $GITHUB_ENV

    - name: Configure AWS CLI
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Delete existing package version
      continue-on-error: true
      run: |
        aws codeartifact delete-package-versions \
          --domain kavia \
          --domain-owner 058264095463 \
          --repository kavia \
          --format pypi \
          --package code_generation_core_agent \
          --versions ${{ env.VERSION }}

    - name: Login to AWS CodeArtifact
      run: |
        aws codeartifact login \
          --tool twine \
          --repository kavia \
          --domain kavia \
          --domain-owner 058264095463

    - name: Upload package to AWS CodeArtifact
      run: |
        twine upload --repository codeartifact dist/*
