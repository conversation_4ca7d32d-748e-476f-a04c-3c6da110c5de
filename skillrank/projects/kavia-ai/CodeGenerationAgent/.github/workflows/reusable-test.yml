name: Reusable Test Workflow

on:
  workflow_call:

jobs:

  test:
    runs-on: linux-arm64
    timeout-minutes: 60
    permissions:
      contents: read
      packages: read
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.11
      uses: actions/setup-python@v3
      with:
        python-version: "3.11"
    - name: Create .env file
      run: |
        echo "ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY }}" >> .env
        echo "PERPLEXITY_API_KEY=${{ secrets.PERPLEXITY_API_KEY }}" >> .env
        echo "AWS_ACCESS_KEY_ID_BEDROCK=${{ secrets.AWS_ACCESS_KEY_ID }}" >> .env
        echo "AWS_SECRET_ACCESS_KEY_BEDROCK=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> .env
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
        echo "NEO4J_PASSWORD=${{ secrets.NEO4J_PASSWORD }}" >> .env
        echo "NEO4J_USER=neo4j" >> .env
        echo "NEO4J_CONNECTION_URI=neo4j+s://3ab082c5.databases.neo4j.io" >> .env

    - name: Set up test environment
      run: |
        sudo mkdir -p /tmp/kavia
        sudo chmod 777 /tmp/kavia

    - name: Set up Docker
      uses: docker/setup-buildx-action@v2
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
    - name: Run tests
      run: |
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitHub Actions"
        source setup.sh
        PYTHONPATH=$PWD/src pytest test/all_tests.py
      env:
        DOCKER_HOST: unix:///var/run/docker.sock
