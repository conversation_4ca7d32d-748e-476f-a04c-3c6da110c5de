name: Python app test with Dock<PERSON>

on:
  pull_request:
    branches: [ "main" ]

jobs:
  test:
    runs-on: largerunner
    timeout-minutes: 60
    permissions:
      contents: read
      packages: read
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v3
      with:
        python-version: "3.11"

    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -

    - name: Configure Poetry
      run: |
        poetry config virtualenvs.create true
        poetry config virtualenvs.in-project true

    - name: Check KAVIA_CONTAINER_VERSION does not contain 'latest'
      run: |
        if grep -i "latest" src/code_generation_core_agent/docker_version.py; then
          echo "ERROR: KAVIA_CONTAINER_VERSION contains 'latest'"
          exit 1
        fi

    - name: Cache Poetry dependencies
      uses: actions/cache@v3
      id: cache-poetry
      with:
        path: |
          .venv
          ~/.cache/pypoetry
        key: ${{ runner.os }}-poetry-${{ hashFiles('**/poetry.lock', 'pyproject.toml') }}

    - name: Create .env file
      run: |
        echo "ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY }}" >> .env
        echo "PERPLEXITY_API_KEY=${{ secrets.PERPLEXITY_API_KEY }}" >> .env
        echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID_BEDROCK }}" >> .env
        echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY_BEDROCK }}" >> .env
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
        echo "NEO4J_PASSWORD=${{ secrets.NEO4J_PASSWORD }}" >> .env
        echo "NEO4J_USER=neo4j" >> .env
        echo "NEO4J_CONNECTION_URI=neo4j+s://3ab082c5.databases.neo4j.io" >> .env
        echo "PYTHONPATH=${{ github.workspace }}/src:${{ github.workspace }}" >> .env

    - name: Set up test environment
      run: |
        sudo mkdir -p /tmp/kavia
        sudo chmod 777 /tmp/kavia


    - name: Install dependencies and run tests
      run: |
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitHub Actions"
        
        # Install dependencies with Poetry
        poetry install

        # Install playwright browsers        
        poetry run playwright install chromium
        
        # Pull the current container
        PYTHONPATH=$PWD/src poetry run python -c "from src.code_generation_core_agent.agents.utilities import pull_current_container; pull_current_container()"
        # Install py-spy
        poetry run pip install py-spy
        
        # Create directory for profiling output
        mkdir -p profiling_data
        
        # Start the test in the background and capture its PID
        poetry run python -m pytest test/code_generation_test.py & 
        TEST_PID=$!
        
        # Run py-spy with sudo on the captured PID until it exits
        sudo -E env PATH=$PATH:$PWD/src poetry run py-spy record -o profiling_data/code_generation_profile.speedscope --pid $TEST_PID --threads --format speedscope --idle --rate 100 --duration 30
        kill $TEST_PID
        
        # Run all tests
        PYTHONPATH=$PWD/src poetry run pytest test/all_tests.py -v --tb=short
      env:
        DOCKER_HOST: unix:///var/run/docker.sock
        PYTHONPATH: ${{ github.workspace }}/src

    - name: Upload profiling results
      uses: actions/upload-artifact@v4
      with:
        name: profiling-speedscope-traces
        path: profiling_data/
        retention-days: 30
