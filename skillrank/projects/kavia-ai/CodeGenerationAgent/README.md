# Code Generation Agent

## Prerequisites
- Python 3.11+
- Git
- Docker
- Homebrew (on Mac OS)

## Container dependency

This project requires a fixed kavia container version. The version can be updated in:
```
code_generation_core_agent/docker_version.py
```
## Reporting Issues

Read the following document 

[Reporting issues]: docs/reporting.md


## Setup

```bash
<NAME_EMAIL>:Kavia-ai/CodeGenerationAgent.git
cd CodeGenerationAgent
git submodule update --init --recursive
```

On Mac OS:
```bash
brew install python@3.11
python3.11 --version 
```

To validate and setup the environment run the `setup.sh` script

```
source setup.sh
```

## Using Bedrock model

In  `.env` set the following values:
```
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
```
## Docker container support

There are two ways to run the CodeGenerator in a Docker container:

1. Running LLM shell instructions inside a Docker container.

2. Running the complete CodeGenerator and G<PERSON> inside a Docker container.

   
## Skip execution steps
- `DEBUG_SKIP_ORCHESTRATOR_STEPS`: Comma-separated list of steps to skip during orchestration for debugging purposes. Supported values:
  - `initial_setup`: Skips the initial setup phase
  - `asset_extractor`: Skips the asset extraction phase
  - `code_generation`: Skips the code generation phase
  - `docker_export`: Skips the Docker container export
  - `interface_extraction`: Skips the interface extraction phase

Example:
```bash
DEBUG_SKIP_ORCHESTRATOR_STEPS=code_generation PYTHONPATH=$PWD/src \
 pytest test/test_long_code_generation.py::TestCodeGenerator::test_complete_codegen_tictactoe_backend  -s
```
