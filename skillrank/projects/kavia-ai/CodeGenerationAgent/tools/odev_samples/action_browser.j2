[{"type": "function", "function": {"name": "noop", "description": "noop()\nnoop(500)", "parameters": {"type": "object", "properties": {"wait_ms": {"type": "number", "default": "1000"}}, "required": []}}}, {"type": "function", "function": {"name": "send_msg_to_user", "description": "send_msg_to_user('Based on the results of my search, the city was built in 1751.')", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "scroll", "description": "scroll(0, 200)\nscroll(-50.2, -100.5)", "parameters": {"type": "object", "properties": {"delta_x": {"type": "number"}, "delta_y": {"type": "number"}}, "required": ["delta_x", "delta_y"]}}}, {"type": "function", "function": {"name": "fill", "description": "fill('237', 'example value')\nfill('45', 'multi-line\\nexample')\nfill('a12', 'example with \"quotes\"')", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}, "value": {"type": "string"}}, "required": ["bid", "value"]}}}, {"type": "function", "function": {"name": "select_option", "description": "select_option('48', 'blue')\nselect_option('48', ['red', 'green', 'blue'])", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}, "options": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["bid", "options"]}}}, {"type": "function", "function": {"name": "click", "description": "click('51')\nclick('b22', button='right')\nclick('48', button='middle', modifiers=['Shift'])", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}, "button": {"type": "string", "enum": ["left"]}, "modifiers": {"type": "array", "items": {"type": "string"}}}, "required": ["bid", "button", "modifiers"]}}}, {"type": "function", "function": {"name": "dblclick", "description": "dblclick('12')\ndblclick('ca42', button='right')\ndblclick('178', button='middle', modifiers=['Shift'])", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}, "button": {"type": "string", "enum": ["left"]}, "modifiers": {"type": "array", "items": {"type": "string"}}}, "required": ["bid", "button", "modifiers"]}}}, {"type": "function", "function": {"name": "hover", "description": "hover('b8')", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}}, "required": ["bid"]}}}, {"type": "function", "function": {"name": "press", "description": "press('88', 'Backspace')\npress('a26', 'Control+a')\npress('a61', 'Meta+Shift+t')", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}, "key_comb": {"type": "string"}}, "required": ["bid", "key_comb"]}}}, {"type": "function", "function": {"name": "focus", "description": "focus('b455')", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}}, "required": ["bid"]}}}, {"type": "function", "function": {"name": "clear", "description": "clear('996')", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}}, "required": ["bid"]}}}, {"type": "function", "function": {"name": "drag_and_drop", "description": "drag_and_drop('56', '498')", "parameters": {"type": "object", "properties": {"from_bid": {"type": "string"}, "to_bid": {"type": "string"}}, "required": ["from_bid", "to_bid"]}}}, {"type": "function", "function": {"name": "upload_file", "description": "upload_file('572', 'my_receipt.pdf')\nupload_file('63', ['/home/<USER>/Documents/image.jpg', '/home/<USER>/Documents/file.zip'])", "parameters": {"type": "object", "properties": {"bid": {"type": "string"}, "file": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["bid", "file"]}}}, {"type": "function", "function": {"name": "go_back", "description": "go_back()", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "go_forward", "description": "go_forward()", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "goto", "description": "goto('http://www.example.com')\nMultiple actions can be provided at once. Example:", "parameters": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "fill", "description": "", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "click", "description": "", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "click", "description": "Multiple actions are meant to be executed sequentially without any feedback from the page.\nDon't execute multiple actions at once if you need feedback from the page.", "parameters": {"type": "object", "properties": {}, "required": []}}}]