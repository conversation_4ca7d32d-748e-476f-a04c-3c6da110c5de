import os
import time
import argparse
import json
import logging
import litellm

from code_generation_core_agent.agents.framework.knowledge import Knowledge
from code_generation_core_agent.agents.framework.knowledge import KnowledgeCodeBase
from code_generation_core_agent.agents.framework.knowledge import KnowledgeReporter
from knowledge_helper import Knowledge_Helper
from knowledge_helper import Reporter

_logger = None

def get_keys():
    knowledge = Knowledge.getKnowledge()
    result = {
            "status": "SUCCESS",
            "keys": f"{knowledge.getKeys()}"
    }
    return result

def get_key_value(key):
    knowledge = Knowledge.getKnowledge()
    status = "ERROR"
    value = knowledge.getKeyValue(key)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def find_relevant_files(search_terms, and_search, code_base = None):
    knowledge = Knowledge.getKnowledge()
    status = "ERROR"
    value = knowledge.findRelevantFiles(search_terms, and_search, code_base)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def get_source_file_description(file_path):
    knowledge = Knowledge.getKnowledge()
    status = "ERROR"
    value = knowledge.getSourceFileDescription(file_path)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def get_source_file_knowledge(file_path):
    knowledge = Knowledge.getKnowledge()
    status = "ERROR"
    value = knowledge.getSourceFileKnowledge(file_path)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def read_file(filename):
    contents = None
    try:
        with open(filename, 'r') as file:
            contents = file.read()
    except FileNotFoundError:
        pass
    return contents

def _extract_json(message):
    """
    Attempt to extract JSON from text which may contain it in form that is not directly parseable.
    For instance, an llm might return an invalid response, then after being told of the error, returns
    something like 'I appologize, here is the json object {...}'
    """
    data_object = None
    _logger.info("Attempting to extract JSON from a response\n")
    prompt = (f"Examine the following text to see if a valid JSON object"
                f" can be extracted from it.  If so, return only the portion of the text"
                f" that comprises the JSON object stripping away any other text."
                f" If no valid JSON object can be found return the text 'None'."
                f"\nThe text to examine:"
                f"\n\n{message}")
    try:
        response = litellm.completion(
            model="claude-3-5-sonnet-20241022",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that examines text."},
                {"role": "user", "content": prompt}
            ]
        )
        output = response.choices[0].message.content
        try:
            data_object = json.loads(output)
        except json.JSONDecodeError:
            pass
    except Exception as e:
        _logger.error(f"Failed to extract JSON {e}\n")
        pass

    return data_object

def _answer_question(question):
    system_prompt = "You are an expert software engineer."
    
    user_prompt = 'Your task is to answer questions about a software project.  Use the provided tools to get ' \
                'information about the project codebase and to determine what files in the project are relevant to the question. ' \
                'Use read_file to directly read relevant files in order to ensure your answers are accurate.\n ' \
                'For example, if you use find_relevant_files with a set of search terms and this returns a ' \
                'large number of files you might use get_source_file_knowledge to get more information about some ' \
                'likely files to decide which are most promising, then use read_file to read these files ' \
                'to get the best information on which to base your answer. If the information you gather suggests there may '  \
                'be additional sources of information then do additional find_relevant_files searches and reads ' \
                'so that your answer can be based on comprehensive information.  If your answer contains multiline text be sure ' \
                'to format it properly for JSON.\n' \
                f'The current question is: {question}\n' \
                'Please format your final response as a JSON object with the following structure:\n' \
                '{{ ' \
                '   "answer": "<answer to question>"\n' \
                '}}\n' \
                'Your final response MUST be a valid JSON object with NO OTHER non-JSON content.'

    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_keys",
                "description": "Get the list of available knowledge keys",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                    }, 
                    "required": []
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "get_key_value",
                "description": "Get the value associated with a specified knowledge keys",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "key": {"type": "string", "description": "The knowledge key to being queried."},
                    },
                    "required": ["key"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "find_relevant_files",
                "description": "Find relevant files",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "search_terms": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of search terms.",
                        },
                        "and_search": {
                            "type": "boolean",
                            "description": "True for AND search, False for OR search",
                        },
                        "code_base": {
                            "type": "string",
                            "description": "Search only within this named codebase"
                        }
                    },
                    "required": ["search_terms", "and_search"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "get_source_file_description",
                "description": "Get source file description",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "file_path": {"type": "string", "description": "The fully qualified path to the file."},
                    },
                    "required": ["file_path"]
                }
            }
        },
        {
            "type": "function", 
            "function": {
                "name": "get_source_file_knowledge",
                "description": "Get source file knowledge",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "file_path": {"type": "string", "description": "The fully qualified path to the file."},
                    },
                    "required": ["file_path"]
                }
            }
        },
        {

            "type": "function",
            "function": {
                "name": "read_file",
                "description": "Reads the contents of a file",
                "parameters": {
                    "type": "object",
                    "strict":  True,
                    "properties": {
                        "file_path": {"type": "string", "description": "The fully qualified path to the file."},
                    },
                    "required": ["file_path"]
                }
            },
        }         
    ]

    try:

        litellm.drop_params=True
        model_name = "claude-3-5-sonnet-20241022"
        messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
        ]
        while True:
            completion = litellm.completion(
                model=model_name,
                custom_llm_provider= "anthropic",
                response_format={"type": "json_object"},
                messages=messages,
                tools = tools,
                tool_choice="auto"
            )
            tool_calls = completion.choices[0].message.tool_calls
            if tool_calls:
                messages.append(completion.choices[0].message)
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    available_functions = {
                        'get_keys': get_keys,
                        'get_key_value': get_key_value,
                        'find_relevant_files': find_relevant_files,
                        'get_source_file_description': get_source_file_description,
                        'get_source_file_knowledge': get_source_file_knowledge,
                        'read_file': read_file
                    }
                    if tool_call.function.name in available_functions:
                        function_to_call = available_functions[function_name]
                        function_args = json.loads(tool_call.function.arguments)
                        if _logger:
                            _logger.info(f"function: {function_name} args: {tool_call.function.arguments}\n")
                        if function_to_call == get_keys:
                            function_response= get_keys()
                        elif function_to_call == get_key_value:
                            function_response = get_key_value( function_args['key'])
                        elif function_to_call == find_relevant_files:
                            function_response = find_relevant_files( function_args['search_terms'],
                                                                     function_args['and_search'],
                                                                     function_args.get('code_base', None) )
                        elif function_to_call == get_source_file_description:
                            function_response = get_source_file_description( function_args['file_path'])
                        elif function_to_call == get_source_file_knowledge:
                            function_response = get_source_file_knowledge( function_args['file_path'])
                        elif function_to_call == read_file:
                            function_response = read_file( function_args['file_path'] )
                        function_response = json.dumps(function_response)
                        if _logger:
                            _logger.info(f"function: {function_name} result: {function_response}\n")
                        messages.append(
                            {
                                "tool_call_id": tool_call.id,
                                "role": "tool",
                                "name": function_name,
                                "content": function_response,
                            }
            )
            else:
                break

        try:
            response = json.loads(completion.choices[0].message.content.strip())
        except json.JSONDecodeError:
            response = _extract_json(completion.choices[0].message.content.strip() )
        print('\n'+response["answer"]+'\n')
    except Exception as e:
        print(f"Exception {e}")

def _setup_logger(name, base_path, log_level=logging.INFO):
    """
    Set up a logger with a file handler, preventing logs from going to the console.

    :param name: Name of the logger and the log file
    :param base_path: Base path for the log directory
    :param log_level: Logging level (default: logging.INFO)
    :return: Configured logger
    """
    logging.disable(logging.NOTSET)
    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    # Remove any existing handlers (including the default StreamHandler)
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Prevent the logger from propagating messages to the root logger
    logger.propagate = False

    # Create logs directory if it doesn't exist
    log_dir = os.path.join(base_path, "logs")
    os.makedirs(log_dir, exist_ok=True)

    # Create file handler
    file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
    file_handler.setLevel(log_level)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)

    # Add file handler to logger
    logger.addHandler(file_handler)
    logger.propagate = False

    return logger

def _parse_codebase( arg ):
    if ":" not in arg:
        raise argparse.ArgumentTypeError("Codebase must be in the format 'name:path'")
    name, path = arg.split(":", 1)
    return KnowledgeCodeBase(path, name)

def _parse_filetypes( arg ):
    if not isinstance( arg, str ):
        raise argparse.ArgumentTypeError("The file_types argument must be a list of strings each starting with '.'")
    file_types = ''.join(arg.split()).split(',')
    for type in file_types:
        if not isinstance(type,str):
            raise argparse.ArgumentTypeError("The file_types argument must be a list of strings each starting with '.'")
        if not type.startswith('.'):
            raise argparse.ArgumentTypeError("The file_types argument must be a list of strings each starting with '.'")
    return file_types

def main():
    print("knowledge_cli")

    value = os.environ["OPENAI_API_KEY"]
    if not value:
        print("A value must be set in the environment for OPENAI_API_KEY")
        return

    value = os.environ["ANTHROPIC_API_KEY"]
    if not value:
        print("A value must be set in the environment for ANTHROPIC_API_KEY")
        return

    parser = argparse.ArgumentParser(
        description="Kavia knowledge commmand line tool"
    )
    parser.add_argument( '--base_folder', action='store', help="Specify base folder for operation", type=str)
    parser.add_argument( '--codebase', action='append', help="Specify a codebase in the form name:path", type=_parse_codebase)
    parser.add_argument( '--file_types', action='append', help="Specify list of ingestible file types", type=_parse_filetypes)

    args = parser.parse_args()

    base_folder = os.getcwd()
    if args.base_folder:
        base_folder = args.base_folder

    codebases = None
    if args.codebase:
        codebases = args.codebase

    file_types = None
    if args.file_types:
        file_types = args.file_types

    global _logger
    _logger = _setup_logger('knowledge_cli', base_folder)

    reporter = Reporter()
    knowledge_helper = Knowledge_Helper(reporter, base_folder, file_types, codebases)
    knowledge = Knowledge.getKnowledge()
    knowledge.start()

    while( True ):
        if reporter.is_ready():
            question = input("Ask a question: ")
            _answer_question(question)
        time.sleep(1)

main()
