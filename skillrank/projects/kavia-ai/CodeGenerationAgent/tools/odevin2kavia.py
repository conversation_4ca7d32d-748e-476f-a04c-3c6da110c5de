import re
import json
from typing import Any, Dict, List, Union
import unittest
import argparse

def parse_type(type_str: str) -> Dict[str, Any]:
    if type_str == 'str':
        return {"type": "string"}
    elif type_str in ['float', 'int']:
        return {"type": "number"}
    elif type_str.startswith('Literal['):
        options = re.findall(r"'([^']*)'", type_str)
        return {"type": "string", "enum": options}
    elif type_str.startswith('list['):
        inner_type = type_str[5:-1]
        return {"type": "array", "items": parse_type(inner_type)}
    elif '|' in type_str:
        types = [parse_type(t.strip()) for t in type_str.split('|')]
        return {"oneOf": types}
    else:
        return {"type": "string"}  # Default to string for unknown types

def parse_param(param: str) -> Dict[str, Any]:
    match = re.match(r'(\w+):\s*(.+?)(?:\s*=\s*(.+))?$', param)
    if not match:
        raise ValueError(f"Invalid parameter format: {param}")

    name, type_str, default = match.groups()
    param_schema = parse_type(type_str)

    if default:
        param_schema["default"] = default.strip()

    return name, param_schema, default is None

def parse_docstring(docstring: str) -> str:
    lines = [line.strip() for line in docstring.split('\n')]
    lines = [line for line in lines if line and not line.startswith('Examples:')]
    return '\n'.join(lines)

def convert_action_space_to_function_schema(action_space_str: str) -> List[Dict[str, Any]]:
    function_schema = []
    actions = re.split(r'\n(?=\w+\()', action_space_str.strip())

    for action in actions:
        lines = action.strip().split('\n')
        if not lines or lines[0].startswith('#') or not re.match(r'\w+\(', lines[0]):
            continue

        function_def = lines[0]
        match = re.match(r'(\w+)\((.*?)\)', function_def)
        if not match:
            continue

        name, params_str = match.groups()
        params = re.findall(r'(\w+:\s*[^,]+(?:\s*=\s*[^,]+)?)', params_str)

        properties = {}
        required = []
        for param in params:
            try:
                param_name, param_schema, is_required = parse_param(param)
                properties[param_name] = param_schema
                if is_required:
                    required.append(param_name)
            except ValueError as e:
                print(f"Warning: Skipping invalid parameter in {name}: {e}")

        description = parse_docstring("\n".join(lines[1:]))

        function_schema.append({
            "type": "function",
            "function": {
                "name": name,
                "description": description,
                "parameters": {
                    "type": "object",
                    "properties": properties,
                    "required": required
                }
            }
        })

    return function_schema

def convert_file(input_file: str, output_file: str):
    with open(input_file, 'r') as f:
        action_space = f.read()

    function_schema = convert_action_space_to_function_schema(action_space)

    with open(output_file, 'w') as f:
        json.dump(function_schema, f, indent=2)

class TestActionSpaceConverter(unittest.TestCase):
    def test_conversion(self):
        action_space = """
        noop(wait_ms: float = 1000)
            Examples:
                noop()
                noop(500)

        send_msg_to_user(text: str)
            Examples:
                send_msg_to_user('Based on the results of my search, the city was built in 1751.')

        scroll(delta_x: float, delta_y: float)
            Examples:
                scroll(0, 200)
                scroll(-50.2, -100.5)

        select_option(bid: str, options: str | list[str])
            Examples:
                select_option('48', 'blue')
                select_option('48', ['red', 'green', 'blue'])

        click(bid: str, button: Literal['left', 'middle', 'right'] = 'left', modifiers: list[typing.Literal['Alt', 'Control', 'Meta', 'Shift']] = [])
            Examples:
                click('51')
                click('b22', button='right')
                click('48', button='middle', modifiers=['Shift'])
        """

        result = convert_action_space_to_function_schema(action_space)
        self.assertEqual(len(result), 5)  # Check if all functions are parsed
        self.assertEqual(result[0]['function']['name'], 'noop')
        self.assertEqual(result[1]['function']['name'], 'send_msg_to_user')
        self.assertEqual(result[2]['function']['name'], 'scroll')
        self.assertEqual(result[3]['function']['name'], 'select_option')
        self.assertEqual(result[4]['function']['name'], 'click')

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert action space to function schema")
    parser.add_argument("input_file", nargs='?', help="Input file containing action space")
    parser.add_argument("output_file", nargs='?', help="Output file for function schema")
    args = parser.parse_args()

    if args.input_file and args.output_file:
        try:
            convert_file(args.input_file, args.output_file)
            print(f"Conversion successful. Output written to {args.output_file}")
        except Exception as e:
            print(f"Error converting action space: {e}")
    else:
        unittest.main(argv=[''], exit=False)
