import os
import time
import argparse
import json
import logging
import litellm

from code_generation_core_agent.agents.framework.projdefn import ProjDefn
from code_generation_core_agent.agents.framework.projdefn import ProjDefnDocSpecifier
from code_generation_core_agent.agents.framework.projdefn import ProjDefnReporter
from code_generation_core_agent.agents.framework.projdefn import ProjDefnHelpers
from projdefn_helper import ProjDefn_Helper
from projdefn_helper import Reporter

_logger = None

def read_file(filename):
    contents = None
    try:
        with open(filename, 'r') as file:
            contents = file.read()
    except FileNotFoundError:
        pass
    return contents

def _setup_logger(name, base_path, log_level=logging.INFO):
    """
    Set up a logger with a file handler, preventing logs from going to the console.

    :param name: Name of the logger and the log file
    :param base_path: Base path for the log directory
    :param log_level: Logging level (default: logging.INFO)
    :return: Configured logger
    """
    logging.disable(logging.NOTSET)
    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    # Remove any existing handlers (including the default StreamHandler)
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Prevent the logger from propagating messages to the root logger
    logger.propagate = False

    # Create logs directory if it doesn't exist
    log_dir = os.path.join(base_path, "logs")
    os.makedirs(log_dir, exist_ok=True)

    # Create file handler
    file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
    file_handler.setLevel(log_level)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)

    # Add file handler to logger
    logger.addHandler(file_handler)
    logger.propagate = False

    return logger

def get_keys():
    projdefn = ProjDefn.getInstance()
    result = {
            "status": "SUCCESS",
            "keys": f"{projdefn.getKeys()}"
    }
    return result

def get_key_values(keys):
    projdefn = ProjDefn.getInstance()
    status = "ERROR"
    value = projdefn.getKeyValues(keys)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def find_relevant_keys(search_terms, and_search):
    projdefn = ProjDefn.getInstance()
    status = "ERROR"
    value = projdefn.findRelevantKeysSemantic(search_terms, and_search)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def find_relevant_document_chunks(search_terms, and_search):
    projdefn = ProjDefn.getInstance()
    status = "ERROR"
    value = projdefn.findRelevantDocumentChunksSemantic(search_terms, and_search)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def find_relevant_document_images(search_terms, and_search):
    projdefn = ProjDefn.getInstance()
    status = "ERROR"
    value = projdefn.findRelevantDocumentImages(search_terms, and_search)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def get_document_chunk(chunk_spec):
    projdefn = ProjDefn.getInstance()
    status = "ERROR"
    value = projdefn.getDocumentChunk(chunk_spec)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def get_document_image(image_spec):
    projdefn = ProjDefn.getInstance()
    status = "ERROR"
    value = projdefn.getDocumentImage(image_spec)
    if value:
        status = "SUCCESS"
    result = {
            "status": f"{status}",
            "value": f"{value}"
    }
    return result

def _extract_json(message):
    """
    Attempt to extract JSON from text which may contain it in form that is not directly parseable.
    For instance, an llm might return an invalid response, then after being told of the error, returns
    something like 'I appologize, here is the json object {...}'
    """
    data_object = None
    _logger.info("Attempting to extract JSON from a response\n")
    prompt = (f"Examine the following text to see if a valid JSON object"
                f" can be extracted from it.  If so, return only the portion of the text"
                f" that comprises the JSON object stripping away any other text."
                f" If no valid JSON object can be found return the text 'None'."
                f"\nThe text to examine:"
                f"\n\n{message}")
    try:
        response = litellm.completion(
            model="claude-3-5-sonnet-20241022",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that examines text."},
                {"role": "user", "content": prompt}
            ]
        )
        output = response.choices[0].message.content
        try:
            data_object = json.loads(output)
        except json.JSONDecodeError:
            pass
    except Exception as e:
        _logger.error(f"Failed to extract JSON {str(e)}\n")
        pass

    return data_object

def _answer_question(question):
    system_prompt = "You are an expert software engineer."
    
    user_prompt = 'Your task is to answer questions about a software project based on a project definition.  Use the provided tools to get ' \
                'information about the project definition and to determine what knowledge keys are relevant to the question. ' \
                'Use find_relevant_keys to find knowedge keys based on search terms and use get_key_values to get the values ' \
                'of lists of knowledge keys.  If your answer contains multiline text be sure ' \
                'to format it properly for JSON.\n' \
                f'The current question is: {question}\n' \
                'Please format your final response as a JSON object with the following structure:\n' \
                '{{ ' \
                '   "answer": "<answer to question>"' \
                '}}\n' \
                'Your final response MUST be a valid JSON object with NO OTHER non-JSON content.'

    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_keys",
                "description": "Get the list of available knowledge keys",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                    }, 
                    "required": []
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "get_key_values",
                "description": "Get the values associated with a specified knowledge keys",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "keys": {
                            "type": "array",
                            "items": {"type": "string"},
                        }
                    },
                    "required": ["keys"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "find_relevant_keys",
                "description": "Find relevant knowledge keys",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "search_terms": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of search terms.",
                        },
                        "and_search": {
                            "type": "boolean",
                            "description": "True for AND search, False for OR search",
                        }
                    },
                    "required": ["search_terms", "and_search"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "find_relevant_document_chunks",
                "description": "Find relevant knowledge document chunks",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "search_terms": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of search terms.",
                        },
                        "and_search": {
                            "type": "boolean",
                            "description": "True for AND search, False for OR search",
                        }
                    },
                    "required": ["search_terms", "and_search"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "find_relevant_document_images",
                "description": "Find relevant knowledge document images",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "search_terms": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of search terms.",
                        },
                        "and_search": {
                            "type": "boolean",
                            "description": "True for AND search, False for OR search",
                        }
                    },
                    "required": ["search_terms", "and_search"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "get_document_chunk",
                "description": "Get the text of a specified document chunk.  This is useful to access the text directly.",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "chunk_spec": {
                            "type": "string",
                            "description": "a chunk specifier returned from find_relevant_document_chunks",
                        }
                    },
                    "required": ["chunk_spec"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "get_document_image",
                "description": "Get a specified document image.  This is useful for when it is necessary to access detail beyond what is in the image description.",
                "parameters": {
                    "type": "object",
                    "strict": True,
                    "properties": {
                        "image_spec": {
                            "type": "string",
                            "description": "an image specifier returned from find_relevant_document_images",
                        }
                    },
                    "required": ["image_spec"]
                }
            }
        },
    ]

    try:

        litellm.drop_params=True
        model_name = "claude-3-5-sonnet-20241022"
        #model_name = "bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0"
        messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
        ]
        while True:
            completion = litellm.completion(
                model=model_name,
                custom_llm_provider= "anthropic", #"bedrock",
                response_format={"type": "json_object"},
                messages=messages,
                tools = tools,
                tool_choice="auto"
            )
            tool_calls = completion.choices[0].message.tool_calls
            if tool_calls:
                messages.append(completion.choices[0].message)
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    available_functions = {
                        'get_keys': get_keys,
                        'get_key_values': get_key_values,
                        'find_relevant_keys': find_relevant_keys,
                        'find_relevant_document_chunks' : find_relevant_document_chunks,
                        'find_relevant_document_images' : find_relevant_document_images,
                        'get_document_chunk' : get_document_chunk,
                        'get_document_image' : get_document_image
                    }
                    if tool_call.function.name in available_functions:
                        function_to_call = available_functions[function_name]
                        function_args = json.loads(tool_call.function.arguments)
                        if _logger:
                            _logger.info(f"function: {function_name} args: {tool_call.function.arguments}\n")
                        if function_to_call == get_keys:
                            function_response= get_keys()
                        elif function_to_call == get_key_values:
                            function_response = get_key_values( function_args['keys'])
                        elif function_to_call == find_relevant_keys:
                            function_response = find_relevant_keys( function_args['search_terms'],
                                                                    function_args['and_search'] )
                        elif function_to_call == find_relevant_document_chunks:
                            function_response = find_relevant_document_chunks( function_args['search_terms'],
                                                                    function_args['and_search'] )
                        elif function_to_call == find_relevant_document_images:
                            function_response = find_relevant_document_images( function_args['search_terms'],
                                                                    function_args['and_search'] )
                        elif function_to_call == get_document_chunk:
                            function_response = get_document_chunk( function_args['chunk_spec'])
                        elif function_to_call == get_document_image:
                            function_response = get_document_image( function_args['image_spec'])
                        function_response = json.dumps(function_response)
                        if _logger:
                            _logger.info(f"function: {function_name} result: {function_response}\n")
                        messages.append(
                            {
                                "tool_call_id": tool_call.id,
                                "role": "tool",
                                "name": function_name,
                                "content": function_response,
                            }
            )
            else:
                break

        try:
            response = json.loads(completion.choices[0].message.content.strip())
        except json.JSONDecodeError:
            response = _extract_json(completion.choices[0].message.content.strip() )
        print('\n'+response["answer"]+'\n')
    except Exception as e:
        print(f"Exception {e}")

def _parse_doc( arg ):
    if ":" not in arg:
        raise argparse.ArgumentTypeError("Document specifier must be in the format 'name:path'")
    name, path = arg.split(":", 1)
    return ProjDefnDocSpecifier(path, name)

def _parse_json( arg ):
    contents = None
    try:
        with open(arg, 'r') as file:
            try:
                criteria = json.load(file)
            except json.JSONDecodeError:
                raise argparse.ArgumentTypeError("Criteria file must contain valid JSON")
            return criteria
    except FileNotFoundError:
        pass
    except UnicodeDecodeError:
        pass
    raise argparse.ArgumentTypeError("Criteria must provide the path of a JSON file")

def _parse_criteria_filter( arg ):
    if not isinstance( arg, str ):
        raise argparse.ArgumentTypeError("The criteria_filter argument must be a list of strings")
    filters = ''.join(arg.split()).split(',')
    return filters

def main():
    print("projdefn_cli")

    value = os.environ["OPENAI_API_KEY"]
    if not value:
        print("A value must be set in the environment for OPENAI_API_KEY")
        return

    value = os.environ["ANTHROPIC_API_KEY"]
    if not value:
        print("A value must be set in the environment for ANTHROPIC_API_KEY")
        return

    parser = argparse.ArgumentParser(
        description="Kavia project definition commmand line tool"
    )
    parser.add_argument( '--base_folder', action='store', help="Specify base folder for operation", type=str)
    parser.add_argument( '--document', action='append', help="Specify a document in the form name:path", type=_parse_doc)
    parser.add_argument( '--criteria', action='store', help="Specify a JSON file specifying project definition criteria", type=_parse_json)
    parser.add_argument( '--criteria_filter', action='store', help='Specify a list of criteria to allow for queries', type=_parse_criteria_filter)

    args = parser.parse_args()

    base_folder = os.getcwd()
    if args.base_folder:
        base_folder = args.base_folder

    documents = None
    if args.document:
        documents = args.document

    criteria = None
    if args.criteria:
        criteria = args.criteria

    criteria_filter = None
    if args.criteria_filter:
        criteria_filter = args.criteria_filter

    global _logger
    _logger = _setup_logger('projdefn_cli', base_folder)

    reporter = Reporter()
    projdefn_helper = ProjDefn_Helper(reporter, base_folder, documents, criteria)
    projdefn = ProjDefn.getInstance()
    if ( criteria_filter ):
        projdefn.setCriteriaFilter( criteria_filter )
    projdefn.start()

    while( True ):
        if reporter.is_ready():
            question = input("Ask a question: ")
            _answer_question(question)
        time.sleep(1)

main()