import os
import base64
import shlex
from openai import OpenAIError, OpenAI

from code_generation_core_agent.agents.framework.projdefn import ProjDefn
from code_generation_core_agent.agents.framework.projdefn import ProjDefnReporter
from code_generation_core_agent.agents.framework.projdefn import ProjDefnHelpers

class Reporter(ProjDefnReporter):
    def __init__(self):
        self.ready = False
        pass

    def send_agent_message(self, message):
        if 'Project definition creation complete' in message:
            self.ready = True
        pass

    def cost_update_callback(self, all_costs, total_cost ):
        pass

    def is_ready(self):
        return self.ready

class Helpers(ProjDefnHelpers):
    def __init__(self, base_path):
        self.base_path = base_path
        global client
        api_key = os.environ["OPENAI_API_KEY"]
        client = OpenAI(api_key=api_key)

    def execute_cmd(self, cmd):
        import subprocess
        try:
            result = subprocess.run(cmd, capture_output=True, shell=True, text=True)
            return result.stdout, result.returncode
        except Exception as e:
            print(f"Error: cmd {cmd} had exception {e}")
        return None, None

    def read_file(self, filename):
        contents = None
        try:
            with open(filename, 'r') as file:
                contents = file.read()
        except UnicodeDecodeError:
            with open(filename, 'rb') as file:
                contents = file.read()
        except FileNotFoundError:
            pass
        except UnicodeDecodeError:
            pass
        return contents
    
    def write_file(self, filename, content):
        try:
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            with open(filename, 'w', encoding='utf-8') as file:
                file.write(content)

            return 0
        except Exception as e:
            print(f"Error writing file: {e}")
            return 1

    def list_directory(self, directory):
        list = []
        try:
            list = os.listdir(directory)
        except FileNotFoundError:
            pass
        except NotADirectoryError:
            pass
        return list

    def get_embedding(self, text):
        def generate_embedding(properties):
                properties_text = properties_to_text(properties)
                response = client.embeddings.create(
                    input=properties_text,
                    model="text-embedding-ada-002"
                )
                embedding = response.data[0].embedding
                return embedding

        def properties_to_text(properties):
                return ", ".join([f"{key}: {value}" for key, value in properties.items()])

        def unique_words(text):
            import re
            words = re.findall(r'\b\w+\b', text.lower())
            seen = set()
            unique_list = []

            for word in words:
                if word not in seen:
                    seen.add(word)
                    unique_list.append(word)

            return unique_list

        content = ' '.join(unique_words(text)).strip()
        embedding = generate_embedding({'content': content})
        return embedding

class ProjDefn_Helper:
    def __init__(self, reporter, base_path, documents = None, criteria = None):
        configuration = {
            "base_path" : base_path,
            "model" : "gpt-4.1-nano-2025-04-14",
            "timeout": 60,
            "chunk_size": 64*1024,
            "cost_tracer" : None,
            "reporter" : reporter,
            "helpers" : Helpers(base_path)
        }
        if documents:
            configuration['documents'] = documents
        if criteria:
            configuration['criteria'] = criteria

        self.knowledge = ProjDefn.getInstance(configuration)

