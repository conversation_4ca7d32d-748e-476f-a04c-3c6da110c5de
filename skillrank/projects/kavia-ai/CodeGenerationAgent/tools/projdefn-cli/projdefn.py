import asyncio
import base64
import copy
import logging
import hashlib
import json
import os
import re
import threading
import time
from abc import ABC, abstractmethod

_lock = threading.Lock()

_default_criteria = {
            "description": "- high level descriptions of project",
            "purpose_and_responsibiliies": "- the main objectives and functionalities of the project",
            "functional_requirements": "- functional requirements",
            "non_functional_requirements": "- non-functional requirements affecting system performance, scalability, and security",
            "standards_compliance": "- standards compliance requirements",
            "regulatory_compliance": "- regulatory compliance requirements",
            "security_requirements": "- security requirements",
            "subsystem_descriptions": "- detailed descriptions of subsystems or features",
            "component_interactions": "- details of how system components interact with users and external systems",
            "behavior_descriptions": "- details of behavioral responsibilities of key system interactions and functionalities",
            "data_management": "- data storage requirements and data flow between components",
            "error_handling": "- potential error scenarios and strategies for error handling",
            "test_considerations": "- important testing aspects and areas to focus on for testing",
            "input_data":  "- descriptions of required input data",
            "output_data": "- descriptions of desired output data",
            "dependencies": "- list the project's dependencies essential for functionality",
            "code_languages": "- implementation programming languages to be used",
            "frameworks": "- implementation frameworks to be used",
            "algorithns": "- algorithms to use",
            "code_fragments": "- sample code supplied for normative or explanatory purposes",
}

class ProjDefnReporter(ABC):
    @abstractmethod
    def send_agent_message(self, message):
        pass

    @abstractmethod
    def cost_update_callback(self, all_costs, total_cost ):
        pass

class ProjDefnHelpers(ABC):
    @abstractmethod
    def execute_cmd(self, cmd):
        pass

    @abstractmethod
    def read_file(self, filename):
        pass

    @abstractmethod
    def write_file(self, filename, content):
        pass

    @abstractmethod
    def list_directory(self, directory):
        pass

    @abstractmethod
    def get_embedding(self, text):
        pass


class ProjDefnDocPage:
    def __init__(self, number:int, text:str):
        self.page_number = number
        self.page_text = text

class ProjDefnDocImage:
    def __init__(self, name:str, image_bytes:bytes, image_format:str, image_info:dict):
        self.image_name = name
        if isinstance(image_bytes, bytes):
            base64_encoded = base64.b64encode(image_bytes)
            image_bytes = base64_encoded.decode('utf-8')
        self.image_bytes = image_bytes
        self.image_format = image_format
        self.image_info = image_info

class ProjDefnDocChunk:
    def __init__(self, start_page:int, end_page:int, text:str):
        self.start_page = start_page
        self.end_page = end_page
        self.chunk_text = text
        self.doc_info = {}

class ProjDefnDocSpecifier:
    def __init__(self, pathname, name = None, file_uuid = None):
        self.name = name
        self.pathname = pathname
        self.file_uuid = None

class ProjDefnDoc:
    def __init__(self, spec:ProjDefnDocSpecifier):
        self.spec = spec
        self.hash = None
        self.need_refresh = False
        self.title = "Untitled"
        self.pages = []
        self.chunks = []
        self.images = []
        self.doc_info = {}

    def add_page(self, page:ProjDefnDocPage):
        self.pages.append(page)

    def add_chunk(self, chunk:ProjDefnDocChunk ):
        self.chunks.append(chunk)
    
    def add_image(self, image:ProjDefnDocImage ):
        self.images.append(image)

    def page_count(self):
        return len(self.pages)

    def chunk_count(self):
        return len(self.chunks)
    
    def get_chunks(self):
        return self.chunks

    def get_chunks_info(self):
        chunks_info = []
        for chunk in self.chunks:
            start_page = chunk.start_page
            end_page = chunk.end_page
            chunk_text = chunk.chunk_text
            doc_info = chunk.doc_info
            chunks_info.append({'doc_info': doc_info, 'start_page': start_page, 'end_page': end_page, 'chunk_text': chunk_text})
        return chunks_info

    def get_pages(self):
        return self.pages

    def get_pages_info(self):
        pages_info = []
        for page in self.pages:
            page_number = page.page_number
            page_text = page.page_text
            pages_info.append({'page_number': page_number, 'page_text': page_text})
        return pages_info

    def get_images(self):
        return self.images

    def get_images_info(self):
        images_info = []
        for image in self.images:
            image_name = image.image_name
            image_bytes = image.image_bytes
            image_format = image.image_format
            image_info = image.image_info
            images_info.append({'image_name': image_name, 'image_bytes': image_bytes, 'image_format': image_format, 'image_info': image_info})
        return images_info

    def set_image_text(self, placeholder, image_text):
        for page in self.pages:
            if placeholder in page.page_text:
                page.page_text = page.page_text.replace( placeholder, image_text, 1)
                break
        for chunk in self.chunks:
            if placeholder in chunk.chunk_text:
                chunk.chunk_text = chunk.chunk_text.replace( placeholder, image_text, 1)
                break

    def set_title(self, title:str):
        self.title = title

    def get_title(self) -> str:
        return self.title

    def add_info(self, key:str, value:str):
        self.doc_info[key] = value

    def get_info(self, key:str) -> str:
        info = self.doc_info.get(key)
        return info
    
    def get_doc_info(self) -> dict:
        return self.doc_info
    
    def get_doc_spec(self) -> ProjDefnDocSpecifier:
        return self.spec
    
    def set_doc_hash(self, hash):
        self.hash = hash

    def get_doc_hash(self):
        return self.hash

class ProjDefn:

    _instances = {}

    def __init__(self, id:str, config:dict):
        self.id = id
        self.config = config
        self.base_path = config['base_path']
        self.model = config['model']
        self.chunk_size = config['chunk_size']
        self.timeout = config['timeout']
        self.num_ingest_threads = config.get('num_ingest_threads', 7)
        self.reporter = config['reporter']
        self.helpers = config['helpers']
        self.cost_tracer = config['cost_tracer']
        self._lock = threading.Lock()
        self._lockConsolidation = threading.Lock()
        self.loop_worker = None
        self.thread_worker = None
        self.stop_worker = False
        self.worker_stopped = False
        self._defnLocked = False
        self._ingest_queue = []
        self._defn_docs = {}
        self._need_refresh = False
        self._full_refresh = False
        self._project_definition = {}
        self._criteriaFilter = None
        logger_name = config.get('logger_name','projdefn')
        self.logger = self._setup_logger(logger_name, self.base_path)
        if 'documents' in config:
            documents = config['documents']
            for doc in documents:
                self.addToIngestQueue(doc.name, doc.pathname)
        self.criteria = _default_criteria
        if 'criteria' in config:
            self.criteria = config['criteria']
        if 'session_path' in config:
            self.projdef_path = config['session_path']
        else:
            self.projdef_path = os.path.join(self.base_path,".projdefn")

    def _setup_logger(self, name, base_path, log_level=logging.INFO):
        """
        Set up a logger with a file handler, preventing logs from going to the console.

        :param name: Name of the logger and the log file
        :param base_path: Base path for the log directory
        :param log_level: Logging level (default: logging.INFO)
        :return: Configured logger
        """
        logger = logging.getLogger(name)
        logger.setLevel(log_level)

        # Remove any existing handlers (including the default StreamHandler)
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Prevent the logger from propagating messages to the root logger
        logger.propagate = False

        # Create logs directory if it doesn't exist
        log_dir = os.path.join(base_path, "logs")
        os.makedirs(log_dir, exist_ok=True)

        # Create file handler
        file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
        file_handler.setLevel(log_level)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add file handler to logger
        logger.addHandler(file_handler)
        logger.propagate = False

        return logger

    def _get_file_hash(self, filename):
        hash = None
        content = self.helpers.read_file(filename)
        if content:
            encoded_content = content
            if not isinstance(content,bytes):
                try:
                    encoded_content = content.encode("utf-8")
                except UnicodeEncodeError:
                    encoded_content = content
            hash = hashlib.md5(encoded_content).hexdigest()
        return hash

    def _add_definitions(self,doc:ProjDefnDoc, can_refresh:bool = False):
        doc_spec = doc.get_doc_spec()
        name = doc_spec.name
        if name in self._defn_docs:
            self._defn_docs.pop(name)
            self._need_refresh = True
        elif can_refresh:
            self._need_refresh = True
        self._defn_docs[name] = doc

    def _persist_project_definition(self):
        self.logger.info(f"persist master project definition data")
        base_path = self.base_path
        with self._lock:
            folder = os.path.join(base_path, '.projdefn')
            persistent_filename = os.path.join( folder, 'project_definition.json')
            content = json.dumps(self._project_definition)
            self.helpers.write_file( persistent_filename, content)

    def _persist_document_definition(self, doc:ProjDefnDoc):
        defn_info = {}
        doc_spec = doc.get_doc_spec()
        defn_info['name'] = doc_spec.name
        defn_info['pathname'] = doc_spec.pathname
        defn_info['title'] = doc.get_title()
        defn_info['hash'] = doc.get_doc_hash()
        defn_info['chunks'] = doc.get_chunks_info()
        defn_info['pages'] = doc.get_pages_info()
        defn_info['images'] = doc.get_images_info()
        defn_info['doc_info'] = doc.get_doc_info()
        self.logger.info(f"persist ingested definition data for {doc_spec.name}")
        base_path = self.base_path
        with self._lock:
            folder = os.path.join(base_path, '.projdefn')
            persistent_filename = os.path.join( folder, 'doc__'+doc_spec.name+'.json')
            content = json.dumps(defn_info)
            self.helpers.write_file( persistent_filename, content)

    def _unpersist_document_definition(self, doc:ProjDefnDoc):
        doc_spec = doc.get_doc_spec()
        base_path = self.base_path
        folder = self.projdef_path
        persistent_filename = os.path.join( folder, 'doc__'+doc_spec.name+'.json')
        os.remove(persistent_filename)

    def _set_chunk_embedding(self, doc_info, text):
        result = False
        embedding = self.helpers.get_embedding(text)
        if embedding:
            doc_info['.embedding'] = embedding
            result = True
        return result

    def _set_defn_embeddings(self):
        result = False
        defn_info = self._project_definition
        defn_info['.embeddings'] = {}
        for criteria,value in defn_info.items():
            if not criteria.startswith('.'):
                embedding = self.helpers.get_embedding(value)
                if embedding:
                    defn_info['.embeddings'][criteria] = embedding
                    result = True
        return result

    def _load_persisted_project_definition(self):
        folder_name = self.projdef_path
        files = self.helpers.list_directory(folder_name)
        for file in files:
            if file.startswith('.'):
                continue
            if file.endswith('.json'):
                self.logger.info(f"loading persisted definitions ({os.path.basename(file)})")
                try:
                    data = self.helpers.read_file(os.path.join(folder_name,file))
                    defn_info = json.loads(data)
                    if file == 'project_definition.json':
                        self._project_definition = defn_info
                        if not '.embeddings' in defn_info:
                            if self._set_defn_embeddings():
                                self._persist_project_definition()
                    elif file.startswith('doc__'):
                        name = defn_info['name']
                        pathname = defn_info['pathname']
                        title = defn_info.get('title')
                        hash = defn_info.get('hash')
                        chunks = defn_info.get('chunks')
                        pages = defn_info.get('pages')
                        images = defn_info.get('images')
                        doc_info = defn_info.get('doc_info')
                        doc_spec = ProjDefnDocSpecifier(pathname,name)
                        doc = ProjDefnDoc(doc_spec)
                        doc.set_title(title)
                        doc.set_doc_hash(hash)
                        if chunks:
                            need_to_persist = False
                            for chunk_item in chunks:
                                start_page = chunk_item.get('start_page')
                                end_page = chunk_item.get('end_page')
                                chunk_text = chunk_item.get('chunk_text')
                                doc_info = chunk_item.get('doc_info')
                                chunk = ProjDefnDocChunk(start_page, end_page, chunk_text)
                                if '.embedding' not in doc_info:
                                    if self._set_chunk_embedding( doc_info, chunk_text ):
                                        need_to_persist = True
                                chunk.doc_info = doc_info
                                doc.add_chunk( chunk )
                            if need_to_persist:
                                self._persist_document_definition(doc)
                        if pages:
                            for page_item in pages:
                                page_number= page_item.get('page_number')
                                page_text = page_item.get('page_text')
                                doc.add_page( ProjDefnDocPage(page_number,page_text))
                        if images:
                            for image_item in images:
                                image_name = image_item.get('image_name')
                                image_bytes = image_item.get('image_bytes')
                                image_format = image_item.get('image_format')
                                image_info = image_item.get('image_info')
                                doc.add_image( ProjDefnDocImage(image_name, image_bytes, image_format, image_info))
                        for key,value in doc_info.items():
                            doc.add_info( key, value)
                        self._add_definitions(doc)

                        if 'hash' in defn_info:
                            hash = self._get_file_hash(pathname)
                            if hash and hash != defn_info['hash']:
                                doc.need_refresh = True
                                self.logger.info(f'modified document ({name}) detected: hash mismatch')
                                self.addToIngestQueue(doc_spec.name, doc_spec.pathname)
                except Exception as e:
                    self.logger.error(f"_load_peristed_project_definition had exception {str(e)} for file {file}")
                    pass
                self.logger.info(f"done loading persisted definitions ({os.path.basename(file)})")
        if not self._project_definition:
            self._need_refresh = True

    def _examine_image(self, doc, image_name, image_bytes, image_format, extract_only):
        import litellm
        image_text = ""
        title = doc.get_title()
        self.logger.info(f"start examination of image {image_name} in document {title}")
        model_name = self.model
        system_prompt = "You are an expert software architect."
        user_prompt = "Your task is to examine the supplied image from a document which is being provided to help " \
                    f"define a software development project.  The image is part of a document with the title '{title}'.\n " \
                    "Provide a detailed description of the contents of the image including any text in the image.\n " \
                    "If the image contains a diagram try to replicate the diagram in text form in your description.\n " \
                    "If the image contains a table try to replicate the table in text from in your description.\n " \
                    "Please format your final response as a JSON object with the following structure:\n" \
                    "{{ " \
                    "   'description': '<string: detailed description of image contents>'" \
                    "   'text': '<string: any text contained by the image'" \
                    "}}" \
                    f"If your response contains multiline text be sure to format it properly for JSON.\n"

        base64_encoded = base64.b64encode(image_bytes)
        image_base64 = base64_encoded.decode('utf-8')

        item_data = {'type': 'image_url',
                    'image_url': {'url': f'data:image/{image_format};base64,{image_base64}'}}
        user_prompt_content = []
        user_prompt_content.append({'type': 'text', 'text': f'{user_prompt}'})
        user_prompt_content.append(item_data)

        try:
            response = litellm.completion(
                model=model_name,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": [{'type': 'text', 'text': f'{system_prompt}'}]},
                    {"role": "user", "content": user_prompt_content}
                ]
            )
            output = response.choices[0].message.content
            image_info = json.loads(output)
            if extract_only:
                image_text = f"{image_info['text']}"
            else:
                image_text = f"image: [description: {image_info['description']} text_in_image: {image_info['text']}]"

        except Exception as e:
            self.logger.error(f"_examine_image had exception processing image {image_name} for doc ({title}): {str(e)}")
            pass

        self.logger.info(f"done examination of image {image_name} in document {title}")

        return image_text

    def _process_image(self, doc, image_name, image_bytes, image_format, image_width, image_height, extract_only=False ):
        from PIL import Image
        import io
        reduced_bytes = image_bytes
        if image_width == -1 or image_height == -1:
            image = Image.open(io.BytesIO(image_bytes))
            image_width = image.width
            image_height = image.height
        image_size = (image_width, image_height)
        while len(reduced_bytes) > self.chunk_size:
            quality = 80
            image_size = (int(image_size[0]*2/3), int(image_size[1]*2/3))
            image = Image.open(io.BytesIO(image_bytes))
            image = image.resize(image_size, Image.LANCZOS)
            output = io.BytesIO()
            image.save(output, format=image_format, quality=quality)
            reduced_bytes = output.getvalue()
        image_bytes = reduced_bytes
        image_text = self._examine_image(doc, image_name, image_bytes, image_format, extract_only)
        if not extract_only:
            doc.add_image( ProjDefnDocImage(image_name, image_bytes, image_format, image_text))
        return image_text

    def _examine_document(self, doc:ProjDefnDoc):
        import concurrent
        import litellm
        title = doc.get_title()
        self.logger.info(f"start examination of ({title})")
        file_uuid = doc.spec.file_uuid
        # Get total number of chunks and criteria for progress tracking
        chunks = doc.get_chunks()
        total_chunks = len(chunks)
        criteria_list = self.criteria
        total_criteria = len(criteria_list)
        total_operations = total_chunks * total_criteria
        processed_operations = 0
        progress_lock = threading.Lock()  # Lock for thread-safe progress updates

        # Track processed operations
        def increment_processed_operations():
            nonlocal processed_operations
            with progress_lock:
                processed_operations += 1
                # Send progress updates if file_uuid is provided
                if file_uuid and hasattr(self.reporter, 'progress_update_callback'):
                    progress_data = {
                        "total_chunks": total_chunks,
                        "total_criteria": total_criteria,
                        "total_operations": total_operations,
                        "processed_operations": processed_operations,
                        "remaining_operations": total_operations - processed_operations,
                        "percentage": int((processed_operations / total_operations) * 100)
                    }
                    self.reporter.progress_update_callback(file_uuid, progress_data)

        # Process a single chunk for a specific criteria
        def process_chunks_criteria(criteria_key, criteria_value):
            if self.stop_worker:
                self.logger.info("stop requested: halting examination")
                return None
            doc_info = None
            chunk_num = 0
            chunks = doc.get_chunks()
            num_chunks = len(chunks)
            for chunk in chunks:
                chunk_num += 1
                if criteria_key not in chunk.doc_info:
                    model_name = self.model
                    system_prompt = "You are an expert software architect."
                    user_prompt = "Your task is to read the supplied text from a document which is being provided to help " \
                                f"define a software development project.  The text provided in this request is part {chunk_num} of {num_chunks} of " \
                                f"a document with the title '{title}'.\n " \
                                "As you read this text extract key information that defines the project in terms of the following critera: \n" \
                                f"{criteria_value}\n" \
                                "Please format your final response as a JSON object with the following structure:\n" \
                                "{{ " \
                                "   'relevant_data_chunk': '<string: extracted data from this chunk matching criteria>'" \
                                "   'relevant_data': '<string: consolidated extracted data matching criteria>'" \
                                "}}" \
                                f"If your response contains multiline text be sure to format it properly for JSON.\n" \
                                f"The current document text to examine is: ({chunk.chunk_text}).\n"
                    current_info = doc.get_info(criteria_key)
                    if current_info:
                        user_prompt += f"\nRelevant data from all prior document chunks for this criteria is ({current_info}). Consolidate this with new " \
                                        "information extracted from the current text such that no information is lost while eliminating any redundancy."
                    try:
                        self.logger.info(f"examine doc chunk {chunk_num} of {num_chunks} for criteria '{criteria_key}'")
                        response = litellm.completion(
                            model=model_name,
                            response_format={"type": "json_object"},
                            messages=[
                                {"role": "system", "content": system_prompt},
                                {"role": "user", "content": user_prompt}
                            ]
                        )
                        output = response.choices[0].message.content
                        prompt_tokens = int(response.usage.prompt_tokens)
                        completion_tokens = int(response.usage.completion_tokens)
                        if self.cost_tracer:
                            with self._lock:  # Use a lock for cost tracking to avoid race conditions
                                self.cost_tracer.add_cost_from_completion_response("Projdefn", response)
                                if self.reporter.cost_update_callback:
                                    all_costs = self.cost_tracer.get_all_costs()
                                    total_cost = self.cost_tracer.get_total_cost()
                                    self.reporter.cost_update_callback(all_costs, total_cost)
                        try:
                            doc_info = json.loads(output)
                            with self._lock:  # Use a lock when modifying shared document data
                                if 'relevant_data_chunk' in doc_info:
                                    chunk.doc_info[criteria_key] = doc_info['relevant_data_chunk']
                                if 'relevant_data' in doc_info:
                                    doc.add_info(criteria_key, doc_info['relevant_data'])
                            # Persist document definition periodically to save progress
                            if (chunk_num % 5) == 0 or chunk_num == num_chunks:
                                self._persist_document_definition(doc)
                        except json.JSONDecodeError:
                            self.logger.error(f"Failed to parse JSON response for criteria '{criteria_key}'")
                            return None
                    except Exception as e:
                        self.logger.error(f"_examine_document had exception processing doc ({title}) for criteria '{criteria_key}': {str(e)}")
                        return None
                    finally:
                        increment_processed_operations()
                else:
                    self.logger.debug(f"keeping current chunk knowledge for critera \'{criteria_key}\'")
            return doc_info

        max_workers = min(total_criteria, self.num_ingest_threads)  # Limit max concurrent workers to avoid overwhelming API
        self.logger.info(f"Starting document processing with {max_workers} worker threads")
        # Use ThreadPoolExecutor to process criteria in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for criteria_key, criteria_value in criteria_list.items():
                self.logger.info(f"examine doc ({title}) for criteria '{criteria_key}'")
                # Submit task to thread pool
                future = executor.submit(
                    process_chunks_criteria,
                    criteria_key,
                    criteria_value
                )
                futures.append(future)
            # Wait for all futures to complete
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()  # Get the result to catch any exceptions
                except Exception as e:
                    self.logger.error(f"Exception in thread: {str(e)}")
        # Final document persistence
        self._persist_document_definition(doc)
        self.logger.info(f"done examination of ({title})")

    def _parse_document(self, docspec:ProjDefnDocSpecifier) -> ProjDefnDoc:
        doc = ProjDefnDoc(docspec)
        pathname = docspec.pathname
        hash = self._get_file_hash(pathname)
        doc.set_doc_hash(hash)
        title = os.path.basename(pathname)
        doc.set_title(title)
        root, ext = os.path.splitext(pathname.lower())
        if ext in ['.pdf']:
            doc = self._parse_pdf_document(docspec, doc)
        elif ext in ['.docx']:
            doc = self._parse_docx_document(docspec, doc)
        elif ext in ['.xlsx']:
            doc = self._parse_xlsx_document(docspec, doc)
        elif ext in ['.png','.jpg','.jpeg','.gif']:
            doc = self._parse_image_document(docspec, doc)
        if doc:
            for chunk in doc.chunks:
                self._set_chunk_embedding( chunk.doc_info, chunk.chunk_text )
            self.logger.info(f"doc: {title} pages {doc.page_count()} chunks {doc.chunk_count()}")
            self._persist_document_definition(doc)
        return doc

    def _parse_image_document(self, docspec:ProjDefnDocSpecifier, doc:ProjDefnDoc) -> ProjDefnDoc:
        from PIL import Image
        import io
        pathname = docspec.pathname
        try:
            image = Image.open(pathname)
            if image:
                output = io.BytesIO()
                image_format = image.format
                image_width, image_height = image.size
                image.save(output, format=f"{image_format}")
                image_bytes = output.getvalue()
                image_text = self._process_image( doc, pathname, image_bytes, image_format, image_width, image_height )
                chunk = []
                page_number = chunk_start_page = chunk_end_page = 1
                chunk.append(image_text)
                doc.add_chunk( ProjDefnDocChunk(chunk_start_page, chunk_end_page, '\n'.join(chunk)) )
                page = ProjDefnDocPage(page_number, image_text)
                doc.add_page(page)
            else:
                self.logger.error(f"Error with document contents: {pathname}")
                doc = None

        except Exception as e:
            self.logger.error(f"Exception in _parse_image_document: {str(e)}")
            doc = None

        return doc

    def _parse_docx_document(self, docspec:ProjDefnDocSpecifier, doc:ProjDefnDoc) -> ProjDefnDoc:
        from docx import Document
        from docx.oxml.ns import qn
        from docx.oxml import parse_xml
        import io
        import concurrent
        pathname = docspec.pathname

        max_workers = self.num_ingest_threads
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []

            def parse_image( doc, placeholder_text, image_name, image_bytes, image_format ):
                image_text = self._process_image( doc, image_name, image_bytes, image_format, -1, -1 )
                with self._lock:
                    doc.set_image_text(placeholder_text, image_text)

            try:
                word_doc = Document(pathname)
                if word_doc:
                    NAMESPACE = {"w": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}
                    NAMESPACE_MAP = {
                        "a": "http://schemas.openxmlformats.org/drawingml/2006/main",
                        "wp": "http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing",
                    }
                    page_size = int(self.chunk_size / 16)
                    chunk_start_page = 1
                    chunk_end_page = 1
                    chunk_bytes = 0
                    chunk = []
                    chunk_overlap_text = []
                    overlap_pages = 1
                    page_number = 0
                    page_text = ""
                    rels = word_doc.part.rels
                    body = word_doc.element.body
                    for element in body:
                        page_breaks = False

                        if element.tag == qn('w:p'):
                            para = element
                            text = "".join([r.text for r in para.iter(qn('w:t')) if r.text])
                            if text.strip():
                                page_text = "\n".join([page_text,text]).strip()
                            page_breaks = para.findall(".//w:br[@w:type='page']", namespaces=NAMESPACE)
                            for run in para.iter(qn("w:r")):
                                drawing = run.find(".//w:drawing", namespaces=run.nsmap)
                                if drawing is None:
                                    drawing = run.find(".//w:pict", namespaces=run.nsmap)
                                if drawing is not None:
                                    nsmap = drawing.nsmap if drawing.nsmap else {}
                                    nsmap.update({k: v for k, v in NAMESPACE_MAP.items() if k not in nsmap})
                                    blip = drawing.find(".//a:blip", namespaces=nsmap)
                                    if blip is not None:
                                        embed_key = "{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed"
                                        if embed_key in blip.attrib:
                                            r_id = blip.attrib[embed_key]
                                            if r_id in rels:
                                                image_part = rels[r_id].target_part
                                                image_data = image_part.blob
                                                output = io.BytesIO(image_data)
                                                image_format = os.path.splitext(image_part.partname)[-1].removeprefix('.')
                                                image_bytes = output.getvalue()
                                                placeholder_text = f"image_{image_part.partname}"
                                                future = executor.submit(
                                                    parse_image,
                                                    doc,
                                                    placeholder_text,
                                                    image_part.partname,
                                                    image_bytes,
                                                    image_format
                                                )
                                                futures.append(future)
                                                page_text = "\n".join([page_text,placeholder_text]).strip()

                        elif element.tag == qn('w:tbl'):
                            table = next(tbl for tbl in word_doc.tables if tbl._element is element)
                            seen_cells = set()
                            for row in table.rows:
                                row_data = []
                                for cell in row.cells:
                                    if cell._tc not in seen_cells:
                                        seen_cells.add(cell._tc)
                                        row_data.append(cell.text.strip())
                                        for para in cell.paragraphs:
                                            for run in para.runs:
                                                drawing = run._element.find(".//w:drawing", namespaces=run._element.nsmap)
                                                if drawing is None:
                                                    drawing = run._element.find(".//w:pict", namespaces=run._element.nsmap)
                                                if drawing is not None:
                                                    nsmap = drawing.nsmap if drawing.nsmap else {}
                                                    nsmap.update({k: v for k, v in NAMESPACE_MAP.items() if k not in nsmap})
                                                    blip = drawing.find(".//a:blip", namespaces=nsmap)
                                                    if blip is not None:
                                                        embed_key = "{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed"
                                                        if embed_key in blip.attrib:
                                                            r_id = blip.attrib[embed_key]
                                                            if r_id in rels:
                                                                image_part = rels[r_id].target_part
                                                                image_data = image_part.blob
                                                                output = io.BytesIO(image_data)
                                                                image_format = os.path.splitext(image_part.partname)[-1]
                                                                image_bytes = output.getvalue()
                                                                placeholder_text = f"image_{image_part.partname}"
                                                                future = executor.submit(
                                                                    parse_image,
                                                                    doc,
                                                                    placeholder_text,
                                                                    image_part.partname,
                                                                    image_bytes,
                                                                    image_format
                                                                )
                                                                futures.append(future)
                                                                row_data.apend(placeholder_text.strip())
                                row_text = " ".join([row_text for row_text in row_data])
                                page_text = "\n".join([page_text,row_text]).strip()

                        elif element.tag == qn('w:drawing') or element.tag == qn('w:pict'):
                            rels = word_doc.part.rels
                            for rel in rels.values():
                                if "image" in rel.target_ref:
                                    image_part = rel.target_part
                                    image_data = io.BytesIO(image_part.blob)
                                    image_bytes = image_data.getvalue()
                                    image_format = image_part.partname.split(".")[-1]
                                    placeholder_text = f"image_{image_part.partname}"
                                    future = executor.submit(
                                        parse_image,
                                        doc,
                                        placeholder_text,
                                        image_part.partname,
                                        image_bytes,
                                        image_format
                                    )
                                    futures.append(future)
                                    page_text = "\n".join([page_text,placeholder_text]).strip()

                        page_bytes = len(page_text)
                        page_lines = page_text.count('\n')

                        if not page_breaks:
                            if page_bytes >= page_size or page_lines >= 80:
                                page_breaks = True

                        if chunk_bytes + page_bytes > self.chunk_size:
                            doc.add_chunk( ProjDefnDocChunk(chunk_start_page, chunk_end_page, '\n'.join(chunk)) )
                            chunk = []
                            chunk_bytes = 0
                            chunk_start_page = page_number
                            chunk_end_page = page_number-1
                            # overlap chunks
                            for overlap_text in chunk_overlap_text:
                                chunk_start_page -= 1
                                chunk.append(overlap_text)
                                chunk_bytes += len(overlap_text)
                        if page_breaks:
                            chunk.append(page_text)
                            chunk_bytes += page_bytes
                            page_number = page_number+1
                            page = ProjDefnDocPage(page_number, page_text)
                            doc.add_page(page)
                            page_text = ""
                            page_bytes = 0
                            page_lines = 0
                            chunk_overlap_text.append(page_text)
                            if len(chunk_overlap_text) > overlap_pages:
                                chunk_overlap_text = chunk_overlap_text[1:]

                    if page_bytes:
                        chunk.append(page_text)
                        page_number = page_number+1
                        page = ProjDefnDocPage(page_number, page_text)
                        doc.add_page(page)
                    if chunk:
                        chunk_end_page = page_number-1
                        doc.add_chunk( ProjDefnDocChunk(chunk_start_page, chunk_end_page, '\n'.join(chunk)) )
                else:
                    self.logger.error(f"Error with document contents: {pathname}")
                    doc = None

            except Exception as e:
                self.logger.error(f"Exception in _parse_docx_document: {str(e)}")
                doc = None

            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"Exception in thread: {str(e)}")

        return doc

    def _parse_xlsx_document(self, docspec:ProjDefnDocSpecifier, doc:ProjDefnDoc) -> ProjDefnDoc:
        import zipfile
        import concurrent
        from xml.etree import ElementTree
        from openpyxl import load_workbook

        pathname = docspec.pathname

        max_workers = self.num_ingest_threads
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []

            def parse_image( doc, placeholder_text, image_name, image_bytes, image_format ):
                image_text = self._process_image( doc, image_name, image_bytes, image_format, -1, -1 )
                with self._lock:
                    doc.set_image_text(placeholder_text, image_text)

            try:
                wb = load_workbook(pathname, data_only=True)
                if wb is not None:

                    sheet_map = {}
                    images = {}
                    drawing_files = {}
                    with zipfile.ZipFile(pathname, 'r') as z:
                        # First establish a mappng between worksheet sheets and the corresponding xml structure
                        with z.open("xl/workbook.xml") as f:
                            tree = ElementTree.parse(f)
                            root = tree.getroot()
                            namespace = {"ns": "http://schemas.openxmlformats.org/spreadsheetml/2006/main"}

                            sheets = root.findall("ns:sheets/ns:sheet", namespace)
                            for sheet in sheets:
                                sheet_map[sheet.attrib['name']] = f"sheet{sheet.attrib['sheetId']}.xml"

                        # Next identify and gather all images the workbook uses and their anchor positions
                        media_files = [f for f in z.namelist() if f.startswith("xl/media/")]
                        for media_file in media_files:
                            image_name = media_file.removeprefix("xl/media/")
                            image_data = z.read(media_file)
                            image_format = media_file.split(".")[-1]
                            images[image_name] = { 'image_name': image_name, 'image_data': image_data, 'image_format': image_format}

                        sheet_rels_files = [f for f in z.namelist() if f.startswith("xl/worksheets/_rels")]
                        for sheet_rels_file in sheet_rels_files:
                            sheet = sheet_rels_file.removeprefix('xl/worksheets/_rels/').removesuffix('.rels')
                            with z.open(sheet_rels_file) as rels_file:
                                tree = ElementTree.parse(rels_file)
                                root = tree.getroot()
                                namespace = {"rel": "http://schemas.openxmlformats.org/package/2006/relationships"}
                                for rel in root.findall("rel:Relationship", namespace):
                                    if "drawing" in rel.attrib["Type"]:
                                        drawing_file = rel.attrib["Target"].replace("../", "xl/")
                                        drawing_files[sheet] = drawing_file

                        for sheet,draw_file in drawing_files.items():
                            xml_data = z.read(draw_file)
                            tree = ElementTree.fromstring(xml_data)
                            for path in [".//{http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing}oneCellAnchor",
                                        ".//{http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing}twoCellAnchor"]:
                                for anchor in tree.findall(path):
                                    pic = anchor.find("{http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing}pic")
                                    if pic is not None:
                                        blip = pic.find(".//{http://schemas.openxmlformats.org/drawingml/2006/main}blip")
                                        if blip is not None:
                                            embed = blip.get("{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed")
                                            draw_name = draw_file.removeprefix('xl/drawings/')
                                            drawing_rels_path = f"xl/drawings/_rels/{draw_name}.rels"
                                            if drawing_rels_path in z.namelist():
                                                rels_data = z.read(drawing_rels_path).decode()
                                                rels_root = ElementTree.fromstring(rels_data)
                                                namespace = {"rel": "http://schemas.openxmlformats.org/package/2006/relationships"}
                                                for rel in rels_root.findall("rel:Relationship", namespace):
                                                    rid = rel.get("Id")
                                                    if rid ==  embed:
                                                        target = rel.get("Target")
                                                        if "media" in target:
                                                            target = target.split('/')[-1]
                                                            namespace = {"xdr": "http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing"}
                                                            from_cell = anchor.find("xdr:from", namespace)
                                                            row = int(from_cell.find("xdr:row", namespace).text)+1
                                                            col = int(from_cell.find("xdr:col", namespace).text)+1
                                                            #print(f"{target} : ({sheet}-{row},{col})")
                                                            images[target]['sheet'] = sheet
                                                            images[target]['row'] = row
                                                            images[target]['col'] = col

                    def get_image(sheet_id, row_id, cell_id):
                        for image in images.values():
                            if sheet_id == image.get('sheet') and row_id == image.get('row') and col_id == image.get('col'):
                                return image
                        return None

                    page_size = int(self.chunk_size / 8)
                    chunk_start_page = 1
                    chunk_end_page = 1
                    chunk_bytes = 0
                    chunk = []
                    chunk_overlap_text = []
                    overlap_pages = 1
                    page_number = 0
                    page_text = ""
                    for sheet in wb.worksheets:
                        page_breaks = False
                        page_text = sheet.title+'\n\n'
                        sheet_id = sheet_map.get(sheet.title,"")
                        row_id = 1
                        col_id = 1
                        for row_id, row in enumerate(sheet.iter_rows(values_only=True), start=1):
                            row_text = []
                            for col_id, cell_value in enumerate(row, start=1):
                                if cell_value is not None:
                                    row_text.append(str(cell_value))
                                image = get_image(sheet_id, row_id, col_id)
                                if image:
                                    image_name = image['image_name']
                                    image_format = image['image_format']
                                    image_bytes = image['image_data']
                                    placeholder_text = f"image_{image_name}"
                                    future = executor.submit(
                                        parse_image,
                                        doc,
                                        placeholder_text,
                                        image_name,
                                        image_bytes,
                                        image_format
                                    )
                                    futures.append(future)
                                    row_text.append(placeholder_text.strip())
                            row_line = " ".join(row_text)
                            if row_line.strip():
                                page_text= '\n'.join([page_text,f"{row_line}"])

                            page_bytes = len(page_text)
                            page_lines = page_text.count('\n')

                            if not page_breaks:
                                if page_bytes >= page_size or page_lines >= 80:
                                    page_breaks = True

                            if chunk_bytes + page_bytes > self.chunk_size:
                                doc.add_chunk( ProjDefnDocChunk(chunk_start_page, chunk_end_page, '\n'.join(chunk)) )
                                chunk = []
                                chunk_bytes = 0
                                chunk_start_page = page_number
                                chunk_end_page = page_number-1
                                # overlap chunks
                                for overlap_text in chunk_overlap_text:
                                    chunk_start_page -= 1
                                    chunk.append(overlap_text)
                                    chunk_bytes += len(overlap_text)
                            if page_breaks:
                                chunk.append(page_text)
                                chunk_bytes += page_bytes
                                page_number = page_number+1
                                page = ProjDefnDocPage(page_number, page_text)
                                doc.add_page(page)
                                page_text = ""
                                page_bytes = 0
                                page_lines = 0
                                page_breaks = False
                                chunk_overlap_text.append(page_text)
                                if len(chunk_overlap_text) > overlap_pages:
                                    chunk_overlap_text = chunk_overlap_text[1:]

                        for image in images.values():
                            if sheet_id == image['sheet']:
                                irow = image['row']
                                icol = image['col']
                                if irow > row_id or (irow == row_id and icol > col_id):
                                    image_name = image['image_name']
                                    image_format = image['image_format']
                                    image_bytes = image['image_data']
                                    placeholder_text = f"image_{image_name}"
                                    future = executor.submit(
                                        parse_image,
                                        doc,
                                        placeholder_text,
                                        image_name,
                                        image_bytes,
                                        image_format
                                    )
                                    futures.append(future)
                                    page_text = '\n'.join([page_text,placeholder_text]).strip()
                                    page_bytes = len(page_text)

                        if page_bytes:
                            chunk.append(page_text)
                            page_number = page_number+1
                            page = ProjDefnDocPage(page_number, page_text)
                            doc.add_page(page)
                        if chunk:
                            chunk_end_page = page_number
                            doc.add_chunk( ProjDefnDocChunk(chunk_start_page, chunk_end_page, '\n'.join(chunk)) )
                            chunk_start_page = page_number
                            chunk_bytes = 0
                            chunk = []
                            chunk_overlap_text = []
                else:
                    self.logger.error(f"Error with document contents: {pathname}")
                    doc = None

            except Exception as e:
                self.logger.error(f"Exception in _parse_xlsx_document: {str(e)}")
                doc = None

            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"Exception in thread: {str(e)}")

        return doc

    def _parse_pdf_document(self, docspec:ProjDefnDocSpecifier, doc:ProjDefnDoc) -> ProjDefnDoc:
        import fitz
        import concurrent
        pathname = docspec.pathname
        try:
            parsed_doc = fitz.open(pathname)
            if parsed_doc:
                max_workers = self.num_ingest_threads
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = []

                    metadata = parsed_doc.metadata
                    if 'title' in metadata and len(metadata['title']) > 0:
                        title = metadata['title']
                        doc.set_title(title)
                    title= doc.title
                    chunk_start_page = 1
                    chunk_end_page = 1
                    chunk_bytes = 0
                    chunk = []
                    chunk_overlap_text = []
                    overlap_pages = 1
                    for parsed_page in parsed_doc:
                        page_images = parsed_page.get_images()
                        page_text = parsed_page.get_text()
                        if not page_text:
                            pix = parsed_page.get_pixmap(matrix=fitz.Matrix(1,1))
                            image_bytes = pix.tobytes("png")
                            image_format = "PNG"
                            image_width = pix.width
                            image_height = pix.height
                            image_name = f"page_{parsed_page.number+1}.png"
                            page_text = self._process_image( doc, image_name, image_bytes, image_format, image_width, image_height, extract_only=True)
                        else:
                            image_text = ""
                            if page_images:
                                for img_index, img in enumerate(page_images):
                                    xref = img[0]
                                    def parse_image(doc, parsed_doc, placeholder_text, xref):
                                        base_image = parsed_doc.extract_image(xref)
                                        image_name = f'xref-{xref}'
                                        image_width = base_image["width"]
                                        image_height = base_image["height"]
                                        image_bytes = base_image["image"]
                                        image_format = base_image["ext"]
                                        image_text = self._process_image( doc, image_name, image_bytes, image_format, image_width, image_height )
                                        with self._lock:
                                            doc.set_image_text(placeholder_text, image_text)
                                    placeholder_text = f"image_{xref}"
                                    page_text = " ".join([page_text,placeholder_text])
                                    future = executor.submit(
                                        parse_image,
                                        doc,
                                        parsed_doc,
                                        placeholder_text,
                                        xref,
                                    )
                                    futures.append(future)

                        page_number = parsed_page.number+1
                        page_bytes = len(page_text)
                        chunk_end_page = page_number-1
                        if chunk_bytes + page_bytes > self.chunk_size:
                            doc.add_chunk( ProjDefnDocChunk(chunk_start_page, chunk_end_page, '\n'.join(chunk)) )
                            chunk = []
                            chunk_bytes = 0
                            chunk_start_page = page_number
                            # overlap chunks
                            for overlap_text in chunk_overlap_text:
                                chunk_start_page -= 1
                                chunk.append(overlap_text)
                                chunk_bytes += len(overlap_text)
                        chunk.append(page_text)
                        chunk_bytes += page_bytes
                        page = ProjDefnDocPage(page_number, page_text)
                        doc.add_page(page)
                        chunk_overlap_text.append(page_text)
                        if len(chunk_overlap_text) > overlap_pages:
                            chunk_overlap_text = chunk_overlap_text[1:]
                    if chunk:
                        doc.add_chunk( ProjDefnDocChunk(chunk_start_page, chunk_end_page, '\n'.join(chunk)) )

                    for future in concurrent.futures.as_completed(futures):
                        try:
                            future.result()
                        except Exception as e:
                            self.logger.error(f"Exception in thread: {str(e)}")
            else:
                self.logger.error(f"Error with document contents: {pathname}")
                doc = None

        except Exception as e:
            self.logger.error(f"Exception in _parse_pdf_document: {str(e)}")
            doc = None

        return doc

    def _consolidate_definitions(self):
        import litellm
        self.logger.info("begin consolidation of project definitions")
        if self._full_refresh:
            self._project_definition.clear()
            self._full_refresh = False
        for doc in self._defn_docs.values():
            title = doc.get_title()
            doc_info = doc.get_doc_info()
            for criteria_key, criteria_value in doc_info.items():
                if self.stop_worker:
                    self.logger.info("stop requested: halting consolidation")
                    return
                if criteria_key.startswith('.'):
                    continue
                master_value = self._project_definition.get(criteria_key)
                if not master_value or len(self._defn_docs) == 1:
                    if not master_value or len(criteria_value) > len(master_value):
                        master_value = criteria_value
                else:
                    model_name = self.model
                    system_prompt = "You are an expert software architect."
                    user_prompt = "Your task is to examine project definition data extracted from different documents and " \
                                "consolidate them into a master project definition.  You are provided with text representing " \
                                "project definition data that has been extracted from a definition document for a certain criteria " \
                                "and also the text representing the current master definition data for that same criteria.  You need " \
                                "to update the master data by combining the new data with the current data such that no information is " \
                                "lost while eliminating any redunancy.  If the new data conflicts with the current data flag this by " \
                                "setting the 'conflicts' field in your final reponse to text that describes the conflict but update the " \
                                "master definition to be consistent with the new data. If there are no conflicts omit the 'conflicts' field " \
                                "from your response. Differences should only be considered a conflict if there is an actual contridiction " \
                                "(for example the master data says 'use X' but the new data says to 'not use X').  Otherwise the updated " \
                                "master text should be a combination of the old master and the new value.\n " \
                                "The definition criteria for the supplied new text and current master text:\n " \
                                f"{criteria_key}\n " \
                                f"The new text: ({criteria_value})\n " \
                                f"The current master text: ({master_value})\n " \
                                "Please format your final response as a JSON object with the following structure:\n" \
                                "{{ " \
                                "   'new_master': '<string: consolidated data for this criteria>' " \
                                "   'conflicts': '<string describing any conflicts detected during consolidation>' " \
                                "}}" \
                                f"If your response contains multiline text be sure to format it properly for JSON.\n"
                    try:
                        response = litellm.completion(
                            model=model_name,
                            response_format={"type": "json_object"},
                            messages=[
                                {"role": "system", "content": system_prompt},
                                {"role": "user", "content": user_prompt}
                            ]
                        )
                        output = response.choices[0].message.content

                        prompt_tokens = int(response.usage.prompt_tokens)
                        completion_tokens = int(response.usage.completion_tokens)

                        if self.cost_tracer:
                            self.cost_tracer.add_cost_from_completion_response("Projdefn", response)
                            if self.reporter.cost_update_callback:
                                all_costs = self.cost_tracer.get_all_costs()
                                total_cost = self.cost_tracer.get_total_cost()
                                self.reporter.cost_update_callback(all_costs, total_cost)

                        try:
                            doc_info = json.loads(output)
                        except json.JSONDecodeError:
                            pass
                    except Exception as e:
                        self.logger.error(f"_consolidate_definition had exception processing doc ({title}) for criteria \'{criteria_key}\':  {str(e)}")
                        pass
                    conflicts = doc_info.get('conflicts')
                    if conflicts:
                        self.logger.warning(f"_consolidate_definition: definition conflict: {conflicts} with doc ({title} for criteria \'{criteria_key}\'")
                    master_value = doc_info.get('new_master')
                if master_value:
                    self._project_definition[criteria_key] = master_value
        self._set_defn_embeddings()
        self.logger.info("done consolidation of project definitions")
        self._persist_project_definition()
        self.reporter.send_agent_message("Project definition creation complete")
        self._need_refresh = False


    def _ingest_document(self, docspec:ProjDefnDocSpecifier):
        doc = None
        with self._lock:
            if docspec.name in self._defn_docs:
                doc = self._defn_docs[docspec.name]
                if doc.need_refresh:
                    doc= None
                else:
                    doc = copy.deepcopy( doc )
        if not doc:
            doc = self._parse_document(docspec)
        if doc:
            self._examine_document(doc)
            self._persist_document_definition(doc)
            self._add_definitions(doc,True)

    def _service_ingest_queue(self):
        docspec = None
        with self._lock:
            if self._ingest_queue:
                docspec = self._ingest_queue[0]
                self._ingest_queue= self._ingest_queue[1:]
        
        if docspec:
            self._ingest_document(docspec)
        return

    def _worker_loop(self):
        self.loop_worker = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop_worker)
        self._load_persisted_project_definition()
        if self._project_definition and not self._ingest_queue:
            self.reporter.send_agent_message("Project definition creation complete")

        while True:
            if not self._defnLocked:
                try:
                    self._service_ingest_queue()
                except Exception as e:
                    self.logger.error(f"Exception servicing ingest queue {str(e)}")

            if self.stop_worker:
                self.worker_stopped = True
                break

            with self._lockConsolidation:
                if not self._defnLocked:
                    need_refresh = False
                    with self._lock:
                        if not self._ingest_queue:
                            need_refresh = self._need_refresh

                if need_refresh:
                    self._consolidate_definitions()

            self.loop_worker.run_until_complete( asyncio.sleep(1) )

        return None

    def _is_ingestible(self, item_path):
        item = os.path.basename(item_path)
        ingestible = True
        if item.startswith('.'):
            ingestible = False
        good_suffix = False
        for suffix in ['.pdf','.docx','.xlsx','.jpg','.jpeg','.png','.gif']:
            if item_path.lower().endswith(suffix):
                good_suffix = True
                break
        if not good_suffix:
            ingestible = False
        return ingestible

    def _is_unit_match(self, term, text):
        # Create a pattern with word boundaries around term
        pattern = r'\b' + re.escape(term) + r'\b'
        
        # Search for the pattern in text, ignoring case
        return bool(re.search(pattern, text, re.IGNORECASE))

    def _cosine_similarity(self, vec1, vec2):
        import numpy as np
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        dot_product = np.dot(vec1, vec2)
        norm_vec1 = np.linalg.norm(vec1)
        norm_vec2 = np.linalg.norm(vec2)
        if norm_vec1 == 0 or norm_vec2 == 0:
            return 0.0
        return dot_product / (norm_vec1 * norm_vec2)

    def findRelevantKeysSemantic(self, search_terms, and_search):
        relevant_keys = []
        max_items = 6
        search_embeddings = []
        if and_search:
            text = ' '.join(search_terms).strip()
            embedding_search = self.helpers.get_embedding(text)
            search_embeddings.append(embedding_search)
        else:
            for term in search_terms:
                embedding_search = self.helpers.get_embedding(term)
                search_embeddings.append(embedding_search)
        embeddings = self._project_definition['.embeddings']
        for embedding_search in search_embeddings:
            for key in self._project_definition.keys():
                embedding_key = embeddings.get(key)
                if embedding_key:
                    similarity = self._cosine_similarity(embedding_search,embedding_key)
                    if not relevant_keys:
                        relevant_keys.append((similarity,key))
                    else:
                        inserted = False
                        for i in range(0,len(relevant_keys)):
                            if similarity > relevant_keys[i][0]:
                                relevant_keys.insert(i,(similarity,key))
                                inserted = True
                                break
                        if not inserted:
                            relevant_keys.append((similarity,key))
                    if len(relevant_keys) > max_items:
                        relevant_keys.pop()

        results = []
        for item in relevant_keys:
            key = item[1]
            if not key in results:
                results.append(item[1])

        return results

    def findRelevantKeys(self, search_terms, and_search):
        relevant_keys = []

        for key, value in self._project_definition.items():
            matchWithKey = False

            if self._criteriaFilter:
                if key not in self._criteriaFilter:
                    continue

            def search_value(key, value, term):
                match = False
                if isinstance(value,dict):
                    domain = value
                    domain_keys = domain.keys()
                    for key in domain_keys:
                        match = search_value(key, domain[key], term)
                        if match:
                            break
                elif isinstance(value,list):
                    match = self._is_unit_match(term,f"{value}")
                elif isinstance(value,str):
                    match = self._is_unit_match(term,value)
                if match:
                    self.logger.debug(f'task knowledge match key {key} with term {term}')
                return match

            for term in search_terms:
                match = search_value(key,value,term)

                matchWithKey |= match
                if and_search and not match:
                    matchWithKey = False
                    break
                if not and_search and match:
                    break

            if matchWithKey:
                if not key in relevant_keys:
                    relevant_keys.append(key)

        results = []
        for key in relevant_keys:
            results.append( key )

        return results

    def findRelevantDocumentChunksSemantic(self, search_terms, and_search):
        relevant_chunks = []
        max_items = 2
        search_embeddings = []
        if and_search:
            text = ' '.join(search_terms).strip()
            embedding_search = self.helpers.get_embedding(text)
            search_embeddings.append(embedding_search)
        else:
            for term in search_terms:
                embedding_search = self.helpers.get_embedding(term)
                search_embeddings.append(embedding_search)
        for embedding_search in search_embeddings:
            for doc in self._defn_docs.values():
                chunks = doc.get_chunks()
                for chunk in chunks:
                    embedding_chunk = chunk.doc_info.get('.embedding')
                    if embedding_chunk:
                        similarity = abs(self._cosine_similarity(embedding_search, embedding_chunk))
                        if not relevant_chunks:
                            relevant_chunks.append((similarity,doc,chunk))
                        else:
                            inserted = False
                            for i in range(0,len(relevant_chunks)):
                                if similarity > relevant_chunks[i][0]:
                                    relevant_chunks.insert(i,(similarity,doc,chunk))
                                    inserted = True
                                    break
                            if not inserted:
                                relevant_chunks.append((similarity,doc,chunk))
                        if len(relevant_chunks) > max_items:
                            relevant_chunks.pop()

        results = []
        for item in relevant_chunks:
            doc = item[1]
            chunk = item[2]
            doc_spec = doc.get_doc_spec()
            chunk_spec = f"doc__{doc_spec.name}__pages_{chunk.start_page}_{chunk.end_page}"
            if not chunk_spec in results:
                results.append(chunk_spec)

        return results


    def findRelevantDocumentChunks(self, search_terms, and_search):
        relevant_chunks = []

        for doc in self._defn_docs.values():
            chunks = doc.get_chunks()
            for chunk in chunks:
                for key, value in chunk.doc_info.items():
                    matchWithChunk = False

                    if self._criteriaFilter:
                        if key not in self._criteriaFilter:
                            continue

                    def search_value(key, value, term):
                        match = False
                        if isinstance(value,dict):
                            domain = value
                            domain_keys = domain.keys()
                            for key in domain_keys:
                                match = search_value(key, domain[key], term)
                                if match:
                                    break
                        elif isinstance(value,list):
                            match = self._is_unit_match(term,f"{value}")
                        elif isinstance(value,str):
                            match = self._is_unit_match(term,value)
                        if match:
                            self.logger.debug(f'task knowledge match key {key} with term {term}')
                        return match

                    for term in search_terms:
                        match = search_value(key,value,term)

                        matchWithChunk |= match
                        if and_search and not match:
                            matchWithChunk = False
                            break
                        if not and_search and match:
                            break

                    if not matchWithChunk:
                        for term in search_terms:
                            match = re.search(re.escape(term), chunk.chunk_text, re.IGNORECASE) is not None
                            matchWithChunk |= match
                            if and_search and not match:
                                matchWithChunk = False
                                break
                            if not and_search and match:
                                break

                    if matchWithChunk:
                        doc_spec = doc.get_doc_spec()
                        chunk_spec = f"doc__{doc_spec.name}__pages_{chunk.start_page}_{chunk.end_page}"
                        if not chunk_spec in relevant_chunks:
                            relevant_chunks.append(chunk_spec)

        results = []
        for chunk_spec in relevant_chunks:
            results.append( chunk_spec )

        return results

    def findRelevantDocumentImages(self, search_terms, and_search):
        relevant_images = {}

        for doc in self._defn_docs.values():
            images = doc.get_images()
            for image in images:
                matchWithImage = False

                for term in search_terms:
                    match = re.search(re.escape(term), image.image_info, re.IGNORECASE) is not None

                    matchWithImage |= match
                    if and_search and not match:
                        matchWithImage = False
                        break
                    if not and_search and match:
                        break

                if matchWithImage:
                    doc_spec = doc.get_doc_spec()
                    image_spec = f"doc__{doc_spec.name}__image__{image.image_name}"
                    relevant_images[image_spec] = image.image_info

        results = []
        for image_spec, image_info in relevant_images.items():
            results.append( f"{{ 'image_spec': {image_spec}, 'description': {image_info} }}" )

        return results

    def getDocumentChunk(self, chunk_spec:str):
        value = None
        parts = chunk_spec.split("__")
        name = parts[1]
        try:
            start_page, end_page = map(int, parts[2].split("_")[1:])
            start_page = int(start_page)
            end_page = int(end_page)
            if name in self._defn_docs:
                doc = self._defn_docs[name]
                chunks = doc.get_chunks()
                for chunk in chunks:
                    if chunk.start_page <= start_page and chunk.end_page >= end_page:
                        value = chunk.chunk_text
                        break
        except (ValueError, IndexError) as e:
            self.logger.error(f"Error parsing chunk_spec ({chunk_spec}): {str(e)}")
        return value

    def getDocumentImage(self, image_spec:str):
        value = None
        parts = image_spec.split("__")
        if len(parts) >= 3:
            doc_name = parts[1]
            image_name = parts[3]
            if doc_name in self._defn_docs:
                doc = self._defn_docs[doc_name]
                images = doc.get_images()
                for image in images:
                    if image_name == image.image_name:
                        value = {'type': 'image_url',
                                    'image_url': {'url': f'data:image/{image.image_format};base64,{image.image_bytes}'}}
                        break
        return value

    def getKeys(self):
        items = []
        for key in self._project_definition.keys():
            if self._criteriaFilter:
                if key not in self._criteriaFilter:
                    continue
            items.append(key)
        return items


    def getKeyValues(self, keys):
        values = []
        with self._lock:
            for key in keys:
                if self._criteriaFilter:
                    if key not in self._criteriaFilter:
                        continue
                if key in self._project_definition.keys():
                    value = self._project_definition[key]
                    values.append( f"{key}: [{value}]")
        return ', '.join(values)


    def start(self):
        is_ready = True
        if not self.thread_worker:
            is_ready = False
            self.thread_worker= threading.Thread(target=self._worker_loop, daemon=True)
            self.thread_worker.start()
        return is_ready

    def setCriteriaFilter(self, filter:list):
        with self._lock:
            self.logger.info(f"setting criteria filter: {filter}")
            self._criteriaFilter= filter

    def lockDefinitions(self):
        with self._lockConsolidation:
            self.logger.info("lockDefinitions")
            self._defnLocked = True

    def addToIngestQueue(self, name, pathname):
        if self._is_ingestible(pathname):
            doc = ProjDefnDocSpecifier(pathname,name)
            with self._lock:
                if pathname not in self._ingest_queue:
                    self.logger.info(f"adding {pathname} to ingest queue")
                    self._ingest_queue.append(doc)

    def removeDocument(self, name, pathname):
        self.logger.info(f"removeDocument: {name}, {pathname}")
        remove_spec = ProjDefnDocSpecifier(name,pathname)
        with self._lock:
            if remove_spec in self._ingest_queue:
                self._ingest_queue.remove(remove_spec)
            if name in self._defn_docs:
                doc = self._defn_docs.pop(name)
                if doc:
                    self._unpersist_document_definition(doc)
                self._need_refresh = True
                self._full_refresh = True

    def unlockDefinitions(self):
        with self._lockConsolidation:
            self.logger.info("unlockDefinitions")
            self._defnLocked = False

    @staticmethod
    def getInstance(config : dict = None, id: str = "default"):
        instance = None
        with _lock:
            if not id in ProjDefn._instances:
                if config:
                    required_keys = ["base_path", "model", "timeout", "chunk_size",
                                     "cost_tracer", "reporter", "helpers" ]
                    for key in required_keys:
                        if key not in config:
                            raise ValueError(f"Missing required key: {key}")
                    ProjDefn._instances[id] = ProjDefn(id, config)
            if id in ProjDefn._instances:
                instance = ProjDefn._instances[id]
        return instance

    @staticmethod
    def releaseInstance(id: str):
        with _lock:
            if id in ProjDefn._instances:
                instance = ProjDefn._instances[id]

                # Stop any running threads
                if instance.thread_worker:
                    instance.stop_worker = True

                # Close event loops
                if instance.loop_worker:
                    while not instance.worker_stopped:
                        time.sleep(1)
                    instance.loop_worker.stop()
                    instance.loop_worker.close()

                # Remove the instance
                ProjDefn._instances.pop(id)