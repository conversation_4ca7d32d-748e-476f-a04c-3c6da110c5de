"""
S3 Metrics Upload Script for Kavia Templates CLI.

This script uploads the metrics.json file generated by the templates CLI to an S3 bucket.
It automatically creates the bucket if it doesn't exist and configures it for optimal metrics storage.


Usage:
    python upload_metrics_to_s3.py --metrics-path /path/to/metrics.json --bucket my-bucket --prefix metrics/2024/01/

Example:
    python upload_metrics_to_s3.py \
        --metrics-path /test.json\
        --bucket kavia_bucket_test \
        --prefix 'test'/ \
        --region us-west-2
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

try:
    import boto3
    from botocore.exceptions import ClientError, NoCredentialsError
except ImportError:
    print("Error: boto3 is required. Install it with: pip install boto3")
    sys.exit(1)


class S3MetricsUploader:
    """
    Handles uploading metrics files to S3 with proper error handling and logging.
    """

    def __init__(self, bucket: str, region: str = "us-west-2"):
        """
        Initialize the S3 uploader.
        
        Args:
            bucket: S3 bucket name
            region: AWS region (default: us-west-2)
        """
        self.bucket = bucket
        self.region = region
        self.s3_client = None
        self.logger = self._setup_logging()



    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        return logging.getLogger(__name__)

    def _connect_to_s3(self) -> bool:
        """
        Establish connection to S3 and create bucket if it doesn't exist.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.s3_client = boto3.client('s3', region_name=self.region)
            self.s3_client.head_bucket(Bucket=self.bucket)
            self.logger.info(f"Successfully connected to S3 bucket: {self.bucket}")
            return True
        except NoCredentialsError:
            self.logger.error("AWS credentials not found. Please configure your AWS credentials.")
            return False
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                self.logger.warning(f"Bucket '{self.bucket}' not found. Attempting to create it...")
                return self._create_bucket_if_not_exists()
            elif error_code == '403':
                self.logger.error(f"Access denied to bucket '{self.bucket}'. Check your permissions.")
            else:
                self.logger.error(f"Error connecting to S3: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error connecting to S3: {e}")
            return False

    def _create_bucket_if_not_exists(self) -> bool:
        """
        Create the S3 bucket if it doesn't exist.
        
        Returns:
            bool: True if bucket created successfully or already exists, False otherwise
        """
        try:
            if self.region == 'us-east-1':
                self.s3_client.create_bucket(Bucket=self.bucket)
            else:
                self.s3_client.create_bucket(
                    Bucket=self.bucket,
                    CreateBucketConfiguration={
                        'LocationConstraint': self.region
                    }
                )
            
            self.s3_client.get_waiter('bucket_exists').wait(Bucket=self.bucket)
            
            self._configure_bucket_for_metrics()
            
            self.logger.info(f"✅ Successfully created bucket: {self.bucket}")
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'BucketAlreadyExists':
                self.logger.info(f"Bucket '{self.bucket}' already exists (created by another process)")
                return True
            elif error_code == 'BucketAlreadyOwnedByYou':
                self.logger.info(f"Bucket '{self.bucket}' is already owned by you")
                return True
            else:
                self.logger.error(f"Error creating bucket: {e}")
                return False
        except Exception as e:
            self.logger.error(f"Unexpected error creating bucket: {e}")
            return False

    def _configure_bucket_for_metrics(self) -> None:
        """
        Configure the bucket with appropriate settings for metrics storage.
        """
        try:
            self.s3_client.put_bucket_versioning(
                Bucket=self.bucket,
                VersioningConfiguration={'Status': 'Enabled'}
            )
            
            self.logger.info(f"Configured bucket '{self.bucket}' with versioning")
            
        except ClientError as e:
            self.logger.warning(f"Could not configure bucket settings (this is optional): {e}")
        except Exception as e:
            self.logger.warning(f"Unexpected error configuring bucket: {e}")





    def _validate_metrics_file(self, metrics_path: Path) -> bool:
        """
        Validate that the metrics file exists and contains valid JSON.
        
        Args:
            metrics_path: Path to the metrics file
            
        Returns:
            bool: True if file is valid, False otherwise
        """
        if not metrics_path.exists():
            self.logger.error(f"Metrics file not found: {metrics_path}")
            return False
        
        if not metrics_path.is_file():
            self.logger.error(f"Path is not a file: {metrics_path}")
            return False
        
        if metrics_path.stat().st_size == 0:
            self.logger.error("Metrics file is empty")
            return False
        
        try:
            with open(metrics_path, 'r') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                self.logger.error("Metrics file should contain a JSON array")
                return False
            
            if len(data) == 0:
                self.logger.warning("Metrics file contains empty array")
                return False
            
            # Validate each entry has required fields
            for i, entry in enumerate(data):
                if not isinstance(entry, dict):
                    self.logger.error(f"Entry {i} is not a dictionary")
                    return False
                
                if 'app_name' not in entry:
                    self.logger.error(f"Entry {i} missing 'app_name' field")
                    return False
            
            self.logger.info(f"Validated metrics file: {len(data)} entries found")
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in metrics file: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error reading metrics file: {e}")
            return False

    def _generate_s3_key(self, prefix: str, metrics_path: Path) -> str:
        """
        Generate the S3 key (path) for the metrics file.
        
        Args:
            prefix: S3 prefix (folder path)
            metrics_path: Local path to metrics file
            
        Returns:
            str: S3 key for the file
        """
        clean_prefix = prefix.strip('/')
        
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        # Include date to avoid overwrites
        if clean_prefix:
            s3_key = f"{clean_prefix}/metrics-{current_date}.json"
        else:
            s3_key = f"metrics-{current_date}.json"
        
        self.logger.info(f"Generated S3 key: {s3_key}")
        return s3_key

    def upload_metrics(self, metrics_path: Path, prefix: str = "metrics/") -> bool:
        """
        Upload the metrics file to S3.
        
        Args:
            metrics_path: Path to the local metrics file
            prefix: S3 prefix (folder path)
            
        Returns:
            bool: True if upload successful, False otherwise
        """
        self.logger.info(f"Starting upload of metrics to S3...")
        
        if not self._validate_metrics_file(metrics_path):
            return False
        
        if not self._connect_to_s3():
            return False
        
        s3_key = self._generate_s3_key(prefix, metrics_path)
        
        try:
            # Read the file once to get entry count and prepare metadata
            with open(metrics_path, 'r') as f:
                data = json.load(f)
                entries_count = len(data)
            
            # Prepare ExtraArgs with basic configuration
            extra_args = {
                'ContentType': 'application/json',
                'Metadata': {
                    'uploaded_at': datetime.now().isoformat(),
                    'source': 'kavia-templates-cli',
                    'entries_count': str(entries_count)
                }
            }
            
            # Files uploaded without encryption
            self.logger.info("📁 Files uploaded without encryption")
            
            self.s3_client.upload_file(
                str(metrics_path),
                self.bucket,
                s3_key,
                ExtraArgs=extra_args
            )
            
            s3_url = f"https://{self.bucket}.s3.{self.region}.amazonaws.com/{s3_key}"
            
            self.logger.info(f"✅ Successfully uploaded metrics to S3")
            self.logger.info(f"   Bucket: {self.bucket}")
            self.logger.info(f"   Key: {s3_key}")
            self.logger.info(f"   URL: {s3_url}")
            
            return True
            
        except ClientError as e:
            self.logger.error(f"Error uploading to S3: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during upload: {e}")
            return False

    def get_upload_stats(self, metrics_path: Path) -> Dict[str, Any]:
        """
        Get statistics about the metrics file.
        
        Args:
            metrics_path: Path to the metrics file
            
        Returns:
            Dict containing file statistics
        """
        try:
            with open(metrics_path, 'r') as f:
                data = json.load(f)
            
            stats = {
                'file_size_bytes': metrics_path.stat().st_size,
                'entries_count': len(data),
                'file_path': str(metrics_path),
                'last_modified': datetime.fromtimestamp(metrics_path.stat().st_mtime).isoformat()
            }
            
            if data:
                costs = [entry.get('total_cost', 0) for entry in data if isinstance(entry.get('total_cost'), (int, float))]
                times = [entry.get('elapsed_time', 0) for entry in data if isinstance(entry.get('elapsed_time'), (int, float))]
                
                if costs:
                    stats['total_cost'] = sum(costs)
                    stats['avg_cost'] = sum(costs) / len(costs)
                
                if times:
                    stats['total_time'] = sum(times)
                    stats['avg_time'] = sum(times) / len(times)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting file stats: {e}")
            return {}


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(
        description="Upload metrics from Kavia Templates CLI to S3",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "--metrics-path",
        type=Path,
        required=True,
        help="Path to the metrics.json file"
    )
    
    parser.add_argument(
        "--bucket",
        type=str,
        required=True,
        help="S3 bucket name"
    )
    
    parser.add_argument(
        "--prefix",
        type=str,
        default="",
        help="S3 prefix (folder path) for the uploaded file"
    )
    
    parser.add_argument(
        "--region",
        type=str,
        default="us-west-2",
        help="AWS region"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    

    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    uploader = S3MetricsUploader(bucket=args.bucket, region=args.region)
    
    stats = uploader.get_upload_stats(args.metrics_path)
    if stats:
        uploader.logger.info("📊 File Statistics:")
        for key, value in stats.items():
            uploader.logger.info(f"   {key}: {value}")
    
    success = uploader.upload_metrics(args.metrics_path, args.prefix)
    
    if success:
        uploader.logger.info("✅ Metrics upload completed successfully!")
        sys.exit(0)
    else:
        uploader.logger.error("❌ Metrics upload failed!")
        sys.exit(1)


if __name__ == "__main__":
    main() 