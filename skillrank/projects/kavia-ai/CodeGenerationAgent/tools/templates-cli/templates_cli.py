#!/usr/bin/env python3
"""
Templates CLI Tool for Kavia Code Generation.

This module provides a command-line interface for running template-based tasks through
the Kavia code generation system. It processes tasks and generates code
based on the provided configuration.

Usage:
    python templates_cli.py --config <config_path> --prompts <prompts_path> --tasks <tasks_path> --budget <budget> --verbose

Example:
    python templates_cli.py --config config.yaml --prompts ./prompts --tasks ./tasks.json --budget 2 --verbose
"""

import argparse
import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union

from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.agents.user_agent import UserAgentOrchestrator
from code_generation_core_agent.config import config


class TemplatesCLI:
    """
    Command-line interface for the Kavia Templates processing system.

    This class handles parsing command-line arguments, setting up logging,
    and orchestrating the execution of template-based code generation tasks.
    """

    def __init__(self) -> None:
        """Initialize the TemplatesCLI with argument parser setup."""
        self.parser = self._setup_argument_parser()
        self.logger: Optional[logging.Logger] = None

    def _setup_argument_parser(self) -> argparse.ArgumentParser:
        """
        Set up the command-line argument parser.

        Returns:
            argparse.ArgumentParser: Configured argument parser.
        """
        parser = argparse.ArgumentParser(
            description="Kavia Auto Maintenance CLI Tool",
            formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        )

        parser.add_argument(
            "--config-path",
            default="src/code_generation_core_agent/agents/micro",
            help="Path to micro agents configuration base directory",
            type=Path,
        )

        parser.add_argument(
            "--prompts-path",
            default="src/code_generation_core_agent/agents/micro/prompts",
            help="Path to prompts directory",
            type=Path,
        )

        parser.add_argument(
            "--tasks-path",
            required=True,
            help="Path to tasks JSON file",
            type=Path
        )

        parser.add_argument(
            "--metrics-path",
            default="metrics.json",
            help="Path to save metrics (relative to workspace directory)",
            type=Path
        )

        parser.add_argument(
            "--instructions-path",
            help="Path to a file containing additional task guidelines/instructions",
            type=Path,
        )        

        parser.add_argument(
            "--model",
            default="claude-3-5-sonnet-20241022",
            help="LLM model to use",
        )


        parser.add_argument(
            "--budget",
            type=int,
            help="Amount ($) of budget to allocate per task",
            default=2,
        )

        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Enable verbose logging",
        )

        parser.add_argument(
            "--streaming",
            action="store_true",
            help="Enable streaming mode for LLM responses",
        )

        parser.add_argument(
            "--override",
            action="store_true",
            help="Override existing task workspace directories",
        )

        return parser

    def _generate_workspace_dir(self, task_name: Optional[str] = None) -> str:
        """
        Generate workspace directory path using config and task name.
        
        Args:
            task_name: Name of the task to create workspace for
            
        Returns:
            str: Path to the workspace directory
        """
        # Sanitize task name for use in file path
        if task_name:
            sanitized_task_name = task_name.replace(" ", "_").replace("/", "_").replace("\\", "_")
        else:
            sanitized_task_name = ''
        
        workspace_root = os.path.join(
            config.get("SYSTEM", "workspace_dir"),
            "templates",
            sanitized_task_name
        )

        # Check if directory exists and create if not
        if not os.path.exists(workspace_root):
            Path(workspace_root).mkdir(parents=True, exist_ok=True)

        return workspace_root

    def _setup_logging(self, workspace_path: Path, verbose: bool) -> logging.Logger:
        """
        Set up the logging configuration.

        Args:
            workspace_path: Path to the workspace directory for log files
            verbose: Flag to enable verbose (DEBUG) logging

        Returns:
            logging.Logger: Configured logger instance
        """
        log_level = logging.DEBUG if verbose else logging.INFO
        date_str = datetime.now().strftime("%Y%m%d")
        logger = setup_logger(f"TemplatesCLI_{date_str}", workspace_path, log_level)
        logger.info("Starting Templates CLI Tool")
        return logger

    def _load_tasks(self, tasks_path: Path) -> List[Dict[str, Any]]:
        """
        Load tasks from the specified JSON file.

        Args:
            tasks_path: Path to the tasks JSON file

        Returns:
            List[Dict[str, Any]]: List of task configurations

        Raises:
            FileNotFoundError: If the tasks file does not exist
            json.JSONDecodeError: If the tasks file contains invalid JSON
        """
        try:
            with open(tasks_path, "r") as tasks_file:
                tasks_list = json.load(tasks_file)
                self.logger.info(f"Loaded {len(tasks_list)} tasks")
                return tasks_list
        except FileNotFoundError:
            self.logger.error(f"Tasks file not found: {tasks_path}")
            raise
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in tasks file: {tasks_path}")
            raise

    def _read_instructions_file(self, file_path: Path) -> Optional[str]:
        """
        Read instructions from a file.

        Args:
            file_path: Path to the file containing instructions

        Returns:
            Optional[str]: The instructions content if successful, None otherwise
        """
        if not file_path:
            return None

        try:
            self.logger.info(f"Reading instructions from {file_path}")
            with open(file_path, "r") as f:
                return f.read().strip()
        except FileNotFoundError:
            self.logger.error(f"Instructions file not found: {file_path}")
            return None
        except Exception as e:
            self.logger.error(f"Error reading instructions file: {e}")
            return None

    async def _process_task(
        self,
        task: Dict[str, Any],
        config_path: str,
        model: str,
        prompts_dir: str,
        budget: int,
        logging_level: int,
        enable_streaming: bool,
        metrics_path: Path,
        instructions: Optional[str] = None,
        override: bool = False,
    ) -> bool:
        """
        Process a single task.

        Args:
            task: The task configuration dictionary
            config_path: Path to the configuration file
            model: LLM model to use
            prompts_dir: Path to prompts directory
            budget: Budget allocation for the task
            logging_level: Logging level
            enable_streaming: Whether to enable streaming
            metrics_path: Path to save metrics
            instructions: Optional additional instructions to override task guidelines
            override: If True, existing task workspace directories will be removed

        Returns:
            bool: True if processing was successful, False otherwise
        """
        task_name = task.get("name", "Unnamed task")
        self.logger.info(f"Generating task: {task_name}...")

        # Create task-specific workspace directory
        task_workspace = self._generate_workspace_dir(task_name)
        # check if workspace exists, and overwrite it if specified. otherwise, the task execution will check if the app is ready
        if os.path.exists(task_workspace) and override:
            import shutil
            self.logger.info(f"Removing existing task workspace: {task_workspace}")
            shutil.rmtree(task_workspace)
        
        # Add command-line instructions if provided and task doesn't have specific instructions
        if instructions and not task.get("additional_system_instructions"):
            self.logger.info(
                "Using instructions from command line (task had no specific instructions)"
            )
            # Make a copy to avoid modifying the original task
            task = task.copy()
            task["additional_system_instructions"] = instructions

        # Create a new orchestrator for this specific task
        builder = UserAgentOrchestrator(
            config_path=config_path,
            model=model,
            prompts_dir=prompts_dir,
            workspace=task_workspace,
            budget=budget,
            logging_level=logging_level,
            enable_streaming=enable_streaming,
            metrics_path=metrics_path,
        )

        await builder.run(task)
        self.logger.info(f"Generated task: {task_name}!")

    async def run(self, args: Optional[argparse.Namespace] = None) -> int:
        """
        Run the Templates CLI with the provided arguments.

        Args:
            args: Parsed command-line arguments. If None, arguments will be parsed from sys.argv.

        Returns:
            int: Exit code (0 for success, non-zero for failure)
        """
        if args is None:
            args = self.parser.parse_args()

        # Create a default workspace for logging (using a generic name)
        default_workspace = self._generate_workspace_dir()
        workspace_metrics_path = default_workspace / args.metrics_path

        
        # Setup logging
        self.logger = self._setup_logging(Path(default_workspace), args.verbose)

        # Load and process tasks
        tasks_list = self._load_tasks(args.tasks_path)

        # Read instructions from file if provided
        instructions = None
        if args.instructions_path:
            instructions = self._read_instructions_file(args.instructions_path)

        success_count = 0
        total_tasks = len(tasks_list)

        # Process each task
        for task in tasks_list:
            success = await self._process_task(
                task=task,
                config_path=str(args.config_path),
                model=args.model,
                prompts_dir=str(args.prompts_path),
                budget=args.budget,
                logging_level=logging.DEBUG if args.verbose else logging.INFO,
                enable_streaming=args.streaming,
                metrics_path=workspace_metrics_path,
                instructions=instructions,
                override=args.override
            )
            if success:
                success_count += 1

        # Log final summary
        self.logger.info(
            f"Finished generating tasks: {success_count}/{total_tasks} succeeded"
        )
    

def run() -> None:
    """
    Main entry point for the TemplatesCLI tool.

    This function initializes and runs the TemplatesCLI instance and handles
    the exit code appropriately.
    """
    tool = TemplatesCLI()
    asyncio.run(tool.run())
    sys.exit(0)


if __name__ == "__main__":
    run()
