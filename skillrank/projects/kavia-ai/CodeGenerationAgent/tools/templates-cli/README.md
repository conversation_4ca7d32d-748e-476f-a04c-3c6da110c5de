# Templates CLI

This folder contains the Templates CLI tool for Kavia Code Generation.

## Files

- `templates_cli.py` - The main CLI tool for processing template-based code generation tasks
- `run_templates_cli.sh` - Shell script to batch process multiple tasks from a JSON file
- `README.md` - This documentation file

## Usage

### Direct CLI Usage

```bash
python tools/templates-cli/templates_cli.py \
    --tasks-path=test/data/templates/sample_tasks.json \
    --instructions-path=test/data/templates/sample_instructions.j2 \
    --override \
    --budget=2 \
    --model=gpt-4.1
```

### Batch Processing

```bash
./tools/templates-cli/run_templates_cli.sh
```

## Features

- Process template-based code generation tasks
- Support for batch processing multiple tasks
- Configurable budget and model settings
- Workspace management with override capability
- Metrics collection and logging 