#!/bin/bash
# This script processes entries from sample_tasks.json and runs templates_cli.py for each entry

set -e  # Exit immediately if a command exits with a non-zero status

# Set workspaceFolder to the current directory
workspaceFolder=$(pwd)
TEMPLATES_CLI="$workspaceFolder/tools/templates-cli/templates_cli.py"

# Define variables from the configuration
DEMO_SAMPLES="$workspaceFolder/test/data/templates/sample_tasks.json"
INSTRUCTIONS_PATH="$workspaceFolder/test/data/templates/sample_instructions.j2"
BUDGET=2
MODEL=gpt-4.1  #claude-3-5-sonnet-20241022

# Create temp directory for individual task files
TEMP_DIR=$(mktemp -d)
echo "Creating temporary directory for task files: $TEMP_DIR"

# Set PYTHONPATH
export PYTHONPATH="$workspaceFolder/src"

# Check if demo_samples.json exists
if [ ! -f "$DEMO_SAMPLES" ]; then
    echo "Error: Demo samples file not found at $DEMO_SAMPLES"
    exit 1
fi

# Extract entries from demo_samples.json using jq
if ! command -v jq &> /dev/null; then
    echo "Error: jq is required but not installed. Please install it with 'brew install jq'."
    exit 1
fi

# Get the number of entries in the JSON array
NUM_ENTRIES=$(jq length "$DEMO_SAMPLES")
echo "Found $NUM_ENTRIES entries in $DEMO_SAMPLES"

# Process each entry in the JSON file
for ((i=0; i<NUM_ENTRIES; i++)); do
    # Extract the entry
    ENTRY=$(jq -c ".[$i]" "$DEMO_SAMPLES")
    
    # Get the name of the task
    TASK_NAME=$(echo $ENTRY | jq -r '.name')
    echo "Processing task: $TASK_NAME (entry $((i+1)) of $NUM_ENTRIES)"
    echo "[$ENTRY]"

    # Create a temporary file for this task
    TASK_FILE="$TEMP_DIR/$TASK_NAME.json"
    echo "[$ENTRY]" > "$TASK_FILE"
    
    echo "Running templates_cli.py for $TASK_NAME..."
    python "$TEMPLATES_CLI" \
        --tasks-path="$TASK_FILE" \
        --instructions-path="$INSTRUCTIONS_PATH" \
        --override \
        --budget="$BUDGET" \
        --model="$MODEL"
    
    echo "Completed task: $TASK_NAME"
    echo "----------------------------------------"
done

# Clean up
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "All tasks completed successfully!"
