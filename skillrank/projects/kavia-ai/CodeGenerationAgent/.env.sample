REGION=
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GROQ_API_KEY=
OPENROUTER_API_KEY=
APTIZE_TOKEN=
# MODELS - litellm
# MODEL_NAME=claude-3-5-sonnet-20240620

# MODEL_NAME=gpt-4o

# MODELS - litellm-openrouter
# MODEL_NAME=openrouter/phind/phind-codellama-34b # error KeyError: 'thoughts'
# MODEL_NAME=openrouter/mistralai/mixtral-8x7b-instruct:nitro # not working Error Unknown
# MODEL_NAME=openrouter/meta-llama/codellama-34b-instruct # error KeyError: 'thoughts'
# MODEL_NAME=openrouter/meta-llama/codellama-70b-instruct # not working Error Unknown

# MODEL_NAME=openrouter/google/palm-2-codechat-bison-32k # working
MODEL_NAME=openrouter/cognitivecomputations/dolphin-mixtral-8x22b # working
# MODEL_NAME=openrouter/google/gemma-2-27b-it # working

# MODELS - litellm-groq
# MODEL_NAME=groq/llama3-70b-8192


# NEO4J
NEO4J_CONNECTION_URI=
NEO4J_USER=
NEO4J_PASSWORD=
# MONGO_URI
MONGO_CONNECTION_URI=
MONGO_DB_NAME=
STAGE = 
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
GITHUB_ACCESS_TOKEN=

###########################################################
# Name: DEBUG_USE_LOCAL_WORK_ITEM
#
# Description:
# Use local work item file for testing.If set, the value
# should be the path to the file.
# When set, the NEO4J database will not be used.
###########################################################

# DEBUG_USE_LOCAL_WORK_ITEM=extended_work_item.json

###########################################################
# Name: FEATURE_FLAG_USE_DOCKER
#
# Description:
# Use docker to run the application.
# Default: False
# Values: True, False
###########################################################

# FEATURE_FLAG_USE_DOCKER=False


###########################################################
# Name: GITHUB_ACCESS_TOKEN
#
# Description:
# Github access token that will be used by the git tools
# Default: ""
# Values: string
###########################################################
GITHUB_ACCESS_TOKEN=

#For local testing use "0.0.0.0"
HOST=

