import os
from typing import Callable
from unittest.mock import MagicMock

import pytest
import asyncio
from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType
from code_generation_core_agent.agents.tools.visible_shell_tools import VisibleShellTools

@pytest.fixture(name="docker_executor_config")
def get_container_configuration() -> Callable[[str], dict[str, str]]:
    def _get_config(platform: ApplicationType = ApplicationType.COMMON,
                    framework: FrameworkType = FrameworkType.DEFAULT,
                    ) -> dict[str, str]:
        platform_loader = PlatformConfigLoader('src/code_generation_core_agent/agents/micro')
        platform_config = platform_loader.get_platform_config(platform, framework)
        platform_config = platform_config.get("container", {
            "container_name": "kavia_default_container_image",
        })
        return platform_config
    return _get_config

def test_loading_different_docker_configurations(
    docker_executor_config: Callable[[str], dict[str, str]]
):
    # Default testing
    default_configuration = docker_executor_config()
    default_image_name = default_configuration['container_name']

    assert default_image_name is not None

@pytest.fixture(scope="module")
def docker_executor():
    executor = DockerExecutor()
    yield executor
    del executor  # This will clean up the container


@pytest.mark.asyncio
async def test_create_subprocess_shell(docker_executor):
    process = await docker_executor.create_subprocess_shell("echo 'Hello, Docker!'")
    assert process is not None
    output = process.stdout.read()
    # Assert if \rHello, Docker! is in the output
    # \r signifies the carriage return character
    assert b"Hello, Docker!" in output

@pytest.mark.asyncio
async def test_terminate_process(docker_executor):
    process = await docker_executor.create_subprocess_shell("sleep 10")
    await docker_executor.terminate_process(process)
    returncode = await process.wait()
    assert returncode != 0  # Non-zero return code indicates termination

@pytest.fixture
def visible_shell(docker_executor):
    base_path = "/tmp"
    logger = MagicMock()
    return VisibleShellTools({}, base_path, logger, executor=docker_executor)


@pytest.mark.asyncio
async def test_create_task(docker_executor):
    async def dummy_task():
        await asyncio.sleep(0.1)
        return "Done"

    task = docker_executor.create_task(dummy_task())
    result = await task
    assert result == "Done"


@pytest.mark.asyncio
async def test_wait(docker_executor):
    async def slow_task():
        await asyncio.sleep(0.2)
        return "Slow"

    async def fast_task():
        return "Fast"

    slow = docker_executor.create_task(slow_task())
    fast = docker_executor.create_task(fast_task())

    done, pending = await docker_executor.wait([slow, fast])
    assert len(done) == 1
    assert len(pending) == 1
    assert fast in done
    assert slow in pending


@pytest.mark.asyncio
async def test_gather(docker_executor):
    async def task1():
        return "Result 1"

    async def task2():
        return "Result 2"

    results = await docker_executor.gather(task1(), task2())
    assert results == ["Result 1", "Result 2"]


@pytest.mark.asyncio
async def test_run_coroutine_threadsafe(docker_executor):
    async def coro():
        await asyncio.sleep(0.1)
        return "Threadsafe result"

    loop = asyncio.get_running_loop()
    future = docker_executor.run_coroutine_threadsafe(coro(), loop)
    result = await asyncio.wrap_future(future)
    assert result == "Threadsafe result"


@pytest.mark.asyncio
async def test_wait_for(docker_executor):
    async def long_task():
        await asyncio.sleep(1)
        return "Long task done"

    with pytest.raises(asyncio.TimeoutError):
        await docker_executor.wait_for(long_task(), 0.1)

    result = await docker_executor.wait_for(long_task(), 2)
    assert result == "Long task done"


@pytest.mark.asyncio
async def test_complex_command(docker_executor):
    command = """
    if command -v figlet &>/dev/null; then
        echo "figlet is already installed"
    else
        apt-get update && 
        apt-get install -y figlet
        echo "figlet has been installed"
    fi &&
    figlet "Package installation is working!"
    """
    process = await docker_executor.create_subprocess_shell(command)
    output = process.stdout.read()
    output_str = output.decode('utf-8')

    assert ("figlet is already installed" in output_str or
            "figlet has been installed" in output_str)

@pytest.mark.asyncio
async def test_file_system_persistence(docker_executor):
    # Create a file
    create_file = await docker_executor.create_subprocess_shell("sh -c \"echo 'Test content' > /tmp/test_file.txt\"")
    output =  create_file.stdout.read()
    # Read the file content
    process = await docker_executor.create_subprocess_shell("cat /tmp/test_file.txt")
    output = process.stdout.read()
    assert "Test content" in output.decode()


@pytest.mark.asyncio
async def test_multiple_commands(docker_executor):
    # Run multiple commands in sequence
    commands = [
        "echo 'Command 1'",
        "echo 'Command 2'",
        "echo 'Command 3'"
    ]

    outputs = []
    for cmd in commands:
        process = await docker_executor.create_subprocess_shell(cmd)
        output = process.stdout.read()
        outputs.append(output.strip())

    # Check that all echo comaands were executed and their output is correct and
    # contains '\rCommand x' where x is the command number (1, 2, 3)

    for i, output in enumerate(outputs):
        assert f"\nCommand {i + 1}" in output.decode()

@pytest.mark.asyncio
async def test_interactive_bash_shell_with_stdin(docker_executor):
    process = await docker_executor.create_subprocess_shell("bash")

    process.stdin.write(b'echo "Interactive shell test"\n')
    process.stdin.write(b'exit\n')
    process.stdin.flush()

    output = process.stdout.read()
    output_str = output.decode('utf-8')

    assert "Interactive shell test" in output_str, "Expected 'Interactive shell test' in output"

    return_code, output = await process.wait()
    assert return_code == 0, "Expected zero return code for successful execution"


async def helper_capture_output(process):
    output = ""
    while True:
        chunk = await process.stdout.read(1024)
        if not chunk:
            break
        output += chunk.decode()
    return output


@pytest.mark.asyncio
async def test_failed_command_and_error_handling(docker_executor):
    # Test a command that should fail
    invalid_command = "nonexistent_command"
    process = await docker_executor.create_subprocess_shell(invalid_command)

    output = process.stdout.read()
    output_str = output.decode('utf-8')

    # Check the return code
    return_code = await process.wait()

    assert return_code != 0, "Expected non-zero return code for failed command"
    assert "command not found" in output_str.lower(), "Expected 'command not found' error message"

    invalid_args_command = "ls --invalid-option"
    process = await docker_executor.create_subprocess_shell(invalid_args_command)

    output = process.stdout.read()
    output_str = output.decode('utf-8')

    return_code = await process.wait()

    assert return_code != 0, "Expected non-zero return code for command with invalid arguments"
    assert "invalid option" in output_str.lower() or "unrecognized option" in output_str.lower(), \
        "Expected error message for invalid option"


@pytest.mark.asyncio
async def test_sudo_no_password(docker_executor):
    """
    Test if sudo works without a password in the container.
    """
    cmd = "sudo -n echo '\nSudo works without password'"
    process = await docker_executor.create_subprocess_shell(cmd)
    return_code, output = await process.wait()

    assert return_code == 0, "Sudo command failed"
    assert "\nSudo works without password" in output, "Sudo didn't work without password"


@pytest.mark.asyncio
async def test_file_permissions_and_ownership(docker_executor):
    """
    Test if a file created by the default docker user is writable by the python test outside the container,
    and check if the UID matches.
    """
    test_filename = f"/tmp/test_file_{os.getpid()}.txt"

    # Create file in container and get UID
    cmd = f"touch {test_filename} && echo $UID > {test_filename} && ls -l {test_filename}"
    process = await docker_executor.create_subprocess_shell(cmd)
    return_code, output = await process.wait()

    assert return_code == 0, f"Failed to create file: {output}"

    # Check if file exists and is writable
    assert os.path.exists(test_filename), f"File {test_filename} was not created"
    assert os.access(test_filename, os.W_OK), f"File {test_filename} is not writable by the python test"

    # Check UID
    with open(test_filename, 'r') as f:
        container_uid = f.read().strip()
    host_uid = str(os.getuid())

    assert container_uid == host_uid, f"UID mismatch. Container UID: {container_uid}, Host UID: {host_uid}"

    # Clean up
    os.remove(test_filename)

@pytest.mark.asyncio
async def test_persistent_home_directory(docker_executor):
    """
    Test if the home directory persists between commands.
    """
    # Create a file in the home directory
    cmd1 = "echo 'test content' > ~/test_file.txt"
    process1 = await docker_executor.create_subprocess_shell(cmd1)
    await process1.wait()

    # Read the file in a new command
    cmd2 = "cat ~/test_file.txt"
    process2 = await docker_executor.create_subprocess_shell(cmd2)
    output = process2.stdout.read()

    assert b"test content" in output, "Home directory content did not persist"

    # Clean up
    cmd3 = "rm ~/test_file.txt"
    process3 = await docker_executor.create_subprocess_shell(cmd3)
    await process3.wait()

@pytest.mark.asyncio
async def test_sudo_mkdir_and_touch(docker_executor):
    """
    Test if sudo can create a directory and a file with the given command.
    """
    cmd = "sudo mkdir -p /tmp/celery && sudo touch /var/log/celery/worker.log && ls -l /var/log/celery/worker.log"
    process = await docker_executor.create_subprocess_shell(cmd)
    output = process.stdout.read()
    return_code, result = await process.wait()

    # Convert bytes to string
    output_str = output.decode('utf-8')

    # Check if the command was successful
    assert return_code == 0, f"Command failed with return code {return_code}"

@pytest.mark.asyncio
async def test_container_restart(docker_executor):
    """
    Test if the container can be stopped and started again.
    """
    # Get the container
    container = docker_executor.client.containers.get(docker_executor.container_name)

    # Stop the container
    container.stop(timeout=1)

    # Start the container
    container.start()

    # Run a command to check if it's working
    cmd = "echo 'Container restarted successfully'"
    process = await docker_executor.create_subprocess_shell(cmd)
    output = process.stdout.read()

    assert b"Container restarted successfully" in output, "Container did not restart successfully"

@pytest.mark.asyncio
async def test_sudo_install_package(docker_executor):
    """
    Test if sudo can install a package with apt-get.
    """
    cmd = "sudo apt-get update && sudo apt-get install -y python3-numpy"
    process = await docker_executor.create_subprocess_shell(cmd)
    return_code, output = await process.wait()
    assert return_code == 0, "Failed to install package"



@pytest.mark.asyncio
async def test_run_background_indefinitely(docker_executor):
    """
    Test if sudo can install a package with apt-get.
    """
    cmd = "export FLASK_APP=src/expense_tracker/app.py && nohup tail -f /dev/null &"
    process = await docker_executor.create_subprocess_shell(cmd)
    return_code, output = await process.wait()
    assert return_code == 0, "Failed to install package"

@pytest.mark.asyncio
async def test_run_foreground_command_and_new_command(docker_executor):
    """
    Test if new command can be run after a foreground command was started
    """
    cmd = "export FLASK_APP=src/expense_tracker/app.py && tail -f /dev/null "
    process = await docker_executor.create_subprocess_shell(cmd)

    return_code, output = await process.wait()

    cmd = "ls"
    process = await docker_executor.create_subprocess_shell(cmd)
    return_code, output = await process.wait()

    assert return_code == 0, "Failed to install package"




@pytest.mark.asyncio
async def test_run_sample_flask_app(docker_executor):
    """
    Run the sample Flask app in the container.
    """
    APP="""from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello, World!"

if __name__ == '__main__':
    app.run(debug=True)"""

    # Create a new temporary directoy name for the Flask app


    # Done


    # Use tmp helper package to create a tmp directory
    import tempfile
    tmpdirname = tempfile.mkdtemp(dir='/tmp/')

    # Write the Flask app to a file
    with open(os.path.join(tmpdirname, 'app.py'), 'w') as f:
        f.write(APP)

    # Run the Flask app using docker executor
    cmd = f"export FLASK_APP={tmpdirname}/app.py && flask run "
    process = await docker_executor.create_subprocess_shell(cmd)
    return_code, output = await process.wait()
    # Check if "Running on" is in the output
    assert "Running on" in output, "Flask app did not run successfully"
