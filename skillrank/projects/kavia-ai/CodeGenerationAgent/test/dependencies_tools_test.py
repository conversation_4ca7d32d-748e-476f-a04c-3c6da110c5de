import pytest
from unittest.mock import MagicMock

from code_generation_core_agent.agents.tools.dependencies import DependencyTools

@pytest.fixture
def dependency_tools():
    base_path = "/tmp"
    logger = MagicMock()
    executor = MagicMock()
    return DependencyTools({}, base_path, logger, executor=executor)

@pytest.fixture
def supported_versions_mock():
    return {
        "Python": {
            "fastapi": [
                "0.115.1",
            ],
            "uvicorn": [
                "0.18.9",
                "1.0.0"
            ]
        },
        "JavaScript/Node.js": {
            "tea": "3.10",
            "soy-milk": "10.56",
            "coffee-script": "~1.7.3"
        }
    }

@pytest.fixture
def mock_supported_versions(supported_versions_mock, monkeypatch):
    return monkeypatch.setattr(
        DependencyTools, "_read_yaml_file", 
        staticmethod(lambda file_path=None: supported_versions_mock))

def test_read_python_versions(mock_supported_versions, dependency_tools):
    """Test adjust_versions with a valid dependency file"""
    
    versions = dependency_tools.get_dependency_versions('Python')['dependencies']
    expected = { "fastapi": ["0.115.1",],
                 "uvicorn": ["0.18.9", "1.0.0"]}

    assert versions == expected

def test_read_node_versions(mock_supported_versions, dependency_tools):
    """Test adjust_versions with a valid dependency file"""
    
    versions = dependency_tools.get_dependency_versions('JavaScript/Node.js')['dependencies']
    expected = {"tea": "3.10", "soy-milk": "10.56", "coffee-script": "~1.7.3"}

    assert versions == expected
