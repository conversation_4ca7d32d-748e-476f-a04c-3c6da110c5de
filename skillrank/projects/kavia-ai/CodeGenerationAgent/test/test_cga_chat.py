import unittest
from unittest.mock import Mock, patch, MagicMock
import asyncio
import json
from queue import Queue
from threading import Event
import time

from code_generation_core_agent.chat.cga_chat import CGAChatSystem, SystemChatRequest
from code_generation_core_agent.chat.cga_user_service import CGAUserService
from code_generation_core_agent.llm.chat_interface import <PERSON><PERSON><PERSON><PERSON><PERSON>, MessageStatus, ChatMessage, MessageType


class TestCGAChatSystem(unittest.TestCase):
    def setUp(self):
        # Create mock chat interface
        self.chat_interface = Mock(spec=ChatInterface)
        self.chat_interface.llm_interface = Mock()

        # Create mock callback registry
        self.callbacks = {}

        def add_callback(event_name, callback):
            self.callbacks[event_name] = callback

        self.chat_interface.add_callback = add_callback

        # Initialize the chat system
        self.chat_system = CGAChatSystem(self.chat_interface)

        # Create event loop for async tests
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        # Stop the chat system
        self.chat_system.running = False
        self.chat_system.request_queue.put(None)
        if self.chat_system.worker_thread:
            self.chat_system.worker_thread.join(timeout=1)

        # Clean up event loop
        self.loop.close()

    def test_initialization(self):
        """Test proper initialization of the chat system"""
        self.assertTrue(self.chat_system.running)
        self.assertIsNotNone(self.chat_system.worker_thread)
        self.assertTrue(self.chat_system.worker_thread.daemon)
        self.assertIn("message_added", self.callbacks)

    @patch('asyncio.Queue')
    def test_handle_new_message_user(self, mock_queue):
        """Test handling of new user messages"""
        # Create a mock Queue instance
        queue_instance = Mock()
        mock_queue.return_value = queue_instance

        # Create a test message with timestamp
        current_time = time.time()
        test_message = ChatMessage(
            id="test_id",
            content="Hello",
            msg_type=MessageType.USER,
            parent_id="parent_id",
            status=MessageStatus.PENDING,
            timestamp=current_time
        )

        self.chat_system._handle_new_message(test_message)

        request = self.chat_system.request_queue.get_nowait()

        self.assertEqual(request.message_id, "test_id")
        self.assertEqual(request.content, "Hello")
        self.assertEqual(request.parent_id, "parent_id")
        self.assertIsInstance(request.response_queue, Mock)

    def test_handle_new_message_non_user(self):
        """Test handling of non-user messages"""
        # Create a test message with LLM type and timestamp
        current_time = time.time()
        test_message = ChatMessage(
            id="test_id",
            content="Response",
            msg_type=MessageType.LLM,
            parent_id="parent_id",
            status=MessageStatus.COMPLETED,
            timestamp=current_time
        )

        # Call the handler
        self.chat_system._handle_new_message(test_message)

        # Verify queue is empty
        self.assertTrue(self.chat_system.request_queue.empty())

    def test_message_history(self):
        """Test message history collection"""
        # Mock get_message to return test messages with correct message type value handling
        messages = {
            "msg3": Mock(msg_type=Mock(value="USER"), content="Hello", parent_id="msg2"),
            "msg2": Mock(msg_type=Mock(value="LLM"), content="Hi", parent_id="msg1"),
            "msg1": Mock(msg_type=Mock(value="USER"), content="Start", parent_id=None)
        }

        self.chat_interface.get_message = lambda id: messages.get(id)

        history = self.chat_system._message_history("msg3")
        expected = "USER: Start\n-----\nLLM: Hi\n-----\nUSER: Hello"
        self.assertEqual(history, expected)


if __name__ == '__main__':
    unittest.main()