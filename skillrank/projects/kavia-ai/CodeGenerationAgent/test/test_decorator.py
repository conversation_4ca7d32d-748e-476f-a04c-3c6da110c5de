"""
Unit tests for various decorators and meta functions.

The protect_from_override decorator is imported from:
code_generation_core_agent.agents.tools.schemas.decorators
"""

import unittest
from code_generation_core_agent.agents.tools.schemas.decorators import protect_from_override

class TestProtectFromOverride(unittest.TestCase):
    """
    A test suite for the protect_from_override decorator.
    """

    def test_base_class_creation(self):
        """
        Test the creation of a base class with a protected method.

        Ensures that:
        1. The base class can be instantiated without errors.
        2. Both protected and unprotected methods function correctly.
        """
        class BaseClass(metaclass=protect_from_override("protected_method")):
            def protected_method(self):
                return "protected"

            def unprotected_method(self):
                return "unprotected"

        base = BaseClass()
        self.assertEqual(base.protected_method(), "protected")
        self.assertEqual(base.unprotected_method(), "unprotected")

    def test_allowed_override(self):
        """
        Test that unprotected methods can be overridden in child classes.

        Ensures that:
        1. Protected methods remain unchanged in the child class.
        2. Unprotected methods can be successfully overridden.
        """
        class BaseClass(metaclass=protect_from_override("protected_method")):
            def protected_method(self):
                return "protected"

            def unprotected_method(self):
                return "unprotected"

        class ChildClass(BaseClass):
            def unprotected_method(self):
                return "overridden"

        child = ChildClass()
        self.assertEqual(child.protected_method(), "protected")
        self.assertEqual(child.unprotected_method(), "overridden")

    def test_prevented_override(self):
        """
        Test that overriding a protected method raises an AttributeError.

        Ensures that:
        1. Attempting to override a protected method in a child class
           raises an AttributeError.
        """
        class BaseClass(metaclass=protect_from_override("protected_method")):
            def protected_method(self):
                return "protected"

        with self.assertRaises(AttributeError):
            class ChildClass(BaseClass):
                def protected_method(self):
                    return "attempt to override"

    def test_multiple_protected_methods(self):
        """
        Test protection of multiple methods simultaneously.

        Ensures that:
        1. Multiple methods can be protected at once.
        2. Unprotected methods can still be overridden.
        3. Attempting to override any protected method raises an AttributeError.
        """
        class BaseClass(metaclass=protect_from_override("method1", "method2")):
            def method1(self):
                return "method1"

            def method2(self):
                return "method2"

            def method3(self):
                return "method3"

        class ChildClass(BaseClass):
            def method3(self):
                return "overridden"

        child = ChildClass()
        self.assertEqual(child.method1(), "method1")
        self.assertEqual(child.method2(), "method2")
        self.assertEqual(child.method3(), "overridden")

        with self.assertRaises(AttributeError):
            class FailedChild(BaseClass):
                def method1(self):
                    return "attempt to override"

        with self.assertRaises(AttributeError):
            class FailedChild(BaseClass):
                def method2(self):
                    return "attempt to override"


    def test_nested_inheritance(self):
        """
        Test protection through multiple levels of inheritance.

        Ensures that:
        1. Protection is maintained through multiple levels of inheritance.
        2. Attempting to override a protected method in any level of
           subclass raises an AttributeError.
        """
        class GrandParent(metaclass=protect_from_override("protected_method")):
            def protected_method(self):
                return "protected"

        class Parent(GrandParent):
            pass

        with self.assertRaises(AttributeError):
            class Child(Parent):
                def protected_method(self):
                    return "attempt to override"

    def test_no_protected_methods(self):
        """
        Test behavior when no methods are protected.

        Ensures that:
        1. The override protection works correctly when no methods are specified for protection.
        2. All methods can be overridden when no protection is specified.
        """
        class BaseClass(metaclass=protect_from_override()):
            def method(self):
                return "method"

        class ChildClass(BaseClass):
            def method(self):
                return "overridden"

        child = ChildClass()
        self.assertEqual(child.method(), "overridden")

    def test_non_method_attributes(self):
        """
        Test protection of non-method attributes.

        Ensures that:
        1. We can protect non-method attributes (e.g., class variables).
        2. Attempting to override a protected non-method attribute raises an AttributeError.
        """
        class BaseClass(metaclass=protect_from_override("attr")):
            attr = "base_value"

        with self.assertRaises(AttributeError):
            class ChildClass(BaseClass):
                attr = "child_value"


if __name__ == '__main__':
    unittest.main()
