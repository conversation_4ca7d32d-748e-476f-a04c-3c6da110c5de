import asyncio
import unittest
from unittest.mock import MagicMock, patch, AsyncMock
from code_generation_core_agent.agents.micro.agent_system import GenericMicroAgent
from code_generation_core_agent.llm.llm_interface import MaxRetriesError
from code_generation_core_agent.config import config
from code_generation_core_agent.agents.utilities import ModelNameSelector


class TestGenericMicroAgent(unittest.TestCase):

    def setUp(self):
        # Add this line to create a new event loop for each test
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        self.llm = MagicMock()
        self.llm.is_cache_allowed.return_value = False  
        self.base_path = "test/base/path"
        self.logger = MagicMock()
        self.model_name = "bedrock/converse/us.anthropic.claude-3-5-sonnet-20241022-v2:0"
        self.agent_tools = MagicMock()
        self.prompt_template = MagicMock()
        self.tools_to_use = ["tool1", "tool2"]
        self.setup_steps = ["step1", "step2"]
        self.agent_factory = MagicMock()
        model_name_selector = ModelNameSelector(self.model_name)
        self.agent = GenericMicroAgent(self.llm, self.base_path, self.logger, model_name_selector, self.agent_tools, self.prompt_template, self.tools_to_use, self.setup_steps, self.agent_factory)

    def tearDown(self):
        # Clean up the event loop after each test
        self.loop.close()

    @patch("code_generation_core_agent.agents.micro.agent_system.config.getint", return_value=5)
    @patch("code_generation_core_agent.agents.micro.agent_system.config.get", return_value="backup-model")
    @patch("code_generation_core_agent.agents.micro.agent_system.GenericMicroAgent._list_important_files", return_value="important_files")

    def test_process_request_normal_execution(self, mock_list_important_files, mock_config_get, mock_config_getint):
        self.agent_tools.get_function_schemas.return_value = "function_schemas"
        self.agent_tools.function_executor = "function_executor"
        self.agent_tools.function_validator = "function_validator"
        self.prompt_template.render.side_effect = ["system_prompt", "user_prompt"]
        self.llm.llm_interaction_wrapper = AsyncMock(return_value=MagicMock(choices=[MagicMock(message=MagicMock(content="response content"))]))

        result = self.loop.run_until_complete(
            self.agent.process_request("action", "subtask", "work_item", "request_details", "tool_call_log", {"capability1": "value1"})
        )

        self.assertEqual(result, "response content")
        self.assertFalse(self.agent.is_processing)

    @patch("code_generation_core_agent.agents.micro.agent_system.config.getint", return_value=5)
    @patch("code_generation_core_agent.agents.micro.agent_system.config.get", return_value="backup-model")
    @patch("code_generation_core_agent.agents.micro.agent_system.GenericMicroAgent._list_important_files", return_value="important_files")
    def test_process_request_max_retries_error(self, mock_list_important_files, mock_config_get, mock_config_getint):
        self.agent_tools.get_function_schemas.return_value = "function_schemas"
        self.agent_tools.function_executor = "function_executor"
        self.agent_tools.function_validator = "function_validator"
        self.prompt_template.render.side_effect = ["system_prompt", "user_prompt"]
        self.llm.llm_interaction_wrapper = AsyncMock(side_effect=MaxRetriesError)

        with self.assertRaises(MaxRetriesError):
            asyncio.run(self.agent.process_request("action", "subtask", "work_item", "request_details", "tool_call_log", {"capability1": "value1"}))

    @patch("code_generation_core_agent.agents.micro.agent_system.config.getint", return_value=5)
    @patch("code_generation_core_agent.agents.micro.agent_system.config.get", return_value="test-model")
    @patch("code_generation_core_agent.agents.micro.agent_system.GenericMicroAgent._list_important_files", return_value="important_files")
    def test_process_request_max_retries_error_same_backup_model(self, mock_list_important_files, mock_config_get, mock_config_getint):
        self.agent_tools.get_function_schemas.return_value = "function_schemas"
        self.agent_tools.function_executor = "function_executor"
        self.agent_tools.function_validator = "function_validator"
        self.prompt_template.render.side_effect = ["system_prompt", "user_prompt"]
        self.llm.llm_interaction_wrapper = AsyncMock(side_effect=MaxRetriesError)
        
        asyncio.run(self.agent.process_request("action", "subtask", "work_item", "request_details", "tool_call_log", {"capability1": "value1"}))

    @patch("code_generation_core_agent.agents.micro.agent_system.config.getint", return_value=5)
    @patch("code_generation_core_agent.agents.micro.agent_system.config.get", return_value="test-model")
    @patch("code_generation_core_agent.agents.micro.agent_system.GenericMicroAgent._list_important_files", return_value="important_files")
    def test_process_request_malformed_completion(self,  mock_list_important_files, mock_config_get, mock_config_getint):
        self.agent_tools.get_function_schemas.return_value = "function_schemas"
        self.agent_tools.function_executor = "function_executor"
        self.agent_tools.function_validator = "function_validator"
        self.prompt_template.render.side_effect = ["system_prompt", "user_prompt"]
        # Mocking a malformed completion object
        self.llm.llm_interaction_wrapper = AsyncMock(return_value=MagicMock(choices=[]))

        result = asyncio.run(self.agent.process_request("action", "subtask", "work_item", "request_details", "tool_call_log", {"capability1": "value1"}))
        self.assertEqual(result, "")
        

if __name__ == '__main__':
    unittest.main()
