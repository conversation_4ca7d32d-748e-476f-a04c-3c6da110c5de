import pytest
from unittest.mock import MagicMock

from code_generation_core_agent.agents.preview.port_utils import (
    get_port_number_from_url_with_fallback,
    is_port_available,
    find_available_port,
    log_info_of_pid_running_on_port,
    stop_process_using_port,
    _get_pid_from_port,
)

class TestPortUtils:
    
    TIMEOUT = 30 # Default timeout for commands

    def test_get_port_number_from_url_with_fallback_valid_url(self):
        """Test extracting port from valid URL"""
        url = "http://localhost:3000/path"
        result = get_port_number_from_url_with_fallback(url)
        assert result == "3000"
    
    def test_get_port_number_from_url_with_fallback_missing_port(self):
        """Test fallback when URL has no port"""
        url = "http://localhost/path"
        result = get_port_number_from_url_with_fallback(url, "4000")
        assert result == "4000"
    
    def test_get_port_number_from_url_with_fallback_invalid_port(self):
        """Test fallback when port is invalid"""
        url = "http://localhost:abcd/path"
        result = get_port_number_from_url_with_fallback(url, "4000")
        assert result == "4000"
    
    def test_is_port_available_port_free(self):
        """Test checking if port is available when it is free"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.return_value = ("", 0)  # No output means no process found
        
        result = is_port_available(3000, mock_executor, mock_logger)
        assert result is True
    
    def test_is_port_available_port_in_use(self):
        """Test checking if port is available when it is in use"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.return_value = ("1234", 0)  # Output with PID, return code 0
        
        result = is_port_available(3000, mock_executor, mock_logger)
        assert result is False
    
    def test_is_port_available_outside_range(self):
        """Test checking port outside allowed range"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        
        result = is_port_available(1000, mock_executor, mock_logger, port_range_start=3000, port_range_end=4000)
        assert result is False
    
    def test_find_available_port(self, monkeypatch):
        """Test finding available port"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        
        # Mock is_port_available to return True for port 3000
        def mock_is_port_available(port, *args, **kwargs):
            return port == 3000
        
        monkeypatch.setattr("code_generation_core_agent.agents.preview.port_utils.is_port_available", 
                        mock_is_port_available)
        result = find_available_port(3000, mock_executor, mock_logger)
        assert result == "3000"
    
    def test_log_info_of_pid_running_on_port_with_process(self):
        """Test logging info when a process is running on port"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.side_effect = [
            ("1234\n", 0),  # PID
            ("PID  USER  COMMAND  START  %CPU  %MEM  CMD\n1234 user node 12:00 5.0 2.0 node server.js", 0)  # Process info
        ]
        
        log_info_of_pid_running_on_port(3000, mock_executor, mock_logger)
        
        # Verify logs were called
        mock_logger.info.assert_any_call("PID: 1234")
        mock_logger.info.assert_any_call(
            "Process info:\nPID  USER  COMMAND  START  %CPU  %MEM  CMD\n1234 user node 12:00 5.0 2.0 node server.js"
        )
    
    def test_log_info_of_pid_running_on_port_no_process(self):
        """Test logging info when no process is running on port"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.return_value = ("", 0)  # No PID found
        
        log_info_of_pid_running_on_port(3000, mock_executor, mock_logger)
        
        mock_logger.info.assert_called_with("No process is running on port 3000")
    
    def test_stop_process_using_port_no_process(self):
        """Test stopping process when no process is running on port"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.return_value = ("", 0)  # No PID found
        
        result = stop_process_using_port(3000, mock_executor, mock_logger)
        
        assert result is True
        mock_logger.info.assert_called_with("No process found running on port 3000")
    
    def test_stop_process_using_port_graceful_success(self):
        """Test stopping process gracefully"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.side_effect = [
            ("1234\n", 0),
            ("", 0),
            ("", 0)
        ]
        
        result = stop_process_using_port(3000, mock_executor, mock_logger)
        
        assert result is True
        mock_executor.run_one_shot_command.assert_any_call("kill -15 1234 2>/dev/null || true", timeout=self.TIMEOUT)
        mock_logger.info.assert_any_call("Process on port 3000 stopped gracefully")

    def test_stop_process_using_port_requires_force(self):
        """Test stopping process that requires force (SIGKILL)"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        # Mock sequence: _get_pid_from_port, kill -15, _get_pid_from_port, kill -9, _get_pid_from_port
        mock_executor.run_one_shot_command.side_effect = [
            ("1234\n", 0),  # First _get_pid_from_port: ss finds PID
            ("", 0),        # kill -15 command
            ("1234\n", 0),  # Second _get_pid_from_port: ss still finds PID (process still running)
            ("", 0),        # kill -9 command
            ("", 0)         # Final _get_pid_from_port: ss finds no PID (process stopped)
        ]
        
        result = stop_process_using_port(3000, mock_executor, mock_logger)
        
        assert result is True
        mock_executor.run_one_shot_command.assert_any_call("kill -15 1234 2>/dev/null || true", timeout=self.TIMEOUT)
        mock_executor.run_one_shot_command.assert_any_call("kill -9 1234 2>/dev/null || true", timeout=self.TIMEOUT)
        mock_logger.warning.assert_called_with("Process on port 3000 didn't respond to graceful termination, using force")
        mock_logger.info.assert_any_call("Process on port 3000 terminated successfully")

    def test_stop_process_using_port_failure(self):
        """Test failure when trying to stop process"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        # Mock sequence: _get_pid_from_port, kill -15, _get_pid_from_port, kill -9 fails
        mock_executor.run_one_shot_command.side_effect = [
            ("1234\n", 0),  # First _get_pid_from_port: ss finds PID
            ("", 0),        # kill -15 command
            ("1234\n", 0),  # Second _get_pid_from_port: ss still finds PID (process still running)
            ("Error", 1)    # kill -9 command fails
        ]
        
        result = stop_process_using_port(3000, mock_executor, mock_logger)
        
        assert result is False
        mock_executor.run_one_shot_command.assert_any_call("kill -9 1234 2>/dev/null || true", timeout=self.TIMEOUT)
        mock_logger.error.assert_called_with("Failed to forcefully terminate process on port 3000")

    def test_stop_process_using_port_ss_not_available(self):
        """Test stopping process when ss is not available (fallback to lsof)"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        # Mock sequence: ss fails, lsof finds PID, kill -15, lsof finds no PID
        mock_executor.run_one_shot_command.side_effect = [
            ("", 1),        # First _get_pid_from_port: ss command fails
            ("1234", 0),    # First _get_pid_from_port: lsof finds PID
            ("", 0),        # kill -15 command
            ("", 1),        # Second _get_pid_from_port: ss command fails  
            ("", 0)         # Second _get_pid_from_port: lsof finds no PID (process stopped)
        ]
        
        result = stop_process_using_port(3000, mock_executor, mock_logger)
        
        assert result is True
        mock_executor.run_one_shot_command.assert_any_call("kill -15 1234 2>/dev/null || true", timeout=self.TIMEOUT)
        mock_logger.info.assert_any_call("Process on port 3000 stopped gracefully")

    def test_get_pid_from_port_ss_success(self):
        """Test _get_pid_from_port when ss successfully finds PID"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.return_value = ("1234\n", 0)
        
        result = _get_pid_from_port(3000, mock_executor, mock_logger)
        
        assert result == "1234"
        # Should only call ss, not lsof or netstat
        mock_executor.run_one_shot_command.assert_called_once_with(f"ss -tlnp 2>/dev/null | grep ':3000 ' | sed 's/.*pid=\\([0-9]*\\).*/\\1/' | head -1", timeout=self.TIMEOUT)
    
    def test_get_pid_from_port_ss_no_process(self):
        """Test _get_pid_from_port when ss successfully runs but finds no process"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.return_value = ("", 0)
        
        result = _get_pid_from_port(3000, mock_executor, mock_logger)
        
        assert result == ""
        # Should only call ss, not lsof or netstat
        mock_executor.run_one_shot_command.assert_called_once_with(f"ss -tlnp 2>/dev/null | grep ':3000 ' | sed 's/.*pid=\\([0-9]*\\).*/\\1/' | head -1", timeout=self.TIMEOUT)
    
    def test_get_pid_from_port_fallback_to_lsof(self):
        """Test _get_pid_from_port falls back to lsof when ss fails"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.side_effect = [
            ("", 1),        # ss command fails
            ("1234", 0)     # lsof finds PID
        ]
        
        result = _get_pid_from_port(3000, mock_executor, mock_logger)
        
        assert result == "1234"
        assert mock_executor.run_one_shot_command.call_count == 2
        mock_executor.run_one_shot_command.assert_any_call(f"ss -tlnp 2>/dev/null | grep ':3000 ' | sed 's/.*pid=\\([0-9]*\\).*/\\1/' | head -1", timeout=self.TIMEOUT)
        mock_executor.run_one_shot_command.assert_any_call("lsof -ti :3000 2>/dev/null || echo ''", timeout=self.TIMEOUT)

    def test_get_pid_from_port_fallback_to_netstat(self):
        """Test _get_pid_from_port falls back to netstat when ss and lsof fail"""
        mock_executor = MagicMock()
        mock_logger = MagicMock()
        mock_executor.run_one_shot_command.side_effect = [
            ("", 1),        # ss command fails
            ("", 1),        # lsof command fails
            ("1234", 0)     # netstat finds PID
        ]
        
        result = _get_pid_from_port(3000, mock_executor, mock_logger)
        
        assert result == "1234"
        assert mock_executor.run_one_shot_command.call_count == 3