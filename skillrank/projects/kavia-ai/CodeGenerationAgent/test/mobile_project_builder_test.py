import json
from pathlib import Path
from unittest.mock import MagicMock, patch
import yaml

import pytest
from code_generation_core_agent.agents.tools.mobile import mobile_project_builder

class MockAndroidEmulatorTool:
    def __init__(self):
        self.build_application = MagicMock()
        self.upload_application = MagicMock()
        self.update_application = MagicMock()

class MockExecutor:
    def __init__(self):
        self.run_one_shot_command = MagicMock()

class MockLogger:
    def __init__(self):
        self.info = MagicMock()
        self.error = MagicMock()
        self.debug = MagicMock()
        self.warning = MagicMock()


@pytest.fixture
def setup_mocks():
    """Fixture to set up common mocks"""
    android_emulator_tools = MockAndroidEmulatorTool()
    executor = MockExecutor()
    logger = MockLogger()
    
    return {
        "android_emulator_tools": android_emulator_tools,
        "executor": executor,
        "logger": logger
    }


@pytest.fixture
def mock_valid_manifest_yaml():
    """Fixture for a valid project manifest YAML file"""
    valid_manifest = {
        'overview': {
            'project_name': 'component_pc',
            'description': 'the pc component app',
        },
        'containers': [
            {
                'container_name': 'component_pc_mobile',
                'container_type': 'mobile',
                'container_root': 'component_pc_mobile_workspace/component_pc_mobile',
                'framework': 'Android'
            }
        ]
    }
    
    with patch('yaml.safe_load', return_value=valid_manifest):
        yield


@pytest.fixture
def mock_valid_init_file():
    """Fixture for a valid .init-build-cmd file"""
    valid_config = {
        "working_directory": "/test/project"
    }
    executor = MockExecutor()
    executor.run_one_shot_command.return_value = (json.dumps(valid_config), 0)
    return executor


class TestMobileProjectBuilder:

    def test_mobile_project_builder_no_init_file(self, setup_mocks, mock_valid_manifest_yaml):
        """Test when the project initialization file does not exist but manifest exists"""
        mocks = setup_mocks
        
        # Mock file existence checks 
        # init file (.init-build-cmd) doesn't exist - should return False
        # manifest file (.project_manifest.yaml) does exist - should return True
        # APK file exists - should return True
        def exists_side_effect(self):
            path_str = str(self)
            if '.init-build-cmd' in path_str:
                return False  # init file doesn't exist
            elif '.project_manifest.yaml' in path_str:
                return True   # manifest exists
            elif 'app-debug.apk' in path_str:
                return True   # APK exists (so no build needed)
            else:
                return False
        
        # Mock file open for reading the YAML file
        mock_open = MagicMock()
        
        with patch.object(Path, 'exists', exists_side_effect):
            with patch('builtins.open', mock_open):
                with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value=None):
                    with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._upload_apk_to_emulator_service') as mock_upload:
                        mock_upload.return_value = "https://appetize.io/app/manifest123"
                        
                        result = mobile_project_builder.mobile_project_builder(
                            app_type="android",
                            android_emulator_tools=mocks["android_emulator_tools"],
                            project_root_path="/test/project",
                            executor=mocks["executor"],
                            logger=mocks["logger"]
                        )
                        
                        # Verify it fell back to manifest (warning should be logged)
                        mocks["logger"].warning.assert_called_once()
                        assert "Init file not found" in mocks["logger"].warning.call_args[0][0]
                        
                        # Verify success
                        assert result == "https://appetize.io/app/manifest123"
                        
    def test_mobile_project_builder_no_init_no_manifest(self, setup_mocks):
        """Test when both the project initialization file and manifest don't exist"""
        mocks = setup_mocks
        
        # Use patch to mock Path.exists to always return False
        with patch.object(Path, 'exists', return_value=False):
            result = mobile_project_builder.mobile_project_builder(
                app_type="android",
                android_emulator_tools=mocks["android_emulator_tools"],
                project_root_path="/test/project",
                executor=mocks["executor"],
                logger=mocks["logger"]
            )

            assert "Project manifest file not found" in result
            mocks["logger"].error.assert_called_once()
            assert "Project manifest file not found" in mocks["logger"].error.call_args[0][0]
    
    def test_mobile_project_builder_kotlin_app_type(self, setup_mocks, mock_valid_init_file):
        """Test specific flow for Kotlin app type"""
        mocks = setup_mocks
        mocks["executor"] = mock_valid_init_file
        mocks["android_emulator_tools"].build_application.return_value = True
        
        # Mock Path.exists calls with appropriate return values
        path_exists_mock = MagicMock()
        # First call (for .init-build-cmd) returns True
        # Second call (for app-debug.apk) returns False to trigger build
        # Third and fourth calls (checking APK after build) return True
        path_exists_mock.side_effect = [True, False, True, True]
        
        with patch.object(Path, 'exists', side_effect=path_exists_mock):
            with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value=None):
                with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._upload_apk_to_emulator_service') as mock_upload:
                    mock_upload.return_value = "https://appetize.io/app/kotlin123"
                    
                    # Call the function with app_type="kotlin"
                    result = mobile_project_builder.mobile_project_builder(
                        app_type="kotlin",
                        android_emulator_tools=mocks["android_emulator_tools"],
                        project_root_path="/test/project",
                        executor=mocks["executor"],
                        logger=mocks["logger"]
                    )
                    
                    # Verify that the build command was called with app_type="kotlin"
                    mocks["android_emulator_tools"].build_application.assert_called_once_with(
                        project_path=Path("/test/project"),
                        app_type="kotlin",
                        output_path=Path("/test/project"),
                        first_run=False
                    )
                    
                    # Verify that the result is the expected URL
                    assert result == "https://appetize.io/app/kotlin123"
    
    def test_mobile_project_builder_invalid_init_file_with_fallback(self, setup_mocks, mock_valid_manifest_yaml):
        """Test when the initialization file contains invalid JSON but falls back to manifest"""
        mocks = setup_mocks
        mocks["executor"].run_one_shot_command.return_value = ("invalid json", 0)
        
        # Mock files exist
        exists_mock = MagicMock()
        exists_mock.return_value = True
        
        # Mock file open for reading the YAML file
        mock_open = MagicMock()
        
        with patch.object(Path, 'exists', exists_mock):
            with patch('builtins.open', mock_open):
                with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value=None):
                    with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._upload_apk_to_emulator_service') as mock_upload:
                        mock_upload.return_value = "https://appetize.io/app/fallback123"
                        
                        result = mobile_project_builder.mobile_project_builder(
                            app_type="android",
                            android_emulator_tools=mocks["android_emulator_tools"],
                            project_root_path="/test/project",
                            executor=mocks["executor"],
                            logger=mocks["logger"]
                        )
                        
                        # Verify logger was called for the invalid JSON
                        mocks["logger"].error.assert_any_call("Invalid build configuration format: Expecting value: line 1 column 1 (char 0)")
                        
                        # Verify it fell back to manifest successfully
                        assert result == "https://appetize.io/app/fallback123"
    
    def test_mobile_project_builder_command_failure_with_fallback(self, setup_mocks, mock_valid_manifest_yaml):
        """Test when the command to read the initialization file fails but falls back to manifest"""
        mocks = setup_mocks
        mocks["executor"].run_one_shot_command.return_value = ("Error reading file", 1)
        
        # Mock files exist
        exists_mock = MagicMock()
        exists_mock.return_value = True
        
        # Mock file open for reading the YAML file
        mock_open = MagicMock()
        
        with patch.object(Path, 'exists', exists_mock):
            with patch('builtins.open', mock_open):
                with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value=None):
                    with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._upload_apk_to_emulator_service') as mock_upload:
                        mock_upload.return_value = "https://appetize.io/app/fallback123"
                        
                        result = mobile_project_builder.mobile_project_builder(
                            app_type="android",
                            android_emulator_tools=mocks["android_emulator_tools"],
                            project_root_path="/test/project",
                            executor=mocks["executor"],
                            logger=mocks["logger"]
                        )
                        
                        # Verify logger was called for the command failure
                        mocks["logger"].error.assert_any_call("Error reading build configuration: Error reading file")
                        
                        # Verify it fell back to manifest successfully
                        assert result == "https://appetize.io/app/fallback123"
    
    def test_mobile_project_builder_apk_exists_no_update(self, setup_mocks, mock_valid_init_file):
        """Test when the APK already exists and no update is requested"""
        mocks = setup_mocks
        mocks["executor"] = mock_valid_init_file
        
        # Mock with a MagicMock that can be programmed with multiple return values
        path_exists_mock = MagicMock()
        # First call (for .init-build-cmd) returns True
        # Second call (for app-release.apk) returns True
        path_exists_mock.side_effect = [True, True]
        
        with patch.object(Path, 'exists', side_effect=path_exists_mock):
            with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value=None):
                with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._upload_apk_to_emulator_service') as mock_upload:
                    mock_upload.return_value = "https://appetize.io/app/test123"
                    
                    result = mobile_project_builder.mobile_project_builder(
                        app_type="android",
                        android_emulator_tools=mocks["android_emulator_tools"],
                        project_root_path="/test/project",
                        executor=mocks["executor"],
                        logger=mocks["logger"]
                    )
                    
                    assert result == "https://appetize.io/app/test123"
                    mock_upload.assert_called_once()
                    assert not mocks["android_emulator_tools"].build_application.called

    def test_mobile_project_builder_build_and_upload(self, setup_mocks, mock_valid_init_file):
        """Test successful build and upload of a new APK"""
        mocks = setup_mocks
        mocks["executor"] = mock_valid_init_file
        mocks["android_emulator_tools"].build_application.return_value = True
        
        path_exists_mock = MagicMock()
        path_exists_mock.side_effect = [True, False, True, True]
        
        with patch.object(Path, 'exists', side_effect=path_exists_mock):
            with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value=None):
                with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._upload_apk_to_emulator_service') as mock_upload:
                    mock_upload.return_value = "https://appetize.io/app/upload123"
                    
                    result = mobile_project_builder.mobile_project_builder(
                        app_type="android",
                        android_emulator_tools=mocks["android_emulator_tools"],
                        project_root_path="/test/project",
                        executor=mocks["executor"],
                        logger=mocks["logger"]
                    )
                    
                    # Verify the build was called
                    mocks["android_emulator_tools"].build_application.assert_called_once()
                    
                    # Verify upload was called
                    mock_upload.assert_called_once()
                    
                    # Verify result is the URL returned from upload
                    assert result == "https://appetize.io/app/upload123"

    def test_mobile_project_builder_build_failure(self, setup_mocks, mock_valid_init_file):
        """Test when build application fails"""
        mocks = setup_mocks
        mocks["executor"] = mock_valid_init_file
        mocks["android_emulator_tools"].build_application.return_value = False
        
        path_exists_mock = MagicMock()
        path_exists_mock.side_effect = [True, False, False, False]
        
        with patch.object(Path, 'exists', side_effect=path_exists_mock):
            with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value=None):
                result = mobile_project_builder.mobile_project_builder(
                    app_type="android",
                    android_emulator_tools=mocks["android_emulator_tools"],
                    project_root_path="/test/project",
                    executor=mocks["executor"],
                    logger=mocks["logger"]
                )

                assert "Failed to build application" in result
                mocks["android_emulator_tools"].build_application.assert_called_once()
                mocks["logger"].error.assert_called_once_with("Failed to build application - build_application returned False")

    def test_mobile_project_builder_build_exception(self, setup_mocks, mock_valid_init_file):
        """Test when build application raises an exception"""
        mocks = setup_mocks
        mocks["executor"] = mock_valid_init_file
        mocks["android_emulator_tools"].build_application.side_effect = Exception("Build error")
        
        path_exists_mock = MagicMock()
        path_exists_mock.side_effect = [True, False, False, False]
        
        with patch.object(Path, 'exists', side_effect=path_exists_mock):
            with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value=None):
                result = mobile_project_builder.mobile_project_builder(
                    app_type="android",
                    android_emulator_tools=mocks["android_emulator_tools"],
                    project_root_path="/test/project",
                    executor=mocks["executor"],
                    logger=mocks["logger"]
                )
                
                assert "Error building application" in result
                assert "Build error" in result
                mocks["android_emulator_tools"].build_application.assert_called_once()
                mocks["logger"].error.assert_called_once()

    def test_mobile_project_builder_with_app_update(self, setup_mocks, mock_valid_init_file):
        """Test when app_update is True"""
        mocks = setup_mocks
        mocks["executor"] = mock_valid_init_file
        mocks["android_emulator_tools"].build_application.return_value = True
        
        path_exists_mock = MagicMock()
        path_exists_mock.side_effect = [True, False, True, True]
        
        # Mock _check_for_public_key to return a key
        with patch.object(Path, 'exists', side_effect=path_exists_mock):
            with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._check_for_public_key', return_value="abc123"):
                with patch('code_generation_core_agent.agents.tools.mobile.mobile_project_builder._update_apk_in_emulator_service') as mock_update:
                    mock_update.return_value = "https://appetize.io/app/updated123"
                    
                    result = mobile_project_builder.mobile_project_builder(
                        app_type="android",
                        android_emulator_tools=mocks["android_emulator_tools"],
                        project_root_path="/test/project",
                        executor=mocks["executor"],
                        logger=mocks["logger"]
                    )
                    
                    # Verify the build was called
                    mocks["android_emulator_tools"].build_application.assert_called_once()
                    
                    # Verify update was called with the correct key
                    mock_update.assert_called_once_with(
                        Path("/test/project/app-debug.apk"),
                        "abc123",
                        mocks["android_emulator_tools"],
                        mocks["logger"]
                    )
                    
                    # Verify result is the URL returned from update
                    assert result == "https://appetize.io/app/updated123"


class TestUpdateAPKInEmulatorService:
    
    def test_update_apk_success(self, setup_mocks):
        """Test successful update of APK"""
        mocks = setup_mocks
        
        # Mock update application
        mocks["android_emulator_tools"].update_application.return_value = "https://appetize.io/app/updated123"
        
        # Test with mocked public_key parameter
        result = mobile_project_builder._update_apk_in_emulator_service(
            apk_path=Path("/test/project/app-release.apk"),
            public_key="abc123",
            android_emulator_tools=mocks["android_emulator_tools"],
            logger=mocks["logger"]
        )
        
        # Assertions
        assert result == "https://appetize.io/app/updated123"
        mocks["android_emulator_tools"].update_application.assert_called_once_with(
            Path("/test/project/app-release.apk"), "abc123"
        )
    
    def test_update_apk_file_read_error(self, setup_mocks):
        """Test when reading emulator initialization file fails"""
        mocks = setup_mocks
        
        # Test with mocked public_key parameter
        result = mobile_project_builder._update_apk_in_emulator_service(
            apk_path=Path("/test/project/app-release.apk"),
            public_key="abc123", 
            android_emulator_tools=mocks["android_emulator_tools"],
            logger=mocks["logger"]
        )
        
        # Just testing that it uses the provided public key
        mocks["android_emulator_tools"].update_application.assert_called_once_with(
            Path("/test/project/app-release.apk"), "abc123"
        )
    
    def test_update_apk_invalid_json(self, setup_mocks):
        """Test when emulator initialization file contains invalid JSON"""
        mocks = setup_mocks
        
        # Test with mocked public_key parameter
        result = mobile_project_builder._update_apk_in_emulator_service(
            apk_path=Path("/test/project/app-release.apk"),
            public_key="abc123",
            android_emulator_tools=mocks["android_emulator_tools"],
            logger=mocks["logger"]
        )
        
        # Just testing that it uses the provided public key
        mocks["android_emulator_tools"].update_application.assert_called_once_with(
            Path("/test/project/app-release.apk"), "abc123"
        )
    
    def test_update_apk_failure(self, setup_mocks):
        """Test when update application fails"""
        mocks = setup_mocks
        
        # Mock update application failure
        mocks["android_emulator_tools"].update_application.return_value = None
        
        # Test with mocked public_key parameter
        result = mobile_project_builder._update_apk_in_emulator_service(
            apk_path=Path("/test/project/app-release.apk"),
            public_key="abc123",
            android_emulator_tools=mocks["android_emulator_tools"],
            logger=mocks["logger"]
        )
        
        # Assertions
        assert "Failed to update APK" in result
        mocks["android_emulator_tools"].update_application.assert_called_once()
        mocks["logger"].error.assert_called_once_with("Failed to update APK - update_application returned None")
    
    def test_update_apk_exception(self, setup_mocks):
        """Test when update application raises an exception"""
        mocks = setup_mocks
        
        # Mock update application raising exception
        mocks["android_emulator_tools"].update_application.side_effect = Exception("Update error")
        
        # Test with mocked public_key parameter
        result = mobile_project_builder._update_apk_in_emulator_service(
            apk_path=Path("/test/project/app-release.apk"),
            public_key="abc123",
            android_emulator_tools=mocks["android_emulator_tools"],
            logger=mocks["logger"]
        )
        
        # Assertions
        assert "Failed to update APK" in result
        assert "Update error" in result
        mocks["android_emulator_tools"].update_application.assert_called_once()
        mocks["logger"].error.assert_called_once()


class TestGetProjectPathFromInitFile:
    
    def test_get_project_path_from_init_file_success(self, setup_mocks):
        """Test successful extraction of project path from init file"""
        mocks = setup_mocks
        valid_config = {
            "working_directory": "/test/project"
        }
        mocks["executor"].run_one_shot_command.return_value = (json.dumps(valid_config), 0)
        
        result = mobile_project_builder._get_project_path_from_init_file(
            init_file=Path("/test/project/.init/.init-build-cmd"),
            executor=mocks["executor"],
            logger=mocks["logger"]
        )
        
        assert result == Path("/test/project")
        
    def test_get_project_path_from_init_file_command_failure(self, setup_mocks):
        """Test when command to read init file fails"""
        mocks = setup_mocks
        mocks["executor"].run_one_shot_command.return_value = ("Error reading file", 1)
        
        result = mobile_project_builder._get_project_path_from_init_file(
            init_file=Path("/test/project/.init/.init-build-cmd"),
            executor=mocks["executor"],
            logger=mocks["logger"]
        )
        
        assert result is None
        mocks["logger"].error.assert_called_once()
        
    def test_get_project_path_from_init_file_invalid_json(self, setup_mocks):
        """Test when init file contains invalid JSON"""
        mocks = setup_mocks
        mocks["executor"].run_one_shot_command.return_value = ("invalid json", 0)
        
        result = mobile_project_builder._get_project_path_from_init_file(
            init_file=Path("/test/project/.init/.init-build-cmd"),
            executor=mocks["executor"],
            logger=mocks["logger"]
        )
        
        assert result is None
        mocks["logger"].error.assert_called_once()


class TestGetProjectPathFromManifest:
    
    def test_get_project_path_from_manifest_success(self, setup_mocks, mock_valid_manifest_yaml):
        """Test successful extraction of project path from manifest"""
        mocks = setup_mocks
        
        # Mock file open
        with patch('builtins.open', MagicMock()):
            result = mobile_project_builder._get_project_path_from_manifest(
                manifest_file=Path("/test/project_parent/.project_manifest.yaml"),
                project_root_path="/test/project",
                logger=mocks["logger"]
            )
            
            # The path should be parent dir + container_root from the manifest
            assert result == Path("/test/component_pc_mobile_workspace/component_pc_mobile")
            mocks["logger"].info.assert_called_once()
            
    def test_get_project_path_from_manifest_no_mobile_container(self, setup_mocks):
        """Test when manifest has no mobile container"""
        mocks = setup_mocks
        
        # Mock manifest with no mobile container
        invalid_manifest = {
            'containers': [
                {
                    'container_type': 'web',
                    'container_root': 'web_app'
                }
            ]
        }
        
        with patch('yaml.safe_load', return_value=invalid_manifest):
            with patch('builtins.open', MagicMock()):
                result = mobile_project_builder._get_project_path_from_manifest(
                    manifest_file=Path("/test/project_parent/.project_manifest.yaml"),
                    project_root_path="/test/project",
                    logger=mocks["logger"]
                )
                
                assert result is None
                mocks["logger"].error.assert_called_once()
                assert "No mobile container found" in mocks["logger"].error.call_args[0][0]
                
    def test_get_project_path_from_manifest_invalid_yaml(self, setup_mocks):
        """Test when manifest contains invalid YAML"""
        mocks = setup_mocks
        
        # Mock YAML parsing exception
        with patch('yaml.safe_load', side_effect=yaml.YAMLError("Invalid YAML")):
            with patch('builtins.open', MagicMock()):
                result = mobile_project_builder._get_project_path_from_manifest(
                    manifest_file=Path("/test/project_parent/.project_manifest.yaml"),
                    project_root_path="/test/project",
                    logger=mocks["logger"]
                )
                
                assert result is None
                mocks["logger"].error.assert_called_once()
                assert "Failed to parse project manifest YAML" in mocks["logger"].error.call_args[0][0]