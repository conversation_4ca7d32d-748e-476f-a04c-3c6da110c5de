import os.path
import tempfile
import time
import uuid
import pytest
from unittest.mock import MagicMock
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor, KAVIA_USER
from code_generation_core_agent.agents.tools.environment_summary import EnvironmentSummary, MAX_DATA_SIZE
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools

# Create random directory for testing
BASE_PATH = os.path.join("/tmp/kavia", str(uuid.uuid4()))

@pytest.fixture(scope="module")
def docker_executor():
    executor = DockerExecutor()
    yield executor
    executor.cleanup_all_containers()


@pytest.fixture(scope="module")
def container_file_tools(docker_executor):
    logger = MagicMock()
    return ContainerFileTools({}, BASE_PATH, logger, executor=docker_executor)


@pytest.fixture(scope="module")
def environment_summary(docker_executor):
    logger = MagicMock()
    return EnvironmentSummary({}, BASE_PATH, logger, executor=docker_executor)


class TestEnvironmentSummary:

    @pytest.fixture(autouse=True)
    def setup_test_environment(self, container_file_tools):
        src_dir = os.path.join(BASE_PATH, "src")
        docs_dir = os.path.join(BASE_PATH, "docs")

        container_file_tools.create_directory(BASE_PATH)
        container_file_tools.create_directory(src_dir)
        container_file_tools.create_directory(docs_dir)

        main_py = os.path.join(src_dir, "main.py")
        utils_py = os.path.join(src_dir, "utils.py")
        readme_md = os.path.join(docs_dir, "README.md")

        # Create some sample files
        container_file_tools.write_file(main_py, "print('Hello, World!')")
        container_file_tools.write_file(main_py, "def main(): pass")

        container_file_tools.write_file(utils_py, "def add(a, b): return a + b")
        container_file_tools.write_file(readme_md, "# Project Documentation")

        yield

        # Cleanup after tests
        container_file_tools.delete_file(BASE_PATH)

    def test_get_file_tree_summary(self, environment_summary):
        result = environment_summary.get_file_tree_summary(BASE_PATH)
        assert isinstance(result, dict)
        assert result['status'] == 'SUCCESS'
        assert isinstance(result['message'], str)
        assert f"File tree summary for {BASE_PATH}:" in result['message']
        assert "src" in result['message']
        assert "docs" in result['message']
        assert "main.py" in result['message']
        assert "utils.py" in result['message']
        assert "README.md" in result['message']

    def test_get_file_tree_summary_hidden_directories(self, environment_summary, container_file_tools):
        # Create a hidden directory and a file within it
        hidden_dir = os.path.join(BASE_PATH, ".hidden_dir")
        container_file_tools.create_directory(hidden_dir)
        container_file_tools.write_file(os.path.join(hidden_dir, "hidden_file.txt"), "Hidden content")

        result = environment_summary.get_file_tree_summary(BASE_PATH)
        assert isinstance(result, dict)
        assert result['status'] == 'SUCCESS'
        assert isinstance(result['message'], str)
        assert f"File tree summary for {BASE_PATH}:" in result['message']
        assert ".hidden_dir" in result['message']
        assert "hidden_file.txt" not in result['message']

    def test_get_file_tree_summary_subdirectories(self, environment_summary, container_file_tools):
        # Create a deeper directory structure
        deep_dir = os.path.join(BASE_PATH, "level1", "level2", "level3")
        container_file_tools.create_directory(deep_dir)
        container_file_tools.write_file(os.path.join(deep_dir, "deep_file.txt"), "Deep content")

        result = environment_summary.get_file_tree_summary(BASE_PATH)
        assert isinstance(result, dict)
        assert result['status'] == 'SUCCESS'
        assert isinstance(result['message'], str)
        assert f"File tree summary for {BASE_PATH}:" in result['message']
        assert "level1" in result['message']
        assert "level2" in result['message']
        assert "level3" in result['message']
        assert "deep_file.txt" in result['message']

    def test_get_file_tree_summary_ignore_dot_directories(self, environment_summary, container_file_tools):
        # Create a .git directory
        dot_git_dir = os.path.join(BASE_PATH, ".git")
        container_file_tools.create_directory(dot_git_dir)
        container_file_tools.write_file(os.path.join(dot_git_dir, "config"), "# Git config")

        result = environment_summary.get_file_tree_summary(BASE_PATH)
        assert result['status'] == 'SUCCESS'
        assert ".git/ # dot directories are not listed in search" in result['message']
        assert "config" not in result['message']

    def test_get_file_tree_summary_non_existent_directory(self, environment_summary):
        non_existent_dir = os.path.join(BASE_PATH, "non_existent")
        result = environment_summary.get_file_tree_summary(non_existent_dir)
        assert isinstance(result, dict)
        assert result['status'] == 'ERROR'
        assert "does not exist" in result['message']

    def test_get_file_tree_summary_outside_base_path(self, environment_summary):
        outside_dir = os.path.dirname(BASE_PATH)  # Parent directory of BASE_PATH
        result = environment_summary.get_file_tree_summary(outside_dir)
        assert isinstance(result, dict)
        assert result['status'] == 'ERROR'
        assert "not a subdirectory" in result['message']

    def test_get_file_tree_summary_md_files(self, environment_summary, container_file_tools):
        container_file_tools.write_file(os.path.join(BASE_PATH, "root.md"), "Root MD content")
        container_file_tools.write_file(os.path.join(BASE_PATH, "src", "code.md"), "Code MD content")

        result = environment_summary.get_file_tree_summary(BASE_PATH)
        assert isinstance(result, dict)
        assert result['status'] == 'SUCCESS'
        assert isinstance(result['message'], str)
        assert "root.md" in result['message']
        assert "README.md" in result['message']
        assert "code.md" in result['message']

    def test_get_code_summary(self, environment_summary):
        result = environment_summary.get_code_summary(BASE_PATH)
        assert isinstance(result, dict)
        assert result["status"] == "SUCCESS"
        assert "Code summary:" in result["message"]
        assert "main.py" in result["message"]
        assert "utils.py" in result["message"]
        assert "def add(a, b): return a + b" in result["message"]

    def test_get_running_network_services_summary(self, environment_summary):
        result = environment_summary.get_running_network_services_summary()
        assert isinstance(result, dict)
        assert result["status"] == "SUCCESS"
        assert "Proto" in result["message"]
        assert "Local Address" in result["message"]

    def test_get_environment_summary(self, environment_summary):
        result = environment_summary.get_environment_summary("test_environment")
        assert isinstance(result, dict)
        assert result["status"] == "SUCCESS"
        assert "Environment Variables:" in result["message"]

    def test_run_oneshot_command_success(self, environment_summary):
        cmd = f"echo 'Hello, World!' > {os.path.join(BASE_PATH, 'test.txt')} && cat {os.path.join(BASE_PATH, 'test.txt')}"
        result, output = environment_summary._run_oneshot_command_in_executor(cmd, timeout=10)
        assert result is True
        assert output.strip() == "Hello, World!"

    def test_run_oneshot_command_failure(self, environment_summary):
        cmd = f"non_existent_command {BASE_PATH}"
        result, output = environment_summary._run_oneshot_command_in_executor(cmd, timeout=10)
        assert result is False
        assert "ERROR executing command" in output

    @pytest.mark.parametrize("directory", ["", "non_existent_dir"])
    def test_get_file_tree_summary_edge_cases(self, environment_summary, directory):
        test_path = os.path.join(BASE_PATH, directory)
        result = environment_summary.get_file_tree_summary(test_path)
        assert isinstance(result, dict)
        if directory == "non_existent_dir":
            assert "ERROR" in result or "non_existent_dir does not exist" in result["message"]
        else:
            assert f"File tree summary for {test_path}" in result["message"]

    def test_get_code_summary_no_python_files(self, environment_summary, container_file_tools):
        # Create a directory with non-Python files
        no_python_dir = os.path.join(BASE_PATH, "no_python")
        container_file_tools.create_directory(no_python_dir)
        container_file_tools.write_file(os.path.join(no_python_dir, "test.txt"), "This is a text file")

        result = environment_summary.get_code_summary(no_python_dir)
        assert isinstance(result, dict)
        assert result["status"] == "SUCCESS"
        assert "Code summary:" in result["message"]
        assert len(result["message"].split("\n")) == 2  # Only the "Code summary:" line with \n at the end

    def test_environment_summary_error_handling(self, environment_summary):
        # Test with a non-existent directory
        non_existent_dir = os.path.join(BASE_PATH, "non_existent_directory")
        result = environment_summary.get_file_tree_summary(non_existent_dir)
        assert "ERROR" in result['status'] or "non_existent_directory does not exist" in result['message']


    def test_subdir_tree(self, environment_summary, container_file_tools):
        dir_path = os.path.join(BASE_PATH, f"dir_1")
        container_file_tools.create_directory(dir_path)
        file_path = os.path.join(dir_path, f"file_1.txt")
        container_file_tools.write_file(file_path, f"Content")

        result = environment_summary.get_file_tree_summary("dir_1")['message']
        print("*************** Got result: ", result)
        assert isinstance(result, str)
        assert f"File tree summary for {os.path.join(BASE_PATH, 'dir_1')}:" in result
        assert "file_1.txt" in result

    def test_large_file_tree(self, environment_summary, container_file_tools):
        for i in range(100):
            dir_path = os.path.join(BASE_PATH, f"dir_{i}")
            container_file_tools.create_directory(dir_path)
            for j in range(10):
                file_path = os.path.join(dir_path, f"file_{j}.txt")
                container_file_tools.write_file(file_path, f"Content of file {j} in directory {i}")

        result = environment_summary.get_file_tree_summary(BASE_PATH)['message']
        assert isinstance(result, str)
        assert f"File tree summary for {BASE_PATH}:" in result
        assert "dir_0" in result
        assert "dir_9" in result
        assert "file_0.txt" not in result
        assert "file_9.txt" not in result

    def test_file_tree_summary_four_levels(self, environment_summary, container_file_tools):
        # Create a 4-level directory structure in BASE_DIR
        for i in range(3):
            level1_dir = os.path.join(BASE_PATH, f"dir_{i}")
            container_file_tools.create_directory(level1_dir)
            for j in range(2):
                level2_dir = os.path.join(level1_dir, f"subdir_{j}")
                container_file_tools.create_directory(level2_dir)
                for k in range(2):
                    level3_dir = os.path.join(level2_dir, f"subsubdir_{k}")
                    container_file_tools.create_directory(level3_dir)
                    for l in range(2):
                        level4_dir = os.path.join(level3_dir, f"subsubsubdir_{l}")
                        container_file_tools.create_directory(level4_dir)
                        # Create a file in the deepest level
                        file_path = os.path.join(level4_dir, "file.txt")
                        container_file_tools.write_file(file_path, "Test content")

        result = environment_summary.get_file_tree_summary(BASE_PATH)

        assert result['status'] == 'SUCCESS', "Expected status to be SUCCESS"
        summary = result['message']

        assert f"File tree summary for {BASE_PATH}:" in summary, "Missing file tree summary header"

        for i in range(3):
            assert f"dir_{i}" in summary, f"Missing top-level directory dir_{i}"

        assert "subdir_0" in summary, "Missing second-level directory subdir_0"
        assert "subdir_1" in summary, "Missing second-level directory subdir_1"

        # Check for third-level directories
        assert "subsubdir_0" in summary, "Missing third-level directory subsubdir_0"
        assert "subsubdir_1" in summary, "Missing third-level directory subsubdir_1"

        # Check for fourth-level directories
        assert "subsubsubdir_0" in summary, "Missing fourth-level directory subsubsubdir_0"
        assert "subsubsubdir_1" in summary, "Missing fourth-level directory subsubsubdir_1"

        assert "file.txt" in summary, "Missing file.txt in the deepest level"

        assert len(summary) <= MAX_DATA_SIZE, f"Summary exceeds MAX_DATA_SIZE of {BASE_PATH}"

    def test_code_summary_with_various_file_types(self, environment_summary, container_file_tools):
        # Create files with different extensions
        container_file_tools.write_file(os.path.join(BASE_PATH, "script.py"), "def script(): print('Python script')")
        container_file_tools.write_file(os.path.join(BASE_PATH, "script.js"), "console.log('JavaScript script');")
        container_file_tools.write_file(os.path.join(BASE_PATH, "style.css"), "body { color: black; }")
        container_file_tools.write_file(os.path.join(BASE_PATH, "data.json"), '{"key": "value"}')

        result = environment_summary.get_code_summary(BASE_PATH)
        assert isinstance(result, dict)
        assert result["status"] == "SUCCESS"
        assert "Code summary:" in result["message"]
        assert "script.py" in result["message"]
        assert "script.js" not in result["message"]
        assert "style.css" in result["message"]
        assert "data.json" in result["message"]

    def test_environment_summary_with_environment_variables(self, environment_summary, container_file_tools):
        # Create a file with environment variables
        env_file = os.path.join(BASE_PATH, ".env")
        container_file_tools.write_file(env_file, "TEST_VAR=test_value\nANOTHER_VAR=another_value")

        result = environment_summary.get_environment_summary("test_environment")
        assert isinstance(result, dict)
        assert result["status"] == "SUCCESS"
        assert "Environment Variables:" in result["message"]
        # Note: The actual environment variables might not be visible in the summary due to security measures

    def test_get_running_network_services_summary_with_active_service(self, environment_summary, docker_executor):
        # Start a simple HTTP server in the background
        cmd = f"nohup python3 -m http.server 8000 --directory {BASE_PATH} &"
        docker_executor.run_one_shot_command(cmd)

        result = environment_summary.get_running_network_services_summary()
        assert isinstance(result, dict)
        assert result["status"] == "SUCCESS"
        assert "Proto" in result["message"]
        assert "Local Address" in result["message"]
        assert ":8000" in result["message"]

        # Cleanup: kill the HTTP server
        docker_executor.run_one_shot_command("pkill 'python3'")

    def test_local_bin_path_in_path(self, environment_summary, docker_executor):
        # Run export command and export the PATH variable
        cmd = "export"
        response, status = docker_executor.run_one_shot_command(cmd)
        assert response is not None
        assert status == 0
        # Split the result and check that the /home/<USER>/.local/bin is in the PATH
        response = response.split("\n")
        path = None
        for line in response:
            if "PATH=" in line:
                path = line
                break
        assert path is not None
        assert f"/home/<USER>/.local/bin" in path
