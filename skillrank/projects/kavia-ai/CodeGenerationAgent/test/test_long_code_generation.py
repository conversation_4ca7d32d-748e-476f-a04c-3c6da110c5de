import os
import random
import string
import time
import unittest

from . import agent_cli
from .data.test_cases import INTERACTIVE_WORK_ITEMS, DESCRIPTION_COMMON_POST_FIX, TARGET_BACKGROUND_COLOR


class TestCodeGenerator(unittest.TestCase):
    """
    A test suite for the code generation process.

    This class contains tests that run for each work item
    in the INTERACTIVE_WORK_ITEMS list, as well as a test for the background color setting.
    """

    @staticmethod
    def wait_for_some_code_generated(dir_path):
        """
        Check if any code files have been generated in the given directory.

        Args:
            dir_path (str): The path to the directory to check.

        Returns:
            bool: True if any code files are found, False otherwise.
        """
        # Traverse the directory and check if there are any .py, .js, .html or .c, .cpp files
        # If there are, return True, else return False
        for root, dirs, files in os.walk(dir_path):
            for file in files:
                if file.endswith(('.py', '.js', '.html', '.c', '.cpp')):
                    return True
        return False

    def run_code_generation_test(self, job):
        """
        Run the code generation process for a given work item.
        Waits for the process to complete and checks if it finished successfully.

        Args:
            job (dict): A dictionary containing the work item and user input list.
        """
        random.seed(time.time())

        if 'KAVIA_SESSION_ID' in os.environ:
            print("----- using session id from env variables ------")
            session_id = os.environ['KAVIA_SESSION_ID']
        else:
            print("---- generating new session id ----")
            session_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        session_id = "codegen-myqvf9mlbi"
        work_item = job['work_item']
        work_item['description'] += DESCRIPTION_COMMON_POST_FIX

        agent = agent_cli.ShellCGExecutor(work_item, job['user_input_list'], session_id=session_id)
        agent.start()

        test_name = f"codegen_{work_item['component_name'].lower().replace(' ', '_')}"
        cost_report_path = os.path.join('/tmp', f"{test_name}.html")

        # Wait max 1h for process to complete
        for _ in range(60 * 60):
            time.sleep(1)
            agent.status_reporter.save_final_cost_report(cost_report_path)
            if not agent.executor.is_alive():
                print(f"Code generation process completed for {work_item['component_name']}")
                break


        agent.status_reporter.save_final_cost_report(cost_report_path)
        print(f"Cost report saved to: {cost_report_path}")

        agent.exit()

        # Check if process completed successfully
        self.assertFalse(agent.executor.is_alive(),
                         f"Code generation process did not complete for {work_item['component_name']}")

    def test_background_color(self):
        """
        Test if the TARGET_BACKGROUND_COLOR is set correctly.

        This test ensures that the background color specified in the test cases
        matches the expected value.
        """
        # This test checks if the TARGET_BACKGROUND_COLOR is correctly set
        self.assertEqual(TARGET_BACKGROUND_COLOR, "#ADDFFF", "TARGET_BACKGROUND_COLOR is not set correctly")


def generate_test_functions():
    """
    Dynamically generate test functions for each work item.
    Creates a test method for each work item in INTERACTIVE_WORK_ITEMS.
    """
    for index, job in enumerate(INTERACTIVE_WORK_ITEMS):
        test_name = f"test_complete_codegen_{job['work_item']['component_name'].lower().replace(' ', '_')}"

        def test_function(self, job=job):
            self.run_code_generation_test(job)

        setattr(TestCodeGenerator, test_name, test_function)


# Generate the test functions
generate_test_functions()

if __name__ == '__main__':
    unittest.main()
