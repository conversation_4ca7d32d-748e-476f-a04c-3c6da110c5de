# Description: This is a test script to run the code generation agent
import asyncio
import base64
import os
import queue
import random
import string
import datetime
import sys
import threading
import time
import unittest
from enum import Enum
from io import BytesIO

import numpy as np
import yaml
from PIL import Image
from dotenv import load_dotenv

from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent, TaskExecutionControl, \
    TaskExecutionReporter
from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator, SummarizerType
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType
from code_generation_core_agent.config import config
from code_generation_core_agent.llm.chat_interface import ChatInterface, MessageType
from code_generation_core_agent.llm.llm_interface import LLMInterface
from .data.test_cases import TARGET_BACKGROUND_COLOR

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TCGReporter(TaskExecutionReporter):
    # Indicate if during the test run we detect the web app to have
    # the required background color. This indicates that the test generated a functional app.
    background_color_found = False
    test_succeeded = False
    test_completed = False
    process_completed = False

    validation_summarizer = OutputTruncator(100, summarizer_type=SummarizerType.VALIDATION_COMPLETED_SUMMARIZER)
    def __init__(self, agent, user_input_list: list, chat: ChatInterface = None):
        super().__init__()

        self.lock = threading.Lock()
        self.agent = agent
        self.message_queue = queue.Queue()
        self.stop_event = threading.Event()
        self.handler_thread = threading.Thread(target=self._user_input_message_handler)
        self.input_values = user_input_list
        self.input_count = len(user_input_list)
        self.current_input_index = 0
        self.current_agent_costs = {}
        self.current_total_cost = 0.0
        self.chat  = chat
        if self.chat:
            self.chat.add_callback("message_added", self.on_message_added)

    def progress_callback(self, status, total_tasks, latest_result, request_context):
        print(f"Progress: {status} {total_tasks} {latest_result} {request_context}")
        pass

    def mark_process_completed(self):
        """Called when process_work_item completes"""
        self.process_completed = True
        self.test_succeeded = True
        print("[*] Process completed successfully")

    def terminal_output_callback(self, output):
        # print(f"Terminal output: {output}")
        pass

    def browser_output_callback(self, image):
        image_data = base64.b64decode(image)
        img = Image.open(BytesIO(image_data))
        img = img.convert('RGB')
        np_img = np.array(img)
        pixels = np_img.reshape(-1, 3)

        hex_colors = ['#{:02x}{:02x}{:02x}'.format(r, g, b) for r, g, b in pixels]
        unique, counts = np.unique(hex_colors, return_counts=True)
        top_5_colors = unique[np.argsort(-counts)][:5]

        if TARGET_BACKGROUND_COLOR.lower() in [color.lower() for color in top_5_colors]:
            print("[*] Background color found. Target color found in top 5 colors.", TARGET_BACKGROUND_COLOR,
                  top_5_colors)
            self.background_color_found = True
        else:
            self.background_color_found = False

        import tempfile
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
        img.save(temp_file.name)

        print(f"***** Image saved to: {temp_file.name}")
        print(f"***** Top 5 colors: {top_5_colors}")
        print(f"***** Process completed: {self.process_completed}")

    def get_user_input(self, question):
        pass
    def send_code_panel_update(self, stream_id, content, metadata=None):
        pass

    def save_final_cost_report(self, filename):
        """Save final cost report as a simple HTML file"""
        html = f"""
              <html>
              <head>
                  <style>
                      body {{ font-family: Arial, sans-serif; margin: 20px; }}
                      table {{ border-collapse: collapse; width: 100%; }}
                      th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                      th {{ background-color: #f2f2f2; }}
                      .total-row {{ font-weight: bold; background-color: #f8f8f8; }}
                      .cost-cell {{ text-align: right; }}
                  </style>
              </head>
              <body>
                  <h2>Final Cost Report</h2>
                  <table>
                      <tr>
                          <th>Agent</th>
                          <th>Cost ($)</th>
                      </tr>
              """

        with self.lock:
            for agent, cost in self.current_agent_costs.items():
                html += f"""
                    <tr>
                        <td>{agent}</td>
                        <td class="cost-cell">${cost:.6f}</td>
                    </tr>
                """

            html += f"""
                    <tr class="total-row">
                        <td>Total</td>
                        <td class="cost-cell">${self.current_total_cost:.6f}</td>
                    </tr>
                </table>
            </body>
            </html>
            """

        with open(filename, 'w') as f:
            f.write(html)

    def cost_update_callback(self, agent_costs, total_cost):
        """Store the latest cost update"""
        with self.lock:
            self.current_agent_costs = agent_costs.copy()
            self.current_total_cost = total_cost

    def task_start_callback(self, task_description):
        print(f"Task start: {task_description}")
        pass

    def user_query_callback(self, questions):
        print(f"User query: {questions}")
        pass

    def send_agent_message(self, message):
        # Keep for logging but remove effect on success criteria
        passed = self.validation_summarizer.truncate_output(message, allways_truncate=True)
        print(f"[*] Agent message received: {passed}")

    def _print_user_input(self, user_input):
        print("*************************************************************************")
        print("*")
        print(f" *** User input: {user_input}")
        print("*")
        print("*************************************************************************")

    def on_message_added(self, message):
        if not self.chat:
            return

        if message.msg_type.value != MessageType.LLM.value or not message.requires_resolution:
            return # We are only interested in messages from the LLM

        print(f"New message {message}")

        if self.current_input_index >= self.input_count:
            self._print_user_input("Visually validate the application. Ensure that it does what it is supposed to do.")
            return "Visually validate the application. Ensure that it does what it is supposed to do."

        current_input = self.input_values[self.current_input_index]

        self.current_input_index += 1
        print(f"[*] Sending user input: {current_input}")
        self.chat.get_user_interface().send_message(current_input, parent_id=message.id)

    def _user_input_message_handler(self):
        while not self.stop_event.is_set():
            try:
                message = self.message_queue.get(timeout=1)
                self.agent.set_user_input(message)
            except queue.Empty:
                pass

    def _start(self):
        """Starts the message handler thread."""
        self.handler_thread.start()

    def _stop(self):
        """Stops the message handler thread."""
        self.stop_event.set()
        self.handler_thread.join()

    def send_input_message(self, message):
        """Adds a message to the queue."""
        self.message_queue.put(message)
    
    def app_state_callback(self, url, state=None):
        """
        Handle application state updates.
        Args:
            url (str): The URL of the application
            state (dict, optional): Additional state information
        """
        print(f"App state update: URL={url}, state={state}")
        pass

    def universal_init_status_callback(self, container_name, status, message=None, error=None):
        """
        Report the status of universal initialization for a container/component.
        
        Args:
            container_name (str): Name of the container or component being initialized
            status (str): Status of initialization ('starting', 'success', 'failed', 'completed')
            message (str, optional): Status message or description
            error (str, optional): Error message if initialization failed
        """
        pass


class TestTCGReporter(unittest.TestCase):
    def setUp(self):
        self.reporter = TCGReporter(agent=None, user_input_list=["do nothing", "do something"])

    def test_browser_output_callback_with_png(self):
        test_image_path = "test/data/ref_image_validation.png"

        self.assertTrue(os.path.exists(test_image_path), "Test image does not exist")

        with open(test_image_path, "rb") as image_file:
            image_data = image_file.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')

        self.reporter.browser_output_callback(image_base64)

        self.assertIsNotNone(self.reporter.test_succeeded)

        print(f"Test succeeded: {self.reporter.test_succeeded}")


def generate_session_dir(session_id="1114") -> str:
    n = 6
    session_root = os.path.join(config.get("SYSTEM", "workspace_dir"), "kavia")
    characters = string.ascii_lowercase + string.digits
    ident = "".join(random.choice(characters) for _ in range(n))
    return os.path.join(session_root, f"codegen-{session_id}")


def makedir_p(path: str):
    from pathlib import Path

    try:
        Path(path).mkdir(parents=True, exist_ok=True)
    except Exception as e:
        print(f"Failed to create directory {path}: {str(e)}")
        raise


def check_pause():
    pass


class TEControl(TaskExecutionControl):
    def __init__(self):
        self.is_paused = False
        self.should_stop = False
        self.pause_condition = threading.Condition()

    def pause(self):
        self.is_paused = True

    def resume(self):
        with self.pause_condition:
            self.is_paused = False
            self.pause_condition.notify_all()

    def stop(self):
        self.should_stop = True
        self.resume()  # In case it's paused

    def check_status(self):
        print("Stop condition: ", self.should_stop)
        if self.should_stop:
            raise StopIteration("Code generation stopped")
        with self.pause_condition:
            while self.is_paused:
                self.pause_condition.wait()

    def reset(self):
        self.is_paused = False
        self.should_stop = False


class ShellCGExecutor:
    """
    CLI executor for code generation
    """
    class ContainerTypes(Enum):
        FRONTEND = "frontend"
        BACKEND = "backend"
        DATABASE = "database"

    def __init__(self, work_item,
                 user_input_list,
                 session_id,
                 containers : dict[ContainerTypes, str] = None,
                 log_handler=None
                 ):
        """
        Shell executor for code generation
        """
        # you can always hard-code this is you want to test
        session_dir = generate_session_dir(session_id)

        print("Using session directory: ", session_dir)
        makedir_p(os.path.join(session_dir, "logs"))

        self.status_reporter = None
        # Create a new executor thread
        self.executor = threading.Thread(target=self.execute)
        self.work_item = work_item
        self.user_input_list = user_input_list
        self.thread_exited_with_error = False

        load_dotenv()

        OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
        model_name = config.get("LLM", "model")

        # rotate any existing log files with a timestamp

        timestamp = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

        for f in os.listdir(session_dir + "/logs"):
            if f.endswith(".log"):
                os.rename(os.path.join(session_dir + "/logs", f),
                          os.path.join(session_dir + "/logs", f"{f}-{timestamp}"))


        self.session_dir = session_dir
        self.container_dir = session_dir + "/" + work_item['containers'][0]['relative_path']

        print(f"**** Using session directory: {session_dir}")

        self.llm = LLMInterface(llm_api_key=OPENAI_API_KEY,
                                session_dir=session_dir, instance_name="TaskExecutionAgent")

        script_dir = os.path.dirname(os.path.abspath(__file__))

        base_config_path = os.path.join(script_dir, "..", "src", "code_generation_core_agent", "agents", "micro")

        platform_loader = PlatformConfigLoader(base_config_path)

        selected_platform = work_item.get("platform", ApplicationType.COMMON)
        selected_framework = work_item.get("framework", FrameworkType.DEFAULT)

        try:
            platform_config = platform_loader.get_platform_config(selected_platform, selected_framework)
        except ValueError as e:
            print(f"Warning: {e}. Falling back to common platform configuration.")
            platform_config = platform_loader.get_platform_config(ApplicationType.COMMON, FrameworkType.DEFAULT)

        prompt_path = os.path.join(script_dir, "..", "src", "code_generation_core_agent", "agents", "micro", "prompts")

        if not os.path.exists(prompt_path):
            raise Exception(f"Prompt path {prompt_path} does not exist")
        self.code_generator_control = TEControl()
        self.chat_interface = ChatInterface()
        self.code_generation_agent = TaskExecutionAgent(self.llm,
                                                        execution_base_path=session_dir,
                                                        micro_agents_config=platform_config,
                                                        model_name=model_name,
                                                        prompts_base_path=prompt_path,
                                                        platform=selected_platform,
                                                        )
        if (log_handler is not None):
            self.code_generation_agent.logger.addHandler(log_handler)

        self.status_reporter = TCGReporter(self.code_generation_agent, self.user_input_list, self.chat_interface)

    def get_session_dir(self):
        return self.session_dir

    def execute(self, container = None):
        try:
            asyncio.run(
                self.code_generation_agent.process_work_item("CodeGeneration", self.work_item,
                                                             False, self.code_generator_control,
                                                             self.status_reporter,
                                                             previous_context=None,
                                                             chat_interface=self.chat_interface))
            self.status_reporter.mark_process_completed()
        except Exception as e:
            from traceback import print_exc
            print_exc()
            self.thread_exited_with_error = True
            raise

    def start(self):
        self.executor.start()

    def exit(self, timeout=10):
        """Exit with timeout

        Args:
            timeout (int): Seconds to wait before giving up on graceful shutdown
        """
        print("Exiting...")
        self._running = False
        self.code_generator_control.stop()
        print("Waiting for code generation agent to complete...")
        self.code_generation_agent.stop()
        print(f"Waiting {timeout} seconds for executor to complete...")

        self.executor.join(timeout=timeout)
        if self.executor.is_alive():
            print("Thread did not terminate gracefully - process will exit anyway since it's a daemon thread")

        print("Executor completed")


def main():
    from data.test_cases import INTERACTIVE_WORK_ITEMS

    job = INTERACTIVE_WORK_ITEMS[0]
    work_item = job['work_item']
    executor = ShellCGExecutor(job['work_item'], job['user_input_list'], "1114")

    executor.start()
    for i in range(60 * 15):
        time.sleep(1)
        if executor.status_reporter.test_succeeded:
            break
    # Fail test if the target color is not found in the top 5 colors
    assert TCGReporter.test_succeeded, "Failed to validate application"
    executor.exit()


if __name__ == "__main__":
    main()
