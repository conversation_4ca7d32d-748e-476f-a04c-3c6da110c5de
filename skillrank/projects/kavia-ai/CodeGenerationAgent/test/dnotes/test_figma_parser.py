import unittest
import json
import os
from pathlib import Path

from dnparsers.figma.figma_parser import FigmaParser, parse_figma_file
from dnparsers.figma.models import *


class TestFigmaParser(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are reused across all tests."""
        cls.test_data_dir = Path(__file__).parent / "data"
        if not cls.test_data_dir.exists():
            os.makedirs(cls.test_data_dir)

    def setUp(self):
        """Set up fresh parser instance for each test."""
        self.parser = FigmaParser()

    def load_test_file(self, filename: str) -> Dict[str, Any]:
        """Helper method to load test JSON files."""
        file_path = self.test_data_dir / filename
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Test file not found: {filename}")
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in test file: {filename}")

    def test_basic_component_parsing(self):
        """Test parsing of a basic component with minimal properties."""
        data = self.load_test_file("basic_component.json")
        result = self.parser.parse(data)

        # Access attributes directly instead of dictionary-style access
        self.assertTrue(hasattr(result, 'components'))
        self.assertTrue(hasattr(result, 'metadata'))
        self.assertTrue(hasattr(result, 'styles'))
        self.assertTrue(hasattr(result, 'assets'))

    def test_large_data(self):
        """Test parsing of a large component."""
        data = self.load_test_file("figma_user_auth.json")
        result = self.parser.parse(data)

        # Access attributes directly
        self.assertTrue(hasattr(result, 'components'))
        self.assertTrue(hasattr(result, 'metadata'))
        self.assertTrue(hasattr(result, 'styles'))
        self.assertTrue(hasattr(result, 'assets'))

    def test_complex_component_hierarchy(self):
        """Test parsing of nested component hierarchies."""
        data = self.load_test_file("nested_component.json")
        result = self.parser.parse(data)

        # Access components directly from the attribute
        components = result.components
        self.assertTrue(any(len(c.hierarchy.children) > 0 for c in components))

    def test_style_extraction(self):
        """Test extraction of various styles from components."""
        data = self.load_test_file("styled_component.json")
        result = self.parser.parse(data)

        # Access styles directly from the attribute
        styles = result.styles
        self.assertIn('fills', styles)
        self.assertIn('effects', styles)
        self.assertIn('text', styles)

    def test_color_processing(self):
        """Test color processing and format conversion."""
        test_color = Color(r=0.5, g=0.25, b=0.75, a=1.0)

        # 0.5 * 255 = 127.5 which rounds to 127
        self.assertEqual(int(test_color.r * 255), 127)  # 0.5 * 255 = 127.5 → 127
        self.assertEqual(int(test_color.g * 255), 63)  # 0.25 * 255 = 63.75 → 63
        self.assertEqual(int(test_color.b * 255), 191)  # 0.75 * 255 = 191.25 → 191
        self.assertEqual(test_color.a, 1.0)

        self.assertEqual(test_color.to_hex(), '#7f3fbfff')

        self.assertEqual(test_color.to_rgba(), 'rgba(127, 63, 191, 1.0)')

    def test_node_data_structures(self):
        """Test the data structure classes."""
        # Test Color class
        color = Color(r=0.5, g=0.25, b=0.75, a=1.0)
        self.assertEqual(color.to_hex(), '#7f3fbfff')
        self.assertEqual(color.to_rgba(), 'rgba(127, 63, 191, 1.0)')

        # Test Paint class
        paint = Paint(
            type=PaintType.SOLID,
            color=color,
            blend_mode=BlendMode.NORMAL,
            opacity=0.8
        )
        self.assertEqual(paint.type, PaintType.SOLID)
        self.assertEqual(paint.opacity, 0.8)

        # Test Layout class
        layout = Layout()
        layout.mode = "HORIZONTAL"
        layout.padding = Padding(top=10, right=20, bottom=10, left=20)
        layout.spacing = 8
        layout.alignment = {'primary': 'CENTER', 'counter': 'CENTER'}

        self.assertEqual(layout.mode, "HORIZONTAL")
        self.assertEqual(layout.padding.top, 10)
        self.assertEqual(layout.spacing, 8)


    def test_layout_extraction(self):
        """Test extraction of layout properties."""
        data = self.load_test_file("layout_component.json")
        result = self.parser.parse(data)

        # Access components directly from the attribute
        component = result.components[0]
        self.assertIsNotNone(component.layout)
        layout = component.layout

        self.assertTrue(hasattr(layout, 'mode'))
        self.assertTrue(hasattr(layout, 'padding'))
        self.assertTrue(hasattr(layout, 'spacing'))
        self.assertTrue(hasattr(layout, 'alignment'))

    def test_invalid_data_handling(self):
        """Test handling of invalid input data."""
        with self.assertRaises(ValueError):
            self.parser.parse({})  # Empty dict should raise ValueError

        with self.assertRaises(ValueError):
            parse_figma_file("invalid json string")

    def test_interaction_parsing(self):
        """Test parsing of component interactions."""
        data = self.load_test_file("interactive_component.json")
        result = self.parser.parse(data)

        # Access interactions directly from the attribute
        interactions = result.interactions
        self.assertTrue(isinstance(interactions, list))

        if interactions:  # If there are any interactions
            interaction = interactions[0]
            self.assertIn('nodeId', interaction)
            self.assertIn('nodeName', interaction)
            self.assertIn('interactions', interaction)

    def test_font_asset_collection(self):
        """Test collection of font assets from components."""
        data = self.load_test_file("text_component.json")
        result = self.parser.parse(data)

        # Access assets directly from the attribute
        assets = result.assets
        self.assertIn('fonts', assets)
        self.assertTrue(isinstance(assets['fonts'], list))

    def test_constraints_extraction(self):
        """Test extraction of layout constraints."""
        data = self.load_test_file("constrained_component.json")
        result = self.parser.parse(data)

        # Access components directly from the attribute
        component = result.components[0]
        self.assertIsNotNone(component.constraints)

        if component.constraints:
            constraints = component.constraints
            self.assertTrue(hasattr(constraints, 'horizontal'))
            self.assertTrue(hasattr(constraints, 'vertical'))

    def test_complete_parse_result(self):
        """Test the complete parse result structure with data models."""
        data = self.load_test_file("figma_user_auth_small.json")
        document = parse_figma_file(json.dumps(data))

        # Verify document structure
        self.assertIsInstance(document, FigmaDocument)
        self.assertTrue(hasattr(document, 'metadata'))
        self.assertTrue(hasattr(document, 'components'))
        self.assertTrue(hasattr(document, 'styles'))
        self.assertTrue(hasattr(document, 'assets'))

        # Verify component structure
        for component in document.components:
            self.assertIsInstance(component, Component)
            self.assertIsInstance(component.hierarchy, Node)
            self.assertTrue(hasattr(component.hierarchy, 'children'))

            # Verify node properties
            self.assertTrue(hasattr(component.hierarchy, 'fills'))
            self.assertTrue(hasattr(component.hierarchy, 'strokes'))
            self.assertTrue(hasattr(component.hierarchy, 'effects'))

            # Test layout properties
            if component.layout:
                self.assertIsInstance(component.layout, Layout)
                self.assertIsInstance(component.layout.padding, Padding)


if __name__ == '__main__':
    unittest.main()