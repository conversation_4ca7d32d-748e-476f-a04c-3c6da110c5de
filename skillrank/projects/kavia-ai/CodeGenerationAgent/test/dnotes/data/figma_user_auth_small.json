{"work_items": {"component_name": "User Authentication Sub-Component", "description": "Manages user login, logout, and session management.", "repository_name": "recipe-app", "root_folder": "/", "figma_components": [{"frame_id": "17:80", "file_key": "OTCSlNZllDbCEZ99hDWrbL", "json_data": {"id": "17:80", "name": "Home", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "17:81", "name": "Titttle", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "17:82", "name": "Hello Jega", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:121", "text": "10:202"}, "absoluteBoundingBox": {"x": 1286, "y": -366, "width": 107, "height": 30}, "absoluteRenderBounds": {"x": 1287.3800048828125, "y": -358.79998779296875, "width": 103.4566650390625, "height": 20.259979248046875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Hello Jega", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 20, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 30, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "17:83", "name": "What are you cooking today?", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.6627451181411743, "g": 0.6627451181411743, "b": 0.6627451181411743, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:123", "text": "12:135"}, "absoluteBoundingBox": {"x": 1286, "y": -331, "width": 195, "height": 17}, "absoluteRenderBounds": {"x": 1286.2530517578125, "y": -327.3710021972656, "width": 160.25048828125, "height": 11.34100341796875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "What are you cooking today?", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1286, "y": -366, "width": 195, "height": 52}, "absoluteRenderBounds": {"x": 1286, "y": -366, "width": 195, "height": 52}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "cornerRadius": 30, "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 1, "g": 1, "b": 1, "a": 1}, "styles": {"fills": "4:125", "fill": "4:125"}, "layoutGrids": [{"pattern": "COLUMNS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.10000000149011612}, "alignment": "MAX", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "COLUMNS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MIN", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "ROWS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MIN", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "ROWS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MAX", "gutterSize": 20, "offset": 0, "count": 1}], "absoluteBoundingBox": {"x": 1256, "y": -430, "width": 375, "height": 812}, "absoluteRenderBounds": {"x": 1256, "y": -430, "width": 375, "height": 812}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, "imageUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/a1da6501-2e00-4991-b4c9-3a0ce19fa5de", "thumbnailUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/f602057a-52c3-4a98-b092-53cbd0828116"}, {"frame_id": "11:235", "file_key": "OTCSlNZllDbCEZ99hDWrbL", "json_data": {"id": "11:235", "name": "Sign In", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "13:110", "name": "Titttle", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "12:29", "name": "Hello,", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:121", "text": "10:201"}, "absoluteBoundingBox": {"x": 456, "y": -336, "width": 84, "height": 45}, "absoluteRenderBounds": {"x": 458.07000732421875, "y": -325.20001220703125, "width": 80.43963623046875, "height": 26.220001220703125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Hello,", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 30, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 45, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "12:30", "name": "Welcome Back!", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:127", "text": "10:207"}, "absoluteBoundingBox": {"x": 456, "y": -291, "width": 155, "height": 30}, "absoluteRenderBounds": {"x": 456.4599914550781, "y": -283.79998779296875, "width": 152.07986450195312, "height": 14.97998046875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Welcome Back!", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 20, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 30, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 456, "y": -336, "width": 155, "height": 75}, "absoluteRenderBounds": {"x": 456, "y": -336, "width": 155, "height": 75}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "12:91", "name": "Forgot Password", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "12:94", "name": "Forgot Password?", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.****************, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "13:176", "text": "12:135"}, "absoluteBoundingBox": {"x": 466, "y": 8, "width": 97, "height": 17}, "absoluteRenderBounds": {"x": 466.96954345703125, "y": 11.859999656677246, "width": 95.3753662109375, "height": 11.110001564025879}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Forgot Password?", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 466, "y": 8, "width": 97, "height": 17}, "absoluteRenderBounds": {"x": 466, "y": 8, "width": 97, "height": 17}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "12:137", "name": "Ellipse 1", "visible": false, "type": "ELLIPSE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.****************, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 426, "y": -473, "width": 308, "height": 106}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "LAYER_BLUR", "visible": true, "radius": 500}], "arcData": {"startingAngle": 0, "endingAngle": 6.2831854820251465, "innerRadius": 0}, "interactions": []}, {"id": "13:35", "name": "<PERSON><PERSON>", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "13:36", "name": "Rectangle 642", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "4:125"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 557, "y": 167, "width": 44, "height": 44}, "absoluteRenderBounds": {"x": 549, "y": 159, "width": 60, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.4117647111415863, "g": 0.4117647111415863, "b": 0.4117647111415863, "a": 0.10000000149011612}, "blendMode": "NORMAL", "offset": {"x": 0, "y": 0}, "radius": 5, "spread": 3, "showShadowBehindNode": false}], "interactions": []}, {"id": "13:48", "name": "icons8-google-240 1", "type": "COMPONENT", "scrollBehavior": "SCROLLS", "children": [{"id": "13:39", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.7568627595901489, "b": 0.027450980618596077, "a": 1}}], "strokes": [], "strokeWeight": 5, "strokeAlign": "INSIDE", "styles": {"fill": "13:175"}, "absoluteBoundingBox": {"x": 569, "y": 179, "width": 19.999996185302734, "height": 19.999996185302734}, "absoluteRenderBounds": {"x": 569, "y": 179, "width": 20, "height": 20}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "13:40", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.239215686917305, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 5, "strokeAlign": "INSIDE", "styles": {"fill": "13:173"}, "absoluteBoundingBox": {"x": 570.1530151367188, "y": 179, "width": 15.655999183654785, "height": 7.755001068115234}, "absoluteRenderBounds": {"x": 570.1530151367188, "y": 179, "width": 15.656005859375, "height": 7.7550048828125}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "13:41", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2980392277240753, "g": 0.686274528503418, "b": 0.3137255012989044, "a": 1}}], "strokes": [], "strokeWeight": 5, "strokeAlign": "INSIDE", "styles": {"fill": "13:171"}, "absoluteBoundingBox": {"x": 570.0974731445312, "y": 191.02700805664062, "width": 15.607002258300781, "height": 7.972998142242432}, "absoluteRenderBounds": {"x": 570.0974731445312, "y": 191.02700805664062, "width": 15.60699462890625, "height": 7.972991943359375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "13:42", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.09803921729326248, "g": 0.4627451002597809, "b": 0.8235294222831726, "a": 1}}], "strokes": [], "strokeWeight": 5, "strokeAlign": "INSIDE", "styles": {"fill": "13:169"}, "absoluteBoundingBox": {"x": 579, "y": 187, "width": 9.999998092651367, "height": 9.4141206741333}, "absoluteRenderBounds": {"x": 579, "y": 187, "width": 10, "height": 9.41412353515625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 567, "y": 177, "width": 24, "height": 24}, "absoluteRenderBounds": {"x": 567, "y": 177, "width": 24, "height": 24}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 557, "y": 167, "width": 44, "height": 44}, "absoluteRenderBounds": {"x": 549, "y": 159, "width": 60, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "13:49", "name": "<PERSON><PERSON>", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "13:50", "name": "Rectangle 642", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "4:125"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 626, "y": 167, "width": 44, "height": 44}, "absoluteRenderBounds": {"x": 618, "y": 159, "width": 60, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.4117647111415863, "g": 0.4117647111415863, "b": 0.4117647111415863, "a": 0.10000000149011612}, "blendMode": "NORMAL", "offset": {"x": 0, "y": 0}, "radius": 5, "spread": 3, "showShadowBehindNode": false}], "interactions": []}, {"id": "13:58", "name": "vuesax/bold/facebook", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "13:59", "name": "facebook", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "13:60", "name": "BG 111", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "opacity": 0.5799999833106995, "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 636, "y": 177, "width": 24, "height": 24}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "13:61", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0117647061124444, "g": 0.35686275362968445, "b": 0.5058823823928833, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "13:66"}, "absoluteBoundingBox": {"x": 638, "y": 179, "width": 20.000003814697266, "height": 20}, "absoluteRenderBounds": {"x": 638, "y": 179, "width": 20, "height": 20}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "13:62", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "opacity": 0, "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 636, "y": 177, "width": 24, "height": 24}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 636, "y": 177, "width": 24, "height": 24}, "absoluteRenderBounds": {"x": 636, "y": 177, "width": 24, "height": 24}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 636, "y": 177, "width": 24, "height": 24}, "absoluteRenderBounds": {"x": 636, "y": 177, "width": 24, "height": 24}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "exportSettings": [{"suffix": "", "format": "SVG", "constraint": {"type": "SCALE", "value": 1}}], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 626, "y": 167, "width": 44, "height": 44}, "absoluteRenderBounds": {"x": 618, "y": 159, "width": 60, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "13:67", "name": "Don’t have an account? Sign up", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:121", "text": "12:127"}, "absoluteBoundingBox": {"x": 525, "y": 266, "width": 177, "height": 17}, "absoluteRenderBounds": {"x": 525.*************, "y": 269.*************, "width": 174.*************, "height": 11.***************}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Don’t have an account? Sign up", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": null, "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2], "styleOverrideTable": {"2": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": null, "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "letterSpacing": 0, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.****************, "b": 0, "a": 1}}], "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%", "inheritFillStyleId": "13:176", "inheritTextStyleId": "12:127"}}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "transitionNodeID": "30:403", "transitionDuration": 1022.09375, "transitionEasing": "GENTLE", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": {"type": "SMART_ANIMATE", "easing": {"type": "GENTLE"}, "duration": 1.0220937728881836}, "preserveScrollPosition": false}]}]}, {"id": "12:139", "name": "Line", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "12:141", "name": "Line 6", "type": "LINE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"fill": "4:124", "stroke": "4:124"}, "absoluteBoundingBox": {"x": 661, "y": 139, "width": 50, "height": 0}, "absoluteRenderBounds": {"x": 661, "y": 138, "width": 50, "height": 1}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "12:140", "name": "Line 5", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": -2.4492937051703357e-16, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"fill": "4:124", "stroke": "4:124"}, "absoluteBoundingBox": {"x": 516, "y": 139, "width": 50, "height": 0}, "absoluteRenderBounds": {"x": 516, "y": 138, "width": 50, "height": 1}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "12:142", "name": "Or Sign in With", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:124", "text": "12:127"}, "absoluteBoundingBox": {"x": 573, "y": 130, "width": 81, "height": 17}, "absoluteRenderBounds": {"x": 573.406982421875, "y": 133.52999877929688, "width": 79.5460205078125, "height": 11.***************}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Or Sign in With", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": null, "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 516, "y": 130, "width": 195, "height": 17}, "absoluteRenderBounds": {"x": 516, "y": 130, "width": 195, "height": 17}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:585", "name": "Input field", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "30:358", "componentProperties": {"Property 1": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I30:585;30:300", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "I30:585;30:301", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}], "children": [{"id": "I30:585;30:298", "name": "Rectangle 6", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1.5, "strokeAlign": "INSIDE", "styles": {"stroke": "4:124"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 456, "y": -178, "width": 315, "height": 55}, "absoluteRenderBounds": {"x": 456, "y": -178, "width": 315, "height": 55}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:585;30:299", "name": "Line 1", "visible": false, "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2823529541492462, "g": 0.2823529541492462, "b": 0.2823529541492462, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"stroke": "4:120"}, "absoluteBoundingBox": {"x": 475, "y": -158, "width": 0, "height": 17}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:585;30:300", "name": "Placeholder", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:124", "text": "12:135"}, "absoluteBoundingBox": {"x": 476, "y": -159, "width": 61, "height": 17}, "absoluteRenderBounds": {"x": 476.84698486328125, "y": -155.37100219726562, "width": 58.65435791015625, "height": 8.4*************5}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "<PERSON><PERSON>", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I30:585;30:301", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:127", "text": "10:210"}, "absoluteBoundingBox": {"x": 456, "y": -204, "width": 38, "height": 21}, "absoluteRenderBounds": {"x": 457.0780029296875, "y": -199.6540069580078, "width": 35.78179931640625, "height": 10.780014038085938}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Email", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 21, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 456, "y": -204, "width": 315, "height": 81}, "absoluteRenderBounds": {"x": 456, "y": -204, "width": 315, "height": 81}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:590", "name": "Input field", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "30:358", "componentProperties": {"Property 1": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I30:590;30:300", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "I30:590;30:301", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}], "children": [{"id": "I30:590;30:298", "name": "Rectangle 6", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1.5, "strokeAlign": "INSIDE", "styles": {"stroke": "4:124"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 456, "y": -67, "width": 315, "height": 55}, "absoluteRenderBounds": {"x": 456, "y": -67, "width": 315, "height": 55}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:590;30:299", "name": "Line 1", "visible": false, "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2823529541492462, "g": 0.2823529541492462, "b": 0.2823529541492462, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"stroke": "4:120"}, "absoluteBoundingBox": {"x": 475, "y": -47, "width": 0, "height": 17}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:590;30:300", "name": "Placeholder", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:124", "text": "12:135"}, "absoluteBoundingBox": {"x": 476, "y": -48, "width": 84, "height": 17}, "absoluteRenderBounds": {"x": 476.84698486328125, "y": -44.13999938964844, "width": 81.74298095703125, "height": 8.238998413085938}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Enter Password", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I30:590;30:301", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:127", "text": "10:210"}, "absoluteBoundingBox": {"x": 456, "y": -93, "width": 107, "height": 21}, "absoluteRenderBounds": {"x": 457.0780029296875, "y": -88.36000061035156, "width": 104.0364990234375, "height": 10.486000061035156}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Enter Password", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 21, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 456, "y": -93, "width": 315, "height": 81}, "absoluteRenderBounds": {"x": 456, "y": -93, "width": 315, "height": 81}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "42:614", "name": "Home Indicator", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "42:609", "componentProperties": {"Property 1": {"value": "Darkmode", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "42:614", "overriddenFields": ["clips<PERSON>ontent", "inheritFillStyleId", "fills"]}], "children": [{"id": "I42:614;42:603", "name": "Line", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "4:127"}, "cornerRadius": 100, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 546, "y": 369, "width": 135, "height": 5}, "absoluteRenderBounds": {"x": 546, "y": 369, "width": 135, "height": 5}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 426, "y": 348, "width": 375, "height": 34}, "absoluteRenderBounds": {"x": 426, "y": 348, "width": 375, "height": 34}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "54:668", "name": "Big Button", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "54:666", "componentProperties": {"Property 1": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I54:668;53:624", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "54:668", "overriddenFields": ["transitionEasing", "transitionDuration", "transitionNodeID"]}], "children": [{"id": "I54:668;53:624", "name": "label", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "9:2", "text": "10:204"}, "absoluteBoundingBox": {"x": 541, "y": 68, "width": 114, "height": 24}, "absoluteRenderBounds": {"x": 571.5816040039062, "y": 73.45600128173828, "width": 52.62664794921875, "height": 16.91199493408203}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Sign In", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-SemiBold", "fontWeight": 600, "textAutoResize": "HEIGHT", "fontSize": 16, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 24, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I54:668;53:625", "name": "Icon/General/Arrow-Right", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "9:9", "overrides": [], "children": [{"id": "I54:668;53:625;139:3256", "name": "Rectangle 1", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "opacity": 0, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "9:2"}, "absoluteBoundingBox": {"x": 666, "y": 70, "width": 20, "height": 20}, "absoluteRenderBounds": null, "preserveRatio": true, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I54:668;53:625;139:3257", "name": "Union", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I54:668;53:625;139:3258", "name": "Stroke 1", "type": "VECTOR", "scrollBehavior": "SCROLLS", "rotation": -1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 669.3333129882812, "y": 79.3541202545166, "width": 13.333174705505371, "height": 1.2916088104248047}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I54:668;53:625;139:3259", "name": "Stroke 3", "type": "VECTOR", "scrollBehavior": "SCROLLS", "rotation": -1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 676.5655517578125, "y": 74.16652297973633, "width": 6.100937843322754, "height": 11.666667938232422}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:2"}, "booleanOperation": "UNION", "absoluteBoundingBox": {"x": 669.3333129882812, "y": 74.16651916503906, "width": 13.333196640014648, "height": 11.666667938232422}, "absoluteRenderBounds": {"x": 669.3333129882812, "y": 74.16651916503906, "width": 13.33319091796875, "height": 11.666671752929688}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 666, "y": 70, "width": 20, "height": 20}, "absoluteRenderBounds": {"x": 666, "y": 70, "width": 20, "height": 20}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0692707896232605, "g": 0.5833333134651184, "b": 0.45995816588401794, "a": 1}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0692707896232605, "g": 0.5833333134651184, "b": 0.45995816588401794, "a": 1}}], "strokes": [], "cornerRadius": 10, "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0692707896232605, "g": 0.5833333134651184, "b": 0.45995816588401794, "a": 1}, "styles": {"fills": "4:148", "fill": "4:148"}, "layoutMode": "HORIZONTAL", "itemSpacing": 11, "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 85, "paddingRight": 85, "paddingTop": 18, "paddingBottom": 18, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 456, "y": 50, "width": 315, "height": 60}, "absoluteRenderBounds": {"x": 456, "y": 50, "width": 315, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "transitionNodeID": "17:80", "transitionDuration": 1022.09375, "transitionEasing": "GENTLE", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": {"type": "SMART_ANIMATE", "easing": {"type": "GENTLE"}, "duration": 1.0220937728881836}, "preserveScrollPosition": false}]}]}, {"id": "13:71", "name": "Status Bar", "type": "INSTANCE", "isFixed": true, "scrollBehavior": "FIXED", "componentId": "9:152", "componentProperties": {"Property 1": {"value": "Light Mode", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I13:71;128:320", "overriddenFields": ["italic", "fontPostScriptName", "fontWeight", "fontFamily"]}], "children": [{"id": "I13:71;128:302", "name": "Symbols", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I13:71;128:303", "name": "Battery", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I13:71;128:304", "name": "Rectangle", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I13:71;128:305", "name": "Exclude", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I13:71;128:306", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.36000001430511475, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 763, "y": -411.8399963378906, "width": 20, "height": 9.5}, "absoluteRenderBounds": null, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "I13:71;128:307", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.36000001430511475, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 762, "y": -412.8399963378906, "width": 22, "height": 11.5}, "absoluteRenderBounds": null, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "booleanOperation": "EXCLUDE", "absoluteBoundingBox": {"x": 762, "y": -412.8399963378906, "width": 22, "height": 11.5}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "I13:71;128:308", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.36000001430511475, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 785, "y": -409.1499938964844, "width": 1.5, "height": 4}, "absoluteRenderBounds": null, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "opacity": 0.4000000059604645, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "booleanOperation": "UNION", "absoluteBoundingBox": {"x": 762, "y": -412.8399963378906, "width": 24.5, "height": 11.5}, "absoluteRenderBounds": {"x": 762, "y": -412.8399963378906, "width": 24.5, "height": 11.5}, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "I13:71;128:309", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 0.2980392277240753, "g": 0.8509804010391235, "b": 0.3921568691730499, "a": 1}}], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "cornerRadius": 1.600000023841858, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 764, "y": -410.92333984375, "width": 18, "height": 7.666666507720947}, "absoluteRenderBounds": {"x": 764, "y": -410.92333984375, "width": 18, "height": 7.666656494140625}, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 762, "y": -412.8399963378906, "width": 24.5, "height": 11.5}, "absoluteRenderBounds": {"x": 762, "y": -412.8399963378906, "width": 24.5, "height": 11.5}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "I13:71;128:310", "name": "Combined Shape", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I13:71;128:311", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "cornerRadius": 1.2000000476837158, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 719.5, "y": -405.6999816894531, "width": 3, "height": 4}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I13:71;128:312", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "cornerRadius": 1.2000000476837158, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 724.2999877929688, "y": -407.6999816894531, "width": 3, "height": 6}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I13:71;128:313", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "cornerRadius": 1.2000000476837158, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 728.9000244140625, "y": -410, "width": 3, "height": 8.300003051757812}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I13:71;128:314", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "cornerRadius": 1.2000000476837158, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 733.5999755859375, "y": -412.3999938964844, "width": 3, "height": 10.699999809265137}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "booleanOperation": "UNION", "absoluteBoundingBox": {"x": 719.5, "y": -412.3999938964844, "width": 17.099990844726562, "height": 10.700004577636719}, "absoluteRenderBounds": {"x": 719.5, "y": -412.3999938964844, "width": 17.0999755859375, "height": 10.*************5}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I13:71;128:315", "name": "Wi-Fi", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I13:71;128:316", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 741.5999755859375, "y": -412.6000061035156, "width": 15.399999618530273, "height": 4.782877445220947}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I13:71;128:317", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 744.2855224609375, "y": -408.7742614746094, "width": 10.03215217590332, "height": 3.664349317550659}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I13:71;128:318", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 746.9678344726562, "y": -404.9454040527344, "width": 4.664956569671631, "height": 3.40260648727417}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "booleanOperation": "EXCLUDE", "absoluteBoundingBox": {"x": 741.5999755859375, "y": -412.6000061035156, "width": 15.399999618530273, "height": 11.057209968566895}, "absoluteRenderBounds": {"x": 741.5999755859375, "y": -412.6000061035156, "width": 15.4000244140625, "height": 11.057220458984375}, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 719.5, "y": -413.3999938964844, "width": 68, "height": 13}, "absoluteRenderBounds": {"x": 719.5, "y": -413.3999938964844, "width": 68, "height": 13}, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "I13:71;128:319", "name": "Bars/Status/_/Time", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I13:71;128:320", "name": "↳ Time", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "absoluteBoundingBox": {"x": 455.5, "y": -416, "width": 37, "height": 18}, "absoluteRenderBounds": {"x": 456.19580078125, "y": -412.77392578125, "width": 35.074859619140625, "height": 10.9716796875}, "constraints": {"vertical": "CENTER", "horizontal": "LEFT"}, "characters": "19:27", "style": {"fontFamily": "SF Pro Display", "fontPostScriptName": "SFProDisplay-Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 15, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": -0.16500000655651093, "lineHeightPx": 17.900390625, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 426, "y": -418, "width": 180, "height": 22}, "absoluteRenderBounds": {"x": 426, "y": -418, "width": 180, "height": 22}, "constraints": {"vertical": "CENTER", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 426, "y": -430, "width": 375, "height": 44}, "absoluteRenderBounds": {"x": 426, "y": -430, "width": 375, "height": 44}, "constraints": {"vertical": "TOP", "horizontal": "LEFT_RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "cornerRadius": 30, "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 1, "g": 1, "b": 1, "a": 1}, "styles": {"fills": "4:125", "fill": "4:125"}, "layoutGrids": [{"pattern": "COLUMNS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.10000000149011612}, "alignment": "MAX", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "COLUMNS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MIN", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "ROWS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MIN", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "ROWS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MAX", "gutterSize": 20, "offset": 0, "count": 1}], "absoluteBoundingBox": {"x": 426, "y": -430, "width": 375, "height": 812}, "absoluteRenderBounds": {"x": 426, "y": -430, "width": 375, "height": 812}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, "imageUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/56f488c8-7597-49a8-9e76-14db8f233ad3", "thumbnailUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/fea6aa22-aa63-4625-92f4-4788f8580b0d"}, {"frame_id": "30:403", "file_key": "OTCSlNZllDbCEZ99hDWrbL", "json_data": {"id": "30:403", "name": "Sign up", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "30:404", "name": "Titttle", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "30:405", "name": "Create an account", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:121", "text": "10:202"}, "absoluteBoundingBox": {"x": 871, "y": -376, "width": 191, "height": 30}, "absoluteRenderBounds": {"x": 871.*************, "y": -368.*************, "width": 188.**************, "height": 14.**************}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Create an account", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 20, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 30, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "30:406", "name": "Let’s help you set up your account, it won’t take long.", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:127", "text": "12:135"}, "absoluteBoundingBox": {"x": 871, "y": -341, "width": 195, "height": 34}, "absoluteRenderBounds": {"x": 871.*************, "y": -337.*************, "width": 188.************, "height": 28.***************}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Let’s help you set up your account, it won’t take long.", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 871, "y": -376, "width": 195, "height": 69}, "absoluteRenderBounds": {"x": 871, "y": -376, "width": 195, "height": 69}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:425", "name": "Forgot Password", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "30:426", "name": "Accept terms & Condition", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.****************, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "13:176", "text": "12:135"}, "absoluteBoundingBox": {"x": 903, "y": 117, "width": 142, "height": 17}, "absoluteRenderBounds": {"x": 903.6256713867188, "y": 120.62899780273438, "width": 140.32354736328125, "height": 11.231002807617188}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Accept terms & Condition", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "30:427", "name": "Rectangle 6", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.****************, "b": 0, "a": 1}}], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "4:125", "stroke": "13:176"}, "cornerRadius": 5, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 881, "y": 117, "width": 17, "height": 17}, "absoluteRenderBounds": {"x": 881, "y": 117, "width": 17, "height": 17}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 5, "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 881, "y": 117, "width": 164, "height": 17}, "absoluteRenderBounds": {"x": 881, "y": 117, "width": 164, "height": 17}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:430", "name": "Ellipse 1", "visible": false, "type": "ELLIPSE", "locked": true, "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.****************, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 841, "y": -473, "width": 308, "height": 106}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "LAYER_BLUR", "visible": true, "radius": 500}], "arcData": {"startingAngle": 0, "endingAngle": 6.2831854820251465, "innerRadius": 0}, "interactions": []}, {"id": "30:431", "name": "<PERSON><PERSON>", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "30:432", "name": "Rectangle 642", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "4:125"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 972, "y": 271, "width": 44, "height": 44}, "absoluteRenderBounds": {"x": 964, "y": 263, "width": 60, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.4117647111415863, "g": 0.4117647111415863, "b": 0.4117647111415863, "a": 0.10000000149011612}, "blendMode": "NORMAL", "offset": {"x": 0, "y": 0}, "radius": 5, "spread": 3, "showShadowBehindNode": false}], "interactions": []}, {"id": "30:434", "name": "icons8-google-240 1", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "13:48", "overrides": [], "children": [{"id": "I30:434;13:39", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.7568627595901489, "b": 0.027450980618596077, "a": 1}}], "strokes": [], "strokeWeight": 5, "strokeAlign": "INSIDE", "styles": {"fill": "13:175"}, "absoluteBoundingBox": {"x": 984, "y": 283, "width": 19.999996185302734, "height": 19.999996185302734}, "absoluteRenderBounds": {"x": 984, "y": 283, "width": 20, "height": 20}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:434;13:40", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.239215686917305, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 5, "strokeAlign": "INSIDE", "styles": {"fill": "13:173"}, "absoluteBoundingBox": {"x": 985.1530151367188, "y": 283, "width": 15.655999183654785, "height": 7.755001068115234}, "absoluteRenderBounds": {"x": 985.1530151367188, "y": 283, "width": 15.656005859375, "height": 7.7550048828125}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:434;13:41", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2980392277240753, "g": 0.686274528503418, "b": 0.3137255012989044, "a": 1}}], "strokes": [], "strokeWeight": 5, "strokeAlign": "INSIDE", "styles": {"fill": "13:171"}, "absoluteBoundingBox": {"x": 985.0974731445312, "y": 295.0270080566406, "width": 15.607002258300781, "height": 7.972998142242432}, "absoluteRenderBounds": {"x": 985.0974731445312, "y": 295.0270080566406, "width": 15.60699462890625, "height": 7.972991943359375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:434;13:42", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.09803921729326248, "g": 0.4627451002597809, "b": 0.8235294222831726, "a": 1}}], "strokes": [], "strokeWeight": 5, "strokeAlign": "INSIDE", "styles": {"fill": "13:169"}, "absoluteBoundingBox": {"x": 994, "y": 291, "width": 9.999998092651367, "height": 9.4141206741333}, "absoluteRenderBounds": {"x": 994, "y": 291, "width": 10, "height": 9.41412353515625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 982, "y": 281, "width": 24, "height": 24}, "absoluteRenderBounds": {"x": 982, "y": 281, "width": 24, "height": 24}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 972, "y": 271, "width": 44, "height": 44}, "absoluteRenderBounds": {"x": 964, "y": 263, "width": 60, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:435", "name": "<PERSON><PERSON>", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "30:436", "name": "Rectangle 642", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "4:125"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 1041, "y": 271, "width": 44, "height": 44}, "absoluteRenderBounds": {"x": 1033, "y": 263, "width": 60, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.4117647111415863, "g": 0.4117647111415863, "b": 0.4117647111415863, "a": 0.10000000149011612}, "blendMode": "NORMAL", "offset": {"x": 0, "y": 0}, "radius": 5, "spread": 3, "showShadowBehindNode": false}], "interactions": []}, {"id": "30:438", "name": "vuesax/bold/facebook", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "30:439", "name": "facebook", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "30:440", "name": "BG 111", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "opacity": 0.5799999833106995, "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1051, "y": 281, "width": 24, "height": 24}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "30:441", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0117647061124444, "g": 0.35686275362968445, "b": 0.5058823823928833, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "13:66"}, "absoluteBoundingBox": {"x": 1053, "y": 283, "width": 20.000003814697266, "height": 20}, "absoluteRenderBounds": {"x": 1053, "y": 283, "width": 20, "height": 20}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "30:442", "name": "Vector", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "opacity": 0, "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1051, "y": 281, "width": 24, "height": 24}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1051, "y": 281, "width": 24, "height": 24}, "absoluteRenderBounds": {"x": 1051, "y": 281, "width": 24, "height": 24}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1051, "y": 281, "width": 24, "height": 24}, "absoluteRenderBounds": {"x": 1051, "y": 281, "width": 24, "height": 24}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "exportSettings": [{"suffix": "", "format": "SVG", "constraint": {"type": "SCALE", "value": 1}}], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1041, "y": 271, "width": 44, "height": 44}, "absoluteRenderBounds": {"x": 1033, "y": 263, "width": 60, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:443", "name": "Already a member? Sign In", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:121", "text": "12:127"}, "absoluteBoundingBox": {"x": 954, "y": 335, "width": 150, "height": 17}, "absoluteRenderBounds": {"x": 954.3740234375, "y": 338.*************, "width": 148.8446044921875, "height": 11.***************}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Already a member? Sign In", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": null, "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2], "styleOverrideTable": {"2": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": null, "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "letterSpacing": 0, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 0.****************, "b": 0, "a": 1}}], "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%", "inheritFillStyleId": "13:176", "inheritTextStyleId": "12:127"}}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "transitionNodeID": "11:235", "transitionDuration": 1022.09375, "transitionEasing": "GENTLE", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": "11:235", "navigation": "NAVIGATE", "transition": {"type": "SMART_ANIMATE", "easing": {"type": "GENTLE"}, "duration": 1.0220937728881836}, "preserveScrollPosition": false}]}]}, {"id": "30:444", "name": "Line", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "30:445", "name": "Line 6", "type": "LINE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"fill": "4:124", "stroke": "4:124"}, "absoluteBoundingBox": {"x": 1076, "y": 243, "width": 50, "height": 0}, "absoluteRenderBounds": {"x": 1076, "y": 242, "width": 50, "height": 1}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:446", "name": "Line 5", "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": -2.4492937051703357e-16, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"fill": "4:124", "stroke": "4:124"}, "absoluteBoundingBox": {"x": 931, "y": 243, "width": 50, "height": 0}, "absoluteRenderBounds": {"x": 931, "y": 242, "width": 50, "height": 1}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:447", "name": "Or Sign in With", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:124", "text": "12:127"}, "absoluteBoundingBox": {"x": 988, "y": 234, "width": 81, "height": 17}, "absoluteRenderBounds": {"x": 988.406982421875, "y": 237.52999877929688, "width": 79.5460205078125, "height": 11.***************}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Or Sign in With", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": null, "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 931, "y": 234, "width": 195, "height": 17}, "absoluteRenderBounds": {"x": 931, "y": 234, "width": 195, "height": 17}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:484", "name": "Input field", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "30:358", "componentProperties": {"Property 1": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I30:484;30:300", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "I30:484;30:301", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}], "children": [{"id": "I30:484;30:298", "name": "Rectangle 6", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1.5, "strokeAlign": "INSIDE", "styles": {"stroke": "4:124"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 871, "y": -261, "width": 315, "height": 55}, "absoluteRenderBounds": {"x": 871, "y": -261, "width": 315, "height": 55}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:484;30:299", "name": "Line 1", "visible": false, "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2823529541492462, "g": 0.2823529541492462, "b": 0.2823529541492462, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"stroke": "4:120"}, "absoluteBoundingBox": {"x": 890, "y": -241, "width": 0, "height": 17}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:484;30:300", "name": "Placeholder", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:124", "text": "12:135"}, "absoluteBoundingBox": {"x": 891, "y": -242, "width": 64, "height": 17}, "absoluteRenderBounds": {"x": 891.8469848632812, "y": -237.67799377441406, "width": 62.541015625, "height": 7.7769927978515625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Enter Name", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I30:484;30:301", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:127", "text": "10:210"}, "absoluteBoundingBox": {"x": 871, "y": -287, "width": 43, "height": 21}, "absoluteRenderBounds": {"x": 872.0780029296875, "y": -281.7720031738281, "width": 40.728515625, "height": 9.89801025390625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Name", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 21, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 871, "y": -287, "width": 315, "height": 81}, "absoluteRenderBounds": {"x": 871, "y": -287, "width": 315, "height": 81}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:489", "name": "Input field", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "30:358", "componentProperties": {"Property 1": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I30:489;30:300", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "I30:489;30:301", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}], "children": [{"id": "I30:489;30:298", "name": "Rectangle 6", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1.5, "strokeAlign": "INSIDE", "styles": {"stroke": "4:124"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 871, "y": -160, "width": 315, "height": 55}, "absoluteRenderBounds": {"x": 871, "y": -160, "width": 315, "height": 55}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:489;30:299", "name": "Line 1", "visible": false, "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2823529541492462, "g": 0.2823529541492462, "b": 0.2823529541492462, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"stroke": "4:120"}, "absoluteBoundingBox": {"x": 890, "y": -140, "width": 0, "height": 17}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:489;30:300", "name": "Placeholder", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:124", "text": "12:135"}, "absoluteBoundingBox": {"x": 891, "y": -141, "width": 61, "height": 17}, "absoluteRenderBounds": {"x": 891.8469848632812, "y": -137.37100219726562, "width": 58.65435791015625, "height": 8.4*************5}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "<PERSON><PERSON>", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I30:489;30:301", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:127", "text": "10:210"}, "absoluteBoundingBox": {"x": 871, "y": -186, "width": 38, "height": 21}, "absoluteRenderBounds": {"x": 872.0780029296875, "y": -181.6540069580078, "width": 35.78179931640625, "height": 10.780014038085938}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Email", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 21, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 871, "y": -186, "width": 315, "height": 81}, "absoluteRenderBounds": {"x": 871, "y": -186, "width": 315, "height": 81}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:494", "name": "Input field", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "30:358", "componentProperties": {"Property 1": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I30:494;30:300", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "I30:494;30:301", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}], "children": [{"id": "I30:494;30:298", "name": "Rectangle 6", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1.5, "strokeAlign": "INSIDE", "styles": {"stroke": "4:124"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 871, "y": -59, "width": 315, "height": 55}, "absoluteRenderBounds": {"x": 871, "y": -59, "width": 315, "height": 55}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:494;30:299", "name": "Line 1", "visible": false, "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2823529541492462, "g": 0.2823529541492462, "b": 0.2823529541492462, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"stroke": "4:120"}, "absoluteBoundingBox": {"x": 890, "y": -39, "width": 0, "height": 17}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:494;30:300", "name": "Placeholder", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:124", "text": "12:135"}, "absoluteBoundingBox": {"x": 891, "y": -40, "width": 84, "height": 17}, "absoluteRenderBounds": {"x": 891.8469848632812, "y": -36.13999938964844, "width": 81.74298095703125, "height": 8.23900032043457}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "<PERSON><PERSON>", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I30:494;30:301", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:127", "text": "10:210"}, "absoluteBoundingBox": {"x": 871, "y": -85, "width": 68, "height": 21}, "absoluteRenderBounds": {"x": 872.0780029296875, "y": -80.36000061035156, "width": 65.1673583984375, "height": 10.486000061035156}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Password", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 21, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 871, "y": -85, "width": 315, "height": 81}, "absoluteRenderBounds": {"x": 871, "y": -85, "width": 315, "height": 81}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "30:499", "name": "Input field", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "30:358", "componentProperties": {"Property 1": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I30:499;30:300", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "I30:499;30:301", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}], "children": [{"id": "I30:499;30:298", "name": "Rectangle 6", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokeWeight": 1.5, "strokeAlign": "INSIDE", "styles": {"stroke": "4:124"}, "cornerRadius": 10, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 871, "y": 42, "width": 315, "height": 55}, "absoluteRenderBounds": {"x": 871, "y": 42, "width": 315, "height": 55}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:499;30:299", "name": "Line 1", "visible": false, "type": "LINE", "scrollBehavior": "SCROLLS", "rotation": 1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2823529541492462, "g": 0.2823529541492462, "b": 0.2823529541492462, "a": 1}}], "strokeWeight": 1, "strokeAlign": "CENTER", "strokeCap": "ROUND", "styles": {"stroke": "4:120"}, "absoluteBoundingBox": {"x": 890, "y": 62, "width": 0, "height": 17}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:499;30:300", "name": "Placeholder", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:124", "text": "12:135"}, "absoluteBoundingBox": {"x": 891, "y": 61, "width": 94, "height": 17}, "absoluteRenderBounds": {"x": 891.8469848632812, "y": 64.86000061035156, "width": 92.10919189453125, "height": 11}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Retype Password", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 11, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 16.5, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I30:499;30:301", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "4:127", "text": "10:210"}, "absoluteBoundingBox": {"x": 871, "y": 16, "width": 128, "height": 21}, "absoluteRenderBounds": {"x": 871.6019897460938, "y": 20.303998947143555, "width": 125.79962158203125, "height": 10.822000503540039}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "Confirm Password", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 21, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 871, "y": 16, "width": 315, "height": 81}, "absoluteRenderBounds": {"x": 871, "y": 16, "width": 315, "height": 81}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "42:617", "name": "Home Indicator", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "42:609", "componentProperties": {"Property 1": {"value": "Darkmode", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "42:617", "overriddenFields": ["clips<PERSON>ontent", "inheritFillStyleId", "fills"]}], "children": [{"id": "I42:617;42:603", "name": "Line", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "4:127"}, "cornerRadius": 100, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 961, "y": 369, "width": 135, "height": 5}, "absoluteRenderBounds": {"x": 961, "y": 369, "width": 135, "height": 5}, "constraints": {"vertical": "CENTER", "horizontal": "CENTER"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 841, "y": 348, "width": 375, "height": 34}, "absoluteRenderBounds": {"x": 841, "y": 348, "width": 375, "height": 34}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "54:679", "name": "Big Button", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "54:666", "componentProperties": {"Property 1": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I54:679;53:624", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "54:679", "overriddenFields": ["transitionEasing", "transitionDuration", "transitionNodeID"]}], "children": [{"id": "I54:679;53:624", "name": "label", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "9:2", "text": "10:204"}, "absoluteBoundingBox": {"x": 956, "y": 178, "width": 114, "height": 24}, "absoluteRenderBounds": {"x": 983.0972290039062, "y": 183.45599365234375, "width": 60.09771728515625, "height": 16.912002563476562}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Sign Up", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Poppins-SemiBold", "fontWeight": 600, "textAutoResize": "HEIGHT", "fontSize": 16, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0, "lineHeightPx": 24, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I54:679;53:625", "name": "Icon/General/Arrow-Right", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "9:9", "overrides": [], "children": [{"id": "I54:679;53:625;139:3256", "name": "Rectangle 1", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "opacity": 0, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "styles": {"fill": "9:2"}, "absoluteBoundingBox": {"x": 1081, "y": 180, "width": 20, "height": 20}, "absoluteRenderBounds": null, "preserveRatio": true, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I54:679;53:625;139:3257", "name": "Union", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I54:679;53:625;139:3258", "name": "Stroke 1", "type": "VECTOR", "scrollBehavior": "SCROLLS", "rotation": -1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1084.3333740234375, "y": 189.35412788391113, "width": 13.333174705505371, "height": 1.2916088104248047}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I54:679;53:625;139:3259", "name": "Stroke 3", "type": "VECTOR", "scrollBehavior": "SCROLLS", "rotation": -1.5707963267948966, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1091.565673828125, "y": 184.16652297973633, "width": 6.100937843322754, "height": 11.666667938232422}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:2"}, "booleanOperation": "UNION", "absoluteBoundingBox": {"x": 1084.3333740234375, "y": 184.16651916503906, "width": 13.333196640014648, "height": 11.666667938232422}, "absoluteRenderBounds": {"x": 1084.3333740234375, "y": 184.16651916503906, "width": 13.333251953125, "height": 11.666671752929688}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1081, "y": 180, "width": 20, "height": 20}, "absoluteRenderBounds": {"x": 1081, "y": 180, "width": 20, "height": 20}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "layoutAlign": "INHERIT", "layoutGrow": 0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0692707896232605, "g": 0.5833333134651184, "b": 0.45995816588401794, "a": 1}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0692707896232605, "g": 0.5833333134651184, "b": 0.45995816588401794, "a": 1}}], "strokes": [], "cornerRadius": 10, "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0692707896232605, "g": 0.5833333134651184, "b": 0.45995816588401794, "a": 1}, "styles": {"fills": "4:148", "fill": "4:148"}, "layoutMode": "HORIZONTAL", "itemSpacing": 11, "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 85, "paddingRight": 85, "paddingTop": 18, "paddingBottom": 18, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 871, "y": 160, "width": 315, "height": 60}, "absoluteRenderBounds": {"x": 871, "y": 160, "width": 315, "height": 60}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "transitionNodeID": "17:80", "transitionDuration": 1022.09375, "transitionEasing": "GENTLE", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": {"type": "SMART_ANIMATE", "easing": {"type": "GENTLE"}, "duration": 1.0220937728881836}, "preserveScrollPosition": false}]}]}, {"id": "30:449", "name": "Status Bar", "type": "INSTANCE", "isFixed": true, "scrollBehavior": "FIXED", "componentId": "9:152", "componentProperties": {"Property 1": {"value": "Light Mode", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I30:449;128:320", "overriddenFields": ["italic", "fontPostScriptName", "fontWeight", "fontFamily"]}], "children": [{"id": "I30:449;128:302", "name": "Symbols", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I30:449;128:303", "name": "Battery", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I30:449;128:304", "name": "Rectangle", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I30:449;128:305", "name": "Exclude", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I30:449;128:306", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.36000001430511475, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1178, "y": -411.8399963378906, "width": 20, "height": 9.5}, "absoluteRenderBounds": null, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "I30:449;128:307", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.36000001430511475, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1177, "y": -412.8399963378906, "width": 22, "height": 11.5}, "absoluteRenderBounds": null, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "booleanOperation": "EXCLUDE", "absoluteBoundingBox": {"x": 1177, "y": -412.8399963378906, "width": 22, "height": 11.5}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "I30:449;128:308", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.36000001430511475, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1200, "y": -409.1499938964844, "width": 1.5, "height": 4}, "absoluteRenderBounds": null, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "opacity": 0.4000000059604645, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "booleanOperation": "UNION", "absoluteBoundingBox": {"x": 1177, "y": -412.8399963378906, "width": 24.5, "height": 11.5}, "absoluteRenderBounds": {"x": 1177, "y": -412.8399963378906, "width": 24.5, "height": 11.5}, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "I30:449;128:309", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 0.2980392277240753, "g": 0.8509804010391235, "b": 0.3921568691730499, "a": 1}}], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "cornerRadius": 1.600000023841858, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 1179, "y": -410.92333984375, "width": 18, "height": 7.666666507720947}, "absoluteRenderBounds": {"x": 1179, "y": -410.92333984375, "width": 18, "height": 7.666656494140625}, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1177, "y": -412.8399963378906, "width": 24.5, "height": 11.5}, "absoluteRenderBounds": {"x": 1177, "y": -412.8399963378906, "width": 24.5, "height": 11.5}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "I30:449;128:310", "name": "Combined Shape", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I30:449;128:311", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "cornerRadius": 1.2000000476837158, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 1134.5, "y": -405.6999816894531, "width": 3, "height": 4}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:449;128:312", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "cornerRadius": 1.2000000476837158, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 1139.300048828125, "y": -407.6999816894531, "width": 3, "height": 6}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:449;128:313", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "cornerRadius": 1.2000000476837158, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 1143.9000244140625, "y": -410, "width": 3, "height": 8.300003051757812}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:449;128:314", "name": "Rectangle", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "cornerRadius": 1.2000000476837158, "cornerSmoothing": 0.6000000238418579, "absoluteBoundingBox": {"x": 1148.5999755859375, "y": -412.3999938964844, "width": 3, "height": 10.699999809265137}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "booleanOperation": "UNION", "absoluteBoundingBox": {"x": 1134.5, "y": -412.3999938964844, "width": 17.099990844726562, "height": 10.700004577636719}, "absoluteRenderBounds": {"x": 1134.5, "y": -412.3999938964844, "width": 17.0999755859375, "height": 10.*************5}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:449;128:315", "name": "Wi-Fi", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "I30:449;128:316", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1156.5999755859375, "y": -412.6000061035156, "width": 15.399999618530273, "height": 4.782877445220947}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:449;128:317", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1159.2855224609375, "y": -408.7742614746094, "width": 10.03215217590332, "height": 3.664349317550659}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "I30:449;128:318", "name": "Path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0, "g": 0, "b": 0, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1161.9677734375, "y": -404.9454040527344, "width": 4.664956569671631, "height": 3.40260648727417}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "booleanOperation": "EXCLUDE", "absoluteBoundingBox": {"x": 1156.5999755859375, "y": -412.6000061035156, "width": 15.399999618530273, "height": 11.057209968566895}, "absoluteRenderBounds": {"x": 1156.5999755859375, "y": -412.6000061035156, "width": 15.4000244140625, "height": 11.057220458984375}, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1134.5, "y": -413.3999938964844, "width": 68, "height": 13}, "absoluteRenderBounds": {"x": 1134.5, "y": -413.3999938964844, "width": 68, "height": 13}, "constraints": {"vertical": "CENTER", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "I30:449;128:319", "name": "Bars/Status/_/Time", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I30:449;128:320", "name": "↳ Time", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1882352977991104, "g": 0.1882352977991104, "b": 0.1882352977991104, "a": 1}}], "strokes": [], "strokeWeight": 0, "strokeAlign": "CENTER", "styles": {"fill": "9:8"}, "absoluteBoundingBox": {"x": 870.5, "y": -416, "width": 37, "height": 18}, "absoluteRenderBounds": {"x": 871.19580078125, "y": -412.77392578125, "width": 35.07489013671875, "height": 10.9716796875}, "constraints": {"vertical": "CENTER", "horizontal": "LEFT"}, "characters": "19:27", "style": {"fontFamily": "SF Pro Display", "fontPostScriptName": "SFProDisplay-Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 15, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": -0.16500000655651093, "lineHeightPx": 17.900390625, "lineHeightPercent": 100, "lineHeightUnit": "INTRINSIC_%"}, "layoutVersion": 3, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 841, "y": -418, "width": 180, "height": 22}, "absoluteRenderBounds": {"x": 841, "y": -418, "width": 180, "height": 22}, "constraints": {"vertical": "CENTER", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 841, "y": -430, "width": 375, "height": 44}, "absoluteRenderBounds": {"x": 841, "y": -430, "width": 375, "height": 44}, "constraints": {"vertical": "TOP", "horizontal": "LEFT_RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "cornerRadius": 30, "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 1, "g": 1, "b": 1, "a": 1}, "styles": {"fills": "4:125", "fill": "4:125"}, "layoutGrids": [{"pattern": "COLUMNS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.10000000149011612}, "alignment": "MAX", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "COLUMNS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MIN", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "ROWS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MIN", "gutterSize": 20, "offset": 0, "count": 1}, {"pattern": "ROWS", "sectionSize": 30, "visible": false, "color": {"r": 1, "g": 0, "b": 0, "a": 0.20000000298023224}, "alignment": "MAX", "gutterSize": 20, "offset": 0, "count": 1}], "absoluteBoundingBox": {"x": 841, "y": -430, "width": 375, "height": 812}, "absoluteRenderBounds": {"x": 841, "y": -430, "width": 375, "height": 812}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, "imageUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/703c4b8d-f1df-48a2-a8dc-c31a4cabc0a4", "thumbnailUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/f6e25953-37b8-4ca1-a9b1-750ea85786ea"}], "design": {"classdiagram_state": "configured", "DataManagement": "Data Storage Requirements:\n- User credentials (hashed passwords) stored in MongoDB.\n- Session tokens stored in memory or a distributed cache.\n\nData Flow:\n1. User submits login credentials.\n2. Credentials are validated against the database.\n3. On success, access and refresh tokens are generated and returned to the user.", "Description": "The test suite for the Order Processing System component includes comprehensive test cases across various types of testing to ensure the functionality, integration, performance, and robustness of the system.", "Interfaces": "1. REST API for user authentication:\n   - POST /login: Authenticates user and returns tokens.\n   - POST /logout: Invalidates user session.\n   - POST /register: Creates a new user account.\n   - POST /recover-password: Initiates password recovery process.\n\nAPI Specifications:\n- Content-Type: application/json\n- Accept: application/json", "InputsAndOutputs": "Inputs:\n- <PERSON>rna<PERSON> (String): The unique identifier for the user.\n- Password (String): The user's password for authentication.\n- Refresh <PERSON>ken (String): A token used to obtain a new access token.\n- Session ID (String): Identifier for the user session.\n\nOutputs:\n- Access Token (String): A token that grants access to protected resources.\n- Refresh Token (String): A token used to refresh the access token.\n- User Profile (Object): Contains user details such as username, email, and roles.\n\nData Formats:\n- JSON for data exchange.\n- Strings for tokens and identifiers.", "Dependencies": "1. User Management Component: Provides user data and profile information.\n2. Database (MongoDB): Stores user credentials and session data.\n3. External Authentication Providers: Supports OAuth 2.0 for third-party logins.", "Title": "Design for User Authentication Sub-Component", "configuration_state": "configured", "test_cases_config_state": "configured", "behavior_state": "configured", "behavior_config_state": "configured", "component_interactions_state": "configured", "FunctionalRequirements": "1. User Registration: Allows new users to create an account.\n2. User Login: Authenticates users and provides access tokens.\n3. User Logout: Invalidates user sessions and tokens.\n4. Session Management: Maintains user sessions across devices.\n5. Password Recovery: Allows users to reset their passwords securely.\n6. Rate Limiting: Prevents abuse by limiting login attempts.\n7. Account Lockout: Temporarily locks accounts after multiple failed login attempts.", "NonFunctionalRequirements": "1. Performance: The system should handle up to 1000 concurrent users with a response time of less than 2 seconds.\n2. Scalability: The architecture should support horizontal scaling to accommodate increased user load.\n3. Security: Implement encryption for sensitive data, use HTTPS, and follow OWASP security guidelines.\n4. Usability: The login and registration processes should be user-friendly and accessible.", "TestingConsiderations": "Key Areas for Testing:\n1. Authentication flow (login, logout, registration).\n2. Session management (token expiration, refresh).\n3. Security testing (vulnerability assessments).\n\nSpecific Testing Requirements:\n- Automated tests for all API endpoints.\n- Load testing to ensure performance under high traffic.", "specification_state": "configured", "ErrorHandling": "Potential Error Scenarios:\n1. Invalid credentials: Return 401 Unauthorized.\n2. Account locked: Return 403 Forbidden.\n3. Session expired: Return 401 Unauthorized.\n\nError Handling Strategies:\n- Provide clear error messages to users.\n- Log errors for monitoring and debugging purposes.", "LinkedFigmaFrames": "[{\"id\": \"17:80\", \"name\": \"Home\", \"file_key\": \"OTCSlNZllDbCEZ99hDWrbL\", \"imageUrl\": \"https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/a1da6501-2e00-4991-b4c9-3a0ce19fa5de\", \"thumbnailUrl\": \"https://api.figma.com/v1/images/OTCSlNZllDbCEZ99hDWrbL?ids=17:80&scale=0.5&format=png\"}, {\"id\": \"11:235\", \"name\": \"Sign In\", \"file_key\": \"OTCSlNZllDbCEZ99hDWrbL\", \"imageUrl\": \"https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/56f488c8-7597-49a8-9e76-14db8f233ad3\", \"thumbnailUrl\": \"https://api.figma.com/v1/images/OTCSlNZllDbCEZ99hDWrbL?ids=11:235&scale=0.5&format=png\"}, {\"id\": \"30:403\", \"name\": \"Sign up\", \"file_key\": \"OTCSlNZllDbCEZ99hDWrbL\", \"imageUrl\": \"https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/703c4b8d-f1df-48a2-a8dc-c31a4cabc0a4\", \"thumbnailUrl\": \"https://api.figma.com/v1/images/OTCSlNZllDbCEZ99hDWrbL?ids=30:403&scale=0.5&format=png\"}]", "component_interactions_config_state": "configured", "class_diagrams_config_state": "configured", "testcases_state": "configured"}, "ClassDiagram": ["{\"Type\": \"ClassDiagram\", \"Title\": \"Order Processing System\", \"Diagram\": \"classDiagram\\nclass Order {\\n+int orderId\\n+String customerName\\n+Date orderDate\\n+validate()\\n+process()\\n}\\nclass Payment {\\n+int paymentId\\n+double amount\\n+processPayment()\\n}\\nclass Shipping {\\n+int shippingId\\n+String address\\n+shipOrder()\\n}\\nOrder \\\"1\\\" -- \\\"1\\\" Payment: has\\nOrder \\\"1\\\" -- \\\"1\\\" Shipping: requires\\n\"}"], "Algorithm": ["{\"Type\": \"Algorithm\", \"Details\": \"1. Receive user data (username, password).\\n2. Validate input data (check for existing username, password strength).\\n3. Hash the password using a secure hashing algorithm.\\n4. Store the user data in MongoDB.\\n5. Return success message or error.\", \"Title\": \"User Registration\"}", "{\"Type\": \"Algorithm\", \"Details\": \"1. Receive login credentials (username, password).\\n2. Validate input data.\\n3. Retrieve user data from MongoDB.\\n4. Compare hashed password with stored hash.\\n5. If valid, generate access and refresh tokens.\\n6. Return tokens and user profile.\", \"Title\": \"User Login\"}", "{\"Type\": \"Algorithm\", \"Details\": \"1. On user login, create a session entry in memory or distributed cache.\\n2. Store access and refresh tokens with expiration times.\\n3. On each request, validate session tokens.\\n4. If token is expired, return error and prompt for refresh.\", \"Title\": \"Session Management\"}", "{\"Type\": \"Algorithm\", \"Details\": \"1. Receive request for password recovery (username).\\n2. Validate username and send recovery email with token.\\n3. On token validation, allow user to set a new password.\\n4. Hash the new password and update in MongoDB.\", \"Title\": \"Password Recovery\"}", "{\"Type\": \"Algorithm\", \"Details\": \"1. Track login attempts per user.\\n2. If attempts exceed threshold, temporarily lock account.\\n3. Return error message for further attempts.\", \"Title\": \"Rate Limiting\"}"], "StateLogic": ["{\"Logic\": \"1. State variables:\\n   - isAuthenticated (boolean): tracks if user is logged in.\\n   - userProfile (object): stores user details.\\n\\n2. State transitions:\\n   - On successful login, set isAuthenticated to true and populate userProfile.\\n   - On logout, set isAuthenticated to false and clear userProfile.\\n\\n3. Data persistence:\\n   - Store session tokens in memory or distributed cache.\", \"Type\": \"StateLogic\", \"Title\": \"User Authentication State\"}", "{\"Logic\": \"1. State variables:\\n   - sessionId (string): unique identifier for user session.\\n   - sessionExpiration (Date): expiration time for session.\\n\\n2. State transitions:\\n   - On login, create a new session with sessionId and set expiration.\\n   - On logout, invalidate session by removing sessionId.\", \"Type\": \"StateLogic\", \"Title\": \"Session State Management\"}"], "Sequence": ["{\"Description\": \"This sequence diagram illustrates the interactions between the Order Processing component and external systems during the order lifecycle.\", \"Type\": \"Sequence\", \"Title\": \"Order Processing Sequence\", \"Diagram\": \"sequenceDiagram\\n    participant User\\n    participant OrderSystem\\n    participant PaymentGateway\\n    participant ShippingService\\n\\n    User->>OrderSystem: Place Order\\n    OrderSystem->>OrderSystem: Validate Order\\n    alt Order Valid\\n        OrderSystem->>PaymentGateway: Process Payment\\n        PaymentGateway-->>OrderSystem: Payment Successful\\n        OrderSystem->>ShippingService: Schedule Shipping\\n        ShippingService-->>OrderSystem: Shipping Scheduled\\n        OrderSystem-->>User: Order Confirmation\\n    else Order Invalid\\n        OrderSystem-->>User: Show Error Message\\n    end\"}"], "StateDiagram": ["{\"Type\": \"StateDiagram\", \"Title\": \"Order State Management\", \"Diagram\": \"stateDiagram-v2\\n    [*] --> Idle: Order Received\\n    Idle --> Validating: Validate Order\\n    Validating --> Processing: Order Valid\\n    Validating --> Rejected: Order Invalid\\n    Processing --> PaymentProcessing: Payment Initiated\\n    PaymentProcessing --> Shipping: Payment Successful\\n    PaymentProcessing --> Cancelled: Payment Failed\\n    Shipping --> Delivered: Order Shipped\\n    Delivered --> [*]: Order Complete\\n    Rejected --> Idle: Reset Order\\n    Cancelled --> Idle: Reset Order\"}"], "UnitTest": ["{\"Description\": \"Test the validate() method of the Order class to ensure it correctly validates order details such as customer name and order date.\", \"Type\": \"UnitTest\", \"ExpectedResult\": \"Returns true if order details are valid, otherwise returns false.\", \"Title\": \"Unit Test: Validate Order Creation\"}", "{\"Description\": \"Test the processPayment() method of the Payment class to ensure it processes payments correctly based on the provided amount.\", \"Type\": \"UnitTest\", \"ExpectedResult\": \"Returns success message if payment is processed, otherwise returns error.\", \"Title\": \"Unit Test: Process Payment\"}", "{\"Description\": \"Test the shipOrder() method of the Shipping class to ensure it correctly ships the order to the specified address.\", \"Type\": \"UnitTest\", \"ExpectedResult\": \"Returns success message if order is shipped, otherwise returns error.\", \"Title\": \"Unit Test: Ship Order\"}"], "IntegrationTest": ["{\"Description\": \"Test the integration between Order and Payment classes to ensure that an order can be created and a payment can be processed successfully.\", \"Type\": \"IntegrationTest\", \"ExpectedResult\": \"Order is created and payment is processed without errors.\", \"Title\": \"Integration Test: Order and Payment Integration\"}", "{\"Description\": \"Test the integration between Order and Shipping classes to ensure that an order can be shipped after payment is processed.\", \"Type\": \"IntegrationTest\", \"ExpectedResult\": \"Order is shipped successfully after payment confirmation.\", \"Title\": \"Integration Test: Order and Shipping Integration\"}", "{\"Description\": \"Test the complete order process from creation to payment and shipping to ensure all components work together seamlessly.\", \"Type\": \"IntegrationTest\", \"ExpectedResult\": \"Order is created, payment is processed, and order is shipped without errors.\", \"Title\": \"Integration Test: Complete Order Process\"}"], "PerformanceTest": ["{\"Description\": \"Test the performance of the order creation process under a load of 1000 simultaneous requests.\", \"Type\": \"PerformanceTest\", \"ExpectedResult\": \"Response time should be under 2 seconds for all requests.\", \"Title\": \"Performance Test: Order Creation Load Test\"}", "{\"Description\": \"Test the payment processing system to handle 500 transactions per minute without failure.\", \"Type\": \"PerformanceTest\", \"ExpectedResult\": \"All transactions are processed successfully within the time limit.\", \"Title\": \"Performance Test: Payment Processing Throughput Test\"}", "{\"Description\": \"Measure the response time of the shipping process when handling multiple shipping requests.\", \"Type\": \"PerformanceTest\", \"ExpectedResult\": \"Response time should be under 3 seconds for each shipping request.\", \"Title\": \"Performance Test: Shipping Process Response Time\"}"], "RobustnessTest": ["{\"Description\": \"Test how the system handles invalid order data during order creation.\", \"Type\": \"RobustnessTest\", \"ExpectedResult\": \"System should return an error message indicating invalid data.\", \"Title\": \"Robustness Test: Invalid Order Data Handling\"}", "{\"Description\": \"Test the system's response when payment processing fails due to insufficient funds.\", \"Type\": \"RobustnessTest\", \"ExpectedResult\": \"System should return an error message indicating payment failure.\", \"Title\": \"Robustness Test: Payment Failure Scenario\"}", "{\"Description\": \"Test how the system handles invalid shipping addresses during the shipping process.\", \"Type\": \"RobustnessTest\", \"ExpectedResult\": \"System should return an error message indicating invalid address.\", \"Title\": \"Robustness Test: Shipping Address Validation\"}"]}}