{"work_items": {"component_name": "InteractiveButton", "figma_components": [{"frame_id": "5:1", "file_key": "mno345", "json_data": {"id": "5:1", "name": "<PERSON><PERSON>", "type": "COMPONENT", "interactions": [{"trigger": "ON_CLICK", "actions": [{"type": "NAVIGATION", "destinationId": "5:2", "navigation": "NAVIGATE", "transition": {"type": "SMART_ANIMATE", "duration": 300}}]}, {"trigger": "ON_HOVER", "actions": [{"type": "ANIMATION", "transition": {"type": "EASE_OUT", "duration": 200}}]}]}}]}}