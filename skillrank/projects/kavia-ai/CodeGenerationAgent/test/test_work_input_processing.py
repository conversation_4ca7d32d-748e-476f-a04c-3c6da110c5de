import unittest
import json
import shutil
import os
from pathlib import Path
from unittest.mock import Mock, patch
from typing import Dict, Any

from code_generation_core_agent.agents.tools.work_input_discovery_tool import WorkInputDiscovery
from dnparsers.figma.models import Paint, PaintType, Color, BlendMode


class TestWorkInputProcessingBase(unittest.TestCase):
    """Base test class with common setup and helper methods"""

    def setUp(self):
        """Set up test environment before each test."""
        self.base_path = Path("test_work_discovery")
        self.base_path.mkdir(exist_ok=True)
        self.mock_callbacks = {"terminal_output_callback": Mock()}
        self.mock_logger = Mock()
        self.discovery = WorkInputDiscovery(
            self.mock_callbacks,
            str(self.base_path),
            self.mock_logger
        )

    def tearDown(self):
        """Clean up after each test."""
        if self.base_path.exists():
            shutil.rmtree(self.base_path)

    def create_test_component(self, name: str, component_type: str = "COMPONENT") -> Dict[str, Any]:
        """Helper method to create a test component with the correct structure"""
        return {
            "work_items": {
                "figma_components": [
                    {
                        "frame_id": f"id_{name}",
                        "file_key": "test_key",
                        "json_data": {
                            "id": f"id_{name}",
                            "name": name,
                            "type": component_type,
                            "visible": True,
                            "children": [],
                            "properties": {},
                            "fills": [],
                            "strokes": [],
                            "effects": [],
                            "characters": None,
                            "text_style": None,
                            "interactions": None
                        }
                    }
                ]
            }
        }


class TestWorkInputDiscoveryBasic(TestWorkInputProcessingBase):
    """Test basic functionality of WorkInputDiscovery"""

    def test_initialization(self):
        """Test proper initialization of WorkInputDiscovery"""
        self.assertTrue(self.discovery.assets_dir.exists())
        self.assertTrue(self.discovery.assets_dir.is_dir())
        self.assertEqual(self.discovery.components, {})
        self.assertEqual(str(self.discovery.assets_dir), str(self.base_path / '.assets'))

    def test_process_single_component(self):
        """Test processing of a single basic component"""
        test_data = self.create_test_component("TestButton")
        hashes, remaining_data = self.discovery.process_figma_json(test_data)  # Removed json.dumps()

        # Verify basic processing results
        self.assertEqual(len(hashes), 1)
        self.assertIsInstance(hashes[0], str)
        self.assertEqual(len(self.discovery.components), 1)

        # Verify component was stored correctly
        component = self.discovery.get_input(hashes[0])
        self.assertEqual(component["status"], "SUCCESS")
        self.assertEqual(component["component"]["type"], "file_asset")
        self.assertEqual(component["component"]["status"], "needs_processing")

    def test_process_multiple_components(self):
        """Test processing multiple components at once"""
        data = {
            "work_items": {
                "figma_components": [
                    {
                        "frame_id": "1",
                        "file_key": "test",
                        "json_data": {
                            "id": "button1",
                            "name": "Button 1",
                            "type": "COMPONENT",
                            "visible": True,
                            "children": []
                        }
                    },
                    {
                        "frame_id": "2",
                        "file_key": "test",
                        "json_data": {
                            "id": "button2",
                            "name": "Button 2",
                            "type": "COMPONENT",
                            "visible": True,
                            "children": []
                        }
                    }
                ]
            }
        }

        hashes, _ = self.discovery.process_figma_json(data)  # Removed json.dumps()
        self.assertEqual(len(hashes), 2)
        self.assertEqual(len(self.discovery.components), 2)


class TestWorkInputDiscoveryFileSystem(TestWorkInputProcessingBase):
    """Test file system interactions of WorkInputDiscovery"""

    def test_component_directory_creation(self):
        """Test that component directories are created correctly"""
        test_data = self.create_test_component("TestComponent")
        hashes, _ = self.discovery.process_figma_json(test_data)  # Removed json.dumps()

        for sha256 in hashes:
            component_dir = self.discovery.assets_dir / sha256
            self.assertTrue(component_dir.exists())
            self.assertTrue(component_dir.is_dir())
            self.assertTrue((component_dir / "component.json").exists())

    def test_component_json_storage(self):
        """Test that component JSON is stored correctly"""
        test_data = self.create_test_component("TestComponent")
        hashes, _ = self.discovery.process_figma_json(test_data)  # Removed json.dumps()

        # Check stored JSON content
        for sha256 in hashes:
            json_path = self.discovery.assets_dir / sha256 / "component.json"
            with open(json_path) as f:
                stored_data = json.load(f)
                self.assertIn("id", stored_data)
                self.assertIn("hierarchy", stored_data)
                self.assertIn("styles", stored_data)

    def test_persistence_across_instances(self):
        """Test that component data persists when creating new instances"""
        # Process data with first instance
        test_data = self.create_test_component("TestComponent")
        hashes, _ = self.discovery.process_figma_json(test_data)  # Removed json.dumps()
        original_hash = hashes[0]

        # Create new instance with same base path
        new_discovery = WorkInputDiscovery(
            self.mock_callbacks,
            str(self.base_path),
            self.mock_logger
        )

        # Verify component directory still exists
        component_dir = new_discovery.assets_dir / original_hash
        self.assertTrue(component_dir.exists())
        self.assertTrue((component_dir / "component.json").exists())


class TestWorkInputDiscoveryProcessing(TestWorkInputProcessingBase):
    """Test component processing functionality"""

    def test_get_next_input_empty(self):
        """Test get_next_input when no components are available"""
        result = self.discovery.get_next_input()
        self.assertEqual(result["status"], "SUCCESS")
        self.assertIsNone(result["component"])

    def test_get_next_input_with_components(self):
        """Test get_next_input with available components"""
        test_data = self.create_test_component("TestComponent")
        self.discovery.process_figma_json(test_data)  # Removed json.dumps()

        result = self.discovery.get_next_input()
        self.assertEqual(result["status"], "SUCCESS")
        self.assertIsNotNone(result["component"])
        self.assertEqual(result["component"]["status"], "needs_processing")

    def test_component_completion(self):
        """Test marking components as completed"""
        test_data = self.create_test_component("TestComponent")
        hashes, _ = self.discovery.process_figma_json(test_data)  # Removed json.dumps()

        # Mark component as completed
        result = self.discovery.set_completed(hashes[0])
        self.assertEqual(result["status"], "SUCCESS")

        # Verify status change
        component = self.discovery.get_input(hashes[0])["component"]
        self.assertEqual(component["status"], "completed")

        # Verify no more uncompleted components
        next_result = self.discovery.get_next_input()
        self.assertIsNone(next_result["component"])


class TestWorkInputDiscoveryComponentTypes(TestWorkInputProcessingBase):
    """Test different component types and their handling"""

    def test_design_note_detection(self):
        """Test detection of design note components"""
        test_data = self.create_test_component("Documentation Note", "TEXT")
        hashes, _ = self.discovery.process_figma_json(test_data)  # Removed json.dumps()

        component = self.discovery.get_input(hashes[0])["component"]
        self.assertEqual(component["type"], "design_note")

    def test_file_asset_detection(self):
        """Test detection of file asset components"""
        test_data = self.create_test_component("Button Component", "COMPONENT")
        hashes, _ = self.discovery.process_figma_json(test_data)  # Removed json.dumps()

        component = self.discovery.get_input(hashes[0])["component"]
        self.assertEqual(component["type"], "file_asset")


class TestWorkInputDiscoveryErrors(TestWorkInputProcessingBase):
    """Test error handling in WorkInputDiscovery"""

    def test_invalid_json(self):
        """Test handling of invalid JSON input"""
        with self.assertRaises(Exception):
            self.discovery.process_figma_json("invalid json")

    def test_missing_required_fields(self):
        """Test handling of JSON missing required fields"""
        invalid_data = {"work_items": {}}  # Missing figma_components
        with self.assertRaises(Exception):
            self.discovery.process_figma_json(invalid_data)

    def test_invalid_component_hash(self):
        """Test handling of invalid component hash"""
        result = self.discovery.get_input("invalid_hash")
        self.assertEqual(result["status"], "ERROR")

    def test_complete_invalid_component(self):
        """Test handling of completing invalid component"""
        result = self.discovery.set_completed("invalid_hash")
        self.assertEqual(result["status"], "ERROR")

    def test_paint_type_processing(self):
        """Test that components with Paint objects are completed and retrieved correctly"""
        # Create test data with a Paint object
        test_data = {
            "work_items": {
                "figma_components": [
                    {
                        "frame_id": "button_with_paint",
                        "file_key": "test_key",
                        "json_data": {
                            "id": "button_with_paint",
                            "name": "Button with Background",
                            "type": "COMPONENT",
                            "visible": True,
                            "children": [],
                            "fills": [
                                {
                                    "type": "SOLID",
                                    "color": {
                                        "r": 0.8,
                                        "g": 0.2,
                                        "b": 0.2,
                                        "a": 1.0
                                    },
                                    "blendMode": "NORMAL",
                                    "opacity": 1.0
                                }
                            ],
                            "strokes": [],
                            "effects": []
                        }
                    }
                ]
            }
        }

        # Process the component
        hashes, _ = self.discovery.process_figma_json(test_data)
        self.assertEqual(len(hashes), 1)

        # Get next work item
        result = self.discovery.get_next_input()

        # Verify the result can be converted to JSON
        json_str = json.dumps(self.discovery.as_json(result))
        self.assertIsInstance(json_str, str)


class TestPaintSerialization(unittest.TestCase):
    def test_paint_serialization(self):
        """Test that Paint objects can be properly serialized"""
        # Create a simple Paint object
        paint = Paint(
            type=PaintType.SOLID,
            color=Color(r=0.8, g=0.2, b=0.2, a=1.0),
            blend_mode=BlendMode.NORMAL,
            opacity=1.0
        )

        # Test for_json() method
        serialized = paint.for_json()
        self.assertIsInstance(serialized, dict)

        # Verify the serialized structure
        self.assertEqual(serialized['type'], 'SOLID')
        self.assertIn('color', serialized)
        self.assertEqual(serialized['blend_mode'], 'NORMAL')
        self.assertEqual(serialized['opacity'], 1.0)

        # Test if the color was properly serialized
        color_data = serialized['color']
        self.assertIsInstance(color_data, dict)
        self.assertEqual(color_data['r'], 0.8)
        self.assertEqual(color_data['g'], 0.2)
        self.assertEqual(color_data['b'], 0.2)
        self.assertEqual(color_data['a'], 1.0)

if __name__ == '__main__':
    unittest.main()