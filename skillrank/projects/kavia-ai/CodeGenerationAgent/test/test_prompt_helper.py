import unittest
from code_generation_core_agent.agents.prompt_helper import prompt_get_tagged_items

class TestPromptHelper(unittest.TestCase):

    def test_prompt_get_tagged_items_with_image(self):
        text = "Here is an image ![image](http://example.com/image.png)"
        expected = [{'type': 'image_url', 'image_url': {'url': 'http://example.com/image.png'}}]
        self.assertEqual(prompt_get_tagged_items(text), expected)

    def test_prompt_get_tagged_items_with_multiple_images(self):
        text = "Here is an image ![image](http://example.com/image1.png) and another one ![image](http://example.com/image2.jpg)"
        expected = [
            {'type': 'image_url', 'image_url': {'url': 'http://example.com/image1.png'}},
            {'type': 'image_url', 'image_url': {'url': 'http://example.com/image2.jpg'}}
        ]
        self.assertEqual(prompt_get_tagged_items(text), expected)

    def test_prompt_get_tagged_items_with_no_tags(self):
        text = "This is a text without any tagged items."
        expected = []
        self.assertEqual(prompt_get_tagged_items(text), expected)

    def test_prompt_get_tagged_items_with_invalid_tags(self):
        text = "This is an invalid tag ![image](not_a_url)"
        expected = [{'type': 'image_url', 'image_url': {'url': 'data:image/none;base64,'}}]
        self.assertEqual(prompt_get_tagged_items(text), expected)
        text = "This is an invalid tag ![grafik](https:\/\/user-images.githubusercontent.com\/2836374\/174673179-f5b14df5-7689-49eb-995a-4c97e31c3c43.png)"
        expected = [{'type': 'image_url', 'image_url': {'url': 'https://user-images.githubusercontent.com/2836374/174673179-f5b14df5-7689-49eb-995a-4c97e31c3c43.png'}}]
        self.assertEqual(prompt_get_tagged_items(text), expected)

    def test_prompt_get_tagged_items_with_mixed_content(self):
        text = "Here is an image ![image](http://example.com/image.png) and some text without tags."
        expected = [{'type': 'image_url', 'image_url': {'url': 'http://example.com/image.png'}}]
        self.assertEqual(prompt_get_tagged_items(text), expected)

if __name__ == '__main__':
    unittest.main()