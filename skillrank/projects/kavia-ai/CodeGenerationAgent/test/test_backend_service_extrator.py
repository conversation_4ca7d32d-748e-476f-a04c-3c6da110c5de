import json

import pytest
import os
import asyncio
from unittest.mock import Magic<PERSON><PERSON>, patch, AsyncMock
import copy

from code_generation_core_agent.tools.backend_service_extractor import ServiceExtractor, BackendValidationError


@pytest.fixture
def service_extractor():
    logger = MagicMock()
    return ServiceExtractor(logger)


@pytest.fixture
def valid_implementation():
    return {
        'install_commands': 'npm install',
        'startup_command': 'npm start',
        'tcp_ports': '3001',
        'folder': 'backend'
    }


@pytest.fixture
def work_item_with_implementations(valid_implementation):
    impl1 = copy.deepcopy(valid_implementation)
    impl2 = copy.deepcopy(valid_implementation)
    impl2['folder'] = 'api-service'

    return {
        'name': 'Test Project',
        'backend_implementations': [impl1, impl2]
    }

class LoggingMock(MagicMock):
    def __del__(self):
        try:
            print(f"\n=== Calls to {self._mock_name} ===")
            for i, call in enumerate(self.call_args_list):
                args, kwargs = call
                print(f"\nCall {i+1}:")
                print(f"  Args: {json.dumps(args, default=str, indent=2)}")
                if kwargs:
                    print(f"  Kwargs: {json.dumps(kwargs, default=str, indent=2)}")
        except:
            pass

@pytest.fixture
def mock_chat():
    chat = MagicMock()
    chat._send_message_to_user = LoggingMock(return_value=(1, True))
    return chat


@pytest.fixture
def mock_executor():
    executor = MagicMock()
    executor.run_one_shot_command = MagicMock(return_value=("Success", 0))
    executor.create_subprocess_shell = AsyncMock()
    executor.is_running = MagicMock(return_value=False)
    return executor


def test_validate_backend_implementation_success(service_extractor, valid_implementation):
    # Should not raise any exception
    service_extractor.validate_backend_implementation(valid_implementation)


def test_validate_backend_implementation_missing_required_field(service_extractor, valid_implementation):
    invalid_impl = copy.deepcopy(valid_implementation)
    del invalid_impl['startup_command']

    with pytest.raises(BackendValidationError, match="Missing required field: startup_command"):
        service_extractor.validate_backend_implementation(invalid_impl)


def test_validate_backend_implementation_unknown_field(service_extractor, valid_implementation):
    invalid_impl = copy.deepcopy(valid_implementation)
    invalid_impl['unknown_field'] = 'value'

    with pytest.raises(BackendValidationError, match="Unknown field: unknown_field"):
        service_extractor.validate_backend_implementation(invalid_impl)


def test_validate_backend_implementation_wrong_type(service_extractor, valid_implementation):
    invalid_impl = copy.deepcopy(valid_implementation)
    invalid_impl['install_commands'] = 123

    with pytest.raises(BackendValidationError, match="Field install_commands must be of type str"):
        service_extractor.validate_backend_implementation(invalid_impl)


def test_validate_backend_implementation_invalid_port(service_extractor, valid_implementation):
    invalid_impl = copy.deepcopy(valid_implementation)
    invalid_impl['tcp_ports'] = '999999'

    with pytest.raises(BackendValidationError, match="TCP port 999999 is out of valid range"):
        service_extractor.validate_backend_implementation(invalid_impl)


def test_validate_backend_implementation_empty_folder(service_extractor, valid_implementation):
    invalid_impl = copy.deepcopy(valid_implementation)
    invalid_impl['folder'] = '   '

    with pytest.raises(BackendValidationError, match="Folder path cannot be empty"):
        service_extractor.validate_backend_implementation(invalid_impl)


def test_validate_backend_implementation_multiple_ports(service_extractor, valid_implementation):
    # Multiple ports should be valid
    valid_multi_port = copy.deepcopy(valid_implementation)
    valid_multi_port['tcp_ports'] = '3002, 3001'

    service_extractor.validate_backend_implementation(valid_multi_port)


def test_validate_backend_implementation_invalid_port_format(service_extractor, valid_implementation):
    # Non-integer port
    invalid_impl = copy.deepcopy(valid_implementation)
    invalid_impl['tcp_ports'] = 'abc'

    with pytest.raises(BackendValidationError, match="TCP ports must be a comma-separated list of integers"):
        service_extractor.validate_backend_implementation(invalid_impl)


def test_extract_backend_implementations_no_implementations(service_extractor, mock_executor, mock_chat):
    work_item = {'name': 'Test Project'}
    result = service_extractor.extract_backend_implementations(work_item, mock_executor, mock_chat, '/tmp')
    assert result == []


@patch('os.path.isdir')
def test_extract_backend_implementations_valid(mock_isdir, service_extractor, work_item_with_implementations,
                                               mock_executor, mock_chat):
    mock_isdir.return_value = True

    result = service_extractor.extract_backend_implementations(
        work_item_with_implementations, mock_executor, mock_chat, '/tmp'
    )

    assert len(result) == 2
    assert result[0]['folder'] == 'backend'
    assert result[1]['folder'] == 'api-service'


@patch('os.path.isdir')
def test_extract_backend_implementations_missing_directory(mock_isdir, service_extractor,
                                                           work_item_with_implementations,
                                                           mock_executor, mock_chat):
    mock_isdir.return_value = False

    result = service_extractor.extract_backend_implementations(
        work_item_with_implementations, mock_executor, mock_chat, '/tmp'
    )

    assert len(result) == 0
    assert len(mock_chat._send_message_to_user.call_args_list) >= 2
    assert any("directory not found" in call[0][0] for call in mock_chat._send_message_to_user.call_args_list)

@patch('os.path.isdir')
def test_extract_backend_implementations_invalid_implementation(mock_isdir, service_extractor,
                                                                work_item_with_implementations,
                                                                mock_executor, mock_chat):
    # Mock directory check to return True
    mock_isdir.return_value = True

    # Add an invalid implementation
    invalid_impl = {'install_commands': 'npm install', 'folder': 'invalid-service'}  # Missing required fields
    work_item_with_implementations['backend_implementations'].append(invalid_impl)

    result = service_extractor.extract_backend_implementations(
        work_item_with_implementations, mock_executor, mock_chat, '/tmp'
    )

    assert len(result) == 2
    assert any("validation failed" in call[0][0] for call in mock_chat._send_message_to_user.call_args_list)


@patch('os.path.isdir')
def test_check_service_directory_exists(mock_isdir, service_extractor, valid_implementation):
    mock_isdir.return_value = True

    result = service_extractor.check_service_directory_exists('/tmp', valid_implementation)
    assert result is True

    # Check with the correct path
    mock_isdir.assert_called_with('/tmp/backend')


@pytest.mark.asyncio
@patch('threading.Thread')
@patch('os.path.isdir')
async def test_install_and_startup(mock_isdir, mock_thread, service_extractor,
                                   valid_implementation, mock_executor, mock_chat):
    # Setup
    mock_isdir.return_value = True

    mock_thread_instance = MagicMock()
    mock_thread.return_value = mock_thread_instance

    service_extractor._services = [valid_implementation]

    mock_executor.run_one_shot_command.return_value = ("Success", 0)

    service_extractor._install_and_startup('/tmp', mock_executor, mock_chat)

    assert mock_chat._send_message_to_user.call_count >= 2
    assert any("Installing backend service" in call[0][0] for call in mock_chat._send_message_to_user.call_args_list)
    assert any("installed successfully" in call[0][0] for call in mock_chat._send_message_to_user.call_args_list)


@pytest.mark.asyncio
@patch('threading.Thread')
@patch('os.path.isdir')
async def test_install_and_startup_failure(mock_isdir, mock_thread, service_extractor,
                                           valid_implementation, mock_executor, mock_chat):
    mock_isdir.return_value = True

    mock_thread_instance = MagicMock()
    mock_thread.return_value = mock_thread_instance

    service_extractor._services = [valid_implementation]

    mock_executor.run_one_shot_command.return_value = ("Installation failed", 1)

    service_extractor._install_and_startup('/tmp', mock_executor, mock_chat)

    assert any(
        "Error installing backend service" in call[0][0] for call in mock_chat._send_message_to_user.call_args_list)

    assert mock_thread.call_count == 0

