#!/bin/bash

# Set variables
PACKAGE_NAME="code_generation_core_agent"
PYTHON_VERSION="3.11"
OUTPUT_DIR="./local_repo"
VENV_NAME="tmp_venv"

# Ensure the output directory exists
mkdir -p "$OUTPUT_DIR"

# Set up Python environment
echo "Setting up Python environment..."
python3 -m venv $VENV_NAME
source $VENV_NAME/bin/activate

# Install build tool
echo "Installing build tool..."
pip install --upgrade pip
pip install build

# Build the package
echo "Building the package..."
python -m build --outdir dist

# Extract version from pyproject.toml using sed
VERSION=$(sed -n 's/^version = "\(.*\)"/\1/p' pyproject.toml)
if [ -z "$VERSION" ]; then
    echo "Failed to extract version from pyproject.toml"
    exit 1
fi
echo "Package version: $VERSION"

# Copy the built package to the local repository
echo "Copying package to local repository..."
cp dist/* "$OUTPUT_DIR/"

# Create a simple index.html file for the local repository
echo "Creating index.html for the local repository..."
cat << EOF > "$OUTPUT_DIR/index.html"
<!DOCTYPE html>
<html>
<body>
<h1>Local PyPI Repository</h1>
<a href="${PACKAGE_NAME}-${VERSION}.tar.gz">${PACKAGE_NAME}-${VERSION}.tar.gz</a><br>
<a href="${PACKAGE_NAME}-${VERSION}-py3-none-any.whl">${PACKAGE_NAME}-${VERSION}-py3-none-any.whl</a>
</body>
</html>
EOF

echo "Package deployed to $OUTPUT_DIR"
echo "To install the package from this local repository, use:"
echo "pip install --find-links file://$PWD/$OUTPUT_DIR $PACKAGE_NAME==$VERSION"

# Clean up
deactivate
rm -rf $VENV_NAME
