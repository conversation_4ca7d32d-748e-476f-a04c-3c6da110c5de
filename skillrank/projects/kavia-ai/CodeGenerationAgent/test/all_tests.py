import pytest
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))

from .docker_executor_tests import *
from .docker_file_tools_tests import *
from .test_decorator import *
from .test_environment_summary import *
from .test_shell_tools import *
from .test_openai_tool_call import *
from .test_memories import *
from .browser_test import *
from .test_git_tool import *
from .test_llm_summarizer import *
from .test_cga_chat import *
from .test_knowledge import *
from .test_prompt_helper import *
#from .test_agent_system import *
#from .test_backend_service_extrator import *
from .test_downloader import *
from .mobile_project_builder_test import *
from .port_utils_test import *
from .preview_manager_test import *
from .test_cost_tracker import *
from .code_generation_test import *
from .test_document_read import *
from .test_monitored_process import *
from .test_preview_manager_integration import *
from .test_project_schema import *
from .test_logsanitizer import *

if __name__ == "__main__":
    # Run all tests
    pytest.main([__file__])


def pytest_sessionfinish(session, exitstatus):
    print("pytest_sessionfinish called. Exit status:", exitstatus)
    try:
        shutdown_browser()
    except Exception as e:
        print(f"Error during browser shutdown: {e}")

    import os
    os._exit(exitstatus)
