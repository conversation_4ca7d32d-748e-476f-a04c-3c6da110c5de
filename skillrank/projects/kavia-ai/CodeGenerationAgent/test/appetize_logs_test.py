import os
import tempfile
import unittest
import shutil

from code_generation_core_agent.agents.tools.mobile.appetize_mobile_logger import AppetizeSessionLogger


class TestAppetizeSessionLogger(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test logs
        self.test_log_dir = tempfile.mkdtemp()
        self.log_file_path = os.path.join(self.test_log_dir, "logs", "AppetizeLogs.log")
        self.json_log_file_path = os.path.join(self.test_log_dir, "logs", "json", "AppetizeLogs.json.log")
    
    def tearDown(self):
        # Clean up the temporary directory after tests
        shutil.rmtree(self.test_log_dir)
    
    def test_logger_initialization(self):
        # Test that the logger is properly initialized
        logger = AppetizeSessionLogger(self.test_log_dir)
        self.assertTrue(os.path.exists(os.path.dirname(self.log_file_path)), 
                       "Log directory should be created")
        self.assertTrue(os.path.exists(self.log_file_path), 
                       "Log file should be created")
        
    def test_sync_logs_single_message(self):
        # Test logging a single message
        logger = AppetizeSessionLogger(self.test_log_dir)
        test_message = "Test Appetize log message"
        logger.sync_logs([test_message])
        
        # Check log file contains the message
        with open(self.log_file_path, 'r') as f:
            log_content = f.read()
            self.assertIn(test_message, log_content, 
                         f"Log file should contain '{test_message}'")
        
        # Also check JSON log file
        self.assertTrue(os.path.exists(self.json_log_file_path), 
                        "JSON log file should be created")
        with open(self.json_log_file_path, 'r') as f:
            json_log_content = f.read()
            self.assertIn(test_message, json_log_content, 
                         f"JSON log file should contain '{test_message}'")
    
    def test_sync_logs_multiple_messages(self):
        # Test logging multiple messages
        logger = AppetizeSessionLogger(self.test_log_dir)
        test_messages = [
            "First test message",
            "Second test message",
            "Third test message with special chars: $#@!"
        ]
        logger.sync_logs(test_messages)
        
        # Check all messages are in the log file
        with open(self.log_file_path, 'r') as f:
            log_content = f.read()
            for msg in test_messages:
                self.assertIn(msg, log_content, 
                             f"Log file should contain '{msg}'")
    
    def test_empty_logs(self):
        # Test with empty logs array
        logger = AppetizeSessionLogger(self.test_log_dir)
        logger.sync_logs([])
        
        # Check initialization message is present but nothing else
        with open(self.log_file_path, 'r') as f:
            log_content = f.read()
            self.assertIn("Appetize session logger initialized", log_content, 
                         "Initialization message should be present")