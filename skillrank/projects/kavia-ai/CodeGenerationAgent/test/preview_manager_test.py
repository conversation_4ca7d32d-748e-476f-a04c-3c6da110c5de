import os

import pytest
import json
from pathlib import Path
from unittest.mock import Magic<PERSON>ock, patch

from code_generation_core_agent.agents.preview.preview_manager import (
    PreviewManager,
    PreviewState,
    PreviewHolder,
)
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType


# -----------------------------------------------------------------------------
# Helper utilities
# -----------------------------------------------------------------------------

def make_mock_container(
    name: str,
    framework: str = FrameworkType.DEFAULT.value,
    port: int | None = None,
    workspace: str = "ws",
    depends_on: list[str] | None = None,
):
    """Create a minimal mock `Container` compatible with `PreviewManager`.

    The real `Container` dataclass lives in `project_schemas`, but for unit tests
    we only need the attributes that `PreviewHolder` consumes. A plain
    ``MagicMock`` keeps the tests lightweight and decoupled from the full
    dataclass signature.
    """
    c = MagicMock()
    c.container_name = name
    c.framework = framework
    c.port = port
    c.dependent_containers = depends_on or []
    c.workspace = workspace
    # Attributes referenced occasionally in the implementation
    c.container_root = ""
    c.cotainer_root = ""  # typo in implementation - keep for compatibility
    c.installCommand = ""
    return c


# -----------------------------------------------------------------------------
# Fixtures
# -----------------------------------------------------------------------------

@pytest.fixture(autouse=True)
def reset_preview_manager():
    """Ensure we get a fresh singleton for each test."""
    # Properly shutdown any existing instance
    if PreviewManager._instance is not None:
        PreviewManager._instance.shutdown()

    # Reset singleton state
    PreviewManager._instance = None
    PreviewManager._initialized = False
    PreviewManager._do_exit = True  # Stop any running threads

    # Wait for thread to actually stop
    if (hasattr(PreviewManager, '_message_handler_thread') and
            PreviewManager._message_handler_thread and
            PreviewManager._message_handler_thread.is_alive()):
        PreviewManager._message_handler_thread.join(timeout=1)

    # Reset thread state
    PreviewManager._message_handler_thread = None
    PreviewManager._do_exit = False  # Reset for next test

    yield

    # Cleanup after test
    if PreviewManager._instance is not None:
        PreviewManager._instance.shutdown()
    PreviewManager._instance = None
    PreviewManager._initialized = False
    PreviewManager._do_exit = True


@pytest.fixture
def mock_executor():
    executor = MagicMock()
    # Default: succeed
    executor.run_one_shot_command.return_value = ("", 0)
    return executor


@pytest.fixture
def mock_logger():
    return MagicMock()


@pytest.fixture
def pm_manager(tmp_path, mock_executor, mock_logger):
    mgr = PreviewManager.get_instance(mock_executor)
    mgr.set_executor(mock_executor)
    mgr.set_logger(mock_logger)
    mgr.set_base_path(Path(tmp_path))
    return mgr


# -----------------------------------------------------------------------------
# Tests
# -----------------------------------------------------------------------------

def test_singleton(mock_executor):
    m1 = PreviewManager.get_instance(mock_executor)
    m2 = PreviewManager.get_instance()
    assert m1 is m2


def test_register_and_retrieve_container(pm_manager):
    container = make_mock_container("web", port=3000, workspace="ws1")
    pm_manager.register_container(container, ApplicationType.WEB.value)

    c = pm_manager.get_container_by_name("web")
    assert isinstance(c, PreviewHolder)
    assert c.container_name == "web"
    assert c.container_type == ApplicationType.WEB.value
    assert c.host == "0.0.0.0"


def test_get_container_by_type(pm_manager):
    db = make_mock_container("db")
    api = make_mock_container("api")
    front = make_mock_container("front")

    pm_manager.register_container(db, ApplicationType.DATABASE.value)
    pm_manager.register_container(api, ApplicationType.BACKEND.value)
    pm_manager.register_container(front, ApplicationType.WEB.value)

    web_containers = pm_manager.get_container_by_type(ApplicationType.WEB.value)
    assert len(web_containers) == 1
    assert web_containers[0].container_name == "front"


def test_get_dependent_containers(pm_manager):
    base = make_mock_container("base")
    child = make_mock_container("child", depends_on=["base"])

    pm_manager.register_container(base, ApplicationType.BACKEND.value)
    pm_manager.register_container(child, ApplicationType.BACKEND.value)

    deps = pm_manager.get_dependent_containers("base")
    assert len(deps) == 1
    assert deps[0].container_name == "child"


def test_to_dict_defaults(pm_manager):
    c = make_mock_container("c")
    pm_manager.register_container(c, ApplicationType.BACKEND.value)
    holder = pm_manager.get_container_by_name("c")
    d = holder.to_dict()
    assert d["status"] == PreviewState.NOT_STARTED.value
    assert d["url"] is None
    assert d["api_route"] is None
    assert d["error"] is None
    assert "timestamp" in d


def test_check_and_prepare_port_success(pm_manager):
    with patch(
        "code_generation_core_agent.agents.preview.preview_manager.log_info_of_pid_running_on_port"
    ) as mock_loginfo, patch(
        "code_generation_core_agent.agents.preview.preview_manager.stop_process_using_port"
    ) as mock_stop:
        res = pm_manager._check_and_prepare_port(3000)
        assert res is True
        mock_loginfo.assert_called_once_with(3000, pm_manager.executor, pm_manager.logger)
        mock_stop.assert_called_once_with(3000, pm_manager.executor, pm_manager.logger)


def test_check_and_prepare_port_exception(pm_manager):
    with patch(
        "code_generation_core_agent.agents.preview.preview_manager.log_info_of_pid_running_on_port"
    ), patch(
        "code_generation_core_agent.agents.preview.preview_manager.stop_process_using_port",
        side_effect=Exception("oops"),
    ):
        res = pm_manager._check_and_prepare_port(3000)
        assert res is True


def test_update_dependencies_success(pm_manager):
    test_container = make_mock_container("dep_test")
    with patch(
        "code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_install_dependencies_info"
    ) as mock_get_info:
        mock_get_info.return_value = ("npm install", "/some/path")

        ok = pm_manager._update_dependencies("/some/path", test_container)

        mock_get_info.assert_called_once_with("/some/path", pm_manager.logger, "dep_test")
        pm_manager.executor.run_one_shot_command.assert_called_with("cd /some/path && npm install")
        assert ok is True


def test_update_dependencies_failure(pm_manager):
    test_container = make_mock_container("dep_fail")
    with patch(
        "code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_install_dependencies_info"
    ) as mock_get_info:
        mock_get_info.return_value = ("npm install", "/another/path")
        pm_manager.executor.run_one_shot_command.return_value = ("fail output", 1)

        ok = pm_manager._update_dependencies("/another/path", test_container)
        assert ok is False


def test_run_container_thread_success(pm_manager):
    pm_manager.set_build_completed_callback(MagicMock())
    c = make_mock_container("c", port=3000)
    pm_manager.register_container(c, ApplicationType.WEB.value)

    called: list[str] = []

    def fake_web_run(container):
        called.append(container.container_name)

    pm_manager._run_container = fake_web_run

    pm_manager._run_container_thread("c", run_dependencies=False, restart=False)

    cont = pm_manager.get_container_by_name("c")
    assert cont.status == PreviewState.RUNNING.value
    assert called == ["c"]
    pm_manager.build_completed_callback.assert_called_with("c", cont.url, cont.status, cont.error)


def test_run_container_dependency_failure(pm_manager):
    pm_manager.set_build_completed_callback(MagicMock())
    b = make_mock_container("B")
    a = make_mock_container("A", depends_on=["B"])
    pm_manager.register_container(b, ApplicationType.BACKEND.value)
    pm_manager.register_container(a, ApplicationType.BACKEND.value)
    pm_manager.set_continue_on_dependency_failure(False)

    def fail_backend(c):
        raise RuntimeError("build error")

    pm_manager._run_backend_container = fail_backend
    pm_manager._run_container_thread("A")

    a_holder = pm_manager.get_container_by_name("A")
    assert a_holder.status == PreviewState.FAILED.value
    assert "Dependency B failed" in a_holder.error


def test_run_application(pm_manager):
    app1 = make_mock_container("app1", port=3000)
    pm_manager.register_container(app1, ApplicationType.WEB.value)
    result = pm_manager.run_application()
    assert result == {"app1": {"status": PreviewState.BUILDING.value}}


def test_dependency_failure_updates_status_file(tmp_path, pm_manager):

    database = make_mock_container("database", port=5432)
    backend = make_mock_container("backend", port=8000, depends_on=["database"])
    pm_manager.register_container(database, ApplicationType.DATABASE.value)
    pm_manager.register_container(backend, ApplicationType.BACKEND.value)
    pm_manager.set_continue_on_dependency_failure(False)

    def fail_database_container(container):
        raise RuntimeError("Database connection failed")

    pm_manager._run_container = fail_database_container
    pm_manager._run_container_thread("backend", run_dependencies=True, restart=False)

    db_holder = pm_manager.get_container_by_name("database")
    backend_holder = pm_manager.get_container_by_name("backend")

    assert db_holder.status == PreviewState.FAILED.value
    assert "Database connection failed" in db_holder.error
    assert backend_holder.status == PreviewState.FAILED.value
    assert "Dependency database failed" in backend_holder.error

    db_container = pm_manager.get_container_by_name("database")
    backend_container = pm_manager.get_container_by_name("backend")

    assert db_container.status == PreviewState.FAILED.value
    assert backend_container.status == PreviewState.FAILED.value
    assert "Database connection failed" in db_container.error
    assert "Dependency database failed" in backend_container.error

def test_multiple_container_failures(tmp_path, pm_manager):

    web1 = make_mock_container("web1", port=3000, workspace="ws1")
    web2 = make_mock_container("web2", port=3001, workspace="ws2")
    backend1 = make_mock_container("backend1", port=8000, workspace="ws3")

    pm_manager.register_container(web1, ApplicationType.WEB.value)
    pm_manager.register_container(web2, ApplicationType.WEB.value)
    pm_manager.register_container(backend1, ApplicationType.BACKEND.value)

    def fail_web_container(container):
        raise RuntimeError(f"Web container {container.container_name} failed")

    pm_manager._run_container = fail_web_container

    pm_manager._run_container_thread("web1", run_dependencies=False, restart=False)
    pm_manager._run_container_thread("web2", run_dependencies=False, restart=False)
    pm_manager._run_container_thread("backend1", run_dependencies=False, restart=False)

    cn_web1 = pm_manager.get_container_by_name("web1")
    cn_web2 = pm_manager.get_container_by_name("web2")
    cn_backend1 = pm_manager.get_container_by_name("backend1")
    assert cn_web1.status == PreviewState.FAILED.value
    assert cn_web1.container_name in cn_web1.error
    assert cn_web2.status == PreviewState.FAILED.value
    assert cn_web2.error in cn_web2.error
    assert cn_backend1.status == PreviewState.FAILED.value
    assert cn_backend1.container_name in cn_backend1.error

def test_build_completed_callback_on_failure(pm_manager):
    callback_mock = MagicMock()
    pm_manager.set_build_completed_callback(callback_mock)

    cb = make_mock_container("callback_test", port=3000)
    pm_manager.register_container(cb, ApplicationType.WEB.value)

    def fail_web_container(container):
        raise RuntimeError("Callback test failure")

    pm_manager._run_container = fail_web_container
    pm_manager._run_container_thread("callback_test", run_dependencies=False, restart=False)

    callback_mock.assert_called_once()
    name, url, status, error = callback_mock.call_args[0]
    assert name == "callback_test"
    assert url is None
    assert status == PreviewState.FAILED.value
    assert "Callback test failure" in error


def test_run_path_fallback_without_universal_setup(pm_manager, tmp_path):
    """Test fallback to manifest path when universal setup returns None for run_path"""
    container = make_mock_container(
        "fallback_test",
        framework="custom_framework",
        port=4000,
        workspace="fallback_ws"
    )
    container.container_root = "custom/container/root"
    container.startCommand = "npm start"
    container.installCommand = "npm install"

    pm_manager.register_container(container, ApplicationType.WEB.value)

    with patch(
            "code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.wait_for_project_setup"
    ), patch(
        "code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_run_info"
    ) as mock_run_info, patch(
        "code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_install_dependencies_info"
    ) as mock_install_info, patch(
        "code_generation_core_agent.agents.preview.preview_manager.MonitoredProcess"
    ) as MockProcessClass, patch(
        "code_generation_core_agent.agents.preview.preview_manager.run_command_template_to_command"
    ) as mock_run_template:
        # Configure the mock instance
        mock_instance = MagicMock()
        mock_instance.start_process = MagicMock()
        mock_instance.wait_for_port_open = MagicMock(return_value=True)
        MockProcessClass.return_value = mock_instance

        mock_run_info.return_value = ("node server.js", None)
        mock_install_info.return_value = (None, None)

        pm_manager._run_container_thread("fallback_test")

        expected_run_path = os.path.join(str(tmp_path), "custom/container/root")

        pm_manager.executor.run_one_shot_command.assert_any_call(
            f"cd {expected_run_path} && npm install"
        )

        MockProcessClass.assert_called_once()
        args, kwargs = MockProcessClass.call_args
        assert args[2] == expected_run_path

        holder = pm_manager.get_container_by_name("fallback_test")
        assert holder.status == PreviewState.RUNNING.value

def test_container_continues_on_dependency_failure(pm_manager):
    """Test that containers attempt to build even when dependencies fail"""
    pm_manager.set_build_completed_callback(MagicMock())
    pm_manager.set_continue_on_dependency_failure(True)
    
    database = make_mock_container("database", port=5432)
    backend = make_mock_container("backend", port=8000, depends_on=["database"])
    
    pm_manager.register_container(database, ApplicationType.DATABASE.value)
    pm_manager.register_container(backend, ApplicationType.BACKEND.value)
    
    # Database fails, backend succeeds
    def selective_run(container):
        if container.container_name == "database":
            raise RuntimeError("Database connection failed")
        # Backend runs successfully
        container.url = f"http://localhost:{container.port}"
    
    pm_manager._run_container = selective_run
    pm_manager._run_container_thread("backend", run_dependencies=True)
    
    db_holder = pm_manager.get_container_by_name("database")
    backend_holder = pm_manager.get_container_by_name("backend")
    
    # Database should fail
    assert db_holder.status == PreviewState.FAILED.value
    assert "Database connection failed" in db_holder.error
    
    # Backend should run despite dependency failure
    assert backend_holder.status == PreviewState.RUNNING.value
    assert backend_holder.failed_dependencies == ["database"]
    assert backend_holder.warning is not None
    assert "http://localhost:8000" in backend_holder.url

def test_operation_mode_skips_wait_for_project_setup(pm_manager, tmp_path):
    """Test that wait_for_project_setup is skipped in maintenance mode"""
    
    # Create a test container
    container = make_mock_container(
        "maintenance_test",
        framework="react",
        port=3000,
        workspace="maintenance_ws"
    )
    container.container_root = "maintenance/root"
    container.startCommand = "npm start"
    
    pm_manager.register_container(container, ApplicationType.WEB.value)
    
    # Mock the wait_for_project_setup to track if it's called
    with patch(
        "code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.wait_for_project_setup"
    ) as mock_wait_setup, patch(
        "code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_run_info"
    ) as mock_run_info, patch(
        "code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_install_dependencies_info"
    ) as mock_install_info, patch(
        "code_generation_core_agent.agents.preview.preview_manager.MonitoredProcess"
    ) as MockProcessClass, patch(
        "code_generation_core_agent.agents.preview.preview_manager.run_command_template_to_command"
    ) as mock_run_template:
        
        # Configure mocks
        mock_instance = MagicMock()
        mock_instance.start_process = MagicMock()
        mock_instance.wait_for_port_open = MagicMock(return_value=True)
        MockProcessClass.return_value = mock_instance
        
        mock_run_info.return_value = ("npm start", "/some/path")
        mock_install_info.return_value = ("npm install", "/some/path")
        
        # Test 1: Default mode (CodeGeneration) - should call wait_for_project_setup
        pm_manager.set_operation_mode("CodeGeneration")
        pm_manager._run_container_thread("maintenance_test")
        
        # Verify wait_for_project_setup was called
        mock_wait_setup.assert_called_once()
        assert mock_wait_setup.call_args[0][0].endswith("maintenance_ws")
        
        # Reset mock
        mock_wait_setup.reset_mock()
        
        # Test 2: CodeMaintenance mode - should NOT call wait_for_project_setup
        pm_manager.set_operation_mode("CodeMaintenance")
        # Reset container status to allow re-run
        container_holder = pm_manager.get_container_by_name("maintenance_test")
        container_holder.status = PreviewState.NOT_STARTED.value
        
        pm_manager._run_container_thread("maintenance_test")
        
        # Verify wait_for_project_setup was NOT called
        mock_wait_setup.assert_not_called()
        
        # Test 3: DocumentCreation mode - should also NOT call wait_for_project_setup
        pm_manager.set_operation_mode("DocumentCreation")
        container_holder.status = PreviewState.NOT_STARTED.value
        
        pm_manager._run_container_thread("maintenance_test")
        
        # Verify wait_for_project_setup was still not called
        mock_wait_setup.assert_not_called()
        
        # Verify the container still runs successfully in all modes
        holder = pm_manager.get_container_by_name("maintenance_test")
        assert holder.status == PreviewState.RUNNING.value

def test_manifest_refresh_on_run_container(pm_manager, tmp_path):
    """Test that manifest is re-read when run_container is called"""
    manifest_path = tmp_path / "project.yaml"
    pm_manager.set_manifest_path(str(manifest_path))
    
    # Create initial container with no start command
    container = make_mock_container("test", port=3000)
    container.startCommand = ""  # Empty
    pm_manager.register_container(container, ApplicationType.BACKEND.value)
    
    # Verify initial state
    holder = pm_manager.get_container_by_name("test")
    assert holder.startCommand == ""
    
    # Mock _refresh_containers_from_manifest to verify it's called
    refresh_called = []
    
    with patch.object(pm_manager, '_refresh_containers_from_manifest') as mock_refresh:
        mock_refresh.side_effect = lambda: refresh_called.append(True)
        
        with patch("code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_run_info") as mock_run, \
             patch("code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.wait_for_project_setup"), \
             patch("code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_install_dependencies_info") as mock_install:
            
            mock_run.return_value = (None, None)  # No .init command
            mock_install.return_value = (None, None)
            
            # This should trigger manifest refresh
            result = pm_manager.run_container("test")
            assert result["status"] == PreviewState.BUILDING.value
            
            # Verify refresh was called
            assert mock_refresh.called, "_refresh_containers_from_manifest should have been called"


def test_empty_string_command_falls_back_to_init(pm_manager):
    """Test that empty string command falls back to .init command"""
    c = make_mock_container("test", port=3000)
    c.startCommand = ""  # Empty string, not None
    pm_manager.register_container(c, ApplicationType.BACKEND.value)
    
    with patch("code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.wait_for_project_setup"), \
         patch("code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_run_info") as mock_run, \
         patch("code_generation_core_agent.agents.preview.preview_manager.universal_project_setup.get_framework_install_dependencies_info") as mock_install, \
         patch("code_generation_core_agent.agents.preview.preview_manager.MonitoredProcess") as MockProcess, \
         patch("code_generation_core_agent.agents.preview.preview_manager.run_command_template_to_command") as mock_run_template:

        mock_run.return_value = ("init_command.sh", "/path")  # .init provides command
        mock_install.return_value = (None, None)
        mock_run_template.return_value = "init_command.sh"
        mock_instance = MagicMock()
        mock_instance.start_process = MagicMock()
        mock_instance.wait_for_port_open = MagicMock(return_value=True)
        MockProcess.return_value = mock_instance
        
        pm_manager._run_container_thread("test")
        
        import time
        time.sleep(0.1)
        
        # Should use init command since manifest command is empty string
        MockProcess.assert_called_once()
        args, _ = MockProcess.call_args
        assert args[1] == "init_command.sh"

def test_cyclic_dependency_simple(pm_manager):
    """Test that direct cyclic dependencies are detected"""
    # Create A -> B -> A cycle
    container_a = make_mock_container("A", depends_on=["B"])
    container_b = make_mock_container("B", depends_on=["A"])
    
    pm_manager.register_container(container_a, ApplicationType.BACKEND.value)
    pm_manager.register_container(container_b, ApplicationType.BACKEND.value)
    
    # Mock the logger.warning method
    with patch.object(pm_manager.logger, 'warning') as mock_warning:
        # Mock _run_container to prevent actual execution
        pm_manager._run_container = MagicMock()
        
        # Start running A with initial processing chain containing A
        # This simulates the recursive call scenario
        pm_manager._run_container_thread("A", run_dependencies=True, restart=False, _processing_chain={"A"})
        
        # Verify the cycle detection warning was logged
        mock_warning.assert_called_with("Cyclic dependency detected: A is already being processed")
        
        # Verify _run_container was never called due to early return
        pm_manager._run_container.assert_not_called()

def test_cyclic_dependency_chain(pm_manager):
    """Test detection of longer cyclic dependency chains"""
    # Create A -> B -> C -> A cycle
    container_a = make_mock_container("A", depends_on=["B"])
    container_b = make_mock_container("B", depends_on=["C"])
    container_c = make_mock_container("C", depends_on=["A"])
    
    pm_manager.register_container(container_a, ApplicationType.BACKEND.value)
    pm_manager.register_container(container_b, ApplicationType.BACKEND.value)
    pm_manager.register_container(container_c, ApplicationType.BACKEND.value)
    
    # Mock the logger.warning method
    with patch.object(pm_manager.logger, 'warning') as mock_warning:
        pm_manager._run_container = MagicMock()
        
        # Simulate being deep in the chain where A is already being processed
        pm_manager._run_container_thread("A", run_dependencies=True, restart=False, _processing_chain={"A", "B", "C"})
        
        mock_warning.assert_called_with("Cyclic dependency detected: A is already being processed")
        pm_manager._run_container.assert_not_called()

def test_runtime_state_preservation_on_reregister(pm_manager):
    """Test that runtime state is preserved when re-registering a container"""
    # Step 1: Register initial container
    container_v1 = make_mock_container("webapp", port=3000, workspace="ws1")
    pm_manager.register_container(container_v1, ApplicationType.WEB.value)
    
    # Step 2: Get the container and set runtime state
    holder = pm_manager.get_container_by_name("webapp")
    holder.status = PreviewState.RUNNING.value
    holder.url = "http://localhost:3000"
    holder.api_route = "/api/v1"
    holder.db_env_vars = {"DB_HOST": "localhost", "DB_PORT": "5432"}
    holder.error = None
    holder.timestamp = "2024-01-01T12:00:00"
    holder.installed = True
    holder.built = True
    holder.failed_dependencies = ["database"]
    holder.warning = "Database dependency failed but continuing"
    
    # Step 3: Create a new container with same name but different config
    # (simulating manifest update)
    container_v2 = make_mock_container("webapp", port=3001, workspace="ws2")  # Different port and workspace
    container_v2.startCommand = "npm run dev"  # New start command
    
    # Step 4: Re-register the container
    pm_manager.register_container(container_v2, ApplicationType.WEB.value, True)
    
    # Step 5: Verify runtime state was preserved
    updated_holder = pm_manager.get_container_by_name("webapp")
    
    # Runtime state should be preserved
    assert updated_holder.status == PreviewState.RUNNING.value
    assert updated_holder.url == "http://localhost:3000"
    assert updated_holder.api_route == "/api/v1"
    assert updated_holder.db_env_vars == {"DB_HOST": "localhost", "DB_PORT": "5432"}
    assert updated_holder.error is None
    assert updated_holder.timestamp == "2024-01-01T12:00:00"
    assert updated_holder.installed is True
    assert updated_holder.built is True
    assert updated_holder.failed_dependencies == ["database"]
    assert updated_holder.warning == "Database dependency failed but continuing"
    
    # But container configuration should be updated
    assert updated_holder.port == 3001  # New port
    assert updated_holder.workspace == "ws2"  # New workspace
    assert updated_holder.startCommand == "npm run dev"  # New start command

def test_append_db_env_variables_no_duplicates(pm_manager, mock_executor):
    """Test that _append_db_env_variables does not append duplicate environment variables"""
    # Set up the database run path
    pm_manager.db_run_path = "/fake/db/path"
    
    # Mock the database .env content with multiple variables
    db_env_content = '''POSTGRES_URL="postgresql://localhost:5000/myapp"
POSTGRES_USER="appuser"
POSTGRES_PASSWORD="dbuser123"
POSTGRES_DB="myapp"
POSTGRES_PORT="5000"'''
    
    # Mock existing .env content with some overlapping variables
    existing_env_content = '''POSTGRES_URL="postgresql://localhost:5000/myapp"
POSTGRES_USER="appuser"
API_KEY="existing_key"'''
    
    # Expected content to be appended (only non-duplicate variables)
    expected_append_content = '''POSTGRES_PASSWORD="dbuser123"
POSTGRES_DB="myapp"
POSTGRES_PORT="5000"'''
    
    # Set up mock responses
    mock_executor.run_one_shot_command.side_effect = [
        (db_env_content, 0),  # First call: read database .env
        (existing_env_content, 0),  # Second call: read existing .env
        ("", 0)  # Third call: append to .env
    ]
    
    # Create a mock container and set it up
    container = make_mock_container("test_container", port=8000)
    pm_manager.register_container(container, ApplicationType.BACKEND.value)
    holder = pm_manager.get_container_by_name("test_container")
    
    # Call the method
    pm_manager._append_db_env_variables("/test/run/path")
    
    # Verify the calls
    assert mock_executor.run_one_shot_command.call_count == 3
    
    # Verify the append command was called with only non-duplicate variables
    append_call = mock_executor.run_one_shot_command.call_args_list[2]
    append_cmd = append_call[0][0]
    
    assert append_cmd.startswith("cd /test/run/path && echo '")
    assert "POSTGRES_PASSWORD=\"dbuser123\"" in append_cmd
    assert "POSTGRES_DB=\"myapp\"" in append_cmd
    assert "POSTGRES_PORT=\"5000\"" in append_cmd
    # Should NOT contain duplicates
    assert append_cmd.count("POSTGRES_URL") == 0
    assert append_cmd.count("POSTGRES_USER") == 0
    
    # Verify the flag was set
    assert pm_manager.copied_db_env_vars is True

def test_append_db_env_variables_no_duplicates(pm_manager, mock_executor):
    """Test that _append_db_env_variables does not append duplicate environment variables"""
    # Set up the database run path
    pm_manager.db_run_path = "/fake/db/path"
    
    # Mock the database .env content with multiple variables
    db_env_content = '''POSTGRES_URL="postgresql://localhost:5000/myapp"
POSTGRES_USER="appuser"
POSTGRES_PASSWORD="dbuser123"
POSTGRES_DB="myapp"
POSTGRES_PORT="5000"'''
    
    # Mock existing .env content with some overlapping variables
    existing_env_content = '''POSTGRES_URL="postgresql://localhost:5000/myapp"
POSTGRES_USER="appuser"
API_KEY="existing_key"'''
    
    # Set up mock responses
    mock_executor.run_one_shot_command.side_effect = [
        (db_env_content, 0),  # First call: read database .env
        (existing_env_content, 0),  # Second call: read existing .env
        ("", 0)  # Third call: append to .env
    ]
    
    # Create a mock container and set it up
    container = make_mock_container("test_container", port=8000)
    pm_manager.register_container(container, ApplicationType.BACKEND.value)
    holder = pm_manager.get_container_by_name("test_container")
    
    # Call the method
    pm_manager._append_db_env_variables("/test/run/path")
    
    # Verify the calls
    assert mock_executor.run_one_shot_command.call_count == 3
    
    # Verify the append command was called with only non-duplicate variables
    append_call = mock_executor.run_one_shot_command.call_args_list[2]
    append_cmd = append_call[0][0]
    
    assert append_cmd.startswith("cd /test/run/path && echo '")
    assert "POSTGRES_PASSWORD=\"dbuser123\"" in append_cmd
    assert "POSTGRES_DB=\"myapp\"" in append_cmd
    assert "POSTGRES_PORT=\"5000\"" in append_cmd
    # Should NOT contain duplicates
    assert append_cmd.count("POSTGRES_URL") == 0
    assert append_cmd.count("POSTGRES_USER") == 0
    
    # Verify the flag was set
    assert pm_manager.copied_db_env_vars is True


def test_append_db_env_variables_handles_comments(pm_manager, mock_executor):
    """Test that _append_db_env_variables correctly handles commented lines"""
    # Set up the database run path
    pm_manager.db_run_path = "/fake/db/path"
    
    # Mock the database .env content with comments
    db_env_content = '''# Database configuration
POSTGRES_URL="postgresql://localhost:5000/myapp"
# User settings
POSTGRES_USER="appuser"
POSTGRES_PASSWORD="dbuser123"'''
    
    # Mock existing .env content with comments
    existing_env_content = '''# Application settings
API_KEY="existing_key"
# Database URL (already configured)
POSTGRES_URL="postgresql://localhost:5000/myapp"
# This is a comment
# POSTGRES_USER="different_user"'''
    
    # Set up mock responses
    mock_executor.run_one_shot_command.side_effect = [
        (db_env_content, 0),  # First call: read database .env
        (existing_env_content, 0),  # Second call: read existing .env
        ("", 0)  # Third call: append to .env
    ]
    
    # Call the method
    pm_manager._append_db_env_variables("/test/run/path")
    
    # Verify the append command
    append_call = mock_executor.run_one_shot_command.call_args_list[2]
    append_cmd = append_call[0][0]
    
    # Should append non-duplicate variables only
    assert "POSTGRES_USER=\"appuser\"" in append_cmd
    assert "POSTGRES_PASSWORD=\"dbuser123\"" in append_cmd
    # Should NOT contain duplicates or comments
    assert append_cmd.count("#") == 0
    assert append_cmd.count("POSTGRES_URL") == 0
    
    # Verify the flag was set
    assert pm_manager.copied_db_env_vars is True


def test_append_db_env_variables_handles_export_prefix(pm_manager, mock_executor):
    """Test that _append_db_env_variables correctly handles export prefix"""
    # Set up the database run path
    pm_manager.db_run_path = "/fake/db/path"
    
    # Mock the database .env content with export prefix
    db_env_content = '''export POSTGRES_URL="postgresql://localhost:5000/myapp"
export POSTGRES_USER="appuser"
export POSTGRES_PASSWORD="dbuser123"'''
    
    # Mock existing .env content with export prefix
    existing_env_content = '''export POSTGRES_URL="postgresql://localhost:5000/myapp"
export API_KEY="existing_key"'''
    
    # Set up mock responses
    mock_executor.run_one_shot_command.side_effect = [
        (db_env_content, 0),  # First call: read database .env
        (existing_env_content, 0),  # Second call: read existing .env
        ("", 0)  # Third call: append to .env
    ]
    
    # Call the method
    pm_manager._append_db_env_variables("/test/run/path")
    
    # Verify the append command
    append_call = mock_executor.run_one_shot_command.call_args_list[2]
    append_cmd = append_call[0][0]
    
    # Should append without export prefix
    assert "POSTGRES_USER=\"appuser\"" in append_cmd
    assert "POSTGRES_PASSWORD=\"dbuser123\"" in append_cmd
    # Should NOT contain export prefix
    assert "export " not in append_cmd
    
    # Verify the flag was set
    assert pm_manager.copied_db_env_vars is True


def test_append_db_env_variables_preserves_export_in_values(pm_manager, mock_executor):
    """Test that _append_db_env_variables preserves 'export' when it appears in values"""
    # Set up the database run path
    pm_manager.db_run_path = "/fake/db/path"
    
    # Mock the database .env content with 'export' in values
    db_env_content = '''export COMMAND="npm run export:data"
export DESCRIPTION="This export feature is important"
export POSTGRES_URL="postgresql://localhost:5000/myapp"'''
    
    # Mock existing .env content
    existing_env_content = '''API_KEY="existing_key"'''
    
    # Set up mock responses
    mock_executor.run_one_shot_command.side_effect = [
        (db_env_content, 0),  # First call: read database .env
        (existing_env_content, 0),  # Second call: read existing .env
        ("", 0)  # Third call: append to .env
    ]
    
    # Call the method
    pm_manager._append_db_env_variables("/test/run/path")
    
    # Verify the append command
    append_call = mock_executor.run_one_shot_command.call_args_list[2]
    append_cmd = append_call[0][0]
    
    # Should preserve 'export' in values
    assert 'COMMAND="npm run export:data"' in append_cmd
    assert 'DESCRIPTION="This export feature is important"' in append_cmd
    # Should NOT have 'export' prefix
    assert not append_cmd.count('export COMMAND')
    assert not append_cmd.count('export DESCRIPTION')
    
    # Verify the flag was set
    assert pm_manager.copied_db_env_vars is True


def test_append_db_env_variables_empty_new_vars(pm_manager, mock_executor):
    """Test that _append_db_env_variables handles case when all vars already exist"""
    # Set up the database run path
    pm_manager.db_run_path = "/fake/db/path"
    
    # Mock the database .env content
    db_env_content = '''POSTGRES_URL="postgresql://localhost:5000/myapp"
POSTGRES_USER="appuser"'''
    
    # Mock existing .env content with all the same variables
    existing_env_content = '''POSTGRES_URL="postgresql://localhost:5000/myapp"
POSTGRES_USER="appuser"
API_KEY="existing_key"'''
    
    # Set up mock responses (only 2 calls expected, no append)
    mock_executor.run_one_shot_command.side_effect = [
        (db_env_content, 0),  # First call: read database .env
        (existing_env_content, 0),  # Second call: read existing .env
    ]
    
    # Call the method
    pm_manager._append_db_env_variables("/test/run/path")
    
    # Verify only 2 calls were made (no append call)
    assert mock_executor.run_one_shot_command.call_count == 2
    
    # Verify the flag was still set
    assert pm_manager.copied_db_env_vars is True


def test_parse_env_content(pm_manager):
    """Test the _parse_env_content helper method"""
    # Test content with various edge cases
    content = '''# This is a comment
export VAR1="value1"
VAR2="value2"
# Another comment
export VAR3="has export in value"
  VAR4="value4"  
# VAR5="commented out"
export VAR6="line with spaces"

# Empty line above
VAR7=no_quotes'''
    
    result = pm_manager._parse_env_content(content)
    
    # Verify correct parsing
    assert len(result) == 6  # Should have 6 variables (excluding comments)
    assert result['VAR1'] == 'VAR1="value1"'
    assert result['VAR2'] == 'VAR2="value2"'
    assert result['VAR3'] == 'VAR3="has export in value"'
    assert result['VAR4'] == 'VAR4="value4"'
    assert result['VAR6'] == 'VAR6="line with spaces"'
    assert result['VAR7'] == 'VAR7=no_quotes'
    
    # Verify comments are not included
    assert 'VAR5' not in result
