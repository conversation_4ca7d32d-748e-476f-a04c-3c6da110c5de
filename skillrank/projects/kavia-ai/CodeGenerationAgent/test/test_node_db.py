
from code_generation_core_agent.database.database_connection import connect_node_db
import asyncio

db = connect_node_db()

async def test_get_work_items():
    architecture_id = 76
    work_items =  await db.get_work_items(architecture_id)
    
    print(work_items)
    
async def test_get_design_with_components():
    architecture_id = 76
    result = await db.get_design_node_with_components(architecture_id)

async def test_get_design_components():
    design_id = 19442
    design_components = await db.get_design_components(design_id)
    print(design_components)
    
if __name__ == "__main__":
    asyncio.run(test_get_work_items())
    asyncio.run(test_get_design_with_components())
    asyncio.run(test_get_design_components())