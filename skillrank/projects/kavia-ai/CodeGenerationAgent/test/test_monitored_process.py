import logging
import os
import asyncio
import sys
import tempfile
import time
from unittest.mock import MagicMock
import pytest

from code_generation_core_agent.agents.preview.monitored_process import MonitoredProcess, MonitoredProcessCallback
from code_generation_core_agent.agents.preview.port_utils import stop_process_using_port
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType



@pytest.fixture(scope="module")
def docker_executor():
    """Fixture to provide a Docker executor for testing"""
    executor = DockerExecutor()
    yield executor
    del executor


@pytest.fixture
def mock_logger():
    """Fixture to provide a logger that prints to stdout"""
    logger = logging.getLogger('test_logger')
    logger.setLevel(logging.DEBUG)

    logger.handlers.clear()

    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)

    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)

    logger.addHandler(handler)
    return logger


@pytest.fixture
def monitored_process(docker_executor, mock_logger):
    """Fixture to provide a basic MonitoredProcess instance with a long-running command"""
    return MonitoredProcess(
        executor=docker_executor,
        cmd="while true; do echo 'Running...'; sleep 1; done",
        working_dir="/tmp",
        logger=mock_logger
    )


class TestMonitoredProcessBasics:
    """Test basic MonitoredProcess functionality"""

    def test_monitored_process_initialization(self, docker_executor, mock_logger):
        """Test that MonitoredProcess initializes correctly"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'test'",
            working_dir="/tmp",
            logger=mock_logger,
            env={"TEST_VAR": "test_value"}
        )

        assert process.executor == docker_executor
        assert process.cmd == "echo 'test'"
        assert process.working_dir == "/tmp"
        assert process.logger == mock_logger
        assert process.env == ["TEST_VAR=test_value"]
        assert process.is_started == False
        assert process.process is None
        assert process.future is None
        assert process.max_lines == 80

    def test_monitored_process_initialization_default_env(self, docker_executor, mock_logger):
        """Test that MonitoredProcess handles default env correctly"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'test'",
            working_dir="/tmp",
            logger=mock_logger
        )

        assert process.env == []

    def test_is_running_initial_state(self, monitored_process):
        """Test is_running returns False initially"""
        assert monitored_process.is_running() == False


@pytest.mark.asyncio
class TestMonitoredProcessAsync:
    """Test async functionality of MonitoredProcess"""

    async def test_start_long_running_process_async(self, docker_executor, mock_logger):
        """Test starting a long-running process asynchronously"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="while true; do echo 'Long running process'; sleep 0.5; done",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()
        assert subprocess is not None
        assert process.is_started == True
        assert process.process is not None

        # Let it run for a bit to verify it's actually running
        await asyncio.sleep(1)
        assert process.is_running() == True

        # Clean up
        await process.shut_down_async()

    async def test_start_background_server_process(self, docker_executor, mock_logger):
        """Test starting a background server-like process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="python3 -m http.server 8188",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()

        assert subprocess is not None
        assert process.is_started == True
        await asyncio.sleep(2)
        assert process.is_running() == True

        # Clean up
        await process.shut_down_async()

    def test_start_background_server_port_open(self, docker_executor, mock_logger):
        import subprocess

        """Test starting a background server-like process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="python3 -m http.server 3001",
            working_dir="/tmp",
            logger=mock_logger
        )

        is_open = process.wait_for_port_open( 3001, timeout=1)

        assert is_open == False  # Port should not be open yet

        res = process.start_process()

        is_open = process.wait_for_port_open( 3001, timeout=5)

        assert is_open == True

    async def test_start_process_with_env_variables(self, docker_executor, mock_logger):
        """Test starting a process that uses environment variables"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="while true; do echo \"SERVER_NAME: $SERVER_NAME, PORT: $PORT\"; sleep 1; done",
            working_dir="/tmp",
            logger=mock_logger,
            env={"SERVER_NAME": "test_server", "PORT": "8188"}
        )

        subprocess = await process.start_process_async()
        assert subprocess is not None
        assert process.is_started == True

        # Let it run to generate some output
        await asyncio.sleep(1.5)

        # Clean up
        await process.shut_down_async()

    async def test_start_process_that_exits_normally(self, docker_executor, mock_logger):
        """Test a process that exits on its own (not long-running)"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'Starting...'; sleep 2; echo 'Finished'; exit 0",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()
        assert subprocess is not None
        assert process.is_started == True

        # Wait for natural exit
        await asyncio.sleep(3)

        # Process should have exited on its own
        # The on_exit callback should have been called
        assert process.is_started == False

    async def test_start_process_that_fails(self, docker_executor, mock_logger):
        """Test a process that fails/crashes"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'Starting...'; sleep 1; exit 1",  # Exit with error
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()
        assert subprocess is not None
        assert process.is_started == True

        # Wait for process to fail
        await asyncio.sleep(2)

        # Process should have exited on its own with error
        assert process.is_started == False

    async def test_start_process_already_started(self, docker_executor, mock_logger):
        """Test that starting an already started process returns the same process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="tail -f /dev/null",  # Runs indefinitely
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess1 = await process.start_process_async()
        subprocess2 = await process.start_process_async()

        assert subprocess1 == subprocess2
        assert process.is_started == True

        # Clean up
        await process.shut_down_async()

    async def test_shut_down_long_running_process(self, docker_executor, mock_logger):
        """Test shutting down a long-running process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="while true; do echo 'Running...'; sleep 0.5; done",
            working_dir="/tmp",
            logger=mock_logger
        )

        await process.start_process_async()
        assert process.is_running() == True

        # Let it run for a bit
        await asyncio.sleep(1)
        assert process.is_running() == True

        await process.shut_down_async()
        assert process.is_running() == False
        assert process.process is None


class TestMonitoredProcessSync:
    """Test synchronous functionality of MonitoredProcess"""

    def test_start_long_running_process_sync(self, docker_executor, mock_logger):
        """Test starting a long-running process synchronously"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="tail -f /dev/null",  # Runs indefinitely
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = process.start_process_sync()
        assert subprocess is not None
        assert process.is_started == True
        assert process.process is not None

        # Clean up
        process.shut_down()

    def test_start_background_service_sync(self, docker_executor, mock_logger):
        """Test starting a background service synchronously"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="while true; do echo 'Service running...'; sleep 2; done",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = process.start_process_sync()
        assert subprocess is not None
        assert process.is_started == True

        # Let it run briefly
        time.sleep(1)
        assert process.is_running() == True

        # Clean up
        process.shut_down()

    def test_start_process_returns_future(self, docker_executor, mock_logger):
        """Test that start_process returns a future for long-running process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="while true; do sleep 1; done",
            working_dir="/tmp",
            logger=mock_logger
        )

        future = process.start_process()
        assert future is not None
        assert hasattr(future, 'result')

        # Wait for completion and clean up
        subprocess = future.result()
        assert subprocess is not None
        process.shut_down()

    def test_shut_down_sync_terminates_process(self, docker_executor, mock_logger):
        """Test that sync shutdown actually terminates a running process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="while true; do echo 'Running...'; sleep 0.5; done",
            working_dir="/tmp",
            logger=mock_logger
        )

        process.start_process_sync()
        assert process.is_running() == True

        # Let it run to ensure it's actually running
        time.sleep(1)
        assert process.is_running() == True

        process.shut_down()
        assert process.is_running() == False

    def test_shut_down_with_future_not_ready(self, docker_executor, mock_logger):
        """Test shutting down when process future is not ready yet"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="sleep 1 && tail -f /dev/null",  # Takes time to start, then runs forever
            working_dir="/tmp",
            logger=mock_logger
        )

        # Start process but don't wait for it
        future = process.start_process()

        # Immediately try to shut down - should wait for process to be ready
        process.shut_down()
        assert process.is_running() == False


class TestMonitoredProcessOutput:
    """Test output handling of MonitoredProcess"""

    def test_output_buffer_initial_state(self, monitored_process):
        """Test that output buffer is initially empty"""
        assert monitored_process.get_last_output() == ""

    def test_on_output_bytes(self, monitored_process):
        """Test on_output callback with bytes input"""
        test_output = b"Line 1\nLine 2\nLine 3"
        monitored_process.on_output(test_output)

        output = monitored_process.get_last_output()
        assert "Line 1" in output
        assert "Line 2" in output
        assert "Line 3" in output

    def test_on_output_string(self, monitored_process):
        """Test on_output callback with string input"""
        test_output = "Line 1\nLine 2\nLine 3"
        monitored_process.on_output(test_output)

        output = monitored_process.get_last_output()
        assert "Line 1" in output
        assert "Line 2" in output
        assert "Line 3" in output

    def test_output_buffer_accumulates_over_time(self, docker_executor, mock_logger):
        """Test that output buffer accumulates output from a running process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="for i in {1..5}; do echo \"Output line $i\"; sleep 0.2; done; tail -f /dev/null",
            working_dir="/tmp",
            logger=mock_logger
        )

        # Start the process
        process.start_process_sync()

        # Let it run and generate output
        time.sleep(1.5)

        # Check that we have accumulated output
        output = process.get_last_output()
        assert len(output) > 0

        # Clean up
        process.shut_down()


    def test_on_exit_callback_with_natural_exit(self, docker_executor, mock_logger):
        """Test on_exit callback when process exits naturally"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'Starting...'; sleep 1; echo 'Exiting...'; exit 0",
            working_dir="/tmp",
            logger=mock_logger
        )

        # Start the process
        process.start_process_sync()

        # Wait for natural exit
        time.sleep(2)

        # Process should have exited and updated state
        assert process.is_started == False
        assert process.process is None


class TestMonitoredProcessContextManager:
    """Test context manager functionality"""

    def test_context_manager_with_long_running_process(self, docker_executor, mock_logger):
        """Test context manager with long-running process"""
        with MonitoredProcess(
                executor=docker_executor,
                cmd="while true; do echo 'Context manager test'; sleep 1; done",
                working_dir="/tmp",
                logger=mock_logger
        ) as process:
            assert process.is_running() == True
            # Let it run briefly
            time.sleep(1)
            assert process.is_running() == True

        # Should be cleaned up after exiting context
        assert process.is_running() == False

    def test_context_manager_with_exception(self, docker_executor, mock_logger):
        """Test context manager with exception during execution"""
        try:
            with MonitoredProcess(
                    executor=docker_executor,
                    cmd="tail -f /dev/null",  # Runs indefinitely
                    working_dir="/tmp",
                    logger=mock_logger
            ) as process:
                assert process.is_running() == True
                time.sleep(0.5)  # Let it start running
                assert process.is_running() == True
                raise ValueError("Test exception")
        except ValueError:
            pass

        # Should still be cleaned up after exception
        assert process.is_running() == False


@pytest.mark.asyncio
class TestMonitoredProcessIntegration:
    """Integration tests with realistic long-running processes"""

    async def test_web_server_process(self, docker_executor, mock_logger):
        """Test monitoring a web server process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="python3 -m http.server 8081",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()
        assert process.is_running() == True

        # Let server start up and run
        await asyncio.sleep(3)
        assert process.is_running() == True

        # Check that we have some output (server startup messages)
        output = process.get_last_output()

        assert len(output) > 0

        await process.shut_down_async()
        assert process.is_running() == False

    async def test_source_env_file_in_subprocess(self, docker_executor, mock_logger):
        """Test sourcing an environment file in a subprocess"""
        temp_dir = tempfile.mkdtemp(dir='/tmp/')
        env_file_path = f"{temp_dir}/.env"

        try:
            # Create a test .env file
            with open(env_file_path, 'w') as f:
                f.write("SOME_ENV_FROM_THE_FILE=hello_from_env_file\n")
                f.write("ANOTHER_VAR=test_value_123\n")

            process = MonitoredProcess(
                executor=docker_executor,
                cmd=f"source {env_file_path} && echo \"SOME_ENV_FROM_THE_FILE is: $SOME_ENV_FROM_THE_FILE\" && sleep 0.5 ",
                working_dir=temp_dir,
                logger=mock_logger
            )

            await process.start_process_async()

            # Let the command complete
            await asyncio.sleep(1)

            # Check that the sourced environment variable was used
            output = process.get_last_output()

            print("Server output:", output)

            assert "SOME_ENV_FROM_THE_FILE is: hello_from_env_file" in output

            await process.shut_down_async()

        finally:
            # Clean up
            if os.path.exists(env_file_path):
                os.remove(env_file_path)
            os.rmdir(temp_dir)

    async def test_background_log_generator(self, docker_executor, mock_logger):
        """Test monitoring a background process that generates logs"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="while true; do echo \"$(date): Background task running\"; sleep 1; done",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()
        assert process.is_running() == True

        # Let it run and generate logs
        await asyncio.sleep(3)
        assert process.is_running() == True

        # Should have accumulated output
        output = process.get_last_output()
        assert "Background task running" in output

        await process.shut_down_async()

    async def test_process_that_crashes_after_running(self, docker_executor, mock_logger):
        """Test monitoring a process that runs then crashes"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'Starting service...'; sleep 2; echo 'Service running...'; sleep 1; echo 'Service crashed!'; exit 1",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()
        assert process.is_running() == True

        # Wait for it to crash
        await asyncio.sleep(5)

        # Process should have exited on its own
        assert process.is_started == False

        # Should have captured the output
        output = process.get_last_output()
        assert "Starting service" in output
        assert "Service crashed" in output

    async def test_monitored_process_failed(self, docker_executor, mock_logger):
        """Test monitoring a process that runs then crashes"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="this_is_a_non_existent_command; echo 'This should not run'",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()
        assert process.is_running() == True

        await asyncio.sleep(1)

        assert process.is_started == False

        output = process.get_last_output()

        assert(process.is_running() == False)
        assert(process.is_failed() == True)

    async def test_monitored_process_succeded(self, docker_executor, mock_logger):
        """Test monitoring a process that runs then crashes"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="ls /tmp",
            working_dir="/tmp",
            logger=mock_logger
        )

        subprocess = await process.start_process_async()
        assert process.is_running() == True

        # Wait for it to crash
        await asyncio.sleep(1)

        output = process.get_last_output()
        assert(process.is_running() == False)
        assert(process.is_failed() == False)


    async def test_database_like_process(self, docker_executor, mock_logger):
        """Test monitoring a database-like process that stays running"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'Database starting...'; while true; do echo \"$(date): Database ready for connections\"; sleep 2; done",
            working_dir="/tmp",
            logger=mock_logger,
            env={"DB_NAME": "test_db", "DB_PORT": "5432"}
        )

        await process.start_process_async()
        assert process.is_running() == True

        # Let it run to establish it's working
        await asyncio.sleep(3)
        assert process.is_running() == True

        # Check output contains expected messages
        output = process.get_last_output()
        assert "Database starting" in output
        assert "ready for connections" in output

        await process.shut_down_async()

    async def test_file_processing_workflow(self, docker_executor, mock_logger):
        """Test monitoring a file processing workflow"""
        temp_dir = tempfile.mkdtemp(dir='/tmp/')

        # Create some test files
        for i in range(3):
            with open(f"{temp_dir}/input_{i}.txt", 'w') as f:
                f.write(f"Test content {i}\n")

        process = MonitoredProcess(
            executor=docker_executor,
            cmd=f"while true; do for file in {temp_dir}/input_*.txt; do echo \"Processing $file\"; sleep 1; done; echo 'Batch complete, waiting...'; sleep 2; done",
            working_dir=temp_dir,
            logger=mock_logger
        )

        await process.start_process_async()
        assert process.is_running() == True

        # Let it process files
        await asyncio.sleep(5)

        # Check output shows file processing
        output = process.get_last_output()
        assert "Processing" in output
        assert "Batch complete" in output

        await process.shut_down_async()

        # Clean up
        for i in range(3):
            os.remove(f"{temp_dir}/input_{i}.txt")
        os.rmdir(temp_dir)

    async def test_environment_dependent_service(self, docker_executor, mock_logger):
        """Test that environment variables are properly used by long-running process"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo \"Starting service on port $SERVICE_PORT\"; while true; do echo \"$(date): Service '$SERVICE_NAME' running on port $SERVICE_PORT\"; sleep 1; done",
            working_dir="/tmp",
            logger=mock_logger,
            env={"SERVICE_NAME": "test_service", "SERVICE_PORT": "9090"}
        )

        await process.start_process_async()
        assert process.is_running() == True

        # Let it run and use env vars
        await asyncio.sleep(2)

        output = process.get_last_output()
        assert "test_service" in output
        assert "9090" in output

        await process.shut_down_async()


    async def test_file_operations(self, docker_executor, mock_logger):
        """Test with file operations"""
        temp_dir = tempfile.mkdtemp(dir='/tmp/')

        process = MonitoredProcess(
            executor=docker_executor,
            cmd=f"echo 'Test content' > {temp_dir}/test_file.txt && cat {temp_dir}/test_file.txt",
            working_dir=temp_dir,
            logger=mock_logger
        )

        await process.start_process_async()

        # Let the command complete
        await asyncio.sleep(0.5)

        await process.shut_down_async()

        # Verify file was created
        assert os.path.exists(f"{temp_dir}/test_file.txt")

        # Clean up
        os.remove(f"{temp_dir}/test_file.txt")
        os.rmdir(temp_dir)

    async def test_environment_variables(self, docker_executor, mock_logger):
        """Test that environment variables are properly passed"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo \"TEST_VAR is: $TEST_VAR\"",
            working_dir="/tmp",
            logger=mock_logger,
            env={"TEST_VAR": "test_value_123"}
        )

        await process.start_process_async()

        # Let the command complete
        await asyncio.sleep(0.5)

        await process.shut_down_async()

    async def test_working_directory(self, docker_executor, mock_logger):
        """Test that working directory is respected"""
        temp_dir = tempfile.mkdtemp(dir='/tmp/')

        process = MonitoredProcess(
            executor=docker_executor,
            cmd="pwd",
            working_dir=temp_dir,
            logger=mock_logger
        )

        await process.start_process_async()

        # Let the command complete
        await asyncio.sleep(0.5)

        await process.shut_down_async()

        # Clean up
        os.rmdir(temp_dir)


class TestMonitoredProcessErrorHandling:
    """Test error handling scenarios"""

    def test_shut_down_not_started(self, monitored_process):
        """Test shutting down a process that was never started"""
        # Should not raise an exception
        monitored_process.shut_down()
        assert monitored_process.is_running() == False

    @pytest.mark.asyncio
    async def test_shut_down_async_not_started(self, monitored_process):
        """Test async shutting down a process that was never started"""
        # Should not raise an exception
        await monitored_process.shut_down_async()
        assert monitored_process.is_running() == False

    def test_multiple_start_calls(self, docker_executor, mock_logger):
        """Test calling start multiple times"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="sleep 2",
            working_dir="/tmp",
            logger=mock_logger
        )

        future1 = process.start_process()
        future2 = process.start_process()

        assert future1 == future2

        # Clean up
        process.shut_down()

    def test_multiple_shutdown_calls(self, docker_executor, mock_logger):
        """Test calling shutdown multiple times"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'test'",
            working_dir="/tmp",
            logger=mock_logger
        )

        process.start_process_sync()

        # Multiple shutdowns should not cause issues
        process.shut_down()
        process.shut_down()
        process.shut_down()

        assert process.is_running() == False


class TestMonitoredProcessCallback:
    """Test MonitoredProcessCallback functionality"""

    def test_callback_on_successful_exit(self, docker_executor, mock_logger):
        """Test callback is called when process exits successfully"""
        callback_called = False
        received_return_code = None
        received_output = None
        received_token = None

        class TestCallback(MonitoredProcessCallback):
            def on_exit(self, return_code: int, output: str, token: object):
                nonlocal callback_called, received_return_code, received_output, received_token
                callback_called = True
                received_return_code = return_code
                received_output = output
                received_token = token

        test_token = {"test": "data", "id": 123}
        callback = TestCallback(test_token)

        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'success output'; exit 0",
            working_dir="/tmp",
            logger=mock_logger,
            error_callback=callback
        )

        process.start_process_sync()
        time.sleep(1)  # Wait for process to complete

        assert callback_called == True
        assert received_return_code == 0
        assert "success output" in received_output
        assert received_token == test_token

    def test_callback_on_failed_exit(self, docker_executor, mock_logger):
        """Test callback is called when process fails"""
        callback_called = False
        received_return_code = None
        received_output = None

        class TestCallback(MonitoredProcessCallback):
            def on_exit(self, return_code: int, output: str, token: object):
                nonlocal callback_called, received_return_code, received_output
                callback_called = True
                received_return_code = return_code
                received_output = output

        callback = TestCallback("failure_token")

        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'error output'; exit 1",
            working_dir="/tmp",
            logger=mock_logger,
            error_callback=callback
        )

        process.start_process_sync()
        time.sleep(1)  # Wait for process to complete

        assert callback_called == True
        assert received_return_code == 1
        assert "error output" in received_output

    def test_process_without_callback(self, docker_executor, mock_logger):
        """Test process works normally without callback"""
        process = MonitoredProcess(
            executor=docker_executor,
            cmd="echo 'no callback'; exit 0",
            working_dir="/tmp",
            logger=mock_logger,
            error_callback=None
        )

        process.start_process_sync()
        time.sleep(1)  # Wait for process to complete

        # Should not crash and should complete normally
        assert process.is_running() == False
        assert process.is_failed() == False

if __name__ == "__main__":
    pytest.main([__file__, "-v"])