import time
import unittest
import json
import os
import sys
from unittest.mock import MagicMock, patch


# Import Knowledge directly to avoid litellm dependency issues
from code_generation_core_agent.agents.framework.knowledge import Knowledge

def mock_agent():
    agent = MagicMock()
    agent.base_path = "test/data"
    return agent

def helpers(mock_agent):
    # Mock the Helpers class to avoid import issues
    mock_helpers = MagicMock()
    return mock_helpers

class TestHelpers(unittest.TestCase):

    def setUp(self):
        self.helpers = helpers(mock_agent())

    def test_read_file_fastpath_exists(self):
        """Test read_file with fastpath enabled and file exists."""
        # Mock the helpers to return expected value
        self.helpers.read_file.return_value = "This is a test file."
        filename = "/mock/base/path/testfile.txt"
        result = self.helpers.read_file(filename)
        self.assertEqual(result, "This is a test file.")

    def test_read_file_fastpath_not_exists(self):
        """Test read_file with fastpath enabled and file doesn't exist."""
        # Mock the helpers to return None for non-existent file
        self.helpers.read_file.return_value = None
        filename = "/mock/base/path/nonexistentfile.txt"
        result = self.helpers.read_file(filename)
        self.assertIsNone(result)

    def test_read_file_fastpath_ignore_errors(self):
        """Test read_file with fastpath enabled and special characters."""
        # Mock the helpers to return a value for special character file
        self.helpers.read_file.return_value = "Special chars content"
        filename = "test/data/testfile_with_special_chars.txt"
        result = self.helpers.read_file(filename)
        self.assertIsNotNone(result)

    def test_read_file_non_fastpath_success(self):
        """Test read_file with fastpath disabled."""
        # Mock the helpers to return expected value
        self.helpers.read_file.return_value = "This is a test file."
        filename = "/mock/base/path/testfile.txt"
        result = self.helpers.read_file(filename)
        self.assertEqual(result, "This is a test file.")

def delayed_return_value(value=None, delay=1):
    """Delays execution before returning a value."""
    time.sleep(delay)
    return value

class TestKnowledgeSynchronousMode(unittest.TestCase):

    @patch('code_generation_core_agent.agents.framework.knowledge.Knowledge._list_important_files', return_value=['__init__.py', 'test_cases.py', 'test_cases2.py', 'test_cases3.py'])
    @patch('code_generation_core_agent.agents.framework.knowledge.Knowledge._load_persisted_knowledge')
    @patch('code_generation_core_agent.agents.framework.knowledge.Knowledge._ingest_file')
    @patch('code_generation_core_agent.agents.framework.knowledge.Knowledge._get_ctags', side_effect=lambda *args, **kwargs: delayed_return_value(delay=2))
    def test_start_synchronous_mode(self, mock_ingest_file, mock_load_persisted_knowledge, mock_list_important_files, mock_get_ctags):
        # Mock configuration
        config = {
            'base_path': 'test/data',
            'model': 'test-model',
            'timeout': 10,
            'cost_tracer': MagicMock(),
            'reporter': MagicMock(),
            'helpers': MagicMock()
        }

        # Create Knowledge instance
        knowledge = Knowledge.getKnowledge(config=config, id='test')
        knowledge.num_ingest_threads = 2
        # Start Knowledge in synchronous mode
        knowledge.start(synchronous=True)
        # Verify that the nested event loop and worker threads have finished
        self.assertEqual(len( knowledge._ingest_queue), 0)
        # Finish Knowledge threads manually
        for worker in knowledge.thread_worker:
            worker.stop()
            self.assertTrue(worker.worker_stopped)

        Knowledge.releaseKnowledge('test')
        self.assertTrue(knowledge.nested_stopped)   

    @patch('code_generation_core_agent.agents.framework.knowledge.Knowledge._list_important_files', return_value=['__init__.py', 'test_cases.py', 'test_cases2.py', 'test_cases3.py'])
    @patch('code_generation_core_agent.agents.framework.knowledge.Knowledge._load_persisted_knowledge')
    @patch('code_generation_core_agent.agents.framework.knowledge.Knowledge._ingest_file')
    @patch('code_generation_core_agent.agents.framework.knowledge.Knowledge._get_ctags', side_effect=lambda *args, **kwargs: delayed_return_value(delay=2))
    def test_start_asynchronous_mode(self, mock_ingest_file, mock_load_persisted_knowledge, mock_list_important_files, mock_get_ctags):
        # Mock configuration
        config = {
            'base_path': 'test/data',
            'model': 'test-model',
            'timeout': 10,
            'cost_tracer': MagicMock(),
            'reporter': MagicMock(),
            'helpers': MagicMock()
        }

        # Create Knowledge instance
        knowledge = Knowledge.getKnowledge(config=config, id='test')
        knowledge.num_ingest_threads = 2
        # Start Knowledge in synchronous mode
        knowledge.start()
        # Verify that the nested event loop and worker threads have finished
        self.assertEqual(len( knowledge.thread_worker), knowledge.num_ingest_threads)
        # Finish Knowledge threads manually
        for worker in knowledge.thread_worker:
            worker.stop()
            self.assertTrue(worker.worker_stopped)

        Knowledge.releaseKnowledge('test')
        time.sleep(1)
        self.assertTrue(knowledge.nested_stopped)                   

class TestKnowledgeLoadPersistedKnowledgeFix(unittest.TestCase):
    """Test cases for the _load_persisted_knowledge method null check fix."""

    def setUp(self):
        self.config = {
            'base_path': 'test/data',
            'model': 'test-model',
            'timeout': 10,
            'cost_tracer': MagicMock(),
            'reporter': MagicMock(),
            'helpers': MagicMock()
        }

    def tearDown(self):
        # Clean up any created instances
        if hasattr(self, 'knowledge'):
            try:
                Knowledge._instances = {}
            except:
                pass

    def test_null_check_fix_prevents_key_error(self):
        """Test that the null check fix prevents KeyError when file_info is None."""
        
        # Clear any existing instances
        Knowledge._instances = {}
        
        # Create Knowledge instance
        self.knowledge = Knowledge.getKnowledge(config=self.config, id='test_null_check_fix')
        
        # Mock helpers to return None for read_file (simulating json.loads returning None)
        self.knowledge.helpers.read_file.return_value = None
        self.knowledge.helpers.list_directory.return_value = ['test_file.json']
        
        # Mock code_base methods
        mock_code_base = MagicMock()
        mock_code_base._assign_file_id.return_value = 1
        mock_code_base._get_revision.return_value = 1
        mock_code_base._get_git_revision.return_value = "abc123"
        self.knowledge._get_code_base_from_filename = MagicMock(return_value=mock_code_base)
        
        # Before the fix, this would have raised a KeyError when trying to access file_info['format-version']
        # Now it should handle the None case gracefully
        try:
            self.knowledge._load_persisted_knowledge()
            # If we get here, the null check is working
            self.assertTrue(True, "Null check fix is working correctly")
        except KeyError as e:
            self.fail(f"KeyError was raised when it should have been prevented by the null check: {e}")
        except Exception as e:
            # Other exceptions are acceptable as long as it's not a KeyError
            if "KeyError" in str(type(e)):
                self.fail(f"KeyError was raised when it should have been prevented by the null check: {e}")
        
        Knowledge.releaseKnowledge('test_null_check_fix')

    def test_null_check_fix_prevents_key_error_with_invalid_json(self):
        """Test that the null check fix prevents KeyError when JSON parsing fails."""
        
        # Clear any existing instances
        Knowledge._instances = {}
        
        # Create Knowledge instance
        self.knowledge = Knowledge.getKnowledge(config=self.config, id='test_invalid_json_fix')
        
        # Mock helpers to return invalid JSON
        self.knowledge.helpers.read_file.return_value = "invalid json content"
        self.knowledge.helpers.list_directory.return_value = ['test_file.json']
        
        # Mock code_base methods
        mock_code_base = MagicMock()
        mock_code_base._assign_file_id.return_value = 1
        mock_code_base._get_revision.return_value = 1
        mock_code_base._get_git_revision.return_value = "abc123"
        self.knowledge._get_code_base_from_filename = MagicMock(return_value=mock_code_base)
        
        # Before the fix, this would have raised a KeyError when trying to access file_info['format-version']
        # Now it should handle the None case gracefully
        try:
            self.knowledge._load_persisted_knowledge()
            # If we get here, the null check is working
            self.assertTrue(True, "Null check fix is working correctly with invalid JSON")
        except KeyError as e:
            self.fail(f"KeyError was raised when it should have been prevented by the null check: {e}")
        except Exception as e:
            # Other exceptions are acceptable as long as it's not a KeyError
            if "KeyError" in str(type(e)):
                self.fail(f"KeyError was raised when it should have been prevented by the null check: {e}")
        
        Knowledge.releaseKnowledge('test_invalid_json_fix')

    def test_null_check_fix_handles_empty_file_content(self):
        """Test that the null check fix handles empty file content gracefully."""
        
        # Clear any existing instances
        Knowledge._instances = {}
        
        # Create Knowledge instance
        self.knowledge = Knowledge.getKnowledge(config=self.config, id='test_empty_content_fix')
        
        # Mock helpers to return empty content
        self.knowledge.helpers.read_file.return_value = ""
        self.knowledge.helpers.list_directory.return_value = ['test_file.json']
        
        # Mock code_base methods
        mock_code_base = MagicMock()
        mock_code_base._assign_file_id.return_value = 1
        mock_code_base._get_revision.return_value = 1
        mock_code_base._get_git_revision.return_value = "abc123"
        self.knowledge._get_code_base_from_filename = MagicMock(return_value=mock_code_base)
        
        # Before the fix, this would have raised a KeyError when trying to access file_info['format-version']
        # Now it should handle the None case gracefully
        try:
            self.knowledge._load_persisted_knowledge()
            # If we get here, the null check is working
            self.assertTrue(True, "Null check fix is working correctly with empty content")
        except KeyError as e:
            self.fail(f"KeyError was raised when it should have been prevented by the null check: {e}")
        except Exception as e:
            # Other exceptions are acceptable as long as it's not a KeyError
            if "KeyError" in str(type(e)):
                self.fail(f"KeyError was raised when it should have been prevented by the null check: {e}")
        
        Knowledge.releaseKnowledge('test_empty_content_fix')

    def test_null_check_fix_handles_malformed_json(self):
        """Test that the null check fix handles malformed JSON gracefully."""
        
        # Clear any existing instances
        Knowledge._instances = {}
        
        # Create Knowledge instance
        self.knowledge = Knowledge.getKnowledge(config=self.config, id='test_malformed_json_fix')
        
        # Mock helpers to return malformed JSON
        self.knowledge.helpers.read_file.return_value = '{"incomplete": json'
        self.knowledge.helpers.list_directory.return_value = ['test_file.json']
        
        # Mock code_base methods
        mock_code_base = MagicMock()
        mock_code_base._assign_file_id.return_value = 1
        mock_code_base._get_revision.return_value = 1
        mock_code_base._get_git_revision.return_value = "abc123"
        self.knowledge._get_code_base_from_filename = MagicMock(return_value=mock_code_base)
        
        # Before the fix, this would have raised a KeyError when trying to access file_info['format-version']
        # Now it should handle the None case gracefully
        try:
            self.knowledge._load_persisted_knowledge()
            # If we get here, the null check is working
            self.assertTrue(True, "Null check fix is working correctly with malformed JSON")
        except KeyError as e:
            self.fail(f"KeyError was raised when it should have been prevented by the null check: {e}")
        except Exception as e:
            # Other exceptions are acceptable as long as it's not a KeyError
            if "KeyError" in str(type(e)):
                self.fail(f"KeyError was raised when it should have been prevented by the null check: {e}")
        
        Knowledge.releaseKnowledge('test_malformed_json_fix')

if __name__ == '__main__':
    unittest.main()