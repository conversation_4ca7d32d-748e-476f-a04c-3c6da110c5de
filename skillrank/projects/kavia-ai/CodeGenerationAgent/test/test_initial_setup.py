import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from code_generation_core_agent.agents.framework.micro_agent_registry import MicroAgentType
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent
from code_generation_core_agent.agents.tools.init.universal_project_setup import (
    wait_for_project_setup, 
    ProjectSetupState,
    _initialize_component
)
import json


class TestRunInitialSetupAgent:
    """Test suite for _run_initial_setup_agent exit conditions"""

    @pytest.fixture
    def mock_task_execution_agent(self):
        """Create a mock TaskExecutionAgent with necessary attributes"""
        from unittest.mock import Mock

        agent = Mock()
        agent.debug_mode = Mock()
        agent.debug_mode.skip_initial_setup = False
        agent.micro_agent_registry = Mock()
        agent.logger = Mock()
        agent._send_message_to_user = Mock()
        agent.reporter = Mock()
        agent.reporter.task_start_callback = Mock()

        return agent

    @pytest.fixture
    def mock_initial_setup_agent(self):
        """Create a mock InitialSetupAgent"""
        agent = Mock()
        agent.get_action_count.return_value = 6  # Default action count
        agent.get_action.return_value = ("init-env", "Test action message")
        agent.process_request = AsyncMock(return_value="success")
        return agent

    @pytest.fixture
    def setup_agent_registry(self, mock_task_execution_agent, mock_initial_setup_agent):
        """Setup the micro agent registry with mocked initial setup agent"""
        agent_map = {MicroAgentType.INITIAL_SETUP_MICRO_AGENT.value: mock_initial_setup_agent}
        mock_task_execution_agent.micro_agent_registry.get_agents.return_value = agent_map
        mock_task_execution_agent.micro_agent_registry.get_capabilities.return_value = {}
        return mock_task_execution_agent, mock_initial_setup_agent

    @pytest.mark.asyncio
    async def test_exits_when_max_step_count_reaches_zero(self, setup_agent_registry):
        """Test that _run_initial_setup_agent exits when initial_setup_max_step_count reaches 0"""
        task_agent, initial_setup_agent = setup_agent_registry


        initial_setup_agent.get_action.return_value = ("init-env", "Test message")

        initial_setup_agent.process_request = AsyncMock(return_value="incomplete")

        work_item = {"test": "item"}
        agent_name = "CodeGeneration"

        await TaskExecutionAgent._run_initial_setup_agent(task_agent, work_item, agent_name)

        expected_calls = 6 + 5  # action_count + 5
        assert initial_setup_agent.process_request.call_count == expected_calls

        assert initial_setup_agent.get_action.call_count == expected_calls


    @pytest.mark.asyncio
    async def test_handles_exception_in_process_request(self, setup_agent_registry):
        """Test that _run_initial_setup_agent handles exceptions gracefully"""
        task_agent, initial_setup_agent = setup_agent_registry

        # Set up the agent to raise an exception
        initial_setup_agent.get_action.return_value = ("init-env", "Test message")
        initial_setup_agent.process_request = AsyncMock(side_effect=Exception("Test exception"))

        work_item = {"test": "item"}
        agent_name = "CodeGeneration"

        # Call the method - it should not raise an exception
        await TaskExecutionAgent._run_initial_setup_agent(task_agent, work_item, agent_name)

        # Verify that the exception was logged
        task_agent.logger.error.assert_called()

        # Verify that process_request was called only once before the exception
        assert initial_setup_agent.process_request.call_count == 1

    @pytest.mark.asyncio
    async def test_only_runs_for_code_generation_agent(self, setup_agent_registry):
        """Test that _run_initial_setup_agent only runs for CodeGeneration agent"""
        task_agent, initial_setup_agent = setup_agent_registry

        work_item = {"test": "item"}
        agent_name = "SomeOtherAgent"  # Not CodeGeneration

        # Call the method
        await TaskExecutionAgent._run_initial_setup_agent(task_agent, work_item, agent_name)

        # Verify that get_action was never called since agent_name != "CodeGeneration"
        initial_setup_agent.get_action.assert_not_called()

    @pytest.mark.asyncio
    async def test_step_count_calculation(self, setup_agent_registry):
        """Test that initial_setup_max_step_count is calculated correctly"""
        task_agent, initial_setup_agent = setup_agent_registry

        # Set a specific action count
        action_count = 8
        initial_setup_agent.get_action_count.return_value = action_count

        # Always return an action to test the full loop
        initial_setup_agent.get_action.return_value = ("init-env", "Test message")
        initial_setup_agent.process_request = AsyncMock(return_value="success")

        work_item = {"test": "item"}
        agent_name = "CodeGeneration"

        # Call the method
        await TaskExecutionAgent._run_initial_setup_agent(task_agent, work_item, agent_name)

        # Verify that process_request was called exactly (action_count + 5) times
        expected_max_steps = action_count + 5  # 8 + 5 = 13
        assert initial_setup_agent.process_request.call_count == expected_max_steps


class TestWaitForProjectSetupWithState:
    """Test suite for wait_for_project_setup with ProjectSetupState"""

    @pytest.fixture(autouse=True)
    def reset_project_setup_state(self):
        """Reset the ProjectSetupState singleton before each test"""
        # Reset the singleton instance
        ProjectSetupState._instance = None
        yield
        # Clean up after test
        ProjectSetupState._instance = None

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger"""
        return Mock()

    @pytest.fixture
    def mock_project_path(self, tmp_path):
        """Create a temporary project path"""
        project_path = tmp_path / "test_project"
        project_path.mkdir(parents=True, exist_ok=True)
        return str(project_path)

    def test_wait_skipped_when_universal_init_not_invoked(self, mock_project_path, mock_logger):
        """Test that wait_for_project_setup exits early when universal init has not been invoked"""
        # Ensure universal init has not been invoked
        assert not ProjectSetupState().is_universal_init_invoked()

        # Call wait_for_project_setup
        wait_for_project_setup(mock_project_path, timeout=10, logger=mock_logger)

        # Verify that the function logged the skip message and returned early
        mock_logger.info.assert_called_once_with("Project setup not invoked, skipping wait for setup completion")

    @patch('code_generation_core_agent.agents.tools.init.universal_project_setup.Path')
    @patch('code_generation_core_agent.agents.tools.init.universal_project_setup.time.time')
    @patch('code_generation_core_agent.agents.tools.init.universal_project_setup.time.sleep')
    def test_wait_happens_when_universal_init_invoked(self, mock_sleep, mock_time, mock_path_class, 
                                                      mock_project_path, mock_logger):
        """Test that wait_for_project_setup waits when universal init has been invoked"""
        # Set up universal init as invoked
        ProjectSetupState().set_universal_init_invoked()
        assert ProjectSetupState().is_universal_init_invoked()

        # Mock the build config file
        mock_init_build_path = MagicMock()
        mock_init_build_path.exists.return_value = True
        mock_init_build_path.read_text.return_value = json.dumps({
            "working_directory": "/test/working/dir"
        })

        # Mock the lock file
        mock_lock_file = MagicMock()
        mock_lock_file.exists.side_effect = [False, False, True]  # Found on third check
        mock_lock_file.read_text.return_value = "SUCCESS"

        # Set up Path mocks
        mock_path_instance = MagicMock()
        mock_path_class.return_value = mock_path_instance
        mock_path_instance.__truediv__.side_effect = lambda x: (
            mock_init_build_path if x == ".init/.init-build-cmd" else mock_lock_file
        )

        # Mock time to simulate timeout
        mock_time.side_effect = [0, 1, 2, 3]  # Incremental time values

        # Call wait_for_project_setup
        result = wait_for_project_setup(mock_project_path, timeout=10, logger=mock_logger)

        # Verify that the function did NOT skip
        assert not any(
            call[0][0] == "Project setup not invoked, skipping wait for setup completion" 
            for call in mock_logger.info.call_args_list
        )

        # Verify that it checked for the lock file
        assert mock_lock_file.exists.called
        assert result is True

    def test_project_setup_state_singleton_behavior(self):
        """Test that ProjectSetupState behaves as a singleton"""
        # Create multiple instances
        state1 = ProjectSetupState()
        state2 = ProjectSetupState()

        # Verify they are the same instance
        assert state1 is state2

        # Verify initial state
        assert not state1.is_universal_init_invoked()

        # Set state in one instance
        state1.set_universal_init_invoked()

        # Verify state is reflected in both instances
        assert state1.is_universal_init_invoked()
        assert state2.is_universal_init_invoked()

    @patch('code_generation_core_agent.agents.tools.init.universal_project_setup._write_initialization_files')
    def test_initialize_component_sets_state(self, mock_write_files, mock_logger):
        """Test that _initialize_component sets the universal init invoked state"""
        # Reset state
        assert not ProjectSetupState().is_universal_init_invoked()

        # Mock executor
        mock_executor = Mock()
        mock_executor.run_one_shot_command.side_effect = [
            ("universalinit==1.0.0", 0),  # pip freeze check
            ("[OUTPUT]{\"template_config\": {}}", 0)  # uniinit command
        ]

        # Mock successful file writing
        mock_write_files.return_value = True

        # Test component
        item = {
            "container_name": "test_component",
            "language": "python",
            "framework": "flask"
        }

        # Call _initialize_component
        result = _initialize_component(
            item, 
            "/test/project/path",
            mock_executor,
            mock_logger,
            framework="flask",
            platform="backend"
        )

        # Verify that universal init state was set
        assert ProjectSetupState().is_universal_init_invoked()
        assert result == "test_component"