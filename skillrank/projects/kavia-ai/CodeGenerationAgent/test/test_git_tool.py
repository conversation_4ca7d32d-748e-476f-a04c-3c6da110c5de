import os
import shutil
import pytest
from unittest.mock import MagicMock
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.git_tool import GitTools
from dotenv import load_dotenv


def load_env():
    """Load environment variables from .env file"""
    dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    load_dotenv(dotenv_path)


load_env()
ACCESS_TOKEN = os.getenv("GITHUB_ACCESS_TOKEN")


@pytest.fixture(scope="session")
def docker_executor():
    executor = DockerExecutor()
    yield executor
    executor.cleanup_all_containers()


class TestGitTools:
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self, docker_executor):
        self.base_path = "/tmp/git_test"
        os.makedirs(self.base_path, exist_ok=True)
        logger = MagicMock()
        llm = MagicMock()
        self.git_tools = GitTools({}, self.base_path, logger, llm=llm, executor=docker_executor,
                                  access_token=ACCESS_TOKEN)

        # Create a temporary directory to act as our "remote" repository
        self.remote_repo_path = os.path.join(self.base_path, "remote_repo")
        os.makedirs(self.remote_repo_path, exist_ok=True)
        os.system(f"git init {self.remote_repo_path}")
        os.system(f"cd {self.remote_repo_path} && touch README.md && git add . && git commit -m 'Initial commit'")

        # Set up the test repository by cloning the "remote" repository
        self.test_repo_path = os.path.join(self.base_path, "test_repo")
        self.git_tools.git_clone(f"file://{self.remote_repo_path}", self.test_repo_path)

        yield

        # Cleanup
        shutil.rmtree(self.base_path, ignore_errors=True)

    def test_git_clone(self):
        new_repo_path = os.path.join(self.base_path, "new_clone")
        result = self.git_tools.git_clone(f"file://{self.remote_repo_path}", new_repo_path)
        assert result.startswith("Cloned repository")
        assert os.path.exists(new_repo_path)

    def test_git_pull(self):
        result = self.git_tools.git_pull(self.test_repo_path)
        assert result == "Git pull successful."

    def test_git_status(self):
        result = self.git_tools.git_status(self.test_repo_path)
        assert "On branch main" in result or "On branch master" in result

    def test_git_branch(self):
        result = self.git_tools.git_branch(self.test_repo_path)
        assert "main" in result or "master" in result

    def test_git_checkout_new_branch(self):
        new_branch = "test-branch"
        result = self.git_tools.git_checkout(new_branch, self.test_repo_path, new_branch=True)
        assert result.startswith(f"Checked out new branch {new_branch}")

    def test_git_add_all(self):
        with open(os.path.join(self.test_repo_path, "test.txt"), "w") as f:
            f.write("Test content")
        result = self.git_tools.git_add_all(self.test_repo_path)
        assert result == "Added all files to Git index."

    def test_git_add(self):
        file_path = "test_add.txt"
        with open(os.path.join(self.test_repo_path, file_path), "w") as f:
            f.write("Test content for add")
        result = self.git_tools.git_add(file_path, self.test_repo_path)
        assert result == f"Added {file_path} to Git index."

    def test_git_commit(self):
        with open(os.path.join(self.test_repo_path, "test_commit.txt"), "w") as f:
            f.write("Test content for commit")
        self.git_tools.git_add_all(self.test_repo_path)
        commit_message = "Test commit"
        result = self.git_tools.git_commit(commit_message, self.test_repo_path)
        assert result == f"Committed changes with message: {commit_message}"
        status_result = self.git_tools.git_status(self.test_repo_path)
        assert "nothing to commit, working tree clean" in status_result

    def test_git_push(self):
        result = self.git_tools.git_push(self.test_repo_path)
        assert result in ["Pushed changes to remote.", "Everything up-to-date"]

    def test_git_revert(self):
        with open(os.path.join(self.test_repo_path, "revert_test.txt"), "w") as f:
            f.write("Content to revert")
        self.git_tools.git_add_all(self.test_repo_path)
        self.git_tools.git_commit("Commit to revert", self.test_repo_path)
        result = self.git_tools.git_revert(repository_path=self.test_repo_path)
        assert result == "Reverted last commit"

    def test_git_merge_squash(self):
        new_branch = "squash-test"
        self.git_tools.git_checkout(new_branch, self.test_repo_path, new_branch=True)
        with open(os.path.join(self.test_repo_path, "squash_test.txt"), "w") as f:
            f.write("Content to squash")
        self.git_tools.git_add_all(self.test_repo_path)
        self.git_tools.git_commit("Commit to squash", self.test_repo_path)
        self.git_tools.git_checkout("main", self.test_repo_path)
        result = self.git_tools.git_merge_squash(new_branch, self.test_repo_path)
        assert result == f"Squash merged branch {new_branch}."