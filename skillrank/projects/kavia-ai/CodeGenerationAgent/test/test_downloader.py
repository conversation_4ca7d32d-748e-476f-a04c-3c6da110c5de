import unittest
from unittest.mock import patch, MagicMock, mock_open, call
import os
import time
import requests
from pathlib import Path
import io
import logging
from contextlib import contextmanager

from code_generation_core_agent.agents.tools.downloader import Downloader


class TestDownloader(unittest.TestCase):
    """Test cases for the Downloader class's download method."""

    def setUp(self):
        """Set up test environment before each test."""
        self.url = "https://example.com/index.html"
        
        # Create a logger for testing
        self.logger = logging.getLogger("TestLogger")
        self.logger.setLevel(logging.INFO)
        
        # Create the downloader with our test logger
        self.downloader = Downloader(logger=self.logger)
        
        # Create mock response for requests
        self.mock_response = MagicMock()
        self.mock_response.status_code = 200
        self.mock_response.headers = {
            "Content-Type": "text/html",
            "Content-Length": "1024",
        }
        self.mock_response.raise_for_status.return_value = None
        self.mock_response.iter_content.return_value = [b"chunk1", b"chunk2", b"chunk3"]
        
        # Prepare example HTML content for realistic responses
        self.html_content = b'<!doctype html>\n<html>\n<head>\n    <title>Example Domain</title>\n</head>\n<body>\n    <h1>Example Page</h1>\n</body>\n</html>'
        
    def test_download_to_directory(self):
        """Test downloading to a directory."""
        with patch("requests.Session") as mock_session, \
             patch("builtins.open", mock_open()) as mock_file, \
             patch("pathlib.Path.is_dir") as mock_is_dir:
            
            # Configure mocks
            session_instance = mock_session.return_value
            session_instance.head.return_value = self.mock_response
            session_instance.get.return_value = self.mock_response
            mock_is_dir.return_value = True
            
            # Call download with directory path
            dest_dir = Path("/tmp/downloads")
            result = self.downloader.download(self.url, dest_dir)
            
            # Expected file name from URL
            expected_file_path = dest_dir / "index.html"
            
            # Verify result
            self.assertEqual(result, str(expected_file_path))
            
            # Verify file operations
            mock_file.assert_called_once_with(expected_file_path, 'wb')
    
    def test_download_with_custom_filename(self):
        """Test downloading with a custom filename."""
        with patch("requests.Session") as mock_session, \
             patch("builtins.open", mock_open()) as mock_file, \
             patch("pathlib.Path.is_dir") as mock_is_dir:
            
            # Configure mocks
            session_instance = mock_session.return_value
            session_instance.head.return_value = self.mock_response
            session_instance.get.return_value = self.mock_response
            mock_is_dir.return_value = True
            
            # Call download with directory path and custom filename
            dest_dir = Path("/tmp/downloads")
            custom_filename = "custom_name.html"
            result = self.downloader.download(self.url, dest_dir, filename=custom_filename)
            
            # Expected file path with custom name
            expected_file_path = dest_dir / custom_filename
            
            # Verify result
            self.assertEqual(result, str(expected_file_path))
            
            # Verify file operations
            mock_file.assert_called_once_with(expected_file_path, 'wb')
    
    
    def test_download_with_progress_display(self):
        """Test download with progress display enabled."""
        with patch("requests.Session") as mock_session, \
             patch("builtins.open", mock_open()) as mock_file:
            
            # Use a more complex approach for mocking time.time()
            orig_time = time.time
            time_values = [0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5]  # More values to handle more calls
            time_iter = iter(time_values)
            
            def mock_time_func():
                try:
                    return next(time_iter)
                except StopIteration:
                    return orig_time()
            
            with patch("time.time", side_effect=mock_time_func):
                # Configure response with content length
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.headers = {"Content-Length": "30000"}
                mock_response.raise_for_status.return_value = None
                
                # Create 3 chunks of 10KB each
                chunk_size = 10000
                mock_response.iter_content.return_value = [b"a" * chunk_size, b"b" * chunk_size, b"c" * chunk_size]
                
                # Configure session mock - skip HEAD
                session_instance = mock_session.return_value
                session_instance.get.return_value = mock_response
                
                # Mock the logger.info to capture progress messages
                with patch.object(self.logger, 'info') as mock_log_info:
                    # Call download with progress display
                    result = self.downloader.download(self.url, show_progress=True)
                    
                    # Verify some log calls were made
                    self.assertTrue(mock_log_info.called)
                    
                    # At minimum we should have a starting and completion log
                    self.assertGreaterEqual(mock_log_info.call_count, 2)
                    
                    # Verify successful download
                    self.assertIn("index.html", result)
    
    def test_download_without_progress_display(self):
        """Test download with progress display disabled."""
        with patch("requests.Session") as mock_session, \
             patch("builtins.open", mock_open()) as mock_file:
            
            # Use a more reliable approach to handle time.time() mocking
            orig_time = time.time
            time_values = [0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8]
            time_iter = iter(time_values)
            
            def mock_time_func():
                try:
                    return next(time_iter)
                except StopIteration:
                    return orig_time()
            
            with patch("time.time", side_effect=mock_time_func):
                # Configure response
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.headers = {"Content-Length": "30000"}
                mock_response.raise_for_status.return_value = None
                mock_response.iter_content.return_value = [b"a" * 10000, b"b" * 10000, b"c" * 10000]
                
                # Configure session mock - skip HEAD
                session_instance = mock_session.return_value
                session_instance.get.return_value = mock_response
                
                # Mock the logger to verify logging behavior
                with patch.object(self.logger, 'info') as mock_log_info:
                    # Call download with progress display disabled
                    result = self.downloader.download(self.url, show_progress=False)
                    
                    # Verify some logging still happens (start and completion)
                    self.assertTrue(mock_log_info.called)
                    
                    # Verify successful download
                    self.assertIn("index.html", result)
                    
                    # We expect at least 2 log calls: start and completion
                    self.assertGreaterEqual(mock_log_info.call_count, 2)


if __name__ == "__main__":
    unittest.main()