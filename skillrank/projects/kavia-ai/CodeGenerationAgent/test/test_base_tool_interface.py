import unittest
from abc import ABC
from unittest.mock import MagicMock, patch

import pytest

from code_generation_core_agent.agents.tools.AgentPreProcessor import AgentPreProcessor, FunctionCallDescription
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.combined_tools import CombinedTools, ToolPreProcessor
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.visible_shell_tools import VisibleShellTools


class DemoPreprocessingTool(ToolPreProcessor):

    def __init__(self):
        super().__init__()

    def pre_process_function_call(self, function_name, **data):
        return data

    def post_process_function_response(self, function_name, arguments, data):
        return data

    def pre_process_function_registration(self, data):
        return data


class SecondDemoTool(BaseToolInterface, ABC):
    call_count = 0

    def get_tool_name(self):
        return "secondarytool"

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)

    @register_function(
        description="This is a sample function to increment a number",
        parameters={
            "type": "object",
            "properties": {
            },
            "required": []
        }
    )
    def sample_function(self):
        self.call_count += 1
        return self.call_count

    @register_function(
        description="This is a sample function to increment a number",
        parameters={
            "type": "object",
            "properties": {
                "number": {"type": "integer", "description": "number to increment"},
            },
            "required": ["number"]
        }
    )
    def sample_function_with_args(self, number):
        self.call_count += number
        return self.call_count

    # Sample function with both mandatory and optional arguments
    @register_function(
        description="This is a sample function to increment a number",
        parameters={
            "type": "object",
            "properties": {
                "number": {"type": "integer", "description": "number to increment"},
                "optional_number": {"type": "integer", "description": "optional number to increment", "default": 0}
            },
            "required": ["number"]
        }
    )
    def sample_function_with_mandatory_and_optional_args(self, number, optional_number=0):
        self.call_count += number + optional_number
        return self.call_count


class DemoTool(BaseToolInterface, ABC):
    call_count = 0

    def get_tool_name(self):
        return "demo"

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)

    @register_function(
        description="This is a sample function to increment a number",
        parameters={
            "type": "object",
            "properties": {
            },
            "required": []
        }
    )
    def sample_function(self):
        self.call_count += 1
        return self.call_count

    @register_function(
        description="This is a sample function to increment a number",
        parameters={
            "type": "object",
            "properties": {
                "number": {"type": "integer", "description": "number to increment"},
            },
            "required": ["number"]
        }
    )
    def sample_function_with_args(self, number):
        self.call_count += number
        return self.call_count

    # Sample function with both mandatory and optional arguments
    @register_function(
        description="This is a sample function to increment a number",
        parameters={
            "type": "object",
            "properties": {
                "number": {"type": "integer", "description": "number to increment"},
                "optional_number": {"type": "integer", "description": "optional number to increment", "default": 0}
            },
            "required": ["number"]
        }
    )
    def sample_function_with_mandatory_and_optional_args(self, number, optional_number=0):
        self.call_count += number + optional_number
        return self.call_count

def function_call_callback(call_description: FunctionCallDescription):
    """
    Return the function call description to the user.
    """
    pass

class TestBaseToolInterface(unittest.TestCase):
    def setUp(self):
        self.logger = MagicMock()
        self.callbacks = {"function_call_callback": function_call_callback}
        pass

    def test_agent_pre_processor(self):
        demo_tool = DemoTool({}, "base_path", self.logger)
        second_tool = SecondDemoTool({}, "base_path", self.logger)
        agent_pre_processor = AgentPreProcessor({'function_call_callback': self.callback}, self.logger)

        combined_tool = CombinedTools([demo_tool, second_tool],
                                      [agent_pre_processor],
                                      "base_path", self.logger)
        call_count = combined_tool.call_function("demo_sample_function")
        self.assertEqual(call_count, 1)
        second_tool_call_count = combined_tool.call_function("secondarytool_sample_function")
        self.assertEqual(second_tool_call_count, 1)

    def test_register_callback_functions(self):
        class SampleCallbackHandler:
            def __init__(self):
                self.call_count = 0

            def callback(self, *args, **kwargs):
                self.call_count += 1

        callback_handler = SampleCallbackHandler()

        demo_tool = DemoTool({"test_call": callback_handler.callback}, "base_path", "logger")

        self.assertEqual(demo_tool.get_tool_name(), "demo")
        demo_tool.callback("test_call")

        self.assertEqual(callback_handler.call_count, 1)

    def test_register_functions(self):
        demo_tool = DemoTool({}, "base_path", None)

        self.assertEqual(demo_tool.get_tool_name(), "demo")
        combined_tool = CombinedTools([demo_tool], [], "base_path", None)

        call_count = combined_tool.call_function("demo_sample_function")

        self.assertEqual(call_count, 1)
        self.assertEqual(demo_tool.call_count, 1)

        call_count = combined_tool.call_function("demo_sample_function_with_args", number=5)

        self.assertEqual(call_count, 6)
        self.assertEqual(demo_tool.call_count, 6)

    def test_function_with_mandatory_and_optional_args(self):
        demo_tool = DemoTool({}, "base_path", None)

        self.assertEqual(demo_tool.get_tool_name(), "demo")
        combined_tool = CombinedTools([demo_tool], [], "base_path", None)

        call_count = combined_tool.call_function("demo_sample_function_with_mandatory_and_optional_args", number=5,
                                                 optional_number=5)

        self.assertEqual(call_count, 10)
        self.assertEqual(demo_tool.call_count, 10)

        call_count = combined_tool.call_function("demo_sample_function_with_mandatory_and_optional_args", number=5)

        self.assertEqual(call_count, 15)
        self.assertEqual(demo_tool.call_count, 15)

    def test_pre_processing_functions(self):
        demo_tool = DemoTool({}, "base_path", None)

        second_tool = SecondDemoTool({}, "base_path", None)

        sample_preprocessing_tool = DemoPreprocessingTool()
        demo_loop_detector = DemoPreprocessingTool()

        combined_tool = CombinedTools([demo_tool, second_tool],
                                      [sample_preprocessing_tool, demo_loop_detector],
                                      "base_path", None)

        call_count = combined_tool.call_function("demo_sample_function")
        self.assertEqual(call_count, 1)

        second_tool_call_count = combined_tool.call_function("secondarytool_sample_function")
        self.assertEqual(second_tool_call_count, 1)

    def test_agent_pre_processor(self):
        demo_tool = DemoTool({}, "base_path", None)

        second_tool = SecondDemoTool({}, "base_path", None)

        agent_pre_processor = AgentPreProcessor(self.callbacks, self.logger)

        combined_tool = CombinedTools([demo_tool, second_tool],
                                      [agent_pre_processor],
                                      "base_path", None)

        call_count = combined_tool.call_function("demo_sample_function")
        self.assertEqual(call_count, 1)

        second_tool_call_count = combined_tool.call_function("secondarytool_sample_function")
        self.assertEqual(second_tool_call_count, 1)

    def test_agent_pre_processor_function_call(self):
        agent_pre_processor = AgentPreProcessor(self.callbacks, self.logger)

        result = agent_pre_processor.pre_process_function_call(
            "test_function",
            reason="Test reason",
            observations="Test observations",
            root_cause_analysis="Test analysis",
            extra_arg="extra"
        )

        self.assertEqual(result, {"extra_arg": "extra"})
        self.logger.info.assert_any_call("Function: test_function")
        self.logger.info.assert_any_call("Reason: Test reason")
        self.logger.info.assert_any_call("Observations: Test observations")
        self.logger.info.assert_any_call("Root Cause Analysis: Test analysis")

    def test_agent_pre_processor_removed_functions(self):
        demo_tool = DemoTool({}, "base_path", None)
        second_tool = SecondDemoTool({}, "base_path", None)

        # Mock the sample_function to check received arguments
        demo_tool.sample_function = MagicMock(return_value=1)
        second_tool.sample_function = MagicMock(return_value=1)

        agent_pre_processor = AgentPreProcessor(self.callbacks, self.logger)

        combined_tool = CombinedTools([demo_tool, second_tool],
                                      [agent_pre_processor],
                                      "base_path", None)

        # Test demo_sample_function
        call_count = combined_tool.call_function("demo_sample_function",
                                                 reason="Test reason",
                                                 observations="Test observations",
                                                 root_cause_analysis="Test analysis",
                                                 extra_arg="extra")
        self.assertEqual(call_count, 1)

        # Print call arguments for debugging
        print("Demo tool sample_function call args:", demo_tool.sample_function.call_args)

        # Check that special arguments were removed
        self.assertTrue(demo_tool.sample_function.called, "demo_tool.sample_function was not called")

        call_args = demo_tool.sample_function.call_args[1] if demo_tool.sample_function.call_args else {}

        self.assertNotIn("reason", call_args)
        self.assertNotIn("observations", call_args)
        self.assertNotIn("root_cause_analysis", call_args)
        self.assertIn("extra_arg", call_args)

        # Check logger calls
        logger_calls = [call[0][0] for call in self.logger.info.call_args_list]
        self.assertIn("Function: sample_function", logger_calls)
        self.assertIn("Reason: Test reason", logger_calls)
        self.assertIn("Observations: Test observations", logger_calls)
        self.assertIn("Root Cause Analysis: Test analysis", logger_calls)

        # Reset mocks
        self.logger.reset_mock()
        demo_tool.sample_function.reset_mock()

        # Test secondarytool_sample_function
        second_tool_call_count = combined_tool.call_function("secondarytool_sample_function",
                                                             reason="Another reason",
                                                             observations="More observations",
                                                             root_cause_analysis="Further analysis")
        self.assertEqual(second_tool_call_count, 1)

        # Print call arguments for debugging
        print("Second tool sample_function call args:", second_tool.sample_function.call_args)

        # Check that special arguments were removed
        self.assertTrue(second_tool.sample_function.called, "second_tool.sample_function was not called")
        call_args = second_tool.sample_function.call_args[1] if second_tool.sample_function.call_args else {}
        self.assertNotIn("reason", call_args)
        self.assertNotIn("observations", call_args)
        self.assertNotIn("root_cause_analysis", call_args)

        # Check logger calls
        logger_calls = [call[0][0] for call in self.logger.info.call_args_list]
        print("Logger calls:", logger_calls)
        self.assertIn("Function: sample_function", logger_calls)
        self.assertIn("Reason: Another reason", logger_calls)
        self.assertIn("Observations: More observations", logger_calls)
        self.assertIn("Root Cause Analysis: Further analysis", logger_calls)

class TestShellFunctionValidation:
    @pytest.fixture(scope="class")
    def docker_executor(self):
        executor = DockerExecutor()
        yield executor
        executor.cleanup_all_containers()

    @pytest.fixture(scope="class")
    def visible_shell(self, docker_executor):
        base_path = "/tmp"
        logger = MagicMock()
        return VisibleShellTools({}, base_path, logger, executor=docker_executor)

    @pytest.fixture(scope="class")
    def combined_tools(self, visible_shell):
        return CombinedTools([visible_shell], [], "/tmp", MagicMock())

    def test_validate_start_long_running_process(self, combined_tools):
        # Valid case
        try:
            combined_tools.validate_function("VisibleShellTools_start_long_running_process",
                                             command="echo 'Hello, World!'",
                                             working_directory="/tmp/test")
        except ValueError:
            pytest.fail("validate_function raised ValueError unexpectedly!")


        # Invalid working directory (not an absolute path)
        with pytest.raises(ValueError) as excinfo:
            combined_tools.validate_function("VisibleShellTools_start_long_running_process",
                                             command="echo 'Hello, World!'",
                                             working_directory="/tmp")
        assert "must be different from" in str(excinfo.value)

if __name__ == '__main__':
    unittest.main()
