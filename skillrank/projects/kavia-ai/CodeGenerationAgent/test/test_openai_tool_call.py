import unittest
import os
import json
from dotenv import load_dotenv
from litellm import completion

from code_generation_core_agent.agents.tools.schemas.decorators import register_function

load_dotenv()
import litellm

import litellm


class WeatherService:
    @register_function(
        description="Get the current weather in a given location."
                    " Supports optional attributes for specifying temperature unit and additional weather details.",
        parameters={
            "type": "object",
            "properties": {
                "location": {"type": "string", "description": "The city and state, e.g. San Francisco, CA"},
                "attributes": {
                    "description": "Optional attributes for the weather request",
                    "type": "object",
                    "properties": {
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "The temperature unit to use. Always specify this! (default: celsius)"
                        },
                        "details": {
                            "type": "boolean",
                            "description": "Whether to include additional weather details (default: false)"
                        },
                        "do_not_use": {
                            "type": "string",
                            "description": "This should not be used. Testing that this item is always ignored."
                        }
                    },
                    "additionalProperties": False
                }
            },
            "required": ["location", "attributes"],
            "additionalProperties": False
        }
    )
    def get_weather(self, location, unit=None, details=None, do_not_use=None):
        temperature = 25 if unit == "celsius" else 77
        weather_condition = "sunny"

        weather_info = f"The weather in {location} is {weather_condition}. Temperature: {temperature}°{unit}"

        return weather_info


class StockService:
    @register_function(
        description="Get the current stock price for a given symbol",
        parameters={
            "type": "object",
            "properties": {
                "symbol": {"type": "string", "description": "The stock symbol, e.g. AAPL for Apple Inc."}
            },
            "required": ["symbol"]
        }
    )
    def get_stock_price(self, symbol):
        print(f"The stock price of {symbol} is $100")
        pass


class TestLiteLLMFunctionCall(unittest.TestCase):
    def setUp(self):
        self.weather_service = WeatherService()
        self.stock_service = StockService()

        self.tools = [
            self.weather_service.get_weather._metadata,
            self.stock_service.get_stock_price._metadata
        ]

        self.messages = [{"role": "user", "content": "What's the weather like in Boston today?"}]

    def test_chat_completion_with_function_call(self):
        api_key = os.getenv("OPENAI_API_KEY")
        self.assertIsNotNone(api_key, "OPENAI_API_KEY is not set in the environment")

        response = completion(
            model="gpt-4o-mini",
            messages=self.messages,
            tools=self.tools,
            tool_choice="auto",
            api_key=api_key
        )

        self.assertIsNotNone(response)

        self.assertIn('choices', response)
        self.assertGreater(len(response.choices), 0)
        self.assertIn('message', response.choices[0])

        message = response.choices[0].message

        self.assertTrue(hasattr(message, 'tool_calls'), "Message does not have 'tool_calls' attribute")
        tool_calls = message.tool_calls

        self.assertIsNotNone(tool_calls)
        self.assertGreater(len(tool_calls), 0)

        self.assertEqual(tool_calls[0].function.name, "get_weather")

        function_args = json.loads(tool_calls[0].function.arguments)

        self.assertIn('attributes', function_args, "Function call is missing 'attributes'")

        self.assertIn('unit', function_args['attributes'], "'unit' is missing in 'attributes'")

        self.assertNotIn('do_not_use', function_args['attributes'], "'do_not_use' should"
                                                                    " not be present in 'attributes'")

        print(f"Response: {response}")
        print(f"Tool Calls: {tool_calls}")
        print(f"Function Arguments: {function_args}")


if __name__ == '__main__':
    unittest.main()
