import atexit
import base64
import io
import os
import time
import tempfile
import pytest
import asyncio
from concurrent.futures import ThreadPoolExecutor


from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.web_browser import WebBrowser, shutdown_browser


@pytest.fixture(scope="module")
def docker_executor():
    executor = DockerExecutor()
    yield executor
    del executor  # This will clean up the container


@pytest.fixture(scope="module")
def browser_shim():

    def browser_callback(callback_data):
        print(f"Browser callback")

    # Create the WebBrowser instance
    browser = WebBrowser(
        callbacks={'browser_output_callback': browser_callback},
        base_path="",
        logger=None,
        llm=None,
        executor=None
    )

    yield browser

    try:
        shutdown_browser()
    except Exception as e:
        print(f"Error during browser shutdown: {e}")

def run_async(coro):
    loop = asyncio.new_event_loop()
    return loop.run_until_complete(coro)


class TestBrowserShim:
    @staticmethod
    async def start_sample_flask_app(executor: DockerExecutor):
        """
        Create a sample Flask app and run it in a Docker container.
        The default port is 5000.
        """
        APP = """from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello, World!"

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3005, debug=True)"""

        tmpdirname = tempfile.mkdtemp(dir='/tmp/')

        # Write the Flask app to a file
        with open(os.path.join(tmpdirname, 'app.py'), 'w') as f:
            f.write(APP)

        cmd = f"export FLASK_APP={tmpdirname}/app.py && flask run --host=0.0.0.0 --port=3005 "
        process = await executor.create_subprocess_shell(cmd)
        return_code, result = await process.wait()
        print(f"Flask app started with return code: {return_code}")
        return result

    def test_open_google(self, docker_executor, browser_shim):

        result = browser_shim.call_dynamic_function("goto", url='https://www.google.com/ncr')
        assert result['status'] == 'SUCCESS'
        assert result['message'] == 'Action completed successfully.'
        assert 'Google' in result['web_page_text']
        assert 'Search' in result['accessibility_tree']

def pytest_sessionfinish(session, exitstatus):
    print("Calling shutdown_browser")
    shutdown_browser()