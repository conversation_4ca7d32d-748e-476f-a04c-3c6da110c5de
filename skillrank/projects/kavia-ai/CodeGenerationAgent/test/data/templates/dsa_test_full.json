[{"name": "sysperf_no_framework", "description": "This front-end application delivers a comprehensive and user-friendly interface for monitoring and analyzing the results of system executions. The core functionality centers on visualizing the output of each system run in a detailed tabular format, with mocked data. Each run's table displays key attributes for every generated application, including app name, elapsed time, total cost, execution date, project link (as an interactive icon), CGA version, and the LLM model used. Users can quickly access the linked application in a new browser tab via the project link icon. A standout feature is the side-by-side comparison of any two system runs. By default, the last two runs are loaded, with the latest on the right. The comparison view highlights changes in numeric metrics with delta icons and percentage changes, enabling rapid identification of performance, cost, and other metric variations. This supports iterative development and optimization by making it easy to track the impact of changes between runs."}, {"name": "sysperf_ruby", "description": "This Ruby on Rails front-end application delivers a comprehensive and user-friendly interface for monitoring and analyzing the results of system executions. The core functionality centers on visualizing the output of each system run in a detailed tabular format, with mocked data. Each run's table displays key attributes for every generated application, including app name, elapsed time, total cost, execution date, project link (as an interactive icon), CGA version, and the LLM model used. Users can quickly access the linked application in a new browser tab via the project link icon. A standout feature is the side-by-side comparison of any two system runs. By default, the last two runs are loaded, with the latest on the right. The comparison view highlights changes in numeric metrics with delta icons and percentage changes, enabling rapid identification of performance, cost, and other metric variations. This supports iterative development and optimization by making it easy to track the impact of changes between runs."}, {"name": "sysperf_qt", "description": "This QT desktop application delivers a comprehensive and user-friendly interface for monitoring and analyzing the results of system executions. The core functionality centers on visualizing the output of each system run in a detailed tabular format, with mocked data. Each run's table displays key attributes for every generated application, including app name, elapsed time, total cost, execution date, project link (as an interactive icon), CGA version, and the LLM model used. Users can quickly access the linked application in a new browser tab via the project link icon. A standout feature is the side-by-side comparison of any two system runs. By default, the last two runs are loaded, with the latest on the right. The comparison view highlights changes in numeric metrics with delta icons and percentage changes, enabling rapid identification of performance, cost, and other metric variations. This supports iterative development and optimization by making it easy to track the impact of changes between runs."}]