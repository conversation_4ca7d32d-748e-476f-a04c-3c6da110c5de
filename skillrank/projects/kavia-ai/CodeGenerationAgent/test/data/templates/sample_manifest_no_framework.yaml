overview:
  project_name: sysperf
  description: System performance monitoring and analysis tool
  third_party_services: []
  env: {}
containers:
- container_name: sysperf_frontend
  description: Provides a user interface to display, compare, and analyze system run
    results using mocked data.
  interfaces: Web UI, interacts with mocked data source
  container_type: frontend
  dependent_containers: []
  workspace: sysperf_frontend_workspace
  container_root: sysperf_frontend_workspace/sysperf_frontend
  port: '3000'
  framework: ''
  type: ''
  buildCommand: npm install && npx tsc --noEmit && npm test -- --ci
  startCommand: PORT=<port> HOST=<host> BROWSER=none npm start
  installCommand: ''
  lintCommand: ''
  generateOpenapiCommand: ''
  container_details:
    features:
    - Tabular display of system run outputs with key metrics
    - Access project links as interactive icons opening in new tabs
    - Side-by-side comparison of any two system runs
    - Default comparison loads last two runs with the latest on the right
    - Highlight changes in numeric metrics with delta icons and percentage changes
    - Supports iterative development by tracking impact of changes
    colors:
      primary: '#1976D2'
      secondary: '#424242'
      accent: '#FF7043'
      background: '#FFFFFF'
    theme: light
    layout_description: Header with project title, main content displaying tables
      for each run, floating comparison panel, modals/dialogs for details, action
      bar to select runs for comparison.
    style: modern, minimalistic
  lintConfig: ''
  routes: []
  apiSpec: ''
  auth: null
  schema: ''
  migrations: ''
  seed: ''
  env: {}
