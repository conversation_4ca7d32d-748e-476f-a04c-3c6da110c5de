[{"name": "sysperf", "overview": "This front-end application delivers a comprehensive and user-friendly interface for monitoring and analyzing the results of system executions. The core functionality centers on visualizing the output of each system run in a detailed tabular format, with mocked data. Each run's table displays key attributes for every generated application, including app name, elapsed time, total cost, execution date, project link (as an interactive icon), CGA version, and the LLM model used. Users can quickly access the linked application in a new browser tab via the project link icon. A standout feature is the side-by-side comparison of any two system runs. By default, the last two runs are loaded, with the latest on the right. The comparison view highlights changes in numeric metrics with delta icons and percentage changes, enabling rapid identification of performance, cost, and other metric variations. This supports iterative development and optimization by making it easy to track the impact of changes between runs.", "containers": [{"container_name": "sysperf_frontend", "platform": "generic", "description": "Provides a user interface to display, compare, and analyze system run results using mocked data.", "interfaces": "Web UI, interacts with mocked data source", "workspace": "sysperf_frontend_workspace", "container_root": "sysperf_frontend_workspace/sysperf_frontend", "dependencies": [], "container_type": "frontend", "container_details": {"features": ["Tabular display of system run outputs with key metrics", "Access project links as interactive icons opening in new tabs", "Side-by-side comparison of any two system runs", "Default comparison loads last two runs with the latest on the right", "Highlight changes in numeric metrics with delta icons and percentage changes", "Supports iterative development by tracking impact of changes"], "colors": {"primary": "#1976D2", "secondary": "#424242", "accent": "#FF7043", "background": "#FFFFFF"}, "theme": "light", "layout_description": "Header with project title, main content displaying tables for each run, floating comparison panel, modals/dialogs for details, action bar to select runs for comparison.", "style": "modern, minimalistic"}}], "3rd_party_services": [], "figma_components": "", "manifest_path": "test/data/templates/sample_manifest_no_framework.yaml"}]