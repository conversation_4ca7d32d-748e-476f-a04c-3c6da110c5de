# Attach text to every description
TARGET_BACKGROUND_COLOR = "#ADDFFF"

DESCRIPTION_COMMON_POST_FIX = f" Generated application must have a background color of #ADDFFF."

USER_INPUT_COMMON_PREFIX = " First, use the validation agent to validate the progress and completition of the application, if not completed: "

INTERACTIVE_WORK_ITEMS = [
    {
        "work_item": {
            "component_name": "online_hello_world",
            'work_item_type': 'container',
            "containers" : [
                {
                "container_name": "online_hello_world",
                "relative_path": "online_hello_world_workspace",
                "framework" : "React",
                }
            ],
            "description": " Download file from the web: 'https://www.dropbox.com/scl/fi/dl9tg4ec94lugk9lw0rqv/download_test.txt?rlkey=n98t1io54syycdzv1h1p2d1rx&st=76hkvg79&dl=1' "
                           " Find out the file type and read out the content. Take a note of the content that should be used as <alert_message> in the next task."
                           " Create a simple Hello World web page using HTML, CSS, and React"
                           " The page should contain a centered heading that says 'Hello World' and"
                           " a single button below it. When the button is clicked, it should show"
                           " an <alert_message>'. The page should be responsive"
                           " and work well on both desktop and mobile devices.",
            "interfaces": "User web interface with a heading and an interactive button",
            "algorithms": "Event handling for button click",
            "pseudocode": "1. Create HTML structure with heading and button\n"
                          "2. Style elements using CSS\n"
                          "3. Add JavaScript to handle button click and show alert"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "hellp_world_app",
            "containers": [
                {
                "container_name": "hello_world",
                "relative_path": "hello_world_workspace",
                "framework": "React",
                }
            ],
            "description": "Create a simple Hello World web page using HTML, CSS, and React."
                           "The page should contain a centered heading that says 'Hello World' and"
                           " a single button below it. When the button is clicked, it should show"
                           " an alert message saying 'Hello, user!'. The page should be responsive"
                           " and work well on both desktop and mobile devices.",
            "interfaces": "User web interface with a heading and an interactive button",
            "algorithms": "Event handling for button click",
            "pseudocode": "1. Create HTML structure with heading and button\n2. Style elements using CSS\n3. Add JavaScript to handle button click and show alert"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Expense Tracker",
            "description": "Create an expense tracker that tracks income and expenses with categorization and summary of monthly spending. The app is written in JavaScript and uses React. The app is deployed locally on port 3000. Functionality includes: 1. Entering an expense or income with date, amount, and category. 2. Showing the entered transactions in a sortable list. 3. Displaying a summary of monthly spending and income. 4. Allowing users to edit or delete existing transactions. 5. Persistance of data using local storage.",
            "interfaces": "User web interface for inputting transactions, viewing summaries, and managing existing entries",
            "algorithms": "Categorization algorithm, monthly summary calculation, data persistence",
            "pseudocode": "1. Create components for transaction input, list, and summary\n2. Implement state management for transactions\n3. Add functions for adding, editing, and deleting transactions\n4. Implement monthly summary calculation\n5. Add local storage functionality for data persistence"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Task Management Kanban Board",
            "description": "Implement a Kanban board for task management with drag-and-drop functionality between columns. The application should be built using React and include features such as creating new tasks, editing existing tasks, moving tasks between columns (e.g., 'To Do', 'In Progress', 'Done'), and the ability to add or remove columns. Each task should have a title, description, and optional due date.",
            "interfaces": "Kanban board UI with draggable task cards, task creation/editing modal, column management interface",
            "algorithms": "Drag-and-drop implementation, task state management, column order preservation",
            "pseudocode": "1. Create components for Board, Column, and Task\n2. Implement drag-and-drop functionality\n3. Add task creation and editing features\n4. Implement column management\n5. Add state management for tasks and columns\n6. Implement data persistence using local storage"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Event Countdown Timer",
            "description": " Develop a web application where users can create events"
                           " with countdown timers and customsizable reminders."
                           " The app should be written in JavaScript using React and deployed locally on port 300"
                           " The application should allow users to input event details including name,"
                           " date, time, and optional description. Users should be able to set multiple"
                           " reminders for each event at various time intervals (e.g., 1 day before, 1 hour before)."
                           " The main interface should display a list of upcoming events with their respective"
                           " countdown timers updating in real-time. ",
            "interfaces": "Event creation form, countdown display, reminder settings, event list view",
            "algorithms": "Countdown calculation, reminder scheduling, real-time updates",
            "pseudocode": "1. Create event input form\n"
                          "2. Implement countdown logic\n"
                          "3. Add reminder scheduling system\n"
                          "4. Create event list view with real-time countdowns\n"
                          "5. Implement notification system for reminders\n"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Meme Generator",
            "description": "Build a meme generator that allows users to upload images and add text overlays. The application should"
                           " provide a canvas where users can drag and position text boxes, choose fonts, sizes, and colors for the text."
                           " Users should be able to add multiple text boxes and adjust their properties individually."
                           " The app should also include a selection of popular meme templates and the ability to save or share the generated memes."
                           " The application should be a single page javascript.",
            "interfaces": "Image upload interface, text input fields, meme preview canvas, template selection, save/share options.",
            "algorithms": "",
            "pseudocode": "1. Implement image upload and display\n2. Create text input and styling controls\n3. Develop canvas for meme preview\n4. Add drag-and-drop functionality for text positioning\n5. Implement template selection feature\n6. Add save and share functionality"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Pomodoro Timer",
            "description": "Create an interval timer based on the Pomodoro Technique for managing work and break periods."
                           " The app should be written in JavaScript using React and deployed locally on port 3000. Features should include customizable work and break durations, a visual countdown timer, audio notifications for interval changes, and the ability to pause, resume, and reset the timer. The app should also keep track of completed Pomodoro cycles and provide a summary of productivity.",
            "interfaces": "Timer display, interval settings, control buttons, productivity summary",
            "algorithms": "Timer logic, interval switching, productivity calculation",
            "pseudocode": "1. Create timer display component\n2. Implement timer logic and interval switching\n3. Add controls for start, pause, resume, and reset\n4. Implement settings for custom durations\n5. Add audio notifications\n6. Create productivity summary component"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Portfolio Website Builder",
            "description": "Develop a tool for users to create a portfolio website by entering project details and skills. The application should provide a selection of customizable templates, allow users to add projects with descriptions and images, list skills with proficiency levels, and include sections for personal information and contact details. The generated portfolio should be responsive and optimized for various devices.",
            "interfaces": "Project and skill input forms, portfolio template selection, live preview, export options",
            "algorithms": "Portfolio generation, layout optimization, responsive design implementation",
            "pseudocode": "1. Create user input forms for personal info, projects, and skills\n2. Implement template selection and customization\n3. Develop live preview functionality\n4. Create portfolio generation algorithm\n5. Implement responsive design for generated portfolios\n6. Add export and hosting options"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Language Learning App",
            "description": "Build an application that helps users learn vocabulary with translations and quizzes. The app should allow users to create and study flashcards, take quizzes to test their knowledge, and track their progress over time. Include features such as spaced repetition for efficient learning, multiple choice and writing exercises, and the ability to organize vocabulary by categories or lessons.",
            "interfaces": "Vocabulary list, flashcard interface, quiz interface, progress tracking dashboard",
            "algorithms": "Translation API integration, quiz generation, spaced repetition algorithm",
            "pseudocode": "1. Implement user authentication\n2. Create vocabulary management system\n3. Integrate translation API\n4. Develop flashcard study interface\n5. Implement quiz generation and interface\n6. Create progress tracking algorithm\n7. Implement spaced repetition system"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Blog Platform",
            "description": "Create a simple blogging platform with post creation, editing, and commenting functionality. The platform should allow users to create accounts, write and publish blog posts with rich text formatting, categorize posts with tags, and enable readers to leave comments. Include features such as a search function, an archive view, and social media sharing options. The platform should also have an admin panel for managing users, posts, and comments.",
            "interfaces": "User registration and login, post editor with rich text formatting, comment section, blog post display, admin panel",
            "algorithms": "Text formatting, comment threading, search functionality, user authentication and authorization",
            "pseudocode": "1. Set up user authentication system\n2. Create post creation and editing interface\n3. Implement comment system with threading\n4. Develop blog post display with pagination\n5. Add search and categorization features\n6. Create admin panel for content management\n7. Implement social media sharing functionality"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {
        "work_item": {
            "component_name": "Movie Recommendation App",
            "description": "Develop an app using HTML, CSS, and JavaScript that suggests movies based on user"
                           "preferences with a rating and watchlist feature. "
                           "The app should allow users to rate movies they've watched, add movies to their watchlist, "
                           "and receive personalized recommendations based on their ratings and viewing history. "
                           "Include features such as movie details (cast, crew, synopsis), genre filtering, and the"
                           " ability to see popular and trending movies. The app should also have a social component"
                           " allowing users to share their ratings and recommendations with friends."
                           " The app only allows anonymous users and no registration is required.",
            "interfaces": "",
            "algorithms": "",
            "pseudocode": "Movies should be stored crawled in a text file that is imported into an"
                          "SQLite database using a script"
        },
        "user_input_list": [
            "Implement the application.",
        ]
    },
    {

        "work_item" : {
            "component_name": "Gomoku Backend",
            "description": "Python Flask backend service for playing Gomoku (Five in a Row) "
                           "The app is written in Python and deployed on port 3010. "
                           "Use the following gomoku python library: "
                           "  https://files.pythonhosted.org/packages/a2/b8/a6a4b74bfae399ba6bc54d653adbca41de5ebc1eb6e5b4b4a5d5bebf1c4f/gomoku-0.1.0.tar.gz"
                           "Functionality includes: "
                           "1. Creating a new game session\n"
                           "2. Randomly deciding who has the first move "
                           "3. Allowing user to place dots"
                           "4. Computer placing dots using Monte Carlo Tree Search with threat-space analysis "
                           "5. Maintaining and displaying the 12x12 board state"
                           "8. First AI move is center-board placement for optimal positioning",
            "interfaces": "GUI interface to play the game on a retro board design",
            "algorithms": "1. Monte Carlo Tree Search (MCTS) with UCT selection for main move evaluation "
                          "2. Threat-space analysis for rapid pruning of poor moves "
                          "3. Pattern matching for quick win/block detection "
                          "4. Five-in-a-row win condition validation (horizontal, vertical, diagonal) "
                          "5. Move validation and board state management",
            "pseudocode": ""
        },
        "user_input_list": [
            "Implement the service.",
        ]
    },
    {

        "work_item": {
            "component_name": "TicTacToe Backend",
            "description": "Python Flask backend service for providing AI functionality for a TicTacToe app"
                           "The app is written in Python and deployed on port 3010. "
                           "Functionality includes: "
                           "1. Creating a new game session\n"
                           "2. Randomly deciding who has the first move."
                           "3. AI with different difficulty levels (easy, medium, hard)",
            "interfaces": "Game interface. No GUI",
            "algorithms": "",
            "pseudocode": ""
        },
        "user_input_list": [
            "Implement the service.",
        ]
    }
]