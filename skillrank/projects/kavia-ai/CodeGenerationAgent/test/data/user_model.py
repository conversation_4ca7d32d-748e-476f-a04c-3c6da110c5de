from pydantic import BaseModel, Field,  validator
from typing import Optional, List, Dict
from datetime import datetime
import json


# LLM Preferences for user 

from enum import Enum
from pydantic import BaseModel, Field, validator

class EnumEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        return super().default(obj)

class LLMModel(Enum):
    gpt_4o_mini = "gpt-4o-mini"
    gpt_4o = "gpt-4o"
    gpt_4_turbo = "gpt-4-turbo" 
    gpt_3dot5 = "gpt-3.5-turbo"
    claude_3_5_sonnet = "claude-3-5-sonnet-20241022"
    bedrock_claude_3_5_sonnet = "bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0"
    
class Modules(Enum):
    DISCUSSION = {
        "name": "discussion",
        "display_name": "Discussion Framework",
        "available_models": [LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.gpt_3dot5]
    }
    CODE_GENERATION = {
        "name": "code_generation",
        "display_name": "Code Generation",
        "available_models": [LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.claude_3_5_sonnet]
    }
    CODE_INSPECTION = {
        "name": "code_inspection",
        "display_name": "Code Inspection",
        "available_models": [LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.claude_3_5_sonnet]
    }
    CONVERSATIONAL_CHAT = {
        "name": "conversational_chat",
        "display_name": "Conversational Chat",
        "available_models": [LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.claude_3_5_sonnet]
    }

class ModuleConfig(BaseModel):
    llm_model: LLMModel = Field(default=LLMModel.gpt_4o_mini, description="The selected Large Language Model")
    temperature: Optional[float] = Field(default=None, description="The temperature parameter for sampling")

class Module(BaseModel):
    name: str
    display_name: str
    current_model: LLMModel = Field(default=LLMModel.claude_3_5_sonnet, description="The current Large Language Model")
    available_models: List[LLMModel]

class UserConfig(BaseModel):
    modules: Dict[str, ModuleConfig] = Field(default_factory=dict, description="Module configurations")

    def model_dump_json(self, **kwargs):
        return json.dumps(self.model_dump(), cls=EnumEncoder, **kwargs)

available_modules = {
    Modules.DISCUSSION.value["name"]: Module(**Modules.DISCUSSION.value),
    Modules.CODE_GENERATION.value["name"]: Module(**Modules.CODE_GENERATION.value),
    Modules.CODE_INSPECTION.value["name"]: Module(**Modules.CODE_INSPECTION.value),
    Modules.CONVERSATIONAL_CHAT.value["name"]: Module(**Modules.CONVERSATIONAL_CHAT.value)

}


class AddRemoveUserDTO(BaseModel):
    """
    Model for adding or removing users from a discussion.
    """
    user_id: str = Field(..., description="The unique identifier for the user (Cognito Username or sub).")
    
class UserUpdate(BaseModel):
    Name: Optional[str] = ""
    Designation: Optional[str] = ""
    Department: Optional[str] = ""


    @validator('*', pre=True)  
    def check_if_empty(cls, value):
        if value == '':
            return None  # Treat empty strings as None
        return value
    
class ProjectMember(BaseModel):
    user_id: str = Field(..., description="Unique identifier for the user")
    role: str = Field(..., description="Role of the user in the project (e.g., 'developer', 'manager', 'qa')")
    responsibilities: Optional[List[str]] = Field(
        default=[], description="List of responsibilities for this user in the project"
    )

class ProjectUpdate(BaseModel):
    members: List[ProjectMember] = Field(..., description="List of members to add to the project")

class TaskAssignment(BaseModel):
    node_id: int = Field(..., description="ID of the WorkItem node")
    user_id: str = Field(..., description="Username of the User node")

class TaskAssignmentResponse(BaseModel):
    success: bool = Field(..., description="Indicates whether the assignment was successful")
    message: str = Field(..., description="Message providing additional details about the assignment")



#Activity Tracking Model
class ProjectUsageModel(BaseModel):
    user_id: str = Field(..., description="Unique identifier of the user")
    project_id: int = Field(..., description="ID of the project accessed")
    timestamp: datetime = Field(default=datetime.now().isoformat(), description="Timestamp of the last access")
