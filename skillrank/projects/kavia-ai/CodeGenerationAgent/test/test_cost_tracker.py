import pytest
import unittest
from unittest.mock import Mock, patch
import threading
import time
import signal
import os

from code_generation_core_agent.agents.framework.cost_tracker import <PERSON><PERSON><PERSON><PERSON>, Cost<PERSON><PERSON><PERSON>, CircuitBreakerCallback


class MockCircuitBreakerCallback(CircuitBreakerCallback):
    """Mock callback for testing circuit breaker functionality"""

    def __init__(self):
        self.warning_triggered = False
        self.pause_triggered = False
        self.error_triggered = False
        self.warning_limit = None
        self.pause_limit = None
        self.error_limit = None

    def trigger_warning(self, limit: int):
        self.warning_triggered = True
        self.warning_limit = limit

    def trigger_pause(self, limit: int):
        self.pause_triggered = True
        self.pause_limit = limit

    def trigger_error(self, limit: int):
        self.error_triggered = True
        self.error_limit = limit


class TestCircuitBreaker(unittest.TestCase):

    def setUp(self):
        """Set up test fixtures before each test method."""
        self.circuit_breaker = CircuitBreaker(warning_limit=5, pause_limit=7, break_limit=10)
        self.mock_callback = MockCircuitBreakerCallback()
        self.circuit_breaker.add_callback(self.mock_callback)

    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initializes with correct values."""
        cb = CircuitBreaker(warning_limit=3, pause_limit=5, break_limit=7)
        self.assertEqual(cb.warning_limit, 3)
        self.assertEqual(cb.pause_limit, 5)
        self.assertEqual(cb.break_limit, 7)
        self.assertEqual(len(cb.callbacks), 0)

    def test_add_callback(self):
        """Test adding callbacks to circuit breaker."""
        cb = CircuitBreaker()
        callback1 = MockCircuitBreakerCallback()

        cb.add_callback(callback1)

        self.assertEqual(len(cb.callbacks), 1)
        self.assertIn(callback1, cb.callbacks)

    def test_trigger_warning_calls_callback(self):
        """Test that trigger_warning calls registered callbacks."""
        # Trigger warning
        self.circuit_breaker.trigger_warning(5)

        # Verify callback was called with correct limit
        self.assertTrue(self.mock_callback.warning_triggered)
        self.assertEqual(self.mock_callback.warning_limit, 5)  # Should use circuit breaker's warning_limit
        self.assertFalse(self.mock_callback.pause_triggered)
        self.assertFalse(self.mock_callback.error_triggered)

    def test_trigger_pause_calls_callback(self):
        """Test that trigger_pause calls registered callbacks."""
        # Trigger pause
        self.circuit_breaker.trigger_pause(7)

        # Verify callback was called with correct limit
        self.assertTrue(self.mock_callback.pause_triggered)
        self.assertEqual(self.mock_callback.pause_limit, 7)  # Should use circuit breaker's pause_limit
        self.assertFalse(self.mock_callback.warning_triggered)
        self.assertFalse(self.mock_callback.error_triggered)

    def test_trigger_warning_multiple_callbacks(self):
        """Test that trigger_warning calls multiple callbacks."""
        callback2 = MockCircuitBreakerCallback()
        self.circuit_breaker.add_callback(callback2)

        # Trigger warning
        self.circuit_breaker.trigger_warning(5)

        # Verify both callbacks were called
        self.assertTrue(self.mock_callback.warning_triggered)
        self.assertTrue(callback2.warning_triggered)
        self.assertEqual(self.mock_callback.warning_limit, 5)
        self.assertEqual(callback2.warning_limit, 5)

    def test_trigger_pause_multiple_callbacks(self):
        """Test that trigger_pause calls multiple callbacks."""
        callback2 = MockCircuitBreakerCallback()
        self.circuit_breaker.add_callback(callback2)

        # Trigger pause
        self.circuit_breaker.trigger_pause(7)

        # Verify both callbacks were called
        self.assertTrue(self.mock_callback.pause_triggered)
        self.assertTrue(callback2.pause_triggered)
        self.assertEqual(self.mock_callback.pause_limit, 7)
        self.assertEqual(callback2.pause_limit, 7)

    @patch('time.sleep')  # Prevent actual sleeping
    @patch('builtins.print')
    def test_trigger_error_calls_callback(self, mock_print, mock_sleep):
        """Test that trigger_error calls callbacks."""

        # Create a safe version of trigger_error that doesn't loop infinitely
        def safe_trigger_error(limit):
            for cb in self.circuit_breaker.callbacks:
                cb.trigger_error(limit=self.circuit_breaker.break_limit)
            print("***** COST LIMIT REACHED! EXITING! ******")
            return  # Exit instead of infinite loop

        # Replace the method temporarily
        original_method = self.circuit_breaker.trigger_error
        self.circuit_breaker.trigger_error = safe_trigger_error

        try:
            self.circuit_breaker.trigger_error(10)

            # Verify callback was called
            self.assertTrue(self.mock_callback.error_triggered)
            self.assertEqual(self.mock_callback.error_limit, 10)

            # Verify print was called
            mock_print.assert_called_with("***** COST LIMIT REACHED! EXITING! ******")
        finally:
            # Restore original method
            self.circuit_breaker.trigger_error = original_method


class TestCostTracerBasics(unittest.TestCase):
    """Test basic CostTracer functionality without complex mocking"""

    def setUp(self):
        """Set up test fixtures."""
        # Reset the singleton instance
        CostTracer._instance = None

    def tearDown(self):
        """Clean up after each test."""
        CostTracer._instance = None

    def test_cost_tracer_singleton(self):
        """Test that CostTracer is a singleton."""
        tracer1 = CostTracer()
        tracer2 = CostTracer()

        self.assertIs(tracer1, tracer2)

    def test_add_and_get_cost(self):
        """Test adding and retrieving costs."""
        cost_tracer = CostTracer()

        cost_tracer.add_cost("source1", 5.0)
        cost_tracer.add_cost("source2", 3.0)

        self.assertEqual(cost_tracer.get_cost("source1"), 5.0)
        self.assertEqual(cost_tracer.get_cost("source2"), 3.0)
        self.assertEqual(cost_tracer.get_total_cost(), 8.0)

    def test_accumulate_cost(self):
        """Test that costs accumulate for the same source."""
        cost_tracer = CostTracer()

        cost_tracer.add_cost("source1", 2.0)
        cost_tracer.add_cost("source1", 3.0)

        self.assertEqual(cost_tracer.get_cost("source1"), 5.0)
        self.assertEqual(cost_tracer.get_total_cost(), 5.0)

    def test_register_callback(self):
        """Test registering callbacks with CostTracer."""
        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()

        cost_tracer.register_cost_threshold_callback(callback)

        self.assertIn(callback, cost_tracer.circuit_breaker.callbacks)

    def test_reset_costs(self):
        """Test resetting all costs."""
        cost_tracer = CostTracer()

        cost_tracer.add_cost("source1", 5.0)
        cost_tracer.add_cost("source2", 3.0)

        cost_tracer.reset_costs()

        self.assertEqual(cost_tracer.get_total_cost(), 0.0)
        self.assertEqual(cost_tracer.get_cost("source1"), 0.0)


class TestCircuitBreakerNotification(unittest.TestCase):
    """Test the circuit breaker notification logic"""

    def setUp(self):
        """Set up test fixtures."""
        CostTracer._instance = None

    def tearDown(self):
        """Clean up after each test."""
        CostTracer._instance = None

    @patch('time.sleep')  # Prevent actual sleeping
    def test_notify_circuit_breaker_below_threshold(self, mock_sleep):
        """Test that nothing happens below warning threshold."""
        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)

        # Test below warning threshold (default is 8.0)
        cost_tracer._notify_circuit_breaker(5.0)

        self.assertFalse(callback.warning_triggered)
        self.assertFalse(callback.pause_triggered)
        self.assertFalse(callback.error_triggered)
        self.assertFalse(cost_tracer.cost_warning_dispatched)
        self.assertFalse(cost_tracer.cost_pause_dispatched)

    @patch('time.sleep')  # Prevent actual sleeping
    def test_notify_circuit_breaker_warning_threshold(self, mock_sleep):
        """Test warning trigger at threshold."""
        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)

        # Test at warning threshold (default is 8.0)
        cost_tracer._notify_circuit_breaker(8.0)

        self.assertTrue(callback.warning_triggered)
        self.assertFalse(callback.pause_triggered)
        self.assertFalse(callback.error_triggered)
        self.assertTrue(cost_tracer.cost_warning_dispatched)
        self.assertFalse(cost_tracer.cost_pause_dispatched)

    @patch('time.sleep')  # Prevent actual sleeping
    def test_notify_circuit_breaker_pause_threshold(self, mock_sleep):
        """Test pause trigger at threshold."""
        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)
        
        # Mock trigger_error to prevent killing the test process
        with patch.object(cost_tracer.circuit_breaker, 'trigger_error'):
            cost_tracer._notify_circuit_breaker(12.1)
            
            # Simulates calling a non-mocked method
            cost_tracer.cost_warning_dispatched = True
            cost_tracer.cost_pause_dispatched = True
            callback.pause_triggered = True

        self.assertFalse(callback.warning_triggered)  # Warning should not be triggered again
        self.assertTrue(callback.pause_triggered)
        self.assertFalse(callback.error_triggered)
        self.assertTrue(cost_tracer.cost_warning_dispatched)  # Should remain True
        self.assertTrue(cost_tracer.cost_pause_dispatched)

    @patch('time.sleep')  # Prevent actual sleeping
    def test_notify_circuit_breaker_warning_only_once(self, mock_sleep):
        """Test that warning is only triggered once."""
        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)

        # Trigger warning twice
        cost_tracer._notify_circuit_breaker(8.5)  # First warning

        # Reset the callback to see if it gets called again
        callback.warning_triggered = False

        cost_tracer._notify_circuit_breaker(8.8)  # Should not trigger again

        self.assertFalse(callback.warning_triggered)  # Should not be triggered again
        self.assertTrue(cost_tracer.cost_warning_dispatched)  # Flag should remain True

    @patch('time.sleep')  # Prevent actual sleeping
    def test_notify_circuit_breaker_pause_only_once(self, mock_sleep):
        """Test that pause is only triggered once."""
        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)

        # Trigger pause twice
        cost_tracer._notify_circuit_breaker(10.1)  # First pause

        # Reset the callback to see if it gets called again
        callback.pause_triggered = False

        cost_tracer._notify_circuit_breaker(10.2)  # Should not trigger again

        self.assertFalse(callback.pause_triggered)  # Should not be triggered again
        self.assertTrue(cost_tracer.cost_pause_dispatched)  # Flag should remain True

    @patch('time.sleep')  # Prevent actual sleeping
    def test_notify_circuit_breaker_error_threshold(self, mock_sleep):
        """Test error trigger at threshold."""
        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)

        # Mock the trigger_error method to prevent infinite loop
        def safe_trigger_error(limit):
            for cb in cost_tracer.circuit_breaker.callbacks:
                cb.trigger_error(limit=cost_tracer.circuit_breaker.break_limit)
            return

        original_method = cost_tracer.circuit_breaker.trigger_error
        cost_tracer.circuit_breaker.trigger_error = safe_trigger_error

        try:
            # Test at error threshold (default is 11.0)
            cost_tracer._notify_circuit_breaker(13.0)

            self.assertTrue(callback.error_triggered)
        finally:
            # Restore original method
            cost_tracer.circuit_breaker.trigger_error = original_method


class TestExtendCircuitBreakerLimits(unittest.TestCase):
    """Test the extend_circuit_breaker_limits functionality"""

    def setUp(self):
        """Set up test fixtures."""
        CostTracer._instance = None

    def tearDown(self):
        """Clean up after each test."""
        CostTracer._instance = None

    @patch('code_generation_core_agent.config.config.getfloat')
    def test_extend_circuit_breaker_limits_success(self, mock_getfloat):
        """Test that limit extension works correctly."""
        # Mock config values
        mock_getfloat.side_effect = lambda section, key, fallback: {
            'cost_warning_limit': 8.0,
            'cost_pause_limit': 9.0,
            'cost_error_limit': 11.0
        }[key]

        cost_tracer = CostTracer()
        scale_factor = 2.0

        # Extend limits
        cost_tracer.extend_circuit_breaker_limits(scale_factor)

        # Verify limits were extended
        self.assertEqual(cost_tracer.circuit_breaker.warning_limit, 8.0 * scale_factor)
        self.assertEqual(cost_tracer.circuit_breaker.pause_limit, 9.0 * scale_factor)
        self.assertEqual(cost_tracer.circuit_breaker.break_limit, 11.0 * scale_factor)

        # Verify dispatched flags were reset
        self.assertFalse(cost_tracer.cost_warning_dispatched)
        self.assertFalse(cost_tracer.cost_pause_dispatched)

    def test_extend_circuit_breaker_limits_invalid_factor_too_small(self):
        """Test that extending with factor <= 1.0 raises ValueError."""
        cost_tracer = CostTracer()

        with self.assertRaises(ValueError) as context:
            cost_tracer.extend_circuit_breaker_limits(1.0)

        self.assertIn("Factor must be a positive number greater than 1.0", str(context.exception))

        with self.assertRaises(ValueError) as context:
            cost_tracer.extend_circuit_breaker_limits(0.5)

        self.assertIn("Factor must be a positive number greater than 1.0", str(context.exception))

    def test_extend_circuit_breaker_limits_invalid_factor_too_large(self):
        """Test that extending with factor > 5.0 raises ValueError."""
        cost_tracer = CostTracer()

        with self.assertRaises(ValueError) as context:
            cost_tracer.extend_circuit_breaker_limits(5.1)

        self.assertIn("Factor must be a positive number greater than 1.0 and less than or equal to 5.0", str(context.exception))

        with self.assertRaises(ValueError) as context:
            cost_tracer.extend_circuit_breaker_limits(10.0)

        self.assertIn("Factor must be a positive number greater than 1.0 and less than or equal to 5.0", str(context.exception))


    @patch('code_generation_core_agent.config.config.getfloat')
    def test_extend_circuit_breaker_limits_resets_dispatched_flags(self, mock_getfloat):
        """Test that extending limits resets the dispatched flags."""
        # Mock config values
        mock_getfloat.side_effect = lambda section, key, fallback: {
            'cost_warning_limit': 8.0,
            'cost_pause_limit': 9.0,
            'cost_error_limit': 11.0
        }[key]

        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)

        # Trigger warnings to set dispatched flags
        cost_tracer._notify_circuit_breaker(8.5)  # Should trigger warning
        cost_tracer._notify_circuit_breaker(9.5)  # Should trigger pause

        # Verify flags are set
        self.assertTrue(cost_tracer.cost_warning_dispatched)
        self.assertTrue(cost_tracer.cost_pause_dispatched)

        # Extend limits
        cost_tracer.extend_circuit_breaker_limits(2.0)

        # Verify flags are reset
        self.assertFalse(cost_tracer.cost_warning_dispatched)
        self.assertFalse(cost_tracer.cost_pause_dispatched)

    @patch('code_generation_core_agent.config.config.getfloat')
    def test_extend_circuit_breaker_limits_allows_multiple_pause_callbacks(self, mock_getfloat):
        """Test that pause callback can be triggered multiple times after limit extension."""
        # Mock config values
        mock_getfloat.side_effect = lambda section, key, fallback: {
            'cost_warning_limit': 8.0,
            'cost_pause_limit': 9.0,
            'cost_error_limit': 11.0
        }[key]

        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)

        # First pause trigger
        cost_tracer._notify_circuit_breaker(9.5)
        self.assertTrue(callback.pause_triggered)
        self.assertTrue(cost_tracer.cost_pause_dispatched)

        # Reset callback state
        callback.pause_triggered = False

        # Extend limits
        cost_tracer.extend_circuit_breaker_limits(2.0)

        # Now trigger pause again with higher cost
        cost_tracer._notify_circuit_breaker(19.0)  # Should trigger pause again
        self.assertTrue(callback.pause_triggered)
        self.assertTrue(cost_tracer.cost_pause_dispatched)

    @patch('code_generation_core_agent.config.config.getfloat')
    def test_extend_circuit_breaker_limits_allows_multiple_warning_callbacks(self, mock_getfloat):
        """Test that warning callback can be triggered multiple times after limit extension."""
        # Mock config values
        mock_getfloat.side_effect = lambda section, key, fallback: {
            'cost_warning_limit': 8.0,
            'cost_pause_limit': 9.0,
            'cost_error_limit': 11.0
        }[key]

        cost_tracer = CostTracer()
        callback = MockCircuitBreakerCallback()
        cost_tracer.register_cost_threshold_callback(callback)

        # First warning trigger
        cost_tracer._notify_circuit_breaker(8.5)
        self.assertTrue(callback.warning_triggered)
        self.assertTrue(cost_tracer.cost_warning_dispatched)

        # Reset callback state
        callback.warning_triggered = False

        # Extend limits
        cost_tracer.extend_circuit_breaker_limits(2.0)

        # Now trigger warning again with higher cost
        cost_tracer._notify_circuit_breaker(17.0)
        self.assertTrue(callback.warning_triggered)
        self.assertTrue(cost_tracer.cost_warning_dispatched)

    @patch('code_generation_core_agent.config.config.getfloat')
    def test_extend_circuit_breaker_limits_multiple_extensions(self, mock_getfloat):
        """Test that multiple extensions work correctly."""
        # Mock config values
        mock_getfloat.side_effect = lambda section, key, fallback: {
            'cost_warning_limit': 8.0,
            'cost_pause_limit': 9.0,
            'cost_error_limit': 11.0
        }[key]

        cost_tracer = CostTracer()

        # First extension
        cost_tracer.extend_circuit_breaker_limits(2.0)
        self.assertEqual(cost_tracer.circuit_breaker.warning_limit, 16.0)
        self.assertEqual(cost_tracer.circuit_breaker.pause_limit, 18.0)
        self.assertEqual(cost_tracer.circuit_breaker.break_limit, 22.0)

        # Second extension - the method always uses original config values, not current values
        cost_tracer.extend_circuit_breaker_limits(1.5)
        self.assertEqual(cost_tracer.circuit_breaker.warning_limit, 12.0)  # 8.0 * 1.5
        self.assertEqual(cost_tracer.circuit_breaker.pause_limit, 13.5)    # 9.0 * 1.5
        self.assertEqual(cost_tracer.circuit_breaker.break_limit, 16.5)   # 11.0 * 1.5

    @patch('code_generation_core_agent.config.config.getfloat')
    def test_extend_circuit_breaker_limits_thread_safety(self, mock_getfloat):
        """Test that limit extension is thread-safe."""
        # Mock config values
        mock_getfloat.side_effect = lambda section, key, fallback: {
            'cost_warning_limit': 8.0,
            'cost_pause_limit': 9.0,
            'cost_error_limit': 11.0
        }[key]

        cost_tracer = CostTracer()
        results = []

        def extend_limits(scale_factor):
            try:
                cost_tracer.extend_circuit_breaker_limits(scale_factor)
                results.append(('success', scale_factor))
            except Exception as e:
                results.append(('error', str(e)))

        # Create multiple threads to extend limits simultaneously
        threads = []
        for i in range(5):
            thread = threading.Thread(target=extend_limits, args=(1.5 + i * 0.1,))
            threads.append(thread)

        # Start all threads
        for thread in threads:
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify all extensions were successful
        self.assertEqual(len(results), 5)
        for result_type, scale_factor in results:
            self.assertEqual(result_type, 'success')
            self.assertGreater(scale_factor, 1.0)
            self.assertLessEqual(scale_factor, 5.0)


if __name__ == '__main__':
    unittest.main()