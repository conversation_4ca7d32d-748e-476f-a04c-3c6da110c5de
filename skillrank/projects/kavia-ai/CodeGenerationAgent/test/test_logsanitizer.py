import pytest
import logging
import io
import time
from contextlib import contextmanager

from code_generation_core_agent.tools.logsanitizer import LogSanitizer, add_sanitizer
from code_generation_core_agent.tools.logsanitizer import SanitizingStreamHandler


@contextmanager
def capture_logs(logger_name='test', level=logging.DEBUG):
    """Context manager to capture log output."""
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)
    stream = io.StringIO()
    handler = logging.StreamHandler(stream)
    handler.setLevel(level)
    logger.addHandler(handler)

    try:
        yield logger, stream
    finally:
        logger.removeHandler(handler)
        handler.close()


@contextmanager
def capture_logs_with_sanitizer(logger_name='test', level=logging.DEBUG):
    """Context manager to capture log output with sanitizing handler."""
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)
    stream = io.StringIO()
    handler = SanitizingStreamHandler(stream)
    handler.setLevel(level)
    logger.addHandler(handler)

    try:
        yield logger, stream
    finally:
        logger.removeHandler(handler)
        handler.close()


class TestLogSanitizer:
    """Comprehensive test suite for LogSanitizer."""

    def test_basic_patterns(self):
        """Test basic key-value patterns."""
        sanitizer = LogSanitizer()

        test_cases = [
            # Input, Expected output
            ('API_KEY=12345', 'API_KEY: ***REDACTED***'),
            ('SECRET_KEY: abcdef', 'SECRET_KEY: ***REDACTED***'),
            ('AUTH_TOKEN="bearer123"', 'AUTH_TOKEN: ***REDACTED***'),
            ("PASSWORD: 'mypass'", 'PASSWORD: ***REDACTED***'),
            ('DB_PASSWORD = secret123', 'DB_PASSWORD: ***REDACTED***'),
        ]

        for input_text, expected in test_cases:
            assert sanitizer._sanitize(input_text) == expected

    def test_case_insensitive(self):
        """Test case insensitive matching."""
        sanitizer = LogSanitizer()

        test_cases = [
            'api_key=12345',
            'Api_Key=12345',
            'API_KEY=12345',
            'aPi_KeY=12345',
        ]

        for test in test_cases:
            result = sanitizer._sanitize(test)
            assert '***REDACTED***' in result
            assert '12345' not in result

    def test_various_separators(self):
        """Test different separator formats."""
        sanitizer = LogSanitizer()

        test_cases = [
            'KEY=value',
            'KEY:value',
            'KEY: value',
            'KEY = value',
            'KEY : value',
            'KEY\' : \'value',
            'KEY" : "value',
            'KEY\' : \'value',
            'KEY" : "value',
        ]

        for test in test_cases:
            result = sanitizer._sanitize(test)
            assert '***REDACTED***' in result
            assert 'value' not in result

    def test_json_like_structures(self):
        """Test JSON-like structures."""
        sanitizer = LogSanitizer()

        test_cases = [
            ('{"api_key": "secret123"}', '{"api_key": ***REDACTED***}'),
            ('{"auth_token":"bearer","user":"john"}', '{"auth_token:***REDACTED***,"user":"john"}'),
            ("[api_key: 'secret']", "[api_key: ***REDACTED***]"),
        ]

        for input_text, expected in test_cases:
            result = sanitizer._sanitize(input_text)
            assert '***REDACTED***' in result
            assert 'secret' not in result
            assert 'bearer' not in result

    def test_environment_variables(self):
        """Test environment variable patterns."""
        sanitizer = LogSanitizer()

        env_vars = [
            'AWS_SECRET_ACCESS_KEY=aws123',
            'STRIPE_SECRET_KEY=sk_live_123',
            'GITHUB_API_TOKEN=ghp_123',
            'DATABASE_PASSWORD=dbpass',
            'OPENAI_API_KEY=sk-proj-123',
            'MY_CUSTOM_SECRET_KEY=custom123',
            'GEMINI_API_KEY=gemini123',
        ]

        for env_var in env_vars:
            result = sanitizer._sanitize(env_var)
            assert '***REDACTED***' in result
            assert not any(
                secret in result for secret in ['aws123', 'sk_live_123', 'ghp_123', 'dbpass', 'custom123', 'gemini123'])

    def test_multiline_logs(self):
        """Test multiline log messages."""
        sanitizer = LogSanitizer()

        multiline = """
        Configuration loaded:
        API_KEY=12345
        DATABASE_URL=****************************
        SECRET_TOKEN: abcdef
        DEBUG=true
        """

        result = sanitizer._sanitize(multiline)
        assert result.count('***REDACTED***') >= 2
        assert '12345' not in result
        assert 'abcdef' not in result

    def test_false_positives(self):
        """Test that non-sensitive data is not redacted (some false positives are acceptable for security)."""
        sanitizer = LogSanitizer()

        safe_logs = [
            'User logged in successfully',
            'Processing payment for user_id=12345',
            'Keyboard shortcut detected',
            'The key feature of this system',
            'Secret mission completed',  # 'Secret' alone without KEY/TOKEN suffix
            'Password requirements: 8 characters',  # Just describing password
        ]

        for log in safe_logs:
            result = sanitizer._sanitize(log)
            assert result == log  # Should remain unchanged

    def test_partial_matches(self):
        """Test partial matches and word boundaries."""
        sanitizer = LogSanitizer()

        test_cases = [
            # Should match
            ('MY_API_KEY=123', True),
            ('CUSTOM_SECRET_KEY=abc', True),
            ('auth_token_key=xyz', True),
        ]

        for text, should_match in test_cases:
            result = sanitizer._sanitize(text)
            if should_match:
                assert '***REDACTED***' in result
            else:
                assert result == text

    def test_custom_replacement(self):
        """Test custom replacement text."""
        custom_text = '[HIDDEN]'
        sanitizer = LogSanitizer(replacement=custom_text)

        result = sanitizer._sanitize('API_KEY=12345')
        assert custom_text in result
        assert '***REDACTED***' not in result
        assert '12345' not in result

    def test_logging_integration(self):
        """Test integration with Python logging module."""
        with capture_logs_with_sanitizer() as (logger, stream):
            # Test various log levels
            logger.debug('Debug: API_KEY=debug123')
            logger.info('Info: SECRET_TOKEN=info123')
            logger.warning('Warning: AUTH_KEY=warn123')
            logger.error('Error: PASSWORD=error123')

            output = stream.getvalue()
            assert 'debug123' not in output
            assert 'info123' not in output
            assert 'warn123' not in output
            assert 'error123' not in output
            assert output.count('***REDACTED***') == 4

    def test_logging_with_args(self):
        """Test logging with arguments."""
        with capture_logs_with_sanitizer() as (logger, stream):
            # Dict args
            logger.info('User data: %(data)s', {'data': 'API_KEY=12345'})

            # Positional args
            logger.info('Keys: %s, %s', 'SECRET_KEY=abc', 'normal_value')

            output = stream.getvalue()
            assert '12345' not in output
            assert 'abc' not in output
            assert 'normal_value' in output

    def test_complex_scenarios(self):
        """Test complex real-world scenarios."""
        sanitizer = LogSanitizer()

        complex_logs = [
            # Multiple keys in one line
            ('export API_KEY=123 && export SECRET_KEY=456',
             lambda r: '123' not in r and '456' not in r and r.count('***REDACTED***') == 2),
        ]

        for log, validator in complex_logs:
            result = sanitizer._sanitize(log)
            assert validator(result), f"Failed for: {log}"

    def test_unicode_and_special_chars(self):
        """Test Unicode and special characters."""
        sanitizer = LogSanitizer()

        test_cases = [
            'API_KEY=café123',
            'SECRET_KEY=🔑123',
            'AUTH_TOKEN=test\\n123',
            'PASSWORD=test\t123',
        ]

        for test in test_cases:
            result = sanitizer._sanitize(test)
            assert '***REDACTED***' in result
            # Check that the sensitive part is redacted, but allow some remaining text
            assert not any(secret in result for secret in ['café123', '🔑123', 'test\\n123', 'test\t123'])

    def test_performance(self):
        """Test performance with large logs."""
        sanitizer = LogSanitizer()

        # Create a large log entry
        large_log = '\n'.join([
            f'Line {i}: API_KEY=secret{i} and some other text'
            for i in range(1000)
        ])

        start_time = time.time()
        result = sanitizer._sanitize(large_log)
        elapsed = time.time() - start_time

        # Should process 1000 lines in under 100ms
        assert elapsed < 0.1, f"Processing took {elapsed:.3f}s"
        assert result.count('***REDACTED***') == 1000
        assert 'secret' not in result

    def test_name_value_pairs(self):
        """Test name-value pairs where the key is in 'name' field and value is in 'value' field."""
        sanitizer = LogSanitizer()

        test_cases = [
            # Standard format
            ('{"name": "OPENAI_API_KEY", "value": "********************************************************"}',
             lambda r: '********************************************************' not in r and '***REDACTED***' in r),
            # With extra whitespace
            ('{"name": "SECRET_KEY", "value": "my-secret-value", "other": "data"}',
             lambda r: 'my-secret-value' not in r and '***REDACTED***' in r),
            # Different spacing
            ('{"name":"API_KEY","value":"secret123"}',
             lambda r: 'secret123' not in r and '***REDACTED***' in r),
            # With other fields
            ('{"type": "config", "name": "DATABASE_PASSWORD", "value": "dbpass123", "env": "prod"}',
             lambda r: 'dbpass123' not in r and '***REDACTED***' in r),
        ]

        for input_text, validator in test_cases:
            result = sanitizer._sanitize(input_text)
            assert validator(result), f"Failed for: {input_text}"

    def test_usage_statistics_not_redacted(self):
        """Test that usage statistics are not redacted."""
        sanitizer = LogSanitizer()

        usage_logs = [
            'Usage: Prompt Tokens: 1234, Completion Tokens: 567, Tokens: 1801',
            'Prompt Tokens: 1000',
            'Completion Tokens: 500',
            'Tokens: 1500',
            'Usage: Prompt Tokens: 1234, Completion Tokens: 567, Tokens: 1801, OPENAI_API_KEY=sk-1234567890',
        ]

        for log in usage_logs:
            result = sanitizer._sanitize(log)
            # Usage statistics should remain unchanged
            assert 'Prompt Tokens:' in result or 'Completion Tokens:' in result or 'Tokens:' in result
            # But API keys should be redacted
            if 'OPENAI_API_KEY=' in log:
                assert 'sk-1234567890' not in result
                assert '***REDACTED***' in result

    def test_edge_cases(self):
        """Test edge cases."""
        sanitizer = LogSanitizer()

        edge_cases = [
            ('', ''),  # Empty string
            ('API_KEY=', 'API_KEY='),  # No value
            ('=API_KEY', '=API_KEY'),  # No key
            ('API_KEY==123', 'API_KEY: ***REDACTED***'),  # Double equals
            ('API_KEY:=123', 'API_KEY: ***REDACTED***'),  # Mixed separators
            ('   API_KEY=123   ', '   API_KEY: ***REDACTED***   '),  # Whitespace
        ]

        for input_text, expected in edge_cases:
            result = sanitizer._sanitize(input_text)
            assert result == expected

    def test_specific_patterns(self):
        """Test specific patterns mentioned in requirements."""
        sanitizer = LogSanitizer()

        patterns = [
            # Original patterns from requirements
            ('....KEY.... : {value}', lambda r: '{value}' not in r),
            ('KEY" :', lambda r: '***REDACTED***' in r),
            ("KEY' =", lambda r: '***REDACTED***' in r),
            ('KEY = ', lambda r: '***REDACTED***' in r),
        ]

        for pattern, validator in patterns:
            # Add a value to make it detectable
            if pattern.endswith((' :', ' =', '= ')):
                test_input = pattern + 'secretvalue'
                result = sanitizer._sanitize(test_input)
                assert 'secretvalue' not in result
                assert validator(result)


@pytest.mark.parametrize('key_type', [
    'KEY', 'TOKEN', 'SECRET', 'PASSWORD', 'PASS', 'PWD', 'AUTH', 'API', 'CRED'
])
def test_all_key_types(key_type):
    """Parametrized test for all supported key types."""
    sanitizer = LogSanitizer()

    test_value = f'MY_{key_type}=testvalue'
    result = sanitizer._sanitize(test_value)
    assert 'testvalue' not in result
    assert '***REDACTED***' in result


if __name__ == '__main__':
    pytest.main([__file__, '-v'])