import json
import os
import string
import sys
import time
import unittest
import random
import logging

from . import agent_cli
from .data.test_cases import INTERACTIVE_WORK_ITEMS

WORK_ITEM_SIMPLE = {
    "component_name": "Browser validation",
    "description": "Write a Guess My Number game that generates a random number "
                   "between 1 and 100 and asks the user to guess the number.",
    "interfaces": "",
    "algorithms": "",
    "pseudocode": ""
}


class TestCodeGenerator(unittest.TestCase):

    @staticmethod
    def wait_for_some_code_generated(dir_path):
        # Traverse the directory and check if there are any .py, .js, .html or .c, .cpp files
        # If there are, return True, else return False
        for root, dirs, files in os.walk(dir_path):
            for file in files:
                if (file.endswith('.py') or file.endswith('.js')
                        or file.endswith('.html') or file.endswith('.c') or file.endswith('.cpp')):
                    # Check if any of the files have the content 'Hello, user!'
                    with open(os.path.join(root, file), 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if 'Hello, user!' in content:
                            print(f"Code generated in {file}: {content}")
                            return True

        print("No code generated yet... Checking again shortly.")
        return False

    @staticmethod
    def check_knowledge_directory_created(dir_path):
        knowledge_dir = os.path.join(dir_path, '.knowledge')
        if os.path.exists(knowledge_dir):
            print("Knowledge directory created successfully.")
            return True
        else:
            print("Knowledge directory not created.")
            return False

    @staticmethod
    def check_file_monitoring_started(dir_path):
        f = os.path.join(dir_path, '.file_activity_report.json')
        # Check if content is JSON  and has top_n_last_10_min_accessed key
        if os.path.exists(f):
            with open(f, 'r') as file:
                try:
                    content = file.read()
                    if '"top_n_last_10_min_accessed"' in content:
                        print("File monitoring started successfully.")
                        return True
                except json.JSONDecodeError:
                    print("File monitoring JSON format is incorrect.")
                    return False
        else:
            print("File monitoring not started yet.")
            return False

    def test_multi_container_code_generation(self):
        """
        Create a code generation agent and start it.
        """
        task_logger = logging.getLogger("TaskExecutionAgent")

        random.seed(time.time())
        session_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        job = INTERACTIVE_WORK_ITEMS[1]

        agent = agent_cli.ShellCGExecutor(job['work_item'],
                                          job['user_input_list'], session_id=session_id,
                                          log_handler = task_logger)

    def test_code_generation(self):
        """
        Create a code generation agent and start it.
        Wait for some code to be generated.
        """
        # Directly capture logs from the TaskExecutionAgent logger
        task_logger = logging.getLogger("TaskExecutionAgent")
        init_success = [False]

        # Create handler to capture the initialization message
        class InitHandler(logging.Handler):
            def emit(self, record):
                message = record.getMessage()
                if "Universal init completed successfully" in message:
                    print(f"✓ Found initialization message: {message}")
                    init_success[0] = True


        handler = InitHandler()
        random.seed(time.time())
        session_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        job = INTERACTIVE_WORK_ITEMS[1]

        agent = agent_cli.ShellCGExecutor(job['work_item'], job['user_input_list'], session_id=session_id,
                                          log_handler = handler)
        agent.start()

        code_generated = False
        for _ in range(60):
            if TestCodeGenerator.wait_for_some_code_generated(agent.container_dir):
                code_generated = True
                break
            self.assertFalse(agent.thread_exited_with_error)
            time.sleep(10)

        agent.exit()

        # Check code generation
        self.assertTrue(code_generated)

        # Check universal init message
        self.assertTrue(init_success[0], "The 'Universal init completed successfully' log message was never encountered")

        # Check file monitoring
        self.assertTrue(TestCodeGenerator.check_file_monitoring_started(agent.container_dir),
                        "File monitoring was never started")

        # Check knowledge directory
        self.assertTrue(TestCodeGenerator.check_knowledge_directory_created(agent.session_dir),
                        "Knowledge directory was never created")