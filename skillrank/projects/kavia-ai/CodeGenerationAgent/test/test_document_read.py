import pytest
import os
import tempfile
import shutil
from unittest.mock import Mo<PERSON>, patch, MagicMock
from pathlib import Path

from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools


class TestReadDocument:
    """Test suite for the read_document function of ContainerFileTools."""

    @pytest.fixture
    def setup_tool(self, tmp_path):
        """Setup ContainerFileTools instance with test documents."""
        base_path = tmp_path / "test_container"
        base_path.mkdir(exist_ok=True)

        # Mock callbacks
        callbacks = {
            "terminal_output_callback": Mock(),
            "codeview_callback": Mock(),
            "agent_message_callback": Mock()
        }

        logger = Mock()
        tool = ContainerFileTools(callbacks, str(base_path), logger)

        test_dir = Path(__file__).parent
        src_docs_dir = test_dir / "data" / "documents"
        dest_docs_dir = base_path / "data" / "documents"

        dest_docs_dir.mkdir(parents=True, exist_ok=True)

        test_files = [
            "testdoc.html",
            "testdoc.rtf",
            "testdoc.docx",
            "testdoc.pdf",
            "testdoc.ods",
            "testdoc.odt",
            "testdoc.xlsx"
        ]

        for doc_file in test_files:
            src_path = src_docs_dir / doc_file
            if src_path.exists():
                shutil.copy(str(src_path), str(dest_docs_dir / doc_file))
            else:
                (dest_docs_dir / doc_file).touch()

        return tool, base_path

    def test_read_pdf_document(self, setup_tool):
        """Test reading the testdoc.pdf file."""
        tool, base_path = setup_tool

        result = tool.read_document("data/documents/testdoc.pdf")

        assert result["status"] == "SUCCESS"
        assert result["format"] == ".pdf"
        assert result["path"].endswith("testdoc.pdf")
        assert "TESTDOCUMENT" in result["content"]
        assert result["lines_read"] >= 1
        assert result["total_lines"] >= 1
        assert "is_full_content_loaded" in result

    def test_read_docx_document(self, setup_tool):
        """Test reading the testdoc.docx file."""
        tool, base_path = setup_tool

        result = tool.read_document("data/documents/testdoc.docx")

        assert result["status"] == "SUCCESS"
        assert result["format"] == ".docx"
        assert result["path"].endswith("testdoc.docx")
        assert "TESTDOCUMENT" in result["content"]
        assert result["lines_read"] >= 1

    def test_read_xlsx_document(self, setup_tool):
        """Test reading the testdoc.docx file."""
        tool, base_path = setup_tool

        result = tool.read_document("data/documents/testdoc.xlsx")
        print(result)
        assert result["status"] == "SUCCESS"
        assert result["format"] == ".xlsx"
        assert result["path"].endswith("testdoc.xlsx")
        assert "test,this" in result["content"]

    def test_read_html_document(self, setup_tool):
        """Test reading the testdoc.html file."""
        tool, base_path = setup_tool

        result = tool.read_document("data/documents/testdoc.html")

        assert result["status"] == "SUCCESS"
        assert result["format"] == ".html"
        assert result["path"].endswith("testdoc.html")
        assert "TESTDOCUMENT" in result["content"]
        # HTML should have scripts and styles removed
        assert "<script>" not in result["content"]
        assert "<style>" not in result["content"]

    def test_read_rtf_document(self, setup_tool):
        """Test reading the testdoc.rtf file."""
        tool, base_path = setup_tool

        result = tool.read_document("data/documents/testdoc.rtf")

        assert result["status"] == "SUCCESS"
        assert result["format"] == ".rtf"
        assert result["path"].endswith("testdoc.rtf")
        assert "TESTDOCUMENT" in result["content"]
        # RTF control codes should be stripped
        assert "\\rtf1" not in result["content"]
        assert "\\par" not in result["content"]

    def test_read_odt_document(self, setup_tool):
        """Test reading the testdoc.odt file."""
        tool, base_path = setup_tool

        result = tool.read_document("data/documents/testdoc.odt")

        assert result["status"] == "SUCCESS"
        assert result["format"] == ".odt"
        assert result["path"].endswith("testdoc.odt")
        assert "TESTDOCUMENT" in result["content"]

    def test_read_ods_document(self, setup_tool):
        """Test reading the testdoc.ods file (OpenDocument Spreadsheet)."""
        tool, base_path = setup_tool

        # Create a simple CSV file as .ods for testing if pandas can't handle it
        ods_path = base_path / "data" / "documents" / "testdoc.ods"
        if ods_path.stat().st_size == 0:  # If it's a dummy file
            ods_path.write_text("Name,Value\nTest1,100\nTest2,200")

        result = tool.read_document("data/documents/testdoc.ods")

        if result["status"] == "SUCCESS":
            assert result["format"] == ".ods"
            assert len(result["content"]) > 0
        else:
            assert "Unsupported file format" in result["message"] or "Missing Python library" in result["message"]

    def test_read_with_start_line(self, setup_tool):
        """Test reading documents with start_line parameter."""
        tool, base_path = setup_tool

        full_result = tool.read_document("data/documents/testdoc.html")

        if full_result["status"] == "SUCCESS" and full_result["total_lines"] > 5:
            partial_result = tool.read_document("data/documents/testdoc.html", start_line=2)

            assert partial_result["status"] == "SUCCESS"
            assert partial_result["lines_read"] < full_result["lines_read"]
            assert partial_result["is_full_content_loaded"] is False
            assert len(partial_result["content"]) < len(full_result["content"])

    def test_file_not_found(self, setup_tool):
        """Test handling of non-existent files."""
        tool, base_path = setup_tool

        result = tool.read_document("data/documents/nonexistent.pdf")

        assert result["status"] == "ERROR"
        assert "File not found" in result["message"]

    def test_invalid_start_line(self, setup_tool):
        """Test handling of invalid start_line parameter."""
        tool, base_path = setup_tool

        # Try to read with start_line beyond file length
        result = tool.read_document("data/documents/testdoc.html", start_line=99999)

        if result["status"] == "ERROR":
            assert "exceeds total lines" in result["message"]

    def test_callbacks_are_invoked(self, setup_tool):
        """Test that callbacks are properly invoked when reading documents."""
        tool, base_path = setup_tool

        # Read any test document
        result = tool.read_document("data/documents/testdoc.html")

        if result["status"] == "SUCCESS":
            # Check that output callback was called
            tool.output_callback.assert_called()
            output_calls = tool.output_callback.call_args_list
            assert any("Reading" in str(call) for call in output_calls)

            # Check that codeview callback was called
            tool.codeview_callback.assert_called_once()
            codeview_data = tool.codeview_callback.call_args[0][0]
            assert codeview_data["operation"] == "read"
            assert "content" in codeview_data
            assert "filename" in codeview_data


    def test_max_lines_limit(self, setup_tool):
        """Test that documents are truncated at MAX_DOCUMENT_LINES."""
        tool, base_path = setup_tool

        large_file_path = base_path / "data" / "documents" / "large_file.txt"
        large_file_path.parent.mkdir(parents=True, exist_ok=True)

        with open(large_file_path, 'w') as f:
            for i in range(10005):
                f.write(f"Line {i}\n")

        result = tool.read_document("data/documents/large_file.txt")

        assert result["status"] == "SUCCESS"
        assert result["lines_read"] == 10000
        assert result["total_lines"] == 10005
        assert result["truncated"] is True
        assert result["is_full_content_loaded"] is False
        assert "truncated" in result["message"].lower()

    @pytest.mark.parametrize("filename,expected_ext", [
        ("testdoc.html", ".html"),
        ("testdoc.rtf", ".rtf"),
        ("testdoc.docx", ".docx"),
        ("testdoc.pdf", ".pdf"),
        ("testdoc.odt", ".odt"),
    ])
    def test_individual_format(self, setup_tool, filename, expected_ext):
        """Parameterized test for each document format."""
        tool, base_path = setup_tool

        result = tool.read_document(f"data/documents/{filename}")

        # We don't require all formats to succeed (some may need libraries)
        # but we validate the response structure
        assert "status" in result
        assert "message" in result or "content" in result

        if result["status"] == "SUCCESS":
            assert result["format"] == expected_ext
            assert "path" in result
            assert "content" in result
            assert "lines_read" in result
            assert "total_lines" in result
            assert "is_full_content_loaded" in result

    def test_exact_content_extraction(self, setup_tool):
        """Test that all documents extract exactly 'TESTDOCUMENT'."""
        tool, base_path = setup_tool

        test_files = [
            "testdoc.html",
            "testdoc.rtf",
            "testdoc.docx",
            "testdoc.pdf",
            "testdoc.odt",
        ]

        for filename in test_files:
            result = tool.read_document(f"data/documents/{filename}")

            if result["status"] == "SUCCESS":
                # The content should contain TESTDOCUMENT
                # Some formats might have extra whitespace or newlines
                content = result["content"].strip()
                assert "TESTDOCUMENT" in content, f"{filename} did not contain TESTDOCUMENT"

                # For most formats, the content should be exactly TESTDOCUMENT
                # HTML might have some extra text from parsing
                if not filename.endswith('.html'):
                    assert content == "TESTDOCUMENT" or content.replace('\n', '') == "TESTDOCUMENT", \
                        f"{filename} content was '{content}' instead of 'TESTDOCUMENT'"

    def test_create_plain_text_files(self, setup_tool):
        """Test creating and reading various plain text formats."""
        tool, base_path = setup_tool

        # Create test files for different text formats
        text_formats = {
            ".txt": "Plain text content",
            ".md": "# Markdown\n\nThis is **markdown** content.",
            ".log": "2024-01-01 10:00:00 INFO: Log entry",
            ".json": '{"key": "value", "number": 123}',
            ".xml": '<?xml version="1.0"?><root><item>Test</item></root>',
            ".yaml": "key: value\nnumber: 123\nlist:\n  - item1\n  - item2",
            ".yml": "another: yaml file\nwith: content"
        }

        docs_dir = base_path / "data" / "documents"
        docs_dir.mkdir(parents=True, exist_ok=True)

        for ext, content in text_formats.items():
            file_path = docs_dir / f"test{ext}"
            file_path.write_text(content, encoding='utf-8')

            # Test reading
            result = tool.read_document(f"data/documents/test{ext}")

            assert result["status"] == "SUCCESS"
            assert result["format"] == ext
            assert result["content"] == content
            assert result["lines_read"] == len(content.splitlines())


# Integration test that requires actual libraries to be installed
class TestReadDocumentIntegration:
    """Integration tests that work with actual document parsing libraries."""

    @pytest.mark.integration
    @pytest.mark.skipif(not Path(__file__).parent.joinpath("data/documents").exists(),
                        reason="Test documents directory not found")
    def test_read_actual_documents_with_libraries(self, tmp_path):
        """Test reading actual documents with real parsing libraries if available."""
        # This test will use actual libraries if they're installed
        test_dir = Path(__file__).parent
        base_path = test_dir  # Use the test directory as base

        callbacks = {
            "terminal_output_callback": lambda x: print(f"Output: {x}"),
            "codeview_callback": lambda x: None,
            "agent_message_callback": lambda x: None
        }

        logger = Mock()
        tool = ContainerFileTools(callbacks, str(base_path), logger)

        # Test each document
        test_files = [
            "data/documents/testdoc.html",
            "data/documents/testdoc.rtf",
            "data/documents/testdoc.docx",
            "data/documents/testdoc.pdf",
            "data/documents/testdoc.odt",
            "data/documents/testdoc.ods",
        ]

        for file_path in test_files:
            if (test_dir / file_path).exists():
                print(f"\nTesting {file_path}...")
                result = tool.read_document(file_path)

                print(f"  Status: {result['status']}")
                if result["status"] == "SUCCESS":
                    print(f"  Lines: {result['lines_read']}")
                    print(f"  Content preview: {result['content'][:100]}...")
                else:
                    print(f"  Error: {result['message']}")

                # Basic assertions
                assert result["status"] in ["SUCCESS", "ERROR"]
                assert "path" in result or "message" in result