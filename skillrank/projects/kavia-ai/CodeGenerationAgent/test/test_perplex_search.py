#!/usr/bin/env python3
"""
Example usage of the Perplexity search API.
This demonstrates how to initialize and use the PerplexitySearchAPI class.
"""

import os
import logging
import json
from pathlib import Path
from typing import Optional

from code_generation_core_agent.agents.tools.search.perplexity import PerplexitySearchAPI

# Read API key from .env file if it exists

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("dotenv module not found. Skipping loading .env file.")

PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY")


def setup_logger():
    """Set up a basic logger for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger("perplexity_example")


def perform_search(
        api_key: str,
        query: str,
        max_results: int = 10
):
    """
    Perform a search using the Perplexity API.

    Args:
        api_key: Perplexity API key
        query: Search query
        result_type: Type of results to search for (optional)
        max_results: Maximum number of results to return
    """
    logger = setup_logger()

    # Initialize the Perplexity search API
    perplexity = PerplexitySearchAPI(
        logger=logger,
        api_key=api_key,
        max_results=max_results
    )

    # Validate the API key
    if not perplexity.validate_api_key():
        logger.error("API key validation failed. Please check your API key.")
        return

    # Perform the search
    logger.info(f"Searching for: '{query}'")

    response = perplexity.search(
        query=query,
        num_results=max_results
    )

    # Display the results
    print("\n" + "=" * 50)

    print(f"Search Query: {response.query}")
    print(f"Search Engine: {response.search_engine}")
    print(f"Total Results: {response.total_results}")
    print("=" * 50)
    print("Raw response:")
    print("-" * 50)
    print(json.dumps(response.to_dict(), indent=2))
    print("-" * 50)
    # Display direct answer if available
    if response.answer:
        print("\nDirect Answer:")
        print("-" * 50)
        print(response.answer)
        print("-" * 50)

    # Display search results
    print("\nSearch Results:")
    for i, result in enumerate(response.results, 1):
        print(f"\n[{i}] {result.title}")
        if result.url:
            print(f"URL: {result.url}")
        if result.snippet:
            print(f"Snippet: {result.snippet[:150]}...")
        print("-" * 30)

    # Return the response for potential further processing
    return response


def main():
    """Main function to run the example."""
    # Get API key from environment variable
    api_key = PERPLEXITY_API_KEY
    # Example searches
    examples = [
        {
            "query": "Find images of italian pasta",
        },
        {
            "query": "tic tac toe game quotes",
        },
        {
            "query": "pandas in bamboo forest",
        }
    ]

    # Run the examples
    for example in examples:
        print(f"\n\nRunning search for: {example['query']}")
        perform_search(
            api_key=api_key,
            query=example["query"],
        )
        input("\nPress Enter to continue to next example...")

    # Custom query from user input
    custom_query = input("\nEnter your own search query (or press Enter to exit): ")
    if custom_query:
        perform_search(api_key=api_key, query=custom_query)


if __name__ == "__main__":
    main()