import unittest
from unittest.mock import Mock, patch

import yaml

from code_generation_core_agent.config import config
from code_generation_core_agent.llm.chat_interface import ChatInterface
from code_generation_core_agent.llm.llm_interface import LLMInterface

import unittest
import os
import tempfile
from unittest.mock import MagicMock
from datetime import datetime

from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent

import unittest
import os
import tempfile
from datetime import datetime
from unittest.mock import MagicMock

from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools


class TestLLMInterface(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.docker_executor = DockerExecutor()

    def setUp(self):
        # Create a temporary directory for the session
        self.temp_dir = tempfile.mkdtemp()
        self.logs_dir = os.path.join(self.temp_dir, "logs")
        os.makedirs(self.logs_dir, exist_ok=True)

        # Initialize LLM Interface
        self.llm = LLMInterface(
            llm_api_key=os.getenv("OPENAI_API_KEY"),
            session_dir=self.temp_dir,
            instance_name="test_instance"
        )

        self.logger = MagicMock()
        self.file_tools = ContainerFileTools(
            {},
            "/tmp",  # Base path in container
            self.logger,
            executor=self.docker_executor
        )

    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)

    @classmethod
    def tearDownClass(cls):
        # Cleanup all containers
        cls.docker_executor.cleanup_all_containers()

    def test_add_preloaded_files_basic(self):
        """Test basic file preloading functionality"""
        # Create test files in container
        test_content = "Test content"
        self.file_tools.write_file("/tmp/test.txt", test_content)

        sample_py_content = "print('hello')"
        self.file_tools.write_file("/tmp/sample.py", sample_py_content)

        # Initial messages
        messages = [
            {
                "role": "system",
                "created_at": datetime.now().isoformat(),
                "content": "Initial system message"
            },
            {
                "role": "user",
                "created_at": datetime.now().isoformat(),
                "content": "Test message"
            }
        ]

        # Preloaded files configuration
        preloaded_files = ["/tmp/test.txt", "/tmp/sample.py"]

        # Call add_preloaded_files
        result = self.llm.add_preloaded_files(
            messages=messages,
            preloaded_files=preloaded_files,
            function_executor=lambda name, args: self.file_tools.read_files(args)
        )
        # Assertions
        self.assertIsNotNone(result)
        self.assertTrue(isinstance(result, list))
        self.assertEqual(len(result), 4)  # Original 2 messages + 2 preloaded files

        # Verify the preloaded file messages are correct
        preloaded_messages = [msg for msg in result if msg.get("msg_type") == "file_preload"]
        self.assertEqual(len(preloaded_messages), 2)

        # Check content preservation
        for msg in preloaded_messages:
            if msg["file_name"] == "/tmp/test.txt":
                self.assertEqual(msg["file_content"], test_content)
            elif msg["file_name"] == "/tmp/sample.py":
                self.assertEqual(msg["file_content"], sample_py_content)

    def test_add_preloaded_files_with_binary(self):
        """Test handling of binary files"""
        # Create a binary file
        binary_content = bytes([0x89, 0x50, 0x4E, 0x47])  # PNG header
        binary_file = "/tmp/test.png"

        with open(binary_file, 'wb') as f:
            f.write(binary_content)

        messages = [{"role": "user", "content": "Initial message"}]
        preloaded_files = [binary_file]

        result = self.llm.add_preloaded_files(
            messages=messages,
            preloaded_files=preloaded_files,
            function_executor=lambda name, args: self.file_tools.read_files(args)
        )

        # Verify binary file handling
        binary_messages = [msg for msg in result if msg.get("file_name") == binary_file]
        self.assertEqual(binary_messages[0].get("file_content"), "base64://iVBORw==")
        self.assertIn("Preloaded current file", binary_messages[0]["content"])

    def test_add_preloaded_files_nested_directories(self):
        """Test handling of files in nested directories"""
        # Create nested directory structure
        nested_dir = "/tmp/nested/dir/structure"
        self.file_tools.create_directory(nested_dir)

        test_file = os.path.join(nested_dir, "test.txt")
        test_content = "Nested file content"
        self.file_tools.write_file(test_file, test_content)

        messages = [{"role": "user", "content": "Initial message"}]
        preloaded_files = [test_file]

        result = self.llm.add_preloaded_files(
            messages=messages,
            preloaded_files=preloaded_files,
            function_executor=lambda name, args: self.file_tools.read_files(args)
        )

        # Verify nested file handling
        nested_messages = [msg for msg in result if msg.get("file_name") == test_file]
        self.assertEqual(len(nested_messages), 1)
        self.assertEqual(nested_messages[0]["file_content"], test_content)

    def test_add_preloaded_files_special_characters(self):
        """Test handling of files with special characters in names"""
        special_filename = "/tmp/test@#$%^&.txt"
        content = "Special characters test"
        self.file_tools.write_file(special_filename, content)

        messages = [{"role": "user", "content": "Initial message"}]
        preloaded_files = [special_filename]

        result = self.llm.add_preloaded_files(
            messages=messages,
            preloaded_files=preloaded_files,
            function_executor=lambda name, args: self.file_tools.read_files(args)
        )

        # Verify special character handling
        special_messages = [msg for msg in result if msg.get("file_name") == special_filename]
        self.assertEqual(len(special_messages), 1)
        self.assertEqual(special_messages[0]["file_content"], content)

    def test_add_preloaded_files_duplicate_files(self):
        """Test handling of duplicate file entries"""
        test_file = "/tmp/duplicate.txt"
        content = "Duplicate test"
        self.file_tools.write_file(test_file, content)

        messages = [{"role": "user", "content": "Initial message"}]
        preloaded_files = [test_file, test_file]  # Intentionally duplicate

        result = self.llm.add_preloaded_files(
            messages=messages,
            preloaded_files=preloaded_files,
            function_executor=lambda name, args: self.file_tools.read_files(args)
        )

        # Verify duplicate handling
        duplicate_messages = [msg for msg in result if msg.get("file_name") == test_file]
        self.assertEqual(len(duplicate_messages), 1)  # Should only appear once

    def test_add_preloaded_files_mixed_scenarios(self):
        """Test multiple scenarios in one test"""
        # Create various test files
        self.file_tools.write_file("/tmp/valid.txt", "Valid content")

        self.file_tools.create_directory("/tmp/nested")
        self.file_tools.write_file("/tmp/nested/test.py", "print('nested')")

        messages = [
            {"role": "user", "content": "Initial message"},
            {
                "role": "user",
                "content": "Preloaded current file: '/tmp/old.txt'",
                "file_name": "/tmp/old.txt",
                "file_content": "old content",
                "is_preloaded": True,
                "msg_type": "file_preload"
            }
        ]

        preloaded_files = [
            "/tmp/valid.txt",
            "/tmp/nonexistent.txt",
            "/tmp/nested/test.py",
            "/tmp/valid.txt"
        ]

        result = self.llm.add_preloaded_files(
            messages=messages,
            preloaded_files=preloaded_files,
            function_executor=lambda name, args: self.file_tools.read_files(args)
        )
        self.assertTrue(any(msg.get("file_name", "") == "/tmp/valid.txt" for msg in result))
        self.assertTrue(any(msg.get("file_name", "") == "/tmp/nested/test.py" for msg in result))
        self.assertTrue(any("Failed to preload file" in msg.get("content", "")  for msg in result))

        self.assertFalse(any(msg.get("file_name", "") == "/tmp/old.txt" for msg in result))

        preloaded_count = len([msg for msg in result if msg.get("msg_type", "") == "file_preload"])
        self.assertEqual(preloaded_count, 3)  # 2 successful + 1 failure message



if __name__ == '__main__':
    unittest.main()

    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_add_preloaded_files(self):
        # Create test files
        test_file_path = os.path.join(self.temp_dir, "test.txt")
        with open(test_file_path, "w") as f:
            f.write("Test content")

        test_py_path = os.path.join(self.temp_dir, "sample.py")
        with open(test_py_path, "w") as f:
            f.write("print('hello')")

        # Initial messages
        messages = [
            {
                "role": "system",
                "created_at": datetime.now().isoformat(),
                "content": "Initial system message"
            },
            {
                "role": "user",
                "created_at": datetime.now().isoformat(),
                "content": "Test message"
            }
        ]

        # Preloaded files configuration with real files
        preloaded_files = {
            "files": ["test.txt", "sample.py"],
            "base_path": self.temp_dir
        }

        # Call add_preloaded_files with the real executor
        result = self.llm.add_preloaded_files(
            messages=messages,
            preloaded_files=preloaded_files,
            function_executor=self.executor.execute_function
        )

        # Assertions
        self.assertIsNotNone(result)
        self.assertTrue(isinstance(result, list))

        # Check if original messages are preserved
        self.assertEqual(len(result), len(messages))
        self.assertEqual(result[0]["role"], "system")
        self.assertEqual(result[1]["role"], "user")

class TestLLMInterfaceSummarizeMessages(unittest.TestCase):
    def setUp(self):
        self.config_patcher = patch('code_generation_core_agent.llm.llm_interface.config')
        self.mock_config = self.config_patcher.start()
        self.long_message = """
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore 
        et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut 
        aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse 
        cillum dolore eu fugiat nulla pariatur.
        """ * 1000

        def mock_config_get(section, key):
            if section == "SYSTEM" and key == "workspace_dir":
                return "/tmp/kavia"
            return "default"

        def mock_config_getint(section, key):
            if section == "LLM" and key == "preserved_tool_calls":
                return 1
            elif section == "LLM" and key == "llm_wrapper_summarization_token_threshold":
                return 4000
            return 0

        def mock_config_getboolean(section, key):
            if section == "LLM" and key == "debug":
                return True
            return False

        self.mock_config.get.side_effect = mock_config_get
        self.mock_config.getint.side_effect = mock_config_getint
        self.mock_config.getboolean.side_effect = mock_config_getboolean

        # Set up required environment variables
        self.env_patcher = patch.dict('os.environ', {
            'OPENAI_API_KEY': 'dummy-openai-key',
            'ANTHROPIC_API_KEY': 'dummy-anthropic-key',
            'AWS_ACCESS_KEY_ID': 'dummy-aws-key',
            'AWS_SECRET_ACCESS_KEY': 'dummy-aws-secret',
            'AWS_REGION_NAME': 'us-west-2'
        })
        self.env_patcher.start()

        api_key = "dummy-openai-key"
        self.llm_interface = LLMInterface( api_key,
            session_dir=config.get("SYSTEM", "workspace_dir"),
            instance_name="test_instance",
            token_limit=None
        )

        self.llm_interface.message_summarizer = Mock()
        self.llm_interface.message_summarizer.truncate_output.side_effect = [
            "First LLM summary of the conversation",
            "Second LLM summary with different content",
            "Third summary showing another variation"
        ]

    def tearDown(self):
        self.config_patcher.stop()
        self.env_patcher.stop()

    def test_short_message_list_no_summarization(self):
        """Test that short message lists (<=5 messages) are not summarized"""
        original_messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi"},
            {"role": "user", "content": "How are you?"},
            {"role": "assistant", "content": "I'm good"}
        ]
        messages = original_messages.copy()
        result = self.llm_interface.summarize_messages(messages)

        self.assertEqual(result, original_messages)
        self.llm_interface.message_summarizer.truncate_output.assert_not_called()

    def test_preserve_first_assistant_messages(self):
        """Test that the first messages and assistant messages are preserved while others are summarized"""
        messages = [
            {"role": "user", "content": "Initial prompt"},
            {"role": "system", "content": "System message"},
            {"role": "assistant", "content": "First assistant response"},
            {"role": "user", "content": self.long_message},
            {"role": "user", "content": "Query 2"},
            {"role": "user", "content": "Query 3"},
            {"role": "tool", "content": "Tool response", "tool_call_id": "123"}
        ]

        result = self.llm_interface.summarize_messages(messages)

        # First 3 messages should be exactly preserved
        self.assertEqual(result[:3], messages[:3])

        # Verify that a summary message was added
        summary_messages = [msg for msg in result if msg.get('is_summary')]
        self.assertEqual(len(summary_messages), 1)
        self.assertEqual(summary_messages[0]['role'], 'user')

        # Verify the tool message was preserved
        self.assertEqual(result[-1], messages[-1])

    def test_preserve_last_tool_calls(self):
        """Test that the last N tool calls are preserved when summarizing"""
        messages = [
            {"role": "system", "content": "System message"},
            {"role": "user", "content": "Initial prompt"},
            {"role": "user", "content": self.long_message},
            {"role": "user", "some_other_key": "Some other value"},
            {"role": "tool", "content": "Old tool response", "tool_call_id": "1"},
            {"role": "tool", "content": "Recent tool response 1", "tool_call_id": "3"},
            {"role": "tool", "content": "Recent tool response 2", "tool_call_id": "4"},
            {"role": "tool", "content": "Recent tool response 3", "tool_call_id": "5"}
        ]

        result = self.llm_interface.summarize_messages(messages)

        self.assertTrue(len(result) >= 4)  # At least summary + 3 tool calls

        self.assertEqual(result[-3:], messages[-3:])

        summary_messages = [msg for msg in result if msg.get('is_summary')]

        self.assertEqual(len(summary_messages), 1)

    def test_handles_existing_summary(self):
        existing_summary = "Previous conversation summary"

        messages = [
            {"role": "user", "content": "Initial prompt"},
            {"role": "system", "content": "System message"},
            {"role": "assistant", "content": "First response"},
            {"role": "user", "content": existing_summary, "is_summary": True},
            {"role": "user", "content": self.long_message},
            {"role": "user", "content": "New message 2"},
            {"role": "user", "content": "New message 3"},
            {"role": "tool", "content": "Tool response", "tool_call_id": "123"}
        ]

        self.llm_interface.summarize_messages(messages)

        self.llm_interface.message_summarizer.truncate_output.assert_called_once()
        call_args = self.llm_interface.message_summarizer.truncate_output.call_args[0]
        _, prev_summary, _ = call_args

        self.assertEqual(prev_summary, existing_summary)
    def test_no_messages_to_summarize(self):
        messages = [
            {"role": "user", "content": "Initial prompt"},
            {"role": "system", "content": "System message"},
            {"role": "tool", "content": "Tool response 1", "tool_call_id": "1"},
            {"role": "tool", "content": "Tool response 2", "tool_call_id": "2"}
        ]

        original_messages = messages.copy()
        result = self.llm_interface.summarize_messages(messages)

        self.assertEqual(result, original_messages)
        self.llm_interface.message_summarizer.truncate_output.assert_not_called()

    def test_summarization_parameters(self):
        messages = [
            {"role": "user", "content": "Important initial context"},
            {"role": "assistant", "content": "First response"},
            {"role": "user", "content": self.long_message},
            {"role": "user", "content": "Message to be summarized 1"},
            {"role": "user", "content": "Message to be summarized 2"},
            {"role": "user", "content": "Message to be summarized 3"},
            {"role": "tool", "content": "Final tool response", "tool_call_id": "123"}
        ]

        self.llm_interface.summarize_messages(messages)

        self.llm_interface.message_summarizer.truncate_output.assert_called_once()
        args, kwargs = self.llm_interface.message_summarizer.truncate_output.call_args

        messages_to_summarize = args[0]

        self.assertIsInstance(messages_to_summarize, list)
        self.assertEqual(args[2], "Important initial context")
        self.assertTrue(kwargs.get('allways_truncate', False))


if __name__ == '__main__':
    unittest.main()