import pytest
import yaml
from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType

@pytest.fixture
def temp_config_dir_setup(tmp_path):
    platform_dir = tmp_path / "platform"
    platform_dir.mkdir()
    
    common_dir = platform_dir / "common"
    common_dir.mkdir()
    common_config = {
        "version": "1.0",
        "common_setting": "base_value",
        "nested": {
            "key1": "value1",
            "key2": "value2"
        }
    }
    with open(common_dir / "platform.yaml", 'w') as f:
        yaml.dump(common_config, f)
    
    # Create other platform directory and config
    android_dir = platform_dir / "android"
    android_dir.mkdir()
    android_config = {
        "platform": "android",
        "common_setting": "override_value",
        "nested": {
            "key2": "new_value",
            "key3": "value3"
        }
    }
    with open(android_dir / "platform.yaml", 'w') as f:
        yaml.dump(android_config, f)
    
    return tmp_path

@pytest.fixture
def loader_setup(temp_config_dir_setup):
    return PlatformConfigLoader(str(temp_config_dir_setup))

def test_merge_configs(loader_setup):
    base = {
        "key1": "value1",
        "nested": {
            "a": 1,
            "b": 2
        }
    }
    child = {
        "key1": "new_value",
        "nested": {
            "b": 3,
            "c": 4
        },
        "key2": "value2"
    }
    
    merged = loader_setup.merge_configs(base, child)
    
    assert merged["key1"] == "new_value"
    assert merged["key2"] == "value2"
    assert merged["nested"]["a"] == 1
    assert merged["nested"]["b"] == 3
    assert merged["nested"]["c"] == 4

def test_load_yaml_with_inheritance(temp_config_dir_setup):
    base_config = {
        "base_key": "base_value",
        "override_me": "original"
    }
    base_path = temp_config_dir_setup / "base.yaml"
    with open(base_path, 'w') as f:
        yaml.dump(base_config, f)
    
    child_config = {
        "include": "base.yaml",
        "child_key": "child_value",
        "override_me": "new_value"
    }
    child_path = temp_config_dir_setup / "child.yaml"
    with open(child_path, 'w') as f:
        yaml.dump(child_config, f)
    
    loader = PlatformConfigLoader(str(temp_config_dir_setup))
    result = loader._load_yaml(str(child_path))
    
    assert result["base_key"] == "base_value" 
    assert result["child_key"] == "child_value"
    assert result["override_me"] == "new_value"

def test_load_platforms(loader_setup, temp_config_dir_setup):
    ios_dir = temp_config_dir_setup / "platform" / "ios"
    ios_dir.mkdir()
    ios_config = {
        "platform": "ios",
        "ios_specific": "value"
    }
    with open(ios_dir / "platform.yaml", 'w') as f:
        yaml.dump(ios_config, f)
    
    loader_setup._load_platforms()
    
    assert set(loader_setup.platform_configs.keys()) == {
        ApplicationType.COMMON, ApplicationType.ANDROID, ApplicationType.IOS}
    assert loader_setup.platform_configs[ApplicationType.ANDROID]["platform"] == "android"
    assert loader_setup.platform_configs[ApplicationType.IOS]["ios_specific"] == "value"
    assert loader_setup.platform_configs[ApplicationType.COMMON]["common_setting"] == "base_value"

def test_get_platform_config(loader_setup):
    common_config = loader_setup.get_platform_config(ApplicationType.COMMON, FrameworkType.DEFAULT)
    assert common_config["common_setting"] == "base_value"
    
    android_config = loader_setup.get_platform_config(ApplicationType.MOBILE, FrameworkType.ANDROID)
    assert android_config["platform"] == "android"
    assert android_config["common_setting"] == "override_value"
    
    # Test getting non-existent platform
    with pytest.raises(ValueError, match="Unknown platform: nonexistent"):
        loader_setup.get_platform_config("nonexistent", FrameworkType.DEFAULT)

def test_merge_configs_tools_override(loader_setup):
    base = {
        "key1": "value1",
        "tools": {
            "tool1": {"path": "/usr/bin"},
            "tool2": {"path": "/usr/local/bin"}
        }
    }
    child = {
        "key1": "new_value",
        "tools": {
            "tool3": {"path": "/opt/bin"}
        }
    }
    
    merged = loader_setup.merge_configs(base, child)
    
    assert merged["key1"] == "new_value"
    
    assert merged["tools"] == child["tools"]
    assert "tool1" not in merged["tools"]
    assert "tool2" not in merged["tools"]
    assert merged["tools"]["tool3"]["path"] == "/opt/bin"

def test_platform_specific_template_rendering(loader_setup, temp_config_dir_setup):
    # Create a simplified version of our merged template for testing
    test_template = """
    {% set platform = platform|default('generic') %}
    {% if platform == 'flutter' %}
    Flutter specific content
    {% elif platform == 'android' %}
    Android specific content
    {% elif platform == 'ios' %}
    iOS specific content
    {% else %}
    Generic content
    {% endif %}
    """
    
    template_path = temp_config_dir_setup / "test_template.j2"
    with open(template_path, 'w') as f:
        f.write(test_template)
    
    # Create a template renderer using Jinja2
    from jinja2 import Environment, FileSystemLoader
    env = Environment(loader=FileSystemLoader(str(temp_config_dir_setup)))
    
    # Test generic platform (default)
    template = env.get_template("test_template.j2")
    result = template.render()
    assert "Generic content" in result
    
    # Test Android platform
    android_config = loader_setup.get_platform_config(ApplicationType.MOBILE, FrameworkType.ANDROID)
    template = env.get_template("test_template.j2")
    template.globals["platform"] = "android"
    result = template.render()
    assert "Android specific content" in result
    
    # Test iOS platform
    ios_dir = temp_config_dir_setup / "platform" / "ios"
    ios_dir.mkdir(exist_ok=True)
    ios_config = {
        "platform": "ios",
        "ios_specific": "value"
    }
    with open(ios_dir / "platform.yaml", 'w') as f:
        yaml.dump(ios_config, f)
    
    # Reload platforms to include iOS
    loader_setup._load_platforms()
    ios_config = loader_setup.get_platform_config(ApplicationType.MOBILE, FrameworkType.IOS)
    
    template = env.get_template("test_template.j2")
    result = template.render(platform="ios")
    assert "iOS specific content" in result
    
    # Test Flutter platform (not explicitly defined in configs but used in template)
    template = env.get_template("test_template.j2")
    result = template.render(platform="flutter")
    assert "Flutter specific content" in result
