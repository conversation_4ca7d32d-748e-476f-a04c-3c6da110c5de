import json
import unittest
from unittest.mock import patch, MagicMock
from enum import Enum
from code_generation_core_agent.agents.framework.cost_tracker import CostTracer
from code_generation_core_agent.agents.framework.llm_truncate import OutputTruncator, SummarizerType, parse_json_with_cleanup
from code_generation_core_agent.config import config

class TestOutputTruncator(unittest.TestCase):

    def setUp(self):
        self.max_size = 100
        self.model = "gpt-4o-mini"
        self.truncator = OutputTruncator(self.max_size, self.model)

    def test_init(self):
        self.assertIsInstance(self.truncator.cost_tracer, CostTracer)
        self.assertEqual(self.truncator.max_size, self.max_size)
        self.assertEqual(self.truncator.model, self.model)
        self.assertIsInstance(self.truncator.prompt_template, str)

    def test_truncate_output_under_max_size(self):
        output = "Short output"
        result = self.truncator.truncate_output(output)
        self.assertEqual(result, output)

    # def test_truncate_large_message(self):
    #     output = "A" * (self.max_size + 1)
    #     result = self.truncator.truncate_output(output)
    #
    #     expected_strings = ["long string", "repeat", "errors"]
    #     self.assertTrue(any(s in result for s in expected_strings))

    # def test_always_truncate(self):
    #     output = "Short output"
    #     result = self.truncator.truncate_output(output, allways_truncate=True)
    #     print("Result = ", result)
    #     self.assertTrue( len(result) > 0  and result != output )

    def test_cost_tracking(self):
        # Reset the cost for the truncator
        CostTracer().reset_costs()

        # First summarization
        output = "AB" * (self.max_size + 1)
        self.truncator.truncate_output(output)
        first_cost = CostTracer().get_cost("Summarizer")

        self.assertGreater(first_cost, 0)
        self.truncator.truncate_output(output)

        second_cost = CostTracer().get_cost("Summarizer")
        self.assertGreater(second_cost, first_cost)


    def test_different_summarizer_types(self):
        for summarizer_type in SummarizerType:
            truncator = OutputTruncator(self.max_size, self.model, summarizer_type)
            self.assertIsInstance(truncator.prompt_template, str)


class TestSessionContinuationSummarizer(unittest.TestCase):

    def setUp(self):
        # Use a smaller model for testing to reduce costs
        self.truncator = OutputTruncator(10000, summarizer_type=SummarizerType.SESSION_CONTINUATION_SUMMARIZER)

    def test_continuation_message(self):
        # Create a sample chat history and continuation message
        chat_history = [
            {"role": "system", "content": "You are an AI assistant."},
            {"role": "user", "content": "How do I implement a binary search tree in Python?"},
            {"role": "assistant",
             "content": "To implement a binary search tree in Python, start by defining a Node class and a BinarySearchTree class. The Node class should have value, left, and right properties. Here's a basic implementation:\n\n```python\nclass Node:\n    def __init__(self, value):\n        self.value = value\n        self.left = None\n        self.right = None\n\nclass BinarySearchTree:\n    def __init__(self):\n        self.root = None\n        \n    def insert(self, value):\n        if self.root is None:\n            self.root = Node(value)\n        else:\n            self._insert_recursive(self.root, value)\n            \n    def _insert_recursive(self, node, value):\n        if value < node.value:\n            if node.left is None:\n                node.left = Node(value)\n            else:\n                self._insert_recursive(node.left, value)\n        else:\n            if node.right is None:\n                node.right = Node(value)\n            else:\n                self._insert_recursive(node.right, value)\n```\n\nThis is a basic implementation. Would you like me to explain how to implement other operations like search, delete, or traversal?"}
        ]

        # Create a continuation message
        new_user_message = "Yes, could you show me how to implement the search operation?"

        # Create sample data
        session_data = {
            "chat_history": chat_history,
            "new_user_message": new_user_message
        }

        # Convert to JSON and use the truncator
        input_data = json.dumps(session_data, indent=2, ensure_ascii=False)
        result = self.truncator.truncate_output(input_data, allways_truncate=True)

        # Parse the result
        parsed_result = parse_json_with_cleanup(result)
        # Assert that the message is identified as a continuation
        self.assertTrue(parsed_result["is_continuation"])
        self.assertIn("reason", parsed_result)

    def test_non_continuation_message(self):
        # Create a sample chat history and non-continuation message
        chat_history = [
            {"role": "system", "content": "You are an AI assistant."},
            {"role": "user", "content": "How do I implement a binary search tree in Python?"},
            {"role": "assistant",
             "content": "To implement a binary search tree in Python, start by defining a Node class and a BinarySearchTree class. The Node class should have value, left, and right properties. Here's a basic implementation:\n\n```python\nclass Node:\n    def __init__(self, value):\n        self.value = value\n        self.left = None\n        self.right = None\n\nclass BinarySearchTree:\n    def __init__(self):\n        self.root = None\n        \n    def insert(self, value):\n        if self.root is None:\n            self.root = Node(value)\n        else:\n            self._insert_recursive(self.root, value)\n            \n    def _insert_recursive(self, node, value):\n        if value < node.value:\n            if node.left is None:\n                node.left = Node(value)\n            else:\n                self._insert_recursive(node.left, value)\n        else:\n            if node.right is None:\n                node.right = Node(value)\n            else:\n                self._insert_recursive(node.right, value)\n```\n\nThis is a basic implementation. Would you like me to explain how to implement other operations like search, delete, or traversal?"}
        ]

        # Create a non-continuation message
        new_user_message = "Forget all of that. I'm starting a new project. How do I implement a REST API with Flask?"

        # Create sample data
        session_data = {
            "chat_history": chat_history,
            "new_user_message": new_user_message
        }

        # Convert to JSON and use the truncator
        input_data = json.dumps(session_data, indent=2, ensure_ascii=False)
        result = self.truncator.truncate_output(input_data, allways_truncate=True)

        # Parse the result
        parsed_result = parse_json_with_cleanup(result)

        # Assert that the message is identified as not a continuation
        self.assertFalse(parsed_result["is_continuation"])
        self.assertIn("reason", parsed_result)
