from unittest.mock import  MagicMock

import pytest
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools


@pytest.fixture(scope="session")
def docker_executor():
    executor = DockerExecutor()
    yield executor
    executor.cleanup_all_containers()


@pytest.fixture(scope="module", autouse=True)
def ensure_docker_setup(docker_executor):
    # This fixture doesn't need to do anything,
    # it just ensures docker_executor is called
    pass


@pytest.fixture(scope="module")
def docker_file_tools(docker_executor):
    logger = MagicMock()
    return ContainerFileTools({},"/tmp", logger, executor=docker_executor)


@pytest.mark.asyncio
async def test_read_write_file(docker_executor, docker_file_tools):
    content = "Hello, World!"
    file_path = "/tmp/test_file.txt"

    # Write file
    write_result = docker_file_tools.write_file(file_path, content)
    assert write_result["status"] == "SUCCESS"
    # Read file
    read_result = docker_file_tools.read_file(file_path)
    assert read_result["status"] == "SUCCESS"
    assert read_result["content"] == content


@pytest.mark.asyncio
async def test_append_to_file(docker_file_tools):
    file_path = "/tmp/append_test.txt"
    initial_content = "Initial content\n"
    append_content = "Appended content"

    docker_file_tools.write_file(file_path, initial_content)

    append_result = docker_file_tools.append_to_file(file_path, append_content)
    assert append_result["status"] == "SUCCESS"

    read_result = docker_file_tools.read_file(file_path)
    assert read_result["status"] == "SUCCESS"
    assert read_result["content"] == initial_content + append_content


@pytest.mark.asyncio
async def test_list_directory(docker_file_tools):
    dir_path = "/tmp/test_dir"
    docker_file_tools.create_directory(dir_path)
    docker_file_tools.write_file(f"{dir_path}/file1.txt", "Content 1")
    docker_file_tools.write_file(f"{dir_path}/file2.txt", "Content 2")

    list_result = docker_file_tools.list_directory(dir_path)

    assert list_result["status"] == "SUCCESS", f"result: {list_result}"
    assert set(list_result["items"]) == {"file1.txt", "file2.txt"}, f"items: {list_result['items']}"


@pytest.mark.asyncio
async def test_create_delete_directory(docker_file_tools):
    dir_path = "/tmp/test_create_delete_dir"

    create_result = docker_file_tools.create_directory(dir_path)
    assert create_result["status"] == "SUCCESS"

    list_result = docker_file_tools.list_directory("/tmp")
    assert "test_create_delete_dir" in list_result["items"]

    delete_result = docker_file_tools.delete_file(dir_path)
    assert delete_result["status"] == "SUCCESS"

    list_result = docker_file_tools.list_directory("/tmp")
    assert "test_create_delete_dir" not in list_result["items"]


@pytest.mark.asyncio
async def test_rename_file(docker_file_tools):
    old_path = "/tmp/old_file.txt"
    new_path = "/tmp/new_file.txt"
    content = "Test content"

    result = docker_file_tools.write_file(old_path, content)
    print("result", result)
    assert result["status"] == "SUCCESS"

    rename_result = docker_file_tools.rename_file(old_path, new_path)
    print("rename_result", rename_result)
    assert rename_result["status"] == "SUCCESS"

    read_result = docker_file_tools.read_file(new_path)
    assert read_result["status"] == "SUCCESS"
    assert read_result["content"] == content


@pytest.mark.asyncio
async def test_copy_file(docker_file_tools):
    source_path = "/tmp/source_file.txt"
    dest_path = "/tmp/dest_file.txt"
    content = "Test content for copying"

    docker_file_tools.write_file(source_path, content)
    copy_result = docker_file_tools.copy_file(source_path, dest_path)
    assert copy_result["status"] == "SUCCESS"

    read_result = docker_file_tools.read_file(dest_path)
    assert read_result["status"] == "SUCCESS"
    assert read_result["content"] == content


@pytest.mark.asyncio
async def test_get_file_info(docker_file_tools):
    file_path = "/tmp/info_test_file.txt"
    content = "Test content for file info"

    docker_file_tools.write_file(file_path, content)
    info_result = docker_file_tools.get_file_info(file_path)
    assert info_result["status"] == "SUCCESS"
    assert info_result["info"]["size"] == len(content)
    assert "created" in info_result["info"]
    assert "modified" in info_result["info"]
    assert "accessed" in info_result["info"]


@pytest.mark.asyncio
async def test_search_files(docker_file_tools):
    dir_path = "/tmp/test_dir2"
    docker_file_tools.create_directory(dir_path)

    docker_file_tools.write_file("/tmp/test_dir2/search_test1.txt", "Content 1")
    docker_file_tools.write_file("/tmp/test_dir2/search_test2.txt", "Content 2")
    docker_file_tools.write_file("/tmp/test_dir2/other_file.txt", "Other content")

    search_result = docker_file_tools.search_files("/tmp/test_dir2", "search_test*.txt")
    assert search_result["status"] == "SUCCESS"
    assert len(search_result["matches"]) == 2
    assert "/tmp/test_dir2/search_test1.txt" in search_result["matches"]
    assert "/tmp/test_dir2/search_test2.txt" in search_result["matches"]


@pytest.mark.asyncio
async def test_file_with_spaces(docker_file_tools):
    file_path = "/tmp/file with spaces.txt"
    content = "Content with spaces"

    write_result = docker_file_tools.write_file(file_path, content)
    assert write_result["status"] == "SUCCESS"

    read_result = docker_file_tools.read_file(file_path)
    print("read_result", read_result)
    assert read_result["status"] == "SUCCESS"
    assert read_result["content"] == content


@pytest.mark.asyncio
async def test_error_file_not_found(docker_file_tools):
    nonexistent_file = "/tmp/nonexistent_file.txt"
    read_result = docker_file_tools.read_file(nonexistent_file)
    print("status", read_result)
    assert read_result["status"] == "ERROR"
    assert "File not found" in read_result["message"]


@pytest.mark.asyncio
async def test_error_permission_denied(docker_file_tools):
    root_file = "/root/test_file.txt"
    write_result = docker_file_tools.write_file(root_file, "Test content")
    assert write_result["status"] == "ERROR"
    assert "Permission denied" in write_result["message"]

@pytest.mark.asyncio
async def test_edit_file_addition(docker_file_tools):
    file_path = "/tmp/test_edit_add.txt"
    initial_content = "line 1\nline 2\nline 3\n"
    docker_file_tools.write_file(file_path, initial_content)

    changes = f"""{file_path}
<<<<<<< SEARCH
line 1
line 2
line 3
=======
line 1
some new line
some more new line
line 2
line 3
>>>>>>> REPLACE
"""
    result = docker_file_tools.edit_file(changes)
    assert result["status"] == "SUCCESS"

    read_result = docker_file_tools.read_file(file_path)
    assert read_result["status"] == "SUCCESS"
    expected_content = "line 1\nsome new line\nsome more new line\nline 2\nline 3\n"
    assert read_result["content"] == expected_content

@pytest.mark.asyncio
async def test_edit_line_removal(docker_file_tools):
    file_path = "/tmp/test_line_remove.txt"
    initial_content = "line 1\nline 2\nline 3\nline 4\n"
    docker_file_tools.write_file(file_path, initial_content)

    changes = f"""{file_path}
<<<<<<< SEARCH
line 1
line 2
line 3
line 4
=======
line 1
line 4
>>>>>>> REPLACE
"""
    result = docker_file_tools.edit_file(changes)
    assert result["status"] == "SUCCESS"

    read_result = docker_file_tools.read_file(file_path)
    assert read_result["status"] == "SUCCESS"
    expected_content = "line 1\nline 4\n"
    assert read_result["content"] == expected_content

@pytest.mark.asyncio
async def test_edit_file_error(docker_file_tools):
    file_path = "/tmp/test_edit_error.txt"
    initial_content = "line 1\nline 2\nline 3\nline 4\n"
    docker_file_tools.write_file(file_path, initial_content)

    changes = f"""{file_path}
<<<<<<< SEARCH
non-existent line
=======
new line
>>>>>>> REPLACE
"""
    result = docker_file_tools.edit_file(changes)
    assert result["status"] == "ERROR"
    assert "This SEARCH block failed to exactly" in result["message"]



@pytest.mark.asyncio
async def test_cpp_edit(docker_file_tools):

    file_path = "/tmp/test_native.cpp"
    os.makedirs("/tmp/.init", exist_ok=True)
    with open("/tmp/.init/.init-build-cmd", "w") as f:
        f.write("""
    {
        "command": "g++ /tmp/test_native.cpp",
        "working_directory": "/tmp/build"
    }
    """)
    initial_content = "int main() {\n  return 0;\n}\n"
    docker_file_tools.write_file(file_path, initial_content)

    changes = f"""{file_path}
<<<<<<< SEARCH
int main() {{
  return 0;
}}
=======
int main() {{
  int i;
  return 0;
}}
>>>>>>> REPLACE
"""
    result = docker_file_tools.edit_file(changes)

    assert result["status"] == "SUCCESS"

    read_result = docker_file_tools.read_file(file_path)
    assert read_result["status"] == "SUCCESS"


import os
import json
import unittest
from unittest.mock import MagicMock, patch, mock_open

import pytest
from code_generation_core_agent.agents.framework.llm_truncate import shorten_message
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools, DockerFileToolsError


class TestValidateBuild(unittest.TestCase):
    def setUp(self):
        self.callback_functions = {}
        self.base_path = "/tmp"
        self.logger = MagicMock()
        self.executor = MagicMock()
        self.file_tools = ContainerFileTools(
            self.callback_functions,
            self.base_path,
            self.logger,
            executor=self.executor
        )

    @patch('os.path.exists')
    def test_validate_build_with_linter_success(self, mock_exists):
        """Test validate_build when linter script exists and runs successfully."""
        # Setup
        mock_exists.return_value = True
        # Patch the linter path check directly without mocking os.path.join
        linter_path = os.path.join(self.base_path, ".init/.linter.sh")
        self.file_tools._run_docker_command = MagicMock(return_value="Linting passed")

        # Execute
        result, message = self.file_tools.validate_build()

        # Assert
        assert result is True
        assert message == ""
        self.file_tools._run_docker_command.assert_called_once_with(f'bash {linter_path}')

    @patch('os.path.exists')
    def test_validate_build_with_linter_failure(self, mock_exists):
        """Test validate_build when linter script exists but fails."""
        # Setup
        mock_exists.return_value = True
        linter_path = os.path.join(self.base_path, ".init/.linter.sh")
        error_message = "Linting failed: syntax error on line 42"
        self.file_tools._run_docker_command = MagicMock(
            side_effect=DockerFileToolsError(error_message)
        )

        # Execute
        result, message = self.file_tools.validate_build()

        # Assert
        assert result is False
        assert message == shorten_message(error_message)
        self.file_tools._run_docker_command.assert_called_once_with(f'bash {linter_path}')

    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open,
           read_data='{"command": "g++ main.cpp", "working_directory": "/tmp/build"}')
    def test_validate_build_with_build_command_success(self, mock_file, mock_exists):
        """Test validate_build when falling back to build command and succeeds."""
        # Setup
        mock_exists.return_value = False  # No linter script
        self.file_tools._run_docker_command = MagicMock(return_value="Build succeeded")

        # Execute
        result, message = self.file_tools.validate_build()

        # Assert
        assert result is True
        assert message == ""
        # Should be called twice - once for mkdir and once for the build command
        assert self.file_tools._run_docker_command.call_count == 2
        self.file_tools._run_docker_command.assert_any_call('mkdir -p /tmp/build')
        self.file_tools._run_docker_command.assert_any_call('g++ main.cpp', work_directory='/tmp/build')

    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open,
           read_data='{"command": "g++ main.cpp", "working_directory": "/tmp/build"}')
    def test_validate_build_with_build_command_failure(self, mock_file, mock_exists):
        """Test validate_build when falling back to build command and fails."""
        # Setup
        mock_exists.return_value = False  # No linter script

        # First call succeeds (mkdir), second call fails (build command)
        self.file_tools._run_docker_command = MagicMock(side_effect=[
            "Directory created",
            DockerFileToolsError("Build failed: undefined reference to 'function'")
        ])

        # Execute
        result, message = self.file_tools.validate_build()

        # Assert
        assert result is False
        assert "Build failed" in message
        assert self.file_tools._run_docker_command.call_count == 2

    @patch('os.path.exists')
    @patch('builtins.open')
    def test_validate_build_with_invalid_build_config(self, mock_file, mock_exists):
        """Test validate_build when build config is invalid or missing."""
        # Setup
        mock_exists.return_value = False  # No linter script
        mock_file.side_effect = FileNotFoundError("File not found")

        # Execute
        result, message = self.file_tools.validate_build()

        # Assert
        assert result is True  # Should return True in case of error with build config
        assert message == ""

    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open,
           read_data='{"command": "g++ main.cpp", "working_directory": "/tmp/build"}')
    def test_validate_build_with_long_error_message(self, mock_file, mock_exists):
        """Test validate_build with truncation of long error messages."""
        # Setup
        mock_exists.return_value = False  # No linter script

        # Generate a very long error message - make it over 20000 chars to ensure truncation occurs
        long_error = "Error: " + "x" * 25000

        self.file_tools._run_docker_command = MagicMock(side_effect=[
            "Directory created",
            DockerFileToolsError(long_error)
        ])

        # Execute
        result, message = self.file_tools.validate_build()

        # Assert
        assert result is False
        assert len(message) < len(long_error)  # Message should be truncated
        assert "truncated" in message  # Should contain truncation indicator


@pytest.mark.asyncio
async def test_validate_build_integration(docker_executor, docker_file_tools):
    """Integration test for validate_build using the Docker executor."""
    # Create a build config
    build_config = {
        "command": "g++ /tmp/test_build.cpp -o /tmp/test_build",
        "working_directory": "/tmp"
    }

    # Ensure the .init directory exists
    os.makedirs("/tmp/.init", exist_ok=True)

    # Write the build config
    with open("/tmp/.init/.init-build-cmd", "w") as f:
        json.dump(build_config, f)

    # 1. Test with valid C++ code
    docker_file_tools.write_file("/tmp/test_build.cpp", "int main() { return 0; }")
    result, message = docker_file_tools.validate_build()
    assert result is True
    assert message == ""

    # 2. Test with invalid C++ code
    docker_file_tools.write_file("/tmp/test_build.cpp", "int main() { undeclared_variable = 10; return 0; }")
    result, message = docker_file_tools.validate_build()
    assert result is False
    assert "undeclared_variable" in message or "not declared" in message

    # 3. Test with a linter script
    docker_file_tools.write_file("/tmp/.init/.linter.sh", """#!/bin/bash
if grep -q "return 0" /tmp/test_build.cpp; then
    exit 0
else
    echo "Error: Missing return statement"
    exit 1
fi
""")
    os.chmod("/tmp/.init/.linter.sh", 0o755)  # Make executable

    # Should pass the linter
    docker_file_tools.write_file("/tmp/test_build.cpp", "int main() { return 0; }")
    result, message = docker_file_tools.validate_build()
    assert result is True
    assert message == ""

    # Should fail the linter
    docker_file_tools.write_file("/tmp/test_build.cpp", "int main() { }")
    result, message = docker_file_tools.validate_build()
    assert result is False
    assert "Missing return statement" in message

    # Clean up
    os.remove("/tmp/.init/.init-build-cmd")
    os.remove("/tmp/.init/.linter.sh")
    os.remove("/tmp/test_build.cpp")