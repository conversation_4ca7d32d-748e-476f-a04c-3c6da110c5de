import re
import time
from unittest.mock import MagicMock, patch

import pytest

from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.visible_shell_tools import VisibleShellTools


@pytest.fixture(scope="session")
def docker_executor():
    executor = DockerExecutor()
    yield executor
    executor.cleanup_all_containers()


@pytest.fixture
def visible_shell(docker_executor):
    base_path = "/tmp"
    logger = MagicMock()
    return VisibleShellTools({}, base_path, logger, executor=docker_executor)


def check_command_is_running(command, visible_shell):
    result = visible_shell.execute_immediate_return_command("ps -eww -o pid,args", "/tmp")

    # Create a regex pattern that matches:
    # 1. Start of line after optional whitespace and PID
    # 2. The exact command (escaped to handle special characters)
    # 3. Either end of line or space followed by arguments
    pattern = r'^\s*\d+\s+' + re.escape(command) + r'(?:\s|$)'

    return bool(re.search(pattern, result["output"], re.MULTILINE))

@pytest.mark.asyncio
async def test_install_and_run_a_blocking_call(visible_shell):
    """
    Goal: Test that a blocking call can be executed and gives a timeout error.
    Steps:
        1.) Install redis and start the server.
        2.) Run a blocking call to redis-cli monitor.
        3.) Verify that the call times out.
        4.) Validate shell is working as expected after the blocking call.
    """

    commands = [
        "sudo apt-get update",
        "sudo apt-get install -y redis-server",
        "redis-server --daemonize yes",
    ]

    for command in commands:
        result = visible_shell.execute_immediate_return_command(command, "/tmp" )
        print(f"Command '{command}' output:\n{result}")

        assert result[
                   "status"] == "SUCCESS", f"Command '{command}' failed with message: {result.get('message', 'No error message')}"
        assert result["returncode"] == 0, f"Command '{command}' returned non-zero exit code: {result['returncode']}"

    result = visible_shell.execute_immediate_return_command("redis-cli monitor", "/tmp")

    assert result["status"] == "ERROR", f"Blocking call didn't time out. result: {result}"
    assert "TimeoutError" in result["message"], "Blocking call didn't time out"

    result = visible_shell.execute_immediate_return_command("echo 'Shell is working'", "/tmp")
    assert result["status"] == "SUCCESS", f"Shell is not working as expected. Result: {result}"
    assert result["returncode"] == 0, f"Shell is not working as expected. Result: {result}"


@pytest.mark.asyncio
async def test_background_startup_failure_triggers_graceful_shutdown():
    """
    Goal: Test that background_startup calls graceful_shutdown when container preparation fails.
    Steps:
        1.) Mock ensure_container to raise an exception
        2.) Mock graceful_shutdown to track if it's called
        3.) Call background_startup
        4.) Verify graceful_shutdown was called due to the failure
    """

    with patch(
            'code_generation_core_agent.agents.tools.executor.docker_executor.graceful_shutdown') as mock_graceful_shutdown:
        # Create executor instance
        executor = DockerExecutor(container_name="test_container_12345_non_existing")
        executor.logger = MagicMock()

        async def failing_ensure_container():
            raise Exception("Container preparation failed")

        executor.ensure_container = failing_ensure_container

        executor.background_startup()

        time.sleep(2.0)

        mock_graceful_shutdown.assert_called_once()

        executor.logger.error.assert_called_once_with(
            "Background container preparation failed: Container preparation failed"
        )