from pprint import pformat
from textwrap import shorten

from code_generation_core_agent.agents.tools.web_browser import WebBrowser, FunctionExecutor
import unittest
from unittest.mock import patch, MagicMock


def truncate_dict_values(d, max_length=120):
    return {k: shorten(str(v), width=max_length, placeholder="...") for k, v in d.items()}

def print_formatted_observation(value):
    truncated_result = truncate_dict_values(value)
    formatted_result = pformat(truncated_result, width=250)
    print(formatted_result)

class TestFunctionExecutor(FunctionExecutor):
    """
    This is a test function executor for the browser shim tool.
    It returns the action without processing it.
    """
    def process_action(self, action):
        return action

class TestBrowserEnvironment(unittest.TestCase):

    def setUp(self):
        self.mock_logger = MagicMock()
        self.shim = WebBrowser(".", self.mock_logger)

    def test_goto_url(self):
        result = self.shim.browser_tool_goto('https://www.google.com/')
        print_formatted_observation(result)
        self.assertEqual(result['status'], 'SUCCESS')


    def test_goto_invalid_url(self):
        result = self.shim.goto('(invalid URL) https://google.com')
        self.assertEqual(result['status'], 'ERROR')
        self.assertIn('Error: Protocol error (Page.navigate): Cannot navigate to invalid URL', result['message'])

class TestBrowserShim(unittest.TestCase):
    def setUp(self):
        self.mock_logger = MagicMock()
        self.shim = WebBrowser(".", self.mock_logger, browser_function_executor=TestFunctionExecutor)

    def test_scroll(self):
        self.assertEqual(self.shim.scroll(0, 200), "scroll(delta_x=0, delta_y=200)")
        self.assertEqual(self.shim.scroll(-50.2, -100.5), "scroll(delta_x=-50.2, delta_y=-100.5)")

    def test_fill(self):
        self.assertEqual(self.shim.fill('237', 'example value'), "fill(bid='237', value='example value')")
        self.assertEqual(self.shim.fill('45', 'multi-line\nexample'), "fill(bid='45', value='multi-line\nexample')")
        self.assertEqual(self.shim.fill('a12', 'example with "quotes"'), "fill(bid='a12', value='example with \"quotes\"')")

    def test_select_option(self):
        self.assertEqual(self.shim.select_option('48', 'blue'), "select_option(bid='48', options='blue')")
        self.assertEqual(self.shim.select_option('48', ['red', 'green', 'blue']), "select_option(bid='48', options=['red', 'green', 'blue'])")

    def test_click(self):
       self.assertEqual(self.shim.click('48'), "click(bid='48')")
       self.assertEqual(self.shim.click('48', button='middle', modifiers=['Shift']), "click(bid='48', button='middle', modifiers=['Shift'])")

    def test_dblclick(self):
        self.assertEqual(self.shim.dblclick('12'), "dblclick(bid='12')")
        self.assertEqual(self.shim.dblclick('ca42', button='right'), "dblclick(bid='ca42', button='right')")
        self.assertEqual(self.shim.dblclick('178', button='middle', modifiers=['Shift']), "dblclick(bid='178', button='middle', modifiers=['Shift'])")

    def test_hover(self):
        self.assertEqual(self.shim.hover('b8'), "hover(bid='b8')")

    def test_press(self):
        self.assertEqual(self.shim.press('88', 'Backspace'), "press(bid='88', key_comb='Backspace')")
        self.assertEqual(self.shim.press('a26', 'Control+a'), "press(bid='a26', key_comb='Control+a')")
        self.assertEqual(self.shim.press('a61', 'Meta+Shift+t'), "press(bid='a61', key_comb='Meta+Shift+t')")

    def test_focus(self):
        self.assertEqual(self.shim.focus('b455'), "focus(bid='b455')")

    def test_clear(self):
        self.assertEqual(self.shim.clear('996'), "clear(bid='996')")

    def test_drag_and_drop(self):
        self.assertEqual(self.shim.drag_and_drop('56', '498'), "drag_and_drop(from_bid='56', to_bid='498')")

    def test_upload_file(self):
        self.assertEqual(self.shim.upload_file('572', 'my_receipt.pdf'), "upload_file(bid='572', file='my_receipt.pdf')")
        self.assertEqual(
            self.shim.upload_file('63', ['/home/<USER>/Documents/image.jpg', '/home/<USER>/Documents/file.zip']),
            "upload_file(bid='63', file=['/home/<USER>/Documents/image.jpg', '/home/<USER>/Documents/file.zip'])"
        )

    def test_go_back(self):
        self.assertEqual(self.shim.go_back(), "go_back()")

    def test_go_forward(self):
        self.assertEqual(self.shim.go_forward(), "go_forward()")

    def test_goto(self):
        self.assertEqual(self.shim.goto('http://www.example.com'), "goto(url='http://www.example.com')")

if __name__ == '__main__':
    unittest.main()