import unittest
from typing import List

from code_generation_core_agent.agents.framework.memories import Memories, ExactMemory, FuzzyMemory, MemoryException


class TestMemories(unittest.TestCase):
    def setUp(self):
        self.memories = Memories(fade_rate=0.05, drop_threshold=0.001)

    def test_exact_memory_save_and_retrieve(self):
        self.memories.save_memory("key1", "This is a test memory")
        result = self.memories.get_memory("key1")
        self.assertEqual(result[0].content, "This is a test memory")
        self.assertIsInstance(self.memories.exact_memories["key1"][0], ExactMemory)

    def test_fuzzy_memory_save_and_retrieve(self):
        self.memories.save_memory("The quick brown fox", "Fuzzy memory 1", is_fuzzy=True)
        self.memories.save_memory("A lazy dog", "Fuzzy memory 2", is_fuzzy=True)

        result = self.memories.get_memory("quick fox", is_fuzzy=True)
        self.assertEqual(result[0].content, "Fuzzy memory 1")

        for memories in self.memories.fuzzy_memories.values():
            for memory in memories:
                self.assertIsInstance(memory, FuzzyMemory)

    def test_exact_memory_not_found(self):
        self.assertEqual(self.memories.get_memory("non_existent_key", is_fuzzy=False), [])

    def test_fuzzy_memory_not_found(self):
        self.assertEqual( self.memories.get_memory("non_existent_key", is_fuzzy=True), [])


    def test_memory_drop(self):
        self.memories = Memories(fade_rate=0.5, drop_threshold=0.2)
        self.memories.save_memory("drop_test", "Dropping memory")

        # Save more memories to trigger fading
        for i in range(10):
            self.memories.save_memory("drop_test", f"Memory {i}")

        all_memories = self.memories.exact_memories["drop_test"]

        # Check that "Dropping memory" is no longer in the memory
        for memory in all_memories:
            self.assertNotEqual(memory.content, "Dropping memory")

    def test_vectorizer_fitting(self):
        self.memories.save_memory("test1", "Fuzzy memory 1", is_fuzzy=True)
        self.assertFalse(self.memories.vectorizer_fitted)

        self.memories.save_memory("test2", "Fuzzy memory 2", is_fuzzy=True)
        self.assertFalse(self.memories.vectorizer_fitted)

        _ = self.memories.get_memory("test query", is_fuzzy=True)
        self.assertTrue(self.memories.vectorizer_fitted)

    def test_fuzzy_memory_similarity_threshold(self):
        # Add some fuzzy memories
        self.memories.save_memory("red apple", "An apple is red", is_fuzzy=True)
        self.memories.save_memory("green banana", "A banana is green", is_fuzzy=True)

        result = self.memories.get_memory("purple grape", is_fuzzy=True)
        self.assertEqual(len(result), 0, "Expected no results for a query with low similarity")
        result = self.memories.get_memory("yellow apple", is_fuzzy=True)
        self.assertEqual(len(result), 1, "Expected one result")

    def test_vectorizer_refit_on_new_fuzzy_memory(self):
        # Add initial fuzzy memories
        self.memories.save_memory("apple", "An apple a day keeps the doctor away", is_fuzzy=True)
        self.memories.save_memory("banana", "Bananas are rich in potassium", is_fuzzy=True)

        # Perform a fuzzy lookup to fit the vectorizer
        self.memories.get_memory("fruit", is_fuzzy=True)

        # Store the current state of the vectorizer
        initial_vocabulary = self.memories.vectorizer.vocabulary_.copy()

        # Add a new fuzzy memory with new words
        self.memories.save_memory("grape", "Grapes are used to make wine", is_fuzzy=True)

        # Perform another fuzzy lookup to trigger vectorizer refitting
        self.memories.get_memory("fruit", is_fuzzy=True)

        # Check if the vectorizer's vocabulary has been updated
        self.assertNotEqual(initial_vocabulary, self.memories.vectorizer.vocabulary_,
                            "Vectorizer should be refitted when new fuzzy memories are added")


if __name__ == '__main__':
    unittest.main()