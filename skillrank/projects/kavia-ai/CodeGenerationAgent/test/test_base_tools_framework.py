"""
Test the AgentTool and BaseTool classes.
"""

import unittest
from code_generation_core_agent.agents.tools.agent_tool import AgentTool
TEST_SCHEMA = [
            {
                "type": "function",
                "function": {
                    "name": "test_schema_initialize_browser",
                    "description": "Initialize a headless Chrome browser.",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            }
]

class TestAgentTool(unittest.TestCase):
    def setUp(self):
        class MockLogger:
            def info(self, message):
                pass
            def error(self, message):
                pass
            def warning(self, message):
                pass

        self.mock_logger = MockLogger()
        self.base_path = "/mock/base/path"
        self.agent_tool = AgentTool(self.base_path, self.mock_logger)

    def test_prevent_override_add_function_schemas(self):
        """
        Test that add_function_schemas cannot be overridden.

        Ensures that:
        1. An AttributeError is raised when attempting to override add_function_schemas.
        """
        with self.assertRaises(AttributeError):
            class AttemptOverride(AgentTool):
                def add_function_schemas(self, schemas, tool_name):
                    pass  # This should raise an AttributeError

    def test_add_function_schemas_single_dict(self):
        """
        Test that add_function_schemas can add a single schema dictionary.

        Ensures that:
        1. The schema is added to the function schemas by tool dictionary.
        """
        self.agent_tool.add_function_schemas(TEST_SCHEMA, "test_tool")
        self.assertIn("test_tool", self.agent_tool._function_schemas_by_tool)



if __name__ == '__main__':
    unittest.main()