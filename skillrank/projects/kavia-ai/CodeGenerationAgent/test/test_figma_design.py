import json
import os
import string
import time
import unittest
import random
from pathlib import Path

from . import agent_cli
from .data.test_cases import INTERACTIVE_WORK_ITEMS

import logging
logging.disable(logging.NOTSET)

class TestCodeGenerator(unittest.TestCase):
    test_data_path = Path("test/dnotes/data")

    @staticmethod
    def wait_for_some_code_generated(dir_path):
        # Traverse the directory and check if there are any .py, .js, .html or .c, .cpp files
        # If there are, return True, else return False
        for root, dirs, files in os.walk(dir_path):
            for file in files:
                if (file.endswith('.py') or file.endswith('.js')
                        or file.endswith('.html') or file.endswith('.c') or file.endswith('.cpp')):
                    return True
        print("No code generated yet... Checking again in a minute.")
        return False

    def load_json_file(self, filename: str) -> dict:
        file_path = self.test_data_path / filename
        with open(file_path, 'r') as f:
            return json.load(f)


    def test_figma_design(self):
        """
        Create a code generation agent and start it.
        Wait for some code to be generated.
        """
        random.seed(1)
        session_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        job = self.load_json_file("figma_user_auth.json")["work_items"]
        agent = agent_cli.ShellCGExecutor(job,  [], session_id=session_id)

        agent.start()

        for _ in range(10):
            if TestCodeGenerator.wait_for_some_code_generated(agent.get_session_dir()):
                break
            time.sleep(1500)

        agent.exit()

        self.assertTrue(TestCodeGenerator.wait_for_some_code_generated(agent.get_session_dir()))
