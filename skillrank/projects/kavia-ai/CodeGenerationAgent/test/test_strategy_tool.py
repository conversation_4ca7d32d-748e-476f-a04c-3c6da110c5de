import os

import pytest
from unittest.mock import Mock
from typing import Dict, Any

from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.strategic_analysis_tool import (
    StrategicAnalysisTool,
    StrategicAnalysisError
)

@pytest.fixture(autouse=True)
def setup_test_files():
    # Create all the test files mentioned in the fixtures
    test_files = [
        "/tmp/test_file.py",
        "/tmp/test_auth.py",
        "/tmp/test_jwt.py",
        "/tmp/legacy_auth.py",
        "/tmp/new_auth.py"
    ]

    for file in test_files:
        with open(file, 'w') as f:
            f.write("# placeholder test file")

    # Cleanup after tests
    yield

    for file in test_files:
        try:
            os.remove(file)
        except OSError:
            pass

@pytest.fixture
def mock_logger():
    return Mock(
        info=Mock(),
        error=Mock(),
        warning=Mock()
    )


@pytest.fixture
def mock_callbacks():
    return {
        "agent_message_callback": <PERSON><PERSON>(),
        "knowledge_lookup": <PERSON><PERSON>(return_value=["Test knowledge"]),
        "terminal_output_callback": <PERSON><PERSON>(),
        "codeview_callback": <PERSON><PERSON>()
    }

docker_executor = DockerExecutor()

@pytest.fixture
def tool(mock_callbacks, mock_logger):
    return StrategicAnalysisTool(
        callback_functions=mock_callbacks,
        base_path="/tmp/",
        logger=mock_logger,
        executor= docker_executor
    )


@pytest.fixture
def python_code_context():
    return {
        "file_paths": ["test_file.py"],
        "current_state": "Need to implement a new REST API endpoint for user authentication",
        "knowledge_keywords": ["authentication", "REST", "FastAPI"],
        "agent_type": "CodeWritingAgent",
        "goal": "Implement a secure login endpoint using FastAPI and JWT tokens"
    }


@pytest.fixture
def test_writing_context():
    return {
        "file_paths": ["test_auth.py", "test_jwt.py"],
        "current_state": "Need to write comprehensive tests for the new authentication endpoint",
        "knowledge_keywords": ["pytest", "authentication-testing", "JWT-testing"],
        "agent_type": "TestWritingAgent",
        "goal": "Create unit and integration tests for the login endpoint"
    }


@pytest.fixture
def refactoring_context():
    return {
        "file_paths": ["legacy_auth.py", "new_auth.py"],
        "current_state": "Need to refactor the legacy authentication system to use modern practices",
        "knowledge_keywords": ["refactoring", "authentication", "modern-practices"],
        "agent_type": "RefactoringAgent",
        "goal": "Modernize the authentication system while maintaining compatibility"
    }


class TestStrategicAnalysisTool:

    def test_initialization(self, tool, mock_callbacks, mock_logger):
        """Test proper initialization of StrategicAnalysisTool."""
        assert tool.get_tool_name() == "StrategicAnalysisTool"
        assert tool.base_path == "/tmp/"
        assert tool.logger == mock_logger
        assert tool.agent_message_callback == mock_callbacks["agent_message_callback"]
        assert tool.knowledge_lookup == mock_callbacks["knowledge_lookup"]

    @pytest.mark.asyncio
    async def test_analyze_context_code_writing(self, tool, python_code_context):
        """Test strategic analysis for code writing scenario with actual LLM."""
        result = await tool.analyze_context(**python_code_context)
        print("result", result)
        assert result["status"] == "SUCCESS"
        assert isinstance(result["refined_understanding"], str)
        assert isinstance(result["strategic_approach"], str)
        assert isinstance(result["action_steps"], list)
        assert len(result["action_steps"]) > 0
        assert isinstance(result["challenges_and_mitigations"], list)

        # Verify the analysis is relevant to authentication and FastAPI
        assert any("implement" in str(step).lower() for step in result["action_steps"])


    @pytest.mark.asyncio
    async def test_analyze_context_refactoring(self, tool, refactoring_context):
        """Test strategic analysis for refactoring scenario with actual LLM."""
        result = tool.analyze_context(**refactoring_context)

        assert result["status"] == "SUCCESS"
        assert isinstance(result["refined_understanding"], str)
        assert isinstance(result["strategic_approach"], str)
        assert isinstance(result["action_steps"], list)
        assert len(result["action_steps"]) > 0

        assert any("refactor" in str(step).lower() for step in result["action_steps"])
        assert "challenges_and_mitigations" in result

    @pytest.mark.asyncio
    async def test_analyze_context_no_goal(self, tool, python_code_context):
        """Test analysis with no goal provided."""
        invalid_context = {**python_code_context, "goal": ""}

        result = tool.analyze_context(**invalid_context)

        assert result["status"] == "ERROR"
        assert "A clear goal must be specified" in result["message"]

    @pytest.mark.asyncio
    async def test_analyze_context_many_files_warning(self, tool, python_code_context):
        """Test warning generation for many files."""
        many_files_context = {
            **python_code_context,
            "file_paths": [f"file{i}.py" for i in range(15)]
        }

        result =  tool.analyze_context(**many_files_context)
        print("-------------", result)
        assert result["status"] == "SUCCESS"
        assert "warnings" in result
        assert "Large number of files" in str(result["warnings"])

    @pytest.mark.asyncio
    async def test_complex_scenario(self, tool):
        """Test a complex scenario combining multiple aspects."""
        complex_context = {
            "file_paths": ["auth.py", "models.py", "views.py"],
            "current_state": "Need to implement OAuth2 authentication with "
                                     "multiple providers while maintaining existing JWT system",
            "knowledge_keywords": ["OAuth2", "JWT", "authentication", "security"],
            "agent_type": "CodeWritingAgent",
            "goal": "Implement OAuth2 authentication with Google and GitHub "
                    "providers while keeping JWT as fallback"
        }

        result =  tool.analyze_context(**complex_context)
        assert result["status"] == "SUCCESS"
        assert isinstance(result["refined_understanding"], str)
        assert isinstance(result["strategic_approach"], str)
        assert isinstance(result["action_steps"], list )
        assert len(result["action_steps"]) >= 3

        # Verify the analysis covers all aspects
        all_steps = str((result["action_steps"])).lower()
        assert "oauth" in all_steps
        assert "jwt" in all_steps

        # Verify challenges are identified
        assert "challenges_and_mitigations" in result
        challenges = result["challenges_and_mitigations"]
        assert isinstance(challenges,  list)
        assert len(challenges) > 0