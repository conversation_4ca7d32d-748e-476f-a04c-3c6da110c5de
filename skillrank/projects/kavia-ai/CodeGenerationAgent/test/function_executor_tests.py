import logging
import unittest
from unittest.mock import Magic<PERSON><PERSON>, patch
import json

from code_generation_core_agent.agents.tools.agent_tool import Agent<PERSON><PERSON>
from code_generation_core_agent.agents.tools.base_tool import BaseTool, ToolRegistry, create_combined_tool
from code_generation_core_agent.agents.tools.container_file_tools import ContainerFileTools
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from code_generation_core_agent.agents.tools.visible_shell_tools import VisibleShellTools
from code_generation_core_agent.agents.tools.web_browser import WebBrowser
from code_generation_core_agent.agents.tools.review_status_tools import ReviewStatusTools


class DummyTool(AgentTool):

    def __init__(self, base_path, logger=None, llm=None, executor=None):
        super().__init__(base_path, logger, llm, executor)

    @register_function(
            description='Dummy test function', 
            parameters={ "properties": {
                "arg1": {"type": "string", "description": "arg1"},
                "arg2": {"type": "string", "description": "arg2"}
                },
            }, 
            validator=None)
    def dummy_test_function(self, arg1, arg2):
        return {"status": "SUCCESS"}

class TestBaseTool(unittest.TestCase):
    def setUp(self):
        self.logger = logging.getLogger('TestLogger')
        self.logger.setLevel(logging.INFO)
        self.base_tool = BaseTool(base_path="/test/path", logger=self.logger)


    def test_function_executor_known_function(self):
        mock_function = MagicMock(return_value={"status": "SUCCESS"})
        self.base_tool.function_mapping = {"test_function": "mock_function"}
        setattr(self.base_tool, "mock_function", mock_function)

        result = self.base_tool.function_executor("test_function", {"arg1": "value1"})

        mock_function.assert_called_once_with(arg1="value1")
        self.assertEqual(result, {"status": "SUCCESS"})

    def test_function_executor_unknown_function(self):
        result = self.base_tool.function_executor("unknown_function", {})
        self.assertEqual(result, {
            "status": "ERROR",
            "message": "Unknown function: unknown_function",
            "error_type": "UnknownFunction",
            "function_name": "unknown_function"
        })

    def test_function_executor_invalid_arguments(self):
        mock_function = MagicMock(side_effect=TypeError("Invalid argument"))
        self.base_tool.function_mapping = {"test_function": "mock_function"}
        setattr(self.base_tool, "mock_function", mock_function)

        result = self.base_tool.function_executor("test_function", {"invalid_arg": "value"})
        self.assertEqual(result["status"], "ERROR")
        self.assertIn("Invalid argument passed to test_function", result["message"])

    def test_function_executor_schema_consistency(self):
        test_schema = {
            "function": {
                "name": "test_function",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "arg1": {"type": "string"}
                    },
                    "required": ["arg1"]
                }
            }
        }
        self.base_tool._function_schemas.append(test_schema)
        self.base_tool.function_mapping["test_function"] = "mock_function"
        mock_function = MagicMock(return_value={"status": "SUCCESS"})
        setattr(self.base_tool, "mock_function", mock_function)

        self.base_tool.function_executor("test_function", {"arg1": "value1"})
        mock_function.assert_called_once_with(arg1="value1")

    def test_add_function_schemas(self):
        test_schema = {
            "function": {
                "name": "test_function",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "arg1": {"type": "string"}
                    },
                    "required": ["arg1"]
                }
            }
        }
        self.base_tool.add_function_schemas([test_schema], "TestTool")

        # Check if the schema is in _function_schemas (accounting for the extra nesting)
        self.assertTrue(any([test_schema] == schema for schema in self.base_tool._function_schemas))

        self.assertIn("TestTool", self.base_tool._function_schemas_by_tool)
        self.assertIn(test_schema, self.base_tool._function_schemas_by_tool["TestTool"])    

    def test_add_function_schemas_duplicate(self):
        test_schema = {
            "function": {
                "name": "test_function",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "arg1": {"type": "string"}
                    },
                    "required": ["arg1"]
                }
            }
        }
        self.base_tool.add_function_schemas([test_schema], "TestTool")
        self.base_tool.add_function_schemas([test_schema], "TestTool")

        assert len(self.base_tool._function_schemas) == 1
        assert len(self.base_tool._function_schemas_by_tool["TestTool"]) == 1

    def test_add_function_schemas_no_class_variable_duplicates(self):
        schema = [{"function": {"name": "test_function"}}]
        base_tool1 = BaseTool(base_path="/test/path1")
        base_tool2 = BaseTool(base_path="/test/path2")
        
        base_tool1.add_function_schemas(schema, "test_tool")
        base_tool2.add_function_schemas(schema, "test_tool")
        
        assert len(base_tool1._function_schemas) == 1
        assert len(base_tool1._function_schemas_by_tool["test_tool"]) == 1
        assert len(base_tool2._function_schemas) == 1
        assert len(base_tool2._function_schemas_by_tool["test_tool"]) == 1

class TestDynamicTool(unittest.TestCase):
    def setUp(self):
        self.registry = ToolRegistry()
        self.executor = MagicMock()



    def test_all_registered_tools_have_declared_functions(self):
        for tool_name, tool_class in self.registry.tools.items():

            with self.subTest(tool=tool_name):
                print(f"Checking tool {tool_name}")
                if tool_name == "ReviewStatusTools":
                    tool_instance = tool_class("/test/path", MagicMock(), llm=MagicMock())
                elif tool_name == "GitTools":
                    tool_instance = tool_class("/test/path", MagicMock())
                else:
                    tool_instance = tool_class("/test/path", MagicMock(), executor=self.executor)

                schema_functions = set()

                for schema in tool_instance._function_schemas:
                    if isinstance(schema, list):
                        for s in schema:
                            schema_functions.add(s['function']['name'])
                    else:
                        schema_functions.add(schema['function']['name'])

                # Get functions from function_mapping
                mapped_functions = set(tool_instance.function_mapping.keys())

                self.assertTrue(schema_functions.issubset(mapped_functions),
                                f"Some functions in {tool_name} schemas are not in the function mapping:\n"
                                f"Missing: {schema_functions - mapped_functions}")

    def test_dynamic_tool_creation(self):

        tools_to_use = [
            VisibleShellTools,
            ReviewStatusTools,
            ContainerFileTools,
            WebBrowser,
            DummyTool]

        combined_tool_instance = create_combined_tool(tools_to_use, "/test/path", MagicMock(), executor=self.executor)

        # Check if we can call the test function
        result = combined_tool_instance.function_executor("dummy_test_function", {"arg1": "value1", "arg2": "value2"})
        self.assertEqual(result, {"status": "SUCCESS"})

    def test_function_schema_consistency(self):
        for tool_name, tool_class in self.registry.tools.items():
            with self.subTest(tool=tool_name):
                tool_instance = tool_class("/test/path", MagicMock(), executor=self.executor)

                for func_name in tool_instance.function_mapping:
                    matching_schemas = [schema for schema in tool_instance._function_schemas
                                        if schema['function']['name'] == func_name]
                    self.assertTrue(matching_schemas, f"No schema found for function {func_name} in {tool_name}")


if __name__ == '__main__':
    unittest.main()