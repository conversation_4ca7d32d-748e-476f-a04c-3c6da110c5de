import threading

import pytest
import os
import time
import logging
import sys
from pathlib import Path
import requests

from code_generation_core_agent.agents.preview.preview_manager import PreviewManager, PreviewState
from code_generation_core_agent.agents.tools.executor.docker_executor import DockerExecutor
from code_generation_core_agent.agents.tools.init import universal_project_setup
from code_generation_core_agent.project_schemas import Container, ContainerType, ProjectSchema, sync_to_all_workspaces
from code_generation_core_agent.agents.utilities import ApplicationType

@pytest.fixture(scope="module")
def docker_executor():
    """Fixture to provide a Docker executor for testing"""
    executor = DockerExecutor()
    yield executor
    del executor


@pytest.fixture
def mock_logger():
    """Fixture to provide a logger that prints to stdout"""
    logger = logging.getLogger('test_preview_manager')
    logger.setLevel(logging.DEBUG)
    logger.handlers.clear()

    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - [TID:%(thread)d] - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    return logger


@pytest.fixture
def temp_project_dir():
    """Create a temporary project directory with a simple Python HTTP server"""
    temp_dir = "/tmp/test_preview_manager_integration"
    os.makedirs(temp_dir, exist_ok=True)
    # Create a simple index.html file
    with open(os.path.join(temp_dir, 'index.html'), 'w') as f:
        f.write("""
<!DOCTYPE html>
<html>
<head>
    <title>Test Server</title>
</head>
<body>
    <h1>Hello from Test Server!</h1>
    <p>This is a test HTTP server running in Docker.</p>
</body>
</html>
""")

    yield temp_dir

    # Cleanup
    import shutil
    try:
        shutil.rmtree(temp_dir, ignore_errors=True)
    except Exception as e:
        print(f"Warning: Could not clean up temp directory {temp_dir}: {e}")


@pytest.fixture
def preview_manager(docker_executor, mock_logger, temp_project_dir):
    """Create a PreviewManager instance"""

    PreviewManager.reset_for_testing()

    manager = PreviewManager(executor=docker_executor)
    manager.set_logger(mock_logger)
    manager.set_base_path(Path(temp_project_dir))
    manager.set_executor(docker_executor)
    manager.containers.clear()  # Clear containers but keep same instance

    yield manager

    manager.shutdown()
    PreviewManager.reset_for_testing()


class TestPreviewManagerIntegration:
    """Integration tests for PreviewManager with real Docker containers"""

    def test_container_dependency_chain(self, preview_manager, temp_project_dir):
        """Test starting containers with dependencies"""

        # Setup universal project setup for both containers
        from code_generation_core_agent.agents.tools.init import universal_project_setup

        # Setup database container
        db_work_item = {
            "component_name": "test_database1",
            "container_name": "test_database1",
            "framework": "sqlite",
            "language": "sql",
            "platform": "database"
        }

        db_result = universal_project_setup.setup_project(
            db_work_item,
            os.path.join(temp_project_dir, "test_database1"),
            preview_manager.executor,
            preview_manager.logger,
            framework="sqlite",
            platform="database"
        )

        # Setup backend container
        backend_work_item = {
            "component_name": "test_backend1",
            "container_name": "test_backend1",
            "framework": "fastapi",
            "language": "python",
            "platform": "backend"
        }

        backend_result = universal_project_setup.setup_project(
            backend_work_item,
            os.path.join(temp_project_dir, "test_backend1"),
            preview_manager.executor,
            preview_manager.logger,
            framework="fastapi",
            platform="backend"
        )

        assert db_result and backend_result, "Universal project setup should succeed for both containers"

        # Create a mock database container
        db_container = Container(
            container_name="test_database1",
            description="Mock database for testing dependencies",
            interfaces="Database connection on port 5432",
            container_type=ContainerType.DATABASE,
            framework="sqlite",
            port="5432",
            workspace="test_database1",
            container_root="test_database",
        )

        # Create a backend container that depends on the database
        backend_container = Container(
            container_name="test_backend1",
            description="Backend API that depends on database",
            interfaces="REST API on port 8081",
            container_type=ContainerType.BACKEND,
            framework="fastapi",
            port="8081",
            workspace="test_backend1",
            container_root="test_backend1",
            dependent_containers=["test_database1"]
        )

        # Register containers
        preview_manager.register_container(db_container, ApplicationType.DATABASE.value)
        preview_manager.register_container(backend_container, ApplicationType.BACKEND.value)

        # Start the backend (should automatically start database first)
        result = preview_manager.run_container("test_backend1", run_dependencies=True)

        assert result["status"] == PreviewState.BUILDING.value

        # Wait for both containers to start
        max_wait = 45
        wait_time = 0

        db_container_status = None
        backend_container_status = None

        while wait_time < max_wait:
            db_container_status = preview_manager.get_container_by_name("test_database1")
            backend_container_status = preview_manager.get_container_by_name("test_backend1")

            if (db_container_status and db_container_status.status == PreviewState.RUNNING.value and
                backend_container_status and backend_container_status.status == PreviewState.RUNNING.value):
                break

            time.sleep(1)
            wait_time += 1

        assert db_container_status is not None, "Database container should exist"
        assert backend_container_status is not None, "Backend container should exist"

        assert db_container_status.status == PreviewState.RUNNING.value, "Database container should be running"
        assert backend_container_status.status == PreviewState.RUNNING.value, "Backend container should be running"

    def test_container_dependency_race_condition(self, preview_manager, temp_project_dir):
        """Test starting containers with dependencies"""

        # Setup universal project setup for both containers
        from code_generation_core_agent.agents.tools.init import universal_project_setup

        # Setup database container
        db_work_item = {
            "component_name": "test_database",
            "container_name": "test_database",
            "framework": "sqlite",
            "language": "sql",
            "platform": "database"
        }

        db_result = universal_project_setup.setup_project(
            db_work_item,
            os.path.join(temp_project_dir, "test_database"),
            preview_manager.executor,
            preview_manager.logger,
            framework="sqlite",
            platform="database"
        )

        # Setup backend container
        backend_work_item = {
            "component_name": "test_backend",
            "container_name": "test_backend",
            "framework": "fastapi",
            "language": "python",
            "platform": "backend"
        }

        backend_result = universal_project_setup.setup_project(
            backend_work_item,
            os.path.join(temp_project_dir, "test_backend"),
            preview_manager.executor,
            preview_manager.logger,
            framework="fastapi",
            platform="backend"
        )

        assert db_result and backend_result, "Universal project setup should succeed for both containers"

        # Create a mock database container
        db_container = Container(
            container_name="test_database",
            description="Mock database for testing dependencies",
            interfaces="Database connection on port 5432",
            container_type=ContainerType.DATABASE,
            framework="postgresql",
            port=5432,
            workspace="test_database",
            container_root="test_database",
        )

        # Create a backend container that depends on the database
        backend_container = Container(
            container_name="test_backend",
            description="Backend API that depends on database",
            interfaces="REST API on port 8081",
            container_type=ContainerType.BACKEND,
            framework="fastapi",
            port=8081,
            workspace="test_backend",
            container_root="test_backend",
            dependent_containers=["test_database"]
        )

        # Register containers
        preview_manager.register_container(db_container, ApplicationType.DATABASE.value)
        preview_manager.register_container(backend_container, ApplicationType.BACKEND.value)

        print("ZK Registered containers:", preview_manager)

        def start_backend():
            preview_manager.run_container("test_backend", run_dependencies=True)

        t1 = threading.Thread(target=start_backend)
        t2 = threading.Thread(target=start_backend)

        t1.start()
        t2.start()
        t1.join()
        t2.join()

        # Wait for both containers to start
        max_wait = 45
        wait_time = 0

        db_container_status = None
        backend_container_status = None

        while wait_time < max_wait:
            db_container_status = preview_manager.get_container_by_name("test_database")
            backend_container_status = preview_manager.get_container_by_name("test_backend")

            if (db_container_status and db_container_status.status == PreviewState.RUNNING.value and
                backend_container_status and backend_container_status.status == PreviewState.RUNNING.value):
                break

            time.sleep(1)
            wait_time += 1

        assert db_container_status is not None, "Database container should exist"
        assert backend_container_status is not None, "Backend container should exist"

        assert db_container_status.status == PreviewState.RUNNING.value, "Database container should be running"
        assert backend_container_status.status == PreviewState.RUNNING.value, "Backend container should be running"


    def test_env_file_flask_simple(self, preview_manager, temp_project_dir):
        """
        This test covers multiple environment variable set/load scenarios. The flow is the following:

        1.) We set an environemnt variable directly on the ProjectSchema container
        2.) When we call save_to_manifest, it should create a .env file with the Flask-specific variables
        3.) We load the project back from the manifest using load_from_file and verify that we can load
            load the environment variables correctly. load_from_file is typically used when we are resuming
            a project and the webui may set a .env for us. So we make sure that we take all the vars .
        4.) We then manually add an additional environment variable to the .env file
            This corresponds to a scenario when the user adds something to .env file manually.
        5.) We then call sync_to_all_workspaces to ensure that the environment variables can be synced back.
           Once the sync is done, the preview manager can get the env variables from the container's env
        """
        project_name = "test_flask"
        container_name = "test_flask_env"
        # Setup Flask project (faster than React - no npm install)
        work_item = {
            "component_name": project_name,
            "framework": "flask",
            "language": "python",
            "platform": "backend"
        }

        project_dir = os.path.join(temp_project_dir, container_name)

        result = universal_project_setup.setup_project(
            work_item,
            project_dir,
            preview_manager.executor,
            preview_manager.logger,
            framework="flask",
            platform="backend"
        )

        assert result, "Flask project setup should succeed"

        # Create container
        container = Container(
            container_name=container_name,
            description="Flask app for env testing",
            interfaces="REST API",
            container_type=ContainerType.BACKEND,
            framework="flask",
            container_root=container_name,
            workspace=container_name,
            port=3002,
        )
        # Store the env in the container.env
        container.env = {
            'FLASK_APP': 'app.py',
            'FLASK_ENV': 'development',
            'SECRET_KEY': 'dev-secret-key-123',
            'DATABASE_URL': 'sqlite:///app.db',
            'DEBUG': 'True',
        }


        project = ProjectSchema()
        project.add_container(container)

        # Create .env file with Flask-specific vars
        env_file_path = os.path.join(project_dir, ".env")
        # os.makedirs(os.path.dirname(env_file_path), exist_ok=True)

        # Test sync and load cycle
        manifest_path = os.path.join(temp_project_dir, "flask_manifest.yaml")
        project.save_to_manifest(manifest_path)

        # Load back
        loaded_project = ProjectSchema.load_from_file(manifest_path)
        loaded_container = loaded_project.get_container_by_name("test_flask_env")

        # Verify Flask env vars loaded correctly
        expected_vars = {
            'FLASK_APP': 'app.py',
            'FLASK_ENV': 'development',
            'SECRET_KEY': 'dev-secret-key-123',
            'DATABASE_URL': 'sqlite:///app.db',
            'DEBUG': 'True'
        }

        for key, expected_value in expected_vars.items():
            assert key in loaded_container.env.keys(), f"Flask env var {key} should be loaded"
            assert loaded_container.env[key] == expected_value, f"Flask env var {key} should match expected value"


        # Now add env variable manually to env_file_path and check if we can sync it back

        f = open(env_file_path, "a")
        f.write("\nADDITIONAL_VAR=additional_value\n")
        f.close()

        # Load the project again to ensure env file is read
        sync_to_all_workspaces(loaded_project, temp_project_dir)

        # Check if the additional variable is present
        loaded_container = loaded_project.get_container_by_name("test_flask_env")
        assert "ADDITIONAL_VAR" in loaded_container.env, "ADDITIONAL_VAR should be synced from .env file"

