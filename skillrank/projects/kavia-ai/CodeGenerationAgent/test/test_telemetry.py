import unittest
import tempfile
import os
import time

from code_generation_core_agent.telemetry.datadog_logger import DatadogLogger
from code_generation_core_agent.config import config
from code_generation_core_agent.telemetry.logger_factory import LoggerFactory


class TestDatadogLogger(unittest.TestCase):

    def setUp(self):
        self.logger = LoggerFactory.get_logger()

    def test_upload_log(self):
        # Create a temporary log file
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
            temp_file.write("Test log content for integration test")
            temp_file_path = temp_file.name

        try:
            # Call the method
            self.logger.upload_log(temp_file_path, "integration_test")

            time.sleep(5)

            print("Log upload completed without errors. Please check Datadog dashboard to confirm.")
        finally:
            os.unlink(temp_file_path)

    def test_report_crash(self):
        error_message = "Test error for integration test"
        stack_trace = "Fake stack trace\nLine 2\nLine 3"

        self.logger.report_crash(error_message, stack_trace)

        time.sleep(5)

        print("Crash report completed without errors. Please check Datadog dashboard to confirm.")

    def test_get_session_id(self):
        session_id = self.logger.get_session_id()
        # Check session id is an uuid
        self.assertEqual(36, len(session_id))
        session_id_from_singleton = LoggerFactory.get_logger_session_id()
        self.assertEqual(session_id, session_id_from_singleton)

if __name__ == '__main__':
    unittest.main()
