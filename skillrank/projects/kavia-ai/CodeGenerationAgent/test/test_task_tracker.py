import pytest
import json
import os
from typing import Dict

from code_generation_core_agent.agents.framework.task_tracker import TaskTracker


class TestTaskTracker:
    """
    Task tracker is a note-taking system for the orchestrator.
    Independently of what is in the task list, the orchestrator can schedule steps and tasks,
    but it is required that it keeps track of the tasks that are being executed.
    """
    @pytest.fixture
    def temp_save_file(self, tmp_path):
        """Fixture to create a temporary save file path."""
        return str(tmp_path / "test_tasks.json")

    @pytest.fixture
    def tracker(self, temp_save_file):
        """Fixture to create a TaskTracker instance."""
        return TaskTracker(
            callback_functions=None,
            base_path="",
            logger=None,
            save_location=temp_save_file,
            task_description="Test Task Description"
        )

    @pytest.fixture
    def sample_task_data(self) -> Dict:
        """Fixture providing sample task data."""
        return {
            "description": "Sample task",
            "task_description": "Test task description",
            "details": "Important task details",
            "steps": [
                {
                    "description": "Step 1: Initialize project",
                    "micro_agent": "SetupAgent",
                    "details": "Set up project structure"
                },
                {
                    "description": "Step 2: Implement core features",
                    "micro_agent": "CoreAgent",
                    "details": "Implement main functionality"
                }
            ]
        }

    def test_initialization(self, temp_save_file):
        """Test TaskTracker initialization and file creation."""
        tracker = TaskTracker(
            callback_functions=None,
            base_path="",
            logger=None,
            save_location=temp_save_file,
            task_description="Test Description"
        )

        assert os.path.exists(temp_save_file)
        with open(temp_save_file, 'r') as f:
            data = json.load(f)
            assert data['task_description'] == "Test Description"
            assert isinstance(data['tasks'], dict)
            assert data['next_task_id'] == 1

    def test_add_task(self, tracker, sample_task_data):
        """Test adding a task with subtasks."""
        result = tracker.add_task(**sample_task_data)

        assert result['status'] == "SUCCESS"
        assert result['task_id'] == "Task1"
        assert len(result['subtask_ids']) == 2
        assert all(id.startswith("Task1SubTask") for id in result['subtask_ids'])

        # Verify task data
        task = tracker.data['tasks']["Task1"]
        assert task["description"] == sample_task_data["description"]
        assert task["task_description"] == sample_task_data["task_description"]
        assert task["details"] == sample_task_data["details"]
        assert task["status"] == "to-do"
        assert len(task["subtasks"]) == 2

    def test_get_tasks(self, tracker, sample_task_data):
        """Test retrieving tasks."""
        # Add a task first
        tracker.add_task(**sample_task_data)
        result = tracker.get_tasks()

        assert result['status'] == "SUCCESS"
        steps_list = result['steps_list']
        assert len(steps_list) == 3  # Main task + 2 subtasks

        # Verify main task
        main_task = next(step for step in steps_list if 'task_id' in step and 'subtask_id' not in step)
        assert main_task['description'] == sample_task_data["description"]
        assert main_task['task_description'] == sample_task_data["task_description"]
        assert main_task['status'] == "to-do"
        assert main_task['details'] == sample_task_data["details"]

        # Verify subtasks
        subtasks = [step for step in steps_list if 'subtask_id' in step]
        assert len(subtasks) == 2
        for subtask, original in zip(subtasks, sample_task_data["steps"]):
            assert subtask['description'] == original["description"]
            assert subtask['micro_agent'] == original["micro_agent"]
            assert subtask['details'] == original["details"]

    def test_update_task(self, tracker, sample_task_data):
        """Test updating task fields."""
        result = tracker.add_task(**sample_task_data)
        task_id = result['task_id']

        updates = {
            "status": "in-progress",
            "description": "Updated description",
            "task_description": "Updated task description",
            "details": "Updated details"
        }

        update_result = tracker.update(task_id=task_id, updates=updates)
        assert update_result['status'] == "SUCCESS"

        task = tracker.data['tasks'][task_id]
        for field, value in updates.items():
            assert task[field] == value

    def test_update_subtask(self, tracker, sample_task_data):
        """Test updating subtask fields."""
        result = tracker.add_task(**sample_task_data)
        task_id = result['task_id']
        subtask_id = result['subtask_ids'][0]

        updates = {
            "status": "completed",
            "description": "Updated subtask",
            "details": "Updated details",
            "micro_agent": "UpdatedAgent"
        }

        update_result = tracker.update(
            task_id=task_id,
            subtask_id=subtask_id,
            updates=updates
        )

        assert update_result['status'] == "SUCCESS"
        subtask = tracker.data['tasks'][task_id]["subtasks"][subtask_id]
        for field, value in updates.items():
            assert subtask[field] == value

    def test_add_subtask(self, tracker, sample_task_data):
        """Test adding a new subtask to existing task."""
        result = tracker.add_task(**sample_task_data)
        task_id = result['task_id']

        new_subtask = {
            "description": "Additional subtask",
            "micro_agent": "NewAgent",
            "details": "New subtask details"
        }

        subtask_result = tracker.add_subtask(
            task_id=task_id,
            **new_subtask
        )

        assert subtask_result['status'] == "SUCCESS"
        assert subtask_result['subtask_id'] == f"{task_id}SubTask3"

        # Verify subtask data
        subtask = tracker.data['tasks'][task_id]["subtasks"][subtask_result['subtask_id']]
        assert subtask["description"] == new_subtask["description"]
        assert subtask["micro_agent"] == new_subtask["micro_agent"]
        assert subtask["details"] == new_subtask["details"]
        assert subtask["status"] == "to-do"

    def test_remove_all_tasks(self, tracker, sample_task_data):
        """Test removing all tasks."""
        # Add some tasks first
        tracker.add_task(**sample_task_data)
        tracker.add_task(**sample_task_data)

        result = tracker.remove_all_tasks()
        assert result['status'] == "SUCCESS"
        assert result['tasks_removed'] == 2
        assert len(tracker.data['tasks']) == 0
        assert tracker.data['next_task_id'] == 1

    def test_invalid_status_update(self, tracker, sample_task_data):
        """Test updating with invalid status."""
        result = tracker.add_task(**sample_task_data)
        update_result = tracker.update(
            task_id=result['task_id'],
            updates={"status": "invalid-status"}
        )

        assert update_result['status'] == "ERROR"
        assert "Invalid status" in update_result['message']

    def test_update_nonexistent_task(self, tracker):
        """Test updating non-existent task."""
        result = tracker.update(
            task_id="NonexistentTask",
            updates={"status": "in-progress"}
        )

        assert result['status'] == "ERROR"
        assert "not found" in result['message']

    def test_task_persistence(self, temp_save_file, sample_task_data):
        """Test that task data persists between instances."""
        # Create first tracker and add task
        tracker1 = TaskTracker(
            callback_functions=None,
            base_path="",
            logger=None,
            save_location=temp_save_file,
            task_description="Test Description"
        )
        result = tracker1.add_task(**sample_task_data)
        task_id = result['task_id']

        # Create second tracker and verify data
        tracker2 = TaskTracker(
            callback_functions=None,
            base_path="",
            logger=None,
            save_location=temp_save_file,
            task_description="Test Description"
        )

        assert task_id in tracker2.data['tasks']
        task = tracker2.data['tasks'][task_id]
        assert task["description"] == sample_task_data["description"]
        assert len(task["subtasks"]) == len(sample_task_data["steps"])

    def test_task_description_change(self, temp_save_file):
        """Test behavior when task description changes."""
        # Create first tracker
        tracker1 = TaskTracker(
            callback_functions=None,
            base_path="",
            logger=None,
            save_location=temp_save_file,
            task_description="Description 1"
        )

        # Create second tracker with different description
        tracker2 = TaskTracker(
            callback_functions=None,
            base_path="",
            logger=None,
            save_location=temp_save_file,
            task_description="Description 2"
        )

        with open(temp_save_file, 'r') as f:
            data = json.load(f)
            assert data['task_description'] == "Description 2"
            assert data['tasks'] == {}
            assert data['next_task_id'] == 1

    def test_update_subtask_with_full_id(self, tracker, sample_task_data):
        """Test updating subtask using full subtask ID format."""
        # First add a task to get some subtasks
        result = tracker.add_task(**sample_task_data)
        task_id = result['task_id']
        subtask_id = result['subtask_ids'][0]  # This will be "Task1SubTask1"

        # Update using the full subtask ID
        updates = {
            "status": "completed",
            "description": "Updated via full ID",
            "details": "Testing full ID update",
            "micro_agent": "UpdatedAgent"
        }

        update_result = tracker.update(
            task_id=subtask_id,  # Using full "Task1SubTask1" as task_id
            updates=updates
        )

        assert update_result['status'] == "SUCCESS"
        assert "Updated" in update_result['message']

        # Verify the update in the data structure
        subtask = tracker.data['tasks'][task_id]["subtasks"][subtask_id]
        for field, value in updates.items():
            assert subtask[field] == value

    def test_update_invalid_full_subtask_id(self, tracker, sample_task_data):
        """Test updating with an invalid or nonexistent full subtask ID."""
        # First add a task
        result = tracker.add_task(**sample_task_data)

        # Try to update with invalid subtask ID format
        invalid_updates = [
            "Task1SubTaskInvalid",  # Invalid subtask number
            "Task99SubTask1",  # Nonexistent task
            "TaskSubTask",  # Missing numbers
            "Task1SubTask99"  # Nonexistent subtask
        ]

        for invalid_id in invalid_updates:
            update_result = tracker.update(
                task_id=invalid_id,
                updates={"status": "completed"}
            )

            assert update_result['status'] == "ERROR"
            assert "not found" in update_result['message']