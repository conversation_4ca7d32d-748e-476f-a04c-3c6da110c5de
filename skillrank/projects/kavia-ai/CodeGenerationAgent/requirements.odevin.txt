aenum==3.1.15 ; python_version >= "3.11" and python_version < "4.0"
aiohttp==3.9.5 ; python_version >= "3.11" and python_version < "4.0"
aiolimiter==1.1.0 ; python_version >= "3.11" and python_version < "4.0"
aiosignal==1.3.1 ; python_version >= "3.11" and python_version < "4.0"
annotated-types==0.7.0 ; python_version >= "3.11" and python_version < "4.0"
anyio==4.4.0 ; python_version >= "3.11" and python_version < "4.0"
argon2-cffi-bindings==21.2.0 ; python_version >= "3.11" and python_version < "4.0"
argon2-cffi==23.1.0 ; python_version >= "3.11" and python_version < "4.0"
attrs==23.2.0 ; python_version >= "3.11" and python_version < "4.0"
bashlex==0.18 ; python_version >= "3.11" and python_version < "4.0"
beartype==0.12.0 ; python_version >= "3.11" and python_version < "4"
beautifulsoup4==4.12.3 ; python_version >= "3.11" and python_version < "4.0"
blinker==1.8.2 ; python_version >= "3.11" and python_version < "4"
boto3==1.34.151 ; python_version >= "3.11" and python_version < "4.0"
botocore==1.34.151 ; python_version >= "3.11" and python_version < "4.0"
browsergym-core==0.3.4 ; python_version >= "3.11" and python_version < "4.0"
browsergym-experiments==0.3.4 ; python_version >= "3.11" and python_version < "4.0"
browsergym-miniwob==0.3.4 ; python_version >= "3.11" and python_version < "4.0"
browsergym-webarena==0.3.4 ; python_version >= "3.11" and python_version < "4.0"
browsergym-workarena==0.3.1 ; python_version >= "3.11" and python_version < "4.0"
browsergym==0.3.4 ; python_version >= "3.11" and python_version < "4.0"
cachetools==5.3.3 ; python_version >= "3.11" and python_version < "4.0"
certifi==2024.6.2 ; python_version >= "3.11" and python_version < "4.0"
cffi==1.16.0 ; python_version >= "3.11" and python_version < "4.0"
charset-normalizer==3.3.2 ; python_version >= "3.11" and python_version < "4.0"
click==8.1.7 ; python_version >= "3.11" and python_version < "4.0"
cloudpickle==3.0.0 ; python_version >= "3.11" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.11" and python_version < "4.0" and (platform_system == "Windows" or sys_platform == "win32")
contourpy==1.2.1 ; python_version >= "3.11" and python_version < "4.0"
cycler==0.12.1 ; python_version >= "3.11" and python_version < "4.0"
datasets==2.20.0 ; python_version >= "3.11" and python_version < "4.0"
dill==0.3.8 ; python_version >= "3.11" and python_version < "4.0"
distro==1.9.0 ; python_version >= "3.11" and python_version < "4.0"
dnspython==2.6.1 ; python_version >= "3.11" and python_version < "4.0"
docker==7.1.0 ; python_version >= "3.11" and python_version < "4.0"
docstring-parser==0.16 ; python_version >= "3.11" and python_version < "4.0"
e2b==0.17.1 ; python_version >= "3.11" and python_version < "4.0"
email-validator==2.2.0 ; python_version >= "3.11" and python_version < "4.0"
english-words==2.0.1 ; python_version >= "3.11" and python_version < "4.0"
evaluate==0.4.2 ; python_version >= "3.11" and python_version < "4"
faker==26.0.0 ; python_version >= "3.11" and python_version < "4.0"
farama-notifications==0.0.4 ; python_version >= "3.11" and python_version < "4.0"
fastapi-cli==0.0.4 ; python_version >= "3.11" and python_version < "4.0"
fastapi==0.111.1 ; python_version >= "3.11" and python_version < "4.0"
filelock==3.15.4 ; python_version >= "3.11" and python_version < "4.0"
flask==3.0.3 ; python_version >= "3.11" and python_version < "4"
fonttools==4.53.0 ; python_version >= "3.11" and python_version < "4.0"
frozenlist==1.4.1 ; python_version >= "3.11" and python_version < "4.0"
fsspec==2024.5.0 ; python_version >= "3.11" and python_version < "4.0"
fsspec[http]==2024.5.0 ; python_version >= "3.11" and python_version < "4.0"
gevent==24.2.1 ; python_version >= "3.11" and python_version < "4.0"
google-ai-generativelanguage==0.6.6 ; python_version >= "3.11" and python_version < "4.0"
google-api-core==2.19.1 ; python_version >= "3.11" and python_version < "4.0"
google-api-core[grpc]==2.19.1 ; python_version >= "3.11" and python_version < "4.0"
google-api-python-client==2.134.0 ; python_version >= "3.11" and python_version < "4.0"
google-auth-httplib2==0.2.0 ; python_version >= "3.11" and python_version < "4.0"
google-auth==2.30.0 ; python_version >= "3.11" and python_version < "4.0"
google-cloud-aiplatform==1.60.0 ; python_version >= "3.11" and python_version < "4.0"
google-cloud-bigquery==3.25.0 ; python_version >= "3.11" and python_version < "4.0"
google-cloud-core==2.4.1 ; python_version >= "3.11" and python_version < "4.0"
google-cloud-resource-manager==1.12.3 ; python_version >= "3.11" and python_version < "4.0"
google-cloud-storage==2.17.0 ; python_version >= "3.11" and python_version < "4.0"
google-crc32c==1.5.0 ; python_version >= "3.11" and python_version < "4.0"
google-generativeai==0.7.2 ; python_version >= "3.11" and python_version < "4.0"
google-resumable-media==2.7.1 ; python_version >= "3.11" and python_version < "4.0"
googleapis-common-protos==1.63.2 ; python_version >= "3.11" and python_version < "4.0"
googleapis-common-protos[grpc]==1.63.2 ; python_version >= "3.11" and python_version < "4.0"
greenlet==3.0.0 ; python_version >= "3.11" and python_version < "4.0"
grep-ast==0.3.2 ; python_version >= "3.11" and python_version < "4.0"
grpc-google-iam-v1==0.13.1 ; python_version >= "3.11" and python_version < "4.0"
grpcio-status==1.62.2 ; python_version >= "3.11" and python_version < "4.0"
grpcio==1.64.1 ; python_version >= "3.11" and python_version < "4.0"
gymnasium==0.29.1 ; python_version >= "3.11" and python_version < "4.0"
h11==0.14.0 ; python_version >= "3.11" and python_version < "4.0"
html2text==2024.2.26 ; python_version >= "3.11" and python_version < "4.0"
httpcore==1.0.5 ; python_version >= "3.11" and python_version < "4.0"
httplib2==0.22.0 ; python_version >= "3.11" and python_version < "4.0"
httptools==0.6.1 ; python_version >= "3.11" and python_version < "4.0"
httpx==0.27.0 ; python_version >= "3.11" and python_version < "4.0"
huggingface-hub==0.23.4 ; python_version >= "3.11" and python_version < "4.0"
idna==3.7 ; python_version >= "3.11" and python_version < "4.0"
importlib-metadata==7.1.0 ; python_version >= "3.11" and python_version < "4.0"
itsdangerous==2.2.0 ; python_version >= "3.11" and python_version < "4"
jinja2==3.1.4 ; python_version >= "3.11" and python_version < "4.0"
jmespath==1.0.1 ; python_version >= "3.11" and python_version < "4.0"
joblib==1.4.2 ; python_version >= "3.11" and python_version < "4"
json-repair==0.25.3 ; python_version >= "3.11" and python_version < "4.0"
jsonrpcclient==4.0.3 ; python_version >= "3.11" and python_version < "4.0"
jsonschema-specifications==2023.12.1 ; python_version >= "3.11" and python_version < "4.0"
jsonschema==4.22.0 ; python_version >= "3.11" and python_version < "4.0"
kiwisolver==1.4.5 ; python_version >= "3.11" and python_version < "4.0"
libwebarena==0.0.3 ; python_version >= "3.11" and python_version < "4"
litellm==1.42.5 ; python_version >= "3.11" and python_version < "4.0"
lxml==5.2.2 ; python_version >= "3.11" and python_version < "4.0"
markdown-it-py==3.0.0 ; python_version >= "3.11" and python_version < "4.0"
markupsafe==2.1.5 ; python_version >= "3.11" and python_version < "4.0"
matplotlib==3.9.0 ; python_version >= "3.11" and python_version < "4.0"
mdurl==0.1.2 ; python_version >= "3.11" and python_version < "4.0"
minio==7.2.7 ; python_version >= "3.11" and python_version < "4.0"
multidict==6.0.5 ; python_version >= "3.11" and python_version < "4.0"
multiprocess==0.70.16 ; python_version >= "3.11" and python_version < "4.0"
nltk==3.8.1 ; python_version >= "3.11" and python_version < "4"
numpy==1.26.4 ; python_version >= "3.11" and python_version < "4.0"
openai==1.99.1 ; python_version >= "3.11" and python_version < "4.0"
packaging==24.1 ; python_version >= "3.11" and python_version < "4.0"
pandas==2.2.2 ; python_version >= "3.11" and python_version < "4.0"
pathspec==0.12.1 ; python_version >= "3.11" and python_version < "4.0"
pexpect==4.9.0 ; python_version >= "3.11" and python_version < "4.0"
pillow==10.3.0 ; python_version >= "3.11" and python_version < "4.0"
playwright==1.39.0 ; python_version >= "3.11" and python_version < "4.0"
proto-plus==1.24.0 ; python_version >= "3.11" and python_version < "4.0"
protobuf==4.25.3 ; python_version >= "3.11" and python_version < "4.0"
ptyprocess==0.7.0 ; python_version >= "3.11" and python_version < "4.0"
pyarrow-hotfix==0.6 ; python_version >= "3.11" and python_version < "4.0"
pyarrow==17.0.0 ; python_version >= "3.11" and python_version < "4.0"
pyasn1-modules==0.4.0 ; python_version >= "3.11" and python_version < "4.0"
pyasn1==0.6.0 ; python_version >= "3.11" and python_version < "4.0"
pycparser==2.22 ; python_version >= "3.11" and python_version < "4.0"
pycryptodome==3.20.0 ; python_version >= "3.11" and python_version < "4.0"
pydantic-core==2.18.4 ; python_version >= "3.11" and python_version < "4.0"
pydantic==2.7.4 ; python_version >= "3.11" and python_version < "4.0"
pyee==11.0.1 ; python_version >= "3.11" and python_version < "4.0"
pygments==2.18.0 ; python_version >= "3.11" and python_version < "4.0"
pyparsing==3.1.2 ; python_version >= "3.11" and python_version < "4.0"
python-dateutil==2.9.0.post0 ; python_version >= "3.11" and python_version < "4.0"
python-dotenv==1.0.1 ; python_version >= "3.11" and python_version < "4.0"
python-multipart==0.0.9 ; python_version >= "3.11" and python_version < "4.0"
pytz==2024.1 ; python_version >= "3.11" and python_version < "4.0"
pywin32==306 ; python_version >= "3.11" and python_version < "4.0" and sys_platform == "win32"
pyyaml==6.0.1 ; python_version >= "3.11" and python_version < "4.0"
referencing==0.35.1 ; python_version >= "3.11" and python_version < "4.0"
regex==2024.5.15 ; python_version >= "3.11" and python_version < "4.0"
requests==2.32.3 ; python_version >= "3.11" and python_version < "4.0"
rich==13.7.1 ; python_version >= "3.11" and python_version < "4.0"
rpds-py==0.18.1 ; python_version >= "3.11" and python_version < "4.0"
rsa==4.9 ; python_version >= "3.11" and python_version < "4"
s3transfer==0.10.2 ; python_version >= "3.11" and python_version < "4.0"
safetensors==0.4.3 ; python_version >= "3.11" and python_version < "4"
seaborn==0.13.2 ; python_version >= "3.11" and python_version < "4.0"
setuptools==70.1.1 ; python_version >= "3.11" and python_version < "4.0"
shapely==2.0.4 ; python_version >= "3.11" and python_version < "4.0"
shellingham==1.5.4 ; python_version >= "3.11" and python_version < "4.0"
six==1.16.0 ; python_version >= "3.11" and python_version < "4.0"
sniffio==1.3.1 ; python_version >= "3.11" and python_version < "4.0"
soupsieve==2.5 ; python_version >= "3.11" and python_version < "4.0"
starlette==0.37.2 ; python_version >= "3.11" and python_version < "4.0"
tenacity==8.5.0 ; python_version >= "3.11" and python_version < "4.0"
termcolor==2.4.0 ; python_version >= "3.11" and python_version < "4.0"
text-generation==0.7.0 ; python_version >= "3.11" and python_version < "4.0"
tiktoken==0.7.0 ; python_version >= "3.11" and python_version < "4.0"
tokenizers==0.19.1 ; python_version >= "3.11" and python_version < "4.0"
toml==0.10.2 ; python_version >= "3.11" and python_version < "4.0"
tqdm==4.66.4 ; python_version >= "3.11" and python_version < "4.0"
transformers==4.42.1 ; python_version >= "3.11" and python_version < "4"
tree-sitter-languages==1.10.2 ; python_version >= "3.11" and python_version < "4.0"
tree-sitter==0.21.3 ; python_version >= "3.11" and python_version < "4.0"
typer==0.12.3 ; python_version >= "3.11" and python_version < "4.0"
types-toml==0.10.8.20240310 ; python_version >= "3.11" and python_version < "4.0"
types-tqdm==4.66.0.20240417 ; python_version >= "3.11" and python_version < "4"
typing-extensions==4.12.2 ; python_version >= "3.11" and python_version < "4.0"
tzdata==2024.1 ; python_version >= "3.11" and python_version < "4.0"
uritemplate==4.1.1 ; python_version >= "3.11" and python_version < "4.0"
urllib3==2.2.2 ; python_version >= "3.11" and python_version < "4.0"
uvicorn==0.30.3 ; python_version >= "3.11" and python_version < "4.0"
uvicorn[standard]==0.30.3 ; python_version >= "3.11" and python_version < "4.0"
uvloop==0.19.0 ; (sys_platform != "win32" and sys_platform != "cygwin") and platform_python_implementation != "PyPy" and python_version >= "3.11" and python_version < "4.0"
watchfiles==0.22.0 ; python_version >= "3.11" and python_version < "4.0"
websockets==12.0 ; python_version >= "3.11" and python_version < "4.0"
werkzeug==3.0.3 ; python_version >= "3.11" and python_version < "4"
xxhash==3.4.1 ; python_version >= "3.11" and python_version < "4.0"
yarl==1.9.4 ; python_version >= "3.11" and python_version < "4.0"
zipp==3.19.2 ; python_version >= "3.11" and python_version < "4.0"
zope-event==5.0 ; python_version >= "3.11" and python_version < "4.0"
zope-interface==6.4.post2 ; python_version >= "3.11" and python_version < "4.0"
