#!/bin/bash

set -e -x

print_help() {
    echo "Usage: $0 [command]"
    echo
    echo "If a command is provided, it will be executed in the Docker container,"
    echo "else an interactive bash session will be started."
    echo
    echo "Examples:"
    echo "  $0                                  # Starts an interactive bash session"
    echo "  $0 'python /app/src/ux/pyQt_ui.py'  # Runs the PyQt UI script"
}

WORKSPACE_DIR=$(realpath .)/workspace
TEMPORARY_WORKSPACE=${WORKSPACE_DIR}/tmp
APP_DIRECTORY=$(realpath .)

mkdir -p $APP_DIRECTORY
mkdir -p $TEMPORARY_WORKSPACE

USER_ID=$(id -u)

# On macOS, use a different group ID to avoid conflicts

if [[ "$(uname -s)" == "Darwin" ]]; then
    GROUP_ID=1000
else
    GROUP_ID=$(id -g)
fi

docker build -t code_generator \
    --build-arg USER_ID=$USER_ID \
    --build-arg GROUP_ID=$GROUP_ID .

if [ $# -eq 0 ]; then
    CMD=("/bin/bash")
elif [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    print_help
    exit 0
else
    CMD=("$@")
fi

##################################################################
#           Handle dispaly forwarding to the container
##################################################################
case "$(uname -s)" in
    Linux*)
        DISPLAY_SETUP="-e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix"
        xhost +local:docker
        ;;
    Darwin*)
        ip=$(ifconfig en0 | grep inet | awk '$1=="inet" {print $2}')
        DISPLAY_SETUP="-e DISPLAY=$ip:0 -v /tmp/.X11-unix:/tmp/.X11-unix"
        xhost + $ip
        ;;
    MINGW*|CYGWIN*|MSYS*)
        # For Windows using VcXsrv or similar
        export DISPLAY=host.docker.internal:0.0
        DISPLAY_SETUP="-e DISPLAY=$DISPLAY"
        ;;
    *)
        echo "Unsupported OS for X11 forwarding. You may need to set this up manually."
        DISPLAY_SETUP=""
        ;;
esac

# Build and run the Docker containers
docker-compose build
docker-compose up -d

# Execute the command in the code_generator container
docker-compose exec code_generator "$CMD"

# Stop the containers when done
docker-compose down

