import subprocess
import toml
from pathlib import Path

def get_installed_versions():
    """Get installed package versions using pip freeze"""
    result = subprocess.run(["pip", "freeze"], capture_output=True, text=True, check=True)
    versions = {}
    for line in result.stdout.splitlines():
        if "==" in line:
            name, version = line.split("==")
            versions[name.lower()] = version
    return versions

def update_versions_in_toml():
    """Update versions in pyproject.toml while preserving structure"""
    with open("pyproject.toml", "r") as f:
        content = f.read()
        pyproject = toml.loads(content)
    
    # Get installed versions
    versions = get_installed_versions()
    
    deps = pyproject["tool"]["poetry"]["dependencies"]
    for package in deps:
        if package.lower() in versions:
            deps[package] = f"^{versions[package.lower()]}"
    
    with open("pyproject.toml", "w") as f:
        f.write(toml.dumps(pyproject))

if __name__ == "__main__":
    update_versions_in_toml()
