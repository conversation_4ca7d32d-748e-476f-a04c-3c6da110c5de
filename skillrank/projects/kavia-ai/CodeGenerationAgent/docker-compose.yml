version: '3.8'

services:
  neo4j:
    image: neo4j:latest
    environment:
      NEO4J_AUTH: neo4j/password
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - ./workspace/database:/data
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:7474 || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  code_generator:
    build:
      context: .
      args:
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}
    volumes:
      - ${TEMPORARY_WORKSPACE:-./workspace/tmp}:/tmp
      - ${APP_DIRECTORY:-.}:/app
      - /run/user/${USER_ID:-1000}:/run/user/${USER_ID:-1000}
    environment:
      - DISPLAY=${DISPLAY}
    depends_on:
      neo4j:
        condition: service_healthy
    entrypoint: ["/entrypoint.sh"]

volumes:
  database:

networks:
  default:
    name: code_generator_network
