# General
DOCKER_ANDROID_VERSION = "DOCKER_ANDROID_VERSION"
USER_BEHAVIOR_ANALYTICS = "USER_BEHAVIOR_ANALYTICS"
APPIUM = "APPIUM"
APPIUM_ADDITIONAL_ARGS = "APPIUM_ADDITIONAL_ARGS"
DISPLAY = "DISPLAY"
SCREEN_DEPTH = "SCREEN_DEPTH"
SCREEN_HEIGHT = "SCREEN_HEIGHT"
SCREEN_NUMBER = "SCREEN_NUMBER"
SCREEN_WIDTH = "SCREEN_WIDTH"
VNC_PASSWORD = "VNC_PASSWORD"
VNC_PORT = "VNC_PORT"
WEB_VNC_PORT = "WEB_VNC_PORT"
WEB_VNC = "WEB_VNC"
WORK_PATH = "WORK_PATH"
LOG_PATH = "LOG_PATH"
WEB_LOG_PORT = "WEB_LOG_PORT"
WEB_LOG = "WEB_LOG"

# Device
DEVICE_INTERVAL_WAITING = "DEVICE_INTERVAL_WAITING"
DEVICE_TYPE = "DEVICE_TYPE"

# Device (Emulator)
EMULATOR_ADDITIONAL_ARGS = "EMULATOR_ADDITIONAL_ARGS"
EMULATOR_ANDROID_VERSION = "EMULATOR_ANDROID_VERSION"
EMULATOR_DATA_PARTITION = "EMULATOR_DATA_PARTITION"
EMULATOR_DEVICE = "EMULATOR_DEVICE"
EMULATOR_IMG_TYPE = "EMULATOR_IMG_TYPE"
EMULATOR_NAME = "EMULATOR_NAME"
EMULATOR_NO_SKIN = "EMULATOR_NO_SKIN"
EMULATOR_SYS_IMG = "EMULATOR_SYS_IMG"

# Device (Genymotion - General)
GENYMOTION_TEMPLATE_PATH = "GENYMOTION_TEMPLATE_PATH"

# Device (Geny_SAAS)
GENY_SAAS_USER = "GENY_SAAS_USER"
GENY_SAAS_PASS = "GENY_SAAS_PASS"
GENY_SAAS_TEMPLATE_FILE_NAME = "saas.json"

# Device (Geny_AWS)
AWS_ACCESS_KEY_ID = "AWS_ACCESS_KEY_ID"
AWS_SECRET_ACCESS_KEY = "AWS_SECRET_ACCESS_KEY"
GENY_AWS_TEMPLATE_FILE_NAME = "aws.json"
