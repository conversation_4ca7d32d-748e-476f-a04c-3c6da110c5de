## License Information

Copyright 2023 budi utomo

This program is subject to the terms of the Apache License, Version 2.0 AND the following amendments on forks and data processing. Thus, this is a Custom Apache 2.0 License, NOT a dual-license model you may choose from.


You can obtain a copy of the license at http://www.apache.org/licenses/LICENSE-2.0


Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.


## Forks
Additionally to Apache-2.0, when you fork this repo you are required to either remove our Google Form ID: 1FAIpQLSdrKWQdMh6Nt8v8NQdYvTIntohebAgqWCpXT3T9NofAoxcpkw or stop usage gathering completely.

## Data processing agreement
By using this software you agree that the following non-PII (non personally identifiable information) data will be collected, processed and used by the maintainers for the purpose of improving the docker-android project.


By using this software you also grant us a nonexclusive, irrevocable, world-wide, perpetual royalty-free permission to use, modify and publish these data for all purposes, internally or publicly, including the right to sub-license said permission rights.


We collect, process and use the following data:

* Date and time when Docker-Android started
* User (it will collect the information about Release Version of Machine), e.g. Linux-5.4.0-146-generic-x86_64-with-glibc2.29_#163-Ubuntu_SMP_Fri_Mar_17_18:26:02_UTC_2023. This does not allow us to track individual users but does enable us to accurately measure user counts
* City (the information come from https://ipinfo.io)
* Region (the information come from https://ipinfo.io)
* Country (the information come from https://ipinfo.io)
* Release version of Docker-Android
* Appium (Whether user use Appium or not - The possible value will be "true" or "false")
* Appium Additional Arguments
* Web-Log (Whether user use Web-Log feature or not - The possible value will be "true" or "false")
* Web-Vnc (Whether user use Web-Vnc feature or not - The possible value will be "true" or "false")
* Screen-Resolution
* Device Type (Which docker image is used - The possible value will be "emulator" or "geny_cloud" or "geny_aws")
* Emulator Device (Which device profile and skin is used if the user use device_type "emulator")
* Emulator Android Version (Which Android version is used if the user use device_type "emulator"
* Emulator No-Skin feature (Whether user use no-skin feature or not - The possible value will be "true" or "false")
* Emulator Data Partition
* Emulator Additional Arguments

## End of License Information

More information about anonymized data collection can be seen [here](./documentations/USER_BEHAVIOR_ANALYTICS.md)
