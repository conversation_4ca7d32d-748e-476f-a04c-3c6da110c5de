FROM appium/appium:v2.15.0-p1

USER root

#================
# Add Kavia User
#----------------
ARG USERNAME=kavia
ARG HOST_UID=1001

# Dynamically find group name with GID 1301
RUN existing_group=$(getent group 1301 | cut -d: -f1) && \
    useradd -m -s /bin/bash -u ${HOST_UID} -G ${existing_group} ${USERNAME} && \
    echo "${USERNAME} ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers && \
    mkdir -p /tmp/kavia && \
    chown -R ${USERNAME}:${existing_group} /tmp/kavia

#================
# Basic Packages
#----------------
RUN apt-get -qqy update && apt-get -qqy install --no-install-recommends \
    socat \
    supervisor \
    python3-pip \
    git \
    xz-utils \
    curl \
    unzip \
    wget \
    openjdk-11-jdk \
 && apt autoremove -y \
 && apt clean all \
 && rm -rf /var/lib/apt/lists/*

#==================
# Android SDK Setup
#==================
ENV ANDROID_HOME=/opt/android
ENV ANDROID_SDK_ROOT=$ANDROID_HOME
ENV PATH=${PATH}:${ANDROID_HOME}/cmdline-tools/latest/bin:${ANDROID_HOME}/platform-tools

# Create directory with existing group permissions
RUN mkdir -p ${ANDROID_HOME} && \
    chown -R 1300:1301 ${ANDROID_HOME} && \
    chmod -R 775 ${ANDROID_HOME}

# Install SDK tools with androidusr
USER 1300:1301
RUN mkdir -p ${ANDROID_HOME}/cmdline-tools && \
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O /tmp/tools.zip && \
    unzip -q /tmp/tools.zip -d /tmp && \
    mv /tmp/cmdline-tools ${ANDROID_HOME}/cmdline-tools/latest && \
    rm /tmp/tools.zip && \
    yes | sdkmanager --licenses && \
    sdkmanager "platform-tools" "platforms;android-33" "build-tools;33.0.0"

USER root

#==================
# Flutter Setup
#==================
ENV FLUTTER_VERSION="3.19.3"
ENV FLUTTER_HOME="/opt/flutter"
ENV PATH="${FLUTTER_HOME}/bin:${PATH}"

# Install Flutter using tarball (faster than git clone)
RUN mkdir -p ${FLUTTER_HOME} && \
    wget -q https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_${FLUTTER_VERSION}-stable.tar.xz -O /tmp/flutter.tar.xz && \
    tar xf /tmp/flutter.tar.xz -C /opt && \
    rm /tmp/flutter.tar.xz && \
    chown -R 1300:1301 ${FLUTTER_HOME} && \
    chmod -R 775 ${FLUTTER_HOME}

USER 1300:1301
RUN flutter precache && \
    yes | flutter doctor --android-licenses && \
    flutter config --no-analytics && \
    flutter doctor -v

USER root
# Configure Git safe directory for androidusr
RUN git config --system --add safe.directory ${FLUTTER_HOME}

#=====================
# Set release version
#=====================
ARG DOCKER_ANDROID_VERSION=test-version
ENV DOCKER_ANDROID_VERSION=${DOCKER_ANDROID_VERSION}

#===============
# Expose Ports
#===============
EXPOSE 4723 5554 5555