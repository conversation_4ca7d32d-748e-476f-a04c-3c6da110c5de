FROM appium/appium:v2.15.0-p1

USER root

#================
# Add Kavia User
#----------------
ARG USERNAME=kavia
ARG HOST_UID=1001

# Dynamically find group name with GID 1301
RUN existing_group=$(getent group 1301 | cut -d: -f1) && \
    useradd -m -s /bin/bash -u ${HOST_UID} -G ${existing_group} ${USERNAME} && \
    echo "${USERNAME} ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers && \
    mkdir -p /tmp/kavia && \
    chown -R ${USERNAME}:${existing_group} /tmp/kavia

#================
# Basic Packages
#----------------
RUN apt-get -qqy update && apt-get -qqy install --no-install-recommends \
    socat \
    supervisor \
    python3-pip \
    git \
    xz-utils \
    curl \
    unzip \
    wget \
    # Java 17 installation
    && wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | apt-key add - \
    && echo "deb https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list \
    && apt-get update && apt-get install -y \
    temurin-17-jdk \
 && apt autoremove -y \
 && apt clean all \
 && rm -rf /var/lib/apt/lists/*

#==================
# Android SDK Setup
#==================
ENV ANDROID_HOME=/opt/android
ENV ANDROID_SDK_ROOT=$ANDROID_HOME
ENV PATH=${PATH}:${ANDROID_HOME}/cmdline-tools/latest/bin:${ANDROID_HOME}/platform-tools:${ANDROID_HOME}/build-tools/33.0.0

# Create directory with existing group permissions
RUN mkdir -p ${ANDROID_HOME} && \
    chown -R 1300:1301 ${ANDROID_HOME} && \
    chmod -R 775 ${ANDROID_HOME}

# Install SDK tools with androidusr
USER 1300:1301
RUN mkdir -p ${ANDROID_HOME}/cmdline-tools && \
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O /tmp/tools.zip && \
    unzip -q /tmp/tools.zip -d /tmp && \
    mv /tmp/cmdline-tools ${ANDROID_HOME}/cmdline-tools/latest && \
    rm /tmp/tools.zip && \
    yes | sdkmanager --licenses && \
    sdkmanager "platform-tools" \
               "platforms;android-33" \
               "build-tools;33.0.0" \
               "extras;android;m2repository" \
               "extras;google;m2repository"

USER root

#=====================
# Set release version
#=====================
ARG DOCKER_ANDROID_VERSION=test-version
ENV DOCKER_ANDROID_VERSION=${DOCKER_ANDROID_VERSION}

#===============
# Expose Ports
#===============
EXPOSE 4723 5554 5555