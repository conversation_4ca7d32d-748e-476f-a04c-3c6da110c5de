This is a modified version of Docker-Android, customized to work specifically with a single Android device (Nexus 4). The original Docker-Android image was built for Android development and testing, but this version has been streamlined for a specific use case with Flutter development.

Advantages of using this project
--------------------------------
1. Pre-configured emulator with Nexus 4 device
2. VNC support for visual access to the container
3. Web-UI accessible logging feature
4. External emulator control via ADB connect


List of Devices
---------------
Device Type | Device
Phone  | Nexus 4

Requirements
------------

1. Docker is installed on your system.

Quick Start
-----------

1. Your machine should support virtualization. To check if the virtualization is enabled is:
    ```
    sudo apt install cpu-checker
    kvm-ok
    ```

2. Build images
- For Android Emulator
    ```
    ./app.sh build android-emulator <version> && ./app.sh build emulator <version> 14.0 android-emulator
    ```
- For Flutter
    ```
    ./app.sh build flutter-emulator <version> && ./app.sh build emulator <version> 14.0 flutter-emulator
    ```

3. Tag the images if you want to push them to the Docker Hub.
    ```
    docker tag <dockerhub_username>/android-emulator:14.0_<version> <dockerhub_username>/android-emulator:14.0
    docker tag <dockerhub_username>/flutter-emulator:14.0_<version> <dockerhub_username>/flutter-emulator:14.0
    ```

4. Push the images to the Docker Hub (Optional). Change the tag to your Docker Hub username.
    ```
    docker push android-emulator:14.0
    docker push flutter-emulator:14.0
    ```

5. Run the container
- For Android Emulator
    ```
    docker run -d -p 6080:6080 -p 5554:5554 -p 5555:5555 -e EMULATOR_DEVICE="Nexus 4" -e EMULATOR_ANDROID_VERSION="14.0" -e WEB_VNC=true --device /dev/kvm --name android-container gerardokavia/android-emulator:14.0_<version>
    ```

- For Flutter
    ```
    docker run -d -p 6080:6080 -p 5554:5554 -p 5555:5555 -e EMULATOR_DEVICE="Nexus 4" -e EMULATOR_ANDROID_VERSION="14.0" -e WEB_VNC=true --device /dev/kvm --name android-container gerardokavia/flutter-emulator:14.0_<version>
    ```

4. Open ***http://localhost:6080*** to see inside running container.

5. To check the status of the emulator
    ```
    docker exec -it android-container cat device_status
    ```

LICENSE
-------
See [License](LICENSE.md)
