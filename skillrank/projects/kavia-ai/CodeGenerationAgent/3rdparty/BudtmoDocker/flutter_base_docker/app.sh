#!/bin/bash

function is_str_in_list(){
    local given_str=${1}
    local list_str=${@:2}

    if [[ ! " ${list_str[*]} " =~ " ${given_str} " ]]; then
        echo "${given_str} is not supported!"
        exit 1
    fi
}

DOCKER_USERNAME="gerardokavia"
base_projects=("android-emulator" "flutter-emulator")
projects=("${base_projects[@]}" "emulator")

if [ -z "${2}" ]; then
    read -p "Project ($(echo "${projects[@]}" | tr ' ' '|')) : " p
else
    p=${2}
fi
is_str_in_list ${p} ${projects[@]}

if [ -z "${3}" ]; then
    read -p "Release Version (v1.0.0|v1.0.1|etc) : " r_v
else
    r_v=${3}
fi

FOLDER_PATH="docker/${p}"
IMAGE_NAME="${DOCKER_USERNAME}/${p}"

if [[ "${p}" == "emulator" ]]; then
    supported_android_version=("9.0" "10.0" "11.0" "12.0" "13.0" "14.0")
    declare -A api_levels=(
        ["9.0"]=28
        ["10.0"]=29
        ["11.0"]=30
        ["12.0"]=32
        ["13.0"]=33
        ["14.0"]=34
    )

    if [ -z "${4}" ]; then
        read -p "Android Version ($(echo "${supported_android_version[@]}" | tr ' ' '|')) : " a_v
    else
        a_v=${4}
    fi
    is_str_in_list ${a_v} ${supported_android_version[@]}
    a_l=${api_levels[${a_v}]}

    if [ -z "${5}" ]; then
        read -p "Base Image Type ($(echo "${base_projects[@]}" | tr ' ' '|')) : " base_type
    else
        base_type=${5}
    fi
    is_str_in_list ${base_type} ${base_projects[@]}
    
    TAG_NAME="${a_v}_${r_v}"
else
    TAG_NAME="base_${r_v}"
    base_type=${p}
fi

IMAGE_NAME_SPECIFIC_RELEASE=${IMAGE_NAME}:${TAG_NAME}
echo "Building: ${IMAGE_NAME_SPECIFIC_RELEASE}"

cmd="docker build -t ${IMAGE_NAME_SPECIFIC_RELEASE} --build-arg DOCKER_ANDROID_VERSION=${r_v}"

cmd+=" --build-arg DOCKER_USERNAME=${DOCKER_USERNAME}"
cmd+=" --build-arg BASE_IMAGE_TAG=${base_type}"

if [[ "${p}" == "emulator" ]]; then
    cmd+=" --build-arg EMULATOR_ANDROID_VERSION=${a_v} --build-arg EMULATOR_API_LEVEL=${a_l}"
fi

cmd+=" -f ${FOLDER_PATH} ."
echo "Executing: ${cmd}"
${cmd}