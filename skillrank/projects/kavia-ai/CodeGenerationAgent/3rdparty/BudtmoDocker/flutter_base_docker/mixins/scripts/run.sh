#!/bin/bash

setup_permissions() {
    sudo mkdir -p "${WORK_DIR}/logs"
    sudo mkdir -p "${APP_PATH}/mixins/configs/process"
    sudo mkdir -p "${WORK_DIR}/.config/Android Open Source Project"
    
    sudo chmod -R 777 "${WORK_DIR}/logs"
    sudo chmod -R 777 "${APP_PATH}"
    sudo chmod -R 777 "${WORK_DIR}/.config"
    
    if [ -d "/opt/android" ]; then
        sudo chmod -R 777 /opt/android
    fi
}

function shutdown(){
    echo "SIGTERM is received! Clean-up will be executed if needed!"
    process_id=$(pgrep -f "start device")
    kill ${process_id}
    sleep 10
    exec gosu kavia "$@"
    if [[ ${DEVICE_TYPE} == "geny_aws" ]]; then
        sleep 180
    fi
}

trap shutdown SIGTERM

setup_permissions

SUPERVISORD_CONFIG_PATH="${APP_PATH}/mixins/configs/process"

sudo chmod -R 777 ${SUPERVISORD_CONFIG_PATH}

if [[ ${DEVICE_TYPE} == "geny_"* ]]; then
    /usr/bin/supervisord --configuration ${SUPERVISORD_CONFIG_PATH}/supervisord-base.conf & \
    wait
elif [[ ${EMULATOR_HEADLESS} == true ]]; then
    /usr/bin/supervisord --configuration ${SUPERVISORD_CONFIG_PATH}/supervisord-port.conf & \
    /usr/bin/supervisord --configuration ${SUPERVISORD_CONFIG_PATH}/supervisord-base.conf & \
    wait
else
    /usr/bin/supervisord --configuration ${SUPERVISORD_CONFIG_PATH}/supervisord-screen.conf & \
    /usr/bin/supervisord --configuration ${SUPERVISORD_CONFIG_PATH}/supervisord-port.conf & \
    /usr/bin/supervisord --configuration ${SUPERVISORD_CONFIG_PATH}/supervisord-base.conf & \
    wait
fi
