[supervisord]
nodaemon=true
logfile=%(ENV_LOG_PATH)s/supervisord-extend.log
childlogdir=%(ENV_LOG_PATH)s

[program:d_screen]
command=/usr/local/bin/docker-android start display_screen
autostart=true
autorestart=true
stdout_logfile=%(ENV_LOG_PATH)s/display_screen.stdout.log
stderr_logfile=%(ENV_LOG_PATH)s/display_screen.stderr.log
redirect_stderr=false
priority=1

[program:d_wm]
command=/usr/local/bin/docker-android start display_wm
autostart=true
autorestart=true
stdout_logfile=%(ENV_LOG_PATH)s/display_wm.stdout.log
stderr_logfile=%(ENV_LOG_PATH)s/display_wm.stderr.log
redirect_stderr=false
priority=2

[program:vnc_server]
command=/usr/local/bin/docker-android start vnc_server
autostart=true
autorestart=true
stdout_logfile=%(ENV_LOG_PATH)s/vnc_server.stdout.log
stderr_logfile=%(ENV_LOG_PATH)s/vnc_server.stderr.log
redirect_stderr=false
priority=3

[program:vnc_web]
command=/usr/local/bin/docker-android start vnc_web
autostart=true
autorestart=true
stdout_logfile=%(ENV_LOG_PATH)s/vnc_web.stdout.log
stderr_logfile=%(ENV_LOG_PATH)s/vnc_web.stderr.log
redirect_stderr=false
priority=3
