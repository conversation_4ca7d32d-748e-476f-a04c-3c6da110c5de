[supervisord]
nodaemon=true
logfile=%(ENV_LOG_PATH)s/supervisord-base.log
childlogdir=%(ENV_LOG_PATH)s

[program:device]
command=/usr/local/bin/docker-android start device
autostart=true
autorestart=false
startretries=0
stdout_logfile=%(ENV_LOG_PATH)s/device.stdout.log
redirect_stderr=true
priority=1
stopsignal=TERM
stopwaitsecs=10

[program:appium]
command=/usr/local/bin/docker-android start appium
autostart=true
autorestart=false
startretries=0
stdout_logfile=%(ENV_LOG_PATH)s/appium.stdout.log
redirect_stderr=true
priority=1

[program:log_web_shared]
command=/usr/local/bin/docker-android share log
autostart=true
autorestart=true
stdout_logfile=%(ENV_LOG_PATH)s/log_shared.stdout.log
stderr_logfile=%(ENV_LOG_PATH)s/log_shared.stderr.log
redirect_stderr=false
priority=2
