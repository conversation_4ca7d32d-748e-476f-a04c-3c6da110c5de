# PreviewManager State Management

## Overview

This document explains the architectural decisions made regarding state management in the `PreviewManager` class. The primary focus is on the consistent state management approach used to track application build and runtime status throughout its lifecycle.


## State Lifecycle

The system uses an Enum `PreviewState` to define four primary states that cover the complete application lifecycle:

| State | Enum Value | Description |
|-------|----------|-------------|
| Not Started | `STATE_NOT_STARTED` | Initial state; no build has been attempted yet |
| Building | `STATE_BUILDING` | Build or setup process is currently in progress |
| Running | `STATE_RUNNING` | Application has successfully built and is running |
| Failed | `STATE_FAILED` | Build or run process has failed |

### State Transitions

The following state transitions are possible:

```
(Initial) → STATE_NOT_STARTED
STATE_NOT_STARTED → STATE_BUILDING
STATE_BUILDING → STATE_RUNNING (success)
STATE_BUILDING → STATE_FAILED (error)
STATE_RUNNING → STATE_BUILDING (rebuild)
Any State → STATE_FAILED (on exception)
```

## Implementation Details

### State Constants

States are defined as class constants at the top of the `PreviewManager` class:

```python
# Define states as class constants for consistency
STATE_NOT_STARTED = 'not_started'
STATE_BUILDING = 'building'
STATE_RUNNING = 'running'
STATE_FAILED = 'failed'
```

### State Storage

States are persisted to a JSON file (`.preview_build_status`) in the project directory, which contains:

- Current state (`status`)
- Timestamp of the state change
- URL of the running application (if available)

Sample JSON for different states:

```json
// Not Started state
{
  "status": "not_started",
  "timestamp": "2025-05-07T18:42:15.123456"
}

// Building state
{
  "status": "building",
  "timestamp": "2025-05-07T18:43:22.654321"
}

// Running state
{
  "status": "running",
  "timestamp": "2025-05-07T18:45:37.987654",
  "url": "http://localhost:3000"
}

// Failed state
{
  "status": "failed",
  "timestamp": "2025-05-07T18:44:18.246135"
}
```

The status file is accessed using thread-safe operations to avoid race conditions in the asynchronous build process.








