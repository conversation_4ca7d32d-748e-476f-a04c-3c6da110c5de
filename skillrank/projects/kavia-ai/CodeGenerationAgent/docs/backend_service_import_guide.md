# Backend Integration: Work Item Configuration

This document describes how to configure backend service implementations in a work item and how this information is processed by the system.

## Work Item Configuration

To integrate backend services, you need to add a `backend_implementations` array to your work item. This array contains configuration objects for each backend service you want to set up and manage.

### Basic Structure

```json
{
  "component_name": "Your Component",
  "description": "Your component description",
  // ... other work item fields ...
  
  "backend_implementations": [
    {
      "folder": "service-directory", // Directory relative to the work directory
      "install_commands": "command1 && command2", // Fields separated by &&
      "startup_command": "python run.py", // Command to start the service
      "tcp_ports": "8080,8081", // Comma-separated list of TCP ports
      "ci_lint_command": "optional-lint-command" // Optional linting command
    },
    // Additional backend services as needed
  ]
}
```

### Required Fields for Each Backend Implementation

Each object in the `backend_implementations` array must include:

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `folder` | String | Directory containing the backend service code | Yes |
| `install_commands` | String | Commands to install dependencies (can be multi-line) | Yes |
| `startup_command` | String | Command to start the service | Yes |
| `tcp_ports` | String | Comma-separated list of TCP ports the service will use | Yes |
| `ci_lint_command` | String | Command for linting (optional) | No |

### Example Configuration

```json
"backend_implementations": [
  {
    "folder": "ttt-backend",
    "install_commands": "python3 -m venv .venv && source .venv/bin/activate && pip3 install -r requirements.txt",
    "startup_command": "source .venv/bin/activate && python3 run.py",
    "tcp_ports": "5010",
    "ci_lint_command": ""
  }
]
```

## How the Information Is Used

The `ServiceExtractor` class processes the backend implementations in the work item through these steps:

1. **Extraction and Validation**: The `extract_backend_implementations` method:
   - Extracts the backend implementations from the work item
   - Validates each implementation against the required schema
   - Ensures TCP ports are valid
   - Returns validated implementations

2. **Installation**: For each validated implementation, the system:
   - Changes to the specified directory (`folder`)
   - Executes the `install_commands` to set up dependencies
   - Reports success or failure to the user

3. **Service Startup**: After installation:
   - The `startup_command` is executed in the background
   - The process is kept tracked for management
   - The service is considered running on the specified `tcp_ports`

4. **Work Item Modification**: After processing:
   - The `status` field is added to each backend implementation (set to "RUNNING")
   - The `install_commands` field is removed from the work item to avoid the LLM repeating the installation
   - The updated work item is then used for subsequent operations

### Code Flow

```python
# 1. Extract and validate backend implementations
services = extractor.extract_backend_implementations(work_item, executor, chat)

# 2. For each service, install and start
for service in services:
    # Install dependencies
    install_command = service['install_commands'].split('\n')
    for command in install_command:
        command = f"cd {service_directory} && {command}"
        result, success = executor.run_one_shot_command(command, timeout=300)
    
    # Start the service
    startup_command = service['startup_command']
    startup_command = f"cd {service_directory} && {startup_command}"
    process = start_long_running_process(startup_command, executor)
    service['process'] = process

# 3. Update work item
for service in work_item['backend_implementations']:
    if 'install_commands' in service:
        del service['install_commands']
    service['status'] = "RUNNING"
```

## Integration Example

In a UI application (like the PyQt example), you would:

1. Collect backend implementation details from the user
2. Add them to a list in your application
3. Include them in the work item before processing:

```python
def start_process(self, previous_context=None):
    # ... other initialization code ...
    
    # Add backend implementations to the work item
    if self.backend_implementations:
        work_item['backend_implementations'] = self.backend_implementations
        print(f"Added {len(self.backend_implementations)} backend implementations to work item")
    
    # Start the agent/process with the configured work item
    self._init_agent(agent_type, llm, session_dir, micro_agent_config,
                    model_name, prompts_base_path, previous_context=previous_context, work_item=work_item)
```

## Validation Rules

The system enforces these validation rules for backend implementations:

1. All required fields must be present
2. `tcp_ports` must be valid port numbers (1-65535)
3. `folder` path must not be empty
4. All fields must be of the correct type

If validation fails, a `BackendValidationError` is raised with details about the issue.
