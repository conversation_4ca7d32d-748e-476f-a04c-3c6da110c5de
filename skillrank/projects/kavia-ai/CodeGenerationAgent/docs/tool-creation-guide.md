# Guide: Creating and Adding a New Tool to Agent Framework

This guide outlines the process of creating a new tool and integrating it into our framework. Follow these steps and best practices to ensure your tool is compatible and follows the established patterns.

## 1. Tool Class Creation

### 1.1 Base Class and Naming

- Create a new Python file for your tool, named appropriately (e.g., `my_new_tool.py`). Tool name cannot contain a `_` character. The tool name should indicate the goals and the scope of the tool.
- Define a class that inherits from `BaseToolInterface`:

```python
from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface

class MyNewTool(BaseToolInterface):
    def get_tool_name(self):
        return "MyNewTool"
```

### 1.2 Constructor

- Implement the `__init__` method, ensuring it takes the required parameters:

```python
def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
    super().__init__(callback_functions, base_path, logger, llm, executor)
    # Initialize any tool-specific attributes here
```

## 2. Function Decoration

### 2.1 Using the `@register_function` Decorator

- Use the `@register_function` decorator to register functions that should be exposed to the LLM:

```python
from code_generation_core_agent.agents.tools.schemas.decorators import register_function

@register_function(
    description="Description of what the function does",
    parameters={
        "type": "object",
        "properties": {
            "param1": {"type": "string", "description": "Description of param1"},
            "param2": {"type": "integer", "description": "Description of param2"}
        },
        "required": ["param1"]
    }
)
def my_function(self, param1, param2=None):
    # Function implementation
```

### 2.2 Decorator Requirements

- Ensure the decorator's `parameters` match the function's signature.
- Mark optional parameters in the function signature with default values.
- Include all parameters in the decorator's `properties`, marking required ones in the `required` list.

## 3. Function Implementation

### 3.1 Error Handling

- Use try-except blocks to catch and handle errors gracefully. Registered functions should always return a dictionary with status.

```python
@register_function(......)
def my_function(self, param1, param2=None):
    try:
        # Function logic here
        return {"status": "SUCCESS", "result": result}
    except Exception as e:
        self.logger.error(f"Error in my_function: {str(e)}")
        return {"status": "ERROR", "message": str(e)}
```

### 3.2 Logging

- Use the provided logger for all logging needs:

```python
self.logger.info(f"Processing {param1}")
```

### 3.3 Output Callbacks

- Use the output callback for user-facing messages:

```python
def _send_output(self, message):
    if self.output_callback:
        self.output_callback(message)
```

## 4. Integration with AgentPreProcessor

The `AgentPreProcessor` class is responsible for pre-processing function calls and registrations. When adding a new tool:

1. Ensure your tool class is added to the `applied_classes` list in the `AgentPreProcessor` initialization.
2. The pre-processor will automatically add standard parameters (reason, observations, root_cause_analysis) to your function schemas.

## 5. Adding the Tool to CombinedTools

To make your tool available in the framework:

1. Import your tool in `combined_tools.py`:

```python
from .my_new_tool import MyNewTool
```

2. Add an instance of your tool to the `tool_instances` list in the `setup_dynamic_tool` method of `CodeGenerationAgent`:

```python
my_new_tool = MyNewTool(callback_functions,
                        request_context['execution_base_path'],
                        logger=tools_logger, llm=llm, executor=self.executor)
tool_instances.append(my_new_tool)
```

After creating your new tool, you'll need to add it to the agent mapping to make it available for use. This is done by updating the `micro_agent_mapping.yaml` file.
