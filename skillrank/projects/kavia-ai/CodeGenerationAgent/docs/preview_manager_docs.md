# PreviewManager Documentation

## Overview

The `PreviewManager` is a singleton class responsible for managing the build and runtime status of applications in the Kavia CodeGenerationAgent framework. It handles both web and mobile application previews, providing asynchronous build management with state tracking.

## Key Features

- **Singleton Pattern**: Ensures only one instance manages preview operations
- **State Management**: Tracks application lifecycle through defined states
- **Asynchronous Operations**: Non-blocking build and server management
- **Multi-Platform Support**: Handles both web and mobile applications
- **Port Management**: Automatic port allocation and conflict resolution
- **Thread-Safe Operations**: Safe concurrent access to shared resources

## State Management

You can check here for the detailed state management documentation: [PreviewManager State Management](docs/preview_state_management.md).

### State Persistence

States are persisted to a JSON file (`.preview_build_status`) in the project directory:

```json
{
  "status": "running",
  "timestamp": "2025-05-07T18:45:37.987654",
  "url": "http://localhost:3000"
}
```

## Basic Usage

### 1. Getting an Instance

```python
from code_generation_core_agent.agents.preview import PreviewManager

# Get singleton instance
preview_manager = PreviewManager.get_instance(executor)
```

### 2. Configuration

```python
# Required configuration
preview_manager.set_base_path(Path("/path/to/project"))
preview_manager.set_executor(executor_instance)
preview_manager.set_logger(logger_instance)
preview_manager.set_platform(ApplicationType.WEB.value)
preview_manager.set_framework("react")

# Optional: Set completion callback
preview_manager.set_build_completed_callback(callback_function)
```

### 3. Running Applications

```python
# Start application preview
result = preview_manager.run_application()

# Result structure:
# {
#   'status': 'building',  # or 'running', 'failed', etc.
#   'message': 'Starting react server...'
# }
```

### 4. Checking Status

```python
# Get current status
status = preview_manager.get_preview_status()
# Returns: {'status': 'running', 'url': 'http://localhost:3000', 'timestamp': '...'}

# Get preview URL
url = preview_manager.get_preview_url()
# Returns: 'http://localhost:3000' or None
```

## API Reference

### Core Methods

#### `run_application()` → `dict`
Starts the application preview asynchronously.
- **Returns**: Status dictionary with current state and message
- **Note**: Returns immediately while build continues in background

#### `get_preview_status()` → `dict`
Retrieves current preview status from persistent storage.
- **Returns**: Dictionary with status, timestamp, and optional URL

#### `get_preview_url()` → `str | None`
Gets the current preview URL if application is running.
- **Returns**: URL string or None if not available

### Configuration Methods

#### `set_base_path(base_path: Path)`
Sets the project base directory path.

#### `set_executor(executor: ExecutorBase)`
Sets the command executor for running build commands.

#### `set_logger(logger: Logger)`
Sets the logger instance for operation tracking.

#### `set_platform(platform: ApplicationType)`
Sets the application platform (WEB, MOBILE, BACKEND).

#### `set_framework(framework: str)`
Sets the framework type (e.g., "react", "vue", "angular").

#### `set_build_completed_callback(callback)`
Sets callback function to be called when builds complete.
```python
def build_callback(preview_url: str, state: str):
    print(f"Build completed: {preview_url} with state: {state}")

preview_manager.set_build_completed_callback(build_callback)
```

## Integration with TaskExecutionAgent

The `TaskExecutionAgent` integrates PreviewManager for automatic application preview management during code generation sessions.

### Setup in TaskExecutionAgent

The preview manager is initialized in the `_setup_preview_manager` method:

```python
def _setup_preview_manager(self, work_item):
    if not self.preview_manager:
        self.preview_manager = PreviewManager()
    
    # Configure preview manager
    self.preview_manager.set_agent_tool_set(self.agent_tools.tool_set)
    self.preview_manager.set_base_path(self.base_path)
    self.preview_manager.set_executor(self.executor)
    self.preview_manager.set_logger(self.logger)
    self.preview_manager.set_platform(self.platform)
    self.preview_manager.set_framework(self.framework)
    
    # Set completion callback
    self.preview_manager.set_build_completed_callback(self._on_preview_build_completed)
```

### Automatic Preview Updates

When code changes are made through the CodeWritingAgent, the TaskExecutionAgent automatically rebuilds the preview:

```python
# After CodeWritingAgent completes
if micro_agent_name == "CodeWritingAgent":
    if self.preview_manager:
        self.logger.info("Reloading the app ...")
        status = self.preview_manager.run_application()
        if status:
            self.logger.info(f"Status: {status.get('status')}; {status.get('message')}")
```

### Build Completion Handling

The `_on_preview_build_completed` callback notifies users and updates the UI:

```python
def _on_preview_build_completed(self, preview_url, state):
    """Callback when a preview build completes"""
    self.logger.info(f"Preview build completed with URL: {preview_url} and state: {state}")
    if preview_url and self.reporter:
        message = f"Preview is now available at: {preview_url}"
        self._send_message_to_user(message, message_type=MessageType.COMMAND)
        # Trigger UI update
        self.reporter.app_state_callback(url=preview_url, state=state)
```

## Usage Patterns

### 1. Manual Preview Management

```python
# Initialize and configure
preview_manager = PreviewManager.get_instance(executor)
preview_manager.set_base_path(Path("/path/to/project"))
preview_manager.set_executor(executor)
preview_manager.set_logger(logger)
preview_manager.set_platform(ApplicationType.WEB.value)
preview_manager.set_framework("react")

# Start preview
result = preview_manager.run_application()
print(f"Preview status: {result['status']}")

# Monitor status
import time
while True:
    status = preview_manager.get_preview_status()
    if status['status'] == 'running':
        print(f"Preview available at: {status['url']}")
        break
    elif status['status'] == 'failed':
        print("Preview build failed")
        break
    time.sleep(2)
```

### 2. Integration with Custom Agents

```python
class CustomAgent:
    def __init__(self, executor, logger):
        self.preview_manager = PreviewManager.get_instance(executor)
        self.preview_manager.set_executor(executor)
        self.preview_manager.set_logger(logger)
        
    def on_code_change(self):
        """Called when code is modified"""
        result = self.preview_manager.run_application()
        if result['status'] == 'building':
            self.logger.info("Rebuilding preview...")
```

### 3. Error Handling

```python
def safe_preview_start(preview_manager):
    try:
        result = preview_manager.run_application()
        
        if result['status'] == 'failed':
            # Handle build failure
            status = preview_manager.get_preview_status()
            error = status.get('error', 'Unknown error')
            print(f"Build failed: {error}")
            
        return result
    except Exception as e:
        print(f"Preview manager error: {e}")
        return None
```

## Best Practices

1. **Always configure all required properties** before calling `run_application()`
2. **Use the singleton pattern** - don't create multiple instances
3. **Handle asynchronous nature** - builds run in background threads
4. **Monitor state changes** through status polling or callbacks
5. **Implement error handling** for build failures
6. **Clean up resources** when shutting down

## Troubleshooting

### Common Issues

1. **Port conflicts**: Check logs for port usage details
2. **Build failures**: Examine `.preview_build_status` file for error details
3. **Framework not detected**: Ensure project structure matches expected patterns
4. **Dependencies missing**: Verify npm/yarn installation in project

### Status File Format

The `.preview_build_status` file contains:

```json
{
  "status": "running|building|failed|not_started",
  "timestamp": "ISO format timestamp",
  "url": "http://localhost:3000",
  "error": "Error message if failed"
}
```

### Log Files

Server logs are written to `<framework>_server.log` containing:
- Startup messages
- Error output
- Framework-specific information
