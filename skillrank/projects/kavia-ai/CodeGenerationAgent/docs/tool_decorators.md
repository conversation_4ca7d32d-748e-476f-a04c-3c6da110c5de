# Implementing Function Validators with `register_function` and YAML

This guide explains how to use the `register_function` decorator in conjunction with YAML validation files.
## Table of Contents

1. [Overview](#overview)
2. [Basic Usage](#basic-usage)
3. [Implementing a Complete Validator](#implementing-a-complete-validator)
   - [YAML Structure](#yaml-structure)
   - [Checks Implementation](#checks-implementation)
   - [Function-Specific Validations](#function-specific-validations)
4. [Integration with `register_function`](#integration-with-register_function)
5. [Example: shell_tools.yaml](#example-shell_toolsyaml)
6. [Best Practices](#best-practices)

## Overview

The `register_function` decorator, combined with YAML-based validators, provides a way to:

1. Define reusable validation rules.
2. Implement complex, function-specific validations.
3. Separate validation logic from function implementation.

## Basic Usage

Here's a short example for of how to use the `register_function` decorator with a YAML validator `shell_tools.yaml`:

```python
@register_function(
    description="Execute a shell command",
    parameters={
        "type": "object",
        "properties": {
            "command": {"type": "string"},
            "working_directory": {"type": "string"}
        },
        "required": ["command", "working_directory"]
    },
    validator="shell_tools.yaml"
)
def execute_command(command, working_directory):
    # Function implementation
    pass
```



## Implementing a Complete Validator

To implement a complete validator like `shell_tools.yaml`, you need to create a YAML file with specific structures and checks.

### YAML Structure

A typical validator YAML file includes:

1. Common checks that can be reused across functions
2. Function-specific validations

### Checks Implementation

Define reusable checks in your YAML file include

```yaml
checks:
  - name: regex
    code: |
      result = re.match(pattern, str(value)) is not None
    error_message: "Argument '{arg_name}' with value '{value}' does not match pattern '{pattern}'"

  - name: range
    code: |
      min_val, max_val = min, max
      result = min_val <= float(value) <= max_val
    error_message: "Argument '{arg_name}' with value {value} is not in range [{min}, {max}]"

  - name: length
    code: |
      min_length, max_length = min, max
      result = min_length <= len(str(value)) <= max_length
    error_message: "Argument '{arg_name}' length must be between {min} and {max}"
```

Each check includes:
- `name`: A unique identifier for the check
- `code`: Python code to perform the validation
- `error_message`: A template for the error message if validation fails

### Function-Specific Validations

Define validations for specific functions:

```yaml
functions:
  execute_command:
    command:
      - name: length
        min: 1
        max: 3000
    working_directory:
      - name: directory_exists
        error_message: "Working directory '{value}' does not exist"
```

This structure allows you to specify which checks should be applied to each parameter of a function.

## How validation worsk

When you use `validator="shell_tools.yaml"` in the `register_function` decorator, the system will:

1. Load the YAML file
2. Extract the relevant checks for the function
3. Perform these checks before the LLM is calling the function and provide a feedback to the LLM if the validation fails.

## Example: shell_tools.yaml

Here's a complete example of what `shell_tools.yaml` might look like:

```yaml
include:
  - common.yaml

checks:
  - name: directory_not_equal
    code: |
      def are_directories_different(dir1, dir2):
          import os
          import filecmp
      
          if not os.path.isdir(dir1) or not os.path.isdir(dir2):
              return True  # If either path is not a directory, consider them different
          
          return not filecmp.dircmp(dir1, dir2).same_files
      
      result = are_directories_different(value, compare_value)
    error_message: "Directory '{arg_name}' with value '{value}' must be different from base directory '{compare_value}'"

functions:
  start_long_running_process:
    command:
      - name: length
        min: 1
        max: 3000
    working_directory:
      - name: directory_not_equal
        compare_value: '{base_path}'
```

Note that variables like  `start_long_running_process` and `working_directory` can be accessed directly from the validation functions.

This example:
1. Includes checks from a `common.yaml` file
2. Defines a custom `directory_not_equal` check
3. Specifies validations for the `start_long_running_process` function

## Best Practices

1. Use `include` to import common checks from shared YAML files
2. Keep YAML files organized by grouping related functions and checks
