Session: 09.05.2025
Participants: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>





Session: 07.05.2025
Participants: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
Updated files: 

[port allocation]: container_architecture/port_allocation.md

<u>Outstanding issues:</u>

* How to deal with code generator restarting or starting multiple instances? For example, visual verification can start an another `node` instance on port 3001.
* Multiple code-maintenance sessions create a port conflict. Potentially can be resolved with multiple docker networks.
  We may need to change the port mapping on the main container (Du<PERSON>lo instance).



<u>Requirements for application build completed/failure:</u>

**Requirements**

* Build completed callback should be implemented in the `CodeGenerationReporter` .
* Callback: **build succeeded**, **build in progress**, **build failed** (with a reason)

