This document summarizes the the port allocations for Duplo Cloud deployment



TCP port usage

| Port (TCP)  | Description        | Exposed by                                                   |
| :---------- | ------------------ | ------------------------------------------------------------ |
| 8080        | VS Code port       | Code Generation Dev Container used for Code Generation       |
| 8085 - 8089 | VS Code port       | Code Generation Dev Container used for parallel code maintenance sessions |
| 3000        | Application port   | Code Generation Dev Container used for Code Generation. Set by universal init. |
| 3001 - 3004 | Backend services   | Code Generation Dev Container, reserved for backend services |
| 3005 - 3009 | Application port   | Code Generation Dev Container used for parallel code maintenance sessions |
| 8088        | Tiny proxy         | Code Generation Dev Container, used with Web Tool (potentially deprecated) |
| 5900        | VNC                | Code Generation Dev Container (**Currently broken in Duplo**) |
| 8089        | VNC web interface  | Code Generation Dev Container (**Currently broken in Duplo**) |
| 5002        | Chat callback port | Mapped to 5001 port in the Code Generation Dev Container. Used for passing back build errors from `shelly` |



Example usage:

![alt text](duplo_ports.png "Duplo port usage")



Outstanding issues:

* How to deal with code generator restarting or starting multiple instances? For example, visual verification can start an another `node` instance on port 3001.
* Multiple code-maintainance sessions create a port conflict. Potentially can be resolved with multiple docker networks.
  We may need to change the port mapping on the main container (duplo instance).