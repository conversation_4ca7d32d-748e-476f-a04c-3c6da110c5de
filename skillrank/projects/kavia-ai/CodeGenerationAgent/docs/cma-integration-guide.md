# Integration Guide: CodeMaintenanceAgent with Git Projects

This guide covers the essential components needed to integrate the CodeMaintenanceAgent 

## 1. Required Classes

Create a new classe

```python
class CMProcessWorker(QObject):
    finished_signal = pyqtSignal()
    progress_signal = pyqtSignal(str)

    def __init__(self, agent, agent_type, control, previous_context, reporter, chat, git_links):
        super().__init__()
        # Store parameters as instance variables

```

## 2. Configuration Values

The CodeMaintenanceAgent requires the following configuration:

```python
# Agent Configuration
agent_type = "CodeMaintenance"
config_file = "src/code_generation_core_agent/agents/micro/cm_micro_agents.yaml"
prompts_path = "src/code_generation_core_agent/agents/micro/prompts"
model_name = "your_model_name"  # e.g., "gpt-4" or your configured model

# Required Environment Variables
required_env_vars = [
    "OPENAI_API_KEY",
    "ANTHROPIC_API_KEY"
]
```

## 3. Initialization Parameters

When initializing the agent, provide:

```python
agent = CodeMaintenanceAgent(
    llm=your_llm_instance,
    execution_base_path=session_directory,
    micro_agents_config=yaml.safe_load(open(config_file)),
    model_name=model_name,
    prompts_base_path=prompts_path
)

worker = CMProcessWorker(
    agent=agent,
    agent_type="CodeMaintenance",
    control=your_control_instance,
    previous_context=None,  # or existing context
    reporter=your_reporter_instance,
    chat=your_chat_interface,
    git_links=list_of_repository_urls
)
```


## 5. Key Differences from Other Agents

- Uses different configuration file (`cm_micro_agents.yaml`)
- Does not require a work item configuration
- Chat interface should be initialized with `disable_task_creation=True`
- Supports multiple GitHub repository URLs
- Uses a dedicated worker class for maintenance tasks

## 6. Git Project Integration

### Project URL Format
Git projects should be passed as fully qualified HTTPS URLs:
```python
git_links = [
    "https://github.com/username/repository",
    "https://github.com/username/another-repository"
]
```

### Passing to the Agent
Projects can be passed like this:

1. During worker initialization:
```python
worker = CMProcessWorker(
    # ... other parameters ...
    git_links=git_links
)
```


### Authentication
- For public repositories: No additional configuration needed
- For private repositories: Set GITHUB_ACCESS_TOKEN in your environment variables

