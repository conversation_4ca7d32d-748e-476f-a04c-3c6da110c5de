{"cells": [{"cell_type": "markdown", "id": "baa19972-0fd3-4d70-90f7-e1e021d8a2de", "metadata": {}, "source": ["# Project documentation"]}, {"cell_type": "markdown", "id": "7bf2a471-aa9c-4fcc-a96c-0e3ce57b8bb8", "metadata": {}, "source": "- [Multi project setup](../multi_project.ipynb) - Multi project setup and configuration\n"}, {"cell_type": "code", "execution_count": null, "id": "3a218570-4fa8-4c8d-995e-4bb07c24d83d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}