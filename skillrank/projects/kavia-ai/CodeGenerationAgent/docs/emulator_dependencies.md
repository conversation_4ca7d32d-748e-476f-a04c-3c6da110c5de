# Emulator Dependencies Installation Guide

> A comprehensive guide for installing and configuring emulator dependencies on Debian/Ubuntu-based systems.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Dependencies](#dependencies)
- [Installation](#installation)
- [Post-Installation](#post-installation)

## Prerequisites

Before you begin, ensure you have:

- Debian/Ubuntu-based Linux distribution
- Root/sudo privileges
- Terminal access

## Dependencies

### Core Virtualization Components

Component | Description
----------|------------
`qemu-kvm` | QEMU/KVM virtualization support
`libvirt-daemon-system` | Virtualization daemon
`bridge-utils` | Ethernet bridge administration
`virtinst` | Virtual machine installation utilities
`libvirt-clients` | Client-side libraries for libvirt

### X11 and XCB Libraries

Required for GUI functionality:

#### Basic X11 libraries
```
libxcursor1    libxdamage1    libxfixes3
libxi6         libxrandr2     libxrender1
libxss1        libxtst6
```

#### XCB Libraries
```
libxcb1              libxcb-cursor0    libxcb-icccm4
libxcb-image0        libxcb-keysyms1   libxcb-randr0
libxcb-render-util0  libxcb-render0    libxcb-shape0
libxcb-shm0         libxcb-sync1      libxcb-xfixes0
libxcb-xinerama0    libxcb-xkb1
```

### Qt Development Libraries
- `qtbase5-dev`: Qt 5 base development files
- `qttools5-dev`: Qt 5 development tools

## Installation

### 1. Create Installation Script

Create a new file named `install-emulator-deps.sh`:

```bash
#!/bin/bash

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo "This script must be run as root (use sudo)"
    exit 1
fi

# Update repositories
apt-get update

# Install emulator dependencies
apt-get install -y \
    qemu-kvm \
    libvirt-daemon-system \
    bridge-utils \
    virtinst \
    libvirt-clients \
    pulseaudio \
    libasound2:amd64 \
    libxcursor1 \
    libxdamage1 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    libxcb1 \
    libxcb-cursor0 \
    libxcb-icccm4 \
    libxcb-image0 \
    libxcb-keysyms1 \
    libxcb-randr0 \
    libxcb-render-util0 \
    libxcb-render0 \
    libxcb-shape0 \
    libxcb-shm0 \
    libxcb-sync1 \
    libxcb-xfixes0 \
    libxcb-xinerama0 \
    libxcb-xkb1 \
    x11-utils \
    qtbase5-dev \
    qttools5-dev

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "All dependencies installed successfully"
else
    echo "An error occurred during installation"
    exit 1
fi

# Check KVM availability
if [ -c /dev/kvm ]; then
    echo "KVM is available on the system"
else
    echo "WARNING: KVM is not available. Verify that virtualization is enabled in BIOS"
fi

# Add current user to libvirt and kvm groups
current_user=$(who am i | awk '{print $1}')
usermod -aG libvirt $current_user
usermod -aG kvm $current_user

echo "User $current_user has been added to libvirt and kvm groups"
echo "Please log out and log back in for changes to take effect"
```

### 2. Make Script Executable

```bash
chmod +x install-emulator-deps.sh
```

### 3. Run Installation Script

```bash
sudo ./install-emulator-deps.sh
```

## Post-Installation

### Verify KVM Installation

```bash
kvm-ok
```

Expected output:
```
INFO: /dev/kvm exists
KVM acceleration can be used
```

### Check Libvirt Service

```bash
systemctl status libvirtd
```

### Verify Group Membership

```bash
groups $USER
```

Look for `libvirt` and `kvm` in the output.

## Troubleshooting

### KVM Not Available

1. Enter BIOS/UEFI settings
2. Enable virtualization:
   - Intel: Enable VT-x
   - AMD: Enable AMD-V
3. Save and reboot

### Libvirt Service Issues

```bash
sudo systemctl restart libvirtd
sudo systemctl enable libvirtd
```
