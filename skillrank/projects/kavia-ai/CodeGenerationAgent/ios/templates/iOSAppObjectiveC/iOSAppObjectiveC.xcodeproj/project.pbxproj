// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		64F3BDAD2D286E6A00559317 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 64F3BD892D286E6900559317 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 64F3BD902D286E6900559317;
			remoteInfo = iOSAppObjectiveC;
		};
		64F3BDB72D286E6A00559317 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 64F3BD892D286E6900559317 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 64F3BD902D286E6900559317;
			remoteInfo = iOSAppObjectiveC;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		64F3BD912D286E6900559317 /* iOSAppObjectiveC.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iOSAppObjectiveC.app; sourceTree = BUILT_PRODUCTS_DIR; };
		64F3BDAC2D286E6A00559317 /* iOSAppObjectiveCTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSAppObjectiveCTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		64F3BDB62D286E6A00559317 /* iOSAppObjectiveCUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSAppObjectiveCUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		64F3BDBE2D286E6A00559317 /* Exceptions for "iOSAppObjectiveC" folder in "iOSAppObjectiveC" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 64F3BD902D286E6900559317 /* iOSAppObjectiveC */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		64F3BD932D286E6900559317 /* iOSAppObjectiveC */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				64F3BDBE2D286E6A00559317 /* Exceptions for "iOSAppObjectiveC" folder in "iOSAppObjectiveC" target */,
			);
			path = iOSAppObjectiveC;
			sourceTree = "<group>";
		};
		64F3BDAF2D286E6A00559317 /* iOSAppObjectiveCTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSAppObjectiveCTests;
			sourceTree = "<group>";
		};
		64F3BDB92D286E6A00559317 /* iOSAppObjectiveCUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSAppObjectiveCUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		64F3BD8E2D286E6900559317 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BDA92D286E6A00559317 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BDB32D286E6A00559317 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		64F3BD882D286E6900559317 = {
			isa = PBXGroup;
			children = (
				64F3BD932D286E6900559317 /* iOSAppObjectiveC */,
				64F3BDAF2D286E6A00559317 /* iOSAppObjectiveCTests */,
				64F3BDB92D286E6A00559317 /* iOSAppObjectiveCUITests */,
				64F3BD922D286E6900559317 /* Products */,
			);
			sourceTree = "<group>";
		};
		64F3BD922D286E6900559317 /* Products */ = {
			isa = PBXGroup;
			children = (
				64F3BD912D286E6900559317 /* iOSAppObjectiveC.app */,
				64F3BDAC2D286E6A00559317 /* iOSAppObjectiveCTests.xctest */,
				64F3BDB62D286E6A00559317 /* iOSAppObjectiveCUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		64F3BD902D286E6900559317 /* iOSAppObjectiveC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64F3BDBF2D286E6A00559317 /* Build configuration list for PBXNativeTarget "iOSAppObjectiveC" */;
			buildPhases = (
				64F3BD8D2D286E6900559317 /* Sources */,
				64F3BD8E2D286E6900559317 /* Frameworks */,
				64F3BD8F2D286E6900559317 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				64F3BD932D286E6900559317 /* iOSAppObjectiveC */,
			);
			name = iOSAppObjectiveC;
			packageProductDependencies = (
			);
			productName = iOSAppObjectiveC;
			productReference = 64F3BD912D286E6900559317 /* iOSAppObjectiveC.app */;
			productType = "com.apple.product-type.application";
		};
		64F3BDAB2D286E6A00559317 /* iOSAppObjectiveCTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64F3BDC42D286E6A00559317 /* Build configuration list for PBXNativeTarget "iOSAppObjectiveCTests" */;
			buildPhases = (
				64F3BDA82D286E6A00559317 /* Sources */,
				64F3BDA92D286E6A00559317 /* Frameworks */,
				64F3BDAA2D286E6A00559317 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				64F3BDAE2D286E6A00559317 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				64F3BDAF2D286E6A00559317 /* iOSAppObjectiveCTests */,
			);
			name = iOSAppObjectiveCTests;
			packageProductDependencies = (
			);
			productName = iOSAppObjectiveCTests;
			productReference = 64F3BDAC2D286E6A00559317 /* iOSAppObjectiveCTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		64F3BDB52D286E6A00559317 /* iOSAppObjectiveCUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64F3BDC72D286E6A00559317 /* Build configuration list for PBXNativeTarget "iOSAppObjectiveCUITests" */;
			buildPhases = (
				64F3BDB22D286E6A00559317 /* Sources */,
				64F3BDB32D286E6A00559317 /* Frameworks */,
				64F3BDB42D286E6A00559317 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				64F3BDB82D286E6A00559317 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				64F3BDB92D286E6A00559317 /* iOSAppObjectiveCUITests */,
			);
			name = iOSAppObjectiveCUITests;
			packageProductDependencies = (
			);
			productName = iOSAppObjectiveCUITests;
			productReference = 64F3BDB62D286E6A00559317 /* iOSAppObjectiveCUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		64F3BD892D286E6900559317 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					64F3BD902D286E6900559317 = {
						CreatedOnToolsVersion = 16.2;
					};
					64F3BDAB2D286E6A00559317 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 64F3BD902D286E6900559317;
					};
					64F3BDB52D286E6A00559317 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 64F3BD902D286E6900559317;
					};
				};
			};
			buildConfigurationList = 64F3BD8C2D286E6900559317 /* Build configuration list for PBXProject "iOSAppObjectiveC" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 64F3BD882D286E6900559317;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 64F3BD922D286E6900559317 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				64F3BD902D286E6900559317 /* iOSAppObjectiveC */,
				64F3BDAB2D286E6A00559317 /* iOSAppObjectiveCTests */,
				64F3BDB52D286E6A00559317 /* iOSAppObjectiveCUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		64F3BD8F2D286E6900559317 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BDAA2D286E6A00559317 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BDB42D286E6A00559317 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		64F3BD8D2D286E6900559317 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BDA82D286E6A00559317 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BDB22D286E6A00559317 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		64F3BDAE2D286E6A00559317 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 64F3BD902D286E6900559317 /* iOSAppObjectiveC */;
			targetProxy = 64F3BDAD2D286E6A00559317 /* PBXContainerItemProxy */;
		};
		64F3BDB82D286E6A00559317 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 64F3BD902D286E6900559317 /* iOSAppObjectiveC */;
			targetProxy = 64F3BDB72D286E6A00559317 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		64F3BDC02D286E6A00559317 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iOSAppObjectiveC/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppObjectiveC;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		64F3BDC12D286E6A00559317 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iOSAppObjectiveC/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppObjectiveC;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		64F3BDC22D286E6A00559317 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		64F3BDC32D286E6A00559317 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		64F3BDC52D286E6A00559317 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppObjectiveCTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOSAppObjectiveC.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOSAppObjectiveC";
			};
			name = Debug;
		};
		64F3BDC62D286E6A00559317 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppObjectiveCTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOSAppObjectiveC.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOSAppObjectiveC";
			};
			name = Release;
		};
		64F3BDC82D286E6A00559317 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppObjectiveCUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = iOSAppObjectiveC;
			};
			name = Debug;
		};
		64F3BDC92D286E6A00559317 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppObjectiveCUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = iOSAppObjectiveC;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		64F3BD8C2D286E6900559317 /* Build configuration list for PBXProject "iOSAppObjectiveC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64F3BDC22D286E6A00559317 /* Debug */,
				64F3BDC32D286E6A00559317 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64F3BDBF2D286E6A00559317 /* Build configuration list for PBXNativeTarget "iOSAppObjectiveC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64F3BDC02D286E6A00559317 /* Debug */,
				64F3BDC12D286E6A00559317 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64F3BDC42D286E6A00559317 /* Build configuration list for PBXNativeTarget "iOSAppObjectiveCTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64F3BDC52D286E6A00559317 /* Debug */,
				64F3BDC62D286E6A00559317 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64F3BDC72D286E6A00559317 /* Build configuration list for PBXNativeTarget "iOSAppObjectiveCUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64F3BDC82D286E6A00559317 /* Debug */,
				64F3BDC92D286E6A00559317 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 64F3BD892D286E6900559317 /* Project object */;
}
