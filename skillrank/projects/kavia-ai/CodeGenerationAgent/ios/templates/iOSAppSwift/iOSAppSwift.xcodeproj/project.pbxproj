// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		64F3BD6C2D286D8B00559317 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 64F3BD532D286D8A00559317 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 64F3BD5A2D286D8A00559317;
			remoteInfo = iOSAppSwift;
		};
		64F3BD762D286D8B00559317 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 64F3BD532D286D8A00559317 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 64F3BD5A2D286D8A00559317;
			remoteInfo = iOSAppSwift;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		64F3BD5B2D286D8A00559317 /* iOSAppSwift.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iOSAppSwift.app; sourceTree = BUILT_PRODUCTS_DIR; };
		64F3BD6B2D286D8B00559317 /* iOSAppSwiftTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSAppSwiftTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		64F3BD752D286D8B00559317 /* iOSAppSwiftUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSAppSwiftUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		64F3BD5D2D286D8A00559317 /* iOSAppSwift */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSAppSwift;
			sourceTree = "<group>";
		};
		64F3BD6E2D286D8B00559317 /* iOSAppSwiftTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSAppSwiftTests;
			sourceTree = "<group>";
		};
		64F3BD782D286D8B00559317 /* iOSAppSwiftUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSAppSwiftUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		64F3BD582D286D8A00559317 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BD682D286D8B00559317 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BD722D286D8B00559317 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		64F3BD522D286D8A00559317 = {
			isa = PBXGroup;
			children = (
				64F3BD5D2D286D8A00559317 /* iOSAppSwift */,
				64F3BD6E2D286D8B00559317 /* iOSAppSwiftTests */,
				64F3BD782D286D8B00559317 /* iOSAppSwiftUITests */,
				64F3BD5C2D286D8A00559317 /* Products */,
			);
			sourceTree = "<group>";
		};
		64F3BD5C2D286D8A00559317 /* Products */ = {
			isa = PBXGroup;
			children = (
				64F3BD5B2D286D8A00559317 /* iOSAppSwift.app */,
				64F3BD6B2D286D8B00559317 /* iOSAppSwiftTests.xctest */,
				64F3BD752D286D8B00559317 /* iOSAppSwiftUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		64F3BD5A2D286D8A00559317 /* iOSAppSwift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64F3BD7F2D286D8B00559317 /* Build configuration list for PBXNativeTarget "iOSAppSwift" */;
			buildPhases = (
				64F3BD572D286D8A00559317 /* Sources */,
				64F3BD582D286D8A00559317 /* Frameworks */,
				64F3BD592D286D8A00559317 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				64F3BD5D2D286D8A00559317 /* iOSAppSwift */,
			);
			name = iOSAppSwift;
			packageProductDependencies = (
			);
			productName = iOSAppSwift;
			productReference = 64F3BD5B2D286D8A00559317 /* iOSAppSwift.app */;
			productType = "com.apple.product-type.application";
		};
		64F3BD6A2D286D8B00559317 /* iOSAppSwiftTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64F3BD822D286D8B00559317 /* Build configuration list for PBXNativeTarget "iOSAppSwiftTests" */;
			buildPhases = (
				64F3BD672D286D8B00559317 /* Sources */,
				64F3BD682D286D8B00559317 /* Frameworks */,
				64F3BD692D286D8B00559317 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				64F3BD6D2D286D8B00559317 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				64F3BD6E2D286D8B00559317 /* iOSAppSwiftTests */,
			);
			name = iOSAppSwiftTests;
			packageProductDependencies = (
			);
			productName = iOSAppSwiftTests;
			productReference = 64F3BD6B2D286D8B00559317 /* iOSAppSwiftTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		64F3BD742D286D8B00559317 /* iOSAppSwiftUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64F3BD852D286D8B00559317 /* Build configuration list for PBXNativeTarget "iOSAppSwiftUITests" */;
			buildPhases = (
				64F3BD712D286D8B00559317 /* Sources */,
				64F3BD722D286D8B00559317 /* Frameworks */,
				64F3BD732D286D8B00559317 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				64F3BD772D286D8B00559317 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				64F3BD782D286D8B00559317 /* iOSAppSwiftUITests */,
			);
			name = iOSAppSwiftUITests;
			packageProductDependencies = (
			);
			productName = iOSAppSwiftUITests;
			productReference = 64F3BD752D286D8B00559317 /* iOSAppSwiftUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		64F3BD532D286D8A00559317 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					64F3BD5A2D286D8A00559317 = {
						CreatedOnToolsVersion = 16.2;
					};
					64F3BD6A2D286D8B00559317 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 64F3BD5A2D286D8A00559317;
					};
					64F3BD742D286D8B00559317 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 64F3BD5A2D286D8A00559317;
					};
				};
			};
			buildConfigurationList = 64F3BD562D286D8A00559317 /* Build configuration list for PBXProject "iOSAppSwift" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 64F3BD522D286D8A00559317;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 64F3BD5C2D286D8A00559317 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				64F3BD5A2D286D8A00559317 /* iOSAppSwift */,
				64F3BD6A2D286D8B00559317 /* iOSAppSwiftTests */,
				64F3BD742D286D8B00559317 /* iOSAppSwiftUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		64F3BD592D286D8A00559317 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BD692D286D8B00559317 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BD732D286D8B00559317 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		64F3BD572D286D8A00559317 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BD672D286D8B00559317 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64F3BD712D286D8B00559317 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		64F3BD6D2D286D8B00559317 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 64F3BD5A2D286D8A00559317 /* iOSAppSwift */;
			targetProxy = 64F3BD6C2D286D8B00559317 /* PBXContainerItemProxy */;
		};
		64F3BD772D286D8B00559317 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 64F3BD5A2D286D8A00559317 /* iOSAppSwift */;
			targetProxy = 64F3BD762D286D8B00559317 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		64F3BD7D2D286D8B00559317 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		64F3BD7E2D286D8B00559317 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		64F3BD802D286D8B00559317 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"iOSAppSwift/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppSwift;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		64F3BD812D286D8B00559317 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"iOSAppSwift/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppSwift;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		64F3BD832D286D8B00559317 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppSwiftTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOSAppSwift.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOSAppSwift";
			};
			name = Debug;
		};
		64F3BD842D286D8B00559317 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppSwiftTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOSAppSwift.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOSAppSwift";
			};
			name = Release;
		};
		64F3BD862D286D8B00559317 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppSwiftUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = iOSAppSwift;
			};
			name = Debug;
		};
		64F3BD872D286D8B00559317 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.apple.iOSAppSwiftUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = iOSAppSwift;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		64F3BD562D286D8A00559317 /* Build configuration list for PBXProject "iOSAppSwift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64F3BD7D2D286D8B00559317 /* Debug */,
				64F3BD7E2D286D8B00559317 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64F3BD7F2D286D8B00559317 /* Build configuration list for PBXNativeTarget "iOSAppSwift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64F3BD802D286D8B00559317 /* Debug */,
				64F3BD812D286D8B00559317 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64F3BD822D286D8B00559317 /* Build configuration list for PBXNativeTarget "iOSAppSwiftTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64F3BD832D286D8B00559317 /* Debug */,
				64F3BD842D286D8B00559317 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64F3BD852D286D8B00559317 /* Build configuration list for PBXNativeTarget "iOSAppSwiftUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64F3BD862D286D8B00559317 /* Debug */,
				64F3BD872D286D8B00559317 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 64F3BD532D286D8A00559317 /* Project object */;
}
