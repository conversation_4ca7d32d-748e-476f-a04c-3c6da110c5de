APP_NAME?=iOSAppSwift
SIMULATOR_NAME?="iPhone 16 Pro"
BUILD_DERIVED_PATH?=build
APP_DIR?=$(APP_NAME)
APP_PATH=$(shell find ./$(BUILD_DERIVED_PATH) -name "$(APP_NAME).app" -type d | grep -i "Debug-iphonesimulator" | head -n 1)
SIMULATOR_ID:=$(shell xcrun simctl list devices available | grep "Booted" | tail -1 | sed -E 's/.*\(([A-Z0-9-]+)\).*/\1/')

get-app-name:
	@echo $(APP_NAME)

get-app-dir:
	@echo $(APP_DIR)

get-app-path:
	@echo $(shell find ./$(BUILD_DERIVED_PATH) -name "$(APP_NAME).app" -type d | grep -i "Debug-iphonesimulator" | head -n 1)

get-simulator-id:
	@echo $(shell xcrun simctl list devices available | grep "Booted" | tail -1 | sed -E 's/.*\(([A-Z0-9-]+)\).*/\1/')

check-env:
	@set -E
	@echo Application name: $(APP_NAME)
	@echo Project root directory: $(PROJ_ROOT_DIR)
	@echo Application directory: $(APP_DIR)
	@echo Build derived path: $(BUILD_DERIVED_PATH)
	@echo Application path: $(APP_PATH)
	@echo Simulator id: $(SIMULATOR_ID)

clean:
	@set -e
	xcodebuild clean -project $(APP_DIR)/$(APP_NAME).xcodeproj -scheme $(APP_NAME)

build:
	@set -e
	@make clean
	@xcodebuild \
		-project $(APP_DIR)/$(APP_NAME).xcodeproj \
		-scheme $(APP_NAME) \
		-configuration Debug \
		-sdk iphonesimulator \
		-derivedDataPath ./$(BUILD_DERIVED_PATH) \
		CODE_SIGN_IDENTITY="" \
		CODE_SIGNING_REQUIRED=NO \
		CODE_SIGNING_ALLOWED=NO
	exit 0

test:
	@set -e
	@xcodebuild test \
		-project $(APP_DIR)/$(APP_NAME).xcodeproj \
		-scheme $(APP_NAME) \
		-sdk iphonesimulator \
		-destination 'platform=iOS Simulator,name=$(SIMULATOR_NAME),OS=latest' \
		CODE_SIGN_IDENTITY="" \
		CODE_SIGNING_REQUIRED=NO \
		CODE_SIGNING_ALLOWED=NO
	exit 0

start-simulator:
	@set -e
	@open -a Simulator
	exit 0

check-for-simulator:
	@if [ -z "$(SIMULATOR_ID)" ]; then \
		echo "No booted simulator found. Trying to boot the simulator first. Please re-run the command."; \
		make start-simulator; \
		exit -1; \
    fi

launch:
	@set -e
	@make check-for-simulator
	@xcrun simctl terminate $(SIMULATOR_ID) \
	"$(shell xcrun simctl listapps "$(SIMULATOR_ID)" | grep $(APP_NAME) | grep CFBundleIdentifier | sed -n 's/.*CFBundleIdentifier = "\(.*\)";.*/\1/p')" 2>/dev/null || true
	@xcrun simctl launch "$(SIMULATOR_ID)" \
	"$(shell xcrun simctl listapps "$(SIMULATOR_ID)" | grep $(APP_NAME) | grep CFBundleIdentifier | sed -n 's/.*CFBundleIdentifier = "\(.*\)";.*/\1/p')"
	@open -a Simulator
	exit 0

run:
	@set -e
	@make check-for-simulator
	@xcrun simctl boot "$(SIMULATOR_ID)" 2>/dev/null || true
	@counter=0; \
	while ! xcrun simctl list | grep "$(SIMULATOR_ID)" | grep "Booted" > /dev/null; do \
		sleep 1; \
		counter=$$((counter + 1)); \
		if [ $$counter -gt 10 ]; then \
			echo "Simulator did not boot within 10 seconds. Exiting."; \
			exit -1; \
		fi; \
	done;
	@if [ -z "$(APP_PATH)" ]; then \
		echo "App not found! Please build it first."; \
		exit -1; \
	fi;
	@xcrun simctl install "$(SIMULATOR_ID)" "$(APP_PATH)";
	@make launch
	exit 0

take-screenshot:
	@set -e
	@make check-for-simulator
	@xcrun simctl io "$(SIMULATOR_ID)" screenshot ./screenshot.png
	@echo "Screenshot saved to ./screenshot.png"
	exit 0
