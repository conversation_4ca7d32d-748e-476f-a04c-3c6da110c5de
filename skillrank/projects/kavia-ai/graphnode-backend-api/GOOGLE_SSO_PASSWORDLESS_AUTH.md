# Google SSO Password-less Authentication

## Overview

This implementation provides password-less authentication for Google SSO users, eliminating the need to reset user passwords during the sign-in process. Instead of using Cognito's password-based authentication flows, it creates custom JWT tokens that maintain compatibility with your existing authentication system.

## Key Benefits

1. **No Password Resets**: Google SSO users no longer have their passwords reset during authentication
2. **Improved Security**: Reduces unnecessary password operations for OAuth users
3. **Better User Experience**: Seamless sign-in without password-based authentication flows
4. **Backward Compatibility**: Falls back to password-based authentication if needed
5. **Configurable**: Can be enabled/disabled via environment variables
6. **Password Preservation**: Existing user passwords remain intact for dual authentication methods

## Configuration

Add this environment variable to enable/disable password-less authentication:

```bash
# Enable password-less Google authentication (default: true)
ENABLE_PASSWORDLESS_GOOGLE_AUTH=true
```

## How It Works

### 1. Authentication Flow

When a Google SSO user signs in:

1. **Check Configuration**: System checks if `ENABLE_PASSWORDLESS_GOOGLE_AUTH` is enabled
2. **User Validation**: Checks if user exists in Cognito without modifying their password
3. **Password-less Authentication**: If enabled, creates custom JWT tokens without password operations
4. **Fallback**: If password-less fails or is disabled, falls back to the original password-based method
5. **Dual Authentication**: Users can still use their original passwords for direct login

### 2. Custom JWT Token Creation

The system creates custom JWT tokens that:
- Use the signing key `"kavia-google-sso-secret"`
- Include all necessary user attributes (is_admin, is_free_user, tenant_id, etc.)
- Have proper expiration times (24 hours for access/ID tokens, 30 days for refresh tokens)
- Maintain compatibility with existing token verification systems
- Include `auth_provider: "google_sso"` for identification

### 3. Token Structure

```json 
{
  "sub": "user-cognito-id",
  "email": "<EMAIL>",
  "cognito:username": "<EMAIL>",
  "iss": "kavia-google-sso",
  "aud": "cognito-client-id",
  "iat": **********,
  "exp": **********,
  "custom:is_admin": "true",
  "custom:free_user": "true",
  "custom:tenant_id": "tenant-id",
  "auth_provider": "google_sso",
  "email_verified": true,
  "token_use": "id"
}
```

## Implementation Details

### Files Modified

1. **`app/routes/authentication_route.py`**:
   - Added `authenticate_google_user_without_password()` function 
   - Modified `google_callback()` to use password-less authentication
   - Modified `google_signup()` to use password-less authentication for existing users
   - Added configuration check for `ENABLE_PASSWORDLESS_GOOGLE_AUTH`
   - Fixed undefined `user_attributes` variable bug
   - Removed password reset operations from Google SSO flows

2. **`app/utils/auth_utils.py`** and **`app/utils/auth_userpool_utils.py`**:
   - Updated token verification to handle Google SSO tokens signed with `"kavia-google-sso-secret"`

### Core Functions

#### `authenticate_google_user_without_password(user_info, tenant_id, client, creds)`
Authenticates Google users without password operations by creating custom tokens.

**Parameters:**
- `user_info`: Dict containing Google user information (email, name, picture)
- `tenant_id`: String tenant identifier
- `client`: Boto3 Cognito client instance
- `creds`: Dict with Cognito credentials (user_pool_id, client_id)

**Returns:**
- Dict with authentication response data compatible with existing frontend expectations

**Features:**
- Extracts user attributes from Cognito without password operations
- Creates JWT tokens using `"kavia-google-sso-secret"` signing key
- Enables disabled users without resetting passwords
- Includes `auth_method: "google_sso_passwordless"` in response

#### Token Verification Updates

The token verification functions now handle both:
- Standard Cognito tokens
- Google SSO tokens signed with `"kavia-google-sso-secret"`

## Password Operations Removed

The following password operations were removed from Google SSO flows:

1. **Line ~925-933**: Removed `admin_set_user_password` from Google callback signup
2. **Google signup function**: Removed password setting for existing users
3. **Replaced with**: JWT token creation using `"kavia-google-sso-secret"`

### Legitimate Password Operations Remaining

Only these password operations remain (which are correct):
- **Line 370**: `/change_password` endpoint (user-initiated password changes)
- **Line 1445**: New user creation in `google_signup` function (only for genuinely new users)

## Usage Examples

### Environment Configuration

```bash
# Enable password-less authentication (recommended)
ENABLE_PASSWORDLESS_GOOGLE_AUTH=true

# Disable password-less authentication (falls back to password method)
ENABLE_PASSWORDLESS_GOOGLE_AUTH=false
```

### Frontend Integration

The response structure remains the same, with additional fields for identification:

```json
{
  "authenticated": true,
  "action": "signin",
  "message": "User signed in successfully with Google SSO (password preserved)",
  "provider": "google",
  "user": {
    "email": "<EMAIL>",
    "name": "User Name",
    "picture": "profile-picture-url",
    "userId": "cognito-user-id",
    "username": "User Name"
  },
  "userId": "cognito-user-id",
  "username": "User Name", 
  "id_token": "jwt-token",
  "refresh_token": "refresh-token",
  "access_token": "jwt-token",
  "tenant_id": "tenant-id",
  "organization_id": "org-id",
  "is_admin": true,
  "free_user": true,
  "is_super_admin": false,
  "ExpiresIn": 86400,
  "TokenType": "Bearer",
  "auth_method": "google_sso_passwordless"
}
```

## Security Considerations

1. **Token Signing**: Uses `"kavia-google-sso-secret"` as signing key. Consider using environment variables for production
2. **Token Verification**: Updated verification systems handle both Cognito and Google SSO tokens
3. **User Validation**: System validates user exists in Cognito before creating tokens
4. **Fallback Safety**: Always falls back to password-based authentication if password-less fails
5. **Password Preservation**: Original user passwords remain intact for dual authentication

## Bug Fixes Applied

### 1. Undefined `user_attributes` Variable
**Problem**: Line 1118 referenced undefined `user_attributes` variable
**Solution**: Replaced with `user_details.get('UserAttributes', [])`

### 2. Missing Function Definition
**Problem**: `authenticate_google_user_without_password` was not defined
**Solution**: Added complete function implementation with proper JWT token creation

### 3. Password Reset Prevention
**Problem**: Google SSO was resetting user passwords
**Solution**: Removed all password operations from Google SSO flows except for new user creation

## Monitoring and Logs

The system logs all authentication attempts:

```
INFO - Attempting password-less authentication <NAME_EMAIL>
INFO - Password-less authentication succeeded <NAME_EMAIL>
INFO - Using password-less authentication for existing <NAME_EMAIL>
WARN - Password-less authentication failed, falling back to password method: [error details]
```

## Testing

### Debug Endpoint

Access `/auth/debug/google-passwordless` to check configuration:

```json
{
  "ENABLE_PASSWORDLESS_GOOGLE_AUTH": true,
  "settings_value": true,
  "google_client_id": "123456789...",
  "message": "Password-less authentication is ENABLED"
}
```

### Test Scenarios
1. **New Google User Signup**: Should create user with password (for new users only)
2. **Existing Google User Login**: Should use password-less authentication  
3. **Disabled User**: Should enable user and authenticate without password reset
4. **Configuration Disabled**: Should fall back to password-based authentication
5. **Password + Google SSO**: Users can use both authentication methods
6. **Token Verification**: Both Cognito and Google SSO tokens should verify correctly

## Migration Guide

### For New Deployments
Set `ENABLE_PASSWORDLESS_GOOGLE_AUTH=true` in your environment variables.

### For Existing Deployments
1. Add `ENABLE_PASSWORDLESS_GOOGLE_AUTH=true` to your environment
2. Deploy the updated code
3. Test with a few Google SSO users
4. Monitor logs for any fallback scenarios
5. Verify existing passwords still work

### Rollback Plan
If issues occur, set `ENABLE_PASSWORDLESS_GOOGLE_AUTH=false` to revert to the original password-based method.

## Performance Benefits

- **Reduced Cognito API Calls**: No password reset operations
- **Faster Authentication**: Direct token creation vs authentication flows
- **Lower Latency**: Fewer round trips to Cognito
- **Reduced Complexity**: Simpler authentication path for OAuth users
- **Preserved User Experience**: No unexpected password changes

## Current Status

✅ **Implemented**: Password-less authentication for Google SSO users
✅ **Fixed**: Undefined variable bugs and missing function definitions  
✅ **Preserved**: Original user passwords for dual authentication
✅ **Tested**: Token verification for both Cognito and Google SSO tokens
✅ **Configurable**: Can be enabled/disabled via environment variables

## Future Enhancements

1. **Environment-based Token Signing**: Use environment variables for JWT signing keys
2. **Token Caching**: Cache tokens for better performance
3. **Enhanced Refresh Token Handling**: Implement proper refresh token validation for Google SSO tokens
4. **Multi-Provider Support**: Extend to other OAuth providers (GitHub, Microsoft, etc.)
5. **Enhanced Monitoring**: Add metrics for password-less vs password-based authentication usage 