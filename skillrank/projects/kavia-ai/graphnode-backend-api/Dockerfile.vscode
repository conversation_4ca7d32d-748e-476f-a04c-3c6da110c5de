FROM zoltankavia/kavia_base_dev_image:v1.0.0

ARG USERNAME=kavia
ARG HOST_UID=1001

# Performance environment variables
ENV CODE_SERVER_DISABLE_TELEMETRY=true
ENV CODE_SERVER_DISABLE_UPDATE_CHECK=true
ENV CODE_SERVER_DISABLE_GETTING_STARTED=true
ENV DONT_PROMPT_WSL_INSTALL=1
ENV NODE_OPTIONS="--max-old-space-size=256"
ENV EXTENSIONS_GALLERY='{"serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery"}'

# Change WORKDIR to avoid conflict with mounted /tmp
WORKDIR /home/<USER>/workspace

# Remove existing user with HOST_UID if exists and add the new user
RUN if id -u ${HOST_UID} >/dev/null 2>&1; then \
      userdel -r $(id -nu ${HOST_UID}); \
    fi && \
    useradd -m -s /bin/bash -u ${HOST_UID} ${USERNAME}

# Grant sudo access to the new user
RUN echo "${USERNAME} ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Install minimal dependencies in one layer
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    nfs-common \
    curl \
    unzip \
    git \
    python3 \
    python3-pip \
    --no-install-recommends && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

# Install code-server
RUN curl -fsSL https://code-server.dev/install.sh | sh

# Configure code-server with lightweight settings
RUN mkdir -p /home/<USER>/.config/code-server && \
    echo "bind-addr: 0.0.0.0:8080\nauth: none\ndisable-telemetry: true\ndisable-update-check: true\n" > /home/<USER>/.config/code-server/config.yaml && \
    chown -R ${USERNAME}:${USERNAME} /home/<USER>/.config

# Create optimized settings for lightweight operation with NO notifications
RUN mkdir -p /root/.local/share/code-server/User/ && \
    echo '{"security.workspace.trust.enabled":false,"workbench.startupEditor":"none","workbench.tips.enabled":false,"workbench.welcome.enabled":false,"terminal.integrated.openOnSessionStart":true,"terminal.integrated.defaultProfile.linux":"bash","terminal.integrated.profiles.linux":{"bash":{"path":"/bin/bash","args":["-l"]}},"telemetry.telemetryLevel":"off","update.mode":"none","extensions.autoCheckUpdates":false,"extensions.autoUpdate":false,"extensions.ignoreRecommendations":true,"editor.minimap.enabled":false,"editor.quickSuggestions":false,"editor.parameterHints.enabled":false,"editor.hover.enabled":false,"editor.codeLens":false,"editor.lightbulb.enabled":"off","breadcrumbs.enabled":false,"outline.icons":false,"problems.decorations.enabled":false,"git.enabled":true,"git.autorefresh":true,"git.autofetch":false,"git.decorations.enabled":true,"git.showProgress":false,"scm.diffDecorations":"gutter","files.watcherExclude":{"**/.git/objects/**":true,"**/.git/subtree-cache/**":true,"**/node_modules/**":true,"**/.hg/store/**":true,"**/tmp/**":true,"**/dist/**":true,"**/build/**":true},"search.exclude":{"**/node_modules":true,"**/bower_components":true,"**/.git":true,"**/.DS_Store":true,"**/tmp":true,"**/dist":true,"**/build":true},"typescript.suggest.enabled":false,"typescript.validate.enable":false,"javascript.suggest.enabled":false,"javascript.validate.enable":false,"workbench.settings.enableNaturalLanguageSearch":false,"notifications.showExtensionsNotifications":false,"window.showWelcomeOnOpen":"none","workbench.enableExperiments":false,"extensions.showRecommendationsOnlyOnDemand":true,"git.showActionButton":{"commit":false,"publish":false,"sync":false},"git.openRepositoryInParentFolders":"never","git.ignoreMissingGitWarning":true,"git.ignoreLegacyWarning":true,"git.ignoreRebaseWarning":true,"git.ignoreSubmodules":true,"git.confirmSync":false,"git.confirmEmptyCommits":false,"git.confirmForcePush":false,"scm.showActionButton":false,"terminal.integrated.enableBell":false,"workbench.activityBar.visible":true,"workbench.statusBar.visible":true,"editor.acceptSuggestionOnEnter":"off","editor.suggestOnTriggerCharacters":false,"editor.wordBasedSuggestions":"off","editor.tabCompletion":"off","workbench.colorCustomizations":{},"window.menuBarVisibility":"toggle","zenMode.hideActivityBar":true,"zenMode.hideStatusBar":true,"zenMode.hideTabs":true,"problems.showCurrentInStatus":false,"editor.gotoLocation.multipleDefinitions":"goto","editor.gotoLocation.multipleReferences":"goto","editor.gotoLocation.multipleImplementations":"goto","workbench.panel.defaultLocation":"bottom","workbench.panel.opensMaximized":"never","problems.visibility":false,"output.smartScroll.enabled":false,"debug.console.fontSize":0,"debug.console.lineHeight":0,"workbench.view.debug.visibility":"hidden","workbench.view.extensions.visibility":"hidden","gitlens.views.repositories.enabled":false,"gitlens.views.fileHistory.enabled":false,"gitlens.views.lineHistory.enabled":false,"gitlens.views.compare.enabled":false,"gitlens.views.search.enabled":false,"gitlens.statusBar.enabled":false,"gitlens.codeLens.enabled":false,"gitlens.currentLine.enabled":false,"gitlens.hovers.enabled":false,"gitlens.blame.highlight.enabled":false,"gitlens.changes.locations":[],"workbench.view.alwaysShowHeaderActions":false}' > /root/.local/share/code-server/User/settings.json

# Set ownership for settings
RUN chown -R ${USERNAME}:${USERNAME} /root/.local

# Add a symbolic link from the user's config to root's config
RUN mkdir -p /home/<USER>/.local/share/code-server/User/ && \
    ln -sf /root/.local/share/code-server/User/settings.json /home/<USER>/.local/share/code-server/User/settings.json && \
    chown -R ${USERNAME}:${USERNAME} /home/<USER>/.local

# Install AWS CLI version 2 with error handling
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    if [ -s awscliv2.zip ]; then \
      unzip awscliv2.zip && \
      ./aws/install --bin-dir /usr/local/bin --install-dir /usr/local/aws-cli --update && \
      rm -rf aws awscliv2.zip; \
    else \
      echo "AWS CLI download failed"; \
      exit 1; \
    fi

# Install AWS CodeCommit helper via pip
RUN pip3 install --no-cache-dir awscli boto3 git-remote-codecommit

# Configure Git for AWS CodeCommit with performance optimizations and no notifications
RUN git config --global user.name "CodeGen Bot" && \
    git config --global user.email "<EMAIL>" && \
    git config --global core.precomposeunicode false && \
    git config --global core.preloadindex true && \
    git config --global core.fscache true && \
    git config --global gc.auto 256 && \
    git config --global pack.threads 2 && \
    git config --global diff.algorithm histogram && \
    git config --global advice.statusHints false && \
    git config --global advice.pushUpdateRejected false && \
    git config --global advice.commitBeforeMerge false && \
    git config --global advice.resolveConflict false && \
    git config --global advice.implicitIdentity false && \
    git config --global advice.detachedHead false && \
    git config --global advice.addIgnoredFile false && \
    git config --global advice.addEmptyPathspec false

# Configure simplified shell prompt to show only '#'
RUN echo 'export PS1="# "' >> /root/.bashrc && \
    echo 'export PS1="# "' >> /home/<USER>/.bashrc && \
    echo 'export PS1="# "' >> /etc/bash.bashrc

# Ensure /tmp directory permissions for the container user
RUN mkdir -p /tmp && \
    chown -R ${HOST_UID}:${HOST_UID} /tmp

# Copy extensions
COPY kavia-terminal-0.1.4.vsix /tmp/kvsce_extention.vsix
COPY kvsce-0.1.16.vsix /tmp/kvsce_terminal_extention.vsix

# Install only essential extensions
RUN code-server --install-extension bierner.markdown-mermaid \
    --disable-telemetry \
    --disable-update-check && \
    code-server --install-extension /tmp/kvsce_extention.vsix \
    --disable-telemetry \
    --disable-update-check && \
    code-server --install-extension /tmp/kvsce_terminal_extention.vsix \
    --disable-telemetry \
    --disable-update-check && \
    rm -f /tmp/kvsce_extention.vsix /tmp/kvsce_terminal_extention.vsix

# Expose the port code-server runs on
EXPOSE 8080

# Set the user
USER root

# Command to run code-server with lightweight flags and no notifications
CMD ["code-server", \
     "--host", "0.0.0.0", \
     "--auth", "none", \
     "--disable-telemetry", \
     "--disable-update-check", \
     "--disable-workspace-trust", \
     "--disable-getting-started-override", \
     "--log", "error", \
     "--verbose", "false"]