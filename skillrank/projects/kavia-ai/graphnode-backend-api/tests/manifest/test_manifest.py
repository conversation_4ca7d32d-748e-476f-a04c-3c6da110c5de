from app.routes.manifest_route import generate_rjsf_schemas


print("Generating RJSF schemas for manifest...")
config = generate_rjsf_schemas()

print("RJSF schemas generated successfully.")
print("Configuration:", config)

#Write the configuration to a file
with open("rjsf_schemas_config.json", "w") as f:
    import json
    json.dump(config, f, indent=4)

print("Configuration written to rjsf_schemas_config.json")

