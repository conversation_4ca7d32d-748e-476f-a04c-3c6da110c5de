from code_generation_core_agent.config import config as code_generation_config
from datetime import datetime
import yaml
import pkg_resources
import shutil
import requests
from code_generation_core_agent.agents.task_execution_agent import (
    TaskExecutionAgent,
    TaskExecutionControl
)
import yaml
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.telemetry.logger_factory import LoggerFactory
from app.core.Settings import settings
from app.models.code_generation_model import Message, AgentParams
from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType
from code_generation_core_agent.chat.chat_enabled_worker import Chat<PERSON><PERSON>bledWorker
from app.core.code_generation import get_prompt_directory
from app.models.reasoning_model import get_options_for_model, check_valid_reasoning_effort

chat_enabled_worker = ChatEnabledWorker([], name="TaskExecutionAgent")

session_dir = "/tmp/kavia/workspace/code-generation"

selected_platform = "web"
selected_framework = "react"

llm = LLMInterface(
    llm_api_key=settings.OPENAI_API_KEY,
    session_dir=session_dir,
    instance_name="TaskExecutionAgent",
    chat_worker=chat_enabled_worker
)

agent_params = AgentParams(
    llm_model="gpt-5",
    agent_name="CodeGeneration"
)
micro_agents_yaml_base_path = pkg_resources.resource_filename(
    "code_generation_core_agent.agents.micro", 
    ""
)
platform_config_loader = PlatformConfigLoader(micro_agents_yaml_base_path)
micro_agents_config = platform_config_loader.get_platform_config(
    platform=ApplicationType.from_string("web"),
    framework=FrameworkType.from_string("react")
)


agent = TaskExecutionAgent(
    llm,
    execution_base_path=session_dir,
    micro_agents_config=micro_agents_config,
    model_name="gpt-5",
    prompts_base_path=get_prompt_directory(agent_params.agent_name),
    platform=selected_platform,
    framework=selected_framework,
)

def change_reasoning_effort(reasoning_effort: str):
    code_generation_config.set_runtime_config("LLM", "openai_reasoning_effort", reasoning_effort)
    code_generation_config.set("LLM", "openai_reasoning_effort", reasoning_effort)

def get_reasoning_effort():
    return  code_generation_config.get("LLM", "openai_reasoning_effort")

def get_config():
    return code_generation_config.get("LLM", "openai_reasoning_effort")

def get_current_model():
    return agent.get_selected_model()

def get_reasoning_options():
    return get_options_for_model(get_current_model())

def check_valid_reasoning_effort_internal(effort: str):
    return check_valid_reasoning_effort(get_current_model(), effort)

print(agent.get_available_models())

while True:
    user_input = input("Enter your command: ")
    if user_input == "exit":
        break
    elif user_input == "models":
        print(agent.get_available_models())
    elif user_input == "reasoning":
        print(get_reasoning_effort())
    elif user_input.startswith("set_reasoning"):
        effort = user_input.split(" ")[1]
        change_reasoning_effort(effort)
    elif user_input == "get_reasoning_options":
        print(get_reasoning_options())
    elif user_input.startswith("check_valid_reasoning_effort"):
        effort = user_input.split(" ")[1]
        print(check_valid_reasoning_effort_internal(effort))
    elif user_input == "config":
        print(get_config())
    else:
        print("Invalid command")