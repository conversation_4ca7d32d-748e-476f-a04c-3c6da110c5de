#!/usr/bin/env python3
"""
test_bootstrap.py - Test Supabase bootstrap with connection debugging
"""

import sys
import psycopg2
import urllib.parse
from typing import Optional
from app.services.supabase_bootstrap import bootstrap_supabase_project_k8s
def test_connection(db_url: str, connection_name: str) -> bool:
    """Test a single connection."""
    print(f"\n{'='*60}")
    print(f"Testing {connection_name}")
    print(f"URL: {db_url.split('@')[0]}@...")
    print(f"{'='*60}")
    
    try:
        print("Attempting connection...")
        conn = psycopg2.connect(db_url, connect_timeout=10)
        
        with conn.cursor() as cur:
            cur.execute("SELECT version(), current_database(), current_user")
            version, database, user = cur.fetchone()
            print(f"✅ SUCCESS!")
            print(f"   Database: {database}")
            print(f"   User: {user}")
            print(f"   Version: {version.split(',')[0]}")
            
            # Check for pgcrypto
            cur.execute("SELECT COUNT(*) FROM pg_extension WHERE extname = 'pgcrypto'")
            has_pgcrypto = cur.fetchone()[0] > 0
            print(f"   pgcrypto: {'✅ Installed' if has_pgcrypto else '❌ Not installed'}")
            
            # Check for run_sql
            cur.execute("""
                SELECT COUNT(*) FROM pg_proc p
                JOIN pg_namespace n ON n.oid = p.pronamespace
                WHERE p.proname = 'run_sql' AND n.nspname = 'public'
            """)
            has_run_sql = cur.fetchone()[0] > 0
            print(f"   run_sql: {'✅ Installed' if has_run_sql else '❌ Not installed'}")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        return False


def main():
    # Your project details
    project_ref = "zmutrnkwaxofrwfqximt"
    password = "Prasanth232@"
    region = "us-east-1"  # Change if different
    
    # URL encode password
    encoded_password = urllib.parse.quote(password, safe='')
    
    # Test different connection formats
    connections = [
      
        # 1. Standard connection
        (
            f"postgresql://postgres.zmutrnkwaxofrwfqximt:{encoded_password}@aws-1-us-east-1.pooler.supabase.com:6543/postgres",
            "Standard Connection"
        )
    ]
    
    print("="*60)
    print("SUPABASE CONNECTION TEST")
    print(f"Project: {project_ref}")
    print(f"Region: {region}")
    print("="*60)
    
    successful_connection = None
    
    for db_url, name in connections:
        if test_connection(db_url, name):
            successful_connection = db_url
            break
    
    if successful_connection:
        print(f"\n{'='*60}")
        print("✅ RECOMMENDED CONNECTION STRING:")
        print(f"{'='*60}")
        print(successful_connection)
        
        # Try bootstrap if requested
        response = input("\nDo you want to run bootstrap? (y/n): ")
        if response.lower() == 'y':
            print("\nRunning bootstrap...")
            result = bootstrap_supabase_project_k8s(successful_connection, verbose=True, auto_fix=False)
            
            if result.success:
                print("\n✅ Bootstrap completed successfully!")
            else:
                print(f"\n❌ Bootstrap failed: {result.message}")
    else:
        print("\n❌ All connection attempts failed")
        print("\nTROUBLESHOOTING:")
        print("1. Check if project is ACTIVE in Supabase dashboard")
        print("2. Verify password is correct")
        print("3. Check network/firewall settings")
        print("4. Try different region if not us-east-1")


if __name__ == "__main__":
    main()