from app.routes import node_route
from app.connection.establish_db_connection import connect_node_db
import pytest
import json
from unittest import mock
from fastapi.responses import StreamingResponse
from pprint import PrettyPrinter
from types import SimpleNamespace

pp = PrettyPrinter(indent=1, depth=None, compact=False)
node_db = connect_node_db()


@pytest.mark.asyncio
@pytest.mark.parametrize("framework, platform", [
    ("React", "web"),
    ("Vue", "android"),
])
async def test_start_project_node_creation(framework, platform):
    """Test to verify nodes are created correctly when starting a project."""
    # Create test request with parametrized framework and platform
    request = SimpleNamespace(
        requirement="Build a simple to-do list app with task tracking",
        framework=framework,
        platform=platform
    )
    
    # Mock current user for the dependency
    current_user = {"cognito:username": "test_user"}
    
    # Create a mock LLM service
    class MockLLMService:
        async def llm_interaction_wrapper(self, messages, user_prompt, system_prompt, response_format, model, stream):
            mock_response = SimpleNamespace()
            mock_response.choices = [SimpleNamespace()]
            mock_response.choices[0].message = SimpleNamespace()
            mock_response.choices[0].message.content = json.dumps({
                "project_name": f"TodoTracker-{framework}-{platform}",
                "architecture_requirement": {"description": "A to-do list application"},
                "system_context": {"description": "Application for managing tasks"},
                "container": {"name": "Container", "description": "Container description"},
                "component": {
                    "name": "Component",
                    "description": "Component description",
                    "design": {
                        "component_name": "Component Name",
                        "description": "Component description",
                        "interfaces": "Interfaces",
                        "algorithms": "Algorithms",
                        "pseudocode": ""
                    }
                }
            })
            return mock_response
    
    # Simple implementation to mock the streaming response
    async def mock_stream_start_workspace_status(*args, **kwargs):
        yield '{"status": "processing", "message": "Creating workspace..."}'
    
    # Mock dependencies
    with mock.patch('app.routes.node_route.get_current_user', return_value=current_user), \
         mock.patch('app.routes.node_route.get_llm_interface', return_value=MockLLMService()), \
         mock.patch('app.routes.node_route.stream_start_workspace_status', return_value=mock_stream_start_workspace_status()), \
         mock.patch('app.routes.node_route.get_node_db', return_value=node_db):
        
        # Call the endpoint
        result = await node_route.start_project(request, current_user, MockLLMService())
        
        # Print the result
        pp.pprint(result)
        
        # Assert that the result is a StreamingResponse
        assert isinstance(result, StreamingResponse)
        
        # Extract project_id from the stream
        project_id = None
        async for chunk in result.body_iterator:
            # Handle the chunk whether it's bytes or string
            chunk_str = chunk.decode('utf-8') if isinstance(chunk, bytes) else chunk
            
            if "project_id" in chunk_str:
                try:
                    # Try to extract JSON from the chunk
                    if chunk_str.startswith('data: '):
                        data_str = chunk_str[6:].strip()
                    else:
                        data_str = chunk_str.strip()
                    
                    data = json.loads(data_str)
                    if "project_id" in data:
                        project_id = data["project_id"]
                        break
                except:
                    continue
        
        # Check that we found a project_id
        assert project_id is not None, f"Project ID not found in stream for {framework} on {platform}"
        
        # Verify that the expected nodes were created
        
        # Get the project node to verify it exists
        project_node = await node_db.get_node_by_id(project_id)
        assert project_node is not None, "Project node not found"
        
        # Check if the node has all expected child types
        # For simplicity we'll just verify the project node exists
        # and that it has at least one child node of each expected type
        
        # Write the result to a JSON file
        with open(f'tests/node_creation_{framework.lower()}_{platform}_result.json', 'w') as json_file:
            json.dump({
                "framework": framework,
                "platform": platform,
                "project_id": project_id,
                "project_node": project_node,
                "test_result": "passed"
            }, json_file, indent=4)
        
        print(f"Results for {framework} on {platform} have been written to 'tests/node_creation_{framework.lower()}_{platform}_result.json'")

# import pytest
# import asyncio
# import time
# import json
# import requests
# from requests.exceptions import RequestException


# def test_start_project_execution_time():
#     """
#     Simple test to measure the execution time of the start-project endpoint
#     using requests instead of AsyncClient.
    
#     This test assumes the server is already running at the specified URL.
#     """
#     # Define endpoint URL
#     url = "http://127.0.0.1:8000/api/node/start-project"
    
#     # Test request data
#     request_data = {
#         "requirement": "Create a simple todo application",
#         "framework": "REACT",
#         "platform": "WEB"
#     }
    
#     # Define the acceptable execution time threshold
#     MAX_EXECUTION_TIME = 3.0
    
#     try:
#         # Start timing
#         start_time = time.time()
        
#         # Make request to the endpoint
#         # Note: stream=True is needed for streaming responses
#         response = requests.post(url, json=request_data, stream=True)
        
#         # Read all content from the streaming response
#         content = b""
#         for chunk in response.iter_content(chunk_size=1024):
#             content += chunk
            
#         # End timing
#         execution_time = time.time() - start_time
        
#         # Print the execution time
#         print(f"\nExecution time for start-project endpoint: {execution_time:.2f} seconds")
        
#         # Basic validation
#         assert response.status_code == 200, f"Request failed with status code {response.status_code}"
        
#         # Performance assertion
#         assert execution_time <= MAX_EXECUTION_TIME, \
#             f"Execution time ({execution_time:.2f}s) exceeds threshold ({MAX_EXECUTION_TIME}s)"
            
#         # Print response content summary (first 200 chars)
#         print(f"Response preview: {content[:200]}")
        
#         # Optional: Parse the response to check contents
#         try:
#             # For SSE format, we need to parse each line
#             # This is a simple approach that assumes JSON in each data line
#             parsed_data = []
#             for line in content.split(b'\n'):
#                 if line.startswith(b'data: '):
#                     json_str = line[6:].decode('utf-8')  # Remove 'data: ' prefix
#                     try:
#                         parsed_data.append(json.loads(json_str))
#                     except json.JSONDecodeError:
#                         pass
                        
#             # Check if we got any valid response data
#             if parsed_data:
#                 print(f"Received {len(parsed_data)} SSE messages")
#                 if parsed_data[-1].get('status') == 'complete':
#                     print("Successfully completed request")
#         except Exception as e:
#             print(f"Error parsing response: {e}")
            
#     except RequestException as e:
#         pytest.fail(f"Request failed: {e}")
        
        
# if __name__ == "__main__":
#     # This allows running the test directly without pytest
#     # Useful for quick testing when the server is already running
#     test_start_project_execution_time()