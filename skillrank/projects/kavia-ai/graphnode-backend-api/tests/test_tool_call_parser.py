from app.utils.llm_tool_parser.tool_call_parser import parse_tool_info_streaming

def test_parse_tool_info_streaming():
    """Test parse_tool_info_streaming with direct log data"""
    
    # Sample log entry - directly use one from your log file
    sample_log_entry = {
        "function_name": "create_file",
        "tool_call_id": "call_d7dx6El2soLWU85xHsfTg6lx",
        "function_args": '{"file_path": "/tmp/figma/b807be60/.assets/design_file.html", "content": "<!DOCTYPE html>\\n<html>\\n<head>\\n    <meta charset=\\"UTF-8\\">\\n    <title>Completed Task</title>\\n    <style>\\n        body {\\n            margin: 0;\\n            padding: 0;\\n            background: #d3d3ee;\\n            font-family: \'Inter\', Arial, sans-serif;\\n        }\\n        .header {\\n            background: #a3a3d9;\\n            height: 120px;\\n            position: relative;\\n            display: flex;\\n            flex-direction: column;\\n            justify-content: flex-end;\\n        }"}',
        "is_complete": False
    }
    
    # Call the function
    current_operations = {}
    try:
        result, updated, current_operations = parse_tool_info_streaming(sample_log_entry, current_operations)
        
        print("\nTEST RESULTS:")
        print("-" * 50)
        print(f"Operation: {result['operation']}")
        print(f"File Path: {result['file_path']}")
        print(f"Tool Call ID: {result['tool_call_id']}")
        print(f"Content Updated: {updated}")
        print("\nFormatted Content:")
        print("-" * 50)
        print(result['content'])
        print("-" * 50)
        print("CONTENT STARTS WITH '\"'? ", result['content'].startswith('"'))
        print("CONTENT STARTS WITH <!DOCTYPE? ", result['content'].startswith('<!DOCTYPE'))
        print("CONTENT ENDS WITH '}'? ", result['content'].endswith('}'))
        print("CONTENT ENDS WITH '\"}'? ", result['content'].endswith('"}'))
        print("-" * 50)
        
        # Test a second update to verify streaming behavior
        second_entry = {
            "function_name": "create_file",
            "tool_call_id": "call_d7dx6El2soLWU85xHsfTg6lx",
            "function_args": '{"file_path": "/tmp/figma/b807be60/.assets/design_file.html", "content": "<!DOCTYPE html>\\n<html>\\n<head>\\n    <meta charset=\\"UTF-8\\">\\n    <title>Completed Task</title>\\n    <style>\\n        body {\\n            margin: 0;\\n            padding: 0;\\n            background: #d3d3ee;\\n            font-family: \'Inter\', Arial, sans-serif;\\n        }\\n        .header {\\n            background: #a3a3d9;\\n            height: 120px;\\n            position: relative;\\n            display: flex;\\n            flex-direction: column;\\n            justify-content: flex-end;\\n        }\\n        .status-bar {\\n            height: 44px;\\n            display: flex;\\n            align-items: center;\\n            padding: 0 20px;\\n        }"}',
            "is_complete": False
        }
        
        result2, updated2, current_operations = parse_tool_info_streaming(second_entry, current_operations)
        
        print("\nSECOND UPDATE RESULTS:")
        print("-" * 50)
        print(f"Content Updated: {updated2}")
        print("\nUpdated Content (showing last part):")
        content_lines = result2['content'].split('\n')
        for line in content_lines[-5:]:
            print(line)
        print("-" * 50)
        print("CONTENT STARTS WITH '\"'? ", result2['content'].startswith('"'))
        print("CONTENT STARTS WITH <!DOCTYPE? ", result2['content'].startswith('<!DOCTYPE'))
        print("CONTENT ENDS WITH '}'? ", result2['content'].endswith('}'))
        print("CONTENT ENDS WITH '\"}'? ", result2['content'].endswith('"}'))
        
        # Check for escape sequence handling
        if "\\n" not in result['content'] and "\n" in result['content']:
            print("\nESCAPE SEQUENCES PROPERLY HANDLED: YES")
        else:
            print("\nESCAPE SEQUENCES PROPERLY HANDLED: NO")
        
        return True
    except Exception as e:
        import traceback
        print(f"TEST ERROR: {str(e)}")
        traceback.print_exc()
        return False

# Run the test
success = test_parse_tool_info_streaming()
print(f"\nTest passed: {success}")