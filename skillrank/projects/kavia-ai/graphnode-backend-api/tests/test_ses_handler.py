#!/usr/bin/env python
"""
Unit tests for the SESHandler class.
"""

import unittest
from unittest.mock import patch, MagicMock
from app.classes.SESHandler import SESHandler

class TestSESHandler(unittest.TestCase):
    
    @patch('boto3.client')
    def setUp(self, mock_boto_client):
        # Create mock SES client
        self.mock_ses_client = MagicMock()
        mock_boto_client.return_value = self.mock_ses_client
        
        # Initialize the SES handler
        self.ses_handler = SESHandler()
        
    def test_get_verified_identities(self):
        # Mock the response
        mock_response = {
            'Identities': ['<EMAIL>', 'example.com']
        }
        self.mock_ses_client.list_identities.return_value = mock_response
        
        # Call the method
        result = self.ses_handler.get_verified_identities()
        
        # Verify the result
        self.assertEqual(result, mock_response)
        self.mock_ses_client.list_identities.assert_called_once_with(
            IdentityType='EmailAddress',
            MaxItems=100
        )
        
    def test_verify_email_identity(self):
        # Mock the response
        self.mock_ses_client.verify_email_identity.return_value = {}
        
        # Call the method
        result = self.ses_handler.verify_email_identity('<EMAIL>')
        
        # Verify the result
        self.assertEqual(result, {
            'message': 'Verification email <NAME_EMAIL>',
            'status': 'success'
        })
        self.mock_ses_client.verify_email_identity.assert_called_once_with(
            EmailAddress='<EMAIL>'
        )
        
    def test_delete_identity(self):
        # Mock the response
        self.mock_ses_client.delete_identity.return_value = {}
        
        # Call the method
        result = self.ses_handler.delete_identity('<EMAIL>')
        
        # Verify the result
        self.assertEqual(result, {
            'message': 'Identity <EMAIL> deleted successfully',
            'status': 'success'
        })
        self.mock_ses_client.delete_identity.assert_called_once_with(
            Identity='<EMAIL>'
        )
        
    @patch('smtplib.SMTP')
    def test_send_email_smtp(self, mock_smtp):
        # Mock the SMTP instance
        mock_smtp_instance = MagicMock()
        mock_smtp.return_value = mock_smtp_instance
        
        # Call the method
        result = self.ses_handler.send_email_smtp(
            to_address='<EMAIL>',
            subject='Test Subject',
            body_text='Test Body'
        )
        
        # Verify the result
        self.assertEqual(result, {
            'message': 'Email sent <NAME_EMAIL>',
            'status': 'success'
        })
        mock_smtp.assert_called_once_with(self.ses_handler.smtp_server, self.ses_handler.smtp_port)
        mock_smtp_instance.starttls.assert_called_once()
        mock_smtp_instance.login.assert_called_once_with(self.ses_handler.smtp_username, self.ses_handler.smtp_password)
        mock_smtp_instance.sendmail.assert_called_once()
        mock_smtp_instance.quit.assert_called_once()
        
    def test_send_email_api(self):
        # Mock the response
        self.mock_ses_client.send_email.return_value = {
            'MessageId': 'test-message-id'
        }
        
        # Call the method
        result = self.ses_handler.send_email_api(
            to_addresses=['<EMAIL>'],
            subject='Test Subject',
            body_text='Test Body',
            body_html='<html><body>Test Body</body></html>'
        )
        
        # Verify the result
        self.assertEqual(result, {
            'message': 'Email sent successfully',
            'message_id': 'test-message-id',
            'status': 'success'
        })
        self.mock_ses_client.send_email.assert_called_once()
        
    def test_get_sending_statistics(self):
        # Mock the response
        mock_response = {
            'SendDataPoints': [
                {
                    'Timestamp': '2023-01-01T00:00:00Z',
                    'DeliveryAttempts': 10,
                    'Bounces': 1,
                    'Complaints': 0,
                    'Rejects': 0
                }
            ]
        }
        self.mock_ses_client.get_send_statistics.return_value = mock_response
        
        # Call the method
        result = self.ses_handler.get_sending_statistics()
        
        # Verify the result
        self.assertEqual(result, mock_response)
        self.mock_ses_client.get_send_statistics.assert_called_once()

if __name__ == '__main__':
    unittest.main() 