#!/usr/bin/env python
"""
Example script demonstrating how to use the SESHandler class.
"""

from app.classes.SESHandler import SESHandler

def main():
    # Initialize the SES handler
    ses_handler = <PERSON><PERSON><PERSON>andler()
    
    # Example 1: Send an email using SMTP
    print("Sending email via SMTP...")
    result = ses_handler.send_email_smtp(
        to_address="<EMAIL>",  # Replace with a verified email
        subject="Test Email from Kavia AI",
        body_text="""
Hello,

This is a test email sent using AWS SES from the Kavia AI system.

Thank you for testing our email delivery system.

Best regards,
Kavia AI Team
        """,
        sender_name="<PERSON><PERSON> AI"
    )
    print(result)
    
    # Example 2: Send an email using SES API
    print("\nSending email via SES API...")
    result = ses_handler.send_email_api(
        to_addresses=["<EMAIL>"],  # Replace with a verified email
        subject="Test Email from Kavia AI API",
        body_text="This is a test email sent using the AWS SES API.",
        body_html="<html><body><h1>Test Email</h1><p>This is a test email sent using the AWS SES API.</p></body></html>"
    )
    print(result)
    
    # Example 3: Get verified identities
    print("\nGetting verified identities...")
    identities = ses_handler.get_verified_identities()
    print(f"Verified identities: {identities}")
    
    # Example 4: Get sending statistics
    print("\nGetting sending statistics...")
    stats = ses_handler.get_sending_statistics()
    print(f"Sending statistics: {stats}")

if __name__ == "__main__":
    main() 