from pymongo import MongoClient

client = MongoClient("******************************************/")  # Your URI
db = client["pre_prod_kaviaroot"]
collection = db["llm_costs"]

# Update all users inside 'users' array that either don't have 'projects' or 'projects' is not an array
update_result = collection.update_many(
    {},  # Match all documents
    {
        "$set": {
            "users.$[user].projects": []
        }
    },
    array_filters=[
        {
            "$or": [
                {"user.projects": {"$exists": False}},
                {"user.projects": {"$not": {"$type": "array"}}}
            ]
        }
    ]
)

print(f"Modified {update_result.modified_count} documents")
