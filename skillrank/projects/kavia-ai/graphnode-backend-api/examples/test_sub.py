import subprocess  # Import from standard library, not asyncio
import os

screen_cmd = ['python', 'app/batch_jobs/fast-api.py', '/home/<USER>/kavia-ai/graphnode-backend-api']
current_env = os.environ.copy()

result = subprocess.run(
    screen_cmd,
    check=True,
    env=current_env,
    capture_output=True,  # Capture stdout/stderr for debugging
    text=True
)

print("Success!")
print(f"Return code: {result.returncode}")
print(f"Output: {result.stdout}")
if result.stderr:
    print(f"Errors: {result.stderr}")