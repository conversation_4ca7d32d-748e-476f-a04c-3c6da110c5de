#!/usr/bin/env python3
"""
QA Plan Upgrade Script (Simulates Stripe Payment Flow)

This script simulates the complete Stripe payment success flow for QA testing.
It performs the exact same operations as the payment_route.py success endpoint
without requiring actual Stripe payment processing.

Usage:
    python verify_user_upgrade.py --user-id <user_id> --plan <plan_name> [--tenant-id <tenant_id>] [--credits <credits>]

Example:
    python verify_user_upgrade.py --user-id "d4b8c438-1041-70fd-959f-3e5522a8bc79" --plan "premium_advanced" --tenant-id "b2c" --credits 550000

API Endpoint Conversion:
    This script can be easily converted to a FastAPI endpoint in super_route.py:

    @router.post("/qa/upgrade-user-plan")
    async def qa_upgrade_user_plan(request: QAUpgradeRequest):
        upgrader = UserUpgradeVerifier(MONGO_URI, DB_NAME)
        try:
            result = upgrader.simulate_stripe_payment_flow(
                request.user_id, request.plan, request.tenant_id, request.credits
            )
            return result
        finally:
            upgrader.close()
"""

import argparse
import sys
from pymongo import MongoClient
from datetime import datetime
import json

# MongoDB connection settings - update these to match your environment
MONGO_URI = "**************************************************************"
DB_NAME = "develop_kaviaroot"

# Plan mapping for display
PLAN_MAPPING = {
    "free_plan": "Free Plan",
    "price_1RWBTrCI2zbViAE2WZFApvc8": "Premium Starter",
    "price_1RIosGCI2zbViAE25Ny8rOkc": "Premium Advanced",
    "price_1RIozwCI2zbViAE2beuA7CDk": "Premium Pro"
}

class UserUpgradeVerifier:
    def __init__(self, mongo_uri: str, db_name: str):
        self.client = MongoClient(mongo_uri)
        self.db = self.client[db_name]

        # Plan mapping with credits (same as upgrade script)
        self.plan_mapping = {
            "premium_starter": {
                "price_id": "price_1RWBTrCI2zbViAE2WZFApvc8",
                "plan_name": "Premium Starter",
                "default_credits": 100000
            },
            "premium_advanced": {
                "price_id": "price_1RIosGCI2zbViAE25Ny8rOkc",
                "plan_name": "Premium Advanced",
                "default_credits": 250000
            },
            "premium_pro": {
                "price_id": "price_1RIozwCI2zbViAE2beuA7CDk",
                "plan_name": "Premium Pro",
                "default_credits": 1100000
            }
        }

    def close(self):
        """Close MongoDB connection"""
        self.client.close()

    def simulate_stripe_payment_flow(self, user_id: str, plan_name: str, tenant_id: str = "b2c", credits: int = None):
        """
        Simulate the complete Stripe payment success flow for QA testing
        Follows the exact same steps as payment_route.py success endpoint
        """
        print(f"🚀 Starting QA Plan Upgrade (Simulating Stripe Payment Flow)")
        print(f"User: {user_id}")
        print(f"Plan: {plan_name}")
        print(f"Tenant: {tenant_id}")

        # DEBUG: Add breakpoint here for debugging
        print("🐛 DEBUG: About to validate plan...")

        # Validate plan
        if plan_name not in self.plan_mapping:
            raise ValueError(f"Invalid plan: {plan_name}. Available: {list(self.plan_mapping.keys())}")

        plan_info = self.plan_mapping[plan_name]
        if credits is None:
            credits = plan_info["default_credits"]

        print(f"🐛 DEBUG: Plan validated. Price ID: {plan_info['price_id']}, Credits: {credits}")

        result = {
            "user_id": user_id,
            "plan_name": plan_name,
            "price_id": plan_info["price_id"],
            "credits": credits,
            "tenant_id": tenant_id,
            "success": False,
            "updates": {},
            "errors": [],
            "plan_history_preserved": False
        }

        try:
            # Step 1: Create/Update Active Subscription (same as Stripe flow)
            print("📝 Step 1: Creating/Updating Active Subscription...")
            subscription_result = self._create_active_subscription(user_id, tenant_id, plan_info, credits)
            result["updates"]["active_subscriptions"] = subscription_result

            # Step 2: Update Organization Credits (same as Stripe flow)
            print("🏢 Step 2: Updating Organization Credits...")
            org_result = self._update_organization_credits(tenant_id, credits)
            result["updates"]["organizations"] = org_result

            # Step 3: Update LLM Costs with Plan History (same as Stripe flow)
            print("💰 Step 3: Updating LLM Costs with Plan History...")
            llm_result = self._update_llm_costs_stripe_flow(user_id, tenant_id, plan_info)
            result["updates"]["llm_costs"] = llm_result
            result["plan_history_preserved"] = llm_result.get("plan_history_preserved", False)

            result["success"] = True
            print("✅ QA Plan Upgrade completed successfully!")

        except Exception as e:
            error_msg = f"QA Plan Upgrade failed: {str(e)}"
            print(f"❌ {error_msg}")
            result["errors"].append(error_msg)

        return result

    def _create_active_subscription(self, user_id: str, tenant_id: str, plan_info: dict, credits: int):
        """Create/Update active subscription (same as payment_route.py)"""
        from datetime import datetime, timedelta

        print(f"🐛 DEBUG: Creating active subscription for user {user_id}")

        current_time = datetime.utcnow().isoformat()
        expires_at = (datetime.utcnow() + timedelta(days=30)).isoformat()
        subscription_id = f"qa_upgrade_{user_id}_{int(datetime.utcnow().timestamp())}"

        print(f"🐛 DEBUG: Subscription ID: {subscription_id}")

        active_subscription_data = {
            "subscription_id": subscription_id,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "status": 2,  # PaymentStatus.COMPLETED
            "created_at": current_time,
            "updated_at": current_time,
            "payment_session_id": subscription_id,
            "price_id": plan_info["price_id"],
            "credits": credits,
            "expires_at": expires_at
        }

        result = self.db["active_subscriptions"].update_one(
            {"subscription_id": subscription_id},
            {"$set": active_subscription_data},
            upsert=True
        )

        return {"matched": result.matched_count, "modified": result.modified_count, "upserted": result.upserted_id is not None}

    def _update_organization_credits(self, tenant_id: str, credits: int):
        """Update organization credits (same as payment_route.py)"""
        result = self.db["organizations"].update_one(
            {"_id": tenant_id},
            {"$set": {"credits": credits}}
        )

        return {"matched": result.matched_count, "modified": result.modified_count}

    def _update_llm_costs_stripe_flow(self, user_id: str, tenant_id: str, plan_info: dict):
        """Update LLM costs with plan history preservation (same as payment_route.py)"""
        print(f"🐛 DEBUG: Updating LLM costs for user {user_id} in organization {tenant_id}")

        # Find the organization document
        llm_cost_doc = self.db["llm_costs"].find_one({"organization_id": tenant_id})

        if not llm_cost_doc:
            raise Exception(f"LLM cost document not found for organization {tenant_id}")

        print(f"🐛 DEBUG: Found LLM cost document with {len(llm_cost_doc.get('users', []))} users")

        # Find the user in the users array
        user_found = False
        plan_history_preserved = False

        for user in llm_cost_doc.get("users", []):
            if user.get("user_id") == user_id:
                user_found = True

                # Preserve current plan in history (same as Stripe flow + projects preservation)
                current_plan = user.get("current_plan")
                if current_plan and current_plan != plan_info["price_id"]:
                    print(f"🐛 DEBUG: Preserving plan history for user {user_id}")
                    print(f"🐛 DEBUG: Previous plan: {current_plan}")
                    print(f"🐛 DEBUG: Previous cost: {user.get('cost', '0.000000')}")
                    print(f"🐛 DEBUG: Previous projects count: {len(user.get('projects', []))}")

                    old_plan = {
                        "plan_id": current_plan,
                        "cost": user.get("cost", "0.000000"),
                        "projects": user.get("projects", []),  # PRESERVE PROJECTS IN HISTORY
                        "upgraded_at": datetime.utcnow().isoformat()
                    }

                    if "plans_history" not in user:
                        user["plans_history"] = []
                    user["plans_history"].append(old_plan)
                    plan_history_preserved = True

                    print(f"🐛 DEBUG: Plan history preserved with {len(old_plan['projects'])} projects")

                # Update user's current plan and reset cost + projects
                user["current_plan"] = plan_info["price_id"]
                user["cost"] = "$0.000"
                user["user_cost"] = "$0.000"
                user["projects"] = []  # RESET PROJECTS FOR NEW PLAN

                print(f"🐛 DEBUG: Updated to new plan: {plan_info['price_id']}")
                print(f"🐛 DEBUG: Reset projects array for new plan tracking")
                break

        if not user_found:
            raise Exception(f"User {user_id} not found in organization {tenant_id}")

        # Update the document (same as Stripe flow)
        result = self.db["llm_costs"].update_one(
            {"organization_id": tenant_id},
            {"$set": llm_cost_doc}
        )

        return {
            "matched": result.matched_count,
            "modified": result.modified_count,
            "plan_history_preserved": plan_history_preserved
        }
    
    def verify_user_upgrade(self, user_id: str, tenant_id: str = "b2c"):
        """Verify user's plan upgrade across all collections"""
        print(f"\n{'='*80}")
        print(f"USER PLAN VERIFICATION")
        print(f"{'='*80}")
        print(f"User ID: {user_id}")
        print(f"Tenant ID: {tenant_id}")
        print(f"Verification Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*80}")
        
        # Check LLM Costs
        self._check_llm_costs(user_id, tenant_id)
        
        # Check Active Subscriptions
        self._check_active_subscriptions(user_id, tenant_id)
        
        # Check Organizations (if applicable)
        self._check_organizations(tenant_id)
        
        print(f"\n{'='*80}")
        print("VERIFICATION COMPLETED")
        print(f"{'='*80}")
    
    def _check_llm_costs(self, user_id: str, tenant_id: str):
        """Check LLM costs collection"""
        print(f"\n📊 LLM COSTS COLLECTION")
        print("-" * 40)

        # Find document by organization_id (tenant_id)
        llm_doc = self.db["llm_costs"].find_one({"organization_id": tenant_id})

        if not llm_doc:
            print(f"❌ No LLM cost document found for organization {tenant_id}")
            return
        
        # Find the specific user
        user_data = None
        for user in llm_doc.get("users", []):
            if user.get("user_id") == user_id:
                user_data = user
                break
        
        if not user_data:
            print("❌ User not found in LLM costs users array")
            return
        
        print("✅ LLM cost document found")
        print(f"Organization ID: {llm_doc.get('organization_id', 'N/A')}")
        print(f"Current Plan: {user_data.get('current_plan', 'N/A')}")
        print(f"Plan Name: {PLAN_MAPPING.get(user_data.get('current_plan', ''), 'Unknown')}")
        print(f"Current Cost: {user_data.get('cost', 'N/A')}")
        print(f"User Cost: {user_data.get('user_cost', 'N/A')}")
        print(f"Projects: {len(user_data.get('projects', []))} projects")
        
        # Plan history
        plans_history = user_data.get('plans_history', [])
        print(f"Plan History: {len(plans_history)} previous plans")

        if plans_history:
            print("  Previous Plans:")
            for i, plan in enumerate(plans_history[-3:], 1):  # Show last 3 plans
                plan_name = PLAN_MAPPING.get(plan.get('plan_id', ''), 'Unknown')
                upgraded_at = plan.get('upgraded_at', 'N/A')
                cost = plan.get('cost', 'N/A')
                projects_count = len(plan.get('projects', []))
                print(f"    {i}. {plan_name} (Cost: {cost}, Projects: {projects_count}, Upgraded: {upgraded_at})")
    
    def _check_active_subscriptions(self, user_id: str, tenant_id: str):
        """Check active subscriptions collection"""
        print(f"\n🔄 ACTIVE SUBSCRIPTIONS COLLECTION")
        print("-" * 40)
        
        # Find latest subscription
        subscription = self.db["active_subscriptions"].find_one(
            {"user_id": user_id},
            sort=[("created_at", -1)]
        )
        
        if not subscription:
            print("❌ No active subscription found for user")
            return
        
        print("✅ Active subscription found")
        print(f"Subscription ID: {subscription.get('subscription_id', 'N/A')}")
        print(f"Price ID: {subscription.get('price_id', 'N/A')}")
        print(f"Plan Name: {PLAN_MAPPING.get(subscription.get('price_id', ''), 'Unknown')}")
        print(f"Credits: {subscription.get('credits', 'N/A'):,}")
        print(f"Status: {subscription.get('status', 'N/A')}")
        print(f"Current Cost: {subscription.get('current_cost', 'N/A')}")
        print(f"Created At: {subscription.get('created_at', 'N/A')}")
        print(f"Expires At: {subscription.get('expires_at', 'N/A')}")
        
        # Check if subscription is active
        try:
            expires_at = subscription.get('expires_at')
            if expires_at:
                from datetime import datetime
                expires_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                is_active = expires_date > datetime.now(expires_date.tzinfo)
                print(f"Is Active: {'✅ Yes' if is_active else '❌ Expired'}")
        except:
            print("Is Active: ❓ Unable to determine")
    
    def _check_organizations(self, tenant_id: str):
        """Check organizations collection"""
        print(f"\n🏢 ORGANIZATIONS COLLECTION")
        print("-" * 40)
        
        org = self.db["organizations"].find_one({"_id": tenant_id})
        
        if not org:
            if tenant_id == "b2c":
                print("ℹ️  B2C tenant - no organization record (normal)")
            else:
                print("❌ Organization not found")
            return
        
        print("✅ Organization found")
        print(f"Name: {org.get('name', 'N/A')}")
        print(f"Credits: {org.get('credits', 'N/A'):,}")
        print(f"Plan ID: {org.get('plan_id', 'N/A')}")
        print(f"Status: {org.get('status', 'N/A')}")
    
    def get_user_summary(self, user_id: str, tenant_id: str = "b2c") -> dict:
        """Get a summary of user's current state"""
        summary = {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "llm_costs": None,
            "active_subscription": None,
            "organization": None
        }
        
        # LLM Costs
        llm_doc = self.db["llm_costs"].find_one({"organization_id": tenant_id})
        if llm_doc:
            user_data = next((u for u in llm_doc.get("users", []) if u.get("user_id") == user_id), None)
            if user_data:
                summary["llm_costs"] = {
                    "current_plan": user_data.get("current_plan"),
                    "plan_name": PLAN_MAPPING.get(user_data.get("current_plan", ""), "Unknown"),
                    "cost": user_data.get("cost"),
                    "current_projects_count": len(user_data.get("projects", [])),
                    "plans_history_count": len(user_data.get("plans_history", [])),
                    "plans_history": [
                        {
                            "plan_id": plan.get("plan_id"),
                            "plan_name": PLAN_MAPPING.get(plan.get("plan_id", ""), "Unknown"),
                            "cost": plan.get("cost"),
                            "projects_count": len(plan.get("projects", [])),
                            "upgraded_at": plan.get("upgraded_at")
                        }
                        for plan in user_data.get("plans_history", [])
                    ]
                }
        
        # Active Subscription
        subscription = self.db["active_subscriptions"].find_one(
            {"user_id": user_id},
            sort=[("created_at", -1)]
        )
        if subscription:
            summary["active_subscription"] = {
                "price_id": subscription.get("price_id"),
                "plan_name": PLAN_MAPPING.get(subscription.get("price_id", ""), "Unknown"),
                "credits": subscription.get("credits"),
                "status": subscription.get("status"),
                "expires_at": subscription.get("expires_at")
            }
        
        # Organization
        org = self.db["organizations"].find_one({"_id": tenant_id})
        if org:
            summary["organization"] = {
                "name": org.get("name"),
                "credits": org.get("credits"),
                "plan_id": org.get("plan_id")
            }
        
        return summary

def main():
    # DEBUG MODE: Set this to True when running with Python debugger
    DEBUG_MODE = True  # Change to False for command line usage

    if DEBUG_MODE:
        # Debug configuration - modify these values for debugging
        class DebugArgs:
            user_id = "04685438-6091-704c-456d-a25e576c3cd0"
            plan = "premium_pro"
            tenant_id = "b2c"
            credits = 1100000
            verify_only = False
            json = False

        args = DebugArgs()
        print("🐛 DEBUG MODE: Using hardcoded arguments")
        print(f"   User ID: {args.user_id}")
        print(f"   Plan: {args.plan}")
        print(f"   Tenant ID: {args.tenant_id}")
        print(f"   Credits: {args.credits}")
        print()
    else:
        # Normal command line argument parsing
        parser = argparse.ArgumentParser(description="QA Plan Upgrade (Simulates Stripe Payment Flow)")
        parser.add_argument("--user-id", required=True, help="User ID to upgrade")
        parser.add_argument("--plan", required=True, help="Plan name (premium_starter, premium_advanced, premium_pro)")
        parser.add_argument("--tenant-id", default="b2c", help="Tenant ID (default: b2c)")
        parser.add_argument("--credits", type=int, help="Credits to allocate (optional)")
        parser.add_argument("--verify-only", action="store_true", help="Only verify current state, don't upgrade")
        parser.add_argument("--json", action="store_true", help="Output as JSON")

        args = parser.parse_args()

    verifier = UserUpgradeVerifier(MONGO_URI, DB_NAME)

    try:
        if args.verify_only:
            # Original verification functionality
            if args.json:
                summary = verifier.get_user_summary(args.user_id, args.tenant_id)
                print(json.dumps(summary, indent=2, default=str))
            else:
                verifier.verify_user_upgrade(args.user_id, args.tenant_id)
        else:
            # New upgrade functionality (simulates Stripe payment flow)
            result = verifier.simulate_stripe_payment_flow(args.user_id, args.plan, args.tenant_id, args.credits)

            if args.json:
                print(json.dumps(result, indent=2, default=str))
            else:
                print_upgrade_result(result)

    except Exception as e:
        print(f"❌ Operation failed: {str(e)}")
        return 1

    finally:
        verifier.close()

    return 0

def print_upgrade_result(result):
    """Print upgrade result in human-readable format"""
    print(f"\n{'='*80}")
    print("QA PLAN UPGRADE RESULT (Stripe Payment Flow Simulation)")
    print(f"{'='*80}")
    print(f"User ID: {result['user_id']}")
    print(f"Plan: {result['plan_name']}")
    print(f"Credits: {result['credits']:,}")
    print(f"Status: {'✅ SUCCESS' if result['success'] else '❌ FAILED'}")

    if result['success']:
        print(f"\n📊 Collections Updated:")
        for collection, update_info in result['updates'].items():
            status = "✅" if update_info['modified'] > 0 else "⚠️"
            print(f"  {status} {collection}: matched={update_info['matched']}, modified={update_info['modified']}")

        if result.get('plan_history_preserved'):
            print(f"\n📚 Plan History: Previous plan preserved in history")

    if result['errors']:
        print(f"\n❌ Errors:")
        for error in result['errors']:
            print(f"  - {error}")

    print(f"\n{'='*80}")

if __name__ == "__main__":
    sys.exit(main())


#  python3 examples/verify_user_upgrade.py --user-id "d4b8c438-1041-70fd-959f-3e5522a8bc79" --plan "premium_pro" --tenant-id "b2c" --credits 1100000