import httpx
from typing import List, Dict, Optional
from fastapi import HTTPException
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def fetch_gitlab_branches(
    owner: str, 
    repo_name: str, 
    gitlab_instance: str = "gitlab.com",
    access_token: Optional[str] = None
) -> List[Dict]:
    """
    Fetch branches from GitLab repository (supports both public and private repos).
    Uses Bearer authentication which works for your setup.
    
    Args:
        owner (str): Repository owner/namespace
        repo_name (str): Repository name
        gitlab_instance (str): GitLab instance domain
        access_token (str, optional): GitLab access token (required for private repos)
    
    Returns:
        List[Dict]: List of branches in GitHub-compatible format
    """
    if not owner or not repo_name:
        raise HTTPException(
            status_code=400,
            detail="Owner and repository name are required"
        )
    
    # Properly URL-encode the project path for GitLab API
    project_path = f"{owner}/{repo_name}".replace('/', '%2F')
    url = f"https://{gitlab_instance}/api/v4/projects/{project_path}/repository/branches?per_page=100"
    
    # Setup headers - use Bearer auth which works for your token
    headers = {
        'User-Agent': 'GitLab-Branches-Fetcher/1.0',
        'Accept': 'application/json'
    }
    
    if access_token:
        # Use Bearer authentication (this works for your setup)
        headers['Authorization'] = f'Bearer {access_token}'
    
    all_branches = []
    
    try:
        async with httpx.AsyncClient(
            timeout=30.0,
            headers=headers,
            follow_redirects=True
        ) as client:
            
            while url:
                logger.info(f"Fetching: {url}")
                
                try:
                    response = await client.get(url)
                    
                    if response.status_code == 404:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Repository {owner}/{repo_name} not found on {gitlab_instance}"
                        )
                    
                    if response.status_code == 401:
                        raise HTTPException(
                            status_code=401,
                            detail="Authentication required. Please provide a valid access token."
                        )
                    
                    if response.status_code == 403:
                        raise HTTPException(
                            status_code=403,
                            detail="Access forbidden. Check token permissions."
                        )
                    
                    response.raise_for_status()
                    gitlab_branches = response.json()
                    
                    # Convert GitLab format to GitHub-compatible format
                    for branch in gitlab_branches:
                        commit_info = branch.get("commit", {})
                        converted_branch = {
                            "name": branch.get("name", ""),
                            "protected": branch.get("protected", False),
                            "commit": {
                                "sha": commit_info.get("id", ""),
                                "url": commit_info.get("web_url", "")
                            }
                        }
                        all_branches.append(converted_branch)
                    
                    # Handle pagination
                    url = None
                    if 'link' in response.headers:
                        links = response.headers['link'].split(',')
                        for link in links:
                            if 'rel="next"' in link:
                                url = link.split(';')[0].strip()[1:-1]
                                break
                    
                    # Safety check
                    if len(gitlab_branches) < 100:
                        break
                        
                except httpx.HTTPStatusError as e:
                    raise HTTPException(
                        status_code=e.response.status_code,
                        detail=f"GitLab API error: {e.response.status_code} - {e.response.text}"
                    )
        
        logger.info(f"Successfully fetched {len(all_branches)} branches")
        return all_branches
        
    except httpx.RequestError as e:
        logger.error(f"Network error: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to GitLab instance {gitlab_instance}"
        )


# Simple test function
async def test_branches_fetch():
    """Test the fixed branches fetch function with your repository"""
    
    # Replace with your actual token
    ACCESS_TOKEN = "1e7f25fdf40a0d5d9405ffec87fa57bb82ed46d2b413d680b92a9df9050c3a34"
    
    try:
        print("🚀 Testing fixed GitLab branches fetcher...")
        
        branches = await fetch_gitlab_branches(
            owner="gnachman",
            repo_name="iterm2"
        )
        
        print(f"✅ SUCCESS! Found {len(branches)} branches:")
        
        for i, branch in enumerate(branches, 1):
            protected_status = "🔒" if branch['protected'] else "🔓"
            commit_short = branch['commit']['sha'][:8]
            print(f"   {i}. {protected_status} {branch['name']} (commit: {commit_short})")
        
        return branches
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None


# Alternative function without FastAPI dependency
async def fetch_gitlab_branches_simple(
    owner: str, 
    repo_name: str, 
    access_token: str,
    gitlab_instance: str = "gitlab.com"
) -> List[Dict]:
    """
    Simple version without FastAPI HTTPException dependency.
    Uses Bearer authentication which works for your token.
    """
    project_path = f"{owner}/{repo_name}".replace('/', '%2F')
    url = f"https://{gitlab_instance}/api/v4/projects/{project_path}/repository/branches?per_page=100"
    
    headers = {
        'User-Agent': 'GitLab-Branches-Fetcher/1.0',
        'Accept': 'application/json',
        'Authorization': f'Bearer {access_token}'  # Use Bearer - this works for your setup
    }
    
    all_branches = []
    
    async with httpx.AsyncClient(timeout=30.0, headers=headers) as client:
        while url:
            response = await client.get(url)
            
            if response.status_code == 404:
                raise FileNotFoundError(f"Repository {owner}/{repo_name} not found")
            
            if response.status_code == 401:
                raise PermissionError("Authentication failed - check your token")
            
            if response.status_code == 403:
                raise PermissionError("Access forbidden - check token permissions")
            
            response.raise_for_status()
            gitlab_branches = response.json()
            
            # Convert to GitHub-compatible format
            for branch in gitlab_branches:
                commit_info = branch.get("commit", {})
                converted_branch = {
                    "name": branch.get("name", ""),
                    "protected": branch.get("protected", False),
                    "commit": {
                        "sha": commit_info.get("id", ""),
                        "url": commit_info.get("web_url", "")
                    }
                }
                all_branches.append(converted_branch)
            
            # Handle pagination
            url = None
            if 'link' in response.headers:
                links = response.headers['link'].split(',')
                for link in links:
                    if 'rel="next"' in link:
                        url = link.split(';')[0].strip()[1:-1]
                        break
            
            if len(gitlab_branches) < 100:
                break
    
    return all_branches


# For immediate testing
if __name__ == "__main__":
    import asyncio
    asyncio.run(test_branches_fetch())