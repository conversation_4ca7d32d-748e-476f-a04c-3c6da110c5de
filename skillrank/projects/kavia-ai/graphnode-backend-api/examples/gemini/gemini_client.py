#!/usr/bin/env python3
"""
Optimized Gemini 2.5 Pro client using google-genai SDK.
Requires: pip install google-genai
"""

import os
import asyncio
import logging
from typing import List, Optional, Generator, AsyncGenerator
from google import genai
from google.genai import types

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GeminiClient:
    """Optimized Gemini client with async support and conversation management."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gemini-2.5-pro-preview-05-06"):
        """
        Initialize Gemini client.
        
        Args:
            api_key: Gemini API key. Falls back to GEMINI_API_KEY env var.
            model: Model name to use.
        """
        self.api_key = api_key or os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError(
                "API key required. Set GEMINI_API_KEY environment variable or pass api_key parameter. "
                "Get your key from: https://aistudio.google.com/app/apikey"
            )
        
        self.model = model
        self.client = genai.Client(api_key=self.api_key)
        self.conversation_history: List[types.Content] = []
        
        logger.info(f"Initialized Gemini client with model: {model}")
    
    def add_system_message(self, system_prompt: str) -> None:
        """Add a system message to guide the conversation."""
        self.conversation_history.append(
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=f"System: {system_prompt}")]
            )
        )
    
    def add_message(self, role: str, content: str) -> None:
        """
        Add a message to conversation history.
        
        Args:
            role: "user" or "model"
            content: Message content
        """
        if role not in ["user", "model"]:
            raise ValueError("Role must be 'user' or 'model'")
        
        self.conversation_history.append(
            types.Content(
                role=role,
                parts=[types.Part.from_text(text=content)]
            )
        )
    
    def clear_history(self) -> None:
        """Clear conversation history."""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")
    
    def generate_stream(
        self, 
        prompt: str, 
        temperature: float = 0.7,
        max_output_tokens: int = 4096,
        use_history: bool = True
    ) -> Generator[str, None, None]:
        """
        Generate streaming response.
        
        Args:
            prompt: User input
            temperature: Sampling temperature (0.0 to 2.0)
            max_output_tokens: Maximum tokens to generate
            use_history: Whether to include conversation history
        
        Yields:
            Response chunks
        """
        try:
            # Prepare contents
            contents = self.conversation_history.copy() if use_history else []
            contents.append(
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=prompt)]
                )
            )
            
            config = types.GenerateContentConfig(
                response_mime_type="text/plain",
                temperature=temperature,
                max_output_tokens=max_output_tokens
            )
            
            full_response = ""
            for chunk in self.client.models.generate_content_stream(
                model=self.model,
                contents=contents,
                config=config
            ):
                if chunk.text:
                    full_response += chunk.text
                    yield chunk.text
            
            # Add to history if using history
            if use_history:
                self.add_message("user", prompt)
                self.add_message("model", full_response)
                
        except Exception as e:
            logger.error(f"Streaming generation failed: {e}")
            raise
    
    async def generate_async(
        self, 
        prompt: str,
        temperature: float = 0.7,
        max_output_tokens: int = 4096,
        use_history: bool = True
    ) -> str:
        """
        Generate response asynchronously.
        
        Args:
            prompt: User input
            temperature: Sampling temperature
            max_output_tokens: Maximum tokens to generate
            use_history: Whether to include conversation history
            
        Returns:
            Complete response text
        """
        return await asyncio.get_event_loop().run_in_executor(
            None, 
            lambda: "".join(self.generate_stream(prompt, temperature, max_output_tokens, use_history))
        )
    
    def generate(
        self, 
        prompt: str,
        temperature: float = 0.7,
        max_output_tokens: int = 4096,
        use_history: bool = True
    ) -> str:
        """
        Generate complete response synchronously.
        
        Args:
            prompt: User input
            temperature: Sampling temperature
            max_output_tokens: Maximum tokens to generate
            use_history: Whether to include conversation history
            
        Returns:
            Complete response text
        """
        return "".join(self.generate_stream(prompt, temperature, max_output_tokens, use_history))
    
    def chat_interactive(self):
        """Start an interactive chat session."""
        print("🤖 Gemini 2.5 Pro Interactive Chat")
        print("Type 'quit', 'exit', or 'bye' to end the conversation")
        print("Type 'clear' to clear conversation history")
        print("Type 'history' to see conversation history")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'clear':
                    self.clear_history()
                    print("🧹 History cleared!")
                    continue
                
                if user_input.lower() == 'history':
                    self._print_history()
                    continue
                
                if not user_input:
                    continue
                
                print("🤖 Gemini: ", end="", flush=True)
                for chunk in self.generate_stream(user_input):
                    print(chunk, end="", flush=True)
                print()  # New line after response
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                logger.error(f"Chat error: {e}")
                print(f"❌ Error: {e}")
    
    def _print_history(self):
        """Print conversation history."""
        if not self.conversation_history:
            print("📝 No conversation history")
            return
        
        print("📝 Conversation History:")
        for i, content in enumerate(self.conversation_history):
            role_emoji = "👤" if content.role == "user" else "🤖"
            text = content.parts[0].text if content.parts else ""
            print(f"{i+1}. {role_emoji} {content.role}: {text[:100]}{'...' if len(text) > 100 else ''}")

def main():
    """Main function with usage examples."""
    try:
        # Initialize client
        client = GeminiClient()
        
        # Example 1: Simple generation
        print("🔄 Example 1: Simple Generation")
        response = client.generate("Explain quantum computing in 2 sentences.")
        print(f"Response: {response}\n")
        
        # Example 2: Streaming response
        print("🔄 Example 2: Streaming Response")
        print("Question: What are the benefits of renewable energy?")
        print("Streaming response: ", end="", flush=True)
        for chunk in client.generate_stream("What are the benefits of renewable energy?"):
            print(chunk, end="", flush=True)
        print("\n")
        
        # Example 3: Conversation with history
        print("🔄 Example 3: Conversation with History")
        client.clear_history()
        client.add_system_message("You are a helpful coding assistant. Keep responses concise.")
        
        response1 = client.generate("What is Python?")
        print(f"Q1: What is Python?\nA1: {response1[:100]}...\n")
        
        response2 = client.generate("What are its main advantages?")
        print(f"Q2: What are its main advantages?\nA2: {response2[:100]}...\n")
        
        # Example 4: Different temperature settings
        print("🔄 Example 4: Temperature Comparison")
        prompts = "Write a creative opening line for a story about time travel."
        
        print("Low temperature (0.2):")
        low_temp = client.generate(prompts, temperature=0.2, use_history=False)
        print(f"  {low_temp}\n")
        
        print("High temperature (1.5):")
        high_temp = client.generate(prompts, temperature=1.5, use_history=False)
        print(f"  {high_temp}\n")
        
        # Start interactive chat
        print("🔄 Starting Interactive Chat...")
        client.clear_history()
        client.chat_interactive()
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        print(f"❌ Error: {e}")
        print("\n💡 Setup Instructions:")
        print("1. Get API key from: https://aistudio.google.com/app/apikey")
        print("2. Set environment variable: export GEMINI_API_KEY='your-key-here'")
        print("3. Install dependencies: pip install google-genai")

async def async_example():
    """Example of async usage."""
    try:
        client = GeminiClient()
        
        # Multiple async requests
        tasks = [
            client.generate_async("What is machine learning?"),
            client.generate_async("What is artificial intelligence?", use_history=False),
            client.generate_async("What is deep learning?", use_history=False)
        ]
        
        responses = await asyncio.gather(*tasks)
        
        for i, response in enumerate(responses, 1):
            print(f"Async Response {i}: {response[:100]}...\n")
    
    except Exception as e:
        logger.error(f"Async example failed: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--async":
        asyncio.run(async_example())
    else:
        main()