import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Generator, AsyncGenerator
import litellm
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VertexGeminiClient:
    """Optimized Vertex AI Gemini client with streaming support."""
    
    def __init__(
        self, 
        vertex_secret: Dict[str, Any],
        project_id: str = "vertex-ai-sandbox-447902",
        location_id: str = "us-central1",
        model_name: str = "gemini-2.5-pro-preview-05-06"
    ):
        """
        Initialize Vertex AI Gemini client.
        
        Args:
            vertex_secret: Service account credentials dictionary
            project_id: GCP project ID
            location_id: Vertex AI location
            model_name: Model name to use
        """
        self.vertex_secret = vertex_secret
        self.project_id = project_id
        self.location_id = location_id
        self.model_name = model_name
        
        # Configure litellm
        litellm.vertex_project = self.project_id
        litellm.vertex_location = self.location_id
        
        # Validate credentials
        if isinstance(vertex_secret, str):
            self.vertex_secret = json.loads(vertex_secret)
        
        logger.info(f"Initialized Vertex Gemini client - Project: {project_id}, Model: {model_name}")
    
    def stream_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> Generator[str, None, None]:
        """
        Stream completion synchronously.
        
        Args:
            messages: List of message dictionaries
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Yields:
            Response chunks as strings
        """
        try:
            completion_kwargs = {
                "vertex_credentials": self.vertex_secret,
                "stream": True,
                "temperature": temperature,
                "max_tokens": max_tokens,
                **kwargs
            }
            
            response_stream = litellm.completion(
                model=self.model_name,
                messages=messages,
                **completion_kwargs
            )
            
            for chunk in response_stream:
                if hasattr(chunk, 'choices') and chunk.choices:
                    if chunk.choices[0].delta and chunk.choices[0].delta.content:
                        yield chunk.choices[0].delta.content
                        
        except Exception as e:
            logger.error(f"Streaming completion failed: {e}")
            raise
    
    async def astream_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Stream completion asynchronously.
        
        Args:
            messages: List of message dictionaries
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Yields:
            Response chunks as strings
        """
        try:
            completion_kwargs = {
                "vertex_credentials": self.vertex_secret,
                "stream": True,
                "temperature": temperature,
                "max_tokens": max_tokens,
                **kwargs
            }
            
            response_stream = await litellm.acompletion(
                model=self.model_name,
                messages=messages,
                **completion_kwargs
            )
            
            async for chunk in response_stream:
                if hasattr(chunk, 'choices') and chunk.choices:
                    if chunk.choices[0].delta and chunk.choices[0].delta.content:
                        yield chunk.choices[0].delta.content
                        
        except Exception as e:
            logger.error(f"Async streaming completion failed: {e}")
            raise
    
    def get_complete_response(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> str:
        """
        Get complete response by collecting all streaming chunks.
        
        Args:
            messages: List of message dictionaries
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Returns:
            Complete response text
        """
        complete_response = ""
        for chunk in self.stream_completion(messages, temperature, max_tokens, **kwargs):
            complete_response += chunk
        return complete_response
    
    async def aget_complete_response(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> str:
        """
        Get complete response asynchronously.
        
        Args:
            messages: List of message dictionaries
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Returns:
            Complete response text
        """
        complete_response = ""
        async for chunk in self.astream_completion(messages, temperature, max_tokens, **kwargs):
            complete_response += chunk
        return complete_response
    
    def chat_stream(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> Generator[str, None, None]:
        """
        Simple chat streaming interface.
        
        Args:
            prompt: User prompt
            system_message: Optional system message
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Yields:
            Response chunks
        """
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        messages.append({"role": "user", "content": prompt})
        
        yield from self.stream_completion(messages, temperature, max_tokens, **kwargs)
    
    def interactive_chat(self):
        """Start an interactive streaming chat session."""
        print("🤖 Vertex AI Gemini 2.5 Pro Interactive Chat")
        print("Type 'quit', 'exit', or 'bye' to end")
        print("Type 'clear' to start fresh conversation")
        print("-" * 50)
        
        conversation_history = []
        
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'clear':
                    conversation_history.clear()
                    print("🧹 Conversation cleared!")
                    continue
                
                if not user_input:
                    continue
                
                # Add user message to history
                conversation_history.append({"role": "user", "content": user_input})
                
                print("🤖 Gemini: ", end="", flush=True)
                
                # Stream response
                assistant_response = ""
                for chunk in self.stream_completion(conversation_history):
                    print(chunk, end="", flush=True)
                    assistant_response += chunk
                
                print()  # New line after response
                
                # Add assistant response to history
                conversation_history.append({"role": "assistant", "content": assistant_response})
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                logger.error(f"Chat error: {e}")
                print(f"\n❌ Error: {e}")

# Initialize with your credentials
vertex_secret = ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

def main():
    """Example usage of the streaming client."""
    try:
        # Initialize client
        client = VertexGeminiClient(vertex_secret)
        
        # Example 1: Simple streaming
        print("🔄 Example 1: Simple Streaming")
        messages = [{"role": "user", "content": "What is your name? Are you gemini 2.5 pro?"}]
        
        print("Streaming response: ", end="", flush=True)
        for chunk in client.stream_completion(messages):
            print(chunk, end="", flush=True)
        print("\n")
        
        # Example 2: Chat streaming with system message
        print("🔄 Example 2: Chat Streaming with System Message")
        print("Question: Explain quantum computing")
        print("Response: ", end="", flush=True)
        
        for chunk in client.chat_stream(
            "Explain quantum computing in simple terms",
            system_message="You are a helpful physics teacher. Keep explanations clear and concise.",
            temperature=0.3
        ):
            print(chunk, end="", flush=True)
        print("\n")
        
        # Example 3: Get complete response
        print("🔄 Example 3: Complete Response Collection")
        complete_response = client.get_complete_response([
            {"role": "user", "content": "List 3 benefits of renewable energy"}
        ])
        print(f"Complete response: {complete_response}\n")
        
        # Example 4: Interactive chat
        print("🔄 Starting Interactive Chat...")
        client.interactive_chat()
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        print(f"❌ Error: {e}")

async def async_example():
    """Example of async streaming."""
    try:
        client = VertexGeminiClient(vertex_secret)
        
        print("🔄 Async Streaming Example")
        messages = [{"role": "user", "content": "Write a short poem about AI"}]
        
        print("Async streaming: ", end="", flush=True)
        async for chunk in client.astream_completion(messages, temperature=0.8):
            print(chunk, end="", flush=True)
        print("\n")
        
        # Multiple concurrent requests
        print("🔄 Multiple Concurrent Requests")
        tasks = [
            client.aget_complete_response([{"role": "user", "content": "What is Python?"}]),
            client.aget_complete_response([{"role": "user", "content": "What is JavaScript?"}]),
            client.aget_complete_response([{"role": "user", "content": "What is Go?"}])
        ]
        
        responses = await asyncio.gather(*tasks)
        for i, response in enumerate(responses, 1):
            print(f"Response {i}: {response[:100]}...\n")
    
    except Exception as e:
        logger.error(f"Async example failed: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--async":
        asyncio.run(async_example())
    else:
        main()