import os
import yaml
import shutil
import json
import re

# Common directories and files to ignore (similar to .gitignore)
IGNORED_DIRS = [
    'node_modules',
    'venv',
    'env',
    '.git',
    '__pycache__',
    'build',
    'dist',
    '.cache',
    '.vscode',
    '.idea',
    'coverage',
    'tmp',
    'temp',
    'logs'
]

IGNORED_FILE_EXTENSIONS = [
    '.pyc',
    '.pyo',
    '.pyd',
    '.so',
    '.dll',
    '.exe',
    '.obj',
    '.o',
    '.a',
    '.lib',
    '.log',
    '.DS_Store',
    '.env',
    '.swp',
    '.swo',
    '.coverage',
    '.pytest_cache',
    '.egg-info'
]

def get_container_workspace_mapping(manifest_path):
    """
    Extract container names and their corresponding workspace names from a project configuration file.
    
    Args:
        manifest_path (str): Path to the manifest file
        
    Returns:
        dict: Mapping of container names to workspace names
    """
    try:
        with open(manifest_path, 'r') as file:
            config = yaml.safe_load(file)
        
        container_mapping = {}
        if 'containers' in config and isinstance(config['containers'], list):
            for container in config['containers']:
                if 'container_name' in container and 'workspace' in container:
                    container_name = container['container_name']
                    workspace_name = container['workspace']
                    container_mapping[container_name] = workspace_name
        
        return container_mapping
    except Exception as e:
        print(f"Error processing manifest file: {str(e)}")
        return {}

def clean_filename(filename, container_name, workspace_name):
    """
    Extract the actual filename by removing workspace and container prefixes.
    
    Args:
        filename (str): Original filename
        container_name (str): Container name from manifest
        workspace_name (str): Workspace name from manifest
        
    Returns:
        str: Cleaned filename
    """
    # Create the pattern based on workspace/container format
    pattern = f"{workspace_name}_{container_name}_"
    
    # If the pattern exists in the filename, extract everything after it
    if pattern in filename:
        return filename.replace(pattern, "")
    
    # If the exact pattern isn't found, try a more generic approach
    parts = filename.split('_')
    for i, part in enumerate(parts):
        if part == workspace_name and i+1 < len(parts) and parts[i+1] == container_name:
            if i+2 < len(parts):
                return "_".join(parts[i+2:])
    
    # If still no match, return the original filename
    return filename

def clean_path_in_filename(path, container_name, workspace_name):
    """
    Clean a path by removing workspace and container name prefixes.
    
    Args:
        path (str): Original path
        container_name (str): Container name
        workspace_name (str): Workspace name
        
    Returns:
        str: Cleaned path
    """
    # Pattern to remove: workspace_name/container_name/
    pattern = f"{workspace_name}/{container_name}/"
    
    # Alternative pattern with underscore: workspace_name_container_name/
    alt_pattern = f"{workspace_name}_{container_name}/"
    
    # Clean the path
    if pattern in path:
        return path.replace(pattern, "")
    elif alt_pattern in path:
        return path.replace(alt_pattern, "")
    
    # Try to clean path with different separators
    pattern2 = f"{workspace_name}_{container_name}_"
    if pattern2 in path:
        return path.replace(pattern2, "")
    
    return path

def should_ignore_path(path):
    """
    Check if a file or directory should be ignored.
    
    Args:
        path (str): Path to check
        
    Returns:
        bool: True if the path should be ignored, False otherwise
    """
    # Get the basename of the path
    basename = os.path.basename(path)
    
    # Check if it's a directory that should be ignored
    if os.path.isdir(path) and basename in IGNORED_DIRS:
        return True
    
    # Check if it's a file with an extension that should be ignored
    _, ext = os.path.splitext(basename)
    if ext in IGNORED_FILE_EXTENSIONS:
        return True
    
    # Check for specific filenames to ignore
    if basename in ['.DS_Store', 'Thumbs.db', '.env', '.gitignore', '.npmrc', 'yarn.lock']:
        return True
    
    return False

def process_knowledge_file(file_path, dest_path, container_name, workspace_name):
    """
    Process a knowledge file by copying it and modifying the "filename" field in its JSON content.
    
    Args:
        file_path (str): Source file path
        dest_path (str): Destination file path
        container_name (str): Container name
        workspace_name (str): Workspace name
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            # Parse the JSON content
            data = json.loads(content)
            
            # Update the "filename" field if it exists
            if 'filename' in data:
                original_filename = data['filename']
                cleaned_filename = clean_path_in_filename(original_filename, container_name, workspace_name)
                data['filename'] = cleaned_filename
                print(f"  Updated filename in JSON: {original_filename} → {cleaned_filename}")
            
            # Write the updated content to the destination file
            with open(dest_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
            
            return True
        except json.JSONDecodeError:
            # If it's not valid JSON, just copy the file as is
            shutil.copy2(file_path, dest_path)
            print(f"  File is not valid JSON, copied as is: {os.path.basename(file_path)}")
            return True
            
    except Exception as e:
        print(f"Error processing knowledge file {file_path}: {str(e)}")
        # Copy the original file as fallback
        shutil.copy2(file_path, dest_path)
        return False

def organize_project_files_to_efs(source_project_path, tenant_id, project_id):
    """
    Organize knowledge files and project files by container name and copy them to EFS destination,
    skipping files and directories that are typically ignored in Git repositories.
    Also updates "filename" fields in knowledge file JSON content.
    
    Args:
        source_project_path (str): Path to the project root directory
        tenant_id (str): Tenant ID (e.g., 'T0005')
        project_id (str): Project ID (e.g., '85948')
    
    Returns:
        dict: Summary of copied files by container
    """
    try:
        # Construct paths
        knowledge_source_path = os.path.join(source_project_path, '.knowledge')
        manifest_path = os.path.join(source_project_path, '.project_manifest.yaml')
        efs_destination = f"/efs/{tenant_id}/{project_id}"
        
        # Verify source paths exist
        if not os.path.exists(source_project_path) or not os.path.isdir(source_project_path):
            print(f"Source project path {source_project_path} does not exist or is not a directory.")
            return {}
            
        if not os.path.exists(manifest_path):
            print(f"Manifest file {manifest_path} does not exist.")
            return {}
        
        # Get container to workspace mapping
        container_mapping = get_container_workspace_mapping(manifest_path)
        if not container_mapping:
            print("No container mapping found in manifest.")
            return {}
        
        # Create the base EFS destination directory if it doesn't exist
        os.makedirs(efs_destination, exist_ok=True)
        
        # Track copied files
        copied_files = {container: {"knowledge": [], "project": []} for container in container_mapping.keys()}
        skipped_files = {container: {"knowledge": [], "project": []} for container in container_mapping.keys()}
        
        # PART 1: Process knowledge files
        if os.path.exists(knowledge_source_path) and os.path.isdir(knowledge_source_path):
            print(f"Processing knowledge files from {knowledge_source_path}...")
            
            for filename in os.listdir(knowledge_source_path):
                file_path = os.path.join(knowledge_source_path, filename)
                
                # Skip directories within .knowledge (like .vector_db) and ignored files
                if os.path.isdir(file_path) or should_ignore_path(file_path):
                    if os.path.isdir(file_path):
                        print(f"Skipping directory: {filename}")
                    else:
                        print(f"Skipping ignored file: {filename}")
                    continue
                    
                # Find which container this file belongs to
                matching_container = None
                for container in container_mapping.keys():
                    # Match files that contain the container name
                    if container in filename:
                        matching_container = container
                        break
                
                # If file matches a container, copy it to the appropriate destination
                if matching_container:
                    workspace_name = container_mapping[matching_container]
                    
                    # Create container path directly (without workspace)
                    container_efs_path = os.path.join(efs_destination, matching_container)
                    container_knowledge_dir = os.path.join(container_efs_path, ".knowledge")
                    
                    # Create the destination directories
                    os.makedirs(container_knowledge_dir, exist_ok=True)
                    
                    # Clean the filename using the workspace/container pattern
                    clean_name = clean_filename(filename, matching_container, workspace_name)
                    
                    # Destination path for the file
                    dest_path = os.path.join(container_knowledge_dir, clean_name)
                    
                    # Process the knowledge file (update JSON content and copy)
                    success = process_knowledge_file(file_path, dest_path, matching_container, workspace_name)
                    if success:
                        copied_files[matching_container]["knowledge"].append((filename, clean_name))
                        print(f"Processed knowledge file: {filename} → {container_knowledge_dir}/{clean_name}")
        
        # PART 2: Process project files for each container
        print("Processing project files...")
        for container, workspace in container_mapping.items():
            # Source container directory path
            container_workspace_path = os.path.join(source_project_path, f"{workspace}")
            container_source_path = os.path.join(container_workspace_path, container)
            
            # Check if the container directory exists
            if not os.path.exists(container_source_path) or not os.path.isdir(container_source_path):
                print(f"Container directory {container_source_path} not found, skipping.")
                continue
                
            # Destination container directory in EFS
            container_efs_path = os.path.join(efs_destination, container)
            
            # Create the destination directory
            os.makedirs(container_efs_path, exist_ok=True)
            
            # Copy the entire container directory structure, excluding ignored directories
            print(f"Copying files from {container_source_path} to {container_efs_path}...")
            
            for root, dirs, files in os.walk(container_source_path):
                # Filter out ignored directories
                dirs[:] = [d for d in dirs if d not in IGNORED_DIRS]
                
                # Get relative path from container source
                rel_path = os.path.relpath(root, container_source_path)
                
                # Create corresponding directory in destination
                if rel_path != '.':
                    dest_dir = os.path.join(container_efs_path, rel_path)
                    os.makedirs(dest_dir, exist_ok=True)
                else:
                    dest_dir = container_efs_path
                
                # Copy each file, excluding ignored files
                for file in files:
                    source_file = os.path.join(root, file)
                    
                    # Skip ignored files
                    if should_ignore_path(source_file):
                        rel_file_path = os.path.join(rel_path, file) if rel_path != '.' else file
                        skipped_files[container]["project"].append(rel_file_path)
                        continue
                    
                    dest_file = os.path.join(dest_dir, file)
                    
                    # Copy the file
                    shutil.copy2(source_file, dest_file)
                    
                    # Get the relative path for reporting
                    rel_file_path = os.path.join(rel_path, file) if rel_path != '.' else file
                    copied_files[container]["project"].append(rel_file_path)
                    
                    print(f"Copied project file: {rel_file_path} to {container}")
        
        # Print summary of skipped files
        print("\nSummary of skipped files:")
        total_skipped = 0
        for container, types in skipped_files.items():
            container_skipped = len(types["knowledge"]) + len(types["project"])
            if container_skipped > 0:
                total_skipped += container_skipped
                print(f"  {container}: {container_skipped} files skipped")
        
        print(f"\nTotal files skipped: {total_skipped}")
        
        return copied_files
            
    except Exception as e:
        print(f"Error organizing files: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}
    
# Example usage
source_project_path = '/home/<USER>/workspace/kavia/kavia/codegen-snake_game'
tenant_id = 'T0005'
project_id = '85948'

result = organize_project_files_to_efs(source_project_path, tenant_id, project_id)

# Print summary
for container, files in result.items():
    print(f"\n== {container} ==")
    
    if files["knowledge"]:
        print(f"\nKnowledge files ({len(files['knowledge'])}):")
        for orig_name, new_name in files["knowledge"]:
            print(f"  - {orig_name} → {new_name}")
    
    if files["project"]:
        print(f"\nProject files ({len(files['project'])}):")
        for file_path in files["project"][:5]:  # Show first 5 files
            print(f"  - {file_path}")
        
        if len(files["project"]) > 5:
            print(f"  ... and {len(files['project']) - 5} more files")