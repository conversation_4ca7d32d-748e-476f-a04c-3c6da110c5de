import subprocess
import os
import time

def run_command(command, check=True):
    """
    Executes a shell command and prints the output in real-time.

    Args:
        command (list): A list of strings representing the command and its arguments.
        check (bool, optional): If True, raises a CalledProcessError if the command fails.
                                 Defaults to True.

    Returns:
        str: The output of the command.

    Raises:
        subprocess.CalledProcessError: If the command fails and check is True.
    """
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
    output = ""
    while True:
        line = process.stdout.readline()
        if not line:
            break
        print(line.strip())  # Print the output in real-time
        output += line
    process.wait()
    if check:
        if process.returncode != 0:
            raise subprocess.CalledProcessError(process.returncode, command, output=output)
    return output



def repo_init_and_sync(manifest_url, manifest_branch, manifest_name, repo_path):
    """
    Initializes a repo and syncs it.

    Args:
        manifest_url (str): The URL of the manifest repository.
        manifest_branch (str): The branch of the manifest repository.
        manifest_name (str): The name of the manifest file.
        repo_path (str): The path where the repo should be initialized and synced.
    """
    try:
        # Create the repository directory if it doesn't exist
        if not os.path.exists(repo_path):
            os.makedirs(repo_path)
        os.chdir(repo_path)  # Change the current directory to the repo path.

        # Initialize the repo
        init_command = ['repo', 'init', '-u', manifest_url, '-b', manifest_branch, '-m', manifest_name]
        print(f"Initializing repo in {repo_path}...")
        run_command(init_command)

        # Configure user identity (replace with your actual email and name)
        config_name_command = ['repo', 'config', '--global', 'user.name', 'esakkiraja100116']
        config_email_command = ['repo', 'config', '--global', 'user.email', '<EMAIL>']

        run_command(config_name_command)
        run_command(config_email_command)
        
        # Choose to enable color output.  Here, we just force 'y'
        # to avoid interactive prompts, but in a real script,
        # you might want to handle this more gracefully.
        try:
            run_command(['repo', 'diff'])
        except subprocess.CalledProcessError:
            print("repo diff failed, assuming no tty")
        else:
            print("Enabling color display...")
            process = subprocess.Popen(['repo', 'color'], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            process.communicate(input='y\n')  # Simulate typing 'y' and pressing Enter
            process.wait()
            

        # Sync the repo
        sync_command = ['repo', 'sync']
        print("Syncing repo...")
        start_time = time.time()
        run_command(sync_command)
        end_time = time.time()
        print(f"Repo sync completed in {end_time - start_time:.2f} seconds.")

        print("Repo initialization and synchronization complete.")

    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        print(f"Command '{' '.join(e.cmd)}' failed with exit code {e.returncode}")
        # Optionally, print the output/stderr for more details:
        # print(e.output)

if __name__ == "__main__":
    # Example usage with a small manifest (replace with your desired manifest)
    manifest_url = "https://android.googlesource.com/mirror/manifest"  # AOSP mirror
    manifest_branch = "master"
    manifest_name = "default.xml"
    repo_path = "./my_android_repo"  # The directory where you want to store the synced files

    repo_init_and_sync(manifest_url, manifest_branch, manifest_name, repo_path)
    print(f"Files are synced at {repo_path}")
