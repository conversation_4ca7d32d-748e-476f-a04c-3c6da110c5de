#!/bin/bash
# neo4j_db_restore.sh - <PERSON><PERSON>t to restore Neo4j database from backup
# Usage: ./neo4j_db_restore.sh <database_name> [backup_path]

# Exit on any error
set -e

# Check for required tools and install if needed
function check_required_tools {
    # Check for curl
    if ! command -v curl &> /dev/null; then
        echo "Installing curl..."
        apt-get update && apt-get install -y curl
        
        if ! command -v curl &> /dev/null; then
            echo "Failed to install curl. Please install it manually."
            exit 1
        fi
    fi
    
    # Check for unzip
    if ! command -v unzip &> /dev/null; then
        echo "Installing unzip..."
        apt-get update && apt-get install -y unzip
        
        if ! command -v unzip &> /dev/null; then
            echo "Failed to install unzip. Please install it manually."
            exit 1
        fi
    fi
}

# Check for AWS CLI
function check_aws_cli {
    if ! command -v aws &> /dev/null; then
        echo "Installing AWS CLI..."
        # First ensure curl and unzip are installed
        check_required_tools
        
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
        unzip -q awscliv2.zip
        ./aws/install
        rm -rf aws awscliv2.zip
        
        # Verify installation
        if ! command -v aws &> /dev/null; then
            echo "Failed to install AWS CLI. Please install it manually."
            exit 1
        fi
    fi
}

# Function to display usage information
function show_usage {
    echo "Usage: $0 <database_name> [backup_path]"
    echo "Example: $0 dev /tmp/neo4j-developt0000.backup/developt0000-2025-05-16T04-22-55.backup"
    exit 1
}

# Check if database name is provided
if [ $# -lt 1 ]; then
    echo "Error: Database name is required!"
    show_usage
fi

DB_NAME="$1"
BACKUP_PATH="$2"
echo "=== Starting restoration for database: ${DB_NAME} ==="

# Configuration variables
TEMP_DIR="/tmp"
NEO4J_USERNAME="neo4j"
NEO4J_PASSWORD="password123"
DATA_DIR="/data"
NEO4J_ADDRESS="bolt://localhost:7687"

# Execute cypher-shell with retries
function execute_cypher {
    local cmd="$1"
    local max_retries=3
    local retry=0
    
    while [ $retry -lt $max_retries ]; do
        if cypher-shell -a $NEO4J_ADDRESS -u $NEO4J_USERNAME -p "$NEO4J_PASSWORD" "$cmd"; then
            return 0
        else
            retry=$((retry + 1))
            if [ $retry -lt $max_retries ]; then
                echo "Command failed. Retrying in 3 seconds... (Attempt $retry of $max_retries)"
                sleep 3
            else
                echo "Command failed after $max_retries attempts."
                return 1
            fi
        fi
    done
}

# Determine backup path
if [ -z "$BACKUP_PATH" ]; then
    # Check for default location based on DB name
    DEFAULT_BACKUP_DIR="${TEMP_DIR}/neo4j-${DB_NAME}.backup"
    
    # Look for any backup files in the default directory
    if [ -d "$DEFAULT_BACKUP_DIR" ]; then
        # Use wildcard to get first backup file
        BACKUP_FILES=($DEFAULT_BACKUP_DIR/*.backup)
        if [ ${#BACKUP_FILES[@]} -gt 0 ] && [ -f "${BACKUP_FILES[0]}" ]; then
            BACKUP_PATH="${BACKUP_FILES[0]}"
            echo "Found local backup file: $BACKUP_PATH"
        fi
    fi
    
    # If still no backup file found, try downloading from S3
    if [ -z "$BACKUP_PATH" ]; then
        echo "No local backup found. Attempting to download from S3..."
        
        # Ensure required tools are installed
        check_required_tools
        
        # Check for AWS CLI and install if needed
        check_aws_cli
        
        # AWS credentials
        export AWS_ACCESS_KEY_ID="********************"
        export AWS_SECRET_ACCESS_KEY="uw7tRN+E4Uo5hBYcWQY4neOpEFvxRThFGutE8vd+"
        export AWS_REGION="us-east-1"
        
        S3_BUCKET="aura-backup-kavia-v1"
        
        # Find today's date for backup
        TODAY=$(date +%Y-%m-%d)
        mkdir -p "$DEFAULT_BACKUP_DIR"
        
        # Try primary S3 path
        echo "Checking primary S3 location..."
        BACKUP_FILENAME="${DB_NAME}-${TODAY}T04-22-55.backup"
        S3_PATH="s3://$S3_BUCKET/tenant_backups/${DB_NAME}/${TODAY}/${BACKUP_FILENAME}"
        
        if aws s3 ls "$S3_PATH" &>/dev/null; then
            echo "Found backup at: $S3_PATH"
        else
            echo "Backup not found at primary location. Trying alternative paths..."
            
            # Alternative paths to check
            ALT_PATHS=(
                "s3://$S3_BUCKET/tenant_backups/${DB_NAME}/${TODAY}/${DB_NAME}-${TODAY}T05-49-45.backup"
                "s3://$S3_BUCKET/pre_prod_backup/${TODAY}/${DB_NAME}/${DB_NAME}-${TODAY}T04-22-55.backup"
                "s3://$S3_BUCKET/pre_prod_backup/${TODAY}/${DB_NAME}/${DB_NAME}-${TODAY}T05-49-45.backup"
                "s3://$S3_BUCKET/pre_prod_backup/${TODAY}/${DB_NAME}-${TODAY}T04-22-55.backup"
                "s3://$S3_BUCKET/pre_prod_backup/${TODAY}/${DB_NAME}-${TODAY}T05-49-45.backup"
            )
            
            BACKUP_FOUND=false
            for alt_path in "${ALT_PATHS[@]}"; do
                echo "Checking: $alt_path"
                if aws s3 ls "$alt_path" &>/dev/null; then
                    S3_PATH="$alt_path"
                    BACKUP_FILENAME=$(basename "$alt_path")
                    echo "Found backup at alternative path: $S3_PATH"
                    BACKUP_FOUND=true
                    break
                fi
            done
            
            if [ "$BACKUP_FOUND" = false ]; then
                # Try searching for any backup with this DB name
                echo "Searching for any backup file for ${DB_NAME}..."
                SEARCH_RESULT=$(aws s3 ls --recursive "s3://$S3_BUCKET/" | grep "${DB_NAME}" | grep ".backup" | sort -r | head -1)
                
                if [ -n "$SEARCH_RESULT" ]; then
                    FULL_PATH=$(echo "$SEARCH_RESULT" | awk '{print $4}')
                    BACKUP_FILENAME=$(basename "$FULL_PATH")
                    S3_PATH="s3://$S3_BUCKET/$FULL_PATH"
                    echo "Found backup via search: $S3_PATH"
                else
                    echo "No backup found in S3 for ${DB_NAME} on ${TODAY}."
                    echo "Checking previous days..."
                    
                    # Try previous days (up to 5 days back)
                    for i in {1..5}; do
                        PREV_DATE=$(date -d "${TODAY} - ${i} days" +%Y-%m-%d)
                        echo "Checking backups from ${PREV_DATE}..."
                        
                        SEARCH_RESULT=$(aws s3 ls --recursive "s3://$S3_BUCKET/" | grep "${DB_NAME}" | grep ".backup" | grep "${PREV_DATE}" | head -1)
                        
                        if [ -n "$SEARCH_RESULT" ]; then
                            FULL_PATH=$(echo "$SEARCH_RESULT" | awk '{print $4}')
                            BACKUP_FILENAME=$(basename "$FULL_PATH")
                            S3_PATH="s3://$S3_BUCKET/$FULL_PATH"
                            echo "Found backup from ${PREV_DATE}: $S3_PATH"
                            break
                        fi
                    done
                    
                    if [ -z "$SEARCH_RESULT" ]; then
                        echo "Error: No backup files found for ${DB_NAME} in the last 5 days."
                        exit 1
                    fi
                fi
            fi
        fi
        
        # Download the backup file
        echo "Downloading backup from S3: $S3_PATH"
        if aws s3 cp "$S3_PATH" "$DEFAULT_BACKUP_DIR/$BACKUP_FILENAME"; then
            echo "Backup successfully downloaded to: $DEFAULT_BACKUP_DIR/$BACKUP_FILENAME"
            BACKUP_PATH="$DEFAULT_BACKUP_DIR/$BACKUP_FILENAME"
        else
            echo "Error: Failed to download backup from S3."
            exit 1
        fi
    fi
fi

# Verify backup file exists
if [ ! -f "$BACKUP_PATH" ]; then
    echo "Error: Backup file not found at $BACKUP_PATH"
    exit 1
fi

# Get the directory containing the backup file
BACKUP_PARENT_DIR=$(dirname "$BACKUP_PATH")
BACKUP_FILENAME=$(basename "$BACKUP_PATH")
echo "Using backup file: $BACKUP_FILENAME"

# Stop the database if it exists
echo "Checking if database ${DB_NAME} exists..."
if cypher-shell -a $NEO4J_ADDRESS -u $NEO4J_USERNAME -p "$NEO4J_PASSWORD" "SHOW DATABASES" | grep -w "$DB_NAME" > /dev/null; then
    echo "Stopping database ${DB_NAME}..."
    execute_cypher "STOP DATABASE $DB_NAME" || echo "Warning: Failed to stop database. It might already be offline."
    sleep 2
else
    echo "Database ${DB_NAME} does not exist. Will be created during restoration."
fi

# Perform the database restore
echo "Restoring database ${DB_NAME} from ${BACKUP_PATH}..."
if neo4j-admin database restore $DB_NAME --from-path=$BACKUP_PARENT_DIR --overwrite-destination=true; then
    echo "✓ Database restore command executed successfully"
else
    echo "⚠ Database restore command returned an error, but we'll continue with permissions fix"
fi

# Create necessary directory structure if it doesn't exist
echo "Ensuring necessary database directories exist..."
mkdir -p "$DATA_DIR/databases/$DB_NAME"
mkdir -p "$DATA_DIR/transactions/$DB_NAME"

# Fix permissions
echo "Setting proper permissions for database files..."
if [ -d "$DATA_DIR/transactions/$DB_NAME" ]; then
    echo "Setting permissions for transaction files..."
    chown -R neo4j:neo4j $DATA_DIR/transactions/$DB_NAME
    find $DATA_DIR/transactions/$DB_NAME -type d -exec chmod 750 {} \;
    find $DATA_DIR/transactions/$DB_NAME -type f -exec chmod 640 {} \;
else
    echo "Warning: Transaction directory $DATA_DIR/transactions/$DB_NAME not found."
fi

if [ -d "$DATA_DIR/databases/$DB_NAME" ]; then
    echo "Setting permissions for database files..."
    chown -R neo4j:neo4j $DATA_DIR/databases/$DB_NAME
    find $DATA_DIR/databases/$DB_NAME -type d -exec chmod 750 {} \;
    find $DATA_DIR/databases/$DB_NAME -type f -exec chmod 640 {} \;
else
    echo "Warning: Database directory $DATA_DIR/databases/$DB_NAME not found."
fi

# Start the database with proper registration
echo "Starting database ${DB_NAME}..."

# First, ensure the database is registered
execute_cypher "CREATE DATABASE $DB_NAME IF NOT EXISTS"
sleep 5

# Try to start the database
execute_cypher "START DATABASE $DB_NAME" || true

# Check if the restore_metadata.cypher script exists and run it if found
METADATA_SCRIPT="/data/scripts/${DB_NAME}/restore_metadata.cypher"
if [ -f "$METADATA_SCRIPT" ]; then
    echo "Found restore_metadata.cypher script. Executing..."
    cypher-shell -a $NEO4J_ADDRESS -u $NEO4J_USERNAME -p "$NEO4J_PASSWORD" -f "$METADATA_SCRIPT" -P "db=${DB_NAME}" || true
    echo "Metadata script execution attempted."
else
    echo "No restore_metadata.cypher script found at $METADATA_SCRIPT. Continuing..."
fi

# Verify database is online
MAX_RETRIES=5
RETRY_COUNT=0
ONLINE=false

echo "Verifying database status..."
while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$ONLINE" != "true" ]; do
    if cypher-shell -a $NEO4J_ADDRESS -u $NEO4J_USERNAME -p "$NEO4J_PASSWORD" "SHOW DATABASE $DB_NAME" | grep -i "online" > /dev/null; then
        ONLINE=true
        echo "Database $DB_NAME is now online."
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            echo "Database not online yet. Retrying in 5 seconds... (Attempt $RETRY_COUNT of $MAX_RETRIES)"
            sleep 5
            
            # Try different approaches as we retry
            if [ $RETRY_COUNT -eq 1 ]; then
                echo "Attempt 1: Simple restart..."
                execute_cypher "STOP DATABASE $DB_NAME" || true
                sleep 2
                execute_cypher "START DATABASE $DB_NAME" || true
            elif [ $RETRY_COUNT -eq 2 ]; then
                echo "Attempt 2: Drop and recreate..."
                execute_cypher "DROP DATABASE $DB_NAME IF EXISTS" || true
                sleep 2
                execute_cypher "CREATE DATABASE $DB_NAME" || true
                sleep 2
                execute_cypher "START DATABASE $DB_NAME" || true
            elif [ $RETRY_COUNT -ge 3 ]; then
                echo "Attempt 3+: Force recreation and restart..."
                execute_cypher "DROP DATABASE $DB_NAME IF EXISTS" || true
                sleep 3
                execute_cypher "CREATE DATABASE $DB_NAME" || true
                sleep 5
                execute_cypher "START DATABASE $DB_NAME" || true
            fi
        else
            echo "Warning: Database still not online after $MAX_RETRIES attempts."
        fi
    fi
done

# Summary
if [ "$ONLINE" = "true" ]; then
    echo "=== Database restoration completed successfully ==="
else
    echo "=== Database restoration completed but database is not online ==="
    echo "Please check the database status manually"
fi

echo ""
echo "Summary:"
echo "  - Database: $DB_NAME"
echo "  - Backup File: $BACKUP_FILENAME"
echo ""

# Display all databases
echo "Current database status:"
cypher-shell -a $NEO4J_ADDRESS -u $NEO4J_USERNAME -p "$NEO4J_PASSWORD" "SHOW DATABASES"