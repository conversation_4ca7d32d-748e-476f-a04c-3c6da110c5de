#!/bin/bash
# tenant_neo4j_s3_backup.sh
# Script to backup a specific Neo4j database (tenant) running in Docker and store it in S3

# Usage: ./tenant_neo4j_s3_backup.sh <tenant_database_name>
# Example: ./tenant_neo4j_s3_backup.sh customer_db1
# ssh -i "neo4j-key.pem" <EMAIL>

# Configuration variables
S3_BUCKET="aura-backup-kavia-v1"
AWS_REGION="us-east-1"
CONTAINER_NAME="neo4j-enterprise"  # From your docker-compose.yml
TEMP_BACKUP_DIR="/tmp/neo4j-backups-v1"
BACKUP_DATE=$(date +%Y-%m-%d)

# Function to display usage information
function show_usage {
    echo "Usage: $0 <tenant_database_name>"
    echo "Example: $0 customer_db1"
    exit 1
}

# Check if tenant database name is provided
if [ $# -ne 1 ]; then
    echo "Error: Tenant database name is required!"
    show_usage
fi

TENANT_DB="$1"
S3_PREFIX="tenant_backups/${TENANT_DB}/${BACKUP_DATE}"

echo "=== Starting backup for tenant database: ${TENANT_DB} ==="

# Ensure AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "Error: AWS CLI is not installed. Please install it first."
    exit 1
fi

# Ensure the Docker container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "Error: Neo4j container '$CONTAINER_NAME' is not running!"
    exit 1
fi

# Verify the tenant database exists
echo "Verifying tenant database exists..."
DB_EXISTS=$(docker exec $CONTAINER_NAME cypher-shell -u neo4j -p "exp!23fg56641@we" \
    "SHOW DATABASES;" | grep -w "$TENANT_DB")

if [ -z "$DB_EXISTS" ]; then
    echo "Error: Tenant database '${TENANT_DB}' was not found!"
    echo "Available databases:"
    docker exec $CONTAINER_NAME cypher-shell -u neo4j -p "exp!23fg56641@we" \
        "SHOW DATABASES;" | awk 'NR>1 {print "  - " $1}' | tr -d '",+'
    exit 1
fi

# Create temp directory for backups
echo "Creating temporary backup directory..."
mkdir -p $TEMP_BACKUP_DIR
if [ ! -d "$TEMP_BACKUP_DIR" ]; then
    echo "Error: Failed to create temporary backup directory!"
    exit 1
fi

# Create a separate backup directory for the tenant database
BACKUP_PATH="/tmp/backup/${TENANT_DB}"

# Clean any existing backup directory for this database
echo "Preparing backup directory in container..."
docker exec $CONTAINER_NAME rm -rf "$BACKUP_PATH"
docker exec $CONTAINER_NAME mkdir -p "$BACKUP_PATH"

# Execute backup command inside the container
echo "Starting backup process for tenant database: ${TENANT_DB}"
if docker exec $CONTAINER_NAME neo4j-admin database backup \
    --verbose \
    --to-path="$BACKUP_PATH" \
    --compress=true \
    "$TENANT_DB"; then
    
    # Get a list of all backup files for this database
    CONTAINER_BACKUP_FILES=$(docker exec $CONTAINER_NAME find "$BACKUP_PATH" -type f -name "${TENANT_DB}-*" -print)
    
    if [ -z "$CONTAINER_BACKUP_FILES" ]; then
        echo "Error: No backup files found for ${TENANT_DB} in container!"
        exit 1
    fi
    
    # Keep track of successful uploads
    TOTAL_FILES=$(echo "$CONTAINER_BACKUP_FILES" | wc -w)
    SUCCESSFUL_UPLOADS=0
    
    # Process each backup file
    for CONTAINER_BACKUP_FILE in $CONTAINER_BACKUP_FILES; do
        # Get just the filename
        BACKUP_FILENAME=$(basename "$CONTAINER_BACKUP_FILE")
        
        # Copy from container to host
        echo "Copying backup file from container to host: $BACKUP_FILENAME"
        if docker cp "$CONTAINER_NAME:$CONTAINER_BACKUP_FILE" "$TEMP_BACKUP_DIR/$BACKUP_FILENAME"; then
            
            if [ -f "$TEMP_BACKUP_DIR/$BACKUP_FILENAME" ]; then
                # Get file size for reporting
                FILE_SIZE=$(du -h "$TEMP_BACKUP_DIR/$BACKUP_FILENAME" | cut -f1)
                
                # Upload to S3
                echo "Uploading backup file to S3 (${FILE_SIZE}): s3://${S3_BUCKET}/${S3_PREFIX}/${BACKUP_FILENAME}"
                
                # Make sure the S3 directory exists
                aws s3api put-object --bucket $S3_BUCKET --key "${S3_PREFIX}/" --content-length 0 >/dev/null
                
                if aws s3 cp "$TEMP_BACKUP_DIR/$BACKUP_FILENAME" "s3://${S3_BUCKET}/${S3_PREFIX}/${BACKUP_FILENAME}"; then
                    echo "✓ Successfully uploaded $BACKUP_FILENAME to S3"
                    # Clean up local file after successful upload
                    rm "$TEMP_BACKUP_DIR/$BACKUP_FILENAME"
                    SUCCESSFUL_UPLOADS=$((SUCCESSFUL_UPLOADS + 1))
                else
                    echo "✗ Error: Failed to upload $BACKUP_FILENAME to S3"
                fi
            else
                echo "Error: Failed to copy file to host: $BACKUP_FILENAME"
            fi
        else
            echo "Error: Failed to copy backup file from container to host"
        fi
    done
    
    echo "Successfully uploaded ${SUCCESSFUL_UPLOADS} out of ${TOTAL_FILES} backup files"
    
    if [ "$SUCCESSFUL_UPLOADS" -eq "$TOTAL_FILES" ]; then
        echo "✓ Tenant database ${TENANT_DB} backup completed successfully!"
    else
        echo "⚠ Warning: Some files failed to upload. Tenant database ${TENANT_DB} backup partially completed."
    fi
else
    echo "✗ Error: Backup failed for tenant database ${TENANT_DB}"
    exit 1
fi

# Clean up in container
echo "Cleaning up resources..."
docker exec $CONTAINER_NAME rm -rf "$BACKUP_PATH"

# Clean up temp directory
if [ -d "$TEMP_BACKUP_DIR" ]; then
    rm -rf "$TEMP_BACKUP_DIR"
fi

echo "=== Tenant backup process completed ==="
echo ""
echo "S3 Backup Location: s3://${S3_BUCKET}/${S3_PREFIX}/"
echo "To verify backup files:"
echo "  aws s3 ls s3://${S3_BUCKET}/${S3_PREFIX}/"
echo ""
