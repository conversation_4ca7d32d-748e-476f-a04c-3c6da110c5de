import subprocess
import os
import argparse
import logging
from datetime import datetime
import json
import re
import sys
from app.core.Settings import settings


def setup_logging():
    """Configure logging for the script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)

def parse_connection_uri(uri):
    """
    Parse Neo4j connection URI to extract host and port.
    
    Args:
        uri (str): Neo4j connection URI (e.g., neo4j://hostname:port)
    
    Returns:
        tuple: (host, port)
    """
    # Default port
    default_port = 7687
    
    # Remove protocol
    uri = uri.replace("neo4j://", "")
    
    # Split by colon to separate host and port
    parts = uri.split(":")
    host = parts[0]
    
    # Extract port if available
    port = default_port
    if len(parts) > 1:
        try:
            port = int(parts[1])
        except ValueError:
            # If port is not a valid number, use default
            pass
    
    return host, port

def get_all_databases(host, port, username, password):
    """
    Get a list of all databases from the Neo4j server.
    
    Args:
        host (str): Neo4j server hostname or IP
        port (int): Neo4j server bolt port
        username (str): Neo4j username
        password (str): Neo4j password
    
    Returns:
        list: List of database names
    """
    logger.info("Retrieving list of all databases")
    
    # First, try to directly use default database 'neo4j' if we can't get the list
    try:
        # Use neo4j-admin directly to get database info
        command = [
            "neo4j-admin", "dbms", "list", "--verbose"
        ]
        
        result = subprocess.run(
            command,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            # Parse output to find database names
            databases = []
            for line in result.stdout.strip().split('\n'):
                if "database:" in line.lower():
                    parts = line.split(":")
                    if len(parts) > 1:
                        db_name = parts[1].strip()
                        databases.append(db_name)
            
            if databases:
                logger.info(f"Found {len(databases)} databases using neo4j-admin: {', '.join(databases)}")
                return databases
    except Exception as e:
        logger.warning(f"Failed to get database list using neo4j-admin: {str(e)}")
    
    # If neo4j-admin didn't work, try cypher-shell
    try:
        # Use cypher-shell to execute query to get all databases
        command = [
            "cypher-shell", 
            "-a", f"neo4j://{host}:{port}",
            "-u", username,
            "-p", password,
            "--format", "plain",
            "SHOW DATABASES;"
        ]
        
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True
        )
        
        # Debug output
        logger.debug(f"Raw cypher-shell output: {result.stdout}")
        
        # Parse the plain text output
        lines = result.stdout.strip().split('\n')
        databases = []
        
        # Skip header lines and footer lines
        for line in lines:
            line = line.strip()
            if line.startswith('|') and not line.startswith('+-') and not "name" in line:
                # Extract database name - it's the first column
                parts = line.split('|')
                if len(parts) > 1:
                    db_name = parts[1].strip()
                    # Remove quotes if present
                    db_name = db_name.strip('"\'')
                    if db_name:  # Only add if not empty
                        databases.append(db_name)
        
        if databases:
            logger.info(f"Found {len(databases)} databases via cypher-shell: {', '.join(databases)}")
            return databases
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to get database list via cypher-shell: {str(e)}")
        if hasattr(e, 'stderr'):
            logger.error(f"Error output: {e.stderr}")
    except Exception as e:
        logger.error(f"Unexpected error getting database list: {str(e)}")
    
    # If everything fails, return default database
    logger.info("Falling back to default 'neo4j' database")
    return ["neo4j"]

def export_neo4j_dump(host, port, username, password, database_name, output_dir):
    """
    Export a Neo4j database dump using username and password authentication.
    
    Args:
        host (str): Neo4j server hostname or IP
        port (int): Neo4j server bolt port
        username (str): Neo4j username
        password (str): Neo4j password
        database_name (str): Name of the database to export
        output_dir (str): Directory to save the dump file
    
    Returns:
        str: Path to the created dump file or None if export failed
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate timestamp for the filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    dump_filename = f"{database_name}_{timestamp}.dump"
    output_path = os.path.join(output_dir, dump_filename)
    
    logger.info(f"Starting export of database '{database_name}' to {output_path}")
    
    # Commands to try, in order of preference
    commands_to_try = [
        # Format for Neo4j 5.x with --server and auth
        [
            "neo4j-admin", "database", "dump",
            database_name,
            "--to-path", output_path,
            "--server", f"{host}:{port}",
            "--user", username,
            "--password", password,
            "--overwrite-destination=true"
        ],
        # Format for Neo4j 4.x and 5.x, local instance
        [
            "neo4j-admin", "database", "dump",
            database_name,
            "--to-path", output_path,
            "--overwrite-destination=true"
        ],
        # Alternative format for some versions with authentication
        [
            "neo4j-admin", "dump",
            "--database", database_name,
            "--to", output_path
        ],
        # Older format
        [
            "neo4j-admin", "dump",
            database_name,
            "--to", output_path
        ]
    ]
    
    success = False
    last_error = None
    
    # Try each command format until one works
    for cmd in commands_to_try:
        try:
            cmd_str = ' '.join(cmd)
            logger.info(f"Trying command: {cmd_str}")
            
            # When using Neo4j in a managed environment, we might need to set these environment variables
            env = os.environ.copy()
            env["NEO4J_USERNAME"] = username
            env["NEO4J_PASSWORD"] = password
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False,  # Don't raise exception on error
                env=env
            )
            
            # Check if command was successful
            if result.returncode == 0:
                logger.info(f"Command succeeded: {cmd_str}")
                
                # Check if file exists at expected location
                expected_paths = [
                    output_path,
                    os.path.join(output_dir, f"{database_name}.dump"),
                    os.path.join(output_dir, dump_filename)
                ]
                
                for path in expected_paths:
                    if os.path.exists(path):
                        logger.info(f"Export of '{database_name}' completed successfully to {path}")
                        return path
                
                # If we got here, command succeeded but file wasn't found at expected locations
                logger.warning(f"Command succeeded but dump file not found at expected locations")
                logger.info(f"Command output: {result.stdout}")
                
                # Maybe it worked but we don't know where the file is
                success = True
                break
            else:
                logger.warning(f"Command failed with error code {result.returncode}")
                logger.warning(f"Error message: {result.stderr}")
                last_error = result
        except Exception as e:
            logger.warning(f"Error executing command: {str(e)}")
            last_error = e
            continue
    
    if success:
        # If a command succeeded but we didn't find the file, we'll look for any new .dump files
        dump_files = [f for f in os.listdir(output_dir) if f.endswith('.dump') and database_name in f]
        dump_files.sort(key=lambda f: os.path.getmtime(os.path.join(output_dir, f)), reverse=True)
        
        if dump_files:
            newest_dump = os.path.join(output_dir, dump_files[0])
            logger.info(f"Found dump file at: {newest_dump}")
            return newest_dump
        else:
            logger.error(f"Command succeeded but couldn't locate dump file for '{database_name}'")
            return None
    else:
        # All commands failed
        logger.error(f"All export attempts for '{database_name}' failed")
        if last_error and hasattr(last_error, 'stderr'):
            logger.error(f"Last error message: {last_error.stderr}")
        
        # Try a more direct approach as last resort - this only works if Neo4j is on the same machine
        try:
            logger.info("Attempting direct file system backup as last resort...")
            neo4j_data_dir = os.environ.get("NEO4J_DATA_DIR", "/var/lib/neo4j/data")
            db_dir = os.path.join(neo4j_data_dir, "databases", database_name)
            
            if os.path.exists(db_dir):
                backup_cmd = [
                    "tar", "-czf", 
                    output_path.replace(".dump", ".tar.gz"), 
                    "-C", neo4j_data_dir, 
                    f"databases/{database_name}"
                ]
                
                result = subprocess.run(
                    backup_cmd,
                    capture_output=True,
                    text=True,
                    check=False
                )
                
                if result.returncode == 0 and os.path.exists(output_path.replace(".dump", ".tar.gz")):
                    logger.info(f"Direct file system backup successful: {output_path.replace('.dump', '.tar.gz')}")
                    return output_path.replace(".dump", ".tar.gz")
                else:
                    logger.error("Direct file system backup failed")
            else:
                logger.error(f"Database directory not found: {db_dir}")
        except Exception as e:
            logger.error(f"Direct backup failed: {str(e)}")
            
        return None

def export_all_databases(connection_uri, username, password, output_dir):
    """
    Export all Neo4j databases.
    
    Args:
        connection_uri (str): Neo4j connection URI
        username (str): Neo4j username
        password (str): Neo4j password
        output_dir (str): Directory to save the dump files
    
    Returns:
        dict: Dictionary mapping database names to export results (path or None)
    """
    # Parse connection URI to get host and port
    host, port = parse_connection_uri(connection_uri)
    logger.info(f"Using host: {host}, port: {port} from connection URI")
    
    # Get all databases
    databases = get_all_databases(host, port, username, password)
    
    # If no databases found, use default neo4j database
    if not databases:
        logger.warning("No databases found, defaulting to 'neo4j' database")
        databases = ["neo4j"]
    
    # Export each database
    results = {}
    for db in databases:
        # Skip system databases if they exist in the list
        if db in ['system']:
            logger.info(f"Skipping system database: {db}")
            continue
            
        output_file = export_neo4j_dump(host, port, username, password, db, output_dir)
        results[db] = output_file
    
    return results

if __name__ == "__main__":
    # Set up the argument parser
    parser = argparse.ArgumentParser(description="Export Neo4j database dumps using settings")
    parser.add_argument("--output-dir", default="./dumps", help="Directory to save the exports")
    parser.add_argument("--uri", help="Override Neo4j connection URI from settings")
    parser.add_argument("--username", help="Override Neo4j username from settings")
    parser.add_argument("--password", help="Override Neo4j password from settings")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Set up logging
    logger = setup_logging()
    
    # Set debug logging if requested
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    
    # Use command line args if provided, otherwise use settings
    try:
        connection_uri = args.uri if args.uri else settings.NEO4J_CONNECTION_URI
        username = args.username if args.username else settings.NEO4J_USER
        password = args.password if args.password else settings.NEO4J_PASSWORD
    except AttributeError as e:
        logger.error(f"Error accessing settings: {str(e)}")
        logger.error("Make sure NEO4J_CONNECTION_URI, NEO4J_USER, and NEO4J_PASSWORD are defined in settings")
        sys.exit(1)
    
    logger.info(f"Using connection URI: {connection_uri}")
    logger.info(f"Using username: {username}")
    
    # Export all databases
    logger.info("Exporting all databases")
    results = export_all_databases(
        connection_uri,
        username,
        password,
        args.output_dir
    )
    
    # Report results
    success_count = sum(1 for v in results.values() if v is not None)
    fail_count = sum(1 for v in results.values() if v is None)
    
    logger.info(f"Export summary: {success_count} succeeded, {fail_count} failed")
    
    for db, result in results.items():
        if result:
            logger.info(f"✅ {db}: {result}")
        else:
            logger.error(f"❌ {db}: Failed to export")
    
    # Set exit code based on success
    if fail_count > 0:
        sys.exit(1)
    else:
        sys.exit(0)