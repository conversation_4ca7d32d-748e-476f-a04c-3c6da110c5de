from kubernetes import client, config
import subprocess

# List of services to keep (from the previous image)
KEEP_SERVICES = [
    'internal-533-dev',
    'internal-clusterip-533-dev',
    'internal-8647-dev',
    'internal-clusterip-8647-dev',
    'internal-17839-dev',
    'internal-clusterip-17839-dev',
    'internal-49-dev',
    'internal-clusterip-49-dev',
    'internal-432517-dev',
    'internal-clusterip-432517-dev'
]

def get_services_to_delete(namespace):
    try:
        # Run kubectl command to get all services
        result = subprocess.run(
            ['kubectl', 'get', 'services', '-n', namespace, '-o', 'jsonpath={.items[*].metadata.name}'], 
            capture_output=True, 
            text=True
        )
        
        # Get all services
        all_services = result.stdout.split()
        
        # Filter services to delete (those starting with 'internal-' but not in KEEP_SERVICES)
        services_to_delete = [
            svc for svc in all_services 
            if svc.startswith('internal-') and svc not in KEEP_SERVICES
        ]
        
        return services_to_delete
    
    except Exception as e:
        print(f"Error getting services: {e}")
        return []

def delete_services(namespace, services):
    try:
        # Load the kube config
        config.load_kube_config()
        
        # Create a CoreV1Api client
        v1 = client.CoreV1Api()
        
        # Delete each service
        for service_name in services:
            v1.delete_namespaced_service(
                name=service_name, 
                namespace=namespace,
                body=client.V1DeleteOptions()
            )
            print(f"Service {service_name} deleted successfully")
    
    except Exception as e:
        print(f"Error deleting services: {e}")

# Namespace and deletion process
namespace = "duploservices-kavia-dev"

# Get services to delete
services_to_delete = get_services_to_delete(namespace)

# Print services that will be deleted
print("The following services will be deleted:")
for service in services_to_delete:
    print(service)

print(f"\nTotal services to be deleted: {len(services_to_delete)}")

# Uncomment the line below to actually delete the services
delete_services(namespace, services_to_delete)