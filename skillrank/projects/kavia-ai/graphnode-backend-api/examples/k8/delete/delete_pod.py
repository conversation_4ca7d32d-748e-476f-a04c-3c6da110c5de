from kubernetes import client, config
from kubernetes.client.rest import ApiException
import logging
from typing import List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def cleanup_codegen_pods(pod_id: str, env_name: str, namespace: str) -> None:
    """
    Clean up Kubernetes resources for codegen pods including deployments, services, PVCs, and configmaps.
    
    Args:
        pod_id (str): The pod identifier used in resource naming
        env_name (str): Environment name  
        namespace (str): Kubernetes namespace
    """
    try:
        # Initialize Kubernetes API clients
        apps_v1 = client.AppsV1Api()
        core_v1 = client.CoreV1Api()
        
        # Get all pods with label service=codegen
        pods = _get_codegen_pods(core_v1, namespace)
        
        if not pods:
            logger.info("No codegen pods found")
            return
            
        logger.info(f"Found {len(pods)} codegen pods to process")
        
        # Process each pod
        for pod_name in pods:
            print(pod_name)
            print(pod_id)
            logger.info(f"Processing pod: {pod_name}")
            
            # Extract deployment name from pod name (first two parts separated by -)
            deployment_name = _extract_deployment_name(pod_name)
            
            if not deployment_name:
                logger.warning(f"Could not extract deployment name from pod: {pod_name}")
                continue
                
            # Delete resources
            # _delete_deployment(apps_v1, deployment_name, namespace)
            # _delete_services(core_v1, deployment_name, namespace)
            # _delete_pvc(core_v1, deployment_name, namespace)
            # _delete_configmap(core_v1, deployment_name, namespace)
            
        logger.info("Cleanup completed successfully")
        
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        raise

def _get_codegen_pods(core_v1: client.CoreV1Api, namespace: str) -> List[str]:
    """Get all pod names with label service=codegen"""
    try:
        pods_response = core_v1.list_namespaced_pod(
            namespace=namespace,
            label_selector="service=codegen"
        )
        return [pod.metadata.name for pod in pods_response.items]
    except ApiException as e:
        logger.error(f"Failed to get pods: {e}")
        return []

def _extract_deployment_name(pod_name: str) -> Optional[str]:
    """Extract deployment name from pod name (first two parts)"""
    parts = pod_name.split('-')
    return '-'.join(parts[:2]) if len(parts) >= 2 else None

def _delete_deployment(apps_v1: client.AppsV1Api, deployment_name: str, namespace: str) -> None:
    """Delete Kubernetes deployment"""
    try:
        apps_v1.delete_namespaced_deployment(
            name=deployment_name,
            namespace=namespace,
            body=client.V1DeleteOptions(
                propagation_policy='Foreground',
                grace_period_seconds=0
            )
        )
        logger.info(f"Deleted deployment: {deployment_name}")
    except ApiException as e:
        if e.status == 404:
            logger.info(f"Deployment {deployment_name} not found (already deleted)")
        else:
            logger.error(f"Failed to delete deployment {deployment_name}: {e}")

def _delete_services(core_v1: client.CoreV1Api, deployment_name: str, namespace: str) -> None:
    """Delete related services"""
    service_names = [
        f"internal-{deployment_name}",
        f"internal-clusterip-{deployment_name}"
    ]
    
    for service_name in service_names:
        try:
            core_v1.delete_namespaced_service(
                name=service_name,
                namespace=namespace,
                body=client.V1DeleteOptions(
                    grace_period_seconds=0
                )
            )
            logger.info(f"Deleted service: {service_name}")
        except ApiException as e:
            if e.status == 404:
                logger.info(f"Service {service_name} not found (already deleted)")
            else:
                logger.error(f"Failed to delete service {service_name}: {e}")

def _delete_pvc(core_v1: client.CoreV1Api, deployment_name: str, namespace: str) -> None:
    """Delete persistent volume claim"""
    pvc_name = f"pvc-{deployment_name}"
    
    try:
        core_v1.delete_namespaced_persistent_volume_claim(
            name=pvc_name,
            namespace=namespace,
            body=client.V1DeleteOptions(
                grace_period_seconds=0
            )
        )
        logger.info(f"Deleted PVC: {pvc_name}")
    except ApiException as e:
        if e.status == 404:
            logger.info(f"PVC {pvc_name} not found (already deleted)")
        else:
            logger.error(f"Failed to delete PVC {pvc_name}: {e}")

def _delete_configmap(core_v1: client.CoreV1Api, deployment_name: str, namespace: str) -> None:
    """Delete configmap"""
    config_name = f"pod-status-{deployment_name}"
    
    try:
        core_v1.delete_namespaced_config_map(
            name=config_name,
            namespace=namespace,
            body=client.V1DeleteOptions(
                grace_period_seconds=0
            )
        )
        logger.info(f"Deleted configmap: {config_name}")
    except ApiException as e:
        if e.status == 404:
            logger.info(f"Configmap {config_name} not found (already deleted)")
        else:
            logger.error(f"Failed to delete configmap {config_name}: {e}")

# Example usage
if __name__ == "__main__":
    # Load kubeconfig (modify as per your configuration method)
    try:
        config.load_incluster_config()
    except config.ConfigException:
        config.load_kube_config()
    
    # Call the cleanup function
    cleanup_codegen_pods(
        pod_id="570",
        env_name="dev", 
        namespace="duploservices-k-dev01"
    )