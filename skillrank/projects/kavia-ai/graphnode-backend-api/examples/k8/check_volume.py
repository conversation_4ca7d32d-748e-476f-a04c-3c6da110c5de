from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time

# Prompt the user for PROJECT_ID, ENV_NAME, and ACTION
project_id = input("Enter the PROJECT_ID: ")
env_name = input("Enter the ENV_NAME: ")
action = input("Enter the ACTION (create/delete): ").strip().lower()

# Validate the inputs
if not project_id or not env_name or action not in ["create", "delete"]:
    raise ValueError("PROJECT_ID, ENV_NAME, and valid ACTION (create/delete) must be provided.")

# Load in-cluster configuration
config.load_kube_config()

# Create Kubernetes API client
batch_v1 = client.BatchV1Api()

# Define the job name and namespace
job_name = f"codegen-{project_id}-{env_name}"
namespace = "duploservices-kavia-dev"
previous_create_job = f"codegen-{project_id}-{env_name}"
hostname = f"vscode-internal-{project_id}-{env_name}.cloud.kavia.ai"

# Define the command based on the action
if action == "create":
    servicename = f"internal-{project_id}-{env_name}"
    kubectl_command = f"""
    echo "Creating deployment for {job_name}..."
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
     s/{{{{ENV_NAME}}}}/{env_name}/g; \
     s/{{{{hostname}}}}/{hostname}/g; \
     s/{{{{servicename}}}}/{servicename}/g" \
     /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
         s/{{{{ENV_NAME}}}}/{env_name}/g" \
         /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
    #sleep 99999
    kubectl apply -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
    kubectl apply -f /tmp/codegenservice-{project_id}-{env_name}.yaml
    kubectl apply -f /tmp/ingress-patch-{project_id}-{env_name}.yaml
    """
elif action == "delete":
    kubectl_command = f"""
    echo "Deleting deployment for {job_name}..."
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
     s/{{{{ENV_NAME}}}}/{env_name}/g; \
     s/{{{{hostname}}}}/{hostname}/g; \
     s/{{{{servicename}}}}/internal-{project_id}-{env_name}/g" \
     /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
         s/{{{{ENV_NAME}}}}/{env_name}/g" \
         /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
    #sleep 99999
    kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true 
    kubectl delete -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml --ignore-not-found=true
    kubectl delete ingress {project_id}-{env_name} -n {namespace} --ignore-not-found=true
    echo "Deleting previous job: {previous_create_job}..."
    kubectl delete job {previous_create_job} -n {namespace} --ignore-not-found=true
    echo "Cleanup completed!"
    """

# Define the job manifest
job_manifest = {
    "apiVersion": "batch/v1",
    "kind": "Job",
    "metadata": {
        "name": job_name,
        "namespace": namespace,
        "labels": {
            "app": job_name,
            "owner": "duploservices",
            "tenantname": namespace,
        },
    },
    "spec": {
        "parallelism": 1,
        "completions": 1,
        "backoffLimit": 6,
        "template": {
            "metadata": {
                "labels": {
                    "app": job_name,
                    "owner": "duploservices",
                    "tenantname": namespace,
                },
            },
            "spec": {
                "serviceAccountName": "duploservices-kavia-dev-edit-user",
                "restartPolicy": "Never",
                "containers": [
                    {
                        "name": "duplocloudcodegen",
                        "image": "bitnami/kubectl:latest",
                        "command": ["/bin/sh", "-c"],
                        "args": [kubectl_command],
                        "env": [
                            {"name": "ACTION", "value": action},
                            {"name": "PROJECT_ID", "value": project_id},
                            {"name": "ENV_NAME", "value": env_name},
                        ],
                        "volumeMounts": [
                            {"name": "codegenservicedeployment4", "mountPath": "/app"},
                            {"name": "ingressservice1", "mountPath": "/app/ingress"},
                            {"name": "codegenpvc", "mountPath": "/app/pvc"},
                        ],
                    }
                ],
                "volumes": [
                    {
                        "name": "codegenservicedeployment4",
                        "configMap": {"name": "codegenservicedeployment4"},
                    },
                    {
                        "name": "ingressservice1",
                        "configMap": {"name": "ingressservice1"},
                    },
                    {
                        "name": "codegenpvc",
                        "configMap": {"name": "codegenpvc"},
                    },
                ],
            },
        },
    },
}

# Monitor job completion and delete it upon success
def wait_for_job_completion(job_name, namespace):
    while True:
        job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
        if job.status.succeeded:
            print(f"Job {job_name} completed successfully, deleting...")
            batch_v1.delete_namespaced_job(
                name=job_name,
                namespace=namespace,
                body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
            )
            print(f"Deleted job: {job_name}")
            break
        time.sleep(5)

# Create the job
try:
    response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
    print(f"Job created: {response.metadata.name}")
    wait_for_job_completion(job_name, namespace)
    
    if action == "create":
        print(f"Ingress hostname: {hostname}")

except ApiException as e:
    print(f"An error occurred: {e}")
    raise

