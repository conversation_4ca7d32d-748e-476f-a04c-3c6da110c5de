"""
Kubernetes resource management functions for code generation service.
This module handles creating and deleting Kubernetes resources for code generation.
"""

from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
import subprocess
import logging
from pathlib import Path
from typing import Literal, Optional, Dict, Any, Union

# Try to import the SSM loader, but don't fail if it's not available
try:
    from app.utils.aws.ssm_loader import load_ssm_dev_param
except ImportError:
    # Define a fallback function if the import fails
    def load_ssm_dev_param():
        logging.warning("SSM loader not available, kubeconfig must be provided through environment")
        return None

def load_kubernetes_config() -> bool:
    """
    Load Kubernetes configuration using multiple fallback methods.
    
    Returns:
        bool: True if configuration was loaded successfully, False otherwise
    """
    try:
        # Try in-cluster config first (for pods)
        config.load_incluster_config()
        logging.info("Using in-cluster Kubernetes configuration")
        return True
    except config.ConfigException:
        try:
            # Fallback to kubeconfig file
            config.load_kube_config()
            logging.info("Using local kubeconfig file")
            return True
        except config.ConfigException:
            # Try to get from SSM (if available)
            try:
                kubeconfig = load_ssm_dev_param()
                if kubeconfig:
                    config.load_kube_config_from_dict(kubeconfig)
                    logging.info("Using SSM-loaded Kubernetes configuration")
                    return True
                else:
                    logging.error("Failed to load Kubernetes config from SSM")
                    return False
            except Exception as e:
                logging.error(f"Failed to load Kubernetes config: {e}")
                return False

def manage_codegen_resources(
    project_id: str,
    stage: str = "dev",
    action: Literal["create", "delete"] = "delete",
    namespace: str = "duploservices-kavia-dev",
    wait_for_completion: bool = True,
    timeout_seconds: int = 300
) -> Dict[str, Any]:
    """
    Create or delete Kubernetes resources for code generation.
    
    Args:
        project_id: The project ID to manage resources for
        stage: The environment stage (dev, qa, prod)
        action: Whether to create or delete resources
        namespace: The Kubernetes namespace
        wait_for_completion: Whether to wait for job completion
        timeout_seconds: Maximum time to wait for job completion
    
    Returns:
        Dict containing status information
    """
    result = {
        "success": False,
        "message": "",
        "details": {},
        "error": None
    }
    
    # Validate inputs
    if not project_id:
        result["error"] = "PROJECT_ID must be provided"
        return result
        
    if not stage:
        result["error"] = "ENV_NAME must be provided"
        return result
        
    if action not in ["create", "delete"]:
        result["error"] = "ACTION must be either 'create' or 'delete'"
        return result
    
    try:
        # Load Kubernetes configuration
        if not load_kubernetes_config():
            result["error"] = "Failed to load Kubernetes configuration"
            return result
            
        # Create Kubernetes API client
        batch_v1 = client.BatchV1Api()
        
        # Define job details
        job_name = f"codegen-{project_id}-{stage}-{action}"
        hostname = f"vscode-internal-{project_id}.{stage}.cloud.kavia.ai"
        
        result["details"]["job_name"] = job_name
        result["details"]["namespace"] = namespace
        
        # Define the command based on the action
        if action == "create":
            servicename = f"internal-{project_id}-{stage}"
            kubectl_command = f"""
            echo "Creating deployment for {job_name}..."
            CONFIG_FILE="/tmp/custom_${{PROJECT_ID}}.conf"
            POD_NAME="nginx-c96774b8d-d98nb"
            echo $NAMESPACE
            echo " Environment: $ENV_NAME"
            echo " Finding oldest pod with label 'pod-status=available'..."
            POD_APP=`kubectl get pods -n "$NAMESPACE" -l pod-status=available \\
            --sort-by=.metadata.creationTimestamp \\
            -o jsonpath="{{.items[0].metadata.name}} {{.items[0].metadata.labels.app}}"`
            POD=`echo "$POD_APP" | awk '{{print $1}}'`
            APP=`echo "$POD_APP" | awk '{{print $2}}'`
            echo " Using pod: $POD"
            echo " App label: $APP"
            SERVER_NAME="vscode-internal-${{PROJECT_ID}}-${{ENV_NAME}}.dev-vscode.cloud.kavia.ai"
            PROXY_port1="http://internal-${{APP}}:3000"
            PROXY_port2="http://internal-${{APP}}:3001"
            PROXY_port3="http://internal-${{APP}}:3002"
            PROXY_port4="http://internal-${{APP}}:3003"
            PROXY_port5="http://internal-${{APP}}:3004"
            PROXY_port6="http://internal-${{APP}}:3005"
            PROXY_port7="http://internal-${{APP}}:3006"
            PROXY_port8="http://internal-${{APP}}:3007"
            PROXY_port9="http://internal-${{APP}}:3008"
            PROXY_port10="http://internal-${{APP}}:3009"
            PROXY_port11="http://internal-${{APP}}:3010"
            PROXY_port12="http://internal-${{APP}}:5000"
            PROXY_port13="http://internal-${{APP}}:5001"
            PROXY_port14="http://internal-${{APP}}:5002"
            PROXY_port15="http://internal-${{APP}}:5003"
            PROXY_port16="http://internal-${{APP}}:5004"
            PROXY_port17="http://internal-${{APP}}:5005"
            PROXY_port18="http://internal-${{APP}}:5006"
            PROXY_port19="http://internal-${{APP}}:5007"
            PROXY_port20="http://internal-${{APP}}:5008"
            PROXY_port21="http://internal-${{APP}}:5009"
            PROXY_port22="http://internal-${{APP}}:5010"
            PROXY_port23="http://internal-${{APP}}:8000"
            PROXY_port24="http://internal-${{APP}}:8001"
            PROXY_port25="http://internal-${{APP}}:8002"
            PROXY_port26="http://internal-${{APP}}:8003"
            PROXY_port27="http://internal-${{APP}}:8004"
            PROXY_port28="http://internal-${{APP}}:8005"
            PROXY_port29="http://internal-${{APP}}:8006"
            PROXY_port30="http://internal-${{APP}}:8007"
            PROXY_port31="http://internal-${{APP}}:8008"
            PROXY_port32="http://internal-${{APP}}:8009"
            PROXY_port33="http://internal-${{APP}}:8010"
            PROXY="http://internal-${{APP}}:8080"
            echo " SERVER_NAME to be added: $SERVER_NAME"
            echo " PROXY to be routed: $PROXY"
            sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; \\
            s|{{{{PROXY}}}}|${{PROXY}}|g; \\
            s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g; \\
            s|{{{{PROXY_port2}}}}|${{PROXY_port2}}|g; \\
            s|{{{{PROXY_port3}}}}|${{PROXY_port3}}|g; \\
            s|{{{{PROXY_port4}}}}|${{PROXY_port4}}|g; \\
            s|{{{{PROXY_port5}}}}|${{PROXY_port5}}|g; \\
            s|{{{{PROXY_port6}}}}|${{PROXY_port6}}|g; \\
            s|{{{{PROXY_port7}}}}|${{PROXY_port7}}|g; \\
            s|{{{{PROXY_port8}}}}|${{PROXY_port8}}|g; \\
            s|{{{{PROXY_port9}}}}|${{PROXY_port9}}|g; \\
            s|{{{{PROXY_port10}}}}|${{PROXY_port10}}|g; \\
            s|{{{{PROXY_port11}}}}|${{PROXY_port11}}|g; \\
            s|{{{{PROXY_port12}}}}|${{PROXY_port12}}|g; \\
            s|{{{{PROXY_port13}}}}|${{PROXY_port13}}|g; \\
            s|{{{{PROXY_port14}}}}|${{PROXY_port14}}|g; \\
            s|{{{{PROXY_port15}}}}|${{PROXY_port15}}|g; \\
            s|{{{{PROXY_port16}}}}|${{PROXY_port16}}|g; \\
            s|{{{{PROXY_port17}}}}|${{PROXY_port17}}|g; \\
            s|{{{{PROXY_port18}}}}|${{PROXY_port18}}|g; \\
            s|{{{{PROXY_port19}}}}|${{PROXY_port19}}|g; \\
            s|{{{{PROXY_port20}}}}|${{PROXY_port20}}|g; \\
            s|{{{{PROXY_port21}}}}|${{PROXY_port21}}|g; \\
            s|{{{{PROXY_port22}}}}|${{PROXY_port22}}|g; \\
            s|{{{{PROXY_port23}}}}|${{PROXY_port23}}|g; \\
            s|{{{{PROXY_port24}}}}|${{PROXY_port24}}|g; \\
            s|{{{{PROXY_port25}}}}|${{PROXY_port25}}|g; \\
            s|{{{{PROXY_port26}}}}|${{PROXY_port26}}|g; \\
            s|{{{{PROXY_port27}}}}|${{PROXY_port27}}|g; \\
            s|{{{{PROXY_port28}}}}|${{PROXY_port28}}|g; \\
            s|{{{{PROXY_port29}}}}|${{PROXY_port29}}|g; \\
            s|{{{{PROXY_port30}}}}|${{PROXY_port30}}|g; \\
            s|{{{{PROXY_port31}}}}|${{PROXY_port31}}|g; \\
            s|{{{{PROXY_port32}}}}|${{PROXY_port32}}|g; \\
            s|{{{{PROXY_port33}}}}|${{PROXY_port33}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"

            echo " Created local config: $CONFIG_FILE"

            DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
            kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
            echo " Copied config into pod: $POD_NAME"
            kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
            echo " Reloaded nginx in pod: $POD_NAME"
            echo " Labeling pod '$POD' as used..."
            kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
            """
            
            result["details"]["hostname"] = hostname
        elif action == "delete":
            kubectl_command = f"""
            echo "Deleting deployment for {job_name}..."
            
            # Get the nginx pod name
            nginx_pod=$(kubectl get pods -n {namespace} | grep -i "nginx" | cut -d " " -f1)
            echo "Found nginx pod: $nginx_pod"

            # Check if the nginx config file exists before attempting to read from it
            if kubectl exec -n "$NAMESPACE" "$nginx_pod" -- test -f /etc/nginx/conf.d/custom_{project_id}.conf 2>/dev/null; then
                echo "Found nginx config for project {project_id}"
                # Get the service name from the nginx configuration
                svc_name=$(kubectl exec -n "$NAMESPACE" "$nginx_pod" -- cat /etc/nginx/conf.d/custom_{project_id}.conf | grep -i "proxy_pass" | head -1 | awk '{{print $2}}' | sed -E 's|http://([^:]+):.*|\\1|')
                echo "Service name: $svc_name"

                # Extract deployment name from service name
                deployment_name=$(echo $svc_name | cut -d "-" -f2-3)
                echo "Deployment name: $deployment_name"
                
                pvc_name="pvc-$deployment_name"
                echo "PVC name: $pvc_name"
                
                # Delete the deployment and service
                echo "Deleting deployment and service"
                kubectl delete deployment "$deployment_name" -n "$NAMESPACE" --ignore-not-found=true
                kubectl delete service "$svc_name" -n "$NAMESPACE" --ignore-not-found=true
                kubectl delete pvc "$pvc_name" -n "$NAMESPACE" --ignore-not-found=true
                kubectl exec -n "$NAMESPACE" "$nginx_pod" -- rm -f /etc/nginx/conf.d/custom_{project_id}.conf
                echo "Cleanup completed successfully"
            else
                echo "No nginx configuration found for project {project_id}"
            fi
            """
            
        # Define the job manifest
        job_manifest = {
            "apiVersion": "batch/v1",
            "kind": "Job",
            "metadata": {
                "name": job_name,
                "namespace": namespace,
                "labels": {
                    "app": job_name,
                    "owner": "duploservices",
                    "tenantname": namespace,
                },
            },
            "spec": {
                "parallelism": 1,
                "completions": 1,
                "backoffLimit": 6,
                "template": {
                    "metadata": {
                        "labels": {
                            "app": job_name,
                            "owner": "duploservices",
                            "tenantname": namespace,
                        },
                    },
                    "spec": {
                        "serviceAccountName": "duploservices-kavia-dev-edit-user",
                        "restartPolicy": "Never",
                        "containers": [
                            {
                                "name": "duplocloudcodegen",
                                "image": "bitnami/kubectl:latest",
                                "command": ["/bin/sh", "-c"],
                                "args": [kubectl_command],
                                "env": [
                                    {"name": "ACTION", "value": action},
                                    {"name": "PROJECT_ID", "value": project_id},
                                    {"name": "ENV_NAME", "value": stage},
                                    {"name": "NAMESPACE", "value": namespace}
                                ],
                                "volumeMounts": [
                                    {"name": "codegenservicedeployment3", "mountPath": "/app"},
                                    {"name": "ingressservice1", "mountPath": "/app/ingress"},
                                    {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                    {"name": "nginx", "mountPath": "/app/nginx"},
                                ],
                            }
                        ],
                        "volumes": [
                            {
                                "name": "codegenservicedeployment3",
                                "configMap": {"name": "codegenservicedeployment3"},
                            },
                            {
                                "name": "ingressservice1",
                                "configMap": {"name": "ingressservice1"},
                            },
                            {
                                "name": "codegenpvc",
                                "configMap": {"name": "codegenpvc"},
                            },
                            {
                                "name": "nginx",
                                "configMap": {"name": "nginx"},
                            },
                        ],
                    },
                },
            },
        }
        
        # Create the job
        logging.info(f"Creating job {job_name} in namespace {namespace}")
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        logging.info(f"Job created: {response.metadata.name}")
        
        result["details"]["job_created"] = True
        
        # Wait for job completion if requested
        if wait_for_completion:
            job_status = wait_for_job_completion_with_timeout(
                batch_v1, job_name, namespace, timeout_seconds
            )
            
            result["details"]["job_completed"] = job_status["completed"]
            result["details"]["job_succeeded"] = job_status["succeeded"]
            
            if job_status["completed"] and job_status["succeeded"]:
                # Delete the job
                try:
                    batch_v1.delete_namespaced_job(
                        name=job_name,
                        namespace=namespace,
                        body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
                    )
                    logging.info(f"Deleted job: {job_name}")
                    result["details"]["job_deleted"] = True
                except Exception as e:
                    logging.warning(f"Failed to delete job {job_name}: {e}")
                    result["details"]["job_deleted"] = False
            elif job_status["completed"] and not job_status["succeeded"]:
                result["error"] = f"Job {job_name} failed"
                return result
        
        # Set success result
        result["success"] = True
        result["message"] = f"Successfully {action}d resources for project {project_id} in {stage}"
        
        return result
    
    except ApiException as e:
        error_msg = f"Kubernetes API error: {e}"
        logging.error(error_msg)
        result["error"] = error_msg
        return result
    except Exception as e:
        error_msg = f"Unexpected error: {e}"
        logging.error(error_msg)
        result["error"] = error_msg
        return result

def wait_for_job_completion_with_timeout(
    batch_v1: client.BatchV1Api,
    job_name: str,
    namespace: str,
    timeout_seconds: int = 300
) -> Dict[str, bool]:
    """
    Wait for a Kubernetes job to complete with a timeout.
    
    Args:
        batch_v1: Kubernetes BatchV1Api client
        job_name: Name of the job to wait for
        namespace: Namespace the job is in
        timeout_seconds: Maximum time to wait in seconds
    
    Returns:
        Dict with completion status
    """
    start_time = time.time()
    result = {
        "completed": False,
        "succeeded": False,
        "timed_out": False
    }
    
    while True:
        # Check for timeout
        if time.time() - start_time > timeout_seconds:
            logging.warning(f"Timed out waiting for job {job_name} to complete")
            result["timed_out"] = True
            return result
        
        # Get job status
        job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
        
        # Check if job is complete
        if job.status.completion_time is not None:
            result["completed"] = True
            
            # Check if job succeeded
            if job.status.succeeded is not None and job.status.succeeded > 0:
                logging.info(f"Job {job_name} completed successfully")
                result["succeeded"] = True
            else:
                logging.error(f"Job {job_name} failed")
                result["succeeded"] = False
                
            return result
            
        # Check for failures
        if job.status.failed is not None and job.status.failed > 0:
            logging.error(f"Job {job_name} failed")
            result["completed"] = True
            result["succeeded"] = False
            return result
            
        # Wait before checking again
        time.sleep(5)