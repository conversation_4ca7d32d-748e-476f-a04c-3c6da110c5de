from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
import time
from kubernetes import client, config

# # Redis connection details
# REDIS_HOST = "rediscodegen"
# REDIS_PORT = 6379
# LOCK_KEY_PREFIX = "pod_lock_"
# LOCK_VALUE = "locked"
# LOCK_TIMEOUT = 10  # seconds

# Kubernetes namespace and label
NAMESPACE = "default"
LABEL_SELECTOR = "pod-status=available"

# Prompt the user for PROJECT_ID, ENV_NAME, and ACTION
project_id = input("Enter the PROJECT_ID: ")
env_name = input("Enter the ENV_NAME: ")
name_space = "duploservices-kavia-qa"
action = input("Enter the ACTION (create/delete): ").strip().lower()

# Validate the inputs
if not project_id or not env_name or action not in ["create", "delete"]:
    raise ValueError("PROJECT_ID, ENV_NAME, and valid ACTION (create/delete) must be provided.")

# Load in-cluster configuration
config.load_kube_config()

# Create Kubernetes API client
batch_v1 = client.BatchV1Api()


# Define the job name and namespace
job_name = f"codegen-{project_id}-{env_name}-{action}"
namespace = "duploservices-kavia-qa"
previous_create_job = f"codegen-{project_id}-{env_name}-create"
hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"

# Define the command based on the action
if action == "create":
    # servicename = f"internal-{project_id}-{env_name}"
    kubectl_command = f"""
    # Redis connection details
    REDIS_HOST="rediscodegen"
    REDIS_PORT=6379
    LOCK_KEY_PREFIX="pod_lock_"
    LOCK_VALUE="locked"
    LOCK_TIMEOUT=10  # seconds

    # Kubernetes namespace and label
    NAMESPACE="duploservices-kavia-qa"
    LABEL_SELECTOR="pod-status=available"
    STATUSES="Running ContainerCreating Pending"  # Correct syntax for /bin/sh

    acquire_lock() {{
        pod_name=$1
        lock_key="$LOCK_KEY_PREFIX${{pod_name}}"
         echo "Running: redis-cli -h $REDIS_HOST -p $REDIS_PORT SETNX $lock_key $LOCK_VALUE"

        # Capture Redis return value
        result=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" SETNX "$lock_key" "$LOCK_VALUE")

        if [ "$result" -eq 1 ]; then
            # Optional: set expiration
            # redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" EXPIRE "$lock_key" "$LOCK_TIMEOUT"
            echo "Lock set"
            return 0
        else
            echo "Lock already exists"
            return 1
        fi
    }}

    release_lock() {{
        pod_name=$1
        lock_key="$LOCK_KEY_PREFIX$pod_name"

        # Delete the lock (DEL command)
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" DEL "$lock_key" > /dev/null
    }}

  
    # Define statuses
    STATUSES="Running ContainerCreating Pending"
    for STATUS in $STATUSES; do
        POD_APP=$(kubectl get pods -n "$NAMESPACE" -l pod-status=available --sort-by=.metadata.creationTimestamp \
        -o jsonpath="{{range .items[?(@.status.phase=='${{STATUS}}')]}}{{.metadata.name}} {{.metadata.labels.app}}\\\\n{{end}}" | head -n 1 ) 

        if [ -n "$POD_APP" ]; then

            # Extract pod name and app label from the selected pod
            POD=$(echo "$POD_APP" | head -n1 | awk '{{print $1}}')
            APP=$(echo "$POD_APP" | head -n1 | awk '{{print $2}}')

            echo " Using pod: $POD"
            echo " App label: $APP"
            echo " Environment: $ENV_NAME"

            if acquire_lock "$POD"; then
                echo "Lock acquired: $POD"
                # servicename = f"internal-{project_id}-{env_name}"
                echo "Creating deployment for {job_name}..."
                echo " Labeling pod '$POD' as used..."
                kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
                kubectl label deployment "$APP" -n "$NAMESPACE" pod-status=used --overwrite
    
                echo " Starting ingress update script..."
                CONFIG_FILE="/tmp/custom_${{PROJECT_ID}}.conf"
                POD_NAME="nginx-0"
                echo $NAMESPACE

                SERVER_NAME="vscode-internal-${{PROJECT_ID}}-${{ENV_NAME}}.qa.cloud.kavia.ai"
                PROXY_port1="http://internal-clusterip-${{APP}}:3000"
                PROXY_port2="http://internal-clusterip-${{APP}}:3001"
                PROXY_port3="http://internal-clusterip-${{APP}}:3002"
                PROXY_port4="http://internal-clusterip-${{APP}}:3003"
                PROXY_port5="http://internal-clusterip-${{APP}}:3004"
                PROXY_port6="http://internal-clusterip-${{APP}}:3005"
                PROXY_port7="http://internal-clusterip-${{APP}}:3006"
                PROXY_port8="http://internal-clusterip-${{APP}}:3007"
                PROXY_port9="http://internal-clusterip-${{APP}}:3008"
                PROXY_port10="http://internal-clusterip-${{APP}}:3009"
                PROXY_port11="http://internal-clusterip-${{APP}}:3010"
                PROXY_port12="http://internal-clusterip-${{APP}}:5000"
                PROXY_port13="http://internal-clusterip-${{APP}}:5001"
                PROXY_port14="http://internal-clusterip-${{APP}}:5002"
                PROXY_port15="http://internal-clusterip-${{APP}}:5003"
                PROXY_port16="http://internal-clusterip-${{APP}}:5004"
                PROXY_port17="http://internal-clusterip-${{APP}}:5005"
                PROXY_port18="http://internal-clusterip-${{APP}}:5006"
                PROXY_port19="http://internal-clusterip-${{APP}}:5007"
                PROXY_port20="http://internal-clusterip-${{APP}}:5008"
                PROXY_port21="http://internal-clusterip-${{APP}}:5009"
                PROXY_port22="http://internal-clusterip-${{APP}}:5010"
                PROXY_port23="http://internal-clusterip-${{APP}}:8000"
                PROXY_port24="http://internal-clusterip-${{APP}}:8001"
                PROXY_port25="http://internal-clusterip-${{APP}}:8002"
                PROXY_port26="http://internal-clusterip-${{APP}}:8003"
                PROXY_port27="http://internal-clusterip-${{APP}}:8004"
                PROXY_port28="http://internal-clusterip-${{APP}}:8005"
                PROXY_port29="http://internal-clusterip-${{APP}}:8006"
                PROXY_port30="http://internal-clusterip-${{APP}}:8007"
                PROXY_port31="http://internal-clusterip-${{APP}}:8008"
                PROXY_port32="http://internal-clusterip-${{APP}}:8009"
                PROXY_port33="http://internal-clusterip-${{APP}}:8010"
                PROXY="http://internal-${{APP}}:8080"
                echo " SERVER_NAME to be added: $SERVER_NAME"
                echo " PROXY to be routed: $PROXY"
                sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; \
                s|{{{{PROXY}}}}|${{PROXY}}|g; \
                s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g; \
                s|{{{{PROXY_port2}}}}|${{PROXY_port2}}|g; \
                s|{{{{PROXY_port3}}}}|${{PROXY_port3}}|g; \
                s|{{{{PROXY_port4}}}}|${{PROXY_port4}}|g; \
                s|{{{{PROXY_port5}}}}|${{PROXY_port5}}|g; \
                s|{{{{PROXY_port6}}}}|${{PROXY_port6}}|g; \
                s|{{{{PROXY_port7}}}}|${{PROXY_port7}}|g; \
                s|{{{{PROXY_port8}}}}|${{PROXY_port8}}|g; \
                s|{{{{PROXY_port9}}}}|${{PROXY_port9}}|g; \
                s|{{{{PROXY_port10}}}}|${{PROXY_port10}}|g; \
                s|{{{{PROXY_port11}}}}|${{PROXY_port11}}|g; \
                s|{{{{PROXY_port12}}}}|${{PROXY_port12}}|g; \
                s|{{{{PROXY_port13}}}}|${{PROXY_port13}}|g; \
                s|{{{{PROXY_port14}}}}|${{PROXY_port14}}|g; \
                s|{{{{PROXY_port15}}}}|${{PROXY_port15}}|g; \
                s|{{{{PROXY_port16}}}}|${{PROXY_port16}}|g; \
                s|{{{{PROXY_port17}}}}|${{PROXY_port17}}|g; \
                s|{{{{PROXY_port18}}}}|${{PROXY_port18}}|g; \
                s|{{{{PROXY_port19}}}}|${{PROXY_port19}}|g; \
                s|{{{{PROXY_port20}}}}|${{PROXY_port20}}|g; \
                s|{{{{PROXY_port21}}}}|${{PROXY_port21}}|g; \
                s|{{{{PROXY_port22}}}}|${{PROXY_port22}}|g; \
                s|{{{{PROXY_port23}}}}|${{PROXY_port23}}|g; \
                s|{{{{PROXY_port24}}}}|${{PROXY_port24}}|g; \
                s|{{{{PROXY_port25}}}}|${{PROXY_port25}}|g; \
                s|{{{{PROXY_port26}}}}|${{PROXY_port26}}|g; \
                s|{{{{PROXY_port27}}}}|${{PROXY_port27}}|g; \
                s|{{{{PROXY_port28}}}}|${{PROXY_port28}}|g; \
                s|{{{{PROXY_port29}}}}|${{PROXY_port29}}|g; \
                s|{{{{PROXY_port30}}}}|${{PROXY_port30}}|g; \
                s|{{{{PROXY_port31}}}}|${{PROXY_port31}}|g; \
                s|{{{{PROXY_port32}}}}|${{PROXY_port32}}|g; \
                s|{{{{PROXY_port33}}}}|${{PROXY_port33}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"

                echo " Created local config: $CONFIG_FILE"
                DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
                kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
                echo " Copied config into pod: $POD_NAME"
                kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
                echo " Reloaded nginx in pod: $POD_NAME"
                release_lock $POD
                break
            else
                echo "Lock was not acquired. retrying.."
                continue 1
            fi
        fi
    done

    sleep 999999
    """
elif action == "delete":
    kubectl_command = f"""
    echo "Deleting deployment for {job_name}..."
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
     s/{{{{ENV_NAME}}}}/{env_name}/g; \
     s/{{{{hostname}}}}/{hostname}/g; \
     s/{{{{servicename}}}}/internal-{project_id}-{env_name}/g" \
     /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
    sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
         s/{{{{ENV_NAME}}}}/{env_name}/g" \
         /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
    #sleep 99999
    kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true 
    kubectl delete -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml --ignore-not-found=true
    kubectl delete -f /tmp/ingress-patch-{project_id}-{env_name}.yaml -n {namespace} --ignore-not-found=true
    echo "Deleting previous job: {previous_create_job}..."
    kubectl delete job {previous_create_job} -n {namespace} --ignore-not-found=true
    echo "Cleanup completed!"
    """

# Define the job manifest
job_manifest = {
    "apiVersion": "batch/v1",
    "kind": "Job",
    "metadata": {
        "name": job_name,
        "namespace": namespace,
        "labels": {
            "app": job_name,
            "owner": "duploservices",
            "tenantname": namespace,
        },
    },
    "spec": {
        "parallelism": 1,
        "completions": 1,
        "backoffLimit": 6,
        "template": {
            "metadata": {
                "labels": {
                    "app": job_name,
                    "owner": "duploservices",
                    "tenantname": namespace,
                },
            },
            "spec": {
                "serviceAccountName": "duploservices-kavia-qa-edit-user",
                "restartPolicy": "Never",
                "containers": [
                    {
                        "name": "duplocloudcodegen",
                        "image": "************.dkr.ecr.us-west-2.amazonaws.com/codegen-job:custom",
                        "command": ["/bin/sh", "-c"],
                        "args": [kubectl_command],
                        "env": [
                            {"name": "ACTION", "value": action},
                            {"name": "PROJECT_ID", "value": project_id},
                            {"name": "ENV_NAME", "value": env_name},
                            {"name": "NAMESPACE", "value": name_space},
                        ],
                        "volumeMounts": [
                            {"name": "codegenserviceqa1", "mountPath": "/app"},
                            {"name": "ingressserviceqa", "mountPath": "/app/ingress"},
                            {"name": "codegenpvc", "mountPath": "/app/pvc"},
                            {"name": "nginx", "mountPath": "/app/nginx"},
                        ],
                    }
                ],
                "volumes": [
                    {
                        "name": "codegenserviceqa1",
                        "configMap": {"name": "codegenserviceqa1"},
                    },
                    {
                        "name": "ingressserviceqa",
                        "configMap": {"name": "ingressserviceqa"},
                    },
                    {
                        "name": "codegenpvc",
                        "configMap": {"name": "codegenpvc"},
                    },
                    {
                        "name": "nginx",
                        "configMap": {"name": "nginx"},
                    },
                ],
            },
        },
    },
}

# Monitor job completion and delete it upon success
def wait_for_job_completion(job_name, namespace):
    while True:
        job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
        if job.status.succeeded:
            print(f"Job {job_name} completed successfully, deleting...")
            batch_v1.delete_namespaced_job(
                name=job_name,
                namespace=namespace,
                body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
            )
            print(f"Deleted job: {job_name}")
            break
        time.sleep(5)

# Create the job
try:
    response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
    print(f"Job created: {response.metadata.name}")
    wait_for_job_completion(job_name, namespace)
    
    if action == "create":
        print(f"Ingress hostname: {hostname}")

except ApiException as e:
    print(f"An error occurred: {e}")
    raise