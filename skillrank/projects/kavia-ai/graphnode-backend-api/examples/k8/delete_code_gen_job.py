#!/usr/bin/env python3
import subprocess
import logging
import argparse
from kubernetes import client, config
from kubernetes.client.rest import ApiException
import sys
import time
import re
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def run_command(cmd, description=None):
    """
    Execute a shell command and return the result
    
    Args:
        cmd (str): Command to execute
        description (str, optional): Description of the command for logging
        
    Returns:
        tuple: (success, stdout, stderr)
    """
    if description:
        logger.info(f"Executing: {description}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return True, result.stdout.strip(), ""
        else:
            return False, "", result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def extract_project_ids_from_kubectl(resource_type="ingress", namespace="duploservices-kavia-dev", env_name="dev"):
    """
    Extract project IDs from kubectl output for a specific resource type
    
    Args:
        resource_type (str): Resource type (ingress, deployment, service)
        namespace (str): Kubernetes namespace
        env_name (str): Environment name suffix to look for
        
    Returns:
        list: Extracted project IDs
    """
    try:
        # Get list of resources
        cmd = f"kubectl get {resource_type} -n {namespace} -o json"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to get {resource_type} list: {result.stderr}")
            return []
            
        # Parse the JSON output
        resources = json.loads(result.stdout)
        project_ids = []
        
        # Extract project IDs based on naming pattern
        for item in resources.get('items', []):
            name = item.get('metadata', {}).get('name', '')
            
            # Match the pattern project_id-env_name
            if name.endswith(f"-{env_name}"):
                # Extract the project ID part
                project_id = name.rsplit(f"-{env_name}", 1)[0]
                
                # For services, they start with "internal-"
                if resource_type == "service" and project_id.startswith("internal-"):
                    project_id = project_id[9:]  # Remove "internal-" prefix
                    
                project_ids.append(project_id)
        
        logger.info(f"Extracted {len(project_ids)} project IDs from {resource_type}")
        return project_ids
        
    except Exception as e:
        logger.error(f"Error extracting project IDs from {resource_type}: {str(e)}")
        return []

def delete_resources_by_ids(project_ids, env_name="dev", namespace="duploservices-kavia-dev", resource_types=None):
    """
    Delete multiple resources by project IDs in the specified namespace.
    
    Args:
        project_ids (list): List of project IDs to delete
        env_name (str): Environment name
        namespace (str): The namespace to clean up
        resource_types (list): List of resource types to delete. If None, delete all.
                             Options: ['deployments', 'services', 'ingress', 'pods', 'jobs', 'pvc']
        
    Returns:
        dict: Summary of deleted resources
    """
    try:
        # Load kubernetes configuration
        config.load_kube_config()
        
        # Initialize clients
        apps_v1 = client.AppsV1Api()
        core_v1 = client.CoreV1Api()
        networking_v1 = client.NetworkingV1Api()
        batch_v1 = client.BatchV1Api()
        
        # Set default resource types if not specified
        if resource_types is None:
            resource_types = ['deployments', 'services', 'ingress', 'pods', 'jobs']
            
        # Initialize counters
        deleted = {
            "deployments": 0,
            "services": 0,
            "ingress": 0,
            "pods": 0,
            "jobs": 0
        }
        
        # Process each project ID
        for project_id in project_ids:
            logger.info(f"Processing resources for project ID: {project_id}")
            
            # 1. Delete deployments
            if 'deployments' in resource_types:
                try:
                    deployment_name = f"{project_id}-{env_name}"
                    logger.info(f"Attempting to delete deployment: {deployment_name}")
                    
                    try:
                        apps_v1.delete_namespaced_deployment(
                            name=deployment_name,
                            namespace=namespace,
                            body=client.V1DeleteOptions(
                                propagation_policy='Foreground',
                                grace_period_seconds=0
                            )
                        )
                        logger.info(f"Successfully deleted deployment: {deployment_name}")
                        deleted["deployments"] += 1
                    except ApiException as e:
                        if e.status != 404:  # Ignore "not found" errors
                            logger.error(f"Failed to delete deployment {deployment_name}: {e}")
                except Exception as e:
                    logger.error(f"Error deleting deployment for {project_id}: {e}")
            
            # 2. Delete services  
            if 'services' in resource_types:
                try:
                    service_name = f"internal-{project_id}-{env_name}"
                    logger.info(f"Attempting to delete service: {service_name}")
                    
                    try:
                        core_v1.delete_namespaced_service(
                            name=service_name,
                            namespace=namespace
                        )
                        logger.info(f"Successfully deleted service: {service_name}")
                        deleted["services"] += 1
                    except ApiException as e:
                        if e.status != 404:  # Ignore "not found" errors
                            logger.error(f"Failed to delete service {service_name}: {e}")
                except Exception as e:
                    logger.error(f"Error deleting service for {project_id}: {e}")
            
            # 3. Delete ingress
            if 'ingress' in resource_types:
                try:
                    ingress_name = f"{project_id}-{env_name}"
                    logger.info(f"Attempting to delete ingress: {ingress_name}")
                    
                    try:
                        networking_v1.delete_namespaced_ingress(
                            name=ingress_name,
                            namespace=namespace
                        )
                        logger.info(f"Successfully deleted ingress: {ingress_name}")
                        deleted["ingress"] += 1
                    except ApiException as e:
                        if e.status != 404:  # Ignore "not found" errors
                            logger.error(f"Failed to delete ingress {ingress_name}: {e}")
                except Exception as e:
                    logger.error(f"Error deleting ingress for {project_id}: {e}")
            
            # 4. Delete pods (optional, as they might be deleted with their parent resources)
            if 'pods' in resource_types:
                try:
                    pods = core_v1.list_namespaced_pod(namespace=namespace, label_selector=f"app={project_id}-{env_name}")
                    for pod in pods.items:
                        logger.info(f"Deleting pod: {pod.metadata.name}")
                        try:
                            core_v1.delete_namespaced_pod(
                                name=pod.metadata.name,
                                namespace=namespace,
                                body=client.V1DeleteOptions(
                                    grace_period_seconds=0
                                )
                            )
                            deleted["pods"] += 1
                        except ApiException as e:
                            if e.status != 404:  # Ignore "not found" errors
                                logger.error(f"Failed to delete pod {pod.metadata.name}: {e}")
                except Exception as e:
                    logger.error(f"Error deleting pods for {project_id}: {e}")
                    
            # 5. Delete jobs
            if 'jobs' in resource_types:
                try:
                    # Delete any job containing the project_id in its name
                    jobs = batch_v1.list_namespaced_job(namespace=namespace)
                    for job in jobs.items:
                        if f"{project_id}-{env_name}" in job.metadata.name:
                            logger.info(f"Deleting job: {job.metadata.name}")
                            try:
                                batch_v1.delete_namespaced_job(
                                    name=job.metadata.name,
                                    namespace=namespace,
                                    body=client.V1DeleteOptions(
                                        propagation_policy='Foreground',
                                        grace_period_seconds=0
                                    )
                                )
                                deleted["jobs"] += 1
                            except ApiException as e:
                                if e.status != 404:  # Ignore "not found" errors
                                    logger.error(f"Failed to delete job {job.metadata.name}: {e}")
                except Exception as e:
                    logger.error(f"Error deleting jobs for {project_id}: {e}")
            
        # Print summary for this batch
        total_deleted = sum(deleted.values())
        logger.info(f"Deleted {total_deleted} resources across {len(project_ids)} project IDs")
                
        return deleted
    
    except Exception as e:
        logger.error(f"Error while deleting resources by project IDs: {str(e)}")
        return deleted

def delete_codegen_resources(project_id=None, env_name=None, namespace="duploservices-kavia-dev", delete_all=False):
    """
    Delete all codegen resources in the specified namespace.
    
    Args:
        project_id (str, optional): Project ID to target specific resources
        env_name (str, optional): Environment name to target specific resources
        namespace (str): The namespace to clean up
        delete_all (bool): If True, delete all codegen resources regardless of project_id and env_name
        
    Returns:
        dict: Summary of deleted resources
    """
    try:
        # Load kubernetes configuration
        config.load_kube_config()
        
        # Initialize clients
        apps_v1 = client.AppsV1Api()
        core_v1 = client.CoreV1Api()
        networking_v1 = client.NetworkingV1Api()
        batch_v1 = client.BatchV1Api()
        
        # Initialize counters
        deleted = {
            "deployments": 0,
            "services": 0,
            "ingress": 0,
            "pods": 0,
            "jobs": 0,
        }
        
        # Build label selector
        label_selector = None
        name_filter = None
        
        if delete_all:
            logger.info(f"Deleting ALL codegen resources in namespace {namespace}")
            name_filter = "codegen"
        elif project_id and env_name:
            logger.info(f"Deleting codegen resources for project {project_id} in environment {env_name}")
            name_filter = f"codegen-{project_id}-{env_name}"
            
            # Also handle internal service name pattern
            service_name_filter = f"internal-{project_id}-{env_name}"
            hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"
        else:
            logger.error("Either provide project_id and env_name or set delete_all=True")
            return deleted
        
        # 1. Delete deployments
        try:
            deployments = apps_v1.list_namespaced_deployment(namespace=namespace)
            for deployment in deployments.items:
                should_delete = False
                deploy_name = deployment.metadata.name
                
                # For specific project_id
                if project_id and env_name and deploy_name == f"{project_id}-{env_name}":
                    should_delete = True
                # For delete_all: match any deployment ending with -dev
                elif delete_all and deploy_name.endswith(f"-{env_name}"):
                    should_delete = True
                # Original codegen detection as fallback
                elif name_filter in deploy_name:
                    should_delete = True
                    
                if should_delete:
                    logger.info(f"Deleting deployment: {deploy_name}")
                    try:
                        apps_v1.delete_namespaced_deployment(
                            name=deploy_name,
                            namespace=namespace,
                            body=client.V1DeleteOptions(
                                propagation_policy='Foreground',
                                grace_period_seconds=0
                            )
                        )
                        deleted["deployments"] += 1
                    except ApiException as e:
                        logger.error(f"Failed to delete deployment {deploy_name}: {e}")
        except ApiException as e:
            logger.error(f"Error listing deployments: {e}")
            
        # 2. Delete services
        try:
            services = core_v1.list_namespaced_service(namespace=namespace)
            for service in services.items:
                should_delete = False
                service_name = service.metadata.name
                
                # Check for internal-PROJECT_ID-ENV_NAME pattern
                if project_id and env_name and service_name == f"internal-{project_id}-{env_name}":
                    should_delete = True
                # For delete_all: match any service starting with internal- and ending with -dev
                elif delete_all and service_name.startswith("internal-") and service_name.endswith(f"-{env_name}"):
                    should_delete = True
                # Original codegen detection as fallback
                elif name_filter in service_name:
                    should_delete = True
                    
                if should_delete:
                    logger.info(f"Deleting service: {service_name}")
                    try:
                        core_v1.delete_namespaced_service(
                            name=service_name,
                            namespace=namespace
                        )
                        deleted["services"] += 1
                    except ApiException as e:
                        logger.error(f"Failed to delete service {service_name}: {e}")
        except ApiException as e:
            logger.error(f"Error listing services: {e}")
            
        # 3. Delete ingress resources
        try:
            ingresses = networking_v1.list_namespaced_ingress(namespace=namespace)
            for ingress in ingresses.items:
                should_delete = False
                ingress_name = ingress.metadata.name
                
                # Check if the ingress name follows the pattern PROJECT_ID-dev
                if project_id and env_name and ingress_name == f"{project_id}-{env_name}":
                    should_delete = True
                # For delete_all: match any ingress ending with -dev suffix
                elif delete_all and ingress_name.endswith(f"-{env_name}"):
                    should_delete = True
                # Check for hostname in rules as fallback
                elif project_id and env_name and hasattr(ingress.spec, 'rules') and ingress.spec.rules:
                    for rule in ingress.spec.rules:
                        if hasattr(rule, 'host') and rule.host and (
                            rule.host == hostname or 
                            f"{project_id}.{env_name}" in rule.host
                        ):
                            should_delete = True
                            break
                
                if should_delete:
                    logger.info(f"Deleting ingress: {ingress_name}")
                    try:
                        networking_v1.delete_namespaced_ingress(
                            name=ingress_name,
                            namespace=namespace
                        )
                        deleted["ingress"] += 1
                    except ApiException as e:
                        logger.error(f"Failed to delete ingress {ingress_name}: {e}")
        except ApiException as e:
            logger.error(f"Error listing ingresses: {e}")
            
        # 4. Delete pods (this might not be needed as they should be deleted with their parent resources)
        try:
            pods = core_v1.list_namespaced_pod(namespace=namespace)
            for pod in pods.items:
                if name_filter in pod.metadata.name:
                    logger.info(f"Deleting pod: {pod.metadata.name}")
                    try:
                        core_v1.delete_namespaced_pod(
                            name=pod.metadata.name,
                            namespace=namespace,
                            body=client.V1DeleteOptions(
                                grace_period_seconds=0
                            )
                        )
                        deleted["pods"] += 1
                    except ApiException as e:
                        logger.error(f"Failed to delete pod {pod.metadata.name}: {e}")
        except ApiException as e:
            logger.error(f"Error listing pods: {e}")
            
        # 5. Delete jobs
        try:
            jobs = batch_v1.list_namespaced_job(namespace=namespace)
            for job in jobs.items:
                if name_filter in job.metadata.name:
                    logger.info(f"Deleting job: {job.metadata.name}")
                    try:
                        batch_v1.delete_namespaced_job(
                            name=job.metadata.name,
                            namespace=namespace,
                            body=client.V1DeleteOptions(
                                propagation_policy='Foreground',
                                grace_period_seconds=0
                            )
                        )
                        deleted["jobs"] += 1
                    except ApiException as e:
                        logger.error(f"Failed to delete job {job.metadata.name}: {e}")
        except ApiException as e:
            logger.error(f"Error listing jobs: {e}")
            
            
        # Print summary
        total_deleted = sum(deleted.values())
        logger.info("=" * 50)
        logger.info("Deletion Summary:")
        for resource_type, count in deleted.items():
            logger.info(f"  {resource_type}: {count}")
        logger.info(f"Total resources deleted: {total_deleted}")
        logger.info("=" * 50)
            
        return deleted
            
    except Exception as e:
        logger.error(f"Error while deleting codegen resources: {str(e)}")
        return deleted

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Delete codegen resources in Kubernetes.')
    parser.add_argument('--project-id', '-p', help='Project ID to target specific resources')
    parser.add_argument('--env-name', '-e', help='Environment name to target specific resources')
    parser.add_argument('--namespace', '-n', default='duploservices-kavia-dev', help='Kubernetes namespace')
    parser.add_argument('--all', '-a', action='store_true', help='Delete all codegen resources')
    
    args = parser.parse_args()
    
    # if args.all:
    #     deleted = delete_codegen_resources(namespace=args.namespace, delete_all=True)
    # elif args.project_id and args.env_name:
    project_ids = [
        1, 11, 158, 2, 3, 30, 300, 413, 43, 480, 50, 55, 56, 6, 609, 
        6299, 64, 7, 71, 72, 805, 8146
    ]
    for project_id in project_ids:
        deleted = delete_codegen_resources(
            project_id=str(project_id),
            env_name='dev',
            namespace=args.namespace
        )
    else:
        parser.print_help()
        sys.exit(1)
    
    total_deleted = sum(deleted.values())
    print(f"Deleted {total_deleted} codegen resources")