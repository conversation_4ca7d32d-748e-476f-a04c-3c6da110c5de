#!/usr/bin/env python3
"""
Kubernetes Pod Assignment - Optimized for performance
"""
import datetime
import time
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import ConnectionFailure
import logging
import sys

# Configuration
MONGO_URI = "**************************************************************"
DB_NAME = "kubernetes_monitor"
PODS_COLLECTION = "pods_available"
USER_PODS_COLLECTION = "pods-user"
LOG_FILE = "/var/log/pod_assignment.log"

# Setup basic logging
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format='[%(asctime)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Global client to persist connection
_mongo_client = None

def get_mongo_client():
    """Get or create MongoDB client with connection pooling"""
    global _mongo_client
    if _mongo_client is None:
        _mongo_client = MongoClient(
            MONGO_URI,
            maxPoolSize=10,
            connect=False,
            socketTimeoutMS=2000,
            serverSelectionTimeoutMS=2000
        )
    return _mongo_client

def create_indexes():
    """Create necessary indexes only once"""
    try:
        client = get_mongo_client()
        db = client[DB_NAME]
        user_pods_collection = db[USER_PODS_COLLECTION]
        
        # Create indexes for faster lookups
        user_pods_collection.create_index([("project_id", ASCENDING)], background=True)
        user_pods_collection.create_index([("pod_id", ASCENDING)], background=True)
        
        logging.info("Indexes created successfully")
    except Exception as e:
        logging.error(f"Error creating indexes: {str(e)}")

# Call this once at startup
create_indexes()

def assign_pod_to_project(project_id):
    """
    Gets one available pod and assigns it to a project
    Optimized for performance
    """
    start_time = time.time()
    
    try:
        client = get_mongo_client()
        db = client[DB_NAME]
        pods_collection = db[PODS_COLLECTION]
        user_pods_collection = db[USER_PODS_COLLECTION]
        
        # Use findOneAndDelete for atomic operation
        selected_pod = pods_collection.find_one_and_delete(
            filter={},
            sort=[("timestamp", DESCENDING)]
        )
        
        if not selected_pod:
            return {"status": "error", "message": "No available pods found"}
        
        pod_name = selected_pod.get("name", "")
        pod_parts = pod_name.split("-")
        
        if not pod_parts or not pod_parts[0].isdigit():
            return {"status": "error", "message": f"Invalid pod name format: {pod_name}"}
        
        pod_prefix = pod_parts[0]
        
        # Prepare assignment data
        pod_assignment = {
            "project_id": project_id,
            "pod_id": pod_prefix,
            "pod_name": pod_name,
            "ip": selected_pod.get("ip"),
            "node": selected_pod.get("node"),
            "assigned_at": datetime.datetime.utcnow(),
            "status": "active"
        }
        
        # Use upsert for combined operation
        update_result = user_pods_collection.update_one(
            {"project_id": project_id},
            {"$set": pod_assignment},
            upsert=True
        )
        
        status = "created" if update_result.upserted_id else "updated"
        
        result = {
            "status": status, 
            "pod_id": pod_prefix, 
            "pod_name": pod_name,
            "pod_removed": True,
            "execution_time": time.time() - start_time
        }
        
        return result
        
    except Exception as e:
        error_msg = str(e)
        logging.error(f"Error assigning pod to project: {error_msg}")
        return {"status": "error", "message": error_msg}

# Main function ensures it runs only once
if __name__ == "__main__":
    # Explicitly add guard to prevent double execution
    _already_run = False
    
    if not _already_run:
        _already_run = True
        
        # Get project ID from command line if provided
        project_id = int(sys.argv[1]) if len(sys.argv) > 1 else 12345
        
        # Start timing
        start_time = time.time()
        
        # Perform the assignment
        result = assign_pod_to_project(project_id)
        
        # Calculate execution time
        execution_time = time.time() - start_time
        
        # Print results
        print(f"Assignment result: {result}")
        print(f"Execution time: {execution_time:.4f} seconds")