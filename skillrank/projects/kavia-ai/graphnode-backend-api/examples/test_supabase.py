
"""
Test script to verify run_sql() function works with supabase-py library.
Based on your existing codebase pattern.
"""

import os
from typing import Dict, Any, Optional

from supabase import Client, create_client




def test_supabase_run_sql():
    """Test the run_sql function using supabase-py library."""
    
    # Get credentials from environment
    supabase_url = os.getenv("SUPABASE_PROJECT_URL", "https://yvqemimphpkpxxrihxec.supabase.co")
    supabase_key = os.getenv("SUPABASE_ANON_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2cWVtaW1waHBrcHh4cmloeGVjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2MTUxNzQsImV4cCI6MjA2NzE5MTE3NH0.Gy6bWCnuqjRX4TcbEiqGpjZNkOB6hni2NqGZHQy6_9I")
    if not supabase_url or not supabase_key:
        print("❌ Missing SUPABASE_URL or SUPABASE_KEY environment variables")
        print("Set them in your .env file:")
        print("SUPABASE_URL=https://your-project.supabase.co")
        print("SUPABASE_KEY=your_anon_key")
        return False
    
    print(f"🔗 Connecting to: {supabase_url}")
    
    try:
        # Create Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)
        print("✅ Supabase client created successfully")
        
        # Test 1: Simple connection test
        print("\n📋 Test 1: Simple connection test")
        result = supabase.rpc("run_sql", {"query": "SELECT 1 as test"}).execute()
        print(f"✅ Connection test passed: {result.data}")
        
        # Test 2: Check current timestamp
        print("\n📋 Test 2: Current timestamp")
        result = supabase.rpc("run_sql", {
            "query": "SELECT CURRENT_TIMESTAMP as now, version() as db_version"
        }).execute()
        print(f"✅ Timestamp test: {result.data}")
        
        # Test 3: List existing tables
        print("\n📋 Test 3: List existing tables")
        result = supabase.rpc("run_sql", {
            "query": """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """
        }).execute()
        print(f"✅ Existing tables: {result.data}")
        
        # Test 4: Create a test table
        print("\n📋 Test 4: Create test table")
        result = supabase.rpc("run_sql", {
            "query": """
                CREATE TABLE IF NOT EXISTS test_table (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
        }).execute()
        print(f"✅ Table creation: {result.data}")
        
        # Test 5: Insert test data
        print("\n📋 Test 5: Insert test data")
        result = supabase.rpc("run_sql", {
            "query": """
                INSERT INTO test_table (name) 
                VALUES ('Test User 1'), ('Test User 2') 
                RETURNING *
            """
        }).execute()
        print(f"✅ Data inserted: {result.data}")
        
        # Test 6: Query the data
        print("\n📋 Test 6: Query test data")
        result = supabase.rpc("run_sql", {
            "query": "SELECT * FROM test_table ORDER BY id"
        }).execute()
        print(f"✅ Query results: {result.data}")
        
        # Test 7: Update data
        print("\n📋 Test 7: Update test data")
        result = supabase.rpc("run_sql", {
            "query": """
                UPDATE test_table 
                SET name = 'Updated User' 
                WHERE id = 1 
                RETURNING *
            """
        }).execute()
        print(f"✅ Update results: {result.data}")
        
        # Test 8: Delete test data
        print("\n📋 Test 8: Delete test data")
        result = supabase.rpc("run_sql", {
            "query": "DELETE FROM test_table WHERE id = 2 RETURNING *"
        }).execute()
        print(f"✅ Delete results: {result.data}")
        
        # Test 9: Drop test table
        print("\n📋 Test 9: Clean up - drop test table")
        result = supabase.rpc("run_sql", {
            "query": "DROP TABLE IF EXISTS test_table"
        }).execute()
        print(f"✅ Cleanup complete: {result.data}")
        
        print("\n🎉 All tests passed! Your run_sql() function is working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ API Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def create_sample_schema():
    """Create a sample schema for testing."""
    
    supabase_url = os.getenv("SUPABASE_PROJECT_URL", "https://yvqemimphpkpxxrihxec.supabase.co")
    supabase_key = os.getenv("SUPABASE_ANON_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2cWVtaW1waHBrcHh4cmloeGVjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2MTUxNzQsImV4cCI6MjA2NzE5MTE3NH0.Gy6bWCnuqjRX4TcbEiqGpjZNkOB6hni2NqGZHQy6_9I")
    if not supabase_url or not supabase_key:
        print("❌ Missing credentials")
        return False
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        
        print("🏗️  Creating sample schema...")
        
        # Users table
        print("📋 Creating users table...")
        result = supabase.rpc("run_sql", {
            "query": """
                CREATE TABLE IF NOT EXISTS users (
                    id SERIAL PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    full_name VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
        }).execute()
        print("✅ Users table created")
        
        # Posts table
        print("📋 Creating posts table...")
        result = supabase.rpc("run_sql", {
            "query": """
                CREATE TABLE IF NOT EXISTS posts (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    title VARCHAR(200) NOT NULL,
                    content TEXT,
                    published BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
        }).execute()
        print("✅ Posts table created")
        
        # Insert sample data
        print("📋 Inserting sample data...")
        result = supabase.rpc("run_sql", {
            "query": """
                INSERT INTO users (username, email, full_name) 
                VALUES 
                    ('john_doe', '<EMAIL>', 'John Doe'),
                    ('jane_smith', '<EMAIL>', 'Jane Smith')
                ON CONFLICT (username) DO NOTHING
                RETURNING *
            """
        }).execute()
        print(f"✅ Sample users: {result.data}")
        
        result = supabase.rpc("run_sql", {
            "query": """
                INSERT INTO posts (user_id, title, content, published) 
                VALUES 
                    (1, 'First Post', 'This is my first post!', true),
                    (1, 'Draft Post', 'This is a draft', false),
                    (2, 'Jane''s Post', 'Hello from Jane!', true)
                RETURNING *
            """
        }).execute()
        print(f"✅ Sample posts: {result.data}")
        
        # Query joined data
        print("📋 Querying joined data...")
        result = supabase.rpc("run_sql", {
            "query": """
                SELECT 
                    u.username,
                    u.full_name,
                    p.title,
                    p.content,
                    p.published,
                    p.created_at
                FROM users u
                JOIN posts p ON u.id = p.user_id
                WHERE p.published = true
                ORDER BY p.created_at DESC
            """
        }).execute()
        print(f"✅ Published posts: {result.data}")
        
        print("\n🎉 Sample schema created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating schema: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Supabase run_sql() function...")
    print("=" * 50)
    
    # Run basic tests
    if test_supabase_run_sql():
        print("\n" + "=" * 50)
        print("🎯 Would you like to create a sample schema? (y/n)")
        choice = input().lower().strip()
        
        if choice in ['y', 'yes']:
            create_sample_schema()
    
    print("\n✨ Test complete!")