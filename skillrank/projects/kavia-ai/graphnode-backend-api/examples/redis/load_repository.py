#!/usr/bin/env python3
"""
MongoDB Project Repository Path Extractor

This script connects to a MongoDB database and extracts the path
information for all project repositories in the specified collection.
"""

import asyncio
import os
import time
import uuid
import pymongo
from typing import List, Dict, Any
import logging
from datetime import datetime
import sys
from pprint import pprint

from app.core.websocket.client import WebSocketClient
from app.knowledge.redis_kg import add_redis_support_to_knowledge
from app.utils.kg_build.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_build.knowledge_helper import Knowledge_Helper, Reporter
from app.core.Settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# MongoDB connection parameters
MONGODB_URI = "mongodb://root:<EMAIL>:27017"
DATABASE_NAME = "pre_prod_T0038"
COLLECTION_NAME = "project_repositories"

def connect_to_mongodb() -> pymongo.MongoClient:
    """Establish connection to MongoDB."""
    try:
        client = pymongo.MongoClient(MONGODB_URI)
        # Check connection
        client.admin.command('ping')
        logger.info("Successfully connected to MongoDB")
        return client
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        sys.exit(1)

def get_repository_paths(client: pymongo.MongoClient) -> List[Dict[str, Any]]:
    """Extract repository paths from all documents in the collection."""
    try:
        db = client[DATABASE_NAME]
        collection = db[COLLECTION_NAME]
        
        # List to store path information
        paths_info = []
        
        # Retrieve all documents
        documents = collection.find({})
        
        # Process each document
        for doc in documents:
            project_id = doc.get("project_id")
            
            # Process each repository in the document
            for repo in doc.get("repositories", []):
                repo_name = repo.get("repository_name")
                
                # Process each branch in the repository
                for branch in repo.get("branches", []):
                    branch_name = branch.get("name")
                    builds = branch.get("builds")
                    
                    if builds and "path" in builds:
                        path = builds.get("path")
                        paths_info.append({
                            "project_id": project_id,
                            "repository_name": repo_name,
                            "branch_name": branch_name,
                            "path": path,
                            "build_id": builds.get("build_id")
                        })
        
        logger.info(f"Found {len(paths_info)} repository paths")
        return paths_info
    
    except Exception as e:
        logger.error(f"Error retrieving repository paths: {e}")
        return []

def save_to_file(paths_info: List[Dict[str, Any]], filename: str = "repository_paths.txt"):
    """Save path information to a file."""
    try:
        
        for info in paths_info:
            project_id = info['project_id']
            print(f"Project ID: {info['project_id']}\n")
            print(f"Repository: {info['repository_name']}\n")
            print(f"Branch: {info['branch_name']}\n")
            print(f"Build ID: {info['build_id']}\n")
            print(f"Path: {info['path']}\n")
            print("-" * 50 + "\n\n")
            session_id = str(uuid.uuid4())
            code_base_path = [
                KnowledgeCodeBase(info['path'], info['repository_name'])
            ]
            
            reporter = Reporter()
            knowledge_helper = Knowledge_Helper(session_id, reporter, os.getcwd(), code_base_path)    
            knowledge = Knowledge.getKnowledge(id=session_id)
            knowledge.start()

            # Wait for processing to complete
            attempt = 0
            max_attempts = 30  # 30 seconds timeout
            while knowledge._state != 2 and attempt < max_attempts:
                time.sleep(1)
                attempt += 1
                logger.info(f"Waiting for knowledge processing to complete: {attempt}/{max_attempts}")
            
            # If processing completed successfully
            if knowledge._state == 2:
                logger.info(f"Knowledge processing completed for session {session_id}")
                # Add Redis support
                if add_redis_support_to_knowledge(knowledge):
                    logger.info(f"Redis data loaded successfully for project {project_id}")
                else:
                    logger.warning(f"Failed to load Redis data for project {project_id}")
            else:
                logger.error(f"Knowledge processing timed out for session {session_id}")
                
        logger.info(f"Path information saved to {filename}")
    except Exception as e:
        logger.error(f"Error saving path information to file: {e}")

def main():
    """Main function to execute the script."""
    logger.info("Starting repository path extraction process")
    
    # Connect to MongoDB
    client = connect_to_mongodb()
    
    try:
        # Get repository paths
        paths_info = get_repository_paths(client)
        
        # Print paths to console
        if paths_info:
            print("\nRepository Paths:")
            print("="*80)
            for info in paths_info:
                print(f"Project ID: {info['project_id']}, Repository: {info['repository_name']}, Branch: {info['branch_name']}")
                print(f"Path: {info['path']}")
                print("-"*80)
        else:
            print("No repository paths found.")
        
        # Save paths to file
        save_to_file(paths_info)
        
    finally:
        # Close MongoDB connection
        client.close()
        logger.info("MongoDB connection closed")

if __name__ == "__main__":
    main()