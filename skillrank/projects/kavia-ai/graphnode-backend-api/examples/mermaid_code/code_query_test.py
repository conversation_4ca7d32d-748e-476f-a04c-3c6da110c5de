import asyncio
import re
from typing import List, Tuple

from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.logs_utils import get_path
from app.utils.mermaid_validator import fix_mermaid_with_llm, validate_mermaid_code


response_content = "Document:\n\n# System Architecture and Component Interaction Diagrams\n\n## Overview\n\nThis project centers around a modular architecture that integrates a language model wrapper for the Anthropic API, a code execution environment leveraging Docker containers, and a file validation subsystem. The system is designed to facilitate advanced language model interactions, including streaming completions and tool execution, with a focus on code generation and validation workflows. The following diagrams illustrate the relationships and data flows between the core components, providing a visual understanding of how the system operates as a cohesive whole.\n\n## 1. High-Level System Architecture\n\nAt the highest level, the system is composed of three primary modules: the Language Model Wrapper (Anthropic), the Code Execution Engine, and the File Validation Service. The Language Model Wrapper serves as the entry point for user prompts and orchestrates downstream tool invocations, including code execution and validation.\n\n```mermaid\nflowchart TD\n    subgraph User Interaction\n        User[User]\n    end\n\n    subgraph LLM_Wrapper\n        Anthropic[Anthropic API Wrapper]\n    end\n\n    subgraph Tools\n        CodeExec[CodeExecution (Docker)]\n        FileVal[FileValidation]\n    end\n\n    User --> Anthropic\n    Anthropic -- \"Streaming Completion / Tool Invocation\" --> CodeExec\n    CodeExec -- \"Execution Results\" --> Anthropic\n    Anthropic -- \"Validation Request\" --> FileVal\n    FileVal -- \"Validation Results\" --> Anthropic\n    Anthropic -- \"Response\" --> User\n```\n\n### Diagram Title: High-Level System Architecture\n\nThis diagram demonstrates the flow of information from the user through the Anthropic wrapper, which may invoke code execution or file validation tools as needed, before returning a response to the user.\n\n## 2. Anthropic API Wrapper Internal Workflow\n\nThe Anthropic class encapsulates the logic for interacting with the Anthropic API, handling both streaming completions and tool execution. When a user prompt is received, the wrapper determines whether to process the request directly or to invoke a tool such as code execution. The results from these tools are then integrated into the final response.\n\n```mermaid\nflowchart TD\n    A[Receive User Prompt] --> B{Requires Tool?}\n    B -- Yes --> C[Invoke Tool (e.g., CodeExecution)]\n    B -- No --> D[Call Anthropic API for Completion]\n    C --> E[Receive Tool Output]\n    D --> F[Receive API Completion]\n    E --> G[Integrate Tool Output]\n    F --> G\n    G[Compose Final Response] --> H[Stream/Return to User]\n```\n\n### Diagram Title: Anthropic API Wrapper Workflow\n\nThis diagram details the decision-making process within the Anthropic wrapper, highlighting how tool invocations are conditionally integrated into the language model's response pipeline.\n\n## 3. Code Execution Tool Lifecycle\n\nThe CodeExecution class is responsible for securely running user-generated code within Docker containers. This subsystem supports multiple programming languages and can interact with databases as required by the generated code. The lifecycle begins with code generation, followed by execution in an isolated environment, and concludes with the collection and return of results.\n\n```mermaid\nflowchart TD\n    A[Receive Code for Execution] --> B[Select Docker Image by Language]\n    B --> C[Launch Docker Container]\n    C --> D[Inject Code and Dependencies]\n    D --> E[Run Code]\n    E --> F[Capture Output/Errors]\n    F --> G[Return Results to Caller]\n```\n\n### Diagram Title: Code Execution Tool Lifecycle\n\nThis diagram illustrates the secure and modular approach to code execution, ensuring that each code snippet is run in an isolated environment tailored to its language requirements.\n\n## 4. File Validation Process\n\nThe FileValidation class provides mechanisms for verifying the integrity and correctness of files, which may be generated or manipulated during code execution. This process ensures that only valid files are used in subsequent operations, maintaining system reliability and security.\n\n```mermaid\nflowchart TD\n    A[Receive File for Validation] --> B[Perform Validation Checks]\n    B --> C{Is File Valid?}\n    C -- Yes --> D[Return Success]\n    C -- No --> E[Return Error/Failure]\n```\n\n### Diagram Title: File Validation Process\n\nThis diagram outlines the straightforward yet critical process of file validation, which acts as a safeguard within the broader workflow.\n\n## Conclusion\n\nThe system's architecture is characterized by clear separation of concerns and robust inter-component communication. The Anthropic wrapper acts as the central orchestrator, delegating specialized tasks to the code execution and validation modules as needed. This modular design not only enhances maintainability and scalability but also ensures that each component can evolve independently while maintaining seamless integration within the overall workflow. The provided diagrams collectively offer a comprehensive visualization of the system's structure and operational dynamics."


async def validate_and_fix_mermaid_in_response(response_content: str) -> str:
    llm = LLMInterface(str(get_path()), 'knowledge', 'test-123' ,int(2),'code_query')

    """
    Extract, validate, and fix all mermaid diagrams in the response content.
    Only updates content if diagrams are successfully fixed.
    
    Args:
        llm: LLM interface for fixing invalid diagrams
        response_content: The complete response content containing mermaid diagrams
        
    Returns:
        Updated response content with fixed mermaid diagrams (only if successfully fixed)
    """
    
    # Pattern to match mermaid code blocks
    mermaid_pattern = r'```mermaid\n(.*?)\n```'
    
    def extract_mermaid_blocks(content: str) -> List[Tuple[str, int, int]]:
        """Extract all mermaid blocks with their positions"""
        blocks = []
        for match in re.finditer(mermaid_pattern, content, re.DOTALL):
            mermaid_code = match.group(1).strip()
            start_pos = match.start()
            end_pos = match.end()
            blocks.append((mermaid_code, start_pos, end_pos))
        return blocks
    
    mermaid_blocks = extract_mermaid_blocks(response_content)
    
    if not mermaid_blocks:
        return response_content
    
    print(f"Found {len(mermaid_blocks)} mermaid blocks to validate")
    
    # Track if any diagrams were actually fixed
    any_fixed = False
    updated_content = response_content
    
    # Process blocks in reverse order to maintain position indices
    for mermaid_code, start_pos, end_pos in reversed(mermaid_blocks):
        is_valid, error_msg = validate_mermaid_code(mermaid_code)
        
        if not is_valid:
            print(f"Invalid mermaid diagram found. Attempting to fix...")
            
            # Try to fix the diagram
            fixed_diagram, is_fixed = await fix_mermaid_with_llm(llm, mermaid_code, error_msg)
            
            if is_fixed and fixed_diagram != mermaid_code:
                # Replace the invalid mermaid block with the fixed one
                fixed_block = f"```mermaid\n{fixed_diagram}\n```"
                updated_content = updated_content[:start_pos] + fixed_block + updated_content[end_pos:]
                any_fixed = True
                print("Mermaid diagram fixed successfully")
                print(fixed_block)
            else:
                print(f"Failed to fix mermaid diagram")
        else:
            print("Mermaid diagram is valid, no fix needed")
            print(mermaid_code)
            print("------------------------------------------")
    
    # Only return updated content if we actually fixed something
    if any_fixed:
        print("Returning updated content with fixed mermaid diagrams")
        return updated_content
    else:
        print("No diagrams were fixed, returning original content")
        return response_content


if __name__ == "__main__":
    results = asyncio.run(validate_and_fix_mermaid_in_response(response_content))
    # print(results)
    