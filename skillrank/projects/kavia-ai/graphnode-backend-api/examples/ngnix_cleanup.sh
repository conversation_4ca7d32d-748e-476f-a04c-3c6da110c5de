#!/bin/bash

# Script to automatically clean up invalid Nginx upstream entries
# Usage: ./nginx_cleanup.sh [--dry-run]

set -e

# Config directory
NGINX_CONF_DIR="/etc/nginx/conf.d"
DRY_RUN=false

# Parse arguments
if [[ "$1" == "--dry-run" ]]; then
  DRY_RUN=true
  echo "Running in dry-run mode (no actual changes will be made)"
fi

echo "=== Nginx Configuration Cleanup Tool ==="
echo "Started at $(date)"

# Function to get problematic config files directly from error output
find_and_remove_problematic_files() {
  local temp_file=$(mktemp)
  echo "Testing Nginx config to identify problematic files..."
  
  # Capture the nginx -s reload error output
  nginx -s reload 2> "$temp_file" || true
  
  # Display the captured error
  echo "Current Nginx errors:"
  cat "$temp_file"
  
  # Extract filenames from the error output
  local files=($(grep -o '/etc/nginx/conf.d/[^ :]*' "$temp_file" | sort -u))
  
  if [[ ${#files[@]} -eq 0 ]]; then
    echo "No problematic files found in the error output."
    rm "$temp_file"
    return 1
  fi
  
  echo "Found ${#files[@]} problematic file(s):"
  for file in "${files[@]}"; do
    echo "  - $file"
    
    if [[ "$DRY_RUN" == "false" ]]; then
      if [[ -f "$file" ]]; then
        echo "Removing file: $file"
        rm "$file"
      else
        echo "File does not exist: $file"
      fi
    else
      echo "[DRY RUN] Would remove: $file"
    fi
  done
  
  rm "$temp_file"
  return 0
}

# Main loop to find and fix issues
MAX_ATTEMPTS=1000
attempt=1

while [[ $attempt -le $MAX_ATTEMPTS ]]; do
  echo "--- Cleanup attempt $attempt of $MAX_ATTEMPTS ---"
  
  if find_and_remove_problematic_files; then
    if [[ "$DRY_RUN" == "false" ]]; then
      echo "Reloading Nginx after removing problematic files..."
      nginx -s reload 2>/dev/null || true
    else
      echo "[DRY RUN] Would reload Nginx here"
    fi
  else
    echo "No more problematic files found."
    break
  fi
  
  # Check if Nginx config is valid now
  if nginx -t &>/dev/null; then
    echo "SUCCESS: Nginx configuration is now valid!"
    break
  fi
  
  attempt=$((attempt + 1))
done

if [[ $attempt -gt $MAX_ATTEMPTS ]]; then
  echo "WARNING: Reached maximum attempts ($MAX_ATTEMPTS). Some issues may remain."
  exit 1
fi

echo "=== Cleanup completed ==="
echo "Completed at $(date)"

if [[ "$DRY_RUN" == "false" ]]; then
  echo "Final Nginx reload..."
  nginx -s reload
  echo "Nginx configuration is now valid."
else
  echo "[DRY RUN] Would perform final Nginx reload"
fi


exit 0