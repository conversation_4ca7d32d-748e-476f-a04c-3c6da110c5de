# Export the mongo db

```
mongodump --uri="mongodb+srv://<username>:<password>@cluster0.6nngekm.mongodb.net/?retryWrites=true&loadBalanced=false&replicaSet=atlas-ccsdv3-shard-0&readPreference=primary&srvServiceName=mongodb&connectTimeoutMS=10000&authSource=admin&authMechanism=SCRAM-SHA-1" --out=/tmp/mongo_db --gzip
```

# Import all the database into to mongo server

**Localhost:**

```
mongorestore --uri "******************************************************************************************************************************************************************************************" --numParallelCollections 4 /home/<USER>/mongodb_backup_local mongorestore --uri "****************************************************************************************************************************************************************************" --numParallelCollections 4 /tmp/mongo_db
```

**k8 import:**

```
mongorestore --uri="mongodb://root:<password>@mongodb.duploservices-kavia-beta.svc.cluster.local:27017/?authSource=admin&authMechanism=SCRAM-SHA-1" --gzip --numParallelCollections=4 /tmp/mongo_db
```

