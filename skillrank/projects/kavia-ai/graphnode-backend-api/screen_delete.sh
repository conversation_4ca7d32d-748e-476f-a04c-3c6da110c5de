kubectl get pods -l pod-status=used -n duploservices-kavia-dev --no-headers | while read -r line; do
  ORIGINAL_POD_NAME=$(echo "$line" | awk '{print $1}')
  STATUS=$(echo "$line" | awk '{print $3}')
  echo "****original pod name is****"
  echo $ORIGINAL_POD_NAME
  echo "****status of the pod is****"
  echo $STATUS
  if [[ "$STATUS" != "Running" ]]; then
    echo "Skipping non-running pod: $ORIGINAL_POD_NAME"
    continue
  fi
  echo "Checking for screen session in pod: $ORIGINAL_POD_NAME..."
  BASE_NAME=$(echo "$ORIGINAL_POD_NAME" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')
  if kubectl exec "$ORIGINAL_POD_NAME" -c "$BASE_NAME" -n duploservices-kavia-dev -- screen -ls | grep -q 'Detached\|Attached'; then
    echo "Screen session found in $ORIGINAL_POD_NAME"
    echo "$ORIGINAL_POD_NAME"
    continue
  else
    echo "No screen session in $ORIGINAL_POD_NAME, proceeding to delete" 
  fi
  BASE_NAME=$(echo "$ORIGINAL_POD_NAME" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')
  nginx_pod=$(kubectl get pods -n duploservices-kavia-dev | grep -i "nginx" | cut -d " " -f1)
  echo "Deleting deployment: $BASE_NAME"
  custom_file=`echo $BASE_NAME | cut -d "-" -f1`
  kubectl delete deployment $BASE_NAME -n duploservices-kavia-dev --ignore-not-found --grace-period=0 --force
  echo "Deleting service"
  service_name="internal-$BASE_NAME"
  service_cluster_name="internal-clusterip-$BASE_NAME"
  kubectl delete service $service_cluster_name -n duploservices-kavia-dev --ignore-not-found
  kubectl delete service $service_name -n duploservices-kavia-dev --ignore-not-found
  echo "INFO: Deleting the pvc: pvc-$BASE_NAME"
  pvc_name="pvc-$BASE_NAME"
  kubectl delete pvc $pvc_name -n duploservices-kavia-dev --ignore-not-found
  kubectl exec -n duploservices-kavia-dev "$nginx_pod" -- rm -f /etc/nginx/conf.d/custom_$custom_file.conf
done