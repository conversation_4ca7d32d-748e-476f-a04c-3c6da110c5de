# Use Ubuntu 22.04 as the base image
FROM ubuntu:22.04

# Set environment variables for non-interactive installation
ENV container=docker
ENV DEBIAN_FRONTEND=noninteractive
ENV AWS_REGION=us-east-1
ENV CODEARTIFACT_DOMAIN=kavia
ENV CODEARTIFACT_OWNER=058264095463
ENV CODEARTIFACT_REPO=kavia

# Build Arguments (available at build-time only)
ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY

# Persist AWS Credentials at Runtime
ENV AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
ENV AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}

RUN apt-get update -y && apt-get upgrade -y && apt-get install -y \
    iproute2 \
    docker.io\
    software-properties-common \
    build-essential \
    zlib1g-dev \
    libncurses5-dev \
    libgdbm-dev \
    rsync \
    git\
    nfs-common \
    libnss3-dev \
    libssl-dev \
    libreadline-dev \
    libffi-dev \
    libsqlite3-dev \
    libbz2-dev \
    liblzma-dev \
    libexpat1-dev \
    tk-dev \
    curl \
    wget \
    systemd \
    tzdata \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    unzip \
    nano \
    moreutils \
    autotools-dev \
    autoconf \
    && rm -rf /var/lib/apt/lists/* 

# Install Screen 4.09.01 from source
RUN wget https://ftp.gnu.org/gnu/screen/screen-4.9.1.tar.gz && \
    tar -xzf screen-4.9.1.tar.gz && \
    cd screen-4.9.1 && \
    ./configure --prefix=/usr/local --enable-colors256 && \
    make && \
    make install && \
    cd .. && \
    rm -rf screen-4.9.1 screen-4.9.1.tar.gz && \
    ln -sf /usr/local/bin/screen /usr/bin/screen
    
# Install Python 3.11 from source
RUN wget https://www.python.org/ftp/python/3.11.6/Python-3.11.6.tgz && \
    tar -xvf Python-3.11.6.tgz && \
    cd Python-3.11.6 && \
    ./configure --enable-optimizations && \
    make -j$(nproc) && \
    make altinstall && \
    make clean && \
    cd .. && \
    rm -rf Python-3.11.6 Python-3.11.6.tgz

# Ensure pip is installed
RUN python3.11 -m ensurepip && python3.11 -m pip install --upgrade pip

RUN git config --global user.name "CodeGen Bot" && \
    git config --global user.email "<EMAIL>"
    
# Create symlinks for easy access
RUN ln -sf /usr/local/bin/python3.11 /usr/bin/python3 && \
    ln -sf /usr/local/bin/python3.11 /usr/bin/python && \
    ln -s /usr/local/bin/pip3.11 /usr/bin/pip

# ✅ Install AWS CLI
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip aws

# Verify AWS CLI installation
RUN aws --version

# ✅ Install Terraform
RUN wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg > /dev/null && \
    echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com jammy main" | tee /etc/apt/sources.list.d/hashicorp.list && \
    apt update && \
    apt install -y terraform

# Verify Terraform installation
RUN terraform --version

# ✅ Configure AWS Credentials (only if provided)
RUN if [ -n "$AWS_ACCESS_KEY_ID" ] && [ -n "$AWS_SECRET_ACCESS_KEY" ]; then \
      aws configure set aws_access_key_id "$AWS_ACCESS_KEY_ID" && \
      aws configure set aws_secret_access_key "$AWS_SECRET_ACCESS_KEY" && \
      aws configure set region "$AWS_REGION"; \
    fi

# ✅ Authenticate with AWS CodeArtifact (optional at build-time)
RUN aws codeartifact login --tool pip --domain $CODEARTIFACT_DOMAIN --domain-owner $CODEARTIFACT_OWNER --repository $CODEARTIFACT_REPO || true

# Set working directory
WORKDIR /app

# Copy application files
COPY . /app

# ✅ Install dependencies
RUN pip install --no-cache-dir -r /app/requirements.txt
RUN pip install playwright
RUN playwright install chromium
 
# ✅ Set Timezone
RUN ln -fs /usr/share/zoneinfo/UTC /etc/localtime && dpkg-reconfigure -f noninteractive tzdata

# ✅ Expose necessary ports
EXPOSE 8765

# ✅ Start the application (replace this with your actual start command)
# command to run the application
# python3.11 -m uvicorn app.code_gen_app:app --port 8003 --reload
CMD ["python3.11", "-m", "uvicorn", "app.code_gen_app:app", "--port", "8003", "--reload"]
#CMD ["bash"]