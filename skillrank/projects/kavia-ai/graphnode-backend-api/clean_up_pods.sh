#!/bin/bash

# Enhanced Kubernetes cleanup script with configurable options

set -euo pipefail

# Default configuration
DEFAULT_SERVICE_LABEL="service=codegen"
EXCLUDE_PREFIX="zoltan-dev-"

# Environment namespace mappings
declare -A ENV_NAMESPACES=(
    ["dev"]="duploservices-k-dev01"
    ["qa"]="duploservices-k-qa01"
    ["beta"]="duploservices-k-beta01"
)

# Function to display usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Options:
    -e, --env ENV           Environment (dev|qa|beta) [required]
    -a, --available-only    Delete only pods with 'available' status in ConfigMap
    -l, --label LABEL       Service label selector (default: $DEFAULT_SERVICE_LABEL)
    -x, --exclude PREFIX    Exclude pods with prefix (default: $EXCLUDE_PREFIX)
    -h, --help             Show this help message

Examples:
    $0 -e dev                           # Delete all codegen pods in dev
    $0 -e qa --available-only           # Delete only available pods in qa
    $0 -e beta -l "app=myapp"          # Delete pods with custom label in beta

EOF
}

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

# Function to check if pod is available based on ConfigMap
is_pod_available() {
    local pod_name="$1"
    local namespace="$2"
    local configmap_name="pod-status-$(echo "$pod_name" | cut -d "-" -f1,2)"
    
    # Check if ConfigMap exists and get pod-status
    local status
    status=$(kubectl get configmap "$configmap_name" -n "$namespace" \
        -o jsonpath='{.data.pod-status}' 2>/dev/null || echo "")
    
    [[ "$status" == "available" ]]
}

# Function to get filtered pods
get_filtered_pods() {
    local namespace="$1"
    local service_label="$2"
    local exclude_prefix="$3"
    local available_only="$4"
    
    local all_pods
    all_pods=$(kubectl get pods -l "$service_label" -n "$namespace" --no-headers | \
        awk '{print $1}' | grep -v "^$exclude_prefix" || true)
    
    if [[ -z "$all_pods" ]]; then
        return 0
    fi
    
    if [[ "$available_only" == "true" ]]; then
        local filtered_pods=()
        while IFS= read -r pod_name; do
            [[ -z "$pod_name" ]] && continue
            if is_pod_available "$pod_name" "$namespace"; then
                filtered_pods+=("$pod_name")
            fi
        done <<< "$all_pods"
        
        if [[ ${#filtered_pods[@]} -gt 0 ]]; then
            printf '%s\n' "${filtered_pods[@]}"
        fi
    else
        echo "$all_pods"
    fi
}

# Function to delete resources in parallel
delete_resources_parallel() {
    local namespace="$1"
    shift
    local deployments=("$@")
    
    [[ ${#deployments[@]} -eq 0 ]] && return 0
    
    local services_internal=()
    local services_clusterip=()
    local pvcs=()
    local configmaps=()
    
    for deployment in "${deployments[@]}"; do
        services_internal+=("internal-$deployment")
        services_clusterip+=("internal-clusterip-$deployment")
        pvcs+=("pvc-$deployment")
        configmaps+=("pod-status-$deployment")
    done
    
    log "Starting parallel deletion of resources..."
    
    # Delete all resource types in parallel
    {
        log "Deleting deployments: ${deployments[*]}"
        kubectl delete deployment "${deployments[@]}" -n "$namespace" --ignore-not-found=true
    } &
    
    {
        log "Deleting internal services: ${services_internal[*]}"
        kubectl delete service "${services_internal[@]}" -n "$namespace" --ignore-not-found=true
    } &
    
    {
        log "Deleting clusterip services: ${services_clusterip[*]}"
        kubectl delete service "${services_clusterip[@]}" -n "$namespace" --ignore-not-found=true
    } &
    
    {
        log "Deleting PVCs: ${pvcs[*]}"
        kubectl delete pvc "${pvcs[@]}" -n "$namespace" --ignore-not-found=true
    } &
    
    {
        log "Deleting ConfigMaps: ${configmaps[*]}"
        kubectl delete configmap "${configmaps[@]}" -n "$namespace" --ignore-not-found=true
    } &
    
    wait
    log "All resources deleted successfully!"
}

# Main function
main() {
    local environment=""
    local available_only="false"
    local service_label="$DEFAULT_SERVICE_LABEL"
    local exclude_prefix="$EXCLUDE_PREFIX"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                environment="$2"
                shift 2
                ;;
            -a|--available-only)
                available_only="true"
                shift
                ;;
            -l|--label)
                service_label="$2"
                shift 2
                ;;
            -x|--exclude)
                exclude_prefix="$2"
                shift 2
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                echo "Error: Unknown option $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate required parameters
    if [[ -z "$environment" ]]; then
        echo "Error: Environment is required (-e|--env)"
        usage
        exit 1
    fi
    
    if [[ -z "${ENV_NAMESPACES[$environment]:-}" ]]; then
        echo "Error: Invalid environment '$environment'. Valid options: ${!ENV_NAMESPACES[*]}"
        exit 1
    fi
    
    local namespace="${ENV_NAMESPACES[$environment]}"
    
    log "Configuration:"
    log "  Environment: $environment"
    log "  Namespace: $namespace"
    log "  Service Label: $service_label"
    log "  Exclude Prefix: $exclude_prefix"
    log "  Available Only: $available_only"
    
    # Get filtered pods
    local pods
    pods=$(get_filtered_pods "$namespace" "$service_label" "$exclude_prefix" "$available_only")
    
    if [[ -z "$pods" ]]; then
        log "No pods found matching criteria"
        exit 0
    fi
    
    log "Found pods: $(echo "$pods" | tr '\n' ' ')"
    
    # Extract unique deployment names
    local deployments=()
    while IFS= read -r pod_name; do
        [[ -z "$pod_name" ]] && continue
        local deployment_name
        deployment_name=$(echo "$pod_name" | cut -d "-" -f1,2)
        deployments+=("$deployment_name")
    done <<< "$pods"
    
    # Remove duplicates
    IFS=$'\n' read -d '' -r -a unique_deployments < <(printf '%s\n' "${deployments[@]}" | sort -u && printf '\0') || true
    
    log "Unique deployments to delete: ${unique_deployments[*]}"
    log "Processing ${#unique_deployments[@]} deployments with their associated resources"
    
    # Delete resources
    delete_resources_parallel "$namespace" "${unique_deployments[@]}"
}

# Execute main function with all arguments
main "$@"