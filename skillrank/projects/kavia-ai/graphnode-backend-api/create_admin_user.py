#!/usr/bin/env python3
"""
Admin User Creation Script for AWS Cognito

This script creates an admin user in AWS Cognito with confirmed password and custom attributes.
It uses AWS credentials from app/core/Settings.py
"""

import boto3
import sys
import os
import logging
from datetime import datetime
from botocore.exceptions import ClientError

# Add the app directory to Python path to import settings
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app.core.Settings import settings
except ImportError as e:
    print(f"Error importing settings: {e}")
    print("Make sure you're running this script from the project root directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AdminUserCreator:
    """Class to handle admin user creation in AWS Cognito"""
    
    def __init__(self, user_pool_id):
        """Initialize the AdminUserCreator with AWS settings"""
        try:
            self.cognito_client = boto3.client(
                'cognito-idp',
                region_name=settings.AWS_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            self.user_pool_id = user_pool_id
            self.client_id = settings.AWS_COGNITO_APP_CLIENT_ID
            logger.info("Successfully initialized Cognito client")
            
        except Exception as e:
            logger.error(f"Failed to initialize Cognito client: {e}")
            raise e
    
    def check_custom_attributes_schema(self):
        """
        Check if required custom attributes exist in User Pool schema
        
        Returns:
            dict: Dictionary with attribute names as keys and existence status as values
        """
        try:
            response = self.cognito_client.describe_user_pool(UserPoolId=self.user_pool_id)
            user_pool = response['UserPool']
            
            # Get existing custom attributes from schema
            existing_attributes = set()
            for attr in user_pool.get('SchemaAttributes', []):
                if attr['Name'].startswith('custom:'):
                    existing_attributes.add(attr['Name'])
            
            # Required custom attributes
            required_attributes = {
                'custom:Name',
                'custom:Designation', 
                'custom:Department',
                'custom:tenant_id',
                'custom:is_admin',
                'custom:free_user'
            }
            
            # Check which attributes exist
            attribute_status = {}
            for attr in required_attributes:
                attribute_status[attr] = attr in existing_attributes
            
            return attribute_status
            
        except Exception as e:
            logger.error(f"Error checking custom attributes schema: {e}")
            return {}
    
    def display_schema_instructions(self, missing_attributes, existing_attributes):
        """Display instructions for adding missing custom attributes"""
        print("\n" + "="*60)
        print("⚠️  SOME CUSTOM ATTRIBUTES MISSING FROM USER POOL SCHEMA")
        print("="*60)
        
        if existing_attributes:
            print(f"\n✅ Found {len(existing_attributes)} existing custom attributes:")
            for attr in sorted(existing_attributes):
                print(f"   • {attr}")
        
        if missing_attributes:
            print(f"\n❌ Missing {len(missing_attributes)} custom attributes:")
            for attr in sorted(missing_attributes):
                print(f"   • {attr}")
            
            print("\nTo add missing custom attributes:")
            print("\n1. Go to AWS Cognito Console")
            print(f"2. Select User Pool: {self.user_pool_id}")
            print("3. Go to 'Sign-up experience' → 'Custom attributes'")
            print("4. Add the following custom attributes:\n")
            
            for attr in sorted(missing_attributes):
                attr_name = attr.replace('custom:', '')
                print(f"   • {attr}")
                print(f"     - Name: {attr_name}")
                print(f"     - Type: String")
                print(f"     - Mutable: Yes")
                print(f"     - Max length: 256")
                print("")
            
            print("5. Save the changes")
        
        print("\n📝 NOTE: User will be created with available attributes only.")
        print("You can add missing attributes later and update the user.")
        print("\n" + "="*60)
    
    def create_admin_user(self, email, name, password, department="Admin", designation="Administrator"):
        """
        Create an admin user in Cognito with confirmed password and custom attributes
        
        Args:
            email (str): User's email address (will be used as username)
            name (str): User's full name
            password (str): User's password
            department (str): User's department (default: "Admin")
            designation (str): User's designation (default: "Administrator")
        
        Returns:
            dict: Result of user creation
        """
        try:
            logger.info(f"Creating admin user: {email}")
            
            # Check custom attributes schema first
            attribute_status = self.check_custom_attributes_schema()
            existing_attributes = [attr for attr, exists in attribute_status.items() if exists]
            missing_attributes = [attr for attr, exists in attribute_status.items() if not exists]
            
            # Build user attributes based on available schema
            user_attributes = [
                {'Name': 'email', 'Value': email},
                {'Name': 'email_verified', 'Value': 'true'}
            ]
            
            # Define all possible custom attributes with their values
            potential_attributes = {
                'custom:Name': name,
                'custom:Designation': designation,
                'custom:Department': department,
                'custom:tenant_id': settings.KAVIA_SUPER_TENANT_ID,
                'custom:is_admin': 'true',
                'custom:free_user': 'false'
            }
            
            # Only add attributes that exist in the schema
            added_attributes = []
            skipped_attributes = []
            for attr_name, attr_value in potential_attributes.items():
                if attr_name in existing_attributes:
                    user_attributes.append({'Name': attr_name, 'Value': attr_value})
                    added_attributes.append(attr_name)
                else:
                    skipped_attributes.append(attr_name)
            
            if missing_attributes:
                logger.warning(f"Some custom attributes missing from schema. Skipping: {', '.join(skipped_attributes)}")
                logger.info(f"Creating user with available attributes: {', '.join(added_attributes)}")
            
            # Create user in Cognito with all attributes
            user_response = self.cognito_client.admin_create_user(
                UserPoolId=self.user_pool_id,
                Username=email,
                UserAttributes=user_attributes,
                MessageAction='SUPPRESS',  # Don't send email notification
                TemporaryPassword=password
            )
            
            logger.info(f"User created successfully with custom attributes: {user_response['User']['Username']}")
            
            # Set permanent password (confirm password)
            self.cognito_client.admin_set_user_password(
                UserPoolId=self.user_pool_id,
                Username=email,
                Password=password,
                Permanent=True
            )
            
            logger.info(f"Password set as permanent for user: {email}")
            
            # Enable the user (in case it was disabled)
            self.cognito_client.admin_enable_user(
                UserPoolId=self.user_pool_id,
                Username=email
            )
            
            logger.info(f"User enabled: {email}")
            
            # Build success message
            success_msg = f'Admin user {email} created successfully with confirmed password'
            if added_attributes:
                success_msg += f' and {len(added_attributes)} custom attributes'
            if skipped_attributes:
                success_msg += f' (skipped {len(skipped_attributes)} unavailable attributes)'
            
            return {
                'success': True,
                'user_id': user_response['User']['Username'],
                'email': email,
                'name': name,
                'added_attributes': added_attributes,
                'skipped_attributes': skipped_attributes,
                'message': success_msg
            }
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            
            if error_code == 'UsernameExistsException':
                logger.warning(f"User {email} already exists")
                return {
                    'success': False,
                    'error': 'User already exists',
                    'message': f'User {email} already exists in the user pool'
                }
            elif error_code == 'InvalidPasswordException':
                logger.error(f"Invalid password for user {email}: {error_message}")
                return {
                    'success': False,
                    'error': 'Invalid password',
                    'message': f'Password does not meet requirements: {error_message}'
                }
            elif 'schema' in error_message.lower() or 'attribute' in error_message.lower():
                logger.error(f"Schema/Attribute error for user {email}: {error_message}")
                # Re-check schema and display instructions
                attribute_status = self.check_custom_attributes_schema()
                existing_attributes = [attr for attr, exists in attribute_status.items() if exists]
                missing_attributes = [attr for attr, exists in attribute_status.items() if not exists]
                if missing_attributes:
                    self.display_schema_instructions(missing_attributes, existing_attributes)
                return {
                    'success': False,
                    'error': 'Schema error',
                    'message': f'Custom attribute schema error: {error_message}'
                }
            else:
                logger.error(f"Cognito error creating user {email}: {error_code} - {error_message}")
                return {
                    'success': False,
                    'error': error_code,
                    'message': error_message
                }
                
        except Exception as e:
            logger.error(f"Unexpected error creating user {email}: {e}")
            return {
                'success': False,
                'error': 'Unexpected error',
                'message': str(e)
            }
    
    def check_user_exists(self, email):
        """
        Check if user already exists in the user pool
        
        Args:
            email (str): User's email address
            
        Returns:
            bool: True if user exists, False otherwise
        """
        try:
            self.cognito_client.admin_get_user(
                UserPoolId=self.user_pool_id,
                Username=email
            )
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'UserNotFoundException':
                return False
            else:
                logger.error(f"Error checking user existence: {e}")
                raise e
    
    def get_user_details(self, email):
        """
        Get user details from Cognito
        
        Args:
            email (str): User's email address
            
        Returns:
            dict: User details or None if not found
        """
        try:
            response = self.cognito_client.admin_get_user(
                UserPoolId=self.user_pool_id,
                Username=email
            )
            
            # Parse user attributes
            attributes = {}
            for attr in response.get('UserAttributes', []):
                attributes[attr['Name']] = attr['Value']
            
            return {
                'username': response['Username'],
                'user_status': response['UserStatus'],
                'enabled': response['Enabled'],
                'user_create_date': response['UserCreateDate'],
                'user_last_modified_date': response['UserLastModifiedDate'],
                'attributes': attributes
            }
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'UserNotFoundException':
                return None
            else:
                logger.error(f"Error getting user details: {e}")
                raise e


def main():
    """Main function to create admin user with interactive prompts"""
    print("=== AWS Cognito Admin User Creator ===\n")
    
    try:
        # Get Cognito User Pool ID with validation
        print("AWS Cognito Configuration:")
        print("Note: User Pool ID should be in format: us-east-1_AbCdEfGhI")
        user_pool_id = input("User Pool ID: ").strip()
        
        if not user_pool_id:
            print("Error: User Pool ID is required!")
            return
        
        # Validate User Pool ID format
        import re
        pool_id_pattern = r'^[\w-]+_[0-9a-zA-Z]+$'
        if not re.match(pool_id_pattern, user_pool_id):
            print(f"Error: Invalid User Pool ID format!")
            print(f"User Pool ID should match pattern: region_randomstring")
            print(f"Example: us-east-1_AbCdEfGhI")
            print(f"You entered: {user_pool_id}")
            return
        
        # Initialize the creator
        creator = AdminUserCreator(user_pool_id)
        
        # Check schema first
        print("\n🔍 Checking User Pool schema for required custom attributes...")
        attribute_status = creator.check_custom_attributes_schema()
        existing_attributes = [attr for attr, exists in attribute_status.items() if exists]
        missing_attributes = [attr for attr, exists in attribute_status.items() if not exists]
        
        if missing_attributes:
            print(f"\n⚠️ Found {len(existing_attributes)} of {len(attribute_status)} custom attributes in schema!")
            creator.display_schema_instructions(missing_attributes, existing_attributes)
            
            # Ask user if they want to continue
            print(f"\nUser will be created with {len(existing_attributes)} available custom attributes.")
            continue_creation = input("Do you want to continue? (y/N): ").strip().lower()
            if continue_creation != 'y':
                print("Operation cancelled.")
                return
        else:
            print("✅ All required custom attributes found in schema!")
        
        # Get user input
        print("\nEnter admin user details:")
        email = input("Email (username): ").strip()
        name = input("Full Name: ").strip()
        password = input("Password: ").strip()
        department = input("Department [Admin]: ").strip() or "Admin"
        designation = input("Designation [Administrator]: ").strip() or "Administrator"
        
        if not email or not name or not password:
            print("Error: Email, name, and password are required!")
            return
        
        print(f"\nCreating admin user with the following details:")
        print(f"Email: {email}")
        print(f"Name: {name}")
        print(f"Department: {department}")
        print(f"Designation: {designation}")
        print(f"User Pool ID: {creator.user_pool_id}")
        print(f"Tenant ID: {settings.KAVIA_SUPER_TENANT_ID}")
        print(f"Admin Status: True")
        print(f"Free User: False")
        
        confirmation = input("\nProceed? (y/N): ").strip().lower()
        if confirmation != 'y':
            print("Operation cancelled.")
            return
        
        # Check if user already exists
        if creator.check_user_exists(email):
            print(f"\nUser {email} already exists!")
            
            # Show existing user details
            user_details = creator.get_user_details(email)
            if user_details:
                print(f"Existing user details:")
                print(f"  Status: {user_details['user_status']}")
                print(f"  Enabled: {user_details['enabled']}")
                print(f"  Created: {user_details['user_create_date']}")
                print(f"  Custom Attributes:")
                for attr_name, attr_value in user_details['attributes'].items():
                    if attr_name.startswith('custom:'):
                        print(f"    {attr_name}: {attr_value}")
                
            overwrite = input("Do you want to continue anyway? (y/N): ").strip().lower()
            if overwrite != 'y':
                print("Operation cancelled.")
                return
        
        # Create the user
        print(f"\nCreating user...")
        result = creator.create_admin_user(
            email=email,
            name=name,
            password=password,
            department=department,
            designation=designation
        )
        
        # Display result
        if result['success']:
            print(f"\n✅ SUCCESS: {result['message']}")
            print(f"User ID: {result['user_id']}")
            print(f"Email: {result['email']}")
            print(f"Name: {result['name']}")
            
            # Get and display user details with custom attributes
            user_details = creator.get_user_details(email)
            if user_details:
                print(f"\nUser created with the following attributes:")
                for attr_name, attr_value in user_details['attributes'].items():
                    if attr_name.startswith('custom:') or attr_name in ['email', 'sub']:
                        print(f"  {attr_name}: {attr_value}")
            
            print(f"\nThe user can now log in with:")
            print(f"  Username: {result['email']}")
            print(f"  Password: [the password you provided]")
        else:
            print(f"\n❌ FAILED: {result['message']}")
            print(f"Error: {result['error']}")
        
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        logger.error(f"Script failed: {e}")
        print(f"\n❌ Script failed: {e}")


if __name__ == "__main__":
    main()