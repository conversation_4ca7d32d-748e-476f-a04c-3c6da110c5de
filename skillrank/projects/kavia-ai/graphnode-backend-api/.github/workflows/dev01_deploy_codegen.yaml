name: Codegenservice Deployment

on:
  push:
    branches:
      - develop
  workflow_dispatch:

env:
  DUPLO_HOST: https://duplo.cloud.kavia.ai
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DUPLO_TENANT: k-dev01
  REPO_NAME: codegenservice

jobs:
  build:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    outputs:
      backend_image: ${{ steps.build-and-push-codegenservice.outputs.image }}:${{ github.sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Duplo Setup
        uses: duplocloud/actions@main

      - name: Build and Push codegenservice Docker Image
        uses: duplocloud/actions/build-image@main
        id: build-and-push-codegenservice
        with:
          push: true
          repo: ${{ env.REPO_NAME }}
          dockerfile: Dockerfile.dev
          platforms: linux/amd64
          cache: false
          build-args: |
            AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}
          tags: ${{ github.sha }}

  deploy:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Duplo Setup
        uses: duplocloud/actions@main
        with:
          admin: true

      - name: Create Kubeconfig
        run: |
          aws eks update-kubeconfig \
            --name duploinfra-dev \
            --region us-west-2

        # Step 1: Update the Duplo config‑map before touching any pods
      - name: update image-id for codegen in duploctl
        id: new_tag
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          echo $GIT_SHA
          NEW_IMAGE_TAG=${IMAGE##*:}
          echo "NEW_IMAGE_TAG=$NEW_IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Checkout CodeGenerationAgent repo
        uses: actions/checkout@v4
        with:
          repository: Kavia-ai/CodeGenerationAgent
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          path: dev-docker-id

      - name: Extract Kavia-dev-container image tag from docker_version.py
        id: kavia
        run: |
          set -e
          echo "Reading version from docker_version.py"
          KAVIA_IMAGE_TAG=$(grep KAVIA_CONTAINER_VERSION dev-docker-id/src/code_generation_core_agent/docker_version.py | cut -d '"' -f2)
          echo "Kavia tag: $KAVIA_IMAGE_TAG"
          echo "kavia_image_tag=$KAVIA_IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Update image tags in Duplo configmap
        env:
          IMAGE: "${{ needs.build.outputs.codegen_image }}"
          KAVIA_TAG: "${{ steps.kavia.outputs.kavia_image_tag }}"
          NEW_IMAGE_TAG: "${{ steps.new_tag.outputs.NEW_IMAGE_TAG}}"
        run: |
          set -e


          echo "Codegen tag: $NEW_IMAGE_TAG"
          echo "Kavia tag: $KAVIA_TAG"

          echo "Fetching current configmap"
          duploctl configmap find codegenservicedeploymentdevconfig -o yaml > /tmp/test7.yaml

          CODEGEN_PREFIX="127214169382.dkr.ecr.us-west-2.amazonaws.com/codegenservice"

          echo "Updating codegen image..."
          sed -i -E "s|(${CODEGEN_PREFIX}):[a-zA-Z0-9._-]+|\1:${NEW_IMAGE_TAG}|g" /tmp/test7.yaml

          echo "Updating FLUTTER_IMAGE_TAG env var..."
          sed -i -E 's|(name: FLUTTER_IMAGE_TAG\s+value: ")[^"]+|\1'"$KAVIA_TAG"'|g' /tmp/test7.yaml

          echo "Final configmap after update:"
          cat /tmp/test7.yaml

          echo "Applying update to Duplo configmap..."
          duploctl configmap update codegenservicedeploymentdevconfig -f /tmp/test7.yaml

      # Step 2: Graceful deployment with buffer management (Race Condition Fixed)
      - name: Deploy to DEV environment (Safe Buffer Rotation)
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          NAMESPACE=duploservices-k-dev01
          SERVICE=codegen
          WAVE_SIZE=4           # Update 4 pods per wave
          MIN_BUFFER=4          # Always keep minimum 4 pods available
          WAIT_TIME=300         # Wait for pods to be ready before next wave

          # Function to check if pod is still available for update
          is_pod_available() {
            local pod_name="$1"
            local pod_full_name
            
            # Find the current pod with this deployment name
            pod_full_name=$(kubectl get pods -l service=$SERVICE -n $NAMESPACE --no-headers | grep "^${pod_name}-" | head -1 | awk '{print $1}')
            
            if [[ -z "$pod_full_name" ]]; then
              echo "Pod $pod_name not found"
              return 1
            fi
            
            # Check if pod is still running
            local status
            status=$(kubectl get pod "$pod_full_name" -n $NAMESPACE --no-headers | awk '{print $3}')
            if [[ "$status" != "Running" ]]; then
              echo "Pod $pod_name is not running (status: $status)"
              return 1
            fi
            
            # Check for screen sessions (critical check)
            if kubectl exec "$pod_full_name" -c "$pod_name" -n $NAMESPACE -- screen -ls 2>/dev/null | grep -q 'Detached\|Attached'; then
              echo "Pod $pod_name now has active screen session - skipping"
              CONFIGMAP_NAME="pod-status-${pod_name}"
              kubectl patch configmap "$CONFIGMAP_NAME" -n $NAMESPACE \
                --type merge \
                -p "{\"data\": {\"deployment-status\": \"not-up-to-date\", \"image-sha\": \"${GIT_SHA}\"}}" \
                2>/dev/null || echo "Warning: Could not update config map $CONFIGMAP_NAME"
              return 1
            fi
            
            return 0
          }

          # Collect initial available pods
          AVAILABLE_PODS=()
          while IFS= read -r line; do
            if [[ -z "$line" ]]; then
              continue
            fi
            
            read -r POD READY STATUS RESTARTS AGE <<< "$line"
            echo "Processing pod: $POD with status: $STATUS"
            
            if [[ "$STATUS" != "Running" ]]; then
              echo "Skipping non-running pod: $POD"
              continue
            fi

            # Skip zoltan-dev pods
            if [[ "$POD" =~ ^zoltan-dev- ]]; then
              echo "Skipping zoltan-dev pod: $POD"
              continue
            fi

            BASE=$(echo "$POD" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')

            if kubectl exec "$POD" -c "$BASE" -n $NAMESPACE -- screen -ls 2>/dev/null | grep -q 'Detached\|Attached'; then
              echo "Screen session detected in $POD - skipping image update"
              CONFIGMAP_NAME="pod-status-${BASE}"
              kubectl patch configmap "$CONFIGMAP_NAME" -n $NAMESPACE \
                --type merge \
                -p "{\"data\": {\"deployment-status\": \"not-up-to-date\", \"image-sha\": \"${GIT_SHA}\"}}" \
                2>/dev/null || echo "Warning: Could not update config map $CONFIGMAP_NAME"
              continue
            fi

            AVAILABLE_PODS+=("$BASE")
            echo "Added $BASE to initial available pods list"
          done < <(kubectl get pods -l service=$SERVICE -n $NAMESPACE --no-headers)

          TOTAL_INITIAL=${#AVAILABLE_PODS[@]}
          echo "Initial available pods for update: $TOTAL_INITIAL"

          # Safety check
          if [ "$TOTAL_INITIAL" -le "$MIN_BUFFER" ]; then
            echo "Not enough pods ($TOTAL_INITIAL) to maintain minimum buffer ($MIN_BUFFER). Skipping deployment."
            exit 1
          fi

          # Buffer Rotation System with Real-time Validation
          UPDATED_PODS=()       # Track pods that have been updated (new buffer)
          REMAINING_PODS=("${AVAILABLE_PODS[@]}")  # Copy of pods still needing update
          TOTAL_UPDATED=0
          WAVE=1

          echo "Starting Safe Buffer Rotation Deployment"
          echo "Strategy: Update pods in waves with real-time availability checks"
          echo "Plan: Update pods using buffer rotation with race condition protection"

          while [ ${#REMAINING_PODS[@]} -gt 0 ]; do
            echo
            echo "=== WAVE $wave ==="
            echo "Status: ${#UPDATED_PODS[@]} updated, ${#REMAINING_PODS[@]} remaining"
            
            # Calculate how many we can update in this wave
            AVAILABLE_BUFFER_COUNT=${#UPDATED_PODS[@]}
            
            # Determine wave size based on available buffer
            if [ "$AVAILABLE_BUFFER_COUNT" -ge "$MIN_BUFFER" ]; then
              # We have updated pods as buffer, can be more aggressive
              CURRENT_WAVE_SIZE=$((${#REMAINING_PODS[@]} < WAVE_SIZE * 2 ? ${#REMAINING_PODS[@]} : WAVE_SIZE * 2))
              echo "Using updated pods as buffer - can attempt $CURRENT_WAVE_SIZE pods"
            else
              # Still building initial buffer, be conservative
              MAX_UPDATE=$((${#REMAINING_PODS[@]} + ${#UPDATED_PODS[@]} - MIN_BUFFER))
              CURRENT_WAVE_SIZE=$((MAX_UPDATE < WAVE_SIZE ? MAX_UPDATE : WAVE_SIZE))
              echo "Building initial buffer - can attempt $CURRENT_WAVE_SIZE pods"
            fi
            
            # Ensure we don't try to update more pods than remaining
            if [ "$CURRENT_WAVE_SIZE" -gt "${#REMAINING_PODS[@]}" ]; then
              CURRENT_WAVE_SIZE=${#REMAINING_PODS[@]}
            fi
            
            # Safety check
            if [ "$CURRENT_WAVE_SIZE" -le 0 ]; then
              echo "Cannot update more pods safely. Stopping."
              break
            fi
            
            echo "Wave $WAVE: Attempting to update $CURRENT_WAVE_SIZE pods with real-time validation"
            
            # Update pods in current wave with real-time checks
            WAVE_UPDATED_PODS=()
            WAVE_SKIPPED_PODS=()
            
            for i in $(seq 0 $((CURRENT_WAVE_SIZE - 1))); do
              if [ $i -lt ${#REMAINING_PODS[@]} ]; then
                DEPLOYMENT=${REMAINING_PODS[$i]}
                echo "Checking real-time availability of $DEPLOYMENT..."
                
                # CRITICAL: Re-check availability just before update
                if is_pod_available "$DEPLOYMENT"; then
                  echo "Updating deployment/$DEPLOYMENT (Wave $WAVE: $((i + 1))/$CURRENT_WAVE_SIZE)"
                  
                  if kubectl set image deployment/$DEPLOYMENT $DEPLOYMENT=$IMAGE -n $NAMESPACE 2>&1 | grep -q 'not found'; then
                    echo "  Deployment $DEPLOYMENT not found, skipping"
                  else
                    echo "  Deployment $DEPLOYMENT update initiated"
                    WAVE_UPDATED_PODS+=("$DEPLOYMENT")
                  fi
                else
                  echo "  Pod $DEPLOYMENT is no longer available - skipped for user safety"
                  WAVE_SKIPPED_PODS+=("$DEPLOYMENT")
                fi
              fi
            done
            
            # Remove both updated and skipped pods from remaining list
            TEMP_REMAINING=()
            for pod in "${REMAINING_PODS[@]}"; do
              if [[ ! " ${WAVE_UPDATED_PODS[@]} " =~ " ${pod} " ]] && [[ ! " ${WAVE_SKIPPED_PODS[@]} " =~ " ${pod} " ]]; then
                TEMP_REMAINING+=("$pod")
              fi
            done
            REMAINING_PODS=("${TEMP_REMAINING[@]}")
            
            echo "Wave $WAVE results:"
            echo "  - Updated: ${#WAVE_UPDATED_PODS[@]} pods"
            echo "  - Skipped (now in use): ${#WAVE_SKIPPED_PODS[@]} pods"
            echo "  - Remaining: ${#REMAINING_PODS[@]} pods"
            
            # Wait for this wave to complete and become ready
            if [ ${#WAVE_UPDATED_PODS[@]} -gt 0 ]; then
              echo "Waiting $WAIT_TIME seconds for wave $WAVE pods to become ready..."
              sleep $WAIT_TIME
              
              # Check readiness and add to updated pods list
              WAVE_READY_COUNT=0
              for deployment in "${WAVE_UPDATED_PODS[@]}"; do
                echo "Checking readiness of $deployment..."
                if kubectl rollout status deployment/$deployment -n $NAMESPACE --timeout=180s; then
                  echo "  $deployment is ready"
                  UPDATED_PODS+=("$deployment")
                  TOTAL_UPDATED=$((TOTAL_UPDATED + 1))
                  WAVE_READY_COUNT=$((WAVE_READY_COUNT + 1))
                else
                  echo "  Warning: $deployment rollout may still be in progress"
                  # Still add to updated list, but with warning
                  UPDATED_PODS+=("$deployment")
                  TOTAL_UPDATED=$((TOTAL_UPDATED + 1))
                fi
              done
              
              echo "Wave $WAVE completed: $WAVE_READY_COUNT/${#WAVE_UPDATED_PODS[@]} pods ready"
              echo "Buffer rotation: ${#UPDATED_PODS[@]} pods now serve as buffer for remaining ${#REMAINING_PODS[@]} pods"
            fi
            
            WAVE=$((WAVE + 1))
            
            # Safety check to prevent infinite loops
            if [ "$WAVE" -gt 20 ]; then
              echo "Maximum wave limit reached. Stopping for safety."
              break
            fi
          done

          echo
          echo "Safe Buffer Rotation Deployment Completed!"
          echo "Final Summary:"
          echo "  - Initial available pods: $TOTAL_INITIAL"
          echo "  - Successfully updated: $TOTAL_UPDATED"
          echo "  - Update percentage: $(( TOTAL_UPDATED * 100 / TOTAL_INITIAL ))%"
          echo "  - Total waves: $((WAVE - 1))"
          echo "  - Race condition protection: Active"

          if [ "$TOTAL_UPDATED" -eq "$TOTAL_INITIAL" ]; then
            echo "SUCCESS: ALL INITIALLY AVAILABLE PODS UPDATED WITH USER PROTECTION!"
          else
            echo "Status: $TOTAL_UPDATED/$TOTAL_INITIAL pods updated"
            echo "Note: Some pods were protected from update due to active usage"
          fi

          # Final verification
          echo
          echo "Final Verification:"
          kubectl get pods -l service=$SERVICE -n $NAMESPACE -o custom-columns="NAME:.metadata.name,IMAGE:.spec.containers[0].image,STATUS:.status.phase"

          echo
          echo "Image summary:"
          kubectl get pods -l service=$SERVICE -n $NAMESPACE -o jsonpath='{.items[*].spec.containers[0].image}' | tr ' ' '\n' | cut -d: -f2 | sort | uniq -c

  notify:
    name: Slack Notification
    runs-on: ubuntu-latest
    needs:
      - build
      - deploy
    if: always() && needs.build.result != 'skipped' && needs.deploy.result != 'skipped'
    steps:
      - name: Debug Job Results
        run: |
          echo "build.result  = '${{ needs.build.result }}'"
          echo "deploy.result = '${{ needs.deploy.result }}'"

      - name: Slack Deployment Succeeded
        if: ${{ needs.build.result == 'success' && needs.deploy.result == 'success' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ *Code Generation deployment complete* on branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\"}" \
            ${{ secrets.SLACK_HOOK_DEV_DEPLOY }}

      - name: Slack Deployment Failed
        if: ${{ needs.build.result == 'failure' || needs.deploy.result == 'failure' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 *Code Generation deployment failed* on branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\nView logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}" \
            ${{ secrets.SLACK_HOOK_DEV_DEPLOY }}
