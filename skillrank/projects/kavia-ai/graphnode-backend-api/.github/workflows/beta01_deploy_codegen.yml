name: Codegenservice Deployment

on:
  push:
    branches:
      - beta
  workflow_dispatch:

env:
  DUPLO_HOST: https://duplo.cloud.kavia.ai
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DUPLO_TENANT: k-beta01
  REPO_NAME: codegenservice

jobs:
  build:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    outputs:
      backend_image: ${{ steps.build-and-push-codegenservice.outputs.image }}:${{ github.sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Duplo Setup
        uses: duplocloud/actions@main

      - name: Build and Push codegenservice Docker Image
        uses: duplocloud/actions/build-image@main
        id: build-and-push-codegenservice
        with:
          push: true
          repo: ${{ env.REPO_NAME }}
          dockerfile: Dockerfile.dev
          platforms: linux/amd64
          cache: false
          build-args: |
            AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}
          tags: ${{ github.sha }}

  deploy:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Duplo Setup
        uses: duplocloud/actions@main
        with:
          admin: true

      - name: Create Kubeconfig
        run: |
          aws eks update-kubeconfig \
            --name duploinfra-preprod \
            --region us-west-2

      # Step 1: Update Duplo ConfigMap
      - name: update image-id for codegen in duploctl
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          echo $GIT_SHA
          NEW_IMAGE_TAG=${IMAGE##*:}
          echo $NEW_IMAGE_TAG
          echo "finding config map"
          duploctl configmap find codegenservicedeploymentbetaconfig -o yaml > /tmp/test7.yaml
          cat /tmp/test7.yaml
          echo "defining image prefix"
          IMAGE_PREFIX="127214169382.dkr.ecr.us-west-2.amazonaws.com/codegenservice"
          echo "using sed"
          sed -i -E "s|(${IMAGE_PREFIX}):[a-zA-Z0-9._-]+|\1:${NEW_IMAGE_TAG}|g" /tmp/test7.yaml
          echo "opening final file"
          cat /tmp/test7.yaml
          echo "updating existing config map"
          duploctl configmap update codegenservicedeploymentbetaconfig -f /tmp/test7.yaml

      - name: Graceful Deployment to BETA environment (Buffer Rotation)
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          NAMESPACE=duploservices-k-beta01
          SERVICE=codegen
          WAVE_SIZE=6           # Update 6 pods per wave (Beta has ~40 pods)
          MIN_BUFFER=8          # Keep minimum 8 pods available  
          WAIT_TIME=300         # Wait for pods to be ready before next wave

          # Collect all available pods
          AVAILABLE_PODS=()
          while IFS= read -r line; do
            if [[ -z "$line" ]]; then
              continue
            fi
            
            read -r POD READY STATUS RESTARTS AGE <<< "$line"
            echo "Processing pod: $POD with status: $STATUS"
            
            if [[ "$STATUS" != "Running" ]]; then
              echo "Skipping non-running pod: $POD"
              continue
            fi

            BASE=$(echo "$POD" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')

            if kubectl exec "$POD" -c "$BASE" -n $NAMESPACE -- screen -ls 2>/dev/null | grep -q 'Detached\|Attached'; then
              echo "Screen session detected in $POD - skipping image update"
              CONFIGMAP_NAME="pod-status-${BASE}"
              echo "Patching config map $CONFIGMAP_NAME for tracking"
              kubectl patch configmap "$CONFIGMAP_NAME" -n $NAMESPACE \
                --type merge \
                -p "{\"data\": {\"deployment-status\": \"not-up-to-date\", \"image-sha\": \"${GIT_SHA}\"}}" \
                2>/dev/null || echo "Warning: Could not update config map $CONFIGMAP_NAME"
              continue
            fi

            AVAILABLE_PODS+=("$BASE")
            echo "Added $BASE to available pods list"
          done < <(kubectl get pods -l service=$SERVICE -n $NAMESPACE --no-headers)

          TOTAL_AVAILABLE=${#AVAILABLE_PODS[@]}
          echo "Total available pods for update: $TOTAL_AVAILABLE"
          echo "Pool status check:"
          echo "- Pool maintained by: codegen-pool-maintainer-2 cronjob"
          echo "- Available for update: $TOTAL_AVAILABLE pods"
          echo "- Will maintain buffer: $MIN_BUFFER pods"

          # Safety check
          if [ "$TOTAL_AVAILABLE" -le "$MIN_BUFFER" ]; then
            echo "Not enough pods ($TOTAL_AVAILABLE) to maintain minimum buffer ($MIN_BUFFER). Skipping deployment."
            exit 1
          fi

          # Buffer Rotation System for Beta
          UPDATED_PODS=()       # Track pods that have been updated (new buffer)
          REMAINING_PODS=("${AVAILABLE_PODS[@]}")  # Copy of pods still needing update
          TOTAL_UPDATED=0
          WAVE=1

          echo "Starting Buffer Rotation Deployment for Beta"
          echo "Strategy: Update pods in waves, use updated pods as new buffer"
          echo "Plan: Update ALL $TOTAL_AVAILABLE pods using buffer rotation"

          while [ ${#REMAINING_PODS[@]} -gt 0 ]; do
            echo
            echo "=== WAVE $WAVE ==="
            echo "Status: ${#UPDATED_PODS[@]} updated, ${#REMAINING_PODS[@]} remaining"
            
            # Calculate how many we can update in this wave
            AVAILABLE_BUFFER_COUNT=${#UPDATED_PODS[@]}
            
            # Determine wave size based on available buffer
            if [ "$AVAILABLE_BUFFER_COUNT" -ge "$MIN_BUFFER" ]; then
              # We have updated pods as buffer, can be more aggressive
              CURRENT_WAVE_SIZE=$((${#REMAINING_PODS[@]} < WAVE_SIZE * 2 ? ${#REMAINING_PODS[@]} : WAVE_SIZE * 2))
              echo "Using updated pods as buffer - can update $CURRENT_WAVE_SIZE pods"
            else
              # Still building initial buffer, be conservative
              MAX_UPDATE=$((${#REMAINING_PODS[@]} + ${#UPDATED_PODS[@]} - MIN_BUFFER))
              CURRENT_WAVE_SIZE=$((MAX_UPDATE < WAVE_SIZE ? MAX_UPDATE : WAVE_SIZE))
              echo "Building initial buffer - can update $CURRENT_WAVE_SIZE pods"
            fi
            
            # Ensure we don't try to update more pods than remaining
            if [ "$CURRENT_WAVE_SIZE" -gt "${#REMAINING_PODS[@]}" ]; then
              CURRENT_WAVE_SIZE=${#REMAINING_PODS[@]}
            fi
            
            # Safety check
            if [ "$CURRENT_WAVE_SIZE" -le 0 ]; then
              echo "Cannot update more pods safely. Stopping."
              break
            fi
            
            echo "Wave $WAVE: Updating $CURRENT_WAVE_SIZE pods"
            echo "Buffer calculation: ${#UPDATED_PODS[@]} updated + $((${#REMAINING_PODS[@]} - CURRENT_WAVE_SIZE)) remaining = $((${#UPDATED_PODS[@]} + ${#REMAINING_PODS[@]} - CURRENT_WAVE_SIZE)) available"
            
            # Update pods in current wave
            WAVE_UPDATED_PODS=()
            for i in $(seq 0 $((CURRENT_WAVE_SIZE - 1))); do
              if [ $i -lt ${#REMAINING_PODS[@]} ]; then
                DEPLOYMENT=${REMAINING_PODS[$i]}
                echo "Updating deployment/$DEPLOYMENT (Wave $WAVE: $((i + 1))/$CURRENT_WAVE_SIZE)"
                
                if kubectl set image deployment/$DEPLOYMENT $DEPLOYMENT=$IMAGE -n $NAMESPACE 2>&1 | grep -q 'not found'; then
                  echo "  Deployment $DEPLOYMENT not found, skipping"
                else
                  echo "  Deployment $DEPLOYMENT update initiated"
                  WAVE_UPDATED_PODS+=("$DEPLOYMENT")
                fi
              fi
            done
            
            # Remove updated pods from remaining list
            TEMP_REMAINING=()
            for pod in "${REMAINING_PODS[@]}"; do
              if [[ ! " ${WAVE_UPDATED_PODS[@]} " =~ " ${pod} " ]]; then
                TEMP_REMAINING+=("$pod")
              fi
            done
            REMAINING_PODS=("${TEMP_REMAINING[@]}")
            
            echo "Wave $WAVE initiated: ${#WAVE_UPDATED_PODS[@]} pods updating"
            
            # Wait for this wave to complete and become ready
            if [ ${#WAVE_UPDATED_PODS[@]} -gt 0 ]; then
              echo "Waiting $WAIT_TIME seconds for wave $WAVE pods to become ready..."
              sleep $WAIT_TIME
              
              # Check readiness and add to updated pods list
              WAVE_READY_COUNT=0
              for deployment in "${WAVE_UPDATED_PODS[@]}"; do
                echo "Checking readiness of $deployment..."
                if kubectl rollout status deployment/$deployment -n $NAMESPACE --timeout=180s; then
                  echo "  $deployment is ready"
                  UPDATED_PODS+=("$deployment")
                  TOTAL_UPDATED=$((TOTAL_UPDATED + 1))
                  WAVE_READY_COUNT=$((WAVE_READY_COUNT + 1))
                else
                  echo "  Warning: $deployment rollout may still be in progress"
                  # Still add to updated list, but with warning
                  UPDATED_PODS+=("$deployment")
                  TOTAL_UPDATED=$((TOTAL_UPDATED + 1))
                fi
              done
              
              echo "Wave $WAVE completed: $WAVE_READY_COUNT/${#WAVE_UPDATED_PODS[@]} pods ready"
              echo "Buffer rotation: ${#UPDATED_PODS[@]} pods now serve as buffer for remaining ${#REMAINING_PODS[@]} pods"
            fi
            
            WAVE=$((WAVE + 1))
            
            # Safety check to prevent infinite loops
            if [ "$WAVE" -gt 20 ]; then
              echo "Maximum wave limit reached. Stopping for safety."
              break
            fi
          done

          echo
          echo "Beta Buffer Rotation Deployment Completed!"
          echo "Final Summary:"
          echo "  - Total pods available: $TOTAL_AVAILABLE"
          echo "  - Successfully updated: $TOTAL_UPDATED"
          echo "  - Update percentage: $(( TOTAL_UPDATED * 100 / TOTAL_AVAILABLE ))%"
          echo "  - Total waves: $((WAVE - 1))"
          echo "  - Buffer rotation: Complete"
          
          if [ "$TOTAL_UPDATED" -eq "$TOTAL_AVAILABLE" ]; then
            echo "SUCCESS: ALL BETA PODS UPDATED IN SINGLE DEPLOYMENT WITH BUFFER ROTATION!"
          else
            echo "Status: $TOTAL_UPDATED/$TOTAL_AVAILABLE pods updated"
            if [ ${#REMAINING_PODS[@]} -gt 0 ]; then
              echo "Remaining ${#REMAINING_PODS[@]} pods: ${REMAINING_PODS[*]}"
            fi
          fi

          # Final verification
          echo
          echo "Final Verification:"
          kubectl get pods -l service=$SERVICE -n $NAMESPACE -o custom-columns="NAME:.metadata.name,IMAGE:.spec.containers[0].image,STATUS:.status.phase"
          
          echo
          echo "Image summary:"
          kubectl get pods -l service=$SERVICE -n $NAMESPACE -o jsonpath='{.items[*].spec.containers[0].image}' | tr ' ' '\n' | cut -d: -f2 | sort | uniq -c

  notify:
    name: Slack Notification
    runs-on: ubuntu-latest
    needs: deploy
    if: always()
    steps:
      - name: Slack Deployment Succeeded
        if: ${{ needs.deploy.result == 'success' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ *Code Generation deployment complete* for branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\"}" \
            ${{ secrets.SLACK_HOOK_BETA_DEPLOY }}

      - name: Slack Deployment Failed
        if: ${{ needs.deploy.result == 'failure' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 *Code Generation Deployment failed* for branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\nView logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}" \
            ${{ secrets.SLACK_HOOK_BETA_DEPLOY }}
