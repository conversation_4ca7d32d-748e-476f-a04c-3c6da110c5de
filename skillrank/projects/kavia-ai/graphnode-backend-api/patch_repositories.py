#!/usr/bin/env python3
"""
Py2Neo script to update organization name from 'Workflow-Manager-admin' to 'kavia-common'
specifically for the 'developrdk7542' tenant database
"""

import json
import logging
from py2neo import Graph
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TenantOrganizationUpdater:
    def __init__(self, uri: str, username: str, password: str, tenant_id: str = "developrdk7542"):
        """
        Initialize the updater for a specific tenant
        
        Args:
            uri: Neo4j connection URI
            username: Neo4j username
            password: Neo4j password 
            tenant_id: Tenant ID to target (default: developrdk7542)
        """
        self.uri = uri
        self.username = username
        self.password = password
        self.tenant_id = tenant_id
        self.old_org = "Workflow-Manager-admin"
        self.new_org = "kavia-common"
        
        # Create database name like the NodeDB class does
        self.database = tenant_id.lower().replace('-', '').replace('_', '')
        
        # Connect to the specific tenant database
        self.graph = Graph(uri, auth=(username, password), name=self.database)
        
        logger.info(f"Connected to tenant database: {self.database}")

    def test_connection(self) -> bool:
        """Test the database connection"""
        try:
            result = self.graph.run("RETURN 1 as test").data()
            logger.info(f"✅ Connection successful to database: {self.database}")
            return True
        except Exception as e:
            logger.error(f"❌ Connection failed: {str(e)}")
            return False

    def find_projects_with_old_organization(self, dry_run: bool = True) -> List[Dict[str, Any]]:
        """
        Find all project nodes that contain repositories with the old organization name
        
        Args:
            dry_run: If True, only shows what would be updated
            
        Returns:
            List of project nodes that need updating
        """
        query = """
        MATCH (p:Project)
        WHERE p.repositories IS NOT NULL 
          AND p.repositories CONTAINS $old_org
        RETURN ID(p) as project_id, p.repositories as repositories, p.Title as title
        """
        
        try:
            result = self.graph.run(query, old_org=self.old_org).data()
            
            projects_to_update = []
            for record in result:
                try:
                    project_id = record['project_id']
                    title = record.get('title', 'Unknown')
                    repositories_json = record['repositories']
                    
                    # Parse the repositories JSON
                    repositories = json.loads(repositories_json)
                    
                    # Check which containers need updating
                    containers_to_update = []
                    for container_id, repo_info in repositories.items():
                        if isinstance(repo_info, dict) and repo_info.get('organization') == self.old_org:
                            containers_to_update.append({
                                'container_id': container_id,
                                'repository_name': repo_info.get('repositoryName', 'Unknown'),
                                'current_org': repo_info.get('organization'),
                                'clone_url_http': repo_info.get('cloneUrlHttp', ''),
                                'clone_url_ssh': repo_info.get('cloneUrlSsh', '')
                            })
                    
                    if containers_to_update:
                        projects_to_update.append({
                            'project_id': project_id,
                            'title': title,
                            'containers': containers_to_update,
                            'original_repositories': repositories
                        })
                        
                        logger.info(f"{'[DRY RUN] ' if dry_run else ''}Found project: {title} (ID: {project_id})")
                        for container in containers_to_update:
                            logger.info(f"  -> Container {container['container_id']}: {container['repository_name']}")
                
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse repositories JSON for project {project_id}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"Error processing project {project_id}: {e}")
                    continue
            
            logger.info(f"{'[DRY RUN] ' if dry_run else ''}Found {len(projects_to_update)} projects to update")
            return projects_to_update
            
        except Exception as e:
            logger.error(f"Error finding projects: {str(e)}")
            raise

    def update_project_repositories(self, project_id: int, repositories: Dict[str, Any]) -> bool:
        """
        Update a single project's repositories JSON
        
        Args:
            project_id: The project node ID
            repositories: Updated repositories dictionary
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Update each container's repository info
            updated_repositories = repositories.copy()
            update_count = 0
            
            for container_id, repo_info in repositories.items():
                if isinstance(repo_info, dict) and repo_info.get('organization') == self.old_org:
                    # Update organization
                    updated_repositories[container_id]['organization'] = self.new_org
                    
                    # Update URLs if they contain the old org name
                    if 'cloneUrlHttp' in repo_info and self.old_org in repo_info['cloneUrlHttp']:
                        updated_repositories[container_id]['cloneUrlHttp'] = repo_info['cloneUrlHttp'].replace(
                            self.old_org, self.new_org
                        )
                    
                    if 'cloneUrlSsh' in repo_info and self.old_org in repo_info['cloneUrlSsh']:
                        updated_repositories[container_id]['cloneUrlSsh'] = repo_info['cloneUrlSsh'].replace(
                            self.old_org, self.new_org
                        )
                    
                    update_count += 1
                    logger.info(f"  -> Updated container {container_id}")
            
            if update_count > 0:
                # Update the project node with new repositories JSON
                update_query = """
                MATCH (p:Project) 
                WHERE ID(p) = $project_id
                SET p.repositories = $repositories
                RETURN ID(p) as updated_id
                """
                
                result = self.graph.run(
                    update_query, 
                    project_id=project_id, 
                    repositories=json.dumps(updated_repositories)
                ).data()
                
                if result:
                    logger.info(f"✅ Updated project {project_id} successfully")
                    return True
                else:
                    logger.error(f"❌ Failed to update project {project_id}")
                    return False
            else:
                logger.warning(f"No containers needed updating for project {project_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error updating project {project_id}: {str(e)}")
            return False

    def update_all_projects(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        Update organization name in all project repositories for the tenant
        
        Args:
            dry_run: If True, only shows what would be updated
            
        Returns:
            Summary of updates
        """
        summary = {
            "tenant_id": self.tenant_id,
            "database": self.database,
            "dry_run": dry_run,
            "old_organization": self.old_org,
            "new_organization": self.new_org,
            "projects_found": 0,
            "projects_updated": 0,
            "containers_updated": 0,
            "errors": []
        }
        
        logger.info(f"{'=' * 60}")
        logger.info(f"{'[DRY RUN] ' if dry_run else ''}Starting organization update for tenant: {self.tenant_id}")
        logger.info(f"Database: {self.database}")
        logger.info(f"Old organization: {self.old_org}")
        logger.info(f"New organization: {self.new_org}")
        logger.info(f"{'=' * 60}")
        
        try:
            # Find all projects that need updating
            projects_to_update = self.find_projects_with_old_organization(dry_run)
            summary["projects_found"] = len(projects_to_update)
            
            if not projects_to_update:
                logger.info("✅ No projects found that need updating")
                return summary
            
            # Update each project
            for project_data in projects_to_update:
                project_id = project_data['project_id']
                title = project_data['title']
                containers = project_data['containers']
                original_repositories = project_data['original_repositories']
                
                logger.info(f"\n📊 Processing project: {title} (ID: {project_id})")
                
                if dry_run:
                    logger.info(f"[DRY RUN] Would update {len(containers)} containers")
                    for container in containers:
                        logger.info(f"  -> Container {container['container_id']}: {container['repository_name']}")
                        logger.info(f"     {container['current_org']} → {self.new_org}")
                    summary["projects_updated"] += 1
                    summary["containers_updated"] += len(containers)
                else:
                    # Perform actual update
                    if self.update_project_repositories(project_id, original_repositories):
                        summary["projects_updated"] += 1
                        summary["containers_updated"] += len(containers)
                    else:
                        summary["errors"].append(f"Failed to update project {project_id} ({title})")
            
            # Print final summary
            logger.info(f"\n{'=' * 60}")
            logger.info(f"{'[DRY RUN] ' if dry_run else ''}Update Summary for {self.tenant_id}:")
            logger.info(f"Database: {self.database}")
            logger.info(f"Projects found: {summary['projects_found']}")
            logger.info(f"Projects {'would be ' if dry_run else ''}updated: {summary['projects_updated']}")
            logger.info(f"Containers {'would be ' if dry_run else ''}updated: {summary['containers_updated']}")
            if summary["errors"]:
                logger.warning(f"Errors: {len(summary['errors'])}")
                for error in summary["errors"]:
                    logger.error(f"  - {error}")
            logger.info(f"{'=' * 60}")
            
        except Exception as e:
            error_msg = f"Fatal error during update: {str(e)}"
            logger.error(error_msg)
            summary["errors"].append(error_msg)
        
        return summary

    def verify_updates(self) -> Dict[str, Any]:
        """
        Verify that the updates were successful by checking for the new organization name
        """
        logger.info("🔍 Verifying updates...")
        
        # Check for new organization
        new_org_query = """
        MATCH (p:Project)
        WHERE p.repositories IS NOT NULL 
          AND p.repositories CONTAINS $new_org
        RETURN ID(p) as project_id, p.Title as title, p.repositories as repositories
        """
        
        # Check for old organization (should be none)
        old_org_query = """
        MATCH (p:Project)
        WHERE p.repositories IS NOT NULL 
          AND p.repositories CONTAINS $old_org
        RETURN ID(p) as project_id, p.Title as title
        """
        
        try:
            new_org_results = self.graph.run(new_org_query, new_org=self.new_org).data()
            old_org_results = self.graph.run(old_org_query, old_org=self.old_org).data()
            
            verification = {
                "tenant_id": self.tenant_id,
                "database": self.database,
                "projects_with_new_org": len(new_org_results),
                "projects_with_old_org": len(old_org_results),
                "verification_passed": len(old_org_results) == 0,
                "new_org_projects": [],
                "remaining_old_org_projects": []
            }
            
            # Process new org results
            for record in new_org_results:
                try:
                    repositories = json.loads(record['repositories'])
                    containers_with_new_org = []
                    
                    for container_id, repo_info in repositories.items():
                        if isinstance(repo_info, dict) and repo_info.get('organization') == self.new_org:
                            containers_with_new_org.append({
                                'container_id': container_id,
                                'repository_name': repo_info.get('repositoryName', 'Unknown')
                            })
                    
                    verification["new_org_projects"].append({
                        'project_id': record['project_id'],
                        'title': record['title'],
                        'containers': containers_with_new_org
                    })
                except:
                    continue
            
            # Process remaining old org results (if any)
            for record in old_org_results:
                verification["remaining_old_org_projects"].append({
                    'project_id': record['project_id'],
                    'title': record['title']
                })
            
            # Print verification results
            if verification["verification_passed"]:
                logger.info(f"✅ Verification PASSED")
                logger.info(f"  - {verification['projects_with_new_org']} projects now use '{self.new_org}'")
                logger.info(f"  - 0 projects still use '{self.old_org}'")
            else:
                logger.warning(f"⚠️ Verification FAILED")
                logger.warning(f"  - {verification['projects_with_old_org']} projects still use '{self.old_org}'")
                for project in verification["remaining_old_org_projects"]:
                    logger.warning(f"    -> {project['title']} (ID: {project['project_id']})")
            
            return verification
            
        except Exception as e:
            logger.error(f"Error during verification: {str(e)}")
            return {"error": str(e)}

    def close(self):
        """Close any connections if needed"""
        logger.info(f"Finished working with database: {self.database}")

def main():
    """Main execution function"""
    # Configuration - UPDATE THESE VALUES
    NEO4J_URI = "neo4j://localhost:7688"  # Update with your Neo4j URI
    NEO4J_USERNAME = "neo4j"              # Update with your username  
    NEO4J_PASSWORD = ""      # Update with your password
    TENANT_ID = "preprodt0006"          # Target tenant
    
    # Initialize updater
    updater = TenantOrganizationUpdater(NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD, TENANT_ID)
    
    try:
        print(f"🚀 Py2Neo Tenant Organization Updater")
        print(f"Target Tenant: {TENANT_ID}")
        print("=" * 50)
        
        # Test connection
        if not updater.test_connection():
            print("❌ Cannot connect to database. Please check your configuration.")
            return
        
        # First run in dry-run mode
        print("\n🔍 Running in DRY RUN mode first...")
        dry_run_results = updater.update_all_projects(dry_run=True)
        
        if dry_run_results["projects_found"] == 0:
            print("✅ No projects need updating - all repositories already use the new organization name")
            return
        
        # Ask for confirmation
        response = input(f"\n❓ Proceed with actual update for {dry_run_results['projects_found']} projects? (y/N): ").strip().lower()
        
        if response == 'y':
            print("\n🔄 Running actual update...")
            actual_results = updater.update_all_projects(dry_run=False)
            
            if actual_results["errors"]:
                print(f"\n⚠️ Some errors occurred:")
                for error in actual_results["errors"]:
                    print(f"   - {error}")
            else:
                print(f"\n✅ Update completed successfully!")
                
                # Verify the updates
                print("\n🔍 Verifying updates...")
                verification = updater.verify_updates()
                
                if verification.get("verification_passed"):
                    print("✅ All updates verified successfully!")
                else:
                    print("⚠️ Some issues found during verification. Check the logs above.")
        else:
            print("\n❌ Update cancelled by user")
    
    except KeyboardInterrupt:
        print("\n❌ Update interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"\n💥 Error: {e}")
    finally:
        updater.close()

if __name__ == "__main__":
    main()