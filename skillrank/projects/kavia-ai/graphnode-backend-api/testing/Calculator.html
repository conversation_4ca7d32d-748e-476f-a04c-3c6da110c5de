<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML/CSS Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }
        
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f5f5;
        }
        
        .calculator {
            width: 300px;
            background-color: #22252d;
            border-radius: 20px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }
        
        .display {
            padding: 20px;
            color: white;
            text-align: right;
            height: 100px;
            display: flex;
            align-items: flex-end;
            justify-content: flex-end;
            font-size: 2.5rem;
            font-weight: 300;
            background-color: #22252d;
            position: relative;
            overflow: hidden;
        }
        
        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 10px;
            padding: 20px;
            background-color: #292d36;
            border-radius: 20px 20px 0 0;
            margin-top: -20px;
        }
        
        button {
            border: none;
            background-color: #292d36;
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            border-radius: 10px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #333842;
        }
        
        .operator {
            color: #ff9500;
        }
        
        .clear, .sign, .percent {
            color: #26f3d0;
        }
        
        .equals {
            background-color: #ff9500;
            color: white;
        }
        
        .equals:hover {
            background-color: #ffaa33;
        }
        
        .zero {
            grid-column: span 2;
        }
    </style>
</head>
<body>
    <div class="calculator">
        <div class="display" id="display">0</div>
        <div class="buttons">
            <button class="clear" onclick="clearAll()">AC</button>
            <button class="sign" onclick="toggleSign()">+/-</button>
            <button class="percent" onclick="percentage()">%</button>
            <button class="operator" onclick="setOperation('/')">÷</button>
            
            <button onclick="appendNumber(7)">7</button>
            <button onclick="appendNumber(8)">8</button>
            <button onclick="appendNumber(9)">9</button>
            <button class="operator" onclick="setOperation('*')">×</button>
            
            <button onclick="appendNumber(4)">4</button>
            <button onclick="appendNumber(5)">5</button>
            <button onclick="appendNumber(6)">6</button>
            <button class="operator" onclick="setOperation('-')">-</button>
            
            <button onclick="appendNumber(1)">1</button>
            <button onclick="appendNumber(2)">2</button>
            <button onclick="appendNumber(3)">3</button>
            <button class="operator" onclick="setOperation('+')">+</button>
            
            <button class="zero" onclick="appendNumber(0)">0</button>
            <button onclick="appendDecimal()">.</button>
            <button class="equals" onclick="calculate()">=</button>
        </div>
    </div>

    <script>
        let displayValue = '0';
        let firstOperand = null;
        let waitingForSecondOperand = false;
        let operator = null;
        
        const display = document.getElementById('display');
        
        function updateDisplay() {
            // Limit the display to fit the screen
            if (displayValue.length > 10) {
                // If the number is too large, use exponential notation
                const num = parseFloat(displayValue);
                displayValue = num.toExponential(6);
            }
            display.textContent = displayValue;
        }
        
        function appendNumber(number) {
            if (waitingForSecondOperand) {
                displayValue = String(number);
                waitingForSecondOperand = false;
            } else {
                // Replace 0 with the new number, otherwise append
                displayValue = displayValue === '0' ? String(number) : displayValue + number;
            }
            updateDisplay();
        }
        
        function appendDecimal() {
            if (waitingForSecondOperand) {
                displayValue = '0.';
                waitingForSecondOperand = false;
            } else if (displayValue.indexOf('.') === -1) {
                displayValue += '.';
            }
            updateDisplay();
        }
        
        function clearAll() {
            displayValue = '0';
            firstOperand = null;
            waitingForSecondOperand = false;
            operator = null;
            updateDisplay();
        }
        
        function toggleSign() {
            displayValue = String(-parseFloat(displayValue));
            updateDisplay();
        }
        
        function percentage() {
            displayValue = String(parseFloat(displayValue) / 100);
            updateDisplay();
        }
        
        function setOperation(nextOperator) {
            const inputValue = parseFloat(displayValue);
            
            // If there's a pending operation, calculate the result first
            if (operator && waitingForSecondOperand) {
                operator = nextOperator;
                return;
            }
            
            if (firstOperand === null) {
                firstOperand = inputValue;
            } else if (operator) {
                const result = performCalculation();
                displayValue = String(result);
                firstOperand = result;
            }
            
            waitingForSecondOperand = true;
            operator = nextOperator;
            updateDisplay();
        }
        
        function performCalculation() {
            const secondOperand = parseFloat(displayValue);
            let result;
            
            switch (operator) {
                case '+':
                    result = firstOperand + secondOperand;
                    break;
                case '-':
                    result = firstOperand - secondOperand;
                    break;
                case '*':
                    result = firstOperand * secondOperand;
                    break;
                case '/':
                    if (secondOperand === 0) {
                        alert('Cannot divide by zero');
                        clearAll();
                        return 0;
                    }
                    result = firstOperand / secondOperand;
                    break;
                default:
                    return secondOperand;
            }
            
            // Format the result 
            if (Number.isInteger(result)) {
                return result;
            } else {
                return parseFloat(result.toFixed(8));
            }
        }
        
        function calculate() {
            if (!operator) return;
            
            const result = performCalculation();
            displayValue = String(result);
            
            // Reset for new calculation
            firstOperand = result;
            waitingForSecondOperand = false;
            operator = null;
            
            updateDisplay();
        }
        
        // Initialize display
        updateDisplay();
    </script>
</body>
</html>