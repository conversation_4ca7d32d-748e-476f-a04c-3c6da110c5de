#!/bin/bash

# Optimized Kubernetes Resource Cleanup Script
# Efficiently handles codegen deployments with proper nginx cleanup

set -euo pipefail

# Configuration
readonly NAMESPACE="duploservices-k-dev01"
readonly SERVICE_LABEL="service=codegen"
readonly NGINX_POD_LABEL="app=nginx"
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly TEMP_DIR=$(mktemp -d)
readonly LOG_FILE="${TEMP_DIR}/cleanup.log"

# Cleanup on exit
trap 'rm -rf "$TEMP_DIR"' EXIT

# Logging functions
log_info() {
    echo "[INFO] $*" | tee -a "$LOG_FILE"
}

log_warn() {
    echo "[WARN] $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[ERROR] $*" >&2 | tee -a "$LOG_FILE"
}

# Create nginx cleanup script
create_nginx_cleanup_script() {
    cat > "${TEMP_DIR}/nginx_cleanup.sh" << 'EOF'
#!/bin/bash
set -e

NGINX_CONF_DIR="/etc/nginx/conf.d"
DRY_RUN=false

if [[ "${1:-}" == "--dry-run" ]]; then
    DRY_RUN=true
    echo "Running in dry-run mode"
fi

echo "=== Nginx Configuration Cleanup Tool ==="
echo "Started at $(date)"

find_and_remove_problematic_files() {
    local temp_file=$(mktemp)
    echo "Testing Nginx config to identify problematic files..."
    
    nginx -s reload 2> "$temp_file" || true
    
    if [[ -s "$temp_file" ]]; then
        echo "Current Nginx errors:"
        cat "$temp_file"
        
        local files=($(grep -o '/etc/nginx/conf.d/[^ :]*' "$temp_file" | sort -u))
        
        if [[ ${#files[@]} -gt 0 ]]; then
            echo "Found ${#files[@]} problematic file(s):"
            for file in "${files[@]}"; do
                echo "  - $file"
                
                if [[ "$DRY_RUN" == "false" ]]; then
                    if [[ -f "$file" ]]; then
                        echo "Removing file: $file"
                        rm "$file"
                    fi
                else
                    echo "[DRY RUN] Would remove: $file"
                fi
            done
            rm "$temp_file"
            return 0
        fi
    fi
    
    rm "$temp_file"
    return 1
}

MAX_ATTEMPTS=50
attempt=1

while [[ $attempt -le $MAX_ATTEMPTS ]]; do
    echo "--- Cleanup attempt $attempt of $MAX_ATTEMPTS ---"
    
    if find_and_remove_problematic_files; then
        if [[ "$DRY_RUN" == "false" ]]; then
            echo "Reloading Nginx after removing problematic files..."
            nginx -s reload 2>/dev/null || true
        fi
    else
        echo "No more problematic files found."
        break
    fi
    
    if nginx -t &>/dev/null; then
        echo "SUCCESS: Nginx configuration is now valid!"
        break
    fi
    
    attempt=$((attempt + 1))
done

if [[ $attempt -gt $MAX_ATTEMPTS ]]; then
    echo "WARNING: Reached maximum attempts. Some issues may remain."
    exit 1
fi

echo "=== Cleanup completed ==="
echo "Completed at $(date)"

if [[ "$DRY_RUN" == "false" ]]; then
    echo "Final Nginx reload..."
    nginx -s reload
    echo "Nginx configuration is now valid."
fi

exit 0
EOF
    chmod +x "${TEMP_DIR}/nginx_cleanup.sh"
}

# Enhanced nginx cleanup function
cleanup_nginx_config() {
    local deployment_name="$1"
    local project_id
    project_id=$(echo "$deployment_name" | cut -d"-" -f2)
    
    log_info "Starting comprehensive nginx cleanup for deployment: $deployment_name"
    
    # Get nginx pod with better error handling
    local nginx_pods
    nginx_pods=$(kubectl get pods -n "$NAMESPACE" -l "$NGINX_POD_LABEL" -o name 2>/dev/null | head -1 | cut -d'/' -f2)
    
    if [[ -z "$nginx_pods" ]]; then
        log_warn "No nginx pod found with label: $NGINX_POD_LABEL"
        return 1
    fi
    
    local nginx_pod="$nginx_pods"
    log_info "Using nginx pod: $nginx_pod"
    
    # Verify pod is ready
    if ! kubectl get pod "$nginx_pod" -n "$NAMESPACE" -o jsonpath='{.status.phase}' | grep -q "Running"; then
        log_warn "Nginx pod is not in Running state"
        return 1
    fi
    
    # Create cleanup script if it doesn't exist
    [[ ! -f "${TEMP_DIR}/nginx_cleanup.sh" ]] && create_nginx_cleanup_script
    
    # Copy cleanup script to nginx pod
    log_info "Copying nginx cleanup script to pod..."
    if ! kubectl cp "${TEMP_DIR}/nginx_cleanup.sh" "$NAMESPACE/$nginx_pod:/tmp/nginx_cleanup.sh"; then
        log_error "Failed to copy cleanup script to nginx pod"
        return 1
    fi
    
    # Make script executable and run it
    log_info "Executing nginx cleanup script inside pod..."
    if kubectl exec -n "$NAMESPACE" "$nginx_pod" -- chmod +x /tmp/nginx_cleanup.sh && \
       kubectl exec -n "$NAMESPACE" "$nginx_pod" -- /tmp/nginx_cleanup.sh; then
        log_info "Nginx cleanup completed successfully"
    else
        log_warn "Nginx cleanup script execution failed, trying fallback method..."
        # Fallback: direct file removal
        kubectl exec -n "$NAMESPACE" "$nginx_pod" -- rm -f "/etc/nginx/conf.d/custom_${project_id}.conf" 2>/dev/null || true
        kubectl exec -n "$NAMESPACE" "$nginx_pod" -- nginx -s reload 2>/dev/null || true
    fi
    
    # Cleanup temporary script from pod
    kubectl exec -n "$NAMESPACE" "$nginx_pod" -- rm -f /tmp/nginx_cleanup.sh 2>/dev/null || true
    
    log_info "Nginx configuration cleanup completed for project: $project_id"
}

# Optimized resource deletion function
delete_deployment_resources() {
    local deployment_name="$1"
    local project_id
    project_id=$(echo "$deployment_name" | cut -d"-" -f2)
    
    log_info "Processing deletion for deployment: $deployment_name (project: $project_id)"
    
    # Define resource names
    local -A resources=(
        ["deployment"]="$deployment_name"
        ["service-internal"]="internal-$deployment_name"
        ["service-clusterip"]="internal-clusterip-$deployment_name"
        ["pvc"]="pvc-$deployment_name"
        ["configmap"]="pod-status-$deployment_name"
    )
    
    # Nginx cleanup first (non-blocking)
    cleanup_nginx_config "$deployment_name" &
    local nginx_cleanup_pid=$!
    
    # Parallel resource deletion with better error handling
    local pids=()
    
    for resource_type in "${!resources[@]}"; do
        {
            local resource_name="${resources[$resource_type]}"
            case "$resource_type" in
                "deployment")
                    kubectl delete deployment "$resource_name" -n "$NAMESPACE" --ignore-not-found --timeout=30s
                    ;;
                "service-internal"|"service-clusterip")
                    kubectl delete service "$resource_name" -n "$NAMESPACE" --ignore-not-found --timeout=10s
                    ;;
                "pvc")
                    kubectl delete pvc "$resource_name" -n "$NAMESPACE" --ignore-not-found --timeout=20s
                    ;;
                "configmap")
                    kubectl delete configmap "$resource_name" -n "$NAMESPACE" --ignore-not-found --timeout=10s
                    ;;
            esac
            log_info "✓ Deleted $resource_type: $resource_name"
        } &
        pids+=($!)
    done
    
    # Wait for all resource deletions
    for pid in "${pids[@]}"; do
        if wait "$pid"; then
            continue
        else
            log_warn "One resource deletion process failed (PID: $pid)"
        fi
    done
    
    # Wait for nginx cleanup
    if wait "$nginx_cleanup_pid"; then
        log_info "✓ Nginx cleanup completed"
    else
        log_warn "Nginx cleanup failed or timed out"
    fi
    
    log_info "✅ Completed deletion of resources for deployment: $deployment_name"
}

# Utility function to extract deployment name from pod name
get_deployment_from_pod() {
    local pod_name="$1"
    echo "$pod_name" | cut -d "-" -f1,2
}

# Function to validate pod and delete its resources
delete_specific_pod() {
    local target_pod="$1"
    
    log_info "Processing deletion request for pod: $target_pod"
    
    # Validate pod existence
    if ! kubectl get pod "$target_pod" -n "$NAMESPACE" &>/dev/null; then
        log_error "Pod '$target_pod' not found in namespace '$NAMESPACE'"
        return 1
    fi
    
    # Verify pod labels (non-blocking warning)
    local pod_labels
    pod_labels=$(kubectl get pod "$target_pod" -n "$NAMESPACE" -o jsonpath='{.metadata.labels}' 2>/dev/null || true)
    if [[ "$pod_labels" != *"service=codegen"* ]]; then
        log_warn "Pod '$target_pod' does not have expected label '$SERVICE_LABEL' - proceeding anyway"
    fi
    
    # Extract deployment name and process deletion
    local deployment_name
    deployment_name=$(get_deployment_from_pod "$target_pod")
    
    log_info "Identified deployment: $deployment_name"
    delete_deployment_resources "$deployment_name"
    
    log_info "✅ Successfully processed deletion for pod: $target_pod"
}

# Function to delete all matching pods
delete_all_pods() {
    log_info "Initiating bulk deletion for all pods with label: $SERVICE_LABEL"
    
    # Get all matching pods efficiently
    local all_pods
    all_pods=$(kubectl get pods -l "$SERVICE_LABEL" -n "$NAMESPACE" --no-headers -o custom-columns=NAME:.metadata.name 2>/dev/null || true)
    
    if [[ -z "$all_pods" ]]; then
        log_info "No pods found with label: $SERVICE_LABEL"
        return 0
    fi
    
    # Extract unique deployment names
    local -A unique_deployments
    while IFS= read -r pod_name; do
        [[ -z "$pod_name" ]] && continue
        local deployment_name
        deployment_name=$(get_deployment_from_pod "$pod_name")
        unique_deployments["$deployment_name"]=1
    done <<< "$all_pods"
    
    local deployment_list=($(printf '%s\n' "${!unique_deployments[@]}" | sort))
    
    log_info "Found ${#deployment_list[@]} unique deployments to process:"
    printf '%s\n' "${deployment_list[@]}" | sed 's/^/  - /' | tee -a "$LOG_FILE"
    
    # Process deletions with controlled parallelism
    local max_parallel=5
    local count=0
    
    for deployment in "${deployment_list[@]}"; do
        delete_deployment_resources "$deployment" &
        
        ((count++))
        if (( count % max_parallel == 0 )); then
            wait  # Wait for current batch to complete
        fi
    done
    
    wait  # Wait for remaining processes
    
    log_info "✅ Successfully processed deletion for all ${#deployment_list[@]} deployments"
}

# Usage information
show_usage() {
    cat << EOF
Kubernetes Codegen Resource Cleanup Tool

USAGE:
    $0 [OPTIONS]

OPTIONS:
    -p, --pod POD_NAME     Delete resources for a specific pod
    -a, --all             Delete resources for all pods with label '$SERVICE_LABEL'
    -h, --help            Show this help message
    -v, --verbose         Enable verbose logging

EXAMPLES:
    $0 --all                           # Delete all codegen deployments
    $0 --pod codegen-abc123-xyz789     # Delete specific pod and its resources
    $0 -p my-pod-name -v               # Delete specific pod with verbose output

FEATURES:
    ✓ Comprehensive nginx configuration cleanup
    ✓ Parallel resource deletion for improved performance
    ✓ Robust error handling and validation
    ✓ Detailed logging and progress tracking
    ✓ Automatic cleanup of temporary files

ENVIRONMENT:
    Namespace: $NAMESPACE
    Service Label: $SERVICE_LABEL
    Nginx Label: $NGINX_POD_LABEL

EOF
}

# Main execution function
main() {
    local pod_name=""
    local delete_all=false
    local verbose=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--pod)
                [[ -z "${2:-}" ]] && { log_error "Pod name required for --pod option"; show_usage; exit 1; }
                pod_name="$2"
                shift 2
                ;;
            -a|--all)
                delete_all=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                set -x
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: '$1'"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Validate mutual exclusivity
    if [[ -n "$pod_name" && "$delete_all" == true ]]; then
        log_error "Cannot specify both --pod and --all options"
        show_usage
        exit 1
    fi
    
    # Require at least one action
    if [[ -z "$pod_name" && "$delete_all" == false ]]; then
        log_error "Must specify either --pod POD_NAME or --all"
        show_usage
        exit 1
    fi
    
    # Verify kubectl connectivity
    if ! kubectl cluster-info &>/dev/null; then
        log_error "Unable to connect to Kubernetes cluster"
        exit 1
    fi
    
    # Verify namespace exists
    if ! kubectl get namespace "$NAMESPACE" &>/dev/null; then
        log_error "Namespace '$NAMESPACE' does not exist"
        exit 1
    fi
    
    log_info "Starting cleanup operation..."
    log_info "Log file: $LOG_FILE"
    
    # Execute based on options
    local start_time=$(date +%s)
    
    if [[ -n "$pod_name" ]]; then
        delete_specific_pod "$pod_name"
    elif [[ "$delete_all" == true ]]; then
        delete_all_pods
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_info "🎉 Operation completed successfully in ${duration}s!"
    [[ "$verbose" == true ]] && log_info "Detailed logs available at: $LOG_FILE"
}

# Execute main function with all arguments
main "$@"