from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod

@dataclass
class Document:
    id: str
    url: str
    title: str
    content: str

@dataclass
class Chunk:
    id: str
    content: str
    document_id: str
    url: str
    title: str
    index: int
    embedding: Optional[List[float]] = None

@dataclass
class SearchResult:
    content: str
    url: str
    title: str
    score: float

class VectorDatabase(ABC):
    @abstractmethod
    async def initialize(self) -> None:
        pass
    
    @abstractmethod
    async def upsert_chunks(self, chunks: List[Chunk]) -> None:
        pass
    
    @abstractmethod
    async def search(self, vector: List[float], top_k: int) -> List[SearchResult]:
        pass

class ChunkingStrategy(ABC):
    @abstractmethod
    def chunk(self, content: str, chunk_size: int = 800) -> List[str]:
        pass