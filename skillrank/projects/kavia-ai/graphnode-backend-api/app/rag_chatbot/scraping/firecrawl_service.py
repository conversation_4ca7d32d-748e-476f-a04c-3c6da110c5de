import os
from typing import Dict, Any, List, Optional
from firecrawl import Async<PERSON><PERSON><PERSON>rawlApp, ScrapeOptions
from app.telemetry.logger_config import get_logger
from app.core.Settings import settings

logger = get_logger(__name__)


class FirecrawlService:
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or settings.FIRECRAWL_API_KEY
        if not self.api_key:
            raise ValueError("FIRECRAWL_API_KEY required")
        
        self.client = AsyncFirecrawlApp(api_key=self.api_key)
        self.max_content = 8000

    async def scrape_url(self, url: str, wait_for: int = 1000, timeout: int = 15000) -> Dict[str, Any]:
        try:
            logger.info(f"Scraping URL ===>: {url}")
            result = await self.client.scrape_url(
                url=url,
                only_main_content=True,
                 formats= [ 'markdown' ],
                parse_pdf= True,
                max_age= 14400000,
                wait_for=wait_for,
                timeout=timeout
            )
            
            return self._process_result(result, url)
                    
        except Exception as e:
            return self._error_response(url, str(e))

    async def crawl_website(self, url: str, max_pages: int = 5, timeout: int = 15000) -> Dict[str, Any]:
        try:
            scrape_options = ScrapeOptions(onlyMainContent=True, timeout=timeout)
            logger.info(f"Crawling URL ===>: {url}")
            result = await self.client.crawl_url(
                url=url,
                limit=min(max_pages, 10),
                scrape_options=scrape_options
            )
            
            if hasattr(result, 'success') and not result.success:
                return {
                    "success": False,
                    "base_url": url,
                    "pages": [],
                    "error": getattr(result, 'error', 'Crawl failed')
                }
            
            pages_data = getattr(result, 'data', []) or getattr(result, 'pages', [])
            pages = [self._extract_page_data(page) for page in pages_data]
            
            return {
                "success": True,
                "base_url": url,
                "pages": pages
            }
                    
        except Exception as e:
            logger.error(f"Crawl error for {url}: {str(e)}")
            return {
                "success": False,
                "base_url": url,
                "pages": [],
                "error": str(e)
            }

    def _process_result(self, result: Any, url: str) -> Dict[str, Any]:
        if hasattr(result, 'success') and not result.success:
            return self._error_response(url, getattr(result, 'error', 'Scraping failed'))
        
        content = self._extract_content(result)
        return {
            "success": True,
            "url": url,
            "content": content
        }

    def _extract_page_data(self, page: Any) -> Dict[str, str]:
        return {
            "url": getattr(page, 'sourceURL', '') or getattr(page, 'url', ''),
            "content": self._extract_content(page)
        }

    def _extract_content(self, item: Any) -> str:
        content = getattr(item, 'markdown', '') or getattr(item, 'content', '') or ""
        return self._truncate(content)

    def _truncate(self, content: str) -> str:
        if not content or len(content) <= self.max_content:
            return content
        
        truncated = content[:self.max_content]
        last_break = max(truncated.rfind('.'), truncated.rfind('\n'))
        
        if last_break > self.max_content * 0.8:
            truncated = truncated[:last_break + 1]
        
        return truncated + "\n[Truncated...]"

    def _error_response(self, url: str, error: str) -> Dict[str, Any]:
        logger.error(f"Scrape error for {url}: {error}")
        return {
            "success": False,
            "url": url,
            "content": "",
            "error": error
        }