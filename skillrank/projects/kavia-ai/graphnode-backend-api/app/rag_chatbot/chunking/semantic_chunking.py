import re
from typing import List
from app.rag_chatbot.types import ChunkingStrategy

class SemanticChunking(ChunkingStrategy):
    def __init__(self, overlap: int = 150):
        self.overlap = overlap
    
    def chunk(self, content: str, chunk_size: int = 800) -> List[str]:
        sections = self._split_by_headers(content)
        chunks = []
        
        for section in sections:
            section_chunks = self._split_text(section, chunk_size)
            chunks.extend(section_chunks)
        
        return [chunk for chunk in chunks if len(chunk.strip()) > 50]
    
    def _split_by_headers(self, content: str) -> List[str]:
        header_pattern = r'^(#{1,6})\s+(.+)$'
        sections = []
        current = ""
        
        for line in content.split('\n'):
            if re.match(header_pattern, line):
                if current.strip():
                    sections.append(current.strip())
                current = line + '\n'
            else:
                current += line + '\n'
        
        if current.strip():
            sections.append(current.strip())
        
        return sections if sections else [content]
    
    def _split_text(self, text: str, max_size: int) -> List[str]:
        if len(text) <= max_size:
            return [text]
        
        for sep in ['\n\n', '\n', '. ', ' ']:
            if sep in text:
                return self._merge_parts(text.split(sep), sep, max_size)
        
        return [text[i:i+max_size] for i in range(0, len(text), max_size)]
    
    def _merge_parts(self, parts: List[str], sep: str, max_size: int) -> List[str]:
        chunks = []
        current = ""
        
        for part in parts:
            proposed = current + (sep if current else "") + part
            
            if len(proposed) <= max_size:
                current = proposed
            else:
                if current:
                    chunks.append(current)
                    overlap_start = max(0, len(current) - self.overlap)
                    current = current[overlap_start:] + sep + part
                else:
                    current = part
        
        if current:
            chunks.append(current)
        
        return chunks