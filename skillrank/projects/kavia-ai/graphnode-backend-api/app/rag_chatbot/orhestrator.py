from typing import List, Dict, Any
from app.rag_chatbot.types import Chunk
from app.rag_chatbot.service import RAGServices

class IngestionOrchestrator:
    def __init__(self):
        self.services = RAGServices()
    
    async def process_urls(self, urls: List[str]) -> Dict[str, Any]:
        await self.services.vector_db.initialize()
        
        results = []
        total_chunks = 0
        
        for url in urls:
            try:
                # Scrape
                document = await self.services.scraping_service.scrape_url(url)
                
                # Chunk
                chunk_texts = self.services.chunking_strategy.chunk(document.content)
                
                # Create chunks with embeddings
                chunks = []
                for i, text in enumerate(chunk_texts):
                    chunks.append(Chunk(
                        id=f"{document.id}-{i}",
                        content=text,
                        document_id=document.id,
                        url=document.url,
                        title=document.title,
                        index=i
                    ))
                
                # Generate embeddings
                embeddings = await self.services.embedding_service.generate_embeddings(
                    [chunk.content for chunk in chunks]
                )
                
                # Attach embeddings
                for chunk, embedding in zip(chunks, embeddings):
                    chunk.embedding = embedding
                
                # Store
                await self.services.vector_db.upsert_chunks(chunks)
                
                results.append({
                    "url": url,
                    "success": True,
                    "chunks_created": len(chunks)
                })
                total_chunks += len(chunks)
                
            except Exception as e:
                results.append({
                    "url": url,
                    "success": False,
                    "error": str(e),
                    "chunks_created": 0
                })
        
        successful = sum(1 for r in results if r["success"])
        
        return {
            "processed": len(urls),
            "successful": successful,
            "failed": len(urls) - successful,
            "total_chunks": total_chunks,
            "results": results
        }

class RetrievalOrchestrator:
    def __init__(self):
        self.services = RAGServices()
    
    async def search(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        # Generate query embedding
        query_embeddings = await self.services.embedding_service.generate_embeddings([query])
                # Search vector DB
        search_results = await self.services.vector_db.search(query_embeddings[0], top_k)
        
        if not search_results:
            return {
                "answer": "No relevant information found in the knowledge base.",
                "sources": [],
                "total": 0
            }
        
        # Prepare sources for LLM
        sources = [
            {
                "content": r.content,
                "url": r.url,
                "title": r.title,
                "score": r.score
            }
            for r in search_results
        ]
        
        # Generate answer
        answer = await self.services.llm_service.generate_answer(query, sources)
        
        return {
            "answer": answer,
            "sources": sources,
            "total": len(sources)
        }