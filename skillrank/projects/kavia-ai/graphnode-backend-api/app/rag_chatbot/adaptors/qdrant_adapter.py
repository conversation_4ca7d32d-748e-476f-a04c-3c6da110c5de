import os
import uuid
from typing import List
from qdrant_client import As<PERSON><PERSON>drant<PERSON><PERSON>
from qdrant_client.models import Distance, VectorParams, PointStruct
from app.rag_chatbot.types import VectorDatabase, Chunk, SearchResult
from app.core.Settings import settings

class QdrantAdapter(VectorDatabase):
    def __init__(self):
        self.client = AsyncQdrantClient(
            url=settings.QDRANT_URL,
            api_key=settings.QDRANT_API_KEY,
        )
        self.collection_name = "help_documents"
    
    async def initialize(self) -> None:
        try:
            await self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=1536, distance=Distance.COSINE),
            )
            print(f"✅ Collection '{self.collection_name}' ready")
        except Exception as e:
            if "already exists" not in str(e).lower():
                print(f"❌ Collection creation failed: {e}")
                raise e
            print(f"✅ Collection '{self.collection_name}' already exists")
    
    async def upsert_chunks(self, chunks: List[Chunk]) -> None:
        points = []
        for chunk in chunks:
            # Use UUID for unique point IDs
            point_id = str(uuid.uuid4())
            points.append(PointStruct(
                id=point_id,
                vector=chunk.embedding,
                payload={
                    "document_id": chunk.document_id,
                    "url": chunk.url,
                    "title": chunk.title,
                    "content": chunk.content,
                    "chunk_index": chunk.index
                }
            ))
        
        await self.client.upsert(
            collection_name=self.collection_name,
            points=points
        )
        print(f"✅ Stored {len(points)} chunks in Qdrant")
    
    async def search(self, vector: List[float], top_k: int) -> List[SearchResult]:
        try:
            print(f"🔍 Searching with vector dim: {len(vector)}, top_k: {top_k}")
            
            results = await self.client.search(
                collection_name=self.collection_name,
                query_vector=vector,
                limit=top_k,
                score_threshold=0.1  # Lower threshold for debugging
            )
            
            print(f"📊 Qdrant returned {len(results)} results")
            
            search_results = []
            for r in results:
                print(f"  - Score: {r.score:.3f}, Title: {r.payload.get('title', 'N/A')}")
                search_results.append(SearchResult(
                    content=r.payload["content"],
                    url=r.payload["url"],
                    title=r.payload["title"],
                    score=r.score
                ))
            
            return search_results
            
        except Exception as e:
            print(f"❌ Qdrant search error: {e}")
            raise e