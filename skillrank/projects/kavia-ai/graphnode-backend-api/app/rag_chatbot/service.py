import os
import hashlib
from typing import List, Dict, Any
from openai import Async<PERSON>penA<PERSON>
from app.rag_chatbot.types import Document, VectorDatabase, ChunkingStrategy
from app.rag_chatbot.scraping.firecrawl_service import FirecrawlService
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.logs_utils import get_path
from app.core.Settings import settings

class RAGServices:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        self.embedding_service = EmbeddingService()
        self.scraping_service = ScrapingService()
        self.llm_service = LLMService()
        self.vector_db = self._create_vector_db()
        self.chunking_strategy = self._create_chunking_strategy()
        self._initialized = True
    
    def _create_vector_db(self) -> VectorDatabase:
        from app.rag_chatbot.adaptors.qdrant_adapter import QdrantAdapter
        return QdrantAdapter()
    
    def _create_chunking_strategy(self) -> ChunkingStrategy:
        from app.rag_chatbot.chunking.semantic_chunking import SemanticChunking
        return SemanticChunking()

class EmbeddingService:
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = "text-embedding-3-small"
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        response = await self.client.embeddings.create(
            input=texts,
            model=self.model
        )
        return [embedding.embedding for embedding in response.data]

class ScrapingService:
    def __init__(self):
        self.firecrawl = FirecrawlService()
    
    async def scrape_url(self, url: str) -> Document:
        result = await self.firecrawl.scrape_url(url)
        
        print("result ========>", result)
        
        if not result.get("success"):
            raise Exception(f"Scraping failed: {result.get('error')}")
        
        doc_id = hashlib.md5(url.encode()).hexdigest()
        
        return Document(
            id=doc_id,
            url=url,
            title=result.get("title", ""),
            content=result.get("content", "")
        )
 

class LLMService:
    def __init__(self):
        self.llm_interface = LLMInterface(
            str(get_path()), 
            'help_chat_rag', 
            'admin', 
            'help_chat_project', 
            'help_chat_agent'
        )
        self.system_prompt = """
You are the official Kavia AI Support Assistant, designed exclusively to help users with Kavia AI platform questions.

## CORE RESPONSIBILITIES:
- Answer questions about Kavia AI platform features, capabilities, integrations, and documentation
- Provide accurate, helpful guidance for Kavia AI users
- Always cite sources when providing information

## STRICT CONTENT BOUNDARIES:
You MUST ONLY answer questions directly related to Kavia AI platform, features, integrations, capabilities, documentation, and troubleshooting.

## RESPONSE PROTOCOLS:

### For NON-Kavia AI questions:
Respond EXACTLY with:
"I'm specifically designed to help with Kavia AI platform questions. For other topics, please visit our documentation at: https://www.kavia.ai/documentation/home"

### For Kavia AI questions WITH context provided:
1. Answer clearly using the provided context
2. ALWAYS cite sources: "According to [Source Title], [information]"
3. Include source URLs when available
# 4. End with: "For more details, see: [source URLs]"

### For Kavia AI questions WITHOUT sufficient context:
Respond with:
"I don't have specific information about that in my current knowledge base. For comprehensive details, please check our documentation at: https://www.kavia.ai/documentation/home

Is there anything else about Kavia AI I can help you with?"

## QUALITY GUARDRAILS:
- Never speculate or provide unsupported information
- Every factual claim must reference a source from provided context
- Maintain professional, concise responses
- Never provide generic programming advice - only Kavia AI specific guidance
- If context is unclear, ask for clarification about the specific Kavia AI feature
        """
    
    async def generate_answer(self, query: str, sources: List[Dict[str, Any]]) -> str:
        # Check if query is about Kavia AI
        kavia_keywords = ['kavia', 'platform', 'integration', 'api', 'dashboard', 'workflow']
        if not any(keyword in query.lower() for keyword in kavia_keywords) and not sources:
            return "I'm specifically designed to help with Kavia AI platform questions. For other topics, please visit our documentation at: https://www.kavia.ai/documentation/home"
        
        if not sources:
            return """I don't have specific information about that in my current knowledge base. For comprehensive details, please check our documentation at: https://www.kavia.ai/documentation/home

Is there anything else about Kavia AI I can help you with?"""
        
        # Format context
        context = "\n\n".join([
            f"Source: {s['title']} ({s.get('url', 'N/A')})\n{s['content']}"
            for s in sources
        ])
        
        user_prompt = f"Context:\n{context}\n\nQuestion: {query}"
        
        try:
            response = await self.llm_interface.llm_interaction_wrapper(
                messages=[],
                user_prompt=user_prompt,
                system_prompt=self.system_prompt,
                model="gpt-4.1-mini",
                response_format={"type": "text"},
                stream=False
            )
            
            return response
                
        except Exception as e:
            print(f"❌ LLM generation error: {e}")
            return "Sorry, I'm experiencing technical difficulties. Please visit our documentation at: https://www.kavia.ai/documentation/home or contact support."