from fastapi import APIRouter, HTTPException, Depends
from typing import Dict,Optional, Union, List
from app.models.scm import *
from app.core.Settings import settings
from app.utils.auth_utils import get_current_user, is_admin
from app.connection.establish_db_connection import get_mongo_db
from github import Github
import gitlab
import datetime
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from authlib.integrations.starlette_client import OAuth
from app.utils.hash import encrypt_string
import secrets
from fastapi import Request
from app.connection.tenant_middleware import get_tenant_id
from app.utils.hash import decrypt_string
import requests
from app.utils.datetime_utils import generate_timestamp, to_utc, from_isoformat
from app.utils.b2c_utils import get_collection_name

router = APIRouter(
    prefix="/scm",
    tags=["SCM Management"],
    responses={404: {"description": "Not found"}}
)


class SCMManager:
    def __init__(self):
        self.mongo_handler = None
        self.db_name = None
        self.collection_name = 'scm_configurations'

    def refresh_token(self, scm_id: str, mongo_db=None) -> SCMConfiguration:
        collection = self.collection_name
        if mongo_db:
            self.mongo_handler = mongo_db
            collection = self.collection_name
        else:
            self.mongo_handler = get_mongo_db().db
            self.db_name = self.mongo_handler.name
            collection = get_collection_name(self.db_name, self.collection_name)

        config = self.mongo_handler[collection].find_one({'scm_id': scm_id})
        if not config:
            raise HTTPException(status_code=404, detail="SCM configuration not found")

        config_obj = SCMConfiguration(**config)
        
        if config_obj.scm_type == SCMType.GITHUB:
            # GitHub uses OAuth refresh token
            headers = {"Accept": "application/json"}
            payload = {
                "client_id": settings.GITHUB_CLIENT_ID_CODEGEN,
                "client_secret": settings.GITHUB_CLIENT_SECRET_CODEGEN,
                "refresh_token": config_obj.credentials.refresh_token,
                "grant_type": "refresh_token"
            }
            response = requests.post(
                "https://github.com/login/oauth/access_token",
                headers=headers,
                data=payload
            )
            token_data = response.json()
            
            if 'error' in token_data:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to refresh token: {token_data.get('error_description', token_data['error'])}"
                )
            
            # Update configuration with new tokens
            config_obj.credentials.access_token = token_data['access_token']
            if 'refresh_token' in token_data:
                config_obj.credentials.refresh_token = token_data['refresh_token']
            config_obj.credentials.token_expires_at = from_isoformat(generate_timestamp()) + datetime.timedelta(hours=2)

        elif config_obj.scm_type == SCMType.GITLAB:
            # GitLab token refresh
            headers = {"Accept": "application/json"}
            payload = {
                "client_id": settings.GITLAB_CLIENT_ID_CODEGEN,
                "client_secret": settings.GITLAB_CLIENT_SECRET_CODEGEN,
                "refresh_token": config_obj.credentials.refresh_token,
                "grant_type": "refresh_token"
            }
            response = requests.post(
                "https://gitlab.com/oauth/token",
                headers=headers,
                data=payload
            )
            token_data = response.json()
            
            if 'error' in token_data:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to refresh token: {token_data.get('error_description', token_data['error'])}"
                )
            
            # Update configuration with new tokens
            config_obj.credentials.access_token = token_data['access_token']
            config_obj.credentials.refresh_token = token_data['refresh_token']
            config_obj.credentials.token_expires_at = from_isoformat(generate_timestamp()) + datetime.timedelta(seconds=token_data['expires_in'])

        # Save updated configuration
        self.save_configuration(config['tenant_id'], config_obj)
        return config_obj

    def get_valid_configuration(self, scm_id: str) -> SCMConfiguration:
        """Get configuration and refresh token if expired"""
        config = self.get_configuration(None, scm_id)
        if not config:
            raise HTTPException(status_code=404, detail="SCM configuration not found")

        if (config.credentials.token_expires_at and 
            config.credentials.token_expires_at <= from_isoformat(generate_timestamp()) and
            config.credentials.refresh_token):
            config = self.refresh_token(scm_id)
        
        return config

    def save_configuration(self, tenant_id: str, config: SCMConfiguration):
        self.mongo_handler = get_mongo_db().db
        self.db_name = self.mongo_handler.name
        collection = get_collection_name(self.db_name, self.collection_name)
        
        # Encrypt sensitive data before saving
        encrypted_config = self._encrypt_sensitive_data(config)
        
        # Add a unique identifier for each SCM configuration
        config_data = encrypted_config.dict()
        config_data['scm_id'] = f"{tenant_id}_{config.scm_type}_{config.credentials.organization}"
        
        self.mongo_handler[collection].update_one(
            filter={'scm_id': config_data['scm_id']},
            update={'$set': {
                **config_data,
                'tenant_id': tenant_id,
                'created_at': from_isoformat(generate_timestamp())
            }},
            upsert=True
        )
        return config_data

    def _encrypt_sensitive_data(self, config: SCMConfiguration) -> SCMConfiguration:
        # TODO: Implement encryption for sensitive fields
        return config

    def get_token_from_tenant_org(self, organization: str) -> Union[str, bool]:
        """
        Get access token from tenant_id and organization name
        
        Args:
            organization (str): Organization name to find the token for
            
        Returns:
            str: access_token if found
            bool: False if not found or error occurs
        """
        try:
            tenant_id = get_tenant_id()
            if not tenant_id:
                print("No tenant ID found")
                return False

            self.mongo_handler = get_mongo_db().db
            self.db_name = self.mongo_handler.name
            collection = get_collection_name(self.db_name, self.collection_name)
            
            # Find configuration for tenant and organization
            config = self.mongo_handler[collection].find_one({
                'tenant_id': tenant_id,
                'credentials.organization': organization
            })
            
            if not config:
                print(f"No configuration found for tenant {tenant_id} and organization {organization}")
                return False
                
            # Handle token expiration
            if config.get('credentials', {}).get('token_expires_at'):
                expires_at = config['credentials']['token_expires_at']
                
                # Convert to timezone aware if needed
                if not expires_at.tzinfo:
                    expires_at = from_isoformat(generate_timestamp())
                    config['credentials']['token_expires_at'] = expires_at
                
                # Refresh token if expired
                if expires_at <= from_isoformat(generate_timestamp()):
                    try:
                        refreshed_config = self.refresh_token(config['scm_id'])
                        config['credentials'] = refreshed_config.credentials.model_dump()
                    except Exception as e:
                        print(f"Error refreshing token: {e}")
                        return False

            # Return access token if exists
            access_token = config.get('credentials', {}).get('access_token')
            if access_token:
                return access_token
                
            return False

        except Exception as e:
            print(f"Error getting token: {e}")
            return False
    
    def get_configuration(self, tenant_id: str=get_tenant_id(), scm_id: str = None, mongo_db=None) -> Optional[Union[SCMConfiguration, List[SCMConfiguration]]]:
        if not tenant_id:
            tenant_id = get_tenant_id()
        print("SCM-id", scm_id)
        collection=self.collection_name
        if mongo_db:
            self.mongo_handler = mongo_db
            collection = self.collection_name
        else:
            self.mongo_handler = get_mongo_db().db
            self.db_name = self.mongo_handler.name
            collection = get_collection_name(self.db_name, self.collection_name)
        
        if scm_id:
            config = self.mongo_handler[collection].find_one({'scm_id': scm_id, 'tenant_id': tenant_id})
            if config:
                # if token_expires_at is not offset aware, convert it to offset aware
                if config.get('credentials').get('token_expires_at'):
                    if not config.get('credentials').get('token_expires_at').tzinfo:
                        config['credentials']['token_expires_at'] = from_isoformat(generate_timestamp())

                if config.get('credentials').get('token_expires_at') and config.get('credentials').get('token_expires_at') <= from_isoformat(generate_timestamp()):
                    try:
                        refreshed_config = self.refresh_token(scm_id)
                        config['credentials'] = refreshed_config.credentials.model_dump()
                    except Exception as e:
                        print(f"Error refreshing token: {e}")

                config['encrypted_scm_id'] = encrypt_string(config['scm_id'])
            return SCMConfiguration(**config) if config else None
        else:
            configs = list(self.mongo_handler[collection].find({'tenant_id': tenant_id}))
            for config in configs:
                # if token_expires_at is not offset aware, convert it to offset aware
                if config.get('credentials').get('token_expires_at'):
                    if not config.get('credentials').get('token_expires_at').tzinfo:
                        config['credentials']['token_expires_at'] = from_isoformat(generate_timestamp())

                if config.get('credentials').get('token_expires_at') and config.get('credentials').get('token_expires_at') <= from_isoformat(generate_timestamp()):
                    try:
                        refreshed_config = self.refresh_token(config.get('scm_id'))
                        config['credentials'] = refreshed_config.credentials.model_dump()
                    except Exception as e:
                        print(f"Error refreshing token: {e}")

                config['encrypted_scm_id'] = encrypt_string(config['scm_id'])
            return [SCMConfiguration(**config) for config in configs] if configs else []

            

    def delete_configuration(self, tenant_id: str, scm_id: str = None):
        self.mongo_handler = get_mongo_db().db
        self.db_name = self.mongo_handler.name
        collection = get_collection_name(self.db_name, self.collection_name)
        
        if scm_id:
            result = self.mongo_handler[collection].delete_one({
                'tenant_id': tenant_id,
                'scm_id': scm_id
            })
        else:
            result = self.mongo_handler[collection].delete_many({'tenant_id': tenant_id})
        return result.deleted_count

    async def generate_ssh_key_pair(self) -> Dict[str, str]:
        # Generate RSA key pair
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        
        # Get private key in PEM format
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        # Get public key in PEM format
        public_key = private_key.public_key()
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.OpenSSH
        )
        
        return {
            "private_key": private_pem.decode(),
            "public_key": public_pem.decode()
        }

class SCMOAuthManager:
    def __init__(self):
        self.mongo_handler = None
        self.db_name = None
        self.collection_name = 'scm_oauth_sessions'
        self.oauth = OAuth()
        
        # Configure OAuth clients
        self.oauth.register(
            name='github',
            client_id=settings.GITHUB_CLIENT_ID_CODEGEN,
            client_secret=settings.GITHUB_CLIENT_SECRET_CODEGEN,
            access_token_url='https://github.com/login/oauth/access_token',
            access_token_params=None,
            authorize_url='https://github.com/login/oauth/authorize',
            authorize_params=None,
            api_base_url='https://api.github.com/',
            client_kwargs={'scope': 'repo admin:org admin:repo_hook'},
        )
        
        self.oauth.register(
            name='gitlab',
            client_id=settings.GITLAB_CLIENT_ID_CODEGEN,
            client_secret=settings.GITLAB_CLIENT_SECRET_CODEGEN,
            access_token_url='https://gitlab.com/oauth/token',
            authorize_url='https://gitlab.com/oauth/authorize',
            api_base_url='https://gitlab.com/api/v4/',
            client_kwargs={'scope': 'api read_repository write_repository'},
        )


    async def save_oauth_session(self, tenant_id: str, state: str, scm_type: SCMType):
        self.mongo_handler = get_mongo_db().db
        self.db_name = self.mongo_handler.name
        collection = get_collection_name(self.db_name, self.collection_name)
        self.mongo_handler[collection].insert_one({
            'tenant_id': tenant_id,
            'state': state,
            'scm_type': scm_type,
            'created_at': from_isoformat(generate_timestamp())
        })

    async def verify_oauth_session(self, state: str) -> Optional[dict]:
        self.mongo_handler = get_mongo_db().db
        self.db_name = self.mongo_handler.name
        collection = get_collection_name(self.db_name, self.collection_name)
        return self.mongo_handler[collection].find_one({'state': state})

scm_manager = SCMManager()
oauth_manager = SCMOAuthManager()

@router.post("/configure")
async def configure_scm(
    config: SCMConfiguration,
    current_user: dict = Depends(get_current_user),
    is_admin: bool = Depends(is_admin)
):
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    tenant_id = current_user.get("custom:tenant_id")
    
    try:
        # Validate SCM access
        if config.scm_type == SCMType.GITHUB:
            g = Github(config.credentials.access_token)
            org = g.get_organization(config.credentials.organization)
            # Test organization access
            org.get_repos()
        elif config.scm_type == SCMType.GITLAB:
            gl = gitlab.Gitlab(
                config.api_url or 'https://gitlab.com',
                private_token=config.credentials.access_token
            )
            gl.auth()
            gl.groups.get(config.credentials.organization)

        # Generate SSH key pair if SSH auth type is selected
        if config.credentials.auth_type == SCMAuthType.SSH:
            ssh_keys = await scm_manager.generate_ssh_key_pair()
            config.credentials.ssh_key = ssh_keys["private_key"]

        # Save configuration
        await scm_manager.save_configuration(tenant_id, config)

        return SCMResponse(
            status="success",
            message="SCM configuration saved successfully",
            data={
                "public_key": ssh_keys["public_key"] if config.credentials.auth_type == SCMAuthType.SSH else None
            }
        )

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/configuration")
async def get_scm_configuration(
    scm_id: Optional[str] = None,
    is_encrypted: bool = False,
    current_user: dict = Depends(get_current_user)
):


    tenant_id = current_user.get("custom:tenant_id")
    if is_encrypted and scm_id:
        scm_id = decrypt_string(scm_id)
    configs = scm_manager.get_configuration(tenant_id, scm_id)
    
    if not configs:
        return SCMResponse(
            status="success",
            message="No SCM configuration found",
            data=None
        )
    
    from datetime import datetime
    from app.utils.datetime_utils import from_isoformat, generate_timestamp

    def get_gitlab_username(config: SCMConfiguration) -> str:
        """
        Fetch GitLab username from user ID with token expiration handling.
        """
        try:
            user_id = config.credentials.organization
            # Check if user_id is numeric
            if not user_id.isdigit():
                return user_id
                
            # Check if token is expired
            if (config.credentials.token_expires_at and 
                config.credentials.token_expires_at <= from_isoformat(generate_timestamp())):
                # Refresh token if expired
                try:
                    config = scm_manager.refresh_token(config.scm_id)
                except Exception as e:
                    print(f"Error refreshing token: {e}")
                    return user_id

            headers = {
                'Authorization': f'Bearer {config.credentials.access_token}',
                'Accept': 'application/json'
            }
            response = requests.get(
                f'https://gitlab.com/api/v4/users/{user_id}',
                headers=headers
            )
            if response.status_code == 200:
                return response.json().get('username', user_id)
            return user_id
        except Exception as e:
            print(f"Error fetching GitLab username: {e}")
            return user_id

    # Modified configuration response section
    if isinstance(configs, list):
        configurations = []
        for config in configs:
            config_dict = config.dict(exclude={'credentials': {'access_token', 'ssh_key', 'ssh_key_passphrase', 'refresh_token'}})
            
            # Convert GitLab user ID to username if applicable
            if (config.scm_type == SCMType.GITLAB and 
                config.credentials.organization and 
                config.credentials.organization.isdigit()):
                # Pass the entire config object instead of just the token
                config_dict['credentials']['organization'] = get_gitlab_username(config)
            
            configurations.append(config_dict)
        
        return SCMResponse(
            status="success",
            message="SCM configurations retrieved successfully",
            data={
                "configurations": configurations
            }
        )
    else:
        # Return single configuration
        return SCMResponse(
            status="success",
            message="SCM configuration retrieved successfully",
            data=configs.dict(exclude={'credentials': {'access_token', 'ssh_key', 'ssh_key_passphrase', 'refresh_token'}})
        )

@router.delete("/configuration")
async def delete_scm_configuration(
    scm_id: str,
    is_encrypted: bool = False,
    current_user: dict = Depends(get_current_user)
):
    """
    Delete SCM configuration by scm_id.
    
    Args:
        scm_id: The encrypted or unencrypted SCM ID
        is_encrypted: Whether the scm_id is encrypted
        current_user: Current authenticated user
        
    Returns:
        SCMResponse with deletion status
    """
    try:
        # Get tenant ID from current user
        tenant_id = current_user.get("custom:tenant_id")
        
        # Decrypt scm_id if it's encrypted
        if is_encrypted:
            scm_id = decrypt_string(scm_id)
            
        # Get MongoDB handler
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='scm_configurations'
        )
        
        # Delete configuration document
        result = await mongo_handler.delete_by_filter(
            filter={
                "scm_id": scm_id,
                "tenant_id": tenant_id
            },
            db=mongo_handler.db
        )
        
        if result.deleted_count > 0:
            return SCMResponse(
                status="success",
                message="SCM configuration deleted successfully",
                data={"deleted_scm_id": scm_id}
            )
        else:
            raise HTTPException(
                status_code=404,
                detail="SCM configuration not found"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete SCM configuration: {str(e)}"
        )
        
@router.get("/github/configurations")
async def list_github_configurations(
    current_user: dict = Depends(get_current_user)
):
    """
    List all GitHub SCM configurations for the current tenant.
    Returns only GitHub configurations with sensitive data excluded.
    """
    tenant_id = current_user.get("custom:tenant_id")
    
    try:
        # Get all configurations for the tenant
        all_configs = scm_manager.get_configuration(tenant_id)
        
        if not all_configs:
            return SCMResponse(
                status="success",
                message="No GitHub SCM configurations found",
                data={
                    "configurations": [],
                    "total_count": 0
                }
            )
        
        # Filter only GitHub configurations
        github_configs = [
            config for config in all_configs 
            if isinstance(config, SCMConfiguration) and config.scm_type == SCMType.GITHUB
        ]
        
        if not github_configs:
            return SCMResponse(
                status="success",
                message="No GitHub SCM configurations found",
                data={
                    "configurations": [],
                    "total_count": 0
                }
            )
        
        # Prepare response data excluding sensitive information
        configs_data = []
        for config in github_configs:
            config_dict = config.dict(exclude={
                'credentials': {
                    'access_token', 
                    'ssh_key', 
                    'ssh_key_passphrase', 
                    'refresh_token'
                }
            })
            
            # Add encrypted SCM ID for frontend operations
            if hasattr(config, 'scm_id'):
                config_dict['encrypted_scm_id'] = encrypt_string(config.scm_id)
     
            config_dict['organization'] = config_dict.get("credentials",{}).get("organization")
            configs_data.append(config_dict)
        
        return SCMResponse(
            status="success",
            message=f"Found {len(github_configs)} GitHub SCM configuration(s)",
            data={
                "configurations": configs_data,
                "total_count": len(github_configs)
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to retrieve GitHub configurations: {str(e)}"
        )

@router.get("/gitlab/configurations")
async def list_gitlab_configurations(
    current_user: dict = Depends(get_current_user)
):
    """
    List all GitLab SCM configurations for the current tenant.
    Returns only GitLab configurations with sensitive data excluded.
    """
    tenant_id = current_user.get("custom:tenant_id")
    
    try:
        # Get all configurations for the tenant
        all_configs = scm_manager.get_configuration(tenant_id)
        
        if not all_configs:
            return SCMResponse(
                status="success",
                message="No GitLab SCM configurations found",
                data={
                    "configurations": [],
                    "total_count": 0
                }
            )
        
        # Filter only GitLab configurations
        gitlab_configs = [
            config for config in all_configs 
            if isinstance(config, SCMConfiguration) and config.scm_type == SCMType.GITLAB
        ]
        
        if not gitlab_configs:
            return SCMResponse(
                status="success",
                message="No GitLab SCM configurations found",
                data={
                    "configurations": [],
                    "total_count": 0
                }
            )
        
        # Prepare response data excluding sensitive information
        configs_data = []
        for config in gitlab_configs:
            config_dict = config.dict(exclude={
                'credentials': {
                    'access_token', 
                    'ssh_key', 
                    'ssh_key_passphrase', 
                    'refresh_token'
                }
            })
            
            # Add encrypted SCM ID for frontend operations
            if hasattr(config, 'scm_id'):
                config_dict['encrypted_scm_id'] = encrypt_string(config.scm_id)
     
            config_dict['organization'] = config_dict.get("credentials",{}).get("organization")
            configs_data.append(config_dict)
        
        return SCMResponse(
            status="success",
            message=f"Found {len(gitlab_configs)} GitLab SCM configuration(s)",
            data={
                "configurations": configs_data,
                "total_count": len(gitlab_configs)
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to retrieve GitLab configurations: {str(e)}"
        )
        
async def delete_scm_configuration(
    scm_id: Optional[str] = None,
    is_encrypted: bool = False,
    current_user: dict = Depends(get_current_user),
    is_admin: bool = Depends(is_admin)
):
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    tenant_id = current_user.get("custom:tenant_id")
    if is_encrypted and scm_id:
        scm_id = decrypt_string(scm_id)
    deleted_count = scm_manager.delete_configuration(tenant_id, scm_id)
    
    if deleted_count == 0:
        raise HTTPException(status_code=404, detail="No SCM configuration found")
    
    return SCMResponse(
        status="success",
        message=f"Successfully deleted {deleted_count} SCM configuration(s)"
    )

# Add new routes for OAuth flow
@router.get("/oauth/{scm_type}/login")
async def oauth_login(
    request: Request,
    scm_type: SCMType,
    return_url: bool = False,
    current_user: dict = Depends(get_current_user),
    is_admin: bool = Depends(is_admin)
):
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    tenant_id = current_user.get("custom:tenant_id")
    state = secrets.token_urlsafe(32)
    
    # Save OAuth session state
    await oauth_manager.save_oauth_session(tenant_id, state, scm_type)
    
    if scm_type == SCMType.GITHUB:
        client = oauth_manager.oauth.github
        redirect_uri = f"{settings.GITHUB_REDIRECTION_URI_CODEGEN}"
    elif scm_type == SCMType.GITLAB:
        client = oauth_manager.oauth.gitlab
        redirect_uri = f"{settings.GITLAB_REDIRECTION_URI_CODEGEN}"
    
    # If return_url is True, return the authorization URL instead of redirecting
    if return_url:
        response = await client.create_authorization_url(
            redirect_uri=redirect_uri,
            state=state
        )
        return SCMResponse(
            status="success",
            message="Authorization URL generated successfully",
            data=response
        )
    
    # Otherwise, perform the redirect
    return await client.authorize_redirect(
        request=request,
        redirect_uri=redirect_uri,
        state=state
    )

@router.get("/oauth/{scm_type}/callback")
async def oauth_callback(
    request: Request,
    scm_type: SCMType,
    state: str,
    code: str,
    current_user: dict = Depends(get_current_user),
    is_admin: bool = Depends(is_admin)
):
    user_id = current_user.get("cognito:username")
    
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    # Verify OAuth session
    session = await oauth_manager.verify_oauth_session(state)
    if not session:
        raise HTTPException(status_code=400, detail="Invalid OAuth session")

    try:
        if scm_type == SCMType.GITHUB:
            # Exchange code for GitHub token
            token_url = "https://github.com/login/oauth/access_token"
            headers = {"Accept": "application/json"}
            payload = {
                "client_id": settings.GITHUB_CLIENT_ID_CODEGEN,
                "client_secret": settings.GITHUB_CLIENT_SECRET_CODEGEN,
                "code": code,
                "redirect_uri": settings.GITHUB_REDIRECTION_URI_CODEGEN,
            }
            
            response = requests.post(token_url, headers=headers, data=payload)
            token_data = response.json()
            
            if 'error' in token_data:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to get access token: {token_data.get('error_description', token_data['error'])}"
                )
            
            access_token = token_data.get('access_token')
            if not access_token:
                raise HTTPException(status_code=400, detail="No access token in response")

            # Test the token by getting user info
            gh = Github(access_token)
            user = gh.get_user()
            
            config = SCMConfiguration(
                scm_type=SCMType.GITHUB,
                credentials=SCMCredentials(
                    auth_type=SCMAuthType.OAUTH,
                    access_token=access_token,
                    organization=user.login  # Default to user's login
                ),
                user_id=user_id
            )

        elif scm_type == SCMType.GITLAB:
            # Exchange code for GitLab token
            token_url = "https://gitlab.com/oauth/token"
            headers = {"Accept": "application/json"}
            payload = {
                "client_id": settings.GITLAB_CLIENT_ID_CODEGEN,
                "client_secret": settings.GITLAB_CLIENT_SECRET_CODEGEN,
                "code": code,
                "grant_type": "authorization_code",
                "redirect_uri": settings.GITLAB_REDIRECTION_URI_CODEGEN,
            }
            
            response = requests.post(token_url, headers=headers, data=payload)
            token_data = response.json()
            
            if 'error' in token_data:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to get access token: {token_data.get('error_description', token_data['error'])}"
                )
            
            access_token = token_data.get('access_token')
            refresh_token = token_data.get('refresh_token')
            token_expires_at = from_isoformat(generate_timestamp()) + datetime.timedelta(seconds=token_data['expires_in'])
            if not access_token:
                raise HTTPException(status_code=400, detail="No access token in response")

            # Test the token
            gl = gitlab.Gitlab(
                'https://gitlab.com',
                oauth_token=access_token
            )
            gl.auth()
            user = gl.users.list(username=gl.user.username)[0]
            
            config = SCMConfiguration(
                scm_type=SCMType.GITLAB,
                api_url='https://gitlab.com',
                credentials=SCMCredentials(
                    auth_type=SCMAuthType.OAUTH,
                    access_token=access_token,
                    refresh_token=refresh_token,
                    token_expires_at=token_expires_at,
                    organization=str(gl.user.username)
                ),
                user_id=user_id
            )

        # Save the configuration
        config = scm_manager.save_configuration(session['tenant_id'], config)
        config['encrypted_scm_id'] = encrypt_string(config['scm_id'])
        # Clean up OAuth session
        collection = get_collection_name(oauth_manager.db_name, oauth_manager.collection_name)
        oauth_manager.mongo_handler[collection].delete_one({'state': state})
        config.pop('access_token', None)
        return SCMResponse(
            status="success",
            message="OAuth authentication successful",
            data=config
        )

    except Exception as e:
        import traceback
        print(f"Exception occurred in oauth_callback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))