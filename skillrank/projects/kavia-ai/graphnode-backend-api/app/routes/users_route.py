from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, UploadFile, File
from fastapi import Body 
from pydantic import BaseModel, Field
import asyncio
import boto3
import json
from typing import Optional, Tuple
from botocore.exceptions import ClientError, ParamValidationError
from app.core.Settings import settings
from app.connection.establish_db_connection import get_node_db, NodeDB, get_mongo_db, get_mongo_db_v1
from app.models.user_model import UserUpdate , ProjectUpdate , AddRemoveUserDTO , TaskAssignment , TaskAssignmentResponse , UserConfig, available_modules, ModuleConfig
from app.models.notification_model import NotificationModel , ProjectNotificationData , TaskNotificationData
from typing import List, Optional
from fastapi import Query
from fastapi.responses import JSONResponse
from app.utils.auth_utils import get_current_user, get_user_id_from_token
from app.utils.user_utils import get_recent_project_usage, send_notification , get_llm_costs, filter_document_by_month, sum_costs, find_code_gen_tasks
from bson.objectid import ObjectId
from datetime import datetime
from app.connection.tenant_middleware import get_tenant_id, get_opentopublic
import calendar
from app.utils.cost_utils import find_agent_plan
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.utils.aws.cognito_main import TenantService
from app.classes.S3Handler import S3Handler
from app.repository.mongodb.notification_repository import NotificationRepository
from app.repository.mongodb.repositories import ConfigRepository
from app.repository.mongodb.client import get_db
from app.repository.mongodb.repositories import LLMCostsRepository
from app.utils.auth_userpool_utils import cognito_manager
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.models.organization_models import Organization, User

tenant_service = TenantService()
from bson import json_util
import mimetypes
from app.models.organization_models import Organization

default_tenant_id = settings.KAVIA_SUPER_TENANT_ID
from PIL import Image
import io

# Constants for image processing
MAX_FILE_SIZE = 2 * 1024 * 1024  # 2MB - common industry standard
MAX_IMAGE_DIMENSION = 2048  # Maximum width/height
JPEG_QUALITY = 85  # Good balance between quality and size
ALLOWED_TYPES = {
    'image/jpeg': 'JPEG',
    'image/png': 'PNG',
    'image/gif': 'GIF'
}


def compress_image(image_data: bytes, content_type: str) -> Tuple[bytes, str]:
    """Compress image while maintaining quality"""
    try:
        img = Image.open(io.BytesIO(image_data))
        
        # Convert RGBA to RGB if necessary
        if img.mode in ('RGBA', 'LA'):
            background = Image.new('RGB', img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1])
            img = background
        
        # Resize if dimensions are too large
        if max(img.size) > MAX_IMAGE_DIMENSION:
            ratio = MAX_IMAGE_DIMENSION / max(img.size)
            new_size = tuple(int(dim * ratio) for dim in img.size)
            img = img.resize(new_size, Image.LANCZOS)
        
        # Save with compression
        output = io.BytesIO()
        
        if content_type == 'image/gif':
            img.save(output, format='GIF', optimize=True)
        else:
            # Convert to JPEG for better compression
            img.save(output, format='JPEG', quality=JPEG_QUALITY, optimize=True)
            content_type = 'image/jpeg'
            
        compressed_data = output.getvalue()
        return compressed_data, content_type
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error processing image: {str(e)}"
        )
    
def get_phase_based_user_cost(user_data):
    new_user_data = {
        'user_id': user_data['user_id'],
        'type': user_data.get('type', ''),
        'user_cost': user_data.get('user_cost', 0),
        'projects': []
    }

    project_data = user_data.get("projects", [])
    for project in project_data:
        new_project_data = {
            'project_id': project['project_id'],
            'project_cost': project['project_cost'],
            'packages': []
        }

        packageData = []
        if 'agents' in project:
            for agent in project['agents']:
                packageName = find_agent_plan(agent['agent_name'])
                planExists = False

                for i in range(len(packageData)):
                    if packageData[i]['package_name'] == packageName:
                        planExists = True
                        packageData[i]["package_cost"] = f'${float(packageData[i]["package_cost"].strip("$")) + float(agent["total_cost"].strip("$")):.6f}'
                        packageData[i]["agents"].append(agent)
                        break

                if not planExists:
                    newPackageData = {
                        "package_name": packageName,
                        "package_cost": agent["total_cost"],
                        "agents": [agent]
                    }
                    packageData.append(newPackageData)

            new_project_data['packages'] = packageData

        new_user_data["projects"].append(new_project_data)

    return new_user_data

async def get_s3_handler(tenant_id: str = None) -> S3Handler:
    """Helper function to get the appropriate S3Handler based on tenant_id"""
    if not tenant_id:
        tenant_id = get_tenant_id()
    return S3Handler(tenant_id=tenant_id)

async def get_cognito_manager(tenant_id: str = None) -> CognitoUserManager:
    """Helper function to get the appropriate CognitoUserManager based on tenant_id"""
    if not tenant_id:
        tenant_id = get_tenant_id()
        
    if tenant_id == default_tenant_id:
        return CognitoUserManager(
            user_pool_id=settings.AWS_COGNITO_USER_POOL_ID,
            client_id=settings.AWS_COGNITO_APP_CLIENT_ID
        )
    else:
        creds = await tenant_service.get_tenant_cred(tenant_id)
        return CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )

_SHOW_NAME = "users"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=["Users Route"],
    responses={404: {"description": "Not found"}},
)

class OrganizationCost(BaseModel):
    organization_id: str
    organization_cost: str


# Model for optional query parameters
class ListUsersQuery(BaseModel):
    limit: int = Field(60, ge=1, le=60, description="Maximum number of users to return per page (max 60)")
    

client = boto3.client('cognito-idp', region_name=settings.AWS_REGION, aws_access_key_id=settings.AWS_ACCESS_KEY_ID, aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
user_pool_id = settings.AWS_COGNITO_USER_POOL_ID
custom_attributes = ["Name", "Designation", "Department"]

def format_response(users):
    for user in users:
        for attr in custom_attributes:
            user.setdefault(attr, "")
        for attr in user["Attributes"]:
            if attr["Name"].startswith("custom:"):
                user[attr["Name"][7:]] = attr["Value"].strip()
            else:
                user[attr["Name"].capitalize()] = attr["Value"].strip()
        del user["Attributes"]
    return users

def format_response_user(user):    
    for attr in custom_attributes:
        user.setdefault(attr, "")
    for attr in user["UserAttributes"]:
        if attr["Name"].startswith("custom:"):
            user[attr["Name"][7:]] = attr["Value"].strip()
        else:
            user[attr["Name"].capitalize()] = attr["Value"].strip()
    del user["UserAttributes"]
    del user["ResponseMetadata"]
    return user

def update_user_attributes(user_id, updates):
    """Constructs attribute list for Cognito API call."""
    attributes = [
        {"Name": f"custom:{key}", "Value": value} 
        for key, value in updates.items() if value 
    ]

    return attributes

def raise_http_exception_from_client_error(e):
    """Raises HTTPException based on the caught ClientError or ParamValidationError"""

    error_code = e.response['Error']['Code'] if isinstance(e, ClientError) else 'ValidationError'

    if error_code in ['InvalidParameterException', 'ValidationError']:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid parameters or request.")
    elif error_code == 'NotAuthorizedException':
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized.")
    elif error_code == 'UserNotFoundException':
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found.")
    else:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error.")

@router.get('/me/', response_description="Get current user details")
async def get_current_user_details(current_user=Depends(get_current_user)):
    data = get_tenant_id()
    # data['status'] = "active"
    
    # Use utility function to get correct user ID
    user_id = get_user_id_from_token(current_user)
    user_details = await User.get(user_id)
    # If user doesn't exist, create a new user
    if not user_details:
        user_data = {
            "_id": user_id,  # Use the correct user ID
            "name": current_user.get("custom:Name", ""),
            "email": current_user.get("email"),
            "contact_number": current_user.get("phone_number", ""),
            "department": current_user.get("custom:Department", ""),
            "organization_id": get_tenant_id(),
            "status": "active",
            "has_accepted_terms": False,
            "accepted_terms_at": None
        }
        user_details = await User.create(user_data)
    filtered_user = {
        "user_id": user_id,  # Use the correct user ID
        "email": current_user.get("email"),
        "name": current_user.get("custom:Name", ""),
        "designation": current_user.get("custom:Designation", ""),
        "department": current_user.get("custom:Department", ""),
        "picture": current_user.get("picture", ""),
        "is_admin": current_user.get("custom:is_admin", "false") == "true",
        "tenant_id": data,
        "tenant_details": await Organization.get(get_tenant_id()),
        "opentopublic": True if get_tenant_id() == settings.KAVIA_SUPER_TENANT_ID else get_opentopublic(),
        "status": user_details.get("status", "active"),
        "has_accepted_terms": user_details.get("has_accepted_terms", False),
        "accepted_terms_at": user_details.get("accepted_terms_at", None)
    }

    print( "filtered_user" , filtered_user)

    tenant_id = get_tenant_id()
    filtered_user["tenant_id"] = tenant_id
    filtered_user["is_super_admin"] = tenant_id == settings.KAVIA_SUPER_TENANT_ID
    return filtered_user


@router.get('/configuration/modules/', response_description="Get configuration details for a user")
async def get_configuration_details(user_id: str = None, current_user=Depends(get_current_user)):
    """Retrieves the configuration details for a user."""
    try:
        if not user_id:
            user_id = get_user_id_from_token(current_user)
        config_repo : ConfigRepository = await get_mongo_db_v1("config")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        configuration = await config_repo.find_one({"user_id": user_id}, db=db)
        user_config = None
        if configuration:
            configuration = configuration.get("user_config", {})
            user_config = configuration.get("modules", {})
        if not user_config:
            # Return default configuration with available modules
            return {
                    module_name: {
                        "current": module_config.current_model,
                        "display_name": module_config.display_name,
                        "options": [model.value for model in module_config.available_models]
                    }
                    for module_name, module_config in available_modules.items()
            }

        for module_name, module_config in available_modules.items():
            if module_name not in user_config:
                user_config[module_name] = {
                    "current": module_config.current_model,
                    "display_name": module_config.display_name,
                    "options": [model.value for model in module_config.available_models]
                }
            else:
                user_config[module_name]["options"] = [model.value for model in module_config.available_models]
                user_config[module_name]["display_name"] = module_config.display_name
                user_config[module_name]["current"] = user_config[module_name].get("llm_model", module_config.current_model)
        return user_config

    except Exception as e:
        print ("Exception:" , e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.put('/configuration/modules/{module_name}/', response_description="Update configuration for a specific module")
async def update_module_configuration(
    module_name: str,
    module_config: ModuleConfig,
    user_id: str = None,
    current_user=Depends(get_current_user)
):
    """Updates the configuration for a specific module."""
    try:
        if not user_id:
            user_id = get_user_id_from_token(current_user)
        config_repo : ConfigRepository = await get_mongo_db_v1("config")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")

        # Validate module and configuration
        if module_name not in available_modules:
            raise ValueError(f"Invalid module: {module_name}")
        if module_config.llm_model not in available_modules[module_name].available_models:
            raise ValueError(f"Invalid LLM model for module {module_name}")

        result = await config_repo.update_one(
            {"user_id": user_id},
            {f"user_config.modules.{module_name}": json.loads(module_config.model_dump_json())},
            db=db,
            upsert=True,
        )
        print(result)
        return {"message": f"Configuration for module {module_name} updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    
@router.get('/configuration/', response_description="Get configuration details for a user")
async def get_configuration_details(user_id: str = None, module: str = None ,current_user=Depends(get_current_user)):
    try:
        if not user_id:
            user_id = get_user_id_from_token(current_user)
        config_repo : ConfigRepository = await get_mongo_db_v1("config") 
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        configuration = await config_repo.find_one({"user_id": user_id}, db=db)
        if configuration:
            configuration = configuration.get("user_config", {}).get(module, {})
            return {"configuration": configuration}
        else:
            return {"configuration": {} }
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.put('/configuration/', response_description="Update configuration for a user")
async def update_configuration(
    user_id: str = None,
    module: str = None,
    config: dict = Body(...),
    current_user=Depends(get_current_user)
):
    try:
        if not user_id:
            user_id = get_user_id_from_token(current_user)
        config_repo :ConfigRepository = await get_mongo_db_v1("config")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        result = await config_repo.update_one(
            {"user_id": user_id},
            {f"user_config.{module}":config },
            db=db,
            upsert=True,
        )
        return {"message": "Configuration updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.patch("/notifications/mark_all_read/", response_description="Mark all notifications as read")
async def mark_all_notifications_as_read(
    user_id: str = None,
    current_user = Depends(get_current_user)):
    try:
        if not user_id:
            user_id = get_user_id_from_token(current_user)
        
        notification_repo: NotificationRepository = await get_mongo_db_v1("notification")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        await notification_repo.mark_all_read(user_id, db)
        return {"message": "All notifications marked as read"}
    except Exception as e:
        print(str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    
@router.get("/notifications/", response_description="Get notifications for a user")
async def get_notifications(
    limit: int = Query(20, ge=1, le=100, description="Maximum number of notifications per page"),
    page: int = Query(1, ge=1, description="Page number"),
    is_read: Optional[bool] = Query(None, description="Filter by read/unread status"),
    user_id: str = None,
    current_user = Depends(get_current_user)
):
    try:
        if not user_id:
            user_id = get_user_id_from_token(current_user)

        notification_repo: NotificationRepository = await get_mongo_db_v1("notification")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        records = await notification_repo.get_notifications(user_id, is_read, page, limit, db)

        if not records:
            return []

        for record in records:
            record["notification_id"] = str(record["_id"])
            record.pop("_id")
        return records
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    
@router.get("/notifications/unread_count/", response_description="Get count of unread notifications for a user")
async def get_unread_notifications_count(user_id: str = None, current_user = Depends(get_current_user)):
    if not user_id:
        user_id = get_user_id_from_token(current_user)
    
    notification_repo: NotificationRepository = await get_mongo_db_v1("notification")
    db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
    return await notification_repo.get_unread_count(user_id, db)

@router.patch("/notifications/{notification_id}/", response_description="Mark a notification as read")
async def mark_notification_as_read(
    notification_id: str,
    user_id: str = None,
    current_user = Depends(get_current_user)
):
    try:
        if not user_id:
            user_id = get_user_id_from_token(current_user)
        
        notification_repo: NotificationRepository = await get_mongo_db_v1("notification")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        await notification_repo.mark_as_read(notification_id, db)
        return {"message": "Notification marked as read"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.delete("/notifications/{notification_id}/", response_description="Delete a notification")
async def delete_notification(
    notification_id: str,
    user_id: str = None,
    current_user = Depends(get_current_user)
):
    try:
        if not user_id:
            user_id = get_user_id_from_token(current_user)
        
        notification_repo: NotificationRepository = await get_mongo_db_v1("notification")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        await notification_repo.delete_notification(notification_id, db)
        return {"message": "Notification deleted"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.delete("/notifications/", response_description="Delete all notifications")
async def delete_all_notifications(
    user_id: str = None,
    current_user = Depends(get_current_user)
):
    try:
        if not user_id:
            user_id = current_user.get("cognito:username")

        notification_repo: NotificationRepository = await get_mongo_db_v1("notification")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        await notification_repo.delete_all_notifications(user_id, db)
        return {"message": f"All notifications deleted for user {user_id}"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get("/", response_description="List all users")
async def list_users(
    query: ListUsersQuery = Depends(),
    pagination_token: str = None,
    all_users: bool = False,
    tenant_id: str = Query(None)
):
    try:
        tenant_id = get_tenant_id()
        cognito_manager = await get_cognito_manager(tenant_id)
        
        kwargs = {
            "UserPoolId": cognito_manager.user_pool_id,
            "Limit": query.limit
        }
        
        if not all_users:
            kwargs["Filter"] = 'cognito:user_status = "CONFIRMED"'
            
        if pagination_token:
            kwargs["PaginationToken"] = pagination_token
            
        response = cognito_manager.cognito.list_users(**kwargs)
        users = format_response(response["Users"])
        
        return {
            "users": users,
            "pagination_token": response.get("PaginationToken")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

        
@router.get("/{user_id}", response_description="Get a user by ID")
async def get_user_by_id(
    user_id: str,
    tenant_id: str = Query(None)
):
    try:
        cognito_manager = await get_cognito_manager(tenant_id)
        user = cognito_manager.get_user(user_id)
        return format_response_user(user)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/{user_id}", response_description="Update user details")
async def update_details_by_id(
    user_id: str,
    user_attributes: UserUpdate = Body(..., embed=True),  
    tenant_id: str = Query(None),
    db = Depends(get_node_db)
):
    try:
        cognito_manager = await get_cognito_manager(tenant_id)
        
        attributes = update_user_attributes(user_id, user_attributes.model_dump())
        print(attributes)
        # Update user attributes
        updated_user = cognito_manager.update_user_attributes(
            identifier=user_id,
            attributes=user_attributes.model_dump(),
            is_custom=True
        )

        # Update in node DB
        await db.upsert_user_db(user_id, format_response_user(updated_user))
        
        return {"message": f"User {user_id} updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/connect_to/{project_id}/", response_description="Connect a user to a Project")
async def connect_users_to_project(
    project_id: int, 
    project_update: ProjectUpdate = Body(..., embed=True),
    db:NodeDB = Depends(get_node_db), 
    current_user = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()):
    try:
  
        for member in project_update.members:
            result = await db.connect_user_to_project( project_id, member)
            notification = NotificationModel(
            receiver_id=str(member.user_id),
            type="project",
            action="add",
            data=ProjectNotificationData(
                message=f"You have been added to project {result[0].get('project_name')} by {current_user.get('email')}.",
                project_id=project_id,
                assigner_id = current_user.get("cognito:username"),
                link = f"/project/{project_id}"
            )
        )
            background_tasks.add_task(send_notification, notification)

        return {"message": f"Project {project_id} members updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.patch("/disconnect_from/{project_id}/", response_description="Disconnect users from a project")
async def disconnect_users_from_project(
    project_id: int,
    users: List[AddRemoveUserDTO] = Body(...), 
    db = Depends(get_node_db)
):
    try:
        for user in users:
            user = user.model_dump()
            await db.disconnect_user_from_project(project_id, user.get("user_id"))

        return {"message": f"Users disconnected from project {project_id} successfully"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get("/project/", response_description="Get users involved in a project")
async def get_project_members(
    project_id: int,
    db = Depends(get_node_db)
):
    try:
        members = await db.get_project_members(project_id)
        return {"members": members}
    except Exception as e:
        
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/project_user_to_add/", response_description="Get users to add in a project")
async def get_project_members(
    project_id: int,
    db:NodeDB = Depends(get_node_db)
):
    try:
        l = ListUsersQuery()
        l.limit = 60
        allusers = await list_users(l)
        allusers = allusers['users']
        users_to_send = []
        keys_to_return = ["Username", "Email", "Name"]
        for user in allusers:
            user_to_send={}
            for key in keys_to_return:
                user_to_send[key] = user.get(key)
            users_to_send.append(user_to_send)
        members = await db.get_project_members(project_id)
        member_user_ids = [member['user_id'] for member in members]
        # Filter non-members
        members_to_remove = []
        members_to_add = []
        for user in users_to_send:
            if user['Username'] in member_user_ids:
                user['Action'] = "Remove"
                members_to_remove.append(user)
            else:
                user['Action'] = "Add"
                members_to_add.append(user)
        # non_members = [user for user in allusers if user['Username'] not in member_user_ids]

        return { "members" : members_to_remove+members_to_add  }

    except Exception as e:

        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    
@router.patch("/assign_task/", response_description="Assign a task to a user")
async def assign_task(
    task_assignment: TaskAssignment = Body(...), 
    db = Depends(get_node_db),
    current_user = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    try:

        user_id = task_assignment.user_id
        node_id = task_assignment.node_id
        task_response, root_node = await asyncio.gather(
            db.assign_task(node_id, user_id, assigner_id=current_user.get("cognito:username")),
            db.get_root_node(node_id)
        )
        if isinstance(task_response, str):
            return TaskAssignmentResponse(success=False, message=task_response)
        task_response = task_response[0]
        task_type = task_response.get("properties").get("Type")
        root_props = root_node.get("properties", {})
        project_title = root_props.get("Title") or root_props.get("Name")
        if not task_type:
            task_type = task_response.get("labels")[0]
        notification = NotificationModel(
            receiver_id=str(user_id),
            type="task",
            action="assign",
            data=TaskNotificationData(
                message=f"You have been assigned a new {task_type} in {project_title}.",  
                task_id=str(node_id),
                project_id=root_node['id'],
                assigner_id=current_user.get("cognito:username"),
                task_type=task_type,
                link = f"/project/{root_node['id']}"      
            )
        )
        background_tasks.add_task(send_notification, notification)

        return TaskAssignmentResponse(success=True, message="Task assigned successfully")
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get("/recent_projects/", response_description="Get recent projects for a user")
async def get_recent_projects(user_id: str = None , current_user = Depends(get_current_user)):
    """Retrieves the most recent project activities for a user."""
    try:
        if not user_id:
            user_id = current_user.get("cognito:username")
        recent_projects = await get_recent_project_usage(user_id)   # Assuming 20 as default limit
        return JSONResponse(content=recent_projects)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving recent projects: {e}")


@router.get("/active/", response_description="Check if user is active")
async def check_user_active(
    user_id: str = None,
    current_user = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
) -> dict:
    if not user_id:
        user_id = current_user.get("cognito:username")
    try:
        # Query NodeDB for user
        user_data = await db.get_user_by_id(user_id)
        
        if user_data:
            # User exists in NodeDB, return active status with user data
            return {
                "active": True
            }
        else:
            # User not found in NodeDB
            return {
                "active": False,
                "message": "User not found in NodeDB"
            }
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking user active status: {str(e)}"
        )

@router.get("/tasks/", response_description="Get tasks assigned to a user")
async def get_assigned_tasks(
    user_id: str = None,
    current_user = Depends(get_current_user),
    db = Depends(get_node_db)
):
    try:
        if not user_id:
            user_id = current_user.get("cognito:username")
        tasks = await db.get_assigned_tasks(user_id)
        return tasks
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get("/count_tasks/", response_description="Get task count assigned to a user")
async def get_total_assigned_tasks(
    user_id: str = None,
    current_user = Depends(get_current_user),
    db = Depends(get_node_db)
):
    try:
        if not user_id:
            user_id = current_user.get("cognito:username")
        tasks = await db.get_total_assigned_tasks(user_id)
        return tasks
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    
    
@router.get("/discussions/", response_description="Get discussions for a user")
async def get_discussions(
    user_id: str = None,
    current_user = Depends(get_current_user),
    db:NodeDB = Depends(get_node_db)
):
    try:
        if not user_id:
            user_id = current_user.get("cognito:username")
        discussions = await db.get_involvement_in_discussions(user_id)
        return discussions
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    


@router.get("/llm_costs/", response_description="Get LLM costs for a user or all users")
async def get_user_llm_costs(
    user_id: str = Query(None),
    month: Optional[int] = Query(None, ge=0, le=12),
    year: Optional[int] = Query(None, ge=2000),
    current_user = Depends(get_current_user)
):
    try:
        if month == 0:  # 0 indicates "All Months"
            if not user_id:
                costs = await get_llm_costs()
                if costs:
                    return JSONResponse(content=costs)  # Return unfiltered costs
            else:
                costs = await get_llm_costs(user_id)
                if costs:
                    return JSONResponse(content=costs)  # Return unfiltered costs
        
       ## Default to current mont/year if not speicified
        if month is None:
            month = datetime.now().month
        if year is None:
            year = datetime.now().year
            
        print(f"Looking for costs - Month: {month}, Year: {year}")

        if not user_id:
            costs = await get_llm_costs()
            print(f"Retrieved all costs: {costs}")
            if costs:
                filtered_costs = [filter_document_by_month(doc, month, year) for doc in costs]
                filtered_costs = [doc for doc in filtered_costs if doc]
                if filtered_costs:
                    return JSONResponse(content=filtered_costs)
        else:
            print(f"Retrieving costs for user: {user_id}")
            costs = await get_llm_costs(user_id)
            print(f"Retrieved costs: {costs}")
            
            if costs:
                filtered_costs = filter_document_by_month(costs, month, year)
                print(f"Filtered costs: {filtered_costs}")
                if filtered_costs:
                    return JSONResponse(content=filtered_costs)
        
        month_name = calendar.month_name[month]
        message = f"No LLM costs found for {month_name} {year}"
        if user_id:
            message += f" for user {user_id}"
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"message": message}
        )
            
    except Exception as e:
        print(f"Error in get_user_llm_costs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving LLM costs: {str(e)}"
        )

@router.get("/llm_costs/{project_id}", response_description="Get LLM costs for a specific project")
async def get_project_llm_costs(
    project_id: int,
    tenant_id,
    current_user = Depends(get_current_user)
):
    try:
        db_prefix = settings.MONGO_DB_NAME
        if db_prefix.startswith('develop'):
            db_name = 'develop_kaviaroot'
        elif db_prefix.startswith('qa'):
            db_name = 'qa_kaviaroot'
        elif db_prefix.startswith('pre_prod'):
            db_name = 'pre_prod_kaviaroot'
        elif db_prefix.startswith('experimental'):
            db_name = 'experimental_kaviaroot'
            
        mongo_handler = get_mongo_db(
            db_name=db_name,
            collection_name="llm_costs"
        )

        tenantData = await mongo_handler.run_aggregate_list([
            {'$match': {'organization_id': tenant_id}},
            {'$unwind': '$users'},
            {'$unwind': '$users.projects'},
            {'$match': {'users.projects.project_id': project_id}},
            { '$group': {
                    '_id' : '$_id',
                    'organization_id': {'$first':'$organization_id'},
                    'users': {'$push': '$users'}
                }
            }
        ], db=mongo_handler.db)
        
        project_cost = 0

        if tenantData and 'users' in tenantData[0]:
            tenantUsers = tenantData[0]['users']
            for (user) in tenantUsers:
                project = user['projects']
                if 'project_id' in project and project['project_id'] == project_id:
                    project_cost += float(project['project_cost'].replace('$', ''))

        project_credits = project_cost * 20000 if tenant_id==settings.KAVIA_B2C_CLIENT_ID else project_cost * 20000 #separate credit calculation for b2c tenants

        response_data = {
            "project_credits": project_credits
        }

        return JSONResponse(content=response_data)

    except Exception as e:
        print(f"Error in get_project_llm_costs: {str(e)}")
        print(f"Current user data: {current_user}")  # Add this for debugging
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving project costs: {str(e)}"
        )
        
@router.get("/organizations/costs", response_model=List[OrganizationCost])
async def get_organization_costs():
    """
    Get organization IDs and costs from all documents in the llm_costs collection.
    """
    try:
        # Get MongoDB instance using your existing connection pattern
        mongo_handler = get_mongo_db(
            db_name="pre_prod_kaviaroot",
            collection_name="llm_costs"
        )

        # Use get_all_by_projection method which is available in your handler
        projection = {
            "organization_id": 1, 
            "organization_cost": 1, 
            "_id": 0
        }
        
        organizations = await mongo_handler.get_all_by_projection(
            projection=projection,
            db=mongo_handler.db
        )
        
        if not organizations:
            return JSONResponse(
                status_code=404,
                content={"detail": "No organizations found"}
            )
            
        return organizations

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"detail": f"Database error: {str(e)}"}
        )
        
 


# @router.get("/organizations/{organization_id}/costs")
# async def get_all_organizations_costs(organization_id: str):
#     """
#     Get organization details based on organization ID.
#     Database prefix is determined from settings.MONGO_DB_NAME
#     """
#     try:
#         # Map settings.MONGO_DB_NAME to full database name
#         db_mapping = {
#             "pre_prod": "pre_prod_kaviaroot",
#             "develop": "develop_kaviaroot",
#             "experimental": "experimental_kaviaroot"
#         }

#         # Get the database prefix from settings
#         db_prefix = settings.MONGO_DB_NAME

#         # Validate and get the full database name
#         if db_prefix not in db_mapping:
#             return JSONResponse(
#                 status_code=400,
#                 content={"detail": f"Invalid database prefix in settings. Must be one of: {', '.join(db_mapping.keys())}"}
#             )

#         full_db_name = db_mapping[db_prefix]
#         collection_name = "llm_costs"

#         # Print database information
#         print(f"MongoDB Database: {full_db_name}")
#         print(f"MongoDB Collection: {collection_name}")
        
#         # Get MongoDB connection with the appropriate database
#         mongo_db = get_mongo_db(
#             db_name=full_db_name,
#             collection_name=collection_name
#         )
        
#         # Get the document for the specific organization
#         document = await mongo_db.get_one(
#             filter={"organization_id": organization_id},
#             db=mongo_db.db
#         )
        
#         if not document:
#             return JSONResponse(
#                 status_code=404,
#                 content={"detail": f"Organization with ID {organization_id} not found in {full_db_name}"}
#             )
        
#         # Convert MongoDB document to JSON serializable format
#         json_compatible_document = json.loads(json_util.dumps(document))
            
#         return JSONResponse(
#             status_code=200,
#             content=json_compatible_document
#         )

#     except Exception as e:
#         return JSONResponse(
#             status_code=500,
#             content={"detail": f"Database error: {str(e)}"}
#         )
        
# @router.get("/organizations/costs")
# async def get_all_organizations_cost():
#     """
#     Get organization_id and organization_cost from all documents in llm_costs collection.
#     """
#     try:
#         # Get MongoDB connection
#         mongo_db = get_mongo_db(
#             db_name="pre_prod_kaviaroot", 
#             collection_name="llm_costs"
#         )
        
#         # Get all documents but only retrieve organization_id and organization_cost fields
#         projection = {
#             "organization_id": 1,
#             "organization_cost": 1,
#             "_id": 0  # Exclude the _id field
#         }
        
#         # Find all documents with the specified projection
#         documents = await mongo_db.get_all(
#             filter={},
#             projection=projection,
#             db=mongo_db.db
#         )
        
#         if not documents:
#             return JSONResponse(
#                 status_code=404,
#                 content={"detail": "No documents found in llm_costs collection"}
#             )
        
#         # Convert MongoDB documents to JSON serializable format
#         json_compatible_documents = json.loads(json_util.dumps(documents))
            
#         return JSONResponse(
#             status_code=200,
#             content=json_compatible_documents
#         )

#     except Exception as e:
#         return JSONResponse(
#             status_code=500,
#             content={"detail": f"Database error: {str(e)}"}
#         )

# @router.get("/organizations/costs")
# async def get_all_organizations_costs():
    # """
    # Get organization ID, name, and total cost for all organizations from llm_costs collection.
    # Returns default name 'Unknown Organization' if name is not present.
    # """
    # try:
    #     print(f"MongoDB Database: {settings.MONGO_DB_NAME}_{get_tenant_id()}")
    #     print(f"MongoDB Collection: llm_costs")
    #     print(f"Neo4j Database: {settings.NEO4J_DATABASE}")
        
    #     # Get MongoDB connection
    #     mongo_db = get_mongo_db(
    #         db_name="pre_prod_kaviaroot", 
    #         collection_name="llm_costs"
    #     )
        
    #     # Get all documents and handle missing organization names
    #     pipeline = [
    #         {
    #             "$project": {
    #                 "_id": 0,
    #                 "organization_id": 1,
    #                 "organization_cost": 1,
    #                 "organization_name": {
    #                     "$ifNull": ["$organization_name", "Unknown Organization"]
    #                 }
    #             }
    #         }
    #     ]
        
    #     documents = await mongo_db.aggregate(pipeline)
        
    #     if not documents:
    #         return JSONResponse(
    #             status_code=404,
    #             content={"detail": "No organizations found"}
    #         )
        
    #     # Convert MongoDB documents to JSON serializable format
    #     json_compatible_documents = json.loads(json_util.dumps(documents))
            
    #     return JSONResponse(
    #         status_code=200,
    #         content=json_compatible_documents
    #     )

    # except Exception as e:
    #     return JSONResponse(
    #         status_code=500,
    #         content={"detail": f"Database error: {str(e)}"}
    #     )
    
# @router.get("/organizations/{organization_id}/costs")
# async def get_all_organizations_costs(organization_id: str):
#     """
#     Get organization details with project titles.
#     """
#     try:
#         # Map settings.MONGO_DB_NAME to full database name
#         db_mapping = {
#             "pre_prod": "pre_prod_kaviaroot",
#             "develop": "develop_kaviaroot",
#             "experimental": "experimental_kaviaroot"
#         }

#         db_prefix = settings.MONGO_DB_NAME
#         if db_prefix not in db_mapping:
#             return JSONResponse(
#                 status_code=400,
#                 content={"detail": f"Invalid database prefix in settings. Must be one of: {', '.join(db_mapping.keys())}"}
#             )

#         full_db_name = db_mapping[db_prefix]
#         collection_name = "llm_costs"

#         print(f"MongoDB Database: {full_db_name}")
#         print(f"MongoDB Collection: {collection_name}")
        
#         # Get MongoDB connection
#         mongo_db = get_mongo_db(
#             db_name=full_db_name,
#             collection_name=collection_name
#         )
        
#         # Get organization document
#         document = await mongo_db.get_one(
#             filter={"organization_id": organization_id},
#             db=mongo_db.db
#         )
        
#         if not document:
#             return JSONResponse(
#                 status_code=404,
#                 content={"detail": f"Organization with ID {organization_id} not found in {full_db_name}"}
#             )

#         # Extract unique project IDs
#         project_ids = []
#         for user in document.get('users', []):
#             for project in user.get('projects', []):
#                 if project['project_id'] not in project_ids:
#                     project_ids.append(project['project_id'])
        
#         # Get project titles if we have project IDs
#         if project_ids:
#             # Get NodeDB connection for the organization
#             node_db = get_node_db(organization_id)
#             project_titles = await node_db.get_project_titles(project_ids)
            
#             # Map titles to projects
#             title_mapping = {item['project_id']: item['project_title'] for item in project_titles}
            
#             # Update document with project titles
#             for user in document.get('users', []):
#                 for project in user.get('projects', []):
#                     project['project_title'] = title_mapping.get(project['project_id'], "Unknown Project")
        
#         return JSONResponse(
#             status_code=200,
#             content=json.loads(json_util.dumps(document))
#         )

#     except Exception as e:
#         print(f"Error in get_all_organizations_costs: {str(e)}")
#         return JSONResponse(
#             status_code=500,
#             content={"detail": f"Database error: {str(e)}"}
#         )


@router.get("/organizations/{organization_id}/costs")
async def get_all_organizations_costs(organization_id: str):
    """
    Get organization details with project titles and user names.
    """
    try:
        # Map settings.MONGO_DB_NAME to full database name
        db_mapping = {
            "pre_prod": "pre_prod_kaviaroot", 
            "develop": "develop_kaviaroot",
            "qa": "qa_kaviaroot",
            "experimental": "experimental_kaviaroot"
        }

        db_prefix = settings.MONGO_DB_NAME
        if db_prefix not in db_mapping:
            return JSONResponse(
                status_code=400,
                content={"detail": f"Invalid database prefix in settings. Must be one of: {', '.join(db_mapping.keys())}"}
            )

        full_db_name = db_mapping[db_prefix]
        collection_name = "llm_costs"
        
        # Get MongoDB connection
        mongo_db = get_mongo_db(
            db_name=full_db_name,
            collection_name=collection_name
        )
        
        # Get organization document
        document = await mongo_db.get_one(
            filter={"organization_id": organization_id},
            db=mongo_db.db
        )
        
        if not document:
            return JSONResponse(
                status_code=404,
                content={"detail": f"Organization with ID {organization_id} not found in {full_db_name}"}
            )

        # Extract unique project IDs and user IDs
        project_ids = []
        user_ids = []
        for user in document.get('users', []):
            user_id = user.get('user_id')
            if user_id not in user_ids:
                user_ids.append(user_id)
            
            for project in user.get('projects', []):
                if project['project_id'] not in project_ids:
                    project_ids.append(project['project_id'])

        # Async tasks for fetching project titles and user names
        async def get_project_titles_async():
            if project_ids:
                node_db = get_node_db(organization_id)
                return await node_db.get_project_titles(project_ids)
            return []

        async def get_user_names_async():
            if user_ids:
                users_db = get_mongo_db(
                    db_name=full_db_name,
                    collection_name="users"
                )
                user_docs = []
                for doc in users_db.db[users_db.collection].find({"_id": {"$in": user_ids}}):
                    user_docs.append(doc)
                return {doc["_id"]: doc.get("name", "Unknown User") for doc in user_docs}
            return {}

        # Execute both async tasks concurrently
        project_titles, user_names = await asyncio.gather(
            get_project_titles_async(),
            get_user_names_async()
        )

        # Create mappings
        title_mapping = {item['project_id']: item['project_title'] for item in project_titles}
        
        # Update document with project titles and user names
        for user in document.get('users', []):
            # Add user name
            user['user_name'] = user_names.get(user['user_id'], "Unknown User")
            
            # Add project titles
            for project in user.get('projects', []):
                project['project_title'] = title_mapping.get(project['project_id'], "Unknown Project")

        return JSONResponse(
            status_code=200,
            content=json.loads(json_util.dumps(document))
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"detail": f"Database error: {str(e)}"}
        )

@router.get("/organizations/{organization_id}/users/{user_id}/costs")
async def get_user_costs(organization_id: str, user_id: str):
    """
    Get organization details with project titles and user names.
    """
    try:
        # Map settings.MONGO_DB_NAME to full database name
        db_mapping = {
            "pre_prod": "pre_prod_kaviaroot", 
            "develop": "develop_kaviaroot",
            "qa": "qa_kaviaroot",
            "experimental": "experimental_kaviaroot"
        }

        db_prefix = settings.MONGO_DB_NAME
        if db_prefix not in db_mapping:
            return JSONResponse(
                status_code=400,
                content={"detail": f"Invalid database prefix in settings. Must be one of: {', '.join(db_mapping.keys())}"}
            )

        full_db_name = db_mapping[db_prefix]
        collection_name = "llm_costs"

        # Get MongoDB connection
        mongo_db = get_mongo_db(
            db_name=full_db_name,
            collection_name=collection_name
        )

        # Get organization document
        document = await mongo_db.get_one(
            filter={"organization_id": organization_id},
            db=mongo_db.db
        )

        users = document.get('users', [])
        user_data = {}

        if users:
            user_data = next((u for u in users if u.get('user_id', '') == user_id), {})

        project_ids = []
        user_id = user_data.get('user_id')
        
        for project in user_data.get('projects', []):
                if project['project_id'] not in project_ids:
                    project_ids.append(project['project_id'])  

        # Async tasks for fetching project titles and user names and plan names

        from app.routes.products_route import get_products  # Import your existing function
        products = await get_products()

        price_to_product_map = {}
        for product in products:
            price_to_product_map[product.price_id] = product.product_name

        # Add free plan to the mapping
        price_to_product_map["free_plan"] = "Free Plan"

        current_plan_id = user_data.get("current_plan")
        if current_plan_id:
            plan_name = price_to_product_map.get(current_plan_id, "Unknown Plan")
            user_data["current_plan_name"] = plan_name

        plans_history = user_data.get("plans_history", [])
        if(len(plans_history)> 0):
            for plan in plans_history:
                plan['plan_name'] = price_to_product_map.get(plan.get('plan_id'), "Unknown Plan")

        user_data['plans_history'] = plans_history
        
        async def get_project_titles_async():
            if project_ids:
                node_db = get_node_db(organization_id)
                return await node_db.get_project_titles(project_ids)
            return []

        async def get_user_names_async():
            if user_id:
                users_db = get_mongo_db(
                    db_name=full_db_name,
                    collection_name="users"
                )
                user_doc = users_db.db[users_db.collection].find_one({"_id": user_id})
                if user_doc:
                    return {user_doc["_id"]: user_doc.get("name", "Unknown User")}
            return {}    
        
        # Execute both async tasks concurrently
        project_titles, user_names = await asyncio.gather(
            get_project_titles_async(),
            get_user_names_async()
        )

        # Create mappings
        title_mapping = {
            item['project_id']: item['project_title']
            for item in project_titles
        }

        # Add user name and project titles to user_data
        user_data['user_name'] = user_names.get(user_id, "Unknown User")

        for project in user_data.get('projects', []):
            project['project_title'] = title_mapping.get(project.get('project_id'), "Unknown Project")

        phase_based_user_cost = get_phase_based_user_cost(user_data)

        return {'user_data': user_data, 'user_phase_costs': phase_based_user_cost}

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"detail": f"Database error: {str(e)}"}
        )

# PUBLIC_INTERFACE
@router.post("/profile-picture", response_description="Upload profile picture")
async def upload_profile_picture(
    file: UploadFile = File(...),
    current_user=Depends(get_current_user),
    node_db: NodeDB = Depends(get_node_db),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Upload a profile picture for a user.
    The image will be stored in S3 and the URL will be updated in Cognito.
    
    Restrictions:
    - Maximum file size: 2MB
    - Maximum dimension: 2048px
    - Allowed file types: JPEG, PNG, GIF
    """
    try:
        user_id = current_user.get("cognito:username")
        cognito_manager = await get_cognito_manager(get_tenant_id())
        # Validate file type
        if file.content_type not in ALLOWED_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type. Only JPEG, PNG and GIF images are allowed"
            )

        # Read initial chunk to verify size
        file_content = await file.read(MAX_FILE_SIZE + 1)
        if len(file_content) > MAX_FILE_SIZE:
            # # Try to compress if too large
            # file_content, content_type = compress_image(file_content, file.content_type)
            
            # if len(file_content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File size exceeds maximum limit of 2MB"
            )
        else:
            content_type = file.content_type
        
        # Get S3 handler with tenant context
        s3_handler = await get_s3_handler()
        s3_handler.set_cognito_manager(cognito_manager)
        # Upload profile picture
        s3key = s3_handler.upload_profile_picture(
            user_id=user_id,
            image_data=file_content,
            content_type=content_type
        )
        
        if not s3key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload profile picture"
            )
            
        # Generate URL for the profile picture
        url = f"https://{s3_handler.profile_pictures_bucket}.s3.amazonaws.com/{s3key}"

        # Update Cognito user attributes
  
        updated_user=cognito_manager.update_user_attributes(
            identifier=user_id,
            attributes={"picture": url},
            is_custom=False
        )
        # Update in node DB
        await node_db.upsert_user_db(user_id, format_response_user(updated_user))

        return {"url": url}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in upload_profile_picture: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error uploading profile picture"
        )
        
@router.get("/profile-picture", response_description="Get profile picture URL")
async def get_profile_picture(user_id: str):
    """
    Get the profile picture URL for a user.
    """
    try:
        # Get Cognito manager with tenant context
        cognito_manager = await get_cognito_manager(get_tenant_id())

        # Get user attributes from Cognito
        try:
            user = cognito_manager.get_user(user_id)
            picture_url = next(
                (attr['Value'] for attr in user['UserAttributes'] 
                if attr['Name'] == 'picture'),
                None
            )
            
            if not picture_url:
                return {"url": None}
                
            return {"url": picture_url}
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

    except Exception as e:
        print(f"Error in get_profile_picture: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving profile picture"
        )

@router.delete("/{user_id}/profile-picture", response_description="Delete profile picture")
async def delete_profile_picture(
    user_id: str,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    """
    Delete a user's profile picture.
    This will remove the image from S3 and clear the URL from Cognito.
    """
    try:
        # Verify user has permission (either same user or admin)
        if current_user.get("cognito:username") != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to delete this user's profile picture"
            )

        # Get required handlers
        s3_handler = await get_s3_handler()
        cognito_manager = await get_cognito_manager(get_tenant_id())
        
        # Get current picture URL from Cognito
        try:
            user = cognito_manager.get_user(user_id)
            picture_url = next(
                (attr['Value'] for attr in user['UserAttributes'] 
                if attr['Name'] == 'picture'),
                None
            )
            
            if not picture_url:
                return {"message": "No profile picture to delete"}
                
            # Extract S3 key from URL
            key = picture_url.split('/')[-1]
            
            # Delete from S3
            success = s3_handler._delete_old_picture(s3_handler.profile_pictures_bucket, key)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to delete profile picture"
                )

            # Clear URL in Cognito
            user_response = cognito_manager.update_user_attributes(
                identifier=user_id,
                attributes={"picture": ""},
                is_custom=False
            )
            
            attributes = {
                "Picture": ""
            }
            await db.upsert_user_db(user_id,attributes)
            return {"message": "Profile picture deleted successfully"}
            
        except Exception as e:
            print(f"Error in delete_profile_picture: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting profile picture"
        )
        
@router.get("/organizations/{organization_id}/planCosts")
async def get_org_plan_costs(organization_id):
    """
    Get organization details with project titles and user names.
    """
    try:
        # Map settings.MONGO_DB_NAME to full database name
        db_mapping = {
            "pre_prod": "pre_prod_kaviaroot", 
            "develop": "develop_kaviaroot",
            "qa": "qa_kaviaroot",
            "experimental": "experimental_kaviaroot"
        }

        db_prefix = settings.MONGO_DB_NAME
        if db_prefix not in db_mapping:
            return JSONResponse(
                status_code=400,
                content={"detail": f"Invalid database prefix in settings. Must be one of: {', '.join(db_mapping.keys())}"}
            )

        full_db_name = db_mapping[db_prefix]
        collection_name = "llm_costs"
        
        # Get MongoDB connection
        mongo_db = get_mongo_db(
            db_name=full_db_name,
            collection_name=collection_name
        )
        
        # Get organization document
        document = await mongo_db.get_one(
            filter={"organization_id": organization_id},
            db=mongo_db.db
        )
        new_document = {
            "organization_id": document['organization_id'],
            'organization_cost': document['organization_cost'],
            'organization_name': document['organization_name'],
            'users': []
        }
        user_data = document['users']
        for user in user_data:
            new_user_data = {
                'user_id': user['user_id'],
                'type': user['type'],
                'user_cost': user.get('user_cost', 0),
                'projects': []
            }
            project_data = user["projects"]
            for project in project_data:
                new_project_data = {
                    'project_id': project['project_id'],
                    'project_cost': project['project_cost'],
                    'packages': []
                }
                packageData = []
                if 'agents' in project:
                    for agent in project['agents']:
                        packageName = find_agent_plan(agent['agent_name'])
                        planExists = False
                        for i in range(0, len(packageData)):
                            if packageData[i]['package_name'] == packageName:
                                planExists = True
                                packageData[i]["package_cost"] = f'${float(packageData[i]["package_cost"].strip("$")) + float(agent["total_cost"].strip("$")):.6f}'
                                packageData[i]["agents"].append(agent)
                                break

                        if not planExists:
                            newPackageData = {
                                "package_name": packageName,
                                "package_cost": agent["total_cost"],
                                "agents": [agent]
                            }
                            packageData.append(newPackageData)

                    new_project_data['packages']=(packageData)
                new_user_data["projects"].append(new_project_data) 
        
            new_document["users"].append(new_user_data)

        return(new_document)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"detail": f"Database error: {str(e)}"}
        )


@router.get('/myplan/', response_description="Get plan details")
async def get_current_user_details(current_user=Depends(get_current_user)):
    try:
        print("current_user", current_user)
        user_id = get_user_id_from_token(current_user)
        print("user_id", user_id)
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="active_subscriptions")
        user = await mongo_db.get_one(filter={"user_id": user_id}, db=mongo_db.db)
        if user:
            price_id = user.get("price_id")
            return {
                "price_id":price_id,
                "user_id":user_id,
            }
        else:
            return {
                "price_id":"free_plan",
                "user_id":user_id,
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.post('/accept-terms', response_description="Accept terms and conditions")
async def accept_terms(current_user=Depends(get_current_user)):
    try:
        # Use utility function to get correct user ID
        user_id = get_user_id_from_token(current_user)
        user = await User.get(user_id)
        if user:
            await User.update(user_id, {"has_accepted_terms": True, "accepted_terms_at": datetime.now()})
            return {"message": "Terms and conditions accepted successfully"}
        else:
            raise HTTPException(status_code=404, detail="User not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
