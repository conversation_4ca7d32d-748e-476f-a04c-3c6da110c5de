import asyncio
import json
import logging
from typing import Dict, Any, List, Optional

# FastAPI imports
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse, StreamingResponse

# App imports
from app.connection.establish_db_connection import get_mongo_db, get_node_db, NodeDB
from app.core.constants import REPOSITORIES_COLLECTION
from app.knowledge.redis_kg import getRedisKnowledge
from app.models.manifest_model import ManifestModel
from app.routes.code_query import generate_random_prefix
from app.utils.auth_utils import get_current_user
from app.utils.code_generation_utils import json_to_yaml, yaml_to_json
from app.utils.datetime_utils import generate_timestamp
from app.utils.kg_inspect.kg_tool import KgTools
from app.utils.logs_utils import get_path
from app.utils.manifest_utils import get_project_manifest_for_generation , deep_merge_dicts, has_meaningful_changes, save_manifest_to_history_async, parse_env_from_container_properties
from app.utils.prompts import manifest_prompt
from app.utils.rjsf_schemas import generate_rjsf_schemas, manifest_dict_to_form_data,  form_data_to_project_schema, dataclass_to_dict_preserve_env, manifest_dict_to_form_data, convert_env_array_to_dict
# Code generation imports
from code_generation_core_agent.agents.utilities import ModelNameSelector

# LLM wrapper imports
from llm_wrapper.core.llm_interface import LLMInterface
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
import copy
import litellm
litellm.enable_json_schema_validation = True

logger = logging.FileHandler("logs/manifest_route.log")
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger.setFormatter(formatter)
logging.getLogger("app.routes.manifest_route").addHandler(logger)
logging.getLogger("app.routes.manifest_route").setLevel(logging.INFO)
manifest_logger = logging.getLogger("app.routes.manifest_route")
manifest_logger.info("++++ Manifest route initialized +++")

_SHOW_NAME = "manifest"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)


async def get_project_form_data(project_id: int, container_ids: Optional[List[int]] = None) -> Dict[str, Any]:
    """Get project form data using get_project_manifest_for_generation"""
    try:
        # Get database connection
        node_db = get_node_db()
        
        # First, get the project node to extract container IDs and existing manifest
        project_node = await node_db.get_node_by_id(project_id)
        project_properties = project_node.get("properties", {})
        
        # Check if there's already a manifest in the project properties
        existing_manifest = project_properties.get("Manifest")
        if existing_manifest:
            try:
                # Use existing manifest if available
                manifest_dict = json.loads(existing_manifest)
                print(f"Using existing manifest from properties")
            except json.JSONDecodeError:
                print(f"Failed to parse existing manifest, will generate new one")
                existing_manifest = None
        
        if not existing_manifest:
            # Extract container IDs from repositories or other sources
            if container_ids is None:
                container_ids = []
                
                # Try to extract container IDs from repositories
                repositories_str = project_properties.get("repositories", "{}")
                try:
                    repositories = json.loads(repositories_str)
                    container_ids = [int(container_id) for container_id in repositories.keys()]
                    print(f"Extracted container IDs from repositories: {container_ids}")
                except (json.JSONDecodeError, ValueError) as e:
                    print(f"Could not extract container IDs from repositories: {e}")
                
                # If still no container IDs, try to get them from child nodes
                if not container_ids:
                    try:
                        child_nodes = await node_db.get_child_nodes(project_id, node_type="container")
                        container_ids = [node["id"] for node in child_nodes if node.get("type") == "container"]
                        print(f"Extracted container IDs from child nodes: {container_ids}")
                    except Exception as e:
                        print(f"Could not get child nodes: {e}")
            
            # Get project manifest with container IDs
            manifest_json = await get_project_manifest_for_generation(
                project_id=project_id,
                container_ids=container_ids,
                node_db=node_db
            )
            
            # Parse JSON string to dictionary
            manifest_dict = json.loads(manifest_json)
        
        # Convert to form data format using the fixed function
        form_data = manifest_dict_to_form_data(manifest_dict)
        
        # Final fallback: if still no containers, try to extract from TempData or other fields
        if not form_data.get('containers'):
            print("No containers found, trying fallback extraction from project properties")
            
            # Try TempData field
            temp_data_str = project_properties.get("TempData", "{}")
            try:
                temp_data = json.loads(temp_data_str)
                if temp_data.get("containers"):
                    print(f"Found containers in TempData: {len(temp_data['containers'])}")
                    form_data = manifest_dict_to_form_data(temp_data)
            except json.JSONDecodeError:
                print("Failed to parse TempData")
        
        return form_data
        
    except Exception as e:
        print(f"Error in get_project_form_data: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # Final fallback: try to extract data directly from project properties
        try:
            node_db = get_node_db()
            project_node = await node_db.get_node_by_id(project_id)
            project_properties = project_node.get("properties", {})
            
            # Try multiple sources for manifest data
            manifest_sources = [
                project_properties.get("Manifest"),
                project_properties.get("TempData"),
                project_properties.get("manifest")
            ]
            
            for source in manifest_sources:
                if source:
                    try:
                        manifest_data = json.loads(source)
                        if manifest_data:
                            print(f"Fallback: Found manifest data in project properties")
                            return manifest_dict_to_form_data(manifest_data)
                    except json.JSONDecodeError:
                        continue
            
            # If still no data, return basic project info at least
            return {
                "overview": {
                    "project_name": project_properties.get("Title", ""),
                    "description": project_properties.get("Description", ""),
                    "third_party_services": [],
                    "env": {}  # Add empty env object
                },
                "containers": []
            }
            
        except Exception as fallback_error:
            print(f"Fallback also failed: {fallback_error}")
            return {
                "overview": {
                    "project_name": "",
                    "description": "",
                    "third_party_services": [],
                    "env": {}  # Add empty env object
                },
                "containers": []
            }


@router.get("/form-config/{project_id}/")
async def get_manifest_form_config(
    project_id: int, 
    container_ids: Optional[List[int]] = None
):
    """Get React JSON Schema Form configuration for manifest editor"""
    try:
        # Generate RJSF schemas
        schemas = generate_rjsf_schemas()
        
        # Get real project form data instead of sample data
        form_data = await get_project_form_data(project_id, container_ids)
        
        return {
            "jsonSchema": schemas["jsonSchema"],
            "uiSchema": schemas["uiSchema"],
            "formData": form_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating form config: {str(e)}")

@router.post("/form-data/{project_id}/")
async def save_manifest_from_form(
    project_id: int,
    form_data: Dict[str, Any],
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Save manifest from RJSF form data, performing a deep, selective update.
    If update_container_nodes=True, also update individual container nodes.
    """
    try:
        # Extract the update_container_nodes flag from form data
        update_container_nodes = form_data.pop('update_container_nodes', False)
        
        # Convert form data to ProjectSchema and then to a fully serialized dict
        project_schema = form_data_to_project_schema(form_data)
        new_manifest = dataclass_to_dict_preserve_env(project_schema)
        
        if isinstance(new_manifest, str):
            try:
                new_manifest = json.loads(new_manifest)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid manifest data format")

        # Fetch the existing project node and manifest
        project = await node_db.get_node_by_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail=f"Project with ID {project_id} not found")

        project_details = project.get("properties", {})
        
        existing_manifest = project_details.get("Manifest", "{}")
        
        try:
            existing_manifest = json.loads(existing_manifest)
        except: 
            existing_manifest = {}

        # Detect changes
        if not has_meaningful_changes(existing_manifest, new_manifest):
            return {
                "message": "No changes detected, manifest not updated",
                "project_id": project_id,
                "containers_count": len(new_manifest.get("containers", []))
            }

        # Save current manifest to history BEFORE updating
        merged_manifest = deep_merge_dicts(existing_manifest, new_manifest)
        manifest_history = await save_manifest_to_history_async(project_id, existing_manifest, node_db)
        
        # Update individual container nodes if requested
        containers_updated = 0

        if update_container_nodes:
            try:    
                containers_updated = await _update_container_nodes_from_manifest(
                    project_id, new_manifest, node_db
                )
            except Exception as e:
                manifest_logger.error(f"Error updating container nodes: {str(e)}")
                import traceback
                traceback.print_exc()
                pass
    
        # Save updated manifest and history
        await node_db.update_node_by_id(
            project_id,
            {
                "Manifest": json.dumps(merged_manifest, indent=2),
                "manifest_history": json.dumps(manifest_history)
            }
        )

        response_data = {
            "message": "Manifest saved and updated successfully",
            "project_id": project_id,
            "containers_count": len(merged_manifest.get("containers", [])),
            "updated_fields": list(new_manifest.keys()),
            "history_versions": len(manifest_history)
        }
        
        # Include container update info if applicable
        if update_container_nodes:
            response_data.update({
                "container_nodes_updated": containers_updated,
                "plan_phase_update": True
            })

        return response_data
        
    except Exception as e:
        manifest_logger.error(f"Error saving manifest: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error saving manifest: {str(e)}")


async def _update_container_nodes_from_manifest(
    project_id: int,
    manifest_dict: Dict[str, Any],
    node_db: NodeDB
) -> int:
    """
    Update individual container nodes with properties from manifest.
    Returns the number of containers updated.
    """
    try:
        containers_updated = 0
        containers_data = manifest_dict.get("containers", [])

        manifest_logger.info("manifest_dict: " + json.dumps(manifest_dict))
        
        if not containers_data:
            manifest_logger.info(f"No containers found in manifest for project {project_id}")
            return 0
        
        # Get all container nodes for this project
        container_nodes = await node_db.get_nodes_connected_by_multiple_hops(
            project_id, "HAS_CHILD", "Container", 5
        )
        
        # Create a mapping of container names to node IDs
        container_name_to_node = {}
        for node in container_nodes:
            node_props = node.get("properties", {})
            container_name = node_props.get("Title", "").replace(' ', '').replace('-', '_')
            if container_name:
                container_name_to_node[container_name] = node.get("id")
        
        manifest_logger.info(f"Found {len(container_nodes)} container nodes for project {project_id}")
        manifest_logger.info(f"Container name mapping: {container_name_to_node}")
        
        # Update each container node with manifest data
        for container_manifest in containers_data:
            container_name = container_manifest.get("container_name", "")
            
            if not container_name:
                manifest_logger.warning(f"Container manifest missing container_name: {container_manifest}")
                continue
                
            # Find matching node ID
            node_id = container_name_to_node.get(container_name)
            if not node_id:
                manifest_logger.warning(f"No container node found for manifest container: {container_name}")
                continue
            
            # Prepare properties to update
            properties_to_update = {}
            
            # Map manifest fields to node properties
            field_mapping = {
                "description": "Description",
                "framework": "framework",
                "port": "Port",
                "buildCommand": "buildCommand",
                "startCommand": "startCommand",
                "installCommand": "installCommand",
                "lintCommand": "lintCommand",
                "working_dir": "working_dir",
                "lintConfig": "lintConfig",
                "routes": "routes",
                "apiSpec": "apiSpec",
                "auth": "auth",
                "schema": "schema",
                "migrations": "migrations",
                "seed": "seed",
                "env": "env",
                "private": "private"
            }
            
            for manifest_field, node_property in field_mapping.items():
                if manifest_field in container_manifest:
                    value = container_manifest[manifest_field]
                    
                    # Serialize complex objects as JSON strings
                    if isinstance(value, (dict, list)) and value:
                        properties_to_update[node_property] = json.dumps(value)
                    elif value and str(value).strip():  # Only update non-empty values
                        properties_to_update[node_property] = str(value)
            
            # Special handling for container_details
            if "container_details" in container_manifest:
                container_details = container_manifest["container_details"]
                if isinstance(container_details, dict):
                    # Extract features if present
                    if "features" in container_details and container_details["features"]:
                        features = container_details["features"]
                        if isinstance(features, list) and features:
                            # Use the first non-empty feature as UserInteractions
                            for feature in features:
                                if feature and str(feature).strip():
                                    properties_to_update["UserInteractions"] = str(feature)
                                    break
                    
                    # Store full container_details as JSON
                    properties_to_update["container_details"] = json.dumps(container_details)
            
            # Update the container node if we have properties to update
            if properties_to_update:
                try:
                    await node_db.update_node_by_id(node_id, properties_to_update)
                    containers_updated += 1
                    manifest_logger.info(f"Updated container node {node_id} ({container_name}) with {len(properties_to_update)} properties: {list(properties_to_update.keys())}")
                except Exception as e:
                    manifest_logger.error(f"Failed to update container node {node_id} ({container_name}): {str(e)}")
            else:
                manifest_logger.info(f"No properties to update for container {container_name}")
        
        manifest_logger.info(f"Successfully updated {containers_updated} container nodes from manifest")
        return containers_updated
        
    except Exception as e:
        manifest_logger.error(f"Error updating container nodes from manifest: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0


@router.get("/history/{project_id}/")
async def get_manifest_history(
    project_id: int,
    limit: Optional[int] = 10,
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Get manifest history for a project.
    
    Args:
        project_id: Project ID
        limit: Maximum number of history entries to return (default 10, max 25)
    
    Returns:
        List of historical manifest versions with timestamps
    """
    try:
        # Validate limit
        if limit and limit > 25:
            limit = 25
        elif limit and limit < 1:
            limit = 1
        else:
            limit = limit or 10
        
        # Get project
        project = await node_db.get_node_by_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail=f"Project with ID {project_id} not found")
        
        project_properties = project.get("properties", {})
        history_str = project_properties.get("manifest_history", "[]")
        
        # Parse history from JSON string
        try:
            history = json.loads(history_str) if isinstance(history_str, str) else history_str
        except (json.JSONDecodeError, TypeError):
            history = []
        
        # Parse manifest JSON strings back to objects in each history entry
        parsed_history = []
        for entry in history:
            try:
                parsed_entry = entry.copy()
                if isinstance(entry.get("manifest"), str):
                    parsed_entry["manifest"] = json.loads(entry["manifest"])
                parsed_history.append(parsed_entry)
            except (json.JSONDecodeError, TypeError):
                # Skip corrupted entries
                continue
        
        # Return limited history
        limited_history = parsed_history[:limit] if parsed_history else []
        
        return {
            "project_id": project_id,
            "total_versions": len(parsed_history),
            "returned_versions": len(limited_history),
            "history": limited_history
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error retrieving manifest history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving manifest history: {str(e)}")

@router.get("/history/{project_id}/version/{version_index}/")
async def get_manifest_version(
    project_id: int,
    version_index: int,
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Get a specific version of manifest from history.
    
    Args:
        project_id: Project ID
        version_index: Index in history (0 = most recent, 1 = second most recent, etc.)
    
    Returns:
        Specific historical manifest version
    """
    try:
        # Get project
        project = await node_db.get_node_by_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail=f"Project with ID {project_id} not found")
        
        project_properties = project.get("properties", {})
        history_str = project_properties.get("manifest_history", "[]")
        
        # Parse history from JSON string
        try:
            history = json.loads(history_str) if isinstance(history_str, str) else history_str
        except (json.JSONDecodeError, TypeError):
            history = []
        
        # Validate version index
        if version_index < 0 or version_index >= len(history):
            raise HTTPException(
                status_code=404, 
                detail=f"Version index {version_index} not found. Available versions: 0-{len(history)-1}"
            )
        
        historical_version = history[version_index].copy()
        
        # Parse manifest JSON string back to object
        try:
            if isinstance(historical_version.get("manifest"), str):
                historical_version["manifest"] = json.loads(historical_version["manifest"])
        except (json.JSONDecodeError, TypeError):
            pass
        
        return {
            "project_id": project_id,
            "version_index": version_index,
            "total_versions": len(history),
            "version_data": historical_version
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error retrieving manifest version: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving manifest version: {str(e)}")


@router.post("/restore/{project_id}/version/{version_index}/")
async def restore_manifest_version(
    project_id: int,
    version_index: int,
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Restore a specific version of manifest from history.
    This will save the current version to history before restoring.
    
    Args:
        project_id: Project ID
        version_index: Index in history (0 = most recent, 1 = second most recent, etc.)
    
    Returns:
        Success message with restored version info
    """
    try:
        # Get project
        project = await node_db.get_node_by_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail=f"Project with ID {project_id} not found")
        
        project_properties = project.get("properties", {})
        history_str = project_properties.get("manifest_history", "[]")
        
        # Parse history from JSON string
        try:
            history = json.loads(history_str) if isinstance(history_str, str) else history_str
        except (json.JSONDecodeError, TypeError):
            history = []
        
        # Validate version index
        if version_index < 0 or version_index >= len(history):
            raise HTTPException(
                status_code=404, 
                detail=f"Version index {version_index} not found. Available versions: 0-{len(history)-1}"
            )
        
        # Get current manifest to save to history
        current_manifest_str = project_properties.get("Manifest", "{}")
        try:
            current_manifest = json.loads(current_manifest_str) if current_manifest_str else {}
        except json.JSONDecodeError:
            current_manifest = yaml_to_json(current_manifest_str)
        
        # Save current manifest to history
        updated_history = await save_manifest_to_history_async(project_id, current_manifest, node_db)
        
        # Get the version to restore
        version_to_restore = history[version_index]
        
        # Parse manifest from JSON string if needed
        if isinstance(version_to_restore.get("manifest"), str):
            restored_manifest = json.loads(version_to_restore["manifest"])
        else:
            restored_manifest = version_to_restore["manifest"]
        
        # Update project with restored manifest and updated history
        await node_db.update_node_by_id(
            project_id,
            {
                "Manifest": json.dumps(restored_manifest, indent=2),
                "manifest_history": json.dumps(updated_history)  # Serialize history as JSON string
            }
        )
        
        return {
            "message": "Manifest version restored successfully",
            "project_id": project_id,
            "restored_version_index": version_index,
            "restored_version_timestamp": version_to_restore["timestamp"],
            "containers_count": len(restored_manifest.get("containers", [])),
            "history_versions": len(updated_history)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error restoring manifest version: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error restoring manifest version: {str(e)}")
    
@router.post("/generate_manifest_json/{project_id}/")
async def generate_manifest_json(
    project_id: int,
    regenerate: bool = Query(False, description="Force regenerate even if manifest exists"),
    include_form_config: bool = Query(True, description="Include RJSF form configuration"),
    current_user=Depends(get_current_user)
):
    """
    Generate project manifest and return as JSON with RJSF form configuration.
    Falls back to plan-based generation if repository not found.
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        user_id = current_user.get("cognito:username")
        node_db = get_node_db()
        
        # Check existing manifest first (unless regenerate requested)
        if not regenerate:
            try:
                project = await node_db.get_node_by_id(project_id)
                if project:
                    existing_manifest_str = project.get("properties", {}).get("Manifest", "")
                    
                    if existing_manifest_str:
                        existing_manifest = await _parse_existing_manifest(existing_manifest_str)
                        if existing_manifest:
                            return await _build_manifest_response(
                                project_id, existing_manifest, include_form_config, 
                                source="plan", generation_time=0
                            )
            except Exception as e:
                manifest_logger.warning(f"Could not retrieve existing manifest: {e}")
        
        # Try repository-based generation first
        try:
            return await _generate_from_repository(
                project_id, user_id, node_db, include_form_config, start_time
            )
        except RepositoryNotFoundError:
            manifest_logger.info(f"Repository not found for project {project_id}, falling back to plan-based generation")
            
            # Fallback to plan-based generation
            return await _generate_from_plan(
                project_id, user_id, node_db, include_form_config, start_time
            )
            
    except HTTPException:
        raise
    except Exception as e:
        manifest_logger.error(f"Error generating manifest: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Failed to generate manifest: {str(e)}",
                "project_id": project_id
            }
        )


class RepositoryNotFoundError(Exception):
    """Custom exception for repository not found"""
    pass


async def _parse_existing_manifest(manifest_str: str) -> Optional[Dict[str, Any]]:
    """Parse existing manifest from JSON or YAML string"""
    try:
        return json.loads(manifest_str)
    except json.JSONDecodeError:
        try:
            return yaml_to_json(manifest_str)
        except:
            return None


async def _build_manifest_response(
    project_id: int, 
    manifest_dict: Dict[str, Any], 
    include_form_config: bool,
    source: str = "kg_tools",
    generation_time: int = 0
) -> JSONResponse:
    """Build standardized manifest response"""
    form_data = manifest_dict_to_form_data(manifest_dict)
    
    response_data = {
        "success": True,
        "project_id": project_id,
        "formData": form_data,
        "manifest": manifest_dict,
        "manifest_yaml": json_to_yaml(json.dumps(manifest_dict)),
        "source": source,
        "generated_at": generate_timestamp(),
        "generation_time_ms": generation_time
    }
    
    if include_form_config:
        schemas = generate_rjsf_schemas()
        response_data.update({
            "jsonSchema": schemas["jsonSchema"],
            "uiSchema": schemas["uiSchema"]
        })
    
    return JSONResponse(status_code=200, content=response_data)


async def _generate_from_repository(
    project_id: int, 
    user_id: str, 
    node_db: NodeDB, 
    include_form_config: bool, 
    start_time: float
) -> JSONResponse:
    """Generate manifest from repository data"""
    mongo_db = get_mongo_db().db[REPOSITORIES_COLLECTION]
    repository = mongo_db.find_one({"project_id": project_id})
    
    if not repository:
        raise RepositoryNotFoundError(f"Repository not found for project {project_id}")
    
    # Extract build_ids
    build_ids = []
    repositories = repository.get('repositories', [])
    
    for repo in repositories:
        for branch in repo.get('branches', []):
            if build_id := branch.get('builds', {}).get('build_id'):
                build_ids.append(build_id)
    
    if not build_ids:
        raise RepositoryNotFoundError("No build IDs found in repository")
    
    # Generate session_id and get knowledge
    session_id_prefix = f"manifest-{generate_random_prefix()}"
    sorted_build_ids = sorted(build_ids)
    build_ids_string = "-".join(map(str, sorted_build_ids))
    session_id = f"{session_id_prefix}-{build_ids_string}"
    
    knowledge_session = getRedisKnowledge(id=sorted_build_ids, verbose=True)
    if not knowledge_session:
        raise RepositoryNotFoundError("No knowledge session found")
    
    # Setup tools and generate
    kg_tool = _setup_kg_tools(repository, session_id)
    additional_prompt=None
    #pass in project_node and systemc_context_with_containers 
    try:
        project_node = await node_db.get_node_by_id(project_id)
        project_repositories = project_node.get("properties", {}).get("repositories", {})
        if isinstance(repositories, str):
            try:
                repositories = json.loads(repositories)
            except json.JSONDecodeError:
                manifest_logger.error(f"Invalid repositories JSON for project {project_id}")
                raise HTTPException(status_code=400, detail="Invalid repositories format")
        containers =  await node_db.get_nodes_connected_by_multiple_hops(project_id, "HAS_CHILD", "Container", 5)
        containers = [container for container in containers if container.get("properties", {}).get("ContainerType","").casefold() != "external"]
        if containers and project_repositories:
            additional_prompt = '''
                If the project has containers, container_name field should be the name of the container.
                Project Information: {project_node}
                Repositories: {repositories}
                Containers: {containers}

    '''
            additional_prompt = additional_prompt.format(
                project_node=json.dumps(project_node.get("properties", {}), indent=2),
                repositories=json.dumps(project_repositories, indent=2),
                containers=json.dumps([container for container in containers], indent=2)
            )
            manifest_logger.info(additional_prompt)
            
    except Exception as e:
        manifest_logger.error(f"Error retrieving project node: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving project node: {str(e)}")
    
    manifest_dict = await _generate_manifest(
        project_id, user_id, manifest_prompt, repositories, kg_tool, "repository", additional_prompt
    )
    
    
    generation_time = int((asyncio.get_event_loop().time() - start_time) * 1000)
    response = await _build_manifest_response(
        project_id, manifest_dict, include_form_config, "kg_tools", generation_time
    )
    
    # Add build_ids to response
    response_content = json.loads(response.body)
    response_content["build_ids"] = sorted_build_ids
    return JSONResponse(status_code=200, content=response_content)


async def _generate_from_plan(
    project_id: int, 
    user_id: str, 
    node_db: NodeDB, 
    include_form_config: bool, 
    start_time: float
) -> JSONResponse:
    """Generate manifest from project plan using template approach from batch_utils"""
    from app.utils.manifest_utils import get_container_type, _get_container_dependancy, _def_interfaces
    from code_generation_core_agent.project_schemas import ContainerType
    from app.core.Settings import settings
    
    try:
        # Get project and containers using batch_utils approach
        project = await node_db.get_node_by_label_id(project_id, "Project")
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_details = project.get("properties", {})
        repositories = project_details.get("repositories", {})

        if isinstance(repositories, str):
            try:
                repositories = json.loads(repositories)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid repositories format")
        
        # Get containers using same logic as batch_utils
        containers = await node_db.get_nodes_connected_by_multiple_hops(project_id, "HAS_CHILD", "Container", 5)
        containers = [container for container in containers if container.get("properties", {}).get("ContainerType","").casefold() != "external"]
        
        manifest_logger.info(f"Containers: {json.dumps(containers, indent=2)}")
        # Helper function for container port (from batch_utils)
        def get_container_port(container_props):
            container_type = get_container_type(container_props)
            if container_type == ContainerType.FRONTEND.value:
                return settings.FRONTEND_PORT
            elif container_type == ContainerType.BACKEND.value:
                return settings.BACKEND_PORT
            elif container_type == ContainerType.DATABASE.value:
                return settings.DATABASE_PORT
            elif container_type == ContainerType.MOBILE.value:
                return None
            return settings.FRONTEND_PORT
        
        # Build containers using batch_utils template
        manifest_containers = []
        missing_fields_containers = []
        
        for container in containers:
            container_id = str(container.get("id"))
            container_details = container.get("properties", {})
            container_details["container_id"] = container_id
            repository_details = repositories.get(container_id, {})
            
            container_name = container_details.get("Title", "").replace(' ', '').replace('-', '_')
            container_type = get_container_type(container_details)
            workspace = repository_details.get("repositoryName", "")
            container_root = ""
            if workspace:
                container_root = f"{workspace}/{container_name}"
            # Create container using exact batch_utils template
            container_manifest = {
                "container_name": container_name,
                "description": container_details.get("Description", ""),
                "interfaces": _def_interfaces(container_type),
                "container_type": container_type,
                "dependent_containers": _get_container_dependancy(container_type, container_name),
                "workspace": workspace,
                "container_root": container_root,
                "port": get_container_port(container_details),
                "framework": container_details.get("framework", ""),
                "type": container_details.get("type", ""),
                "buildCommand": container_details.get("buildCommand", ""),
                "startCommand": container_details.get("startCommand", ""),
                "installCommand": container_details.get("installCommand", ""),
                "lintCommand": container_details.get("lintCommand", ""),
                "working_dir": container_details.get("working_dir", ""),
                "container_details": {'features': [container_details.get("UserInteractions", "")]},
                "lintConfig": container_details.get("lintConfig", ""),
                "routes": container_details.get("routes", []),
                "apiSpec": container_details.get("apiSpec", ""),
                "auth": container_details.get("auth"),
                "schema": container_details.get("schema", ""),
                "migrations": container_details.get("migrations", ""),
                "seed": container_details.get("seed", ""),
                "env": parse_env_from_container_properties(container_details.get("env", {})),
                "private": container_details.get("private", {})
            }
            
            # Remove port for mobile containers
            if container_manifest['container_type'] == 'mobile':
                container_manifest.pop('port', None)
            
            # Identify missing critical fields for potential LLM fill
            missing_fields = {}
            if not container_manifest["framework"]:
                missing_fields["framework"] = True
            if not container_manifest["buildCommand"]:
                missing_fields["buildCommand"] = True  
            if not container_manifest["startCommand"]:
                missing_fields["startCommand"] = True
            if not container_manifest["installCommand"]:
                missing_fields["installCommand"] = True
                
            if missing_fields:
                missing_fields_containers.append({
                    "container": container_manifest,
                    "missing_fields": missing_fields,
                    "container_details": container_details
                })
            
            manifest_containers.append(container_manifest)
        
        # Create manifest using template approach
        manifest_dict = {
            "overview": {
                "project_name": project_details.get("Title", ""),
                "description": project_details.get("Description", ""),
                "third_party_services": []
            },
            "containers": manifest_containers
        }
        
        # Optionally fill missing critical fields with LLM (lightweight)
        if missing_fields_containers:
            manifest_dict = await _fill_missing_fields_with_llm(
                project_id, user_id, manifest_dict, missing_fields_containers
            )
        
        
        generation_time = int((asyncio.get_event_loop().time() - start_time) * 1000)
        return await _build_manifest_response(
            project_id, manifest_dict, include_form_config, "plan", generation_time
        )
        
    except Exception as e:
        manifest_logger.error(f"Error in template-based plan generation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Template generation failed: {str(e)}")


async def _fill_missing_fields_with_llm(
    project_id: int,
    user_id: str, 
    manifest_dict: Dict[str, Any],
    missing_fields_containers: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Use LLM to fill only missing critical fields"""
    try:
        missing_fields_prompt = """Fill missing fields for these containers based on their type and description:
        
"""
        
        for item in missing_fields_containers:
            container = item["container"]
            missing = item["missing_fields"]
            
            missing_fields_prompt += f"""
Container: {container['container_name']} ({container['container_type']})
Description: {container['description']}
Missing fields: {list(missing.keys())}

"""
        
        missing_fields_prompt += """
Provide only the missing values in JSON format. Use these guidelines:
-Do not combine commands of backend and frontend. If the container is frontend, provide only frontend commands for the selected framework.
- framework: Primary tech stack (react, vue, fastapi, express, etc.)
- buildCommand: Build/compile command 
- startCommand: Development server command
- installCommand: Dependency installation command

Return format: {"container_name": {"field": "value"}}
"""
        
        llm = LLMInterface(
            str(get_path()),
            'knowledge', 
            user_id,
            int(project_id),
            'manifest_missing_fields'
        )
        
        model_selector = ModelNameSelector("gpt-4.1")
        selected_model = model_selector.get_selected_model()
        
        messages = [
            {"role": "system", "content": "You are a manifest completion expert. Fill missing fields concisely." + manifest_prompt},
            {"role": "user", "content": missing_fields_prompt}
        ]
        manifest_logger.info(messages)
        
        response = await llm.llm_interaction_wrapper(
            messages=messages,
            user_prompt=None,
            system_prompt=None,
            response_format={"type":"json_object"},
            model=selected_model,
            stream=False
        )
        manifest_logger.info(f"LLM response for missing fields: {response}")
        
        # Parse and update manifest
        try:
            # Handle both dict and string responses
            if isinstance(response, dict):
                filled_fields = response
            else:
                filled_fields = json.loads(response)
            
            manifest_logger.info(f"LLM response for missing fields: {filled_fields}")
            
            for container in manifest_dict["containers"]:
                container_name = container["container_name"]
                if container_name in filled_fields:
                    updates = filled_fields[container_name]
                    for field, value in updates.items():
                        if field in container and not container[field]:
                            container[field] = value
                            manifest_logger.info(f"Updated {container_name}.{field} = {value}")
                            
        except (json.JSONDecodeError, TypeError) as e:
            manifest_logger.warning(f"Could not parse LLM response for missing fields: {e}")
        
        return manifest_dict
        
    except Exception as e:
        manifest_logger.error(f"Error filling missing fields: {str(e)}")
        return manifest_dict
    

def _setup_kg_tools(repository: Dict[str, Any], session_id: str) -> Any:
    """Setup knowledge graph tools"""
    general_registry = ToolRegistry()
    general_registry.register_tool("KgTools", KgTools)
    general_factory = DynamicToolFactory(general_registry)
    general_exec_agent = general_factory.create_dynamic_tool(["KgTools"])
    
    return general_exec_agent(
        base_path=repository.get('local_path', '/tmp'),
        logger=None,
        user_id=session_id
    )


async def _generate_manifest(
    project_id: int, 
    user_id: str, 
    system_prompt: str, 
    context_data: Dict[str, Any],
    kg_tool: Optional[Any] = None,
    generation_type: str = "repository",
    user_prompt: Optional[str] = None
) -> Dict[str, Any]:
    """Generate manifest using LLM with appropriate prompt and context"""
    llm = LLMInterface(
        str(get_path()),
        'knowledge',
        user_id,
        int(project_id),
        'manifest_generation'
    )
    
    model_selector = ModelNameSelector("gpt-4.1")
    selected_model = model_selector.get_selected_model()
    messages=[]
    # Build context message based on generation type
    if generation_type == "repository":
        context_msg = f"Repository metadata: {json.dumps(context_data, indent=2)}"
        messages = [
            {
                "role": "system",
                "content": manifest_prompt
            },
            {
                "role": "user",
                "content": f"Please analyze the codebase for project and generate a comprehensive project manifest. You need to fill every fields by making use of best of your knowledge and analytical skills that are not optional in your response. This is the repositories metadata overview which has the repository_name, repository_url: {json.dumps(context_data, indent=2)}"
            }
        ]
    else:  # plan-based generation
        messages = [
        {"role": "system", "content": system_prompt},
        {
            "role": "user", 
            "content": f"Please analyze the project information and generate a comprehensive manifest. Fill all non-optional fields using your analytical skills. {context_msg}"
        }
    ]
        context_msg = f"Project node: {json.dumps(context_data.get('project_node'), indent=2)}. System context: {json.dumps(context_data.get('system_context'), indent=2)}"
    


    if user_prompt:
        messages.append({"role": "user", "content": user_prompt})
    
    # Generate with or without tools based on availability
    if kg_tool:
        response = await llm.llm_interaction_wrapper(
            messages=messages,
            user_prompt=None,
            system_prompt=None,
            model=selected_model,
            stream=False,
            response_format=ManifestModel,
            function_schemas=kg_tool.function_schemas,
            function_executor=kg_tool.function_executor
        )
    else:
        response = await llm.llm_interaction_wrapper(
            messages=messages,
            user_prompt=None,
            system_prompt=None,
            model=selected_model,
            stream=False,
            response_format=ManifestModel
        )
    
    return json.loads(response)
