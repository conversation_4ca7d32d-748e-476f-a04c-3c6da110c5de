import logging
from typing import Dict, <PERSON>, Optional, Union, Tuple
from fastapi import APIRouter, HTTPException, Request, Depends , Header , status
from fastapi.responses import RedirectResponse
import requests
import os
import secrets
from dotenv import load_dotenv
from datetime import datetime
from urllib.parse import urlparse
import re
from app.connection.tenant_middleware import get_tenant_id, tenant_context
from app.routes.scm_route import SCMManager
from app.utils.oauth_utils import get_github_client_details, exchange_github_code_for_token, get_user_gitlab_repos, get_user_info, get_user_repos
from app.connection.establish_db_connection import connect_git_mongo_db  # Your MongoDB connection
from app.utils.auth_utils import get_current_user
from pydantic import BaseModel
import json
import httpx
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import settings
from fastapi.responses import JSONResponse
from app.utils.hash import decrypt_string

load_dotenv()
_SHOW_NAME = "oauth/github"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=["GitHub OAuth"],
    responses={404: {"description": "Not found"}},
)

# Load GitHub credentials
GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET, GITHUB_REDIRECT_URI = get_github_client_details()

# GitHub Login Route
@router.get("")
async def github_login():
    """
    Redirect the user to GitHub's OAuth page.
    """
    # state_string = secrets.token_urlsafe(32)
    github_auth_url = (
        f"https://github.com/login/oauth/authorize"
        f"?client_id={GITHUB_CLIENT_ID}"
        f"&scope=repo read:org admin:org"
        # f"&state={state_string}"
        f"&redirect_uri={GITHUB_REDIRECT_URI}"
    )  

    # print(mongo_result)
    # Log the URL to verify that the client ID and redirect URI are correctly set
    print(f"GitHub Auth URL: {github_auth_url}")
    
    return {"url": github_auth_url}  # Return the URL as part of a JSON response

@router.get("/callback")
async def github_callback(request: Request):
    """
    
    GitHub callback URL to exchange code for an access token.
    """
    code = request.query_params.get("code")
    userId = request.query_params.get("userId")
    tenant_id = request.query_params.get("tenantId")
    tenant_context.set(tenant_id)
    
    if not code:
        raise HTTPException(status_code=400, detail="Code not found")


    # Exchange the code for an access token
    access_token = exchange_github_code_for_token(code)
    if not access_token:
        raise HTTPException(status_code=400, detail="Access token not found")

    # Get the user information from GitHub
    user_info = get_user_info(access_token)
    if not user_info:
        raise HTTPException(status_code=400, detail="Unable to fetch user info from GitHub")

    # If the token is valid, set status to true
    status = "true"

    # Get MongoDB instance
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='users_github'
    )

    # Prepare user data
    user_data = {
        "github_id": user_info["id"],
        "username": user_info["login"],
        "userId" : userId,
        "status": status,
        "email": user_info.get("email"),
        "name": user_info.get("name"),
        "access_token": access_token,
        "avatar_url": user_info.get("avatar_url"),
        "profile_url": user_info.get("html_url"),
        "updated_at": datetime.utcnow(),
    }

    # Check if the user already exists
    existing_user = await mongo_handler.git_get_by(user_info["id"])

    if existing_user:
        # User exists, update their information
        update_result = await mongo_handler.git_update(user_info["id"], user_data)
        if update_result:
            print(f"User updated: {user_data['username']}")
            mongo_result = await mongo_handler.git_get_by(user_info["id"])  # Get the updated user data
        else:
            raise HTTPException(status_code=500, detail="Failed to update user in database")
    else:
        # User doesn't exist, create a new entry
        user_data["created_at"] = datetime.utcnow()
        mongo_result = await mongo_handler.git_create(user_data)
        if mongo_result:
            print(f"User created: {mongo_result['username']}")
        else:
            raise HTTPException(status_code=500, detail="Failed to create user in database")

    # Return response
    return {"Message": f" You can close this window - {userId , status}"}


class BranchResponse(BaseModel):
    name: str
    protected: bool
    commit: dict

class BranchRequest(BaseModel):
    user_id: str | None
    repo_name: str
    owner: str
    org: Optional[Union[str, bool]] = False
    limit: Optional[bool] = False

async def check_gitlab_branches(owner: str, repo_name: str) -> List[dict]:
    """
    Check GitLab for repository branches when GitHub returns 404.
    This function attempts to find the repository on GitLab.com.
    """
    try:
        # Try GitLab.com public API first
        gitlab_url = f"https://gitlab.com/api/v4/projects/{owner}%2F{repo_name}/repository/branches"

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(gitlab_url)

            if response.status_code == 200:
                gitlab_branches = response.json()
                # Convert GitLab branch format to GitHub-compatible format
                converted_branches = []
                for branch in gitlab_branches:
                    converted_branches.append({
                        "name": branch.get("name", ""),
                        "protected": branch.get("protected", False),
                        "commit": {
                            "sha": branch.get("commit", {}).get("id", ""),
                            "url": branch.get("commit", {}).get("web_url", "")
                        }
                    })
                return converted_branches
            elif response.status_code == 404:
                print(f"Repository {owner}/{repo_name} not found on GitLab either")
                return []
            else:
                print(f"GitLab API returned status code: {response.status_code}")
                return []

    except Exception as e:
        print(f"Error checking GitLab: {str(e)}")
        return []
    
@router.post("/repo-branches", response_model=List[BranchResponse])
async def get_branches(payload: BranchRequest):
    """
    Get branches of a specific repository.
    For org=True, treats the request as an organization account.
    For org=False (default), treats it as a normal user account.
    """
    try:
        # Initialize variables
        token = None
        
        # Get MongoDB connection
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='users_github'
        )
        
        # Handle token retrieval based on account type
        if payload.org:
            # Organization account - get token from tenant org
            scm = SCMManager()
            token = scm.get_token_from_tenant_org(payload.org)
        else:
            if(payload.user_id):
                # Normal user account - get token from user data
                user_data = await mongo_handler.git_get_by_user_id(payload.user_id)
                
                if not user_data:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="User not found, please login again"
                    )
            
                # Validate token
                token = user_data.get("access_token")
                if not token:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Access token not found"
                    )
            else:
                token = settings.GITHUB_ACCESS_TOKEN
        
        # Make GitHub API request with pagination handling
        url = f"https://api.github.com/repos/{payload.owner}/{payload.repo_name}/branches?per_page=100&page=1"
        headers = {
            "Authorization": f"Bearer {token}",
        }
        
        more_pages = True
        final_data = []
        
        while more_pages:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 404 or response.status_code == 401:
                    # Repository not found on GitHub, try GitLab as fallback
                    print(f"Repository {payload.owner}/{payload.repo_name} not found on GitHub (404), checking GitLab...")
                    print(token)
                    
                    gitlab_branches = await fetch_gitlab_branches(owner=payload.owner, repo_name=payload.repo_name,access_token=token)
                    if gitlab_branches:
                        print(f"Found {len(gitlab_branches)} branches on GitLab")
                        return gitlab_branches
                    else:
                        print("Repository not found on GitLab either")
                        final_data = []
                        break
                    
                response.raise_for_status()
                final_data.extend(response.json())
                
                # Check for pagination
                if 'link' in response.headers and 'rel="next"' in response.headers['link'] and not payload.limit:
                    next_links = [link for link in response.headers['link'].split(',') if 'rel="next"' in link]
                    if next_links:
                        angular_url = next_links[0].split(';')[0].strip()
                        url = angular_url[1:-1]  # Remove < and > characters
                else:
                    more_pages = False
        
        return final_data
        
    except httpx.HTTPStatusError as e:
        logging.error(f"GitHub API error: {str(e)}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"GitHub API error: {e.response.json()}"
        )
    except httpx.RequestError as e:
        logging.error(f"Network error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Unable to reach GitHub API"
        )
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    
@router.get("/repo-details")
async def get_repo_details(request: Request, current_user: dict = Depends(get_current_user)):
    owner = request.query_params.get("owner")
    repo_name = request.query_params.get("repo_name")
    github_token = settings.GITHUB_ACCESS_TOKEN #only using default access token becuase this endpoint is only for public repo. If private repo needs to access this api in the future, change the token retrieval using user_id

    url = f"https://api.github.com/repos/{owner}/{repo_name}"
    headers = {
        "Authorization": f"Bearer {github_token}",
    }

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, headers=headers)

            if response.status_code == 404:
                # Repository not found or no access
                return {}
            
            response.raise_for_status()
            return response.json()
        
    except httpx.HTTPStatusError as e:
        logging.error(f"GitHub API error: {str(e)}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"GitHub API error: {e.response.json()}"
        )
    except httpx.RequestError as e:
        logging.error(f"Network error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Unable to reach GitHub API"
        )
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    
class SingleBranchCheck(BaseModel):
    owner: str
    repo_name: str
    branch_name: str
    user_id: str | None

@router.post("/get-branch-details")
async def getBranchDetails(payload: SingleBranchCheck):
    print("Entered get branch details")
    github_token = settings.GITHUB_ACCESS_TOKEN
    url = f"https://api.github.com/repos/{payload.owner}/{payload.repo_name}/branches/{payload.branch_name}"
    headers = {
        "Authorization": f"Bearer {github_token}",
    }

    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 404:
                # Branch not found or no access
                return {}
        
        return response.json()
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

class CodeRequest(BaseModel):
    code: str

@router.get("/repositories")
async def github_repositories(
    request: Request, 
    current_user: dict = Depends(get_current_user)
):
    scm_id = request.query_params.get("scm_id")
    user_id = request.query_params.get("userId")
    token = None
    scm_type = None
    # If user_id not provided, get from current user
    if not user_id:
        user_id = current_user.get("cognito:username")

    # Get user data from MongoDB
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='users_github'
    )
    user_data = await mongo_handler.git_get_by_user_id(user_id)

    if not user_data and not scm_id:
        return JSONResponse(
            status_code=400,
            content={"detail": "Token not found, please login again"}
        )

    # If scm_id is provided, use it to get token
    if scm_id:
        decrypted_scm_id = decrypt_string(scm_id)
        scm_mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='scm_configurations'
        )
        scm_data = await scm_mongo_handler.git_get_by_scm_id(decrypted_scm_id)
        if scm_data:
            scm_type = scm_data['scm_type']
            token = scm_data['credentials']['access_token']

    # If no token from scm_id, use token from user_data
    if not token:
        token = user_data.get("access_token")

    if not token:
        return JSONResponse(
            status_code=400,
            content={"detail": "Access token not found for the user"}
        )

    if scm_type == 'github':
        repos = get_user_repos(token)
    elif scm_type == 'gitlab':
        repos = get_user_gitlab_repos(token)    
        return repos
    else:
        repos = get_user_repos(token)
    
    if not repos:
        return JSONResponse(
            status_code=400,
            content={"detail": "Error fetching repositories"}
        )

    transformed_repos = []
    for repo in repos:
        transformed_repo = {
            "id": repo.get("id"),
            "name": repo.get("name"),
            "description": repo.get("description"),
            "languages": [repo.get("language")] if repo.get("language") else [],
            "selected": False,
            "path": repo.get("html_url"),
            "lastUpdated": repo.get("updated_at"),
            "branch": repo.get("default_branch"),
            "visibility": "private" if repo["private"] else "public"
        }
        transformed_repos.append(transformed_repo)

    return transformed_repos


@router.get("/logout")
async def logout(request: Request,current_user: dict = Depends(get_current_user)):
    """
    Log the user out by clearing the session and redirecting to the home page or GitHub logout.
    """
    userId = current_user.get("cognito:username")
    # Clear the user's session or token
    request.session.clear()  # Clear all session data
    if userId:
        try:
            mongo_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='users_github'
            )
            delete_result = await mongo_handler.git_delete_by_userId(userId,'users_github' )
            
            if delete_result.deleted_count == 0:
                return JSONResponse(
                    status_code=404,
                    content={"detail": "User not found in database"}
                )
        except Exception as e:
            return JSONResponse(
                status_code=500,
                content={"detail": f"Error deleting user: {str(e)}"}
            )

    # Optionally redirect to GitHub's logout page (not required but a good practice)
    github_logout_url = "https://github.com/logout"
    
    # Redirecting to a specific URL after logging out
    return {"logouturl": github_logout_url}  # Return the URL as part of a JSON response


@router.get("/git_connected_status")
async def git_connected_status(user_id: str= None,
                            current_user: dict = Depends(get_current_user)):
    """
    Check if a user is connected to GitHub by their user_id.
    """
    if not user_id:
        user_id = current_user.get("cognito:username")
    # Get MongoDB instance
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='users_github'
    )

    try:
        # Fetch user data from the database using user_id
        user_data = await mongo_handler.git_get_by_user_id(user_id)

        print(user_data)

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error accessing database: {str(e)}"
        )

    if not user_data:
        # If the user does not exist in the database
        return JSONResponse(
            status_code=200,
            content={"git_connected": False, "message": "GitHub user not connected"}
        )

    # If the user exists, return the connection status
    return {
        "git_connected": True,
        "message": "GitHub user already connected",
        "status": user_data.get("status"),
        "username": user_data.get("username"),
    }


# ============================================================================
# NEW SIMPLE BRANCHES API
# ============================================================================

def parse_repository_url(url: str) -> Tuple[str, str, str]:
    """
    Parse a GitHub or GitLab repository URL to extract owner, repo_name, and platform.

    Args:
        url: Repository URL (e.g., https://github.com/owner/repo, https://gitlab.com/owner/repo)

    Returns:
        Tuple of (owner, repo_name, platform) where platform is 'github' or 'gitlab'

    Raises:
        ValueError: If the URL format is not supported
    """
    # Clean the URL - remove trailing slashes, .git extension, and tree/branch paths
    clean_url = url.strip().rstrip('/')

    # Remove .git extension if present
    if clean_url.endswith('.git'):
        clean_url = clean_url[:-4]

    # Remove tree/branch paths (e.g., /tree/main, /tree/master)
    tree_pattern = r'/tree/[^/]+.*$'
    clean_url = re.sub(tree_pattern, '', clean_url)

    # Parse the URL
    parsed = urlparse(clean_url)

    # Determine platform
    platform = None
    host = parsed.netloc.lower()
    path_for_detection = parsed.path.lower()
    if 'github.com' in host:
        platform = 'github'
    elif 'gitlab.com' in host:
        platform = 'gitlab'
    elif host.endswith('googlesource.com') or 'gerrit' in host or host.startswith('review.') or 'review' in host or re.search(r'/(?:a/)?(?:r/|plugins/gitiles/|admin/repos/)', path_for_detection):
        # Generic Gerrit detection for various hosts and common Gerrit path prefixes
        platform = 'gerrit'
    else:
        raise ValueError(f"Unsupported platform. Only GitHub, GitLab, and Gerrit are supported. Got: {parsed.netloc}")

    # Extract path components
    path_parts = [part for part in parsed.path.split('/') if part]

    if platform == 'gerrit':
        # Strip common Gerrit prefixes
        parts = path_parts[:]
        if parts and parts[0] in {'a', 'r'}:
            parts = parts[1:]
        if len(parts) >= 2 and parts[0] == 'plugins' and parts[1] == 'gitiles':
            parts = parts[2:]
        if len(parts) >= 2 and parts[0] == 'admin' and parts[1] == 'repos':
            parts = parts[2:]

        if len(parts) < 1:
            raise ValueError(f"Invalid Gerrit repository URL format. Expected format: https://host/group/project or https://host/r/group/project. Got: {url}")

        if len(parts) == 1:
            owner = ''
            repo_name = parts[0]
        else:
            repo_name = parts[-1]
            owner = '/'.join(parts[:-1])
    else:
        if len(path_parts) < 2:
            raise ValueError(f"Invalid repository URL format. Expected format: https://platform.com/owner/repo. Got: {url}")

        # For GitLab, handle nested groups (e.g., gitlab.com/group/subgroup/project)
        # The last component is always the repository name
        repo_name = path_parts[-1]

        # Everything before the last component is the owner/namespace
        if platform == 'gitlab' and len(path_parts) > 2:
            # For GitLab nested groups: gitlab.com/group/subgroup/project
            owner = '/'.join(path_parts[:-1])
        else:
            # For GitHub or simple GitLab: platform.com/owner/repo
            owner = path_parts[0]

    return owner, repo_name, platform


async def fetch_github_branches(owner: str, repo_name: str) -> List[dict]:
    """Fetch branches from GitHub repository"""
    url = f"https://api.github.com/repos/{owner}/{repo_name}/branches?per_page=100"
    headers = {"Authorization": f"Bearer {settings.GITHUB_ACCESS_TOKEN}"}

    all_branches = []

    async with httpx.AsyncClient(timeout=30.0) as client:
        while url:
            response = await client.get(url, headers=headers)

            if response.status_code == 404:
                raise HTTPException(
                    status_code=404,
                    detail=f"Repository {owner}/{repo_name} not found on GitHub"
                )

            response.raise_for_status()
            branches = response.json()
            all_branches.extend(branches)

            # Check for pagination
            url = None
            if 'link' in response.headers:
                links = response.headers['link'].split(',')
                for link in links:
                    if 'rel="next"' in link:
                        url = link.split(';')[0].strip()[1:-1]  # Remove < and >
                        break

    return all_branches

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def fetch_gitlab_branches(
    owner: str, 
    repo_name: str, 
    gitlab_instance: str = "gitlab.com",
    access_token: Optional[str] = None
) -> List[Dict]:
    """
    Fetch branches from GitLab repository (supports both public and private repos).
    Uses Bearer authentication which works for your setup.
    
    Args:
        owner (str): Repository owner/namespace
        repo_name (str): Repository name
        gitlab_instance (str): GitLab instance domain
        access_token (str, optional): GitLab access token (required for private repos)
    
    Returns:
        List[Dict]: List of branches in GitHub-compatible format
    """
    if not owner or not repo_name:
        raise HTTPException(
            status_code=400,
            detail="Owner and repository name are required"
        )
    
    # Properly URL-encode the project path for GitLab API
    project_path = f"{owner}/{repo_name}".replace('/', '%2F')
    url = f"https://{gitlab_instance}/api/v4/projects/{project_path}/repository/branches?per_page=100"
    
    # Setup headers - use Bearer auth which works for your token
    headers = {
        'User-Agent': 'GitLab-Branches-Fetcher/1.0',
        'Accept': 'application/json'
    }
    
    if access_token:
        # Use Bearer authentication (this works for your setup)
        headers['Authorization'] = f'Bearer {access_token}'
    
    all_branches = []
    
    try:
        async with httpx.AsyncClient(
            timeout=30.0,
            headers=headers,
            follow_redirects=True
        ) as client:
            
            while url:
                logger.info(f"Fetching: {url}")
                
                try:
                    response = await client.get(url)
                    
                    if response.status_code == 404:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Repository {owner}/{repo_name} not found on {gitlab_instance}"
                        )
                    
                    if response.status_code == 401:
                        raise HTTPException(
                            status_code=401,
                            detail="Authentication required. Please provide a valid access token."
                        )
                    
                    if response.status_code == 403:
                        raise HTTPException(
                            status_code=403,
                            detail="Access forbidden. Check token permissions."
                        )
                    
                    response.raise_for_status()
                    gitlab_branches = response.json()
                    
                    # Convert GitLab format to GitHub-compatible format
                    for branch in gitlab_branches:
                        commit_info = branch.get("commit", {})
                        converted_branch = {
                            "name": branch.get("name", ""),
                            "protected": branch.get("protected", False),
                            "commit": {
                                "sha": commit_info.get("id", ""),
                                "url": commit_info.get("web_url", "")
                            }
                        }
                        all_branches.append(converted_branch)
                    
                    # Handle pagination
                    url = None
                    if 'link' in response.headers:
                        links = response.headers['link'].split(',')
                        for link in links:
                            if 'rel="next"' in link:
                                url = link.split(';')[0].strip()[1:-1]
                                break
                    
                    # Safety check
                    if len(gitlab_branches) < 100:
                        break
                        
                except httpx.HTTPStatusError as e:
                    raise HTTPException(
                        status_code=e.response.status_code,
                        detail=f"GitLab API error: {e.response.status_code} - {e.response.text}"
                    )
        
        logger.info(f"Successfully fetched {len(all_branches)} branches")
        return all_branches
        
    except httpx.RequestError as e:
        logger.error(f"Network error: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to GitLab instance {gitlab_instance}"
        )



async def fetch_gerrit_branches(owner: str, repo_name: str, gerrit_instance: str) -> List[dict]:
    """Fetch branches from a Gerrit repository (any Gerrit host).

    Strategy:
    1) Try Gitiles public endpoint: /{project}/+refs?format=JSON
    2) Fallback to Gerrit REST API: /projects/{project}/branches/

    Both endpoints may include an XSSI prefix ")]}'" that we strip before JSON parsing.
    """
    project_for_url = f"{owner}/{repo_name}" if owner else repo_name

    async with httpx.AsyncClient(timeout=30.0) as client:
        # Attempt 1: Gitiles +refs (public and works on googlesource)
        gitiles_url = f"https://{gerrit_instance}/{project_for_url}/+refs?format=JSON"
        try:
            gitiles_resp = await client.get(gitiles_url)
            if gitiles_resp.status_code == 200:
                text = gitiles_resp.text
                if text.startswith(")]}'"):
                    newline_index = text.find('\n')
                    text = text[newline_index + 1:] if newline_index != -1 else ''

                data = json.loads(text)
                converted: List[dict] = []
                for ref_name, ref_info in data.items():
                    if ref_name.startswith('refs/heads/'):
                        name = ref_name[len('refs/heads/'):]
                        sha = ref_info.get('value', '')
                        commit_url = f"https://{gerrit_instance}/{project_for_url}/+/{sha}" if sha else ""
                        converted.append({
                            "name": name,
                            "protected": False,
                            "commit": {
                                "sha": sha,
                                "url": commit_url
                            }
                        })
                return converted
        except Exception:
            # Ignore and try REST fallback below
            pass

        # Attempt 2: Gerrit REST API
        project_path = project_for_url.replace('/', '%2F')
        rest_url = f"https://{gerrit_instance}/projects/{project_path}/branches/"
        rest_resp = await client.get(rest_url)

        if rest_resp.status_code == 404:
            # For Gerrit instances that don't expose REST APIs, return basic info
            # This is common for many Gerrit installations
            print(f"Gerrit REST API not available for {gerrit_instance}, returning basic branch info")
            return [
                {
                    "name": "master",
                    "protected": False,
                    "commit": {
                        "sha": "",
                        "url": f"https://{gerrit_instance}/r/{project_for_url}"
                    }
                }
            ]

        rest_resp.raise_for_status()
        text = rest_resp.text
        if text.startswith(")]}'"):
            newline_index = text.find('\n')
            text = text[newline_index + 1:] if newline_index != -1 else ''

        try:
            gerrit_branches = json.loads(text)
        except json.JSONDecodeError as exc:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to parse Gerrit response: {str(exc)}"
            )

        converted: List[dict] = []
        for branch in gerrit_branches:
            ref = branch.get('ref', '')
            name = ref.split('refs/heads/')[-1] if 'refs/heads/' in ref else ref
            sha = branch.get('revision', '')
            commit_url = f"https://{gerrit_instance}/{project_for_url}/+/{sha}" if sha else ""
            converted.append({
                "name": name,
                "protected": False,
                "commit": {
                    "sha": sha,
                    "url": commit_url
                }
            })

        return converted


class RepositoryUrlRequest(BaseModel):
    repository_url: str


@router.post("/get-branches-by-url", response_model=List[dict])
async def get_branches_by_url(request: RepositoryUrlRequest):
    """
    Get all branches from a GitHub, GitLab, or Gerrit (e.g., *.googlesource.com) repository using the repository URL.

    This is a simple API that takes a repository URL and returns all branches.
    Supports both GitHub and GitLab public repositories.

    Args:
        request: Contains the repository_url (e.g., https://github.com/owner/repo)

    Returns:
        List of branches with name, protected status, and commit information

    Example:
        POST /oauth/github/get-branches-by-url
        {
            "repository_url": "https://github.com/rdkcentral/Ripple"
        }
    """
    try:
        # Parse the repository URL
        owner, repo_name, platform = parse_repository_url(request.repository_url)

        print(f"Fetching branches for {platform}: {owner}/{repo_name}")

        if platform == "github":
            branches = await fetch_github_branches(owner, repo_name)
        elif platform == "gitlab":
            # Extract GitLab instance from URL if it's not gitlab.com
            parsed_url = urlparse(request.repository_url)
            gitlab_instance = parsed_url.netloc
            branches = await fetch_gitlab_branches(owner, repo_name, gitlab_instance)
        elif platform == "gerrit":
            parsed_url = urlparse(request.repository_url)
            gerrit_instance = parsed_url.netloc
            branches = await fetch_gerrit_branches(owner, repo_name, gerrit_instance)
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported platform: {platform}"
            )

        print(f"Found {len(branches)} branches")
        return branches

    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid repository URL: {str(e)}"
        )
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            raise HTTPException(
                status_code=404,
                detail="Repository not found"
            )
        else:
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"API error: {e.response.text}"
            )
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=503,
            detail=f"Network error: {str(e)}"
        )
    except Exception as e:
        logging.error(f"Unexpected error in get_branches_by_url: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


async def fetch_github_repo_details(owner: str, repo_name: str) -> dict:
    """Fetch repository details from GitHub"""
    url = f"https://api.github.com/repos/{owner}/{repo_name}"
    headers = {"Authorization": f"Bearer {settings.GITHUB_ACCESS_TOKEN}"}

    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.get(url, headers=headers)

        if response.status_code == 404:
            raise HTTPException(
                status_code=404,
                detail=f"Repository {owner}/{repo_name} not found on GitHub"
            )

        response.raise_for_status()
        return response.json()


async def fetch_gitlab_repo_details(owner: str, repo_name: str, gitlab_instance: str = "gitlab.com") -> dict:
    """Fetch repository details from GitLab"""
    # Properly URL-encode the project path for GitLab API
    project_path = f"{owner}/{repo_name}".replace('/', '%2F')
    url = f"https://{gitlab_instance}/api/v4/projects/{project_path}"

    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.get(url)

        if response.status_code == 404:
            raise HTTPException(
                status_code=404,
                detail=f"Repository {owner}/{repo_name} not found on {gitlab_instance}"
            )

        response.raise_for_status()
        gitlab_data = response.json()

        # Convert GitLab format to GitHub-compatible format for consistency
        converted_data = {
            "id": gitlab_data.get("id"),
            "name": gitlab_data.get("name"),
            "full_name": gitlab_data.get("path_with_namespace"),
            "description": gitlab_data.get("description"),
            "html_url": gitlab_data.get("web_url"),
            "clone_url": gitlab_data.get("http_url_to_repo"),
            "ssh_url": gitlab_data.get("ssh_url_to_repo"),
            "default_branch": gitlab_data.get("default_branch"),
            "created_at": gitlab_data.get("created_at"),
            "updated_at": gitlab_data.get("last_activity_at"),
            "language": None,  # GitLab doesn't provide primary language in project details
            "stargazers_count": gitlab_data.get("star_count", 0),
            "forks_count": gitlab_data.get("forks_count", 0),
            "open_issues_count": gitlab_data.get("open_issues_count", 0),
            "visibility": gitlab_data.get("visibility"),
            "private": gitlab_data.get("visibility") == "private",
            "archived": gitlab_data.get("archived", False),
            "topics": gitlab_data.get("topics", []),
            "license": gitlab_data.get("license"),
            "owner": {
                "login": gitlab_data.get("namespace", {}).get("path"),
                "id": gitlab_data.get("namespace", {}).get("id"),
                "avatar_url": gitlab_data.get("namespace", {}).get("avatar_url"),
                "html_url": gitlab_data.get("namespace", {}).get("web_url"),
                "type": gitlab_data.get("namespace", {}).get("kind")
            }
        }

        return converted_data


@router.post("/get-repo-details-by-url")
async def get_repo_details_by_url(request: RepositoryUrlRequest):
    """
    Get repository details from a GitHub or GitLab repository using the repository URL.

    This is a simple API that takes a repository URL and returns repository details.
    Supports both GitHub and GitLab public repositories.

    Args:
        request: Contains the repository_url (e.g., https://github.com/owner/repo)

    Returns:
        Repository details including name, description, stars, forks, etc.

    Example:
        POST /oauth/github/get-repo-details-by-url
        {
            "repository_url": "https://github.com/rdkcentral/Ripple"
        }
    """
    try:
        # Parse the repository URL
        owner, repo_name, platform = parse_repository_url(request.repository_url)

        print(f"Fetching repository details for {platform}: {owner}/{repo_name}")

        if platform == "github":
            repo_details = await fetch_github_repo_details(owner, repo_name)
        elif platform == "gitlab":
            # Extract GitLab instance from URL if it's not gitlab.com
            parsed_url = urlparse(request.repository_url)
            gitlab_instance = parsed_url.netloc
            repo_details = await fetch_gitlab_repo_details(owner, repo_name, gitlab_instance)
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported platform: {platform}"
            )

        print(f"Successfully fetched repository details")
        return repo_details

    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid repository URL: {str(e)}"
        )
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            raise HTTPException(
                status_code=404,
                detail="Repository not found"
            )
        else:
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"API error: {e.response.text}"
            )
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=503,
            detail=f"Network error: {str(e)}"
        )
    except Exception as e:
        logging.error(f"Unexpected error in get_repo_details_by_url: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
