
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from app.rag_chatbot.orhestrator import IngestionOrchestrator, RetrievalOrchestrator
from app.rag_chatbot.scraping.firecrawl_service import FirecrawlService
from dotenv import load_dotenv

load_dotenv()

router = APIRouter(prefix="/help_chat", tags=["help_chat"])

# Initialize orchestrators
ingestion_orchestrator = IngestionOrchestrator()
retrieval_orchestrator = RetrievalOrchestrator()

class IndexRequest(BaseModel):
    urls: List[str]

class SearchRequest(BaseModel):
    query: str
    limit: int = 5

class SearchResponse(BaseModel):
    answer: str
    sources: List[str]  # Just URLs
    status: str  # "success" or "failure"

@router.post("/ingest")
async def ingest_urls(request: IndexRequest):
    """Ingest multiple URLs into knowledge base"""
    if not request.urls:
        raise HTTPException(status_code=400, detail="URLs list cannot be empty")
    
    try:
        result = await ingestion_orchestrator.process_urls(request.urls)
        
        return {
            "status": "completed",
            "message": f"Processed {result['processed']} URLs, created {result['total_chunks']} chunks",
            **result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ingestion failed: {str(e)}")
    
@router.post("/test_scrape")
async def test_scrape(request: IndexRequest):
    """Test scraping URLs directly using FirecrawlService"""
    if not request.urls:
        raise HTTPException(status_code=400, detail="URLs list cannot be empty")
    
    try:
        # Initialize FirecrawlService
        firecrawl_service = FirecrawlService()
        
        # Scrape each URL
        results = []
        for url in request.urls:
            result = await firecrawl_service.scrape_url(url)
            results.append(result)
        
        return {
            "status": "completed",
            "message": f"Scraped {len(request.urls)} URLs",
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

@router.post("/search", response_model=SearchResponse)
async def search_knowledge_base(request: SearchRequest):
    """Search knowledge base and generate answer"""
    if not request.query.strip():
        raise HTTPException(status_code=400, detail="Query cannot be empty")
    
    try:
        print(f"💭 Processing query: {request.query}")
        result = await retrieval_orchestrator.search(request.query, request.limit)
        print(f"✅ Search completed, returning {len(result['sources'])} sources")
        
        # Extract only URLs from sources
        sources = [source["url"] for source in result["sources"]]
        
        return SearchResponse(
            answer=result["answer"],
            sources=sources,
            status="success"
        )
    except Exception as e:
        print(f"❌ Search error: {str(e)}")
        print(f"❌ Error type: {type(e).__name__}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")