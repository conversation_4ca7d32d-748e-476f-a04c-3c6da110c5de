from code_generation_core_agent.project_schemas import (
    ProjectSchema, 
    ProjectOverview, 
    Container, 
    ContainerType,
    dict_to_project_schema
)

from typing import List, Dict, Any,  get_origin, get_args
from enum import Enum
from dataclasses import fields, is_dataclass
import json


def convert_env_dict_to_array(env_dict: Dict[str, str]) -> List[Dict[str, str]]:
    """Convert environment dict to array format for RJSF"""
    if not env_dict:
        return []
    return [{"key": k, "value": v} for k, v in env_dict.items()]

def convert_env_array_to_dict(env_array: List[Dict[str, str]]) -> Dict[str, str]:
    """Convert environment array format back to dict"""
    if not env_array:
        return {}
    return {item["key"]: item["value"] for item in env_array if item.get("key")}

def get_framework_options_for_container_type(container_type: ContainerType) -> List[Dict[str, str]]:
    """Get framework options based on container type"""
    framework_map = {
        ContainerType.FRONTEND: [
            {"value": "react", "label": "React"},
            {"value": "vue", "label": "Vue"},
            {"value": "angular", "label": "Angular"},
            {"value": "svelte", "label": "Svelte"},
            {"value": "nextjs", "label": "Next.js"},
            {"value": "nuxt", "label": "Nuxt"},
            {"value": "astro", "label": "Astro"},
            {"value": "remix", "label": "Remix"},
            {"value": "qwik", "label": "Qwik"},
            {"value": "vite", "label": "Vite"},
            {"value": "typescript", "label": "TypeScript"},
        ],
        ContainerType.BACKEND: [
            {"value": "fastapi", "label": "FastAPI"},
            {"value": "django", "label": "Django"},
            {"value": "flask", "label": "Flask"},
            {"value": "express", "label": "Express"},
            {"value": "dotnet", "label": "DotNET"},
            {"value": "springboot", "label": "SpringBoot"},
        ],
        ContainerType.MOBILE: [
            {"value": "flutter", "label": "Flutter"},
            {"value": "android", "label": "Android"},
            {"value": "ios", "label": "iOS"},
            {"value": "kotlin", "label": "Kotlin"},
            {"value": "reactnative", "label": "React Native"}
        ],
        ContainerType.DATABASE: [
            {"value": "postgresql", "label": "PostgreSQL"},
            {"value": "mysql", "label": "MySQL"}, 
            {"value": "sqlite", "label": "SQLite"},
            {"value": "mongodb", "label": "MongoDB"},
        ]
    }
    
    return framework_map.get(container_type, [{"value": "default", "label": "Default"}])

def get_all_framework_options() -> List[Dict[str, str]]:
    """Get all available framework options"""
    return [
        # Frontend
        {"value": "react", "label": "React"},
        {"value": "vue", "label": "Vue"},
        {"value": "angular", "label": "Angular"},
        {"value": "svelte", "label": "Svelte"},
        {"value": "nextjs", "label": "Next.js"},
        {"value": "nuxt", "label": "Nuxt"},
        {"value": "astro", "label": "Astro"},
        {"value": "remix", "label": "Remix"},
        {"value": "qwik", "label": "Qwik"},
        {"value": "vite", "label": "Vite"},
        {"value": "typescript", "label": "TypeScript"},
        
        # Backend
        {"value": "fastapi", "label": "FastAPI"},
        {"value": "django", "label": "Django"},
        {"value": "flask", "label": "Flask"},
        {"value": "express", "label": "Express"},
        {"value": "dotnet", "label": "DotNET"},
        {"value": "springboot", "label": "SpringBoot"},
        
        # Mobile
        {"value": "flutter", "label": "Flutter"},
        {"value": "android", "label": "Android"},
        {"value": "ios", "label": "iOS"},
        {"value": "kotlin", "label": "Kotlin"},
        {"value": "reactnative", "label": "React Native"},
        
        # Database
        {"value": "postgresql", "label": "PostgreSQL"},
        {"value": "mysql", "label": "MySQL"},
        {"value": "sqlite", "label": "SQLite"},
        {"value": "mongodb", "label": "MongoDB"},
        
        # Default
        {"value": "default", "label": "Default"},
    ]

def get_container_details_schema() -> tuple[Dict[str, Any], Dict[str, Any]]:
    """Get schema for container_details complex object"""
    json_schema = {
        "type": "object",
        "properties": {
            "features": {
                "type": "array",
                "items": {"type": "string"},
                "description": "List of container features"
            },
            "layout_description": {
                "type": "string",
            },
            "style": {
                "type": "string",
            },
            "theme": {
                "type": "string",
            },
            "colors": {
                "type": "object",
                "properties": {
                    "primary": {"type": "string"},
                    "secondary": {"type": "string"},
                    "accent": {"type": "string"}
                },
            }
        },
        "description": "Container design and feature details"
    }
    
    ui_schema = {
        "features": {
            "ui:options": {
                "addable": True,
                "removable": True,
                "orderable": True
            }
        },
        "layout_description": {
            "ui:widget": "textarea",
            "ui:options": {"rows": 3}
        },
        "style": {
            "ui:placeholder": "e.g., minimalistic, modern, classic"
        },
        "theme": {
            "ui:placeholder": "e.g., light, dark, colorful"
        },
        "colors": {
            "ui:title": "Color Scheme",
            "primary": {"ui:placeholder": "e.g., #1976d2"},
            "secondary": {"ui:placeholder": "e.g., #424242"},
            "accent": {"ui:placeholder": "e.g., #ffeb3b"}
        }
    }
    
    return json_schema, ui_schema


def get_json_schema_field(field_info) -> tuple[Dict[str, Any], Dict[str, Any]]:
    """Map dataclass field to JSON Schema property + UI Schema with proper routes handling"""
    field_type = field_info.type
    field_name = field_info.name
    
    # Get description from metadata
    description = field_info.metadata.get("metadata", {}).get("description", "")
    
    # Handle Optional types
    origin = get_origin(field_type)
    if origin is not None:
        args = get_args(field_type)
        if len(args) == 2 and type(None) in args:
            field_type = args[0] if args[1] is type(None) else args[1]
    
    # JSON Schema property
    json_schema_prop = {}
    ui_schema_prop = {}
    
    # Handle routes field specifically - array of dict objects
    if field_name == "routes":
        json_schema_prop = {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                    },
                    "endpoint": {
                        "type": "string", 
                    }
                },
                "required": ["path", "endpoint"],
                "additionalProperties": False
            },
            "description": description or "API routes configuration"
        }
        ui_schema_prop = {
            "ui:title": "Routes",
            "ui:options": {
                "addable": True,
                "removable": True,
                "orderable": True
            },
            "items": {
                "ui:order": ["path", "endpoint"],
                "path": {
                    "ui:placeholder": "e.g., /home",
                },
                "endpoint": {
                    "ui:placeholder": "e.g., GET /home",
                }
            }
        }
        return json_schema_prop, ui_schema_prop
    
    # FIXED: Handle env field specifically - use proper key-value structure
    if field_name == "env":
        json_schema_prop = {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "key": {
                        "type": "string",
                        "title": "Variable Name"
                    },
                    "value": {
                        "type": "string", 
                        "title": "Value"
                    }
                },
                "required": ["key", "value"],
                "additionalProperties": False
            },
            "description": description or "Environment variables as key-value pairs"
        }
        ui_schema_prop = {
            "ui:title": "Environment Variables",
            "ui:description": "Add environment variables as key-value pairs",
            "ui:options": {
                "addable": True,
                "removable": True,
                "orderable": False
            },
            "items": {
                "ui:order": ["key", "value"],
                "key": {
                    "ui:placeholder": "e.g., DATABASE_URL",
                    "ui:title": "Variable Name"
                },
                "value": {
                    "ui:placeholder": "e.g., postgresql://localhost:5432/mydb",
                    "ui:title": "Value"
                }
            }
        }
        return json_schema_prop, ui_schema_prop
    
    # Handle ContainerType enum specifically
    if field_type == ContainerType:
        json_schema_prop = {
            "type": "string",
            "enum": [e.value for e in ContainerType],
            "description": description or "Select container type"
        }
        ui_schema_prop = {
            "ui:placeholder": "Select container type...",
            "ui:enumOptions": [
                {"value": ContainerType.FRONTEND.value, "label": "Frontend"},
                {"value": ContainerType.BACKEND.value, "label": "Backend"}, 
                {"value": ContainerType.MOBILE.value, "label": "Mobile"},
                {"value": ContainerType.DATABASE.value, "label": "Database"}
            ]
        }
    
    # Handle framework field specifically
    elif field_name == "framework":
        json_schema_prop = {
            "type": "string",
            "description": description or "Select framework"
        }
        ui_schema_prop = {
            "ui:placeholder": "Select framework...",
        }
    
    # Handle container_details specifically
    elif field_name == "container_details":
        json_schema_prop, ui_schema_prop = get_container_details_schema()
    
    # Handle other enum types
    elif hasattr(field_type, '__mro__') and Enum in field_type.__mro__:
        json_schema_prop = {
            "type": "string",
            "enum": [e.value for e in field_type],
            "description": description or f"Select {field_name.replace('_', ' ')}"
        }
        ui_schema_prop = {"ui:placeholder": "Select..."}
    
    # Handle different field types
    elif field_type == str:
        json_schema_prop = {"type": "string"}
        if description:
            json_schema_prop["description"] = description
            
        # Use textarea for longer text fields
        if any(keyword in field_name.lower() for keyword in ["description", "command", "config", "spec"]):
            
            if field_name == "buildCommand":
                ui_schema_prop ={
                    "ui:widget": "textarea",
                    "ui:options": {"rows": 3},
                    "ui:placeholder": "e.g., npm run build || yarn build || make build",
                    "ui:help": "This is required for building the package before deployment"
                    
                }
            elif field_name == "installCommand":
                ui_schema_prop = {
                    "ui:widget": "textarea",
                    "ui:options": {"rows": 3},
                    "ui:placeholder": "e.g., npm install || yarn install || pip install -r requirements.txt"
                }
            
            elif field_name == "startCommand":
                ui_schema_prop = {
                    "ui:widget": "textarea",
                    "ui:options": {"rows": 3},
                    "ui:placeholder": "e.g., npm start || yarn start || python app.py"
                }
                
            
            else:
                ui_schema_prop = {
                    "ui:widget": "textarea",
                    "ui:options": {"rows": 3}
                }
        else:
            ui_schema_prop = {"ui:placeholder": f"Enter {field_name.replace('_', ' ')}"}
    
    elif field_type == int:
        json_schema_prop = {"type": "integer"}
        if description:
            json_schema_prop["description"] = description
        ui_schema_prop = {"ui:placeholder": f"Enter {field_name.replace('_', ' ')}"}
    
    elif get_origin(field_type) is list:
        inner_type = get_args(field_type)[0] if get_args(field_type) else str
        
        # Handle List[Route] type specifically
        if hasattr(inner_type, '__name__') and inner_type.__name__ == 'Route':
            # This case is already handled above in the routes-specific section
            pass
        else:
            # Handle other list types (strings, etc.)
            json_schema_prop = {
                "type": "array",
                "items": {"type": "string"},
                "description": description or f"List of {field_name.replace('_', ' ')}"
            }
            ui_schema_prop = {
                "ui:options": {
                    "addable": True,
                    "removable": True,
                    "orderable": True
                }
            }
    
    elif field_type == dict or get_origin(field_type) is dict:
        # Handle other dict fields (not env) as textarea
        if field_name != "env":
            json_schema_prop = {
                "type": "object",
                "additionalProperties": {"type": "string"},
                "description": description or "Key-value pairs"
            }
            ui_schema_prop = {"ui:widget": "textarea"}
        else:
            # This should be handled by the env-specific case above
            json_schema_prop = {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "key": {"type": "string"},
                        "value": {"type": "string"}
                    },
                    "required": ["key", "value"],
                    "additionalProperties": False
                },
                "description": description or "Environment variables"
            }
            ui_schema_prop = {
                "ui:options": {
                    "addable": True,
                    "removable": True
                }
            }
    
    else:
        json_schema_prop = {"type": "string"}
        if description:
            json_schema_prop["description"] = description
        ui_schema_prop = {"ui:placeholder": f"Enter {field_name.replace('_', ' ')}"}
    
    return json_schema_prop, ui_schema_prop

def generate_dataclass_schemas(dataclass_type) -> tuple[Dict[str, Any], Dict[str, Any]]:
    """Generate JSON Schema + UI Schema from dataclass"""
    properties = {}
    ui_schema = {}
    
    for field_info in fields(dataclass_type):
        # Skip private fields only (include container_details now)
        if field_info.name in ['private', 'seed', 'migrations', 'schema', 'apiSpec', 'lintConfig', 'type', 'auth']:
            continue
            
        json_prop, ui_prop = get_json_schema_field(field_info)
        properties[field_info.name] = json_prop
        
        if ui_prop:
            ui_schema[field_info.name] = ui_prop
    
    json_schema = {
        "type": "object",
        "properties": properties
    }
    
    return json_schema, ui_schema

def generate_rjsf_schemas() -> Dict[str, Any]:
    """Generate React JSON Schema Form compatible schemas with optimized routes handling"""
    
    # Generate overview schemas
    overview_json_schema, overview_ui_schema = generate_dataclass_schemas(ProjectOverview)
    
    # Generate container schemas
    container_json_schema, container_ui_schema = generate_dataclass_schemas(Container)
    
    # Set required fields based on your dataclass requirements
    overview_json_schema["required"] = ["project_name", "description"]
    container_json_schema["required"] = ["container_name", "description", "framework", "container_type"]
    
    # Override framework field to be conditional based on container_type
    container_json_schema["properties"]["framework"] = {
        "type": "string",
        "description": "Framework selection based on container type"
    }
    
    # Add conditional framework schemas based on container_type
    container_json_schema["dependencies"] = {
        "container_type": {
            "oneOf": [
                {
                    "properties": {
                        "container_type": {"const": "frontend"},
                        "framework": {
                            "type": "string",
                            "enum": ["react", "vue", "angular", "svelte", "nextjs", "nuxt", "astro", "remix", "qwik", "vite", "typescript"],
                            "description": "Select frontend framework"
                        }
                    }
                },
                {
                    "properties": {
                        "container_type": {"const": "backend"},
                        "framework": {
                            "type": "string", 
                            "enum": ["fastapi", "django", "flask", "express", "dotnet", "springboot"],
                            "description": "Select backend framework"
                        }
                    }
                },
                {
                    "properties": {
                        "container_type": {"const": "mobile"},
                        "framework": {
                            "type": "string",
                            "enum": ["flutter", "android", "ios", "kotlin", "reactnative"],
                            "description": "Select mobile framework"
                        }
                    }
                },
                {
                    "properties": {
                        "container_type": {"const": "database"},
                        "framework": {
                            "type": "string",
                            "enum": ["postgresql", "mysql", "sqlite", "mongodb"],
                            "description": "Select database framework"
                        }
                    }
                }
            ]
        }
    }
    
    # Get all property names for ui:order
    overview_properties = list(overview_json_schema["properties"].keys())
    container_properties = list(container_json_schema["properties"].keys())
    
    # Main form schema
    main_json_schema = {
        "type": "object",
        "title": "Project Manifest",
        "properties": {
            "overview": {
                **overview_json_schema,
                "title": "Project Overview"
            },
            "containers": {
                "type": "array",
                "title": "Containers",
                "description": "List of project containers",
                "items": {
                    **container_json_schema,
                    "title": "Container"
                },
                "minItems": 1
            }
        },
        "required": ["overview", "containers"]
    }
    
    # Define preferred field order with routes positioned appropriately
    preferred_overview_order = [
        "project_name", 
        "description", 
        "third_party_services",
        "env"
    ]
    
    preferred_container_order = [
        "container_name", 
        "description", 
        "container_type",
        "framework",
        "interfaces",
        "port",
        "dependent_containers",
        "workspace",
        "container_root", 
        "routes",  # Position routes after basic config but before commands
        "auth",
        "installCommand",
        "buildCommand", 
        "startCommand",
        "lintCommand",
        "generateOpenapiCommand",
        "type",
        "lintConfig",
        "apiSpec",
        "schema",
        "migrations",
        "seed",
        "env",
        "container_details"
    ]
    
    # Create complete ui:order that includes all properties
    overview_ui_order = []
    container_ui_order = []
    
    # Add preferred fields first
    for field in preferred_overview_order:
        if field in overview_properties:
            overview_ui_order.append(field)
    
    for field in preferred_container_order:
        if field in container_properties:
            container_ui_order.append(field)
    
    # Add any remaining fields that weren't in preferred order
    for field in overview_properties:
        if field not in overview_ui_order:
            overview_ui_order.append(field)
            
    for field in container_properties:
        if field not in container_ui_order:
            container_ui_order.append(field)
    
    # Update UI schema with conditional framework options
    container_ui_schema["framework"] = {
        "ui:placeholder": "Select framework based on container type...",
        "ui:help": "Framework options will update based on your container type selection"
    }
    
    # Main UI schema with complete field ordering
    main_ui_schema = {
        "ui:order": ["overview", "containers"],
        "overview": {
            "ui:title": "Project Overview",
            "ui:description": "Define your project's basic information",
            "ui:order": overview_ui_order,
            **overview_ui_schema
        },
        "containers": {
            "ui:title": "Containers", 
            "ui:description": "Define your project containers and their configurations",
            "ui:options": {
                "addable": True,
                "removable": True,
                "orderable": True
            },
            "items": {
                "ui:title": "Container Configuration",
                "ui:order": container_ui_order,
                **container_ui_schema
            }
        }
    }
    
    return {
        "jsonSchema": main_json_schema,
        "uiSchema": main_ui_schema
    }
    
def form_data_to_project_schema(form_data: Dict[str, Any]) -> ProjectSchema:
    """Convert RJSF form data back to ProjectSchema with proper env handling"""
    try:
        # First convert form data to proper manifest dict format
        manifest_dict = form_data_to_manifest_dict(form_data)
        
        # Then use existing utility to convert dict to ProjectSchema
        return dict_to_project_schema(manifest_dict)
    except Exception as e:
        raise ValueError(f"Failed to convert form data to ProjectSchema: {str(e)}")

def project_schema_to_form_data(schema: ProjectSchema) -> Dict[str, Any]:
    """Convert ProjectSchema instance to RJSF form data"""
    
    def dataclass_to_dict(obj):
        if obj is None:
            return {}
        result = {}
        for field_info in fields(obj):
            # Skip only private fields now
            if field_info.name in ['private']:
                continue
                
            value = getattr(obj, field_info.name)
            if isinstance(value, Enum):
                result[field_info.name] = value.value
            elif is_dataclass(value):
                result[field_info.name] = dataclass_to_dict(value)
            elif isinstance(value, list) and value and is_dataclass(value[0]):
                result[field_info.name] = [dataclass_to_dict(item) for item in value]
            elif field_info.name == 'env' and isinstance(value, dict):
                # Convert env dict to array format for RJSF
                result[field_info.name] = convert_env_dict_to_array(value)
            else:
                # Ensure empty strings for required fields to avoid validation issues
                result[field_info.name] = value if value is not None else ""
        return result
    
    overview_data = dataclass_to_dict(schema.overview) if schema.overview else {
        "project_name": "",
        "description": "", 
        "third_party_services": [],
        "env": []
    }
    
    containers_data = [dataclass_to_dict(container) for container in schema.containers] if schema.containers else []
    
    return {
        "overview": overview_data,
        "containers": containers_data
    }

def parse_form_container_data(container_data: Dict[str, Any]) -> Dict[str, Any]:
    """Parse form container data back to proper types with improved routes and env handling"""
    parsed = {}
    
    for key, value in container_data.items():
        if key in ['private']:
            continue
            
        # Handle routes field - ensure it's an array of dict objects
        if key == 'routes':
            if isinstance(value, list):
                parsed_routes = []
                for route_item in value:
                    if isinstance(route_item, dict):
                        # Ensure required fields exist (path and endpoint only)
                        route_obj = {
                            "path": route_item.get("path", ""),
                            "endpoint": route_item.get("endpoint", "")
                        }
                        parsed_routes.append(route_obj)
                    elif isinstance(route_item, str):
                        # Handle legacy string format - convert to dict
                        parsed_routes.append({
                            "path": route_item,
                            "endpoint": route_item
                        })
                parsed[key] = parsed_routes
            else:
                parsed[key] = []
                
        # FIXED: Handle env field - convert array format back to dict
        elif key == 'env':
            if isinstance(value, list):
                parsed[key] = convert_env_array_to_dict(value)
            elif isinstance(value, str) and value.strip():
                try:
                    parsed[key] = json.loads(value)
                except json.JSONDecodeError:
                    print(f"Warning: Invalid JSON in env field: {value}")
                    parsed[key] = {}
            elif isinstance(value, dict):
                parsed[key] = value
            else:
                parsed[key] = {}
                
        # Handle other JSON string fields
        elif key in ['lintConfig']:
            if isinstance(value, str) and value.strip():
                try:
                    parsed[key] = json.loads(value)
                except json.JSONDecodeError:
                    print(f"Warning: Invalid JSON in {key} field: {value}")
                    parsed[key] = {}
            elif isinstance(value, dict):
                parsed[key] = value
            else:
                parsed[key] = {}
                
        # Handle port conversion
        elif key == 'port':
            if isinstance(value, str) and value.isdigit():
                parsed[key] = int(value)
            elif isinstance(value, (int, float)):
                parsed[key] = int(value)
            else:
                parsed[key] = value
                
        # Handle other lists (excluding routes which is handled above)
        elif key in ['dependent_containers', 'third_party_services']:
            if isinstance(value, list):
                parsed[key] = [str(item) for item in value if item]
            else:
                parsed[key] = []
                
        # All other fields
        else:
            parsed[key] = value
    
    return parsed
# Update the clean_container_data function
def clean_container_data(container: Dict[str, Any]) -> Dict[str, Any]:
    """Clean and format container data for form with proper routes handling"""
    cleaned = {}
    
    # Map fields and ensure proper types
    for key, value in container.items():
        # Only skip private fields now
        if key in ['private']:
            continue
            
        # Handle routes field - ensure proper structure as array of dicts
        if key == 'routes':
            if isinstance(value, list):
                cleaned_routes = []
                for route_item in value:
                    if isinstance(route_item, dict):
                        # Ensure required fields are present (path and endpoint only)
                        cleaned_route = {
                            "path": str(route_item.get("path", "")),
                            "endpoint": str(route_item.get("endpoint", ""))
                        }
                        cleaned_routes.append(cleaned_route)
                    elif isinstance(route_item, str):
                        # Convert legacy string format to dict format
                        cleaned_routes.append({
                            "path": route_item,
                            "endpoint": route_item
                        })
                cleaned[key] = cleaned_routes
            else:
                cleaned[key] = []
                
        # Handle port - convert to string if it's a number
        elif key == 'port' and isinstance(value, (int, float)):
            cleaned[key] = str(value)
            
        # Handle lists (excluding routes which is handled above)
        elif key in ['dependent_containers', 'third_party_services'] and isinstance(value, list):
            cleaned[key] = [str(item) for item in value if item]
            
        # FIXED: Handle env field - convert dict to array format for RJSF
        elif key == 'env':
            if isinstance(value, dict):
                cleaned[key] = convert_env_dict_to_array(value)
            elif isinstance(value, list):
                # Already in array format, validate structure
                cleaned[key] = [
                    {"key": item.get("key", ""), "value": item.get("value", "")}
                    for item in value if isinstance(item, dict)
                ]
            elif isinstance(value, str) and value.strip():
                try:
                    env_dict = json.loads(value)
                    cleaned[key] = convert_env_dict_to_array(env_dict)
                except json.JSONDecodeError:
                    cleaned[key] = []
            else:
                cleaned[key] = []
                
        # Handle dictionaries - convert to string representation for textarea fields (except env)
        elif key in ['lintConfig'] and isinstance(value, dict):
            if value:
                cleaned[key] = json.dumps(value, indent=2)
            else:
                cleaned[key] = ""
                
        # Handle container_details as a complex object (keep as dict)
        elif key == 'container_details' and isinstance(value, dict):
            cleaned[key] = value
            
        # Handle auth field
        elif key == 'auth':
            cleaned[key] = value if value is not None else ""
            
        # Handle other fields
        else:
            cleaned[key] = value if value is not None else ""
            
    return cleaned

def manifest_dict_to_form_data(manifest_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Convert manifest dictionary to RJSF form data format"""
    
    # Process overview data
    overview = manifest_dict.get("overview", {})
    overview_data = {
        "project_name": overview.get("project_name", ""),
        "description": overview.get("description", ""),
        "third_party_services": overview.get("third_party_services", [])
    }
    
    # Handle overview env field - keep as dict, don't convert to string
    if "env" in overview:
        if isinstance(overview["env"], dict):
            overview_data["env"] = overview["env"]
        elif isinstance(overview["env"], str) and overview["env"].strip():
            try:
                overview_data["env"] = json.loads(overview["env"])
            except json.JSONDecodeError:
                overview_data["env"] = {}
        else:
            overview_data["env"] = {}
    
    # Process containers data
    containers = manifest_dict.get("containers", [])
    containers_data = [clean_container_data(container) for container in containers]
    
    return {
        "overview": overview_data,
        "containers": containers_data
    }

def dataclass_to_dict_preserve_env(obj):
    """Convert dataclass to dict while preserving env field structure"""
    from dataclasses import is_dataclass, fields
    from enum import Enum
    
    if obj is None:
        return None
    if isinstance(obj, Enum):
        return obj.value
    if is_dataclass(obj):
        result = {}
        for field_info in fields(obj):
            if field_info.name in ['private']:
                continue
            value = getattr(obj, field_info.name)
            
            # Special handling for env field - keep as dict
            if field_info.name == 'env' and isinstance(value, dict):
                result[field_info.name] = value
            else:
                result[field_info.name] = dataclass_to_dict_preserve_env(value)
        return result
    if isinstance(obj, list):
        return [dataclass_to_dict_preserve_env(item) for item in obj]
    if isinstance(obj, dict):
        return {k: dataclass_to_dict_preserve_env(v) for k, v in obj.items()}
    return obj
    
def form_data_to_manifest_dict(form_data: Dict[str, Any]) -> Dict[str, Any]:
    """Convert RJSF form data back to manifest dictionary format with proper env handling"""
    
    # Process overview data
    overview_data = form_data.get("overview", {})
    processed_overview = {}
    
    for key, value in overview_data.items():
        if key == 'env' and isinstance(value, str) and value.strip():
            try:
                processed_overview[key] = json.loads(value)
            except json.JSONDecodeError:
                print(f"Warning: Invalid JSON in overview env field: {value}")
                processed_overview[key] = {}
        else:
            processed_overview[key] = value
    
    # Process containers data
    containers_data = form_data.get("containers", [])
    processed_containers = [parse_form_container_data(container) for container in containers_data]
    
    return {
        "overview": processed_overview,
        "containers": processed_containers
    }


