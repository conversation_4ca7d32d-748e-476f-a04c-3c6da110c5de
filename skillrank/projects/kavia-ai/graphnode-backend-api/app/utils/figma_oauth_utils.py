# app/utils/figma_oauth_utils.py

import requests

from typing import Dict, List, Optional, Any

from app.core.Settings import settings
import httpx
import json
import urllib.parse 

def get_figma_client_details():
    """Load Figma OAuth details from settings."""
    from app.core.Settings import settings
    
    FIGMA_CLIENT_ID = settings.FIGMA_CLIENT_ID
    FIGMA_CLIENT_SECRET = settings.FIGMA_CLIENT_SECRET
    FIGMA_REDIRECT_URI = settings.FIGMA_REDIRECT_URI
    
    # Validate that all required settings are set
    if not FIGMA_CLIENT_ID:
        raise ValueError("FIGMA_CLIENT_ID setting is not configured")
    if not FIGMA_CLIENT_SECRET:
        raise ValueError("FIGMA_CLIENT_SECRET setting is not configured")
    if not FIGMA_REDIRECT_URI:
        raise ValueError("FIGMA_REDIRECT_URI setting is not configured")
    
    return FIGMA_CLIENT_ID, FIGMA_CLIENT_SECRET, FIGMA_REDIRECT_URI








def exchange_figma_code_for_token(code: str, code_verifier: str = None) -> Optional[Dict[str, Any]]:
    """Exchange authorization code for access token"""
    try:
        FIGMA_CLIENT_ID, FIGMA_CLIENT_SECRET, FIGMA_REDIRECT_URI = get_figma_client_details()
        
        # Use correct token endpoint URL
        token_url = settings.FIGMA_TOKEN_URL 
        
        # Build payload - ensure all required fields are present
        payload = {
            "client_id": FIGMA_CLIENT_ID,
            "client_secret": FIGMA_CLIENT_SECRET,
            "code": code,
            "redirect_uri": FIGMA_REDIRECT_URI,
            "grant_type": "authorization_code"
        }
        
        print(f"Token exchange - Payload: {payload}")
        
        # Add PKCE code_verifier if provided
        if code_verifier:
            payload["code_verifier"] = code_verifier
        
        # Use proper headers for form-encoded data
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json",
            "User-Agent": "KaviaApp/1.0"
        }
        
        print(f"Token exchange - URL: {token_url}")
        print(f"Token exchange - Client ID: {FIGMA_CLIENT_ID}")
        print(f"Token exchange - Redirect URI: {FIGMA_REDIRECT_URI}")
        print(f"Token exchange - Using PKCE: {code_verifier is not None}")
        
        # Use proper form encoding
        response = requests.post(
            token_url,
            headers=headers,
            data=urllib.parse.urlencode(payload),  # Now urllib is imported!
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"Token exchange failed: {response.status_code} - {response.text}")
            return None
        
        try:
            response_json = response.json()
            if "error" in response_json:
                print(f"OAuth error: {response_json}")
                return None
            
            # Handle the actual response structure
            return {
                "access_token": response_json.get("access_token"),
                "refresh_token": response_json.get("refresh_token"),
                "expires_in": response_json.get("expires_in"),
                "user_id": response_json.get("user_id")
            }
        except ValueError as e:
            print(f"Failed to parse JSON: {response.text}")
            return None
            
    except Exception as e:
        print(f"Exception during token exchange: {str(e)}")
        return None

def get_figma_user_info(access_token: str):
    """Get user information from Figma."""
    try:
        user_info_url = "https://api.figma.com/v1/me"
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(user_info_url, headers=headers)
        
        # Check if response is successful
        if response.status_code != 200:
            print(f"Failed to get Figma user info with status {response.status_code}: {response.text}")
            return None
        
        # Try to parse JSON response
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError as e:
            print(f"Failed to parse JSON response from Figma user info: {response.text}")
            return None
            
    except Exception as e:
        print(f"Exception during Figma user info request: {str(e)}")
        return None





def validate_figma_token(access_token: str) -> bool:
    """Validate if Figma access token is still valid"""
    try:
        if not access_token:
            print("Token validation failed: No access token provided")
            return False
            
        headers = {
            'Authorization': f'Bearer {access_token}',
            'X-Figma-Token': access_token
        }
        
        print(f"Validating token: {access_token[:10]}...")
        
        response = requests.get(
            'https://api.figma.com/v1/me',
            headers=headers,
            timeout=10.0
        )
        
        print(f"Token validation response status: {response.status_code}")
        
        if response.status_code == 200:
            print("Token validation successful")
            return True
        else:
            print(f"Token validation failed: {response.status_code} - {response.text}")
            return False
        
    except Exception as e:
        print(f"Token validation exception: {str(e)}")
        return False


 