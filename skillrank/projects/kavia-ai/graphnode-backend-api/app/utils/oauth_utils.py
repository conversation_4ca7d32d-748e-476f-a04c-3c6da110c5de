from datetime import datetime
from typing import Any, Dict, List, Optional
import requests
import os
from dotenv import load_dotenv

load_dotenv()

def get_github_client_details():
    """Load GitHub OAuth details from environment variables."""
    GITHUB_CLIENT_ID = os.getenv("GITHUB_CLIENT_ID")
    GITHUB_CLIENT_SECRET = os.getenv("GITHUB_CLIENT_SECRET")
    GITHUB_REDIRECT_URI = os.getenv("GITHUB_REDIRECT_URI")
    print(GITHUB_CLIENT_SECRET)
    
    return GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET, GITHUB_REDIRECT_URI

def exchange_github_code_for_token(code: str):
    """Exchange authorization code for an access token."""
    GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET, GITHUB_REDIRECT_URI = get_github_client_details()
    
    token_url = "https://github.com/login/oauth/access_token"
    headers = {"Accept": "application/json"}
    payload = {
        "client_id": GITHUB_CLIENT_ID,
        "client_secret": GITHUB_CLIENT_SECRET,
        "code": code,
        "redirect_uri": GITHUB_REDIRECT_URI,
    }

    # print(payload)
    response = requests.post(token_url, headers=headers, data=payload)
    response_json = response.json()
    return response_json.get("access_token")

def get_user_info(access_token: str):
    """Get user information from GitHub."""
    user_info_url = "https://api.github.com/user"
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(user_info_url, headers=headers)
    return response.json()

def get_user_repos(token: str):
    """Get list of repositories for authenticated user."""
    all_repos = []
    page = 1
    per_page = 100  # Maximum allowed by GitHub
    
    while True:
        repo_url = f"https://api.github.com/user/repos?per_page={per_page}&page={page}"
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(repo_url, headers=headers)
        
        if response.status_code != 200:
            return None
            
        repos = response.json()
        if not repos:  # No more repos to fetch
            break
            
        all_repos.extend(repos)
        page += 1
    
    return all_repos


def validate_token_expiry(token: str) -> bool:
    """
    Check if GitLab token is valid and not expired.
    
    Args:
        token: GitLab personal access token or OAuth token
        
    Returns:
        bool: True if token is valid and not expired, False otherwise
    """
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Use the /user endpoint to validate token and get expiry info
        response = requests.get("https://gitlab.com/api/v4/user", headers=headers, timeout=10)
        
        if response.status_code != 200:
            return False
            
        # For personal access tokens, check via /personal_access_tokens endpoint
        token_response = requests.get(
            "https://gitlab.com/api/v4/personal_access_tokens", 
            headers=headers, 
            timeout=10
        )
        
        if token_response.status_code == 200:
            tokens = token_response.json()
            if tokens:
                # Find current token (it will be the active one)
                current_token = tokens[0] if tokens else None
                if current_token and current_token.get('expires_at'):
                    expiry_date = datetime.fromisoformat(
                        current_token['expires_at'].replace('Z', '+00:00')
                    )
                    return datetime.now(expiry_date.tzinfo) < expiry_date
        
        # If we can't get expiry info but token works, assume it's valid
        return True
        
    except (requests.RequestException, ValueError, KeyError):
        return False


def get_user_gitlab_repos(token: str) -> Optional[List[Dict[str, Any]]]:
    """
    Get list of repositories for authenticated GitLab user.
    First validates token expiry, then fetches all repositories if valid.
    
    Args:
        token: GitLab personal access token or OAuth token
        
    Returns:
        List of formatted repository dictionaries if successful, None if token invalid/expired or error
    """
    # Check token validity and expiry first
    if not validate_token_expiry(token):
        print("Token is invalid or expired")
        return None
    
    all_repos = []
    page = 1
    per_page = 100  # Maximum allowed by GitLab
    headers = {"Authorization": f"Bearer {token}"}
    
    while True:
        # GitLab API endpoint for user's projects (repositories)
        repo_url = (
            f"https://gitlab.com/api/v4/projects?"
            f"membership=true&per_page={per_page}&page={page}&order_by=updated_at"
        )
        
        try:
            response = requests.get(repo_url, headers=headers, timeout=30)
            
            if response.status_code != 200:
                print(f"Failed to fetch repositories: HTTP {response.status_code}")
                return None
                
            repos = response.json()
            
            # No more repos to fetch
            if not repos:
                break
                
            # Transform each repository to match the expected format
            for repo in repos:
                formatted_repo = {
                    "id": repo.get("id"),
                    "name": repo.get("name", ""),
                    "description": bool(repo.get("description")),  # Convert to boolean
                    "languages": [],  # GitLab API requires separate call for languages
                    "selected": False,  # Default value
                    "path": repo.get("web_url", ""),
                    "lastUpdated": repo.get("updated_at", ""),
                    "branch": repo.get("default_branch", "main")
                }
                
                if repo.get('private') == 'private':
                    formatted_repo['visibility'] = 'private'
                else:
                    formatted_repo['visibility'] = 'public'
                    
                all_repos.append(formatted_repo)
            
            # Check if we've reached the last page
            total_pages = response.headers.get('X-Total-Pages')
            if total_pages and page >= int(total_pages):
                break
                
            page += 1
            
        except requests.RequestException as e:
            print(f"Request failed: {e}")
            return None
        except (ValueError, KeyError) as e:
            print(f"Failed to parse response: {e}")
            return None
    
    return all_repos


def get_user_gitlab_repos_optimized(token: str) -> Optional[List[Dict[str, Any]]]:
    """
    Optimized version that fetches repositories with minimal required fields.
    
    Args:
        token: GitLab personal access token or OAuth token
        
    Returns:
        List of formatted repository dictionaries if successful, None if token invalid/expired or error
    """
    if not validate_token_expiry(token):
        print("Token is invalid or expired")
        return None
    
    all_repos = []
    page = 1
    per_page = 100
    headers = {"Authorization": f"Bearer {token}"}
    
    while True:
        repo_url = (
            f"https://gitlab.com/api/v4/projects?"
            f"membership=true&per_page={per_page}&page={page}"
            f"&order_by=updated_at&simple=true"
        )
        
        try:
            response = requests.get(repo_url, headers=headers, timeout=30)
            
            if response.status_code != 200:
                return None
                
            repos = response.json()
            if not repos:
                break
            
            # Transform each repository to match the expected format
            for repo in repos:
                formatted_repo = {
                    "id": repo.get("id"),
                    "name": repo.get("name", ""),
                    "description": bool(repo.get("description")),
                    "languages": [],
                    "selected": False,
                    "path": repo.get("web_url", ""),
                    "lastUpdated": repo.get("updated_at", ""),
                    "branch": repo.get("default_branch", "main")
                }
                all_repos.append(formatted_repo)
                
            # Use Link header for more efficient pagination
            link_header = response.headers.get('Link', '')
            if 'rel="next"' not in link_header:
                break
                
            page += 1
            
        except (requests.RequestException, ValueError, KeyError):
            return None
    