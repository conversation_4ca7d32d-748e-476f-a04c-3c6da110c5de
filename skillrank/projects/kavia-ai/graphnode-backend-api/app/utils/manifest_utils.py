from app.connection.establish_db_connection import get_mongo_db, get_node_db
from app.utils.code_generation_utils import yaml_to_json, json_to_yaml
from app.utils.batch_utils import get_container_type
from code_generation_core_agent.project_schemas import ContainerType
from app.core.constants import R<PERSON><PERSON><PERSON><PERSON>IES_COLLECTION
import json
from app.core.Settings import settings
from app.utils.datetime_utils import generate_timestamp
from typing import List, Dict, Any
import copy
from app.connection.establish_db_connection import NodeDB


def parse_env_from_container_properties(env_value):
    """Parse env field from container properties"""
    if isinstance(env_value, dict):
        return env_value
    elif isinstance(env_value, str) and env_value.strip():
        try:
            return json.loads(env_value)
        except json.JSONDecodeError:
            print(f"Warning: Invalid JSON in env field: {env_value}")
            return {}
    else:
        return {}
        
def deep_merge_dicts(existing: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
    """
    Perform a deep merge of dictionaries, updating only changed values.
    Special handling for env and routes fields.
    """
    result = copy.deepcopy(existing)
    
    for key, value in updates.items():
        if key not in result:
            result[key] = value
        elif key in ["env", "routes"]:
            # For env and routes fields, replace entirely to handle deletions/reordering
            result[key] = value
        elif isinstance(value, dict) and isinstance(result[key], dict):
            result[key] = deep_merge_dicts(result[key], value)
        elif isinstance(value, list) and isinstance(result[key], list):
            # Handle list merging based on context
            if key == "containers":
                result[key] = merge_containers_with_deletions(result[key], value)
            else:
                result[key] = value  # Replace list entirely for non-container lists
        else:
            # Only update if values are different
            if result[key] != value:
                result[key] = value
                
    return result

def merge_containers_with_deletions(existing_containers: List[Dict[str, Any]], 
                                   updated_containers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Merge container lists by container_name, ONLY keeping containers that exist in updated_containers.
    This properly handles deletions - if a container is not in updated_containers, it's removed.
    """
    # Create lookup maps for efficient access
    existing_map = {container.get("container_name"): container 
                   for container in existing_containers}
    updated_map = {container.get("container_name"): container 
                  for container in updated_containers}
    
    result = []
    
    # ONLY process containers that are in the updated list (this handles deletions)
    for container_name in updated_map.keys():
        if container_name in existing_map:
            # Merge existing container with updates
            existing_container = existing_map[container_name]
            updated_container = updated_map[container_name]
            
            merged_container = copy.deepcopy(existing_container)
            
            # Apply updates with special handling for env and routes
            for key, value in updated_container.items():
                if key in ["env", "routes"]:
                    # Replace entirely to handle deletions/reordering
                    merged_container[key] = value
                elif key not in merged_container or merged_container[key] != value:
                    merged_container[key] = value
            
            result.append(merged_container)
        else:
            # New container
            result.append(updated_map[container_name])
    
    return result


def merge_containers(existing_containers: List[Dict[str, Any]], 
                    updated_containers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Merge container lists by container_name, updating only changed containers.
    Special handling for env and routes fields.
    """
    # Create lookup maps for efficient access
    existing_map = {container.get("container_name"): container 
                   for container in existing_containers}
    updated_map = {container.get("container_name"): container 
                  for container in updated_containers}
    
    result = []
    
    # Process all container names (existing + new)
    all_container_names = set(existing_map.keys()) | set(updated_map.keys())
    
    for container_name in all_container_names:
        if container_name in updated_map:
            if container_name in existing_map:
                # Merge existing container with updates
                existing_container = existing_map[container_name]
                updated_container = updated_map[container_name]
                
                merged_container = copy.deepcopy(existing_container)
                
                # Apply updates with special handling for env and routes
                for key, value in updated_container.items():
                    if key in ["env", "routes"]:
                        # Replace entirely to handle deletions/reordering
                        merged_container[key] = value
                    elif key not in merged_container or merged_container[key] != value:
                        merged_container[key] = value
                
                result.append(merged_container)
            else:
                # New container
                result.append(updated_map[container_name])
        else:
            # Keep existing container unchanged
            result.append(existing_map[container_name])
    
    return result

def save_manifest_to_history(project_id: int, current_manifest: Dict[str, Any], node_db: NodeDB) -> None:
    """
    Save current manifest to history before updating with new one.
    Maintains last 25 versions in manifest_history array.
    """
    try:
        # Create history entry
        history_entry = {
            "timestamp": generate_timestamp(),
            "manifest": current_manifest,
            "version": len(current_manifest.get("containers", [])) # Simple version based on container count
        }
        
        # Get current project
        project = node_db.get_node_by_id(project_id)
        if not project:
            print(f"Project {project_id} not found for history saving")
            return
            
        project_properties = project.get("properties", {})
        current_history = project_properties.get("manifest_history", [])
        
        # Add new entry to beginning of history
        current_history.insert(0, history_entry)
        
        # Keep only last 25 versions
        if len(current_history) > 25:
            current_history = current_history[:25]
        
        # Save updated history (this will be saved along with the manifest update)
        return current_history
        
    except Exception as e:
        print(f"Error saving manifest to history: {str(e)}")
        return []


async def save_manifest_to_history_async(project_id: int, current_manifest: Dict[str, Any], node_db: NodeDB) -> List[Dict[str, Any]]:
    """
    Async version of save_manifest_to_history
    """
    try:
        # Create history entry - serialize manifest to JSON string for Neo4j compatibility
        history_entry = {
            "timestamp": generate_timestamp(),
            "manifest": json.dumps(current_manifest),  # Serialize to JSON string
            "version": len(current_manifest.get("containers", [])) # Simple version based on container count
        }
        
        # Get current project
        project = await node_db.get_node_by_id(project_id)
        if not project:
            print(f"Project {project_id} not found for history saving")
            return []
            
        project_properties = project.get("properties", {})
        manifest_history_str = project_properties.get("manifest_history", "[]")
        
        # Parse existing history (handle both string and list formats)
        try:
            if isinstance(manifest_history_str, str):
                current_history = json.loads(manifest_history_str)
            else:
                current_history = manifest_history_str
        except (json.JSONDecodeError, TypeError):
            current_history = []
        
        # Add new entry to beginning of history
        current_history.insert(0, history_entry)
        
        # Keep only last 25 versions
        if len(current_history) > 25:
            current_history = current_history[:25]
        
        return current_history
        
    except Exception as e:
        print(f"Error saving manifest to history: {str(e)}")
        return []

def has_meaningful_changes(existing: Dict[str, Any], updates: Dict[str, Any]) -> bool:
    """
    Check if there are meaningful changes between existing and updated data.
    
    Args:
        existing: Existing manifest data
        updates: Updated manifest data
        
    Returns:
        True if there are changes, False otherwise
    """
    def normalize_for_comparison(data):
        """Normalize data for comparison (handle None, empty strings, etc.)"""
        if isinstance(data, dict):
            return {k: normalize_for_comparison(v) for k, v in data.items() 
                   if v is not None and v != ""}
        elif isinstance(data, list):
            return [normalize_for_comparison(item) for item in data]
        return data
    
    normalized_existing = normalize_for_comparison(existing)
    normalized_updates = normalize_for_comparison(updates)
    
    return normalized_existing != normalized_updates



def _get_container_dependancy(container_type, container_name):
    if container_type.lower() in 'backend':
        return [container_name+'_'+'database']
    if container_type.lower() in 'frontend':
        return [container_name+'_'+'backend']
    if container_type.lower() in 'database':
        return [container_name+'_'+'backend']
    return []
    
def _def_interfaces(container_type):
    if container_type.lower() in 'backend':
        return "API endpoints based of the project description"
    if container_type.lower() in 'frontend':
        return "API requests to the backend container"
    if container_type.lower() in 'database':
        return "Database Accessible by the backend for data persistence"
    return ""
    
async def get_project_manifest_for_generation(project_id: int, container_ids =[], node_db=None):
    
    project_node = await node_db.get_node_by_id(project_id)
    project_properties = project_node.get("properties", {})
    print(f"Project Properties: {project_properties}")
    project_schema = {
            "overview": {
                "project_name": project_properties.get("Title", ""),
                "description": project_properties.get("Description", ""),
                "third_party_services": []
            },
            "containers": []
        }
    
    ports_to_map = {
        "frontend": settings.FRONTEND_PORT,
        "backend": settings.BACKEND_PORT,
        "database": settings.DATABASE_PORT,
    }
    
    def get_container_port(container):
        container_type = get_container_type(container)
        if container_type == ContainerType.FRONTEND.value:
            return settings.FRONTEND_PORT
        elif container_type == ContainerType.BACKEND.value:
            return settings.BACKEND_PORT
        elif container_type == ContainerType.DATABASE.value:
            return settings.DATABASE_PORT
        elif container_type == ContainerType.MOBILE.value:
            return None
        
        return ports_to_map.pop(ContainerType.FRONTEND.value, 0)

        
    for container_id in container_ids:
        container_id = int(container_id)
        container_node = await node_db.get_node_by_id(container_id)
        container_properties = container_node.get("properties", {})
        container_name = container_properties.get("Title").replace(' ','').replace('-','_')
        container_type = get_container_type(container_properties)
        container = {
            "container_name": container_name,
            "description": container_properties.get("Description", ""),
            "interfaces": _def_interfaces(container_type),
            "container_type": container_type,
            "dependent_containers": _get_container_dependancy(get_container_type(container_properties), container_name),
            "workspace": "",
            "container_root":  "",
            "port": get_container_port(container_properties),
            "framework": container_properties.get("framework", ""),
            "type": container_properties.get("type", ""),
            "buildCommand": container_properties.get("buildCommand", ""),
            "startCommand": container_properties.get("startCommand", ""),
            "installCommand": container_properties.get("installCommand", ""),
            "lintCommand": container_properties.get("lintCommand", ""),
            "working_dir": container_properties.get("working_dir", ""),
            "container_details": {'features': container_properties.get("UserInteractions","")},
            "lintConfig": container_properties.get("lintConfig", ""),
            "routes": container_properties.get("routes", []),
            "apiSpec": container_properties.get("apiSpec", ""),
            "auth": container_properties.get("auth"),
            "schema": container_properties.get("schema", ""),
            "migrations": container_properties.get("migrations", ""),
            "seed": container_properties.get("seed", ""),
            "env": container_properties.get("env", {}),
            "private": container_properties.get("private", {})
            }
        
        if container['container_type'] == 'mobile':
            container.pop('port', None)
        project_schema['containers'].append(container)

    return json.dumps(project_schema)

async def get_project_manifest_for_maintenance(project_id: int, repository_ids = [], all_repositories=False, node_db=None):
    mongo_db = get_mongo_db().db
    
    if not node_db:
        node_db = get_node_db()
    
    project = await node_db.get_node_by_id(project_id)
    project_details = project.get("properties")
    
    project_repos = mongo_db[REPOSITORIES_COLLECTION].find_one({
            "project_id": project_id
        }
        )
    project_manifest = project_repos.get("project_manifest", "")
    
    if project_manifest and not repository_ids:
        return project_manifest
    
    if repository_ids or all_repositories:
        container_manifests = []
        third_party_services = []
        for repository in project_repos.get("repositories"):
            if (repository.get("repo_id") in repository_ids) or all_repositories:
                current_manifest = repository.get("project_manifest")
                if current_manifest:
                    container_manifest = yaml_to_json(current_manifest)
                    containers = container_manifest.get("containers", [])
                    third_party_services.extend(container_manifest.get("third_party_services", []))
                    container_manifests.extend(containers)
                    
    if not container_manifests:
        return ""
    final_manifest = {
        "overview" : {
            "project_name" : project_details.get("Title", ""),
            "description" : project_details.get("Description", ""),
            "third_party_services" : list(set(third_party_services))
        },
        "containers" : container_manifests
    }
    
    return json_to_yaml(json.dumps(final_manifest))

    