"""
Utility for creating repositories in kavia-common organization from public GitHub/GitLab URLs.
"""

import re
import asyncio
import logging
import tempfile
import shutil
import os
from typing import Tuple, Dict, Any, Optional
from urllib.parse import urlparse
from github import Github
import gitlab
import httpx
import git

from app.core.Settings import settings
from app.utils.datetime_utils import generate_timestamp


def extract_branch_from_url(url: str) -> Optional[str]:
    """
    Extract branch name from repository URL if present.

    Args:
        url: Repository URL that may contain branch information

    Returns:
        Branch name if found in URL, None otherwise
    """
    # Look for /tree/branch_name pattern (GitHub/GitLab)
    tree_match = re.search(r'/tree/([^/]+)', url)
    if tree_match:
        return tree_match.group(1)

    # Look for /-/tree/branch_name pattern (GitLab)
    gitlab_tree_match = re.search(r'/-/tree/([^/]+)', url)
    if gitlab_tree_match:
        return gitlab_tree_match.group(1)

    return None


def parse_repository_url(url: str) -> Tuple[str, str, str]:
    """
    Parse a GitHub or GitLab repository URL to extract owner, repo_name, and platform.

    Args:
        url: Repository URL (e.g., https://github.com/owner/repo, https://gitlab.com/owner/repo)

    Returns:
        Tuple of (owner, repo_name, platform) where platform is 'github' or 'gitlab'

    Raises:
        ValueError: If the URL format is not supported
    """
    # Clean the URL - remove trailing slashes, .git extension, and tree/branch paths
    clean_url = url.strip().rstrip('/')

    # Remove .git extension if present
    if clean_url.endswith('.git'):
        clean_url = clean_url[:-4]

    # Remove various tree/branch path patterns
    patterns_to_remove = [
        r'/-/tree/[^/]+.*$',    # GitLab: /-/tree/branch_name (check first)
        r'/-/blob/[^/]+.*$',    # GitLab: /-/blob/branch_name/file
        r'/-/commits/[^/]+.*$', # GitLab: /-/commits/branch_name
        r'/tree/[^/]+.*$',      # GitHub: /tree/branch_name
        r'/blob/[^/]+.*$',      # GitHub: /blob/branch_name/file
        r'/commits/[^/]+.*$',   # GitHub: /commits/branch_name
    ]

    for pattern in patterns_to_remove:
        clean_url = re.sub(pattern, '', clean_url)
    
    # Parse the URL
    parsed = urlparse(clean_url)
    
    # Determine platform
    platform = None
    host = parsed.netloc.lower()
    path_for_detection = parsed.path.lower()
    if 'github.com' in host:
        platform = 'github'
    elif 'gitlab.com' in host:
        platform = 'gitlab'
    elif host.endswith('googlesource.com') or 'gerrit' in host or host.startswith('review.') or 'review' in host or re.search(r'/(?:a/)?(?:r/|plugins/gitiles/|admin/repos/)', path_for_detection):
        # Generic Gerrit detection for various hosts and common Gerrit path prefixes
        platform = 'gerrit'
    else:
        raise ValueError(f"Unsupported platform. Only GitHub, GitLab, and Gerrit are supported. Got: {parsed.netloc}")
    
    # Extract path components
    path_parts = [part for part in parsed.path.split('/') if part]

    if platform == 'gerrit':
        # Strip common Gerrit prefixes
        parts = path_parts[:]
        if parts and parts[0] in {'a', 'r'}:
            parts = parts[1:]
        if len(parts) >= 2 and parts[0] == 'plugins' and parts[1] == 'gitiles':
            parts = parts[2:]
        if len(parts) >= 2 and parts[0] == 'admin' and parts[1] == 'repos':
            parts = parts[2:]

        if len(parts) < 1:
            raise ValueError(f"Invalid Gerrit repository URL format. Expected format: https://host/group/project or https://host/r/group/project. Got: {url}")

        if len(parts) == 1:
            owner = ''
            repo_name = parts[0]
        else:
            repo_name = parts[-1]
            owner = '/'.join(parts[:-1])
    else:
        if len(path_parts) < 2:
            raise ValueError(f"Invalid repository URL format. Expected format: https://platform.com/owner/repo. Got: {url}")

        # For GitLab, handle nested groups (e.g., gitlab.com/group/subgroup/project)
        # The last component is always the repository name
        repo_name = path_parts[-1]

        # Everything before the last component is the owner/namespace
        if platform == 'gitlab' and len(path_parts) > 2:
            # For GitLab nested groups: gitlab.com/group/subgroup/project
            owner = '/'.join(path_parts[:-1])
        else:
            # For GitHub or simple GitLab: platform.com/owner/repo
            owner = path_parts[0]
    
    return owner, repo_name, platform


async def get_repository_details(url: str, platform: str, owner: str, repo_name: str) -> Dict[str, Any]:
    """
    Get repository details from GitHub or GitLab API.
    
    Args:
        url: Original repository URL
        platform: 'github' or 'gitlab'
        owner: Repository owner/namespace
        repo_name: Repository name
        
    Returns:
        Dictionary containing repository details
    """
    if platform == "github":
        # Use GitHub API
        github_url = f"https://api.github.com/repos/{owner}/{repo_name}"
        headers = {"Authorization": f"Bearer {settings.GITHUB_ACCESS_TOKEN}"}
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(github_url, headers=headers)
            
            if response.status_code == 404:
                raise ValueError(f"Repository {owner}/{repo_name} not found on GitHub")
            
            response.raise_for_status()
            return response.json()
            
    elif platform == "gitlab":
        # Use GitLab API
        project_path = f"{owner}/{repo_name}".replace('/', '%2F')
        gitlab_instance = urlparse(url).netloc
        gitlab_url = f"https://{gitlab_instance}/api/v4/projects/{project_path}"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(gitlab_url)
            
            if response.status_code == 404:
                raise ValueError(f"Repository {owner}/{repo_name} not found on {gitlab_instance}")
            
            response.raise_for_status()
            gitlab_data = response.json()
            
            # Convert GitLab format to GitHub-compatible format
            return {
                "id": gitlab_data.get("id"),
                "name": gitlab_data.get("name"),
                "full_name": gitlab_data.get("path_with_namespace"),
                "description": gitlab_data.get("description"),
                "html_url": gitlab_data.get("web_url"),
                "clone_url": gitlab_data.get("http_url_to_repo"),
                "default_branch": gitlab_data.get("default_branch"),
                "created_at": gitlab_data.get("created_at"),
                "updated_at": gitlab_data.get("last_activity_at"),
                "stargazers_count": gitlab_data.get("star_count", 0),
                "forks_count": gitlab_data.get("forks_count", 0),
                "visibility": gitlab_data.get("visibility"),
                "private": gitlab_data.get("visibility") == "private",
            }
    elif platform == "gerrit":
        # Use Gerrit API with Gitiles fallback
        gerrit_instance = urlparse(url).netloc
        project_for_url = f"{owner}/{repo_name}" if owner else repo_name
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Try Gitiles first (public)
            gitiles_url = f"https://{gerrit_instance}/{project_for_url}/+refs?format=JSON"
            try:
                gitiles_resp = await client.get(gitiles_url)
                if gitiles_resp.status_code == 200:
                    text = gitiles_resp.text
                    if text.startswith(")]}'"):
                        newline_index = text.find('\n')
                        text = text[newline_index + 1:] if newline_index != -1 else ''
                    
                    data = json.loads(text)
                    # Find default branch (usually HEAD points to default)
                    default_branch = None
                    if 'HEAD' in data and 'target' in data['HEAD']:
                        head_ref = data['HEAD']['target']
                        if head_ref.startswith('refs/heads/'):
                            default_branch = head_ref[len('refs/heads/'):]
                    
                    # Convert to GitHub-compatible format
                    return {
                        "id": hash(f"{owner}/{repo_name}"),  # Generate ID from path
                        "name": repo_name,
                        "full_name": f"{owner}/{repo_name}" if owner else repo_name,
                        "description": f"Gerrit repository: {project_for_url}",
                        "html_url": f"https://{gerrit_instance}/{project_for_url}",
                        "clone_url": f"https://{gerrit_instance}/{project_for_url}.git",
                        "default_branch": default_branch or "master",
                        "created_at": None,  # Gerrit doesn't provide creation time
                        "updated_at": None,  # Gerrit doesn't provide update time
                        "stargazers_count": 0,  # Gerrit doesn't have stars
                        "forks_count": 0,  # Gerrit doesn't have forks
                        "visibility": "public",
                        "private": False,
                    }
            except Exception:
                pass  # Fall back to REST API
            
            # Fallback to Gerrit REST API
            project_path = project_for_url.replace('/', '%2F')
            rest_url = f"https://{gerrit_instance}/projects/{project_path}"
            response = await client.get(rest_url)
            
            if response.status_code == 404:
                raise ValueError(f"Repository {owner}/{repo_name} not found on {gerrit_instance}")
            
            response.raise_for_status()
            text = response.text
            if text.startswith(")]}'"):
                newline_index = text.find('\n')
                text = text[newline_index + 1:] if newline_index != -1 else ''
            
            gerrit_data = json.loads(text)
            
            # Convert Gerrit format to GitHub-compatible format
            return {
                "id": gerrit_data.get("id", hash(f"{owner}/{repo_name}")),
                "name": gerrit_data.get("name", repo_name),
                "full_name": gerrit_data.get("path_with_namespace", f"{owner}/{repo_name}" if owner else repo_name),
                "description": gerrit_data.get("description", f"Gerrit repository: {project_for_url}"),
                "html_url": gerrit_data.get("web_url", f"https://{gerrit_instance}/{project_for_url}"),
                "clone_url": gerrit_data.get("http_url_to_repo", f"https://{gerrit_instance}/{project_for_url}.git"),
                "default_branch": gerrit_data.get("default_branch", "master"),
                "created_at": gerrit_data.get("created_at"),
                "updated_at": gerrit_data.get("last_activity_at"),
                "stargazers_count": 0,  # Gerrit doesn't have stars
                "forks_count": 0,  # Gerrit doesn't have forks
                "visibility": "public",
                "private": False,
            }
    
    raise ValueError(f"Unsupported platform: {platform}")


async def create_branch_github(
    repository_name: str,
    access_token: str,
    organization: str,
    source_branch: str,
    new_branch: str
) -> bool:
    """
    Create a new branch in GitHub repository using REST API.
    Simplified version to avoid circular imports.
    """
    try:
        headers = {
            "Authorization": f"token {access_token}",
            "Accept": "application/vnd.github+json",
            "X-GitHub-Api-Version": "2022-11-28"
        }

        async with httpx.AsyncClient() as client:
            # Get source branch SHA
            get_ref_url = f"https://api.github.com/repos/{organization}/{repository_name}/git/ref/heads/{source_branch}"
            get_response = await client.get(get_ref_url, headers=headers)

            if get_response.status_code != 200:
                print(generate_timestamp(), f"⚠️ Could not get source branch {source_branch}: {get_response.text}")
                return False

            source_sha = get_response.json()["object"]["sha"]

            # Create new branch
            create_ref_url = f"https://api.github.com/repos/{organization}/{repository_name}/git/refs"
            create_ref_data = {
                "ref": f"refs/heads/{new_branch}",
                "sha": source_sha
            }

            create_response = await client.post(create_ref_url, headers=headers, json=create_ref_data)

            if create_response.status_code in [200, 201]:
                print(generate_timestamp(), f"✅ Successfully created branch {new_branch}")
                return True
            elif create_response.status_code == 422 and "already exists" in create_response.text:
                print(generate_timestamp(), f"✅ Branch {new_branch} already exists")
                return True
            else:
                print(generate_timestamp(), f"⚠️ Failed to create branch {new_branch}: {create_response.text}")
                return False

    except Exception as e:
        print(generate_timestamp(), f"❌ Error creating branch: {str(e)}")
        return False


async def clone_and_push_repository(
    source_url: str,
    target_repo_url: str,
    target_branch: str,
    source_branch: str,
    access_token: str
) -> bool:
    """
    Clone specific branch from source repository and push to target repository as kavia-{branch}.

    Args:
        source_url: Source repository URL (GitHub or GitLab)
        target_repo_url: Target GitHub repository URL
        target_branch: Target branch name (kavia-{branch_name})
        source_branch: Source branch to clone from
        access_token: GitHub access token

    Returns:
        True if successful, False otherwise
    """
    temp_dir = None
    try:
        print(generate_timestamp(), f"🔄 Cloning source repository: {source_url}")

        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        source_dir = os.path.join(temp_dir, "source")

        # Clean source URL for cloning
        clean_source_url = source_url.strip().rstrip('/')
        if clean_source_url.endswith('.git'):
            clean_source_url = clean_source_url[:-4]

        # Remove tree/branch paths
        tree_pattern = r'/tree/[^/]+.*$'
        clean_source_url = re.sub(tree_pattern, '', clean_source_url)
        clone_url = f"{clean_source_url}.git"

        # Clone source repository with specific branch
        try:
            # Try to clone the specific branch first
            source_repo = git.Repo.clone_from(clone_url, source_dir, branch=source_branch, single_branch=True)
            print(generate_timestamp(), f"✅ Source repository cloned (branch: {source_branch}) to: {source_dir}")
        except Exception as e:
            print(generate_timestamp(), f"⚠️ Failed to clone specific branch {source_branch}, trying default: {str(e)}")
            # Fallback: clone default branch
            source_repo = git.Repo.clone_from(clone_url, source_dir)
            print(generate_timestamp(), f"✅ Source repository cloned (default branch) to: {source_dir}")

            # Try to checkout the specific branch
            try:
                source_repo.git.checkout(f'origin/{source_branch}', b=source_branch)
                print(generate_timestamp(), f"✅ Checked out branch: {source_branch}")
            except Exception as checkout_error:
                print(generate_timestamp(), f"⚠️ Could not checkout {source_branch}, using current branch: {str(checkout_error)}")

        # Prepare authenticated target URL
        auth_target_url = target_repo_url.replace('https://', f'https://{access_token}@')

        # Add target remote
        target_remote = source_repo.create_remote('target', auth_target_url)
        print(generate_timestamp(), f"🔗 Added target remote: {target_repo_url}")

        # Get current branch name
        current_branch = source_repo.active_branch.name
        print(generate_timestamp(), f"📋 Current branch: {current_branch}")

        # Push current branch to target as kavia-{branch_name}
        try:
            target_remote.push(f'{current_branch}:{target_branch}', force=True)
            print(generate_timestamp(), f"✅ Pushed {current_branch} as {target_branch}")
        except Exception as e:
            print(generate_timestamp(), f"⚠️ Failed to push branch: {str(e)}")
            raise

        print(generate_timestamp(), f"🎉 Successfully cloned and pushed repository content!")
        return True

    except Exception as e:
        print(generate_timestamp(), f"❌ Failed to clone and push repository: {str(e)}")
        return False

    finally:
        # Clean up temporary directory
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(generate_timestamp(), f"🧹 Cleaned up temporary directory")
            except Exception as e:
                print(generate_timestamp(), f"⚠️ Failed to clean up temp directory: {str(e)}")


async def create_kavia_repository(
    public_url: str, 
    branch_name: str = "main",
    project_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a repository in kavia-common organization from a public GitHub/GitLab URL.
    
    Args:
        public_url: Public repository URL (GitHub or GitLab)
        branch_name: Branch name to create (default: "main")
        project_id: Optional project ID to include in repository name
        
    Returns:
        Dictionary containing created repository metadata
        
    Raises:
        ValueError: If URL is invalid or repository not found
        Exception: If repository creation fails
    """
    try:
        print(generate_timestamp(), f"🚀 Creating Kavia repository from: {public_url}")
        
        # Parse the repository URL
        owner, repo_name, platform = parse_repository_url(public_url)
        print(generate_timestamp(), f"📋 Parsed: platform={platform}, owner={owner}, repo={repo_name}")

        # Extract branch from URL if present, otherwise use provided branch_name
        url_branch = extract_branch_from_url(public_url)
        if url_branch:
            actual_branch = url_branch
            print(generate_timestamp(), f"🌿 Branch detected from URL: {actual_branch}")
        else:
            actual_branch = branch_name
            print(generate_timestamp(), f"🌿 Using provided branch: {actual_branch}")
    
        # Generate repository name for kavia-common
        if project_id:
            kavia_repo_name = f"{repo_name}-{project_id}"
        
        print(generate_timestamp(), f"📝 Creating repository: {kavia_repo_name}")
        
        # Initialize GitHub client for kavia-common
        github_client = Github(settings.GITHUB_ACCESS_TOKEN)

        # Try to get as organization first, fallback to user account
        try:
            kavia_org = github_client.get_organization(settings.GITHUB_DEFAULT_ORGANIZATION)
            # Create repository in kavia-common organization
            kavia_repo = kavia_org.create_repo(
                name=kavia_repo_name,
                description=f"Kavia repository created from {public_url}",
                private=False,  # Public repository as requested
                auto_init=False  # Don't auto-initialize to avoid conflicts
            )
            print(generate_timestamp(), f"📁 Created repository in organization: {settings.GITHUB_DEFAULT_ORGANIZATION}")
        except Exception as org_error:
            print(generate_timestamp(), f"⚠️ Could not create in organization, trying user account: {str(org_error)}")
            # Fallback to creating under user account
            user = github_client.get_user()
            kavia_repo = user.create_repo(
                name=kavia_repo_name,
                description=f"Kavia repository created from {public_url}",
                private=False,  # Public repository as requested
                auto_init=False  # Don't auto-initialize to avoid conflicts
            )
            print(generate_timestamp(), f"📁 Created repository under user account: {user.login}")
        
        print(generate_timestamp(), f"✅ Repository created: {kavia_repo.html_url}")

        # Clone source repository and push to new GitHub repository
        clone_success = await clone_and_push_repository(
            source_url=public_url,
            target_repo_url=kavia_repo.clone_url,
            target_branch=f"kavia-{actual_branch}",
            source_branch=actual_branch,
            access_token=settings.GITHUB_ACCESS_TOKEN
        )

        if not clone_success:
            print(generate_timestamp(), f"⚠️ Failed to clone and push repository content")

        # Wait for repository to be fully initialized
        await asyncio.sleep(2)
        
        # Handle branch setup
        kavia_branch_name = f"kavia-{actual_branch}"

        if clone_success:
            # Repository has content from cloning, check if kavia branch exists
            try:
                # Refresh repository object to get latest state
                kavia_repo = github_client.get_repo(kavia_repo.full_name)

                # Check if kavia branch exists
                try:
                    kavia_repo.get_branch(kavia_branch_name)
                    print(generate_timestamp(), f"✅ Branch {kavia_branch_name} already exists from cloning")

                    # Set kavia-{branch_name} as default branch
                    try:
                        kavia_repo.edit(default_branch=kavia_branch_name)
                        print(generate_timestamp(), f"🎯 Set {kavia_branch_name} as default branch")
                    except Exception as e:
                        print(generate_timestamp(), f"⚠️ Could not set {kavia_branch_name} as default branch: {str(e)}")

                except Exception:
                    print(generate_timestamp(), f"⚠️ Branch {kavia_branch_name} not found, will use default branch")

            except Exception as e:
                print(generate_timestamp(), f"⚠️ Error checking branches: {str(e)}")
        else:
            # Fallback: create branch manually if cloning failed
            try:
                # Get the default branch (usually 'main' or 'master')
                default_branch = kavia_repo.default_branch or 'main'
                print(generate_timestamp(), f"📌 Default branch: {default_branch}")

                # Create kavia-{branch_name} branch from the default branch
                repo_owner = kavia_repo.owner.login
                await create_branch_github(
                    repository_name=kavia_repo_name,
                    access_token=settings.GITHUB_ACCESS_TOKEN,
                    organization=repo_owner,
                    source_branch=default_branch,
                    new_branch=kavia_branch_name
                )

                print(generate_timestamp(), f"🌿 Created branch: {kavia_branch_name}")

            except Exception as e:
                print(generate_timestamp(), f"⚠️ Could not create {kavia_branch_name} branch: {str(e)}")
        
        # Prepare repository metadata
        repository_metadata = {
            "status": "success",
            "message": "Repository created successfully in kavia-common" + (" with source content" if clone_success else " (content cloning failed)"),
            "content_cloned": clone_success,
            "original_url": public_url,
            "original_platform": platform,
            "original_owner": owner,
            "original_repo": repo_name,
            "kavia_repository": {
                "name": kavia_repo_name,
                "full_name": kavia_repo.full_name,
                "html_url": kavia_repo.html_url,
                "clone_url": kavia_repo.clone_url,
                "ssh_url": kavia_repo.ssh_url,
                "default_branch": kavia_repo.default_branch,
                "created_at": kavia_repo.created_at.isoformat(),
                "organization": kavia_repo.owner.login,
                "visibility": "public",
                "kavia_branch": kavia_branch_name
            }
        }
        
        print(generate_timestamp(), f"🎉 Repository creation completed successfully!")
        return repository_metadata
        
    except Exception as e:
        error_msg = f"Failed to create Kavia repository: {str(e)}"
        print(generate_timestamp(), f"❌ {error_msg}")
        return {
            "status": "error",
            "message": error_msg,
            "original_url": public_url,
            "error": str(e)
        }


# Test function
async def test_create_kavia_repository():
    """Test function for creating repository from GitLab URL"""
    test_url = "https://gitlab.com/coocook/coocook"
    test_branch = "master"

    # Add timestamp to make repository name unique
    import time
    timestamp = str(int(time.time()))[-6:]  # Last 6 digits of timestamp
    test_project_id = f"test-{timestamp}"

    print(generate_timestamp(), "🧪 Testing repository creation...")
    result = await create_kavia_repository(test_url, test_branch, test_project_id)
    
    if result["status"] == "success":
        print(generate_timestamp(), f"✅ Test successful!")
        print(generate_timestamp(), f"🔗 Repository URL: {result['kavia_repository']['html_url']}")
        print(generate_timestamp(), f"🌿 Branch: {result['kavia_repository']['kavia_branch']}")
    else:
        print(generate_timestamp(), f"❌ Test failed: {result['message']}")
    
    return result


if __name__ == "__main__":
    # Run test
    asyncio.run(test_create_kavia_repository())


# API endpoint integration
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter(
    prefix="/utils",
    tags=["Repository Utils"],
    responses={404: {"description": "Not found"}},
)

class CreateRepoRequest(BaseModel):
    public_url: str
    branch_name: str = "main"
    project_id: Optional[str] = None

@router.post("/create-kavia-repository")
async def create_kavia_repository_endpoint(request: CreateRepoRequest):
    """
    Create a repository in kavia-common from a public GitHub/GitLab URL.

    Args:
        request: Contains public_url, branch_name (default: "main"), and optional project_id

    Returns:
        Repository creation result with metadata

    Example:
        POST /utils/create-kavia-repository
        {
            "public_url": "https://gitlab.com/coocook/coocook",
            "branch_name": "main",
            "project_id": "12345"
        }
    """
    try:
        result = await create_kavia_repository(
            public_url=request.public_url,
            branch_name=request.branch_name,
            project_id=request.project_id
        )

        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["message"])

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create repository: {str(e)}"
        )
