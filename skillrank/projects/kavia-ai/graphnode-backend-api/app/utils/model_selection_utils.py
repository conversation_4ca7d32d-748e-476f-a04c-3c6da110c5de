from code_generation_core_agent.config import config as code_generation_config

def change_reasoning_effort(reasoning_effort: str):
    code_generation_config.set_runtime_config("LLM", "openai_reasoning_effort", reasoning_effort)
    code_generation_config.set("LLM", "openai_reasoning_effort", reasoning_effort)

def get_reasoning_effort():
    return  code_generation_config.get("LLM", "openai_reasoning_effort", fallback="low")