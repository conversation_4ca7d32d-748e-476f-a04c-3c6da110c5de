#!/usr/bin/env python3

import json
import re
from typing import Dict, List, Set, Any, Optional, Tuple, Union
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import httpx
import asyncio

class NodeType(Enum):
    FRAME = "FRAME"
    GROUP = "GROUP" 
    TEXT = "TEXT"
    RECTANGLE = "RECTANGLE"
    ELLIPSE = "ELLIPSE"
    VECTOR = "VECTOR"
    INSTANCE = "INSTANCE"
    REGULAR_POLYGON = "REGULAR_POLYGON"

class UIType(Enum):
    CONTAINER = "container"
    TEXT = "text"
    RECTANGLE = "rectangle"
    ELLIPSE = "ellipse"
    ICON = "icon"
    COMPONENT = "component"
    POLYGON = "polygon"
    BUTTON = "button"
    SLIDER = "slider"

@dataclass
class Color:
    name: str
    hex: str
    rgba: Optional[Dict[str, float]] = None

@dataclass
class Typography:
    family: str
    size: float
    weight: int
    line_height: Optional[float] = None
    letter_spacing: Optional[float] = None
    text_align: Optional[str] = None

@dataclass
class Component:
    name: str
    id: str
    description: str
    key: Optional[str] = None
    props: Optional[Dict[str, Any]] = None

@dataclass
class Shadow:
    type: str
    offset_x: float
    offset_y: float
    blur: float
    spread: float
    color: str

@dataclass
class Dimensions:
    x: float
    y: float
    width: float
    height: float

class OptimizedFigmaPreprocessor:
    """
    Optimized Figma JSON preprocessor with centralized design system references.
    Reduces context size by avoiding duplication and creating proper references.
    Enhanced with image URL support for all nodes.
    """
    
    def __init__(self, figma_json: Dict[str, Any], figma_api_key: str = None, file_key: str = None):
        self.figma_json = figma_json
        self.figma_api_key = figma_api_key
        self.file_key = file_key
        self.design_system = {
            'colors': {},
            'typography': {},
            'components': {},
            'styles': {},
            'spacing': set(),
            'border_radius': set(),
            'shadows': {}
        }
        
        # Reference registries for deduplication
        self.color_registry = {}
        self.typography_registry = {}
        self.style_registry = {}
        self.shadow_registry = {}
        
        # Frame registry for easy lookup
        self.frame_registry = {}
        
        # Image URL cache
        self.image_url_cache = {}
        
    async def fetch_all_node_images_async(self, node_ids: List[str]) -> Dict[str, str]:
        """
        Fetch image URLs for multiple nodes asynchronously using the same logic as figma_utils.
        """
        if not self.figma_api_key or not self.file_key or not node_ids:
            return {}
            
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                url = f"https://api.figma.com/v1/images/{self.file_key}"
                params = {
                    "ids": ",".join(node_ids),
                    "scale": 2,
                    "format": "png"
                }
                headers = {"X-Figma-Token": self.figma_api_key}

                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                images = response.json().get('images', {})
                
                # Cache the results
                self.image_url_cache.update(images)
                return images
                
        except Exception as e:
            print(f"Warning: Failed to fetch images for nodes: {str(e)}")
            return {}
    async def fetch_all_node_images_url_async(self, figma_api_key, file_key, node_ids: List[str]) -> Dict[str, str]:
        """
        Fetch image URLs for multiple nodes asynchronously using the same logic as figma_utils.
        """
        if not figma_api_key or not file_key or not node_ids:
            return {}
            
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                url = f"https://api.figma.com/v1/images/{file_key}"
                params = {
                    "ids": ",".join(node_ids),
                    "scale": 2,
                    "format": "png"
                }
                headers = {"X-Figma-Token": figma_api_key}

                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                images = response.json().get('images', {})
                
                # Cache the results
                self.image_url_cache.update(images)
                return images
                
        except Exception as e:
            print(f"Warning: Failed to fetch images for nodes: {str(e)}")
            return {}
    
    def collect_all_node_ids(self) -> List[str]:
        """
        Collect all node IDs from the Figma JSON that could potentially have images.
        """
        node_ids = []
        
        def traverse_and_collect(node: Dict[str, Any]):
            if not isinstance(node, dict):
                return
                
            node_id = node.get('id')
            if node_id:
                node_ids.append(node_id)
            
            # Recursively process children
            children = node.get('children', [])
            if isinstance(children, list):
                for child in children:
                    traverse_and_collect(child)
        
        # Start from document root
        if 'document' in self.figma_json:
            traverse_and_collect(self.figma_json['document'])
        
        return node_ids
    
    def extract_design_system_only(self) -> Dict[str, Any]:
        """Extract only the design system without processing screens, but capture all design tokens."""
        # First extract from styles and components
        self._extract_design_system()
        
        # Then extract all design tokens from all frames (including those we might process later)
        # This ensures we capture the same tokens as the original full processing
        self._extract_all_frame_design_tokens()
        
        return {
            'design_system': self._build_design_system(),
            'metadata': {
                'total_colors': len(self.design_system['colors']),
                'total_typography_styles': len(self.design_system['typography']),
                'total_components': len(self.design_system['components']),
                'total_styles': len(self.design_system['styles']),
                'generated_at': datetime.now().isoformat(),
                'optimization_stats': self._get_optimization_stats()
            }
        }
    
    async def process_screens_by_frame_ids_with_images(self, frame_ids: List[str], skip_design_system_extraction: bool = False) -> List[Dict[str, Any]]:
        """
        Process specific screens by their frame IDs, referencing the design system and including image URLs.
        """
        # Build frame registry if not already built
        if not self.frame_registry:
            self._build_frame_registry()
        
        # Only extract design system if not already done
        if not skip_design_system_extraction and not self.design_system['colors']:
            self._extract_design_system()
            self._extract_all_frame_design_tokens()
        
        # Collect all node IDs that we'll need images for
        all_node_ids = []
        for frame_id in frame_ids:
            if frame_id in self.frame_registry:
                frame = self.frame_registry[frame_id]
                frame_node_ids = self._collect_node_ids_from_frame(frame)
                all_node_ids.extend(frame_node_ids)
        
        # Fetch all images at once if API key is available
        if self.figma_api_key and self.file_key and all_node_ids:
            print(f"Fetching images for {len(all_node_ids)} nodes...")
            await self.fetch_all_node_images_async(all_node_ids)
        
        # Process screens with image URLs
        screens = []
        for frame_id in frame_ids:
            if frame_id in self.frame_registry:
                frame = self.frame_registry[frame_id]
                screen = self._process_screen_by_frame_id_with_images(frame, frame_id)
                screens.append(screen)
            else:
                print(f"Warning: Frame ID '{frame_id}' not found in Figma data")
        
        return screens
    
    def process_screens_by_frame_ids(self, frame_ids: List[str], skip_design_system_extraction: bool = False) -> List[Dict[str, Any]]:
        """
        Original method maintained for backward compatibility.
        For new implementations, use process_screens_by_frame_ids_with_images.
        """
        # Build frame registry if not already built
        if not self.frame_registry:
            self._build_frame_registry()
        
        # Only extract design system if not already done
        if not skip_design_system_extraction and not self.design_system['colors']:
            self._extract_design_system()
            self._extract_all_frame_design_tokens()
        
        screens = []
        for frame_id in frame_ids:
            if frame_id in self.frame_registry:
                frame = self.frame_registry[frame_id]
                screen = self._process_screen_by_frame_id(frame, frame_id)
                screens.append(screen)
            else:
                print(f"Warning: Frame ID '{frame_id}' not found in Figma data")
        
        return screens
    
    def _collect_node_ids_from_frame(self, frame: Dict[str, Any]) -> List[str]:
        """
        Collect all node IDs from a frame and its children.
        """
        node_ids = []
        
        def traverse_frame(node: Dict[str, Any]):
            if not isinstance(node, dict):
                return
                
            node_id = node.get('id')
            if node_id:
                node_ids.append(node_id)
            
            # Recursively process children
            children = node.get('children', [])
            if isinstance(children, list):
                for child in children:
                    if child.get('visible', True):  # Only visible nodes
                        traverse_frame(child)
        
        traverse_frame(frame)
        return node_ids
    
    def _process_screen_by_frame_id_with_images(self, frame: Dict[str, Any], frame_id: str) -> Dict[str, Any]:
        """
        Process individual screen from frame using frame ID as identifier, including image URLs.
        """
        bbox = frame.get('absoluteBoundingBox', {})
        
        screen = {
            'screen_id': f"screen_{frame_id}",
            'name': frame.get('name', 'Unnamed Screen'),
            'figma_id': frame_id,
            'dimensions': {
                'width': bbox.get('width', 0),
                'height': bbox.get('height', 0)
            },
            'background': self._extract_background_ref(frame),
            'root': self._process_node_optimized_with_images(frame),
            'components_used': set(),
            'interactions': []
        }
        
        # Add image URL for the screen itself if available
        screen_image_url = self.image_url_cache.get(frame_id)
        if screen_image_url:
            screen['imageUrl'] = screen_image_url
        
        # Identify app-specific patterns
        self._identify_app_patterns(screen)
        
        # Convert sets to lists for JSON serialization
        screen['components_used'] = list(screen['components_used'])
        
        return screen
    
    def _process_node_optimized_with_images(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process individual node with optimized references and include image URLs.
        """
        processed_node = {
            'id': node.get('id'),
            'name': node.get('name', ''),
            'type': self._map_figma_type_to_ui_type(node.get('type', '')),
            'figma_type': node.get('type', '')
        }
        
        # Add image URL if available
        node_id = node.get('id')
        if node_id and node_id in self.image_url_cache:
            processed_node['imageUrl'] = self.image_url_cache[node_id]
        
        # Add dimensions if available
        bbox = node.get('absoluteBoundingBox')
        if bbox:
            processed_node['dimensions'] = {
                'x': bbox.get('x', 0),
                'y': bbox.get('y', 0),
                'width': bbox.get('width', 0),
                'height': bbox.get('height', 0)
            }
        
        # Add layout properties
        layout_mode = node.get('layoutMode')
        if layout_mode:
            processed_node['layout'] = layout_mode.lower().replace('_', '-')
        
        # Add optimized styling with references
        style_ref = self._extract_node_style_ref(node)
        if style_ref:
            processed_node['style_ref'] = style_ref
        
        # Add text content and typography reference
        if node.get('type') == 'TEXT':
            processed_node['text'] = node.get('characters', '')
            typo_ref = self._extract_text_style_ref(node)
            if typo_ref:
                processed_node['typography_ref'] = typo_ref
        
        # Add component information
        component_id = node.get('componentId')
        if component_id:
            comp_ref = self._find_component_ref(component_id)
            if comp_ref:
                processed_node['component_ref'] = comp_ref
        
        # Process children with image URLs
        children = node.get('children', [])
        if children:
            processed_node['children'] = [
                self._process_node_optimized_with_images(child) 
                for child in children 
                if child.get('visible', True)
            ]
        
        return processed_node
    
    # Keep all existing methods unchanged...
    def _extract_all_frame_design_tokens(self):
        """Extract design tokens from all frames to ensure complete design system."""
        document = self.figma_json.get('document', {})
        children = document.get('children', [])
        
        # Traverse all canvas children to find frames - match original logic exactly
        for canvas in children:
            if canvas.get('type') != 'CANVAS':
                continue
                
            canvas_children = canvas.get('children', [])
            for frame in canvas_children:
                if frame.get('type') == 'FRAME' and frame.get('visible', True):
                    # Process this frame exactly like original screen processing
                    self._simulate_screen_processing(frame)
    
    def _simulate_screen_processing(self, frame: Dict[str, Any]):
        """Simulate the exact screen processing logic to capture all style registrations."""
        # Process the frame itself like in _process_screen
        self._extract_background_ref(frame)
        
        # Process the frame node like in _process_node_optimized
        self._simulate_node_processing(frame)
    
    def _simulate_node_processing(self, node: Dict[str, Any]):
        """Simulate the exact node processing logic from _process_node_optimized."""
        # This exactly mirrors _process_node_optimized but only focuses on style registration
        
        # Extract node style reference (this is where styles get registered in original code)
        self._extract_node_style_ref(node)
        
        # Extract text style reference for text nodes
        if node.get('type') == 'TEXT':
            self._extract_text_style_ref(node)
        
        # Process children with the same visibility filter as original
        children = node.get('children', [])
        if children:
            for child in children:
                if child.get('visible', True):  # Same visibility filter as original
                    self._simulate_node_processing(child)
    
    def _build_frame_registry(self):
        """Build a registry of all frames by their IDs for quick lookup."""
        document = self.figma_json.get('document', {})
        self._traverse_and_register_frames(document)
    
    def _traverse_and_register_frames(self, node: Dict[str, Any]):
        """Recursively traverse and register all frames."""
        if not isinstance(node, dict):
            return
        
        # Register frame if it's a FRAME type
        if node.get('type') == 'FRAME':
            frame_id = node.get('id')
            if frame_id:
                self.frame_registry[frame_id] = node
        
        # Recursively process children
        children = node.get('children', [])
        if isinstance(children, list):
            for child in children:
                self._traverse_and_register_frames(child)
    
    def _process_screen_by_frame_id(self, frame: Dict[str, Any], frame_id: str) -> Dict[str, Any]:
        """Process individual screen from frame using frame ID as identifier."""
        bbox = frame.get('absoluteBoundingBox', {})
        
        screen = {
            'screen_id': f"screen_{frame_id}",
            'name': frame.get('name', 'Unnamed Screen'),
            'figma_id': frame_id,
            'dimensions': {
                'width': bbox.get('width', 0),
                'height': bbox.get('height', 0)
            },
            'background': self._extract_background_ref(frame),
            'root': self._process_node_optimized(frame),
            'components_used': set(),
            'interactions': []
        }
        
        # Identify app-specific patterns
        self._identify_app_patterns(screen)
        
        # Convert sets to lists for JSON serialization
        screen['components_used'] = list(screen['components_used'])
        
        return screen
    
    def _get_optimization_stats(self) -> Dict[str, int]:
        """Get statistics about optimization effectiveness."""
        return {
            'unique_colors': len(self.color_registry),
            'unique_typography': len(self.typography_registry),
            'unique_styles': len(self.style_registry),
            'unique_shadows': len(self.shadow_registry)
        }
    
    def _extract_design_system(self):
        """Extract design system elements from Figma JSON."""
        # Extract colors and typography from styles
        self._extract_styles()
        
        # Extract components
        self._extract_components()
        
        # Traverse document for design tokens
        if 'document' in self.figma_json:
            self._traverse_for_design_tokens(self.figma_json['document'])
    
    def _extract_styles(self):
        """Extract colors and typography from Figma styles."""
        if 'styles' not in self.figma_json:
            return
            
        for style_id, style in self.figma_json['styles'].items():
            if style.get('styleType') == 'FILL':
                color_name = self._sanitize_name(style.get('name', ''))
                color_key = f"color_{color_name}"
                self.color_registry[style_id] = color_key
                self.design_system['colors'][color_key] = {
                    'name': style.get('name', ''),
                    'figma_id': style_id
                }
    
    def _extract_components(self):
        """Extract reusable components from Figma."""
        if 'components' not in self.figma_json:
            return
            
        for comp_id, component in self.figma_json['components'].items():
            comp_key = self._sanitize_name(component.get('name', ''))
            self.design_system['components'][comp_key] = {
                'name': component.get('name', ''),
                'id': comp_id,
                'figma_key': component.get('key'),
                'description': component.get('description', '')
            }
    
    def _traverse_for_design_tokens(self, node: Dict[str, Any]):
        """Recursively traverse nodes to extract design tokens."""
        if not isinstance(node, dict):
            return
            
        # Extract and register design tokens
        self._extract_colors_from_fills(node)
        self._extract_colors_from_strokes(node)
        self._extract_typography_from_text(node)
        self._extract_spacing(node)
        self._extract_border_radius(node)
        self._extract_shadows(node)
        self._extract_styles_from_node(node)
        
        # Recursively process children
        if 'children' in node and isinstance(node['children'], list):
            for child in node['children']:
                self._traverse_for_design_tokens(child)
    
    def _extract_colors_from_fills(self, node: Dict[str, Any]):
        """Extract colors from node fills and register them."""
        fills = node.get('fills', [])
        if not isinstance(fills, list):
            return
            
        for fill in fills:
            if fill.get('type') == 'SOLID' and 'color' in fill:
                hex_color = self._rgba_to_hex(fill['color'])
                color_key = self._register_color(hex_color)
    
    def _extract_colors_from_strokes(self, node: Dict[str, Any]):
        """Extract colors from node strokes and register them."""
        strokes = node.get('strokes', [])
        if not isinstance(strokes, list):
            return
            
        for stroke in strokes:
            if stroke.get('type') == 'SOLID' and 'color' in stroke:
                hex_color = self._rgba_to_hex(stroke['color'])
                color_key = self._register_color(hex_color)
    
    def _extract_typography_from_text(self, node: Dict[str, Any]):
        """Extract typography from text nodes and register them."""
        if node.get('type') != 'TEXT' or 'style' not in node:
            return
            
        style = node['style']
        typo_data = {
            'family': style.get('fontFamily', 'default'),
            'size': style.get('fontSize', 16),
            'weight': style.get('fontWeight', 400),
            'line_height': style.get('lineHeightPx'),
            'letter_spacing': style.get('letterSpacing'),
            'text_align': style.get('textAlignHorizontal', '').lower() if style.get('textAlignHorizontal') else None
        }
        
        typo_key = self._register_typography(typo_data)
    
    def _extract_spacing(self, node: Dict[str, Any]):
        """Extract spacing values from node dimensions."""
        bbox = node.get('absoluteBoundingBox')
        if not bbox:
            return
            
        # Add width and height as potential spacing values
        width = bbox.get('width', 0)
        height = bbox.get('height', 0)
        
        if 0 < width < 2000:  # Reasonable spacing range
            self.design_system['spacing'].add(width)
        if 0 < height < 2000:
            self.design_system['spacing'].add(height)
            
        # Calculate gaps between children for spacing system
        children = node.get('children', [])
        if len(children) > 1:
            for i in range(len(children) - 1):
                current = children[i]
                next_child = children[i + 1]
                
                current_bbox = current.get('absoluteBoundingBox')
                next_bbox = next_child.get('absoluteBoundingBox')
                
                if current_bbox and next_bbox:
                    # Calculate horizontal gap
                    gap = abs(next_bbox.get('x', 0) - (current_bbox.get('x', 0) + current_bbox.get('width', 0)))
                    if 0 < gap < 200:  # Reasonable gap size
                        self.design_system['spacing'].add(gap)
    
    def _extract_border_radius(self, node: Dict[str, Any]):
        """Extract border radius values."""
        corner_radius = node.get('cornerRadius')
        if corner_radius is not None and corner_radius >= 0:
            self.design_system['border_radius'].add(corner_radius)
    
    def _extract_shadows(self, node: Dict[str, Any]):
        """Extract shadow effects and register them."""
        effects = node.get('effects', [])
        if not isinstance(effects, list):
            return
            
        for effect in effects:
            if effect.get('type') == 'DROP_SHADOW' and effect.get('visible', True):
                shadow_data = {
                    'type': 'drop_shadow',
                    'offset_x': effect.get('offset', {}).get('x', 0),
                    'offset_y': effect.get('offset', {}).get('y', 0),
                    'blur': effect.get('radius', 0),
                    'spread': effect.get('spread', 0),
                    'color': self._rgba_to_hex(effect.get('color', {}))
                }
                shadow_key = self._register_shadow(shadow_data)
    
    def _extract_styles_from_node(self, node: Dict[str, Any]):
        """Extract and register style combinations from nodes."""
        style_data = {}
        
        # Background/Fill
        fills = node.get('fills', [])
        if fills and isinstance(fills, list) and len(fills) > 0:
            fill = fills[0]
            if fill.get('type') == 'SOLID' and 'color' in fill:
                hex_color = self._rgba_to_hex(fill['color'])
                color_key = self._register_color(hex_color)
                style_data['background_color'] = color_key
        
        # Border
        strokes = node.get('strokes', [])
        if strokes and isinstance(strokes, list) and len(strokes) > 0:
            stroke = strokes[0]
            if stroke.get('type') == 'SOLID' and 'color' in stroke:
                hex_color = self._rgba_to_hex(stroke['color'])
                color_key = self._register_color(hex_color)
                style_data['border_color'] = color_key
                style_data['border_width'] = node.get('strokeWeight', 1)
        
        # Border radius
        corner_radius = node.get('cornerRadius')
        if corner_radius is not None:
            style_data['border_radius'] = corner_radius
        
        # Opacity
        opacity = node.get('opacity')
        if opacity is not None and opacity != 1:
            style_data['opacity'] = opacity
        
        # Register style if it has any properties
        if style_data:
            style_key = self._register_style(style_data)
    
    def _register_color(self, hex_color: str) -> str:
        """Register a color and return its key."""
        if hex_color in self.color_registry:
            return self.color_registry[hex_color]
        
        color_key = f"color_{hex_color.replace('#', '')}"
        self.color_registry[hex_color] = color_key
        self.design_system['colors'][color_key] = {
            'hex': hex_color,
            'name': hex_color
        }
        return color_key
    
    def _register_typography(self, typo_data: Dict[str, Any]) -> str:
        """Register typography and return its key."""
        # Create a hash of the typography data for deduplication
        typo_hash = self._hash_dict(typo_data)
        
        if typo_hash in self.typography_registry:
            return self.typography_registry[typo_hash]
        
        typo_key = f"typo_{len(self.typography_registry)}"
        self.typography_registry[typo_hash] = typo_key
        self.design_system['typography'][typo_key] = typo_data
        return typo_key
    
    def _register_style(self, style_data: Dict[str, Any]) -> str:
        """Register style combination and return its key."""
        style_hash = self._hash_dict(style_data)
        
        if style_hash in self.style_registry:
            return self.style_registry[style_hash]
        
        style_key = f"style_{len(self.style_registry)}"
        self.style_registry[style_hash] = style_key
        self.design_system['styles'][style_key] = style_data
        return style_key
    
    def _register_shadow(self, shadow_data: Dict[str, Any]) -> str:
        """Register shadow and return its key."""
        shadow_hash = self._hash_dict(shadow_data)
        
        if shadow_hash in self.shadow_registry:
            return self.shadow_registry[shadow_hash]
        
        shadow_key = f"shadow_{len(self.shadow_registry)}"
        self.shadow_registry[shadow_hash] = shadow_key
        self.design_system['shadows'][shadow_key] = shadow_data
        return shadow_key
    
    def _hash_dict(self, data: Dict[str, Any]) -> str:
        """Create a hash of dictionary data for deduplication."""
        # Filter out None values and sort for consistent hashing
        clean_data = {k: v for k, v in data.items() if v is not None}
        serialized = json.dumps(clean_data, sort_keys=True)
        return hashlib.md5(serialized.encode()).hexdigest()
    
    def _process_node_optimized(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Process individual node with optimized references."""
        processed_node = {
            'id': node.get('id'),
            'name': node.get('name', ''),
            'type': self._map_figma_type_to_ui_type(node.get('type', '')),
            'figma_type': node.get('type', '')
        }
        
        # Add dimensions if available
        bbox = node.get('absoluteBoundingBox')
        if bbox:
            processed_node['dimensions'] = {
                'x': bbox.get('x', 0),
                'y': bbox.get('y', 0),
                'width': bbox.get('width', 0),
                'height': bbox.get('height', 0)
            }
        
        # Add layout properties
        layout_mode = node.get('layoutMode')
        if layout_mode:
            processed_node['layout'] = layout_mode.lower().replace('_', '-')
        
        # Add optimized styling with references
        style_ref = self._extract_node_style_ref(node)
        if style_ref:
            processed_node['style_ref'] = style_ref
        
        # Add text content and typography reference
        if node.get('type') == 'TEXT':
            processed_node['text'] = node.get('characters', '')
            typo_ref = self._extract_text_style_ref(node)
            if typo_ref:
                processed_node['typography_ref'] = typo_ref
        
        # Add component information
        component_id = node.get('componentId')
        if component_id:
            comp_ref = self._find_component_ref(component_id)
            if comp_ref:
                processed_node['component_ref'] = comp_ref
        
        # Process children
        children = node.get('children', [])
        if children:
            processed_node['children'] = [
                self._process_node_optimized(child) 
                for child in children 
                if child.get('visible', True)
            ]
        
        return processed_node
    
    def _extract_node_style_ref(self, node: Dict[str, Any]) -> Optional[str]:
        """Extract node styling and return reference to centralized style."""
        style_data = {}
        
        # Background/Fill
        fills = node.get('fills', [])
        if fills and isinstance(fills, list) and len(fills) > 0:
            fill = fills[0]
            if fill.get('type') == 'SOLID' and 'color' in fill:
                hex_color = self._rgba_to_hex(fill['color'])
                color_key = self._register_color(hex_color)
                style_data['background_color'] = color_key
        
        # Border
        strokes = node.get('strokes', [])
        if strokes and isinstance(strokes, list) and len(strokes) > 0:
            stroke = strokes[0]
            if stroke.get('type') == 'SOLID' and 'color' in stroke:
                hex_color = self._rgba_to_hex(stroke['color'])
                color_key = self._register_color(hex_color)
                style_data['border_color'] = color_key
                style_data['border_width'] = node.get('strokeWeight', 1)
        
        # Border radius
        corner_radius = node.get('cornerRadius')
        if corner_radius is not None:
            style_data['border_radius'] = corner_radius
        
        # Opacity
        opacity = node.get('opacity')
        if opacity is not None and opacity != 1:
            style_data['opacity'] = opacity
        
        # Effects (shadows)
        effects = node.get('effects', [])
        shadow_refs = []
        for effect in effects:
            if effect.get('type') == 'DROP_SHADOW' and effect.get('visible', True):
                shadow_data = {
                    'type': 'drop_shadow',
                    'offset_x': effect.get('offset', {}).get('x', 0),
                    'offset_y': effect.get('offset', {}).get('y', 0),
                    'blur': effect.get('radius', 0),
                    'spread': effect.get('spread', 0),
                    'color': self._rgba_to_hex(effect.get('color', {}))
                }
                shadow_key = self._register_shadow(shadow_data)
                shadow_refs.append(shadow_key)
        
        if shadow_refs:
            style_data['shadows'] = shadow_refs
        
        # Return style reference if we have any style data
        if style_data:
            return self._register_style(style_data)
        
        return None
    
    def _extract_text_style_ref(self, node: Dict[str, Any]) -> Optional[str]:
        """Extract text styling and return reference to centralized typography."""
        style = node.get('style', {})
        if not style:
            return None
        
        typo_data = {
            'family': style.get('fontFamily'),
            'size': style.get('fontSize'),
            'weight': style.get('fontWeight'),
            'line_height': style.get('lineHeightPx'),
            'text_align': style.get('textAlignHorizontal', '').lower() if style.get('textAlignHorizontal') else None,
            'letter_spacing': style.get('letterSpacing')
        }
        
        # Extract text color from fills
        fills = node.get('fills', [])
        if fills and isinstance(fills, list) and len(fills) > 0:
            fill = fills[0]
            if fill.get('type') == 'SOLID' and 'color' in fill:
                hex_color = self._rgba_to_hex(fill['color'])
                color_key = self._register_color(hex_color)
                typo_data['color'] = color_key
        
        # Filter out None values
        typo_data = {k: v for k, v in typo_data.items() if v is not None}
        
        if typo_data:
            return self._register_typography(typo_data)
        
        return None
    
    def _extract_background_ref(self, node: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract background information with color reference."""
        fills = node.get('fills', [])
        if not fills or not isinstance(fills, list) or len(fills) == 0:
            return None
            
        fill = fills[0]
        if fill.get('type') == 'SOLID' and 'color' in fill:
            hex_color = self._rgba_to_hex(fill['color'])
            color_key = self._register_color(hex_color)
            return {
                'type': 'solid',
                'color_ref': color_key
            }
        
        return None
    
    def _find_component_ref(self, component_id: str) -> Optional[str]:
        """Find component reference key by component ID."""
        for comp_key, comp_data in self.design_system['components'].items():
            if comp_data['id'] == component_id:
                return comp_key
        return None
    
    def _identify_app_patterns(self, screen: Dict[str, Any]):
        """Identify app-specific patterns (e.g., Morse code converter patterns)."""
        self._traverse_for_patterns(screen['root'], screen)
    
    def _traverse_for_patterns(self, node: Dict[str, Any], screen: Dict[str, Any]):
        """Traverse nodes to identify semantic patterns."""
        if not isinstance(node, dict):
            return
            
        name = node.get('name', '').lower()
        
        # Identify semantic roles based on name patterns
        if any(keyword in name for keyword in ['input', 'type']):
            node['semantic_role'] = 'input_area'
            node['functionality'] = 'text_input_for_conversion'
        elif any(keyword in name for keyword in ['output', 'translated', 'result']):
            node['semantic_role'] = 'output_area'
            node['functionality'] = 'converted_text_display'
        elif any(keyword in name for keyword in ['speed', 'pitch', 'volume']):
            node['semantic_role'] = 'control_slider'
            node['functionality'] = 'parameter_control'
            if self._is_slider_structure(node):
                node['type'] = 'slider'
                node['slider_config'] = self._extract_slider_config(node)
        elif any(keyword in name for keyword in ['play', 'pause', 'stop']):
            node['semantic_role'] = 'media_control'
            node['functionality'] = 'audio_playback_control'
            node['type'] = 'button'
        elif any(keyword in name for keyword in ['button', 'btn']):
            node['type'] = 'button'
        
        # Recursively process children
        children = node.get('children', [])
        if children:
            for child in children:
                self._traverse_for_patterns(child, screen)
    
    def _is_slider_structure(self, node: Dict[str, Any]) -> bool:
        """Detect if node has slider-like structure."""
        children = node.get('children', [])
        if len(children) < 2:
            return False
            
        # Look for track-like and thumb-like elements
        has_track = any(
            child.get('figma_type') == 'RECTANGLE' and 
            child.get('dimensions', {}).get('width', 0) > child.get('dimensions', {}).get('height', 0) * 3
            for child in children
        )
        
        has_thumb = any(
            child.get('figma_type') in ['ELLIPSE', 'RECTANGLE'] and
            child.get('dimensions', {}).get('width', 0) < 100 and
            child.get('dimensions', {}).get('height', 0) < 100
            for child in children
        )
        
        return has_track and has_thumb
    
    def _extract_slider_config(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Extract slider configuration from node structure."""
        children = node.get('children', [])
        config = {}
        
        for child in children:
            child_type = child.get('figma_type')
            if child_type == 'RECTANGLE':
                dims = child.get('dimensions', {})
                if dims.get('width', 0) > dims.get('height', 0) * 3:
                    style_ref = child.get('style_ref')
                    config['track'] = {
                        'width': dims.get('width'),
                        'height': dims.get('height'),
                        'style_ref': style_ref
                    }
            elif child_type in ['ELLIPSE', 'RECTANGLE']:
                dims = child.get('dimensions', {})
                if dims.get('width', 0) < 100 and dims.get('height', 0) < 100:
                    style_ref = child.get('style_ref')
                    config['thumb'] = {
                        'width': dims.get('width'),
                        'height': dims.get('height'),
                        'style_ref': style_ref
                    }
        
        return config
    
    def _map_figma_type_to_ui_type(self, figma_type: str) -> str:
        """Map Figma node types to UI component types."""
        type_mapping = {
            'FRAME': 'container',
            'GROUP': 'container',
            'TEXT': 'text',
            'RECTANGLE': 'rectangle',
            'ELLIPSE': 'ellipse',
            'VECTOR': 'icon',
            'INSTANCE': 'component',
            'REGULAR_POLYGON': 'polygon'
        }
        
        return type_mapping.get(figma_type, figma_type.lower())
    
    def _build_design_system(self) -> Dict[str, Any]:
        """Build the final design system object with all centralized references."""
        return {
            'colors': self.design_system['colors'],
            'typography': self.design_system['typography'],
            'components': self.design_system['components'],
            'styles': self.design_system['styles'],
            'spacing': sorted(list(self.design_system['spacing'])),
            'border_radius': sorted(list(self.design_system['border_radius'])),
            'shadows': self.design_system['shadows']
        }
    
    def _rgba_to_hex(self, rgba_color: Dict[str, float]) -> str:
        """Convert RGBA color to hex format."""
        if not isinstance(rgba_color, dict):
            return '#000000'
        
        r = max(0, min(1, rgba_color.get('r', 0)))
        g = max(0, min(1, rgba_color.get('g', 0)))  
        b = max(0, min(1, rgba_color.get('b', 0)))
        
        r_int = int(round(r * 255))
        g_int = int(round(g * 255))
        b_int = int(round(b * 255))
        
        return f"#{r_int:02x}{g_int:02x}{b_int:02x}"
    
    def _sanitize_name(self, name: str) -> str:
        """Sanitize name for use as dictionary key."""
        if not isinstance(name, str):
            return 'unnamed'
        return re.sub(r'[^a-zA-Z0-9]', '_', name).lower().strip('_')


def create_design_system(figma_data: Dict[str, Any], output_dir: str, figma_id: str) -> str:
    """
    Create design_system.json file from Figma JSON data.
    
    Args:
        figma_data: Figma JSON data (data.response)
        output_dir: Directory to save design_system.json
        figma_id: Unique identifier for this figma file
        
    Returns:
        Path to the created design_system.json file
    """
    # Create preprocessor and extract design system
    preprocessor = OptimizedFigmaPreprocessor(figma_data)
    result = preprocessor.extract_design_system_only()
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save design system
    design_system_path = output_path / f'design_system_{figma_id}.json'
    with open(design_system_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Design system created successfully!")
    print(f"📁 File location: {design_system_path}")
    print(f"🎨 Total colors: {result['metadata']['total_colors']}")
    print(f"📝 Total typography styles: {result['metadata']['total_typography_styles']}")
    print(f"🎯 Total components: {result['metadata']['total_components']}")
    print(f"✨ Total styles: {result['metadata']['total_styles']}")
    
    return str(design_system_path)


async def create_screens_by_frame_ids_with_images(
    figma_data: Dict[str, Any], 
    frame_ids: List[str], 
    design_system_path: str, 
    output_dir: str,
    figma_api_key: str = None,
    file_key: str = None
) -> Dict[str, str]:
    """
    Create screen JSON files for specific frame IDs that reference the design system and include image URLs.
    
    Args:
        figma_data: Figma JSON data (data.response)
        frame_ids: List of frame IDs to process
        design_system_path: Path to the design_system.json file (for reference)
        output_dir: Directory to save screen JSON files
        figma_api_key: Figma API key for fetching images (optional)
        file_key: Figma file key for fetching images (optional)
        
    Returns:
        Dictionary mapping screen filenames to their paths
    """
    # Verify design system exists
    if not Path(design_system_path).exists():
        raise FileNotFoundError(f"Design system file not found: {design_system_path}")
    
    # Create preprocessor with image support
    preprocessor = OptimizedFigmaPreprocessor(figma_data, figma_api_key, file_key)
    
    # Extract design system to populate registries (same as what was done in create_design_system)
    preprocessor._extract_design_system()
    preprocessor._extract_all_frame_design_tokens()
    
    # Process screens for specific frame IDs with image URLs
    screens = await preprocessor.process_screens_by_frame_ids_with_images(frame_ids, skip_design_system_extraction=True)
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save each screen with reference to design system
    file_paths = {}
    design_system_relative_path = Path(design_system_path).name
    
    for screen in screens:
        # Add reference to design system
        screen['design_system_ref'] = design_system_relative_path
        screen['created_at'] = datetime.now().isoformat()
        
        # Create filename in format: screen_{frame_id}.json
        screen_filename = f"{screen['screen_id']}.json"
        screen_path = output_path / screen_filename
        
        with open(screen_path, 'w', encoding='utf-8') as f:
            json.dump(screen, f, indent=2, ensure_ascii=False)
        
        file_paths[screen_filename] = str(screen_path)
    
    print(f"✅ Created {len(screens)} screen files successfully with image URLs!")
    print(f"📁 Output directory: {output_dir}")
    print("📄 Generated screen files:")
    
    for filename in file_paths.keys():
        print(f"   • {filename}")
    
    return file_paths


def create_screens_by_frame_ids(figma_data: Dict[str, Any], frame_ids: List[str], 
                                design_system_path: str, output_dir: str) -> Dict[str, str]:
    """
    Create screen JSON files for specific frame IDs that reference the design system.
    Original function maintained for backward compatibility.
    
    Args:
        figma_data: Figma JSON data (data.response)
        frame_ids: List of frame IDs to process
        design_system_path: Path to the design_system.json file (for reference)
        output_dir: Directory to save screen JSON files
        
    Returns:
        Dictionary mapping screen filenames to their paths
    """
    # Verify design system exists
    if not Path(design_system_path).exists():
        raise FileNotFoundError(f"Design system file not found: {design_system_path}")
    
    # Create preprocessor and process specific frames
    preprocessor = OptimizedFigmaPreprocessor(figma_data)
    
    # Extract design system to populate registries (same as what was done in create_design_system)
    preprocessor._extract_design_system()
    preprocessor._extract_all_frame_design_tokens()
    
    # Process screens for specific frame IDs (skip design system extraction since we just did it)
    screens = preprocessor.process_screens_by_frame_ids(frame_ids, skip_design_system_extraction=True)
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save each screen with reference to design system
    file_paths = {}
    design_system_relative_path = Path(design_system_path).name
    
    for screen in screens:
        # Add reference to design system
        screen['design_system_ref'] = design_system_relative_path
        screen['created_at'] = datetime.now().isoformat()
        
        # Create filename in format: screen_{frame_id}.json
        screen_filename = f"{screen['screen_id']}.json"
        screen_path = output_path / screen_filename
        
        with open(screen_path, 'w', encoding='utf-8') as f:
            json.dump(screen, f, indent=2, ensure_ascii=False)
        
        file_paths[screen_filename] = str(screen_path)
    
    print(f"✅ Created {len(screens)} screen files successfully!")
    print(f"📁 Output directory: {output_dir}")
    print("📄 Generated screen files:")
    
    for filename in file_paths.keys():
        print(f"   • {filename}")
    
    return file_paths


async def process_figma_complete_with_images(
    figma_data: Dict[str, Any], 
    frame_ids: Optional[List[str]] = None, 
    output_dir: str = './output',
    figma_api_key: str = None,
    file_key: str = None
) -> Dict[str, Any]:
    """
    Complete processing with image URLs: Create design system and optionally process specific frames.
    
    Args:
        figma_data: Figma JSON data (data.response)
        frame_ids: Optional list of frame IDs to process. If None, processes all frames.
        output_dir: Directory to save output files
        figma_api_key: Figma API key for fetching images (optional)
        file_key: Figma file key for fetching images (optional)
        
    Returns:
        Dictionary with file paths and processing stats
    """
    # Step 1: Create design system
    design_system_path = create_design_system(figma_data, output_dir, file_key or 'unknown')
    
    result = {
        'design_system_path': design_system_path,
        'screen_paths': {},
        'total_screens_processed': 0
    }
    
    # Step 2: Process screens if frame_ids provided
    if frame_ids:
        screen_paths = await create_screens_by_frame_ids_with_images(
            figma_data, frame_ids, design_system_path, output_dir, figma_api_key, file_key
        )
        result['screen_paths'] = screen_paths
        result['total_screens_processed'] = len(screen_paths)
        
        print(f"\n🎯 Complete processing finished with image URLs!")
        print(f"📊 Total screens processed: {len(screen_paths)}")
    else:
        print(f"\n🎯 Design system created! Provide frame_ids to process specific screens.")
    
    return result


def process_figma_complete(figma_data: Dict[str, Any], frame_ids: Optional[List[str]] = None, 
                          output_dir: str = './output') -> Dict[str, Any]:
    """
    Complete processing: Create design system and optionally process specific frames.
    Original function maintained for backward compatibility.
    
    Args:
        figma_data: Figma JSON data (data.response)
        frame_ids: Optional list of frame IDs to process. If None, processes all frames.
        output_dir: Directory to save output files
        
    Returns:
        Dictionary with file paths and processing stats
    """
    # Step 1: Create design system
    design_system_path = create_design_system(figma_data, output_dir, 'unknown')
    
    result = {
        'design_system_path': design_system_path,
        'screen_paths': {},
        'total_screens_processed': 0
    }
    
    # Step 2: Process screens if frame_ids provided
    if frame_ids:
        screen_paths = create_screens_by_frame_ids(
            figma_data, frame_ids, design_system_path, output_dir
        )
        result['screen_paths'] = screen_paths
        result['total_screens_processed'] = len(screen_paths)
        
        print(f"\n🎯 Complete processing finished!")
        print(f"📊 Total screens processed: {len(screen_paths)}")
    else:
        print(f"\n🎯 Design system created! Provide frame_ids to process specific screens.")
    
    return result


async def get_figma_image_url(frame_id: Union[str, List[str]], 
                        figma_api_key: str = None, file_key: str = None) -> Dict[str, str]:
    try:
        print("figma_api_key",figma_api_key)
        preprocessor = OptimizedFigmaPreprocessor(figma_api_key, file_key)
        frame_ids = frame_id if isinstance(frame_id, list) else [frame_id]
        screens = await preprocessor.fetch_all_node_images_url_async(figma_api_key,file_key,frame_ids)
        print("screens",screens)

        return {fid: screens.get(fid, "") for fid in frame_ids if screens.get(fid)}

    except Exception as e:
        print(f"❌ Error retrieving image URL(s): {str(e)}")
        return {}


if __name__ == '__main__':
    # Example usage with image URLs:
    async def main():
        figma_file = 'logs/figma_data.json'
        output_directory = 'logs/figma_output_optimized'
        figma_api_key = 'your_figma_api_key'  # Replace with actual API key
        file_key = 'your_file_key'  # Replace with actual file key
        
        try:
            # Load figma data
            with open(figma_file, 'r', encoding='utf-8') as f:
                figma_data = json.load(f)
            
            # Option 1: Just create design system
            design_system_path = create_design_system(figma_data, output_directory, file_key)
            
            # Option 2: Create specific screens with images (provide actual frame IDs)
            example_frame_ids = ["1:123", "1:456", "1:789"]  # Replace with actual frame IDs
            screen_paths = await create_screens_by_frame_ids_with_images(
                figma_data, example_frame_ids, design_system_path, output_directory,
                figma_api_key, file_key
            )
            
            # Option 3: Complete processing with images
            # result = await process_figma_complete_with_images(
            #     figma_data, example_frame_ids, output_directory, figma_api_key, file_key
            # )
            
        except Exception as e:
            print(f"❌ Error processing Figma JSON: {str(e)}")
    
    # Run the async main function
    asyncio.run(main())