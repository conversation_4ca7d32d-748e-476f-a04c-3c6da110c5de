
import json
import os
import threading
import boto3
from botocore.exceptions import ClientError
from app.core.Settings import settings
from app.utils.datetime_utils import generate_timestamp

def setup_google_credentials(persistent_path="google_credentials.json"):
    """Setup Google credentials - optimized for threading."""
    try:
        # Check if credentials file already exists
        if os.path.exists(persistent_path):
            print(generate_timestamp(),f"✅ Using existing credentials file at: {persistent_path}")
            # Set the environment variable
            absolute_path = os.path.abspath(persistent_path)
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = absolute_path
            return absolute_path
        
        print(generate_timestamp(),"🔄 Fetching Google credentials from secrets...")
        
        # Get the vertex secret data
        credentials_data = get_vertex_secret()
        
        # Print type and sample of credentials
        print(generate_timestamp(),f"📋 Type of credentials: {type(credentials_data)}")

        # Write to the persistent path
        with open(persistent_path, 'w') as f:
            # Handle both dictionary and string cases
            if isinstance(credentials_data, dict):
                json.dump(credentials_data, f)
            else:
                # If it's already a string, write directly
                f.write(credentials_data)
        
        # Get absolute path
        absolute_path = os.path.abspath(persistent_path)
        
        # Set the environment variable
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = absolute_path
        
        print(generate_timestamp(),f"✅ GOOGLE_APPLICATION_CREDENTIALS set to: {absolute_path}")
        return absolute_path
        
    except Exception as e:
        print(generate_timestamp(),f"❌ Error setting up credentials: {e}")
        return None
    
def setup_google_credentials_daemon(persistent_path="google_credentials.json"):
    """
    Setup Google credentials in a daemon thread.
    Returns immediately, credentials are set up in background.
    """
    def _setup_credentials():
        try:
            print(generate_timestamp(),"🔄 Starting Google credentials setup in daemon thread...")
            path = setup_google_credentials(persistent_path)
            if path:
                print(generate_timestamp(),f"✅ Google credentials daemon setup completed: {path}")
            else:
                print(generate_timestamp(),"⚠️  Google credentials daemon setup failed")
        except Exception as e:
            print(generate_timestamp(),f"❌ Error in daemon credential setup: {e}")
    
    # Create and start daemon thread
    thread = threading.Thread(target=_setup_credentials, daemon=True)
    thread.start()
    print(generate_timestamp(),"Google credentials setup started in daemon thread", generate_timestamp())
    
    return thread


def get_vertex_secret():
    try:
        secret_name = "vertex_secret"
        region_name = "us-east-1"
        
        aws_access_key_id = settings.AWS_ACCESS_KEY_ID
        aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY
        session = boto3.session.Session(
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )

        # Create a Secrets Manager client
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name=region_name
        )

        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=secret_name
            )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e

        secret = get_secret_value_response['SecretString']
        return secret
    except Exception as e:
        print(f"Error retrieving secret: {e}")
        return None