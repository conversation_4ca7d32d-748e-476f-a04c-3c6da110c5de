import os
from typing import Dict, List, Optional
from botocore.exceptions import ClientError, NoCredentialsError
from app.utils.datetime_utils import generate_timestamp
from app.utils.code_generation_utils import get_codegeneration_path
from app.utils.file_utils.upload_utils import get_tenant_bucket
from app.utils.file_utils.upload_utils import s3_client

def download_s3_folder_to_local(
    bucket_name: str,
    s3_prefix: str,
    local_directory: str,
    s3_client,
    preserve_structure: bool = True,
    file_extensions: Optional[List[str]] = None
) -> Dict:
    """
    Download all files from an S3 folder to a local directory.
    
    Args:
        bucket_name: Name of the S3 bucket
        s3_prefix: S3 prefix/folder path (e.g., "extracted-docs-tenant123/project-456/")
        local_directory: Local directory path to download files to
        s3_client: Boto3 S3 client instance
        preserve_structure: Whether to preserve S3 folder structure locally
        file_extensions: Optional list of file extensions to filter (e.g., ['.json', '.pdf'])
        
    Returns:
        dict: Status information with files_downloaded, failed_downloads, and error details
    """
    try:
        # Ensure local directory exists
        os.makedirs(local_directory, exist_ok=True)
        print(generate_timestamp(), f"Created local directory: {local_directory}")
        
        # Remove trailing slash from prefix if present
        s3_prefix = s3_prefix.rstrip('/')
        
        files_downloaded = 0
        failed_downloads = []
        downloaded_files = []
        
        # Use paginator for large folders
        paginator = s3_client.get_paginator('list_objects_v2')
        page_iterator = paginator.paginate(
            Bucket=bucket_name,
            Prefix=s3_prefix
        )
        
        total_objects = 0
        
        print(generate_timestamp(), f"Scanning S3 folder: s3://{bucket_name}/{s3_prefix}")
        
        for page in page_iterator:
            if 'Contents' not in page:
                continue
                
            for obj in page['Contents']:
                s3_key = obj['Key']
                file_size = obj['Size']
                
                # Skip if it's a folder (ends with /)
                if s3_key.endswith('/'):
                    continue
                
                total_objects += 1
                filename = os.path.basename(s3_key)
                
                # Apply file extension filter if specified
                if file_extensions:
                    file_ext = os.path.splitext(filename)[1].lower()
                    if file_ext not in [ext.lower() for ext in file_extensions]:
                        print(generate_timestamp(), f"Skipping {filename} (extension filter)")
                        continue
                
                try:
                    if preserve_structure:
                        # Preserve S3 folder structure
                        relative_path = s3_key[len(s3_prefix):].lstrip('/')
                        local_file_path = os.path.join(local_directory, relative_path)
                        
                        # Create subdirectories if needed
                        local_file_dir = os.path.dirname(local_file_path)
                        if local_file_dir:
                            os.makedirs(local_file_dir, exist_ok=True)
                    else:
                        # Flatten all files to the root local directory
                        local_file_path = os.path.join(local_directory, filename)
                    
                    # Download the file
                    s3_client.download_file(
                        Bucket=bucket_name,
                        Key=s3_key,
                        Filename=local_file_path
                    )
                    
                    files_downloaded += 1
                    downloaded_files.append({
                        "filename": filename,
                        "s3_key": s3_key,
                        "local_path": local_file_path,
                        "size": file_size
                    })
                    
                    print(generate_timestamp(), f"Downloaded: {filename} ({file_size} bytes)")
                    
                except ClientError as e:
                    error_code = e.response['Error']['Code']
                    error_msg = f"S3 error downloading {filename}: {error_code}"
                    failed_downloads.append({"filename": filename, "error": error_msg})
                    print(generate_timestamp(), error_msg)
                    
                except Exception as e:
                    error_msg = f"Error downloading {filename}: {str(e)}"
                    failed_downloads.append({"filename": filename, "error": error_msg})
                    print(generate_timestamp(), error_msg)
        
        # Prepare result summary
        result = {
            "status": "success" if files_downloaded > 0 else "no_files",
            "bucket_name": bucket_name,
            "s3_prefix": s3_prefix,
            "local_directory": local_directory,
            "total_objects_found": total_objects,
            "files_downloaded": files_downloaded,
            "failed_downloads": len(failed_downloads),
            "downloaded_files": downloaded_files,
            "failed_files": failed_downloads
        }
        
        if files_downloaded > 0:
            print(generate_timestamp(), f"Download complete: {files_downloaded} files downloaded to {local_directory}")
        else:
            print(generate_timestamp(), f"No files downloaded from s3://{bucket_name}/{s3_prefix}")
            
        return result
        
    except NoCredentialsError:
        error_msg = "AWS credentials not configured"
        print(generate_timestamp(), f"Error: {error_msg}")
        return {
            "status": "error",
            "message": error_msg,
            "files_downloaded": 0
        }
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_msg = f"S3 access error: {error_code}"
        print(generate_timestamp(), f"Error: {error_msg}")
        return {
            "status": "error", 
            "message": error_msg,
            "files_downloaded": 0
        }
        
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(generate_timestamp(), f"Error: {error_msg}")
        return {
            "status": "error",
            "message": error_msg, 
            "files_downloaded": 0
        }


def download_projdefn_documents(tenant_id: str, project_id: int, task_id: str, agent_name: str) -> Dict:
    """
    Specialized wrapper to download projdefn documents for CGA workspace.
    
    Args:
        tenant_id: Tenant identifier
        project_id: Project identifier
        task_id: Current task identifier  
        agent_name: Agent name for workspace path
        
    Returns:
        dict: Download status information
    """
    # Get workspace and create .projdefn directory
    workspace_path = get_codegeneration_path(agent_name=agent_name, task_id=task_id)
    projdefn_dir = os.path.join(workspace_path, ".projdefn")
    
    # Get S3 configuration
    bucket_name = get_tenant_bucket(tenant_id)
    s3_prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files"
    
    # Download with JSON filter for projdefn files
    return download_s3_folder_to_local(
        bucket_name=bucket_name,
        s3_prefix=s3_prefix,
        local_directory=projdefn_dir,
        s3_client=s3_client,
        preserve_structure=False,  # Flatten projdefn files to .projdefn root
        file_extensions=['.json']  # Only download JSON files
    )