"""
app/services/supabase_lambda_service.py
Service for direct Lambda invocation for Supabase operations
"""

import boto3
import json
import asyncio
import os
from app.core.Settings import settings

class SupabaseLambdaService:
    def __init__(self):
        self.lambda_client = boto3.client(
            'lambda',
            aws_access_key_id=settings.DUPLO_AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.DUPLO_AWS_SECRET_ACCESS_KEY,
            region_name=settings.DUPLO_AWS_REGION
        )
        self.function_name = os.getenv('SUPABASE_LAMBDA_FUNCTION_NAME', 'kavia-hello-world')
    
    async def bootstrap_project(self, project_id_supabase: str, db_password: str):
        """Bootstrap Supabase project via direct Lambda invoke"""
        payload = {
            "action": "bootstrap",
            "project_id_supabase": project_id_supabase,
            "db_password": db_password
        }
        
        return await self._invoke_lambda(payload)
    
    async def execute_sql(self, project_id_supabase: str, db_password: str, sql: str):
        """Execute SQL via direct Lambda invoke"""
        payload = {
            "action": "execute_sql",
            "project_id_supabase": project_id_supabase,
            "db_password": db_password,
            "sql": sql
        }
        
        return await self._invoke_lambda(payload)
    
    async def _invoke_lambda(self, payload):
        """Invoke Lambda function directly"""
        loop = asyncio.get_event_loop()
        
        try:
            # Invoke Lambda synchronously
            response = await loop.run_in_executor(
                None,
                lambda: self.lambda_client.invoke(
                    FunctionName=self.function_name,
                    InvocationType='RequestResponse',  # Synchronous
                    Payload=json.dumps(payload)
                )
            )
            
            # Parse response
            response_payload = json.loads(response['Payload'].read())
            return response_payload
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Lambda invoke failed: {str(e)}"
            }