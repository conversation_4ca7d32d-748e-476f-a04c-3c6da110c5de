#!/usr/bin/env python3
"""
supabase_bootstrap_k8s.py
Enhanced bootstrap for K8s with automatic connection string detection and fixing
"""

import os
import re
import time
import textwrap
import psycopg2
from contextlib import contextmanager
from typing import Optional, Dict, Any, Tu<PERSON>
from dataclasses import dataclass
from urllib.parse import urlparse, urlunparse, parse_qs


@dataclass
class BootstrapResult:
    """Result object for bootstrap operations"""
    success: bool
    message: str
    details: Optional[Dict[str, Any]] = None


class SupabaseBootstrapK8s:
    """
    K8s-optimized Bootstrap with automatic connection string fixing.
    """
    
    def __init__(self, db_url: str, verbose: bool = True, auto_fix: bool = True):
        """
        Initialize with automatic connection string fixing for K8s.
        
        Args:
            db_url: PostgreSQL connection string
            verbose: Whether to print status messages
            auto_fix: Automatically fix connection strings for pooler
        """
        self.original_url = db_url
        self.db_url = self._fix_connection_string(db_url) if auto_fix else db_url
        self.verbose = verbose
        self.connection_type = self._detect_connection_type(self.db_url)
        
        # SQL payloads
        self.DROP_RUN_SQL = "DROP FUNCTION IF EXISTS public.run_sql(text);"
        self.CREATE_RUN_SQL = textwrap.dedent(r"""
            CREATE OR REPLACE FUNCTION public.run_sql(query text)
            RETURNS jsonb
            LANGUAGE plpgsql
            SECURITY DEFINER
            SET search_path = public
            AS $$
            DECLARE
                clean text := regexp_replace(query, ';\\s*$', '');
                result jsonb;
                produces_rows boolean :=
                     clean ~* '^\s*(select|with)\s'
                  or clean ~* '\\breturning\\b';
            BEGIN
                IF produces_rows THEN
                    EXECUTE format(
                       'WITH _q AS (%s) SELECT coalesce(json_agg(_q), ''[]'') FROM _q',
                       clean
                    ) INTO result;
                    RETURN result;
                ELSE
                    EXECUTE clean;
                    RETURN 'null'::jsonb;
                END IF;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE EXCEPTION 'SQL execution failed: %', SQLERRM;
            END;
            $$;
        """)
    
    def _detect_connection_type(self, db_url: str) -> str:
        """Detect if this is a pooler or direct connection."""
        parsed = urlparse(db_url)
        hostname = parsed.hostname or ""
        
        if 'pooler.supabase.com' in hostname:
            return 'pooler'
        elif 'supabase.co' in hostname:
            return 'direct'
        else:
            return 'unknown'
    
    def _extract_project_ref(self, db_url: str) -> Optional[str]:
        """Extract project reference from various URL formats."""
        # Try to extract from hostname (db.PROJECT_REF.supabase.co)
        match = re.search(r'db\.([a-z]+)\.supabase\.co', db_url)
        if match:
            return match.group(1)
        
        # Try to extract from pooler username (postgres.PROJECT_REF)
        match = re.search(r'postgres\.([a-z]+)', db_url)
        if match:
            return match.group(1)
        
        # Try to extract from anywhere in the URL
        match = re.search(r'([a-z]{20})', db_url)
        if match:
            return match.group(1)
        
        return None
    
    def _fix_connection_string(self, db_url: str) -> str:
        """
        Automatically fix connection string for pooler connections.
        Converts between direct and pooler formats as needed.
        """
        parsed = urlparse(db_url)
        hostname = parsed.hostname or ""
        username = parsed.username or "postgres"
        password = parsed.password or ""
        database = parsed.path.lstrip('/') or "postgres"
        
        # Extract project reference
        project_ref = self._extract_project_ref(db_url)
        
        if not project_ref:
            self._log("⚠️  Could not extract project reference from URL", is_error=True)
            return db_url
        
        # If this is a pooler connection, ensure proper username format
        if 'pooler.supabase.com' in hostname:
            if '.' not in username:
                # Fix: Add project reference to username
                fixed_username = f"postgres.{project_ref}"
                self._log(f"Fixed pooler username: {username} → {fixed_username}")
                
                # Rebuild URL with fixed username
                netloc = f"{fixed_username}:{password}@{hostname}:{parsed.port or 5432}"
                fixed_url = urlunparse((
                    parsed.scheme,
                    netloc,
                    parsed.path,
                    parsed.params,
                    parsed.query or "sslmode=require",
                    parsed.fragment
                ))
                
                return fixed_url
        
        # For direct connections, optionally convert to pooler
        elif 'supabase.co' in hostname and 'KUBERNETES_SERVICE_HOST' in os.environ:
            # Running in K8s, convert to pooler connection
            self._log("Detected K8s environment, converting to pooler connection")
            
            # Determine region (default to ap-south-1 based on your example)
            region = os.getenv('SUPABASE_REGION', 'ap-south-1')
            pooler_host = f"aws-0-{region}.pooler.supabase.com"
            pooler_username = f"postgres.{project_ref}"
            
            netloc = f"{pooler_username}:{password}@{pooler_host}:5432"
            pooler_url = urlunparse((
                "postgresql",
                netloc,
                "/postgres",
                "",
                "sslmode=require",
                ""
            ))
            
            self._log(f"Converted to pooler URL: {pooler_host}")
            return pooler_url
        
        # Add sslmode if missing
        if 'sslmode' not in db_url:
            separator = '?' if '?' not in db_url else '&'
            return f"{db_url}{separator}sslmode=require"
        
        return db_url
    
    def _log(self, message: str, is_error: bool = False):
        """Enhanced logging with connection type info."""
        if self.verbose:
            prefix = "❌" if is_error else "✅"
            conn_info = f"[{self.connection_type}]" if self.connection_type != 'unknown' else ""
            print(f"{prefix} {conn_info} {message}")
    
    @contextmanager
    def _get_cursor(self):
        """Context manager with connection retry logic."""
        conn = None
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                self._log(f"Connection attempt {attempt + 1}/{max_retries}")
                
                # Try connection with timeout
                conn = psycopg2.connect(
                    self.db_url,
                    connect_timeout=30,
                    options='-c statement_timeout=60000'
                )
                
                # Test connection
                with conn.cursor() as cur:
                    cur.execute("SELECT version()")
                    version = cur.fetchone()[0]
                    self._log(f"Connected: {version.split(',')[0]}")
                    
                # Connection successful
                with conn.cursor() as cur:
                    yield cur
                conn.commit()
                return
                
            except psycopg2.OperationalError as e:
                error_msg = str(e).strip()
                
                if "Tenant or user not found" in error_msg:
                    self._log(
                        f"Authentication error. Check connection string format:\n"
                        f"  Current: {self.db_url.split('@')[0].split('://')[-1]}@...\n"
                        f"  Expected for pooler: postgres.PROJECT_REF:password@...",
                        is_error=True
                    )
                    
                    # Try to auto-fix if not already attempted
                    if attempt == 0 and self.original_url != self.db_url:
                        self._log("Already attempted auto-fix. Manual intervention required.", is_error=True)
                    break
                    
                elif "timeout" in error_msg.lower() or "could not connect" in error_msg.lower():
                    if attempt < max_retries - 1:
                        self._log(f"Connection timeout. Retrying in {retry_delay}s...", is_error=True)
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        self._log("Connection failed after all retries", is_error=True)
                        raise
                else:
                    self._log(f"Database error: {error_msg}", is_error=True)
                    raise
                    
            except Exception as e:
                if conn:
                    conn.rollback()
                self._log(f"Error: {str(e)}", is_error=True)
                raise
                
            finally:
                if conn:
                    conn.close()
    
    def test_connection(self) -> Tuple[bool, str]:
        """Test database connection and permissions."""
        try:
            with self._get_cursor() as cur:
                # Check version and user
                cur.execute("SELECT version(), current_database(), current_user")
                version, database, user = cur.fetchone()
                
                self._log(f"Database: {database}, User: {user}")
                
                # Check permissions
                cur.execute("""
                    SELECT has_database_privilege(current_user, current_database(), 'CREATE')
                """)
                has_create = cur.fetchone()[0]
                
                if not has_create:
                    return False, "User lacks CREATE privileges"
                
                return True, "Connection successful"
                
        except Exception as e:
            return False, str(e)
    
    def check_pgcrypto(self) -> bool:
        """Check if pgcrypto extension is installed."""
        try:
            with self._get_cursor() as cur:
                cur.execute(
                    "SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto' LIMIT 1;"
                )
                return cur.fetchone() is not None
        except Exception:
            return False
    
    def install_pgcrypto(self) -> BootstrapResult:
        """Install pgcrypto extension."""
        try:
            already_exists = self.check_pgcrypto()
            
            with self._get_cursor() as cur:
                if already_exists:
                    self._log("pgcrypto already present – reinstalling")
                    cur.execute("DROP EXTENSION IF EXISTS pgcrypto CASCADE;")
                
                self._log("Installing pgcrypto extension")
                cur.execute("CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA public;")
            
            self._log("pgcrypto installed successfully")
            return BootstrapResult(
                success=True,
                message="pgcrypto extension installed",
                details={"was_existing": already_exists}
            )
            
        except psycopg2.errors.InsufficientPrivilege as e:
            error_msg = "Insufficient privileges. Use service_role key."
            self._log(error_msg, is_error=True)
            return BootstrapResult(
                success=False,
                message=error_msg,
                details={"pgerror": str(e.pgerror).strip()}
            )
        except Exception as e:
            error_msg = f"Failed to install pgcrypto: {str(e)}"
            self._log(error_msg, is_error=True)
            return BootstrapResult(success=False, message=error_msg)
    
    def check_run_sql(self) -> bool:
        """Check if public.run_sql function exists."""
        try:
            with self._get_cursor() as cur:
                cur.execute("""
                    SELECT 1
                    FROM pg_proc p
                    JOIN pg_namespace n ON n.oid = p.pronamespace
                    WHERE p.proname = 'run_sql' AND n.nspname = 'public'
                    LIMIT 1;
                """)
                return cur.fetchone() is not None
        except Exception:
            return False
    
    def drop_run_sql(self) -> BootstrapResult:
        """Drop existing run_sql function."""
        try:
            with self._get_cursor() as cur:
                cur.execute(self.DROP_RUN_SQL)
            
            self._log("run_sql() function dropped")
            return BootstrapResult(success=True, message="run_sql function dropped")
            
        except Exception as e:
            error_msg = f"Failed to drop run_sql: {str(e)}"
            self._log(error_msg, is_error=True)
            return BootstrapResult(success=False, message=error_msg)
    
    def install_run_sql(self) -> BootstrapResult:
        """Install and verify run_sql function."""
        try:
            with self._get_cursor() as cur:
                cur.execute(self.CREATE_RUN_SQL)
            
            if self._verify_run_sql():
                self._log("run_sql() function installed and verified")
                return BootstrapResult(
                    success=True,
                    message="run_sql function installed and verified"
                )
            else:
                error_msg = "run_sql installation failed verification"
                self._log(error_msg, is_error=True)
                return BootstrapResult(success=False, message=error_msg)
                
        except Exception as e:
            error_msg = f"Failed to install run_sql: {str(e)}"
            self._log(error_msg, is_error=True)
            return BootstrapResult(success=False, message=error_msg)
    
    def _verify_run_sql(self) -> bool:
        """Verify run_sql function works."""
        try:
            if not self.check_run_sql():
                return False
            
            with self._get_cursor() as cur:
                cur.execute("SELECT public.run_sql('SELECT 1 as test');")
                result = cur.fetchone()
                return result is not None and result[0] is not None
                
        except Exception as e:
            self._log(f"Verification failed: {str(e)}", is_error=True)
            return False
    
    def setup(self) -> BootstrapResult:
        """Complete bootstrap setup."""
        self._log("Starting Supabase bootstrap setup")
        
        # Show connection details
        self._log(f"Connection type: {self.connection_type}")
        if self.original_url != self.db_url:
            self._log("Connection string was auto-fixed for K8s/pooler compatibility")
        
        # Test connection first
        conn_success, conn_msg = self.test_connection()
        if not conn_success:
            return BootstrapResult(
                success=False,
                message=f"Connection test failed: {conn_msg}",
                details={"connection_type": self.connection_type}
            )
        
        results = []
        
        # Install pgcrypto
        pgcrypto_result = self.install_pgcrypto()
        results.append(pgcrypto_result)
        
        if not pgcrypto_result.success:
            return BootstrapResult(
                success=False,
                message="Bootstrap failed at pgcrypto installation",
                details={"results": results}
            )
        
        # Drop and reinstall run_sql
        drop_result = self.drop_run_sql()
        results.append(drop_result)
        
        if not drop_result.success:
            return BootstrapResult(
                success=False,
                message="Bootstrap failed at run_sql cleanup",
                details={"results": results}
            )
        
        install_result = self.install_run_sql()
        results.append(install_result)
        
        if not install_result.success:
            return BootstrapResult(
                success=False,
                message="Bootstrap failed at run_sql installation",
                details={"results": results}
            )

        success_message = "Bootstrap setup completed successfully"

        self._log(success_message)
        return BootstrapResult(
            success=True,
            message="Bootstrap setup completed successfully",
            details={"results": results, "connection_type": self.connection_type}
        )
    
    def status(self) -> Dict[str, Any]:
        """Check current status of bootstrap components."""
        conn_success, conn_msg = self.test_connection()
        return {
            "connection": {
                "success": conn_success,
                "message": conn_msg,
                "type": self.connection_type
            },
            "components": {
                "pgcrypto_installed": self.check_pgcrypto() if conn_success else False,
                "run_sql_installed": self.check_run_sql() if conn_success else False,
                "run_sql_functional": self._verify_run_sql() if conn_success else False
            }
        }


def bootstrap_supabase_project_k8s(
    db_url: str,
    verbose: bool = True,
    auto_fix: bool = True
) -> BootstrapResult:
    """
    K8s-aware bootstrap with automatic connection string fixing.
    
    Args:
        db_url: PostgreSQL connection string
        verbose: Whether to print status messages
        auto_fix: Automatically fix connection strings for pooler/K8s
        
    Returns:
        BootstrapResult indicating success/failure
    """
    bootstrap = SupabaseBootstrapK8s(db_url, verbose, auto_fix)
    return bootstrap.setup()


# Helper function to generate correct connection strings
def generate_supabase_connection_url(
    project_ref: str,
    password: str,
    use_pooler: bool = True,
    region: str = "ap-south-1"
) -> str:
    """
    Generate correct Supabase connection URL.
    
    Args:
        project_ref: Supabase project reference (20 char string)
        password: Database password
        use_pooler: Use pooler connection (recommended for K8s)
        region: AWS region for pooler
        
    Returns:
        Properly formatted connection string
    """
    import urllib.parse
    encoded_password = urllib.parse.quote(password, safe='')
    
    if use_pooler:
        # Pooler connection (K8s/serverless friendly)
        return (
            f"postgresql://postgres.{project_ref}:{encoded_password}"
            f"@aws-0-{region}.pooler.supabase.com:5432/postgres?sslmode=require"
        )
    else:
        # Direct connection
        return (
            f"postgresql://postgres:{encoded_password}"
            f"@db.{project_ref}.supabase.co:5432/postgres?sslmode=require"
        )


if __name__ == "__main__":
    import sys
    
    # Get connection URL from environment or command line
    if len(sys.argv) > 1:
        db_url = sys.argv[1]
    else:
        db_url = os.getenv('DATABASE_URL') or os.getenv('SUPABASE_DB_URL')
    
    if not db_url:
        print("❌ No database URL provided")
        print("Usage: python supabase_bootstrap_k8s.py <database_url>")
        print("Or set DATABASE_URL environment variable")
        sys.exit(1)
    
    # Run bootstrap with auto-fix enabled
    result = bootstrap_supabase_project_k8s(db_url, verbose=True, auto_fix=True)
    
    if result.success:
        print("\n✅ Bootstrap completed successfully!")
    else:
        print(f"\n❌ Bootstrap failed: {result.message}")
    
    sys.exit(0 if result.success else 1)