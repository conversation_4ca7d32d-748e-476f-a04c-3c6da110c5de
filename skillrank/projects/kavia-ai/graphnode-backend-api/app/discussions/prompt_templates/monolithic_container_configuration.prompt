{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
As an expert software architect, configure the container based on the C4 model:

{% else %}
You are an expert system architect reviewing the Container for potential reconfiguration.

1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
     - Components: {{ bg_info.get('system_context', {}).get('components') | tojson(indent=2) }}

   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * ID: {{ req.id }} - {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * ID: {{ req.id }} - {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated system context:
     - Users: {{ new_bg.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ new_bg.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * ID: {{ req.id }} - {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * ID: {{ req.id }} - {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:

1. Impact Analysis:
   - Compare original and new functional and non-functional requirements, system_context, and project context.
   - Identify potential changes in user interactions and external system interactions.

2. Required Changes:
  -  Modify Existing container, corresponding interfaces based on the above analysis.
  - Follow the below guidelines as a reference while modifying the existing container and its child.
  - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.



{% endif %}


GENERIC GUIDELINES:

As an expert software architect, review the project details, system context, Fucntion and Non-Fucntional Requirement and configure this container following C4 architecture principles. 
NOTE: This is a C4 Container-level configuration and requires a single-component design:

1.  Provide a description of the container - explaining its core functionalities its role in the overall system and its interaction with other containers, users and external systems. 

2. Tech Stack Selection:
   - Look at the `Project Details :` part at the end of the prompt and get an idea from `additional_details` key.
   - Based on the selected platform, choose a relevant technology (with the exact value,case sensitive) from the list below:
     * Backend options: 'flask', 'fastapi', 'django', 'express'
     * Mobile options: 'flutter', 'android', 'kotlin'
     * Frontend (Web) options: 'react', 'angular', 'astro', 'nextjs', 'qwik', 'nuxt', 'remix', 'remotion', 'slidev', 'svelte', 'vite', 'vue'
     * Database options: 'PostgreSQL', 'MySQL', 'MongoDB', 'SQLite'


{% if 'Project' in details_for_discussion['root_node']['labels'] and details_for_discussion['root_node']['properties']['ArchitecturePattern'] == 'multi-container-single-component' %}
  
3. Interface Definition and Creation:
   - CREATE interface nodes as child nodes when you think the containers need to communicate
   - For each interface node:
     * Create as a child node with unique ID (e.g., 'NEW-INTERFACE-1', 'NEW-INTERFACE-2')
     * Set Type: "Interface"
     * Properties to include:
       - Title: Clear, descriptive name
       - Description: Detailed description of the interface purpose
       - InterfaceType: Determine appropriate type based on:
         - Communication patterns (synchronous/asynchronous)
         - Performance requirements
         - Reliability needs
         - Integration constraints
       - Protocol: Specific protocol used
       - DataFormat: Data exchange format
       - ApiSignature: Examples of API calls or message formats

   Example interface creation scenarios:
   - Container-to-container communication requires REST API
   - Asynchronous processing needs Message Queue interface
   - Database access requires Database Interface
   - File operations need File Interface

{% endif %}

3.User Interactions:
     * Identify which user types from the system context interact with this container
     * Define the container's role in supporting user needs
   
4.Create relationship with External System:
    
    - Review the available external containers and specify dependencies for this container ( {{ details_for_discussion.get('current_node')}})
    Available External Containers:
    {{details_for_discussion.get('external_containers')| tojson(indent=2)  }}
    - Identify which external systems this container interacts with
    - Create USES Relationship with the external container - with Properties:

    type: Fixed value "USES" indicating external system dependency
    name: External service or API name that is being consumed
    description: Purpose and functionality being used from the external service
    source id: Node id of the current container consuming the service
    target id: Node id of the external container providing the service
    technology: Protocol or method used to integrate with the external service

   Database Creation:
    - Set HasDatabase property TRUE if this container has USES relationship with Database.

    Database Details:
    Based on the container's requirements, create Databases as child nodes of this container.
    - Provide a suitable description for the Database and its purpose.
    - Database Selection: Choose appropriate database type.
    - Provide an appropriate database name.

    Current External Dependencies:
    {% if details_for_discussion.get('existing_uses') %}
    Currently using:
    {% for rel in details_for_discussion.get('existing_uses') %}

    {{ rel.properties.name }} ({{ rel.properties.technology }})
    {% endfor %}
    {% endif %}


6.Map Implemented Requirements
- Analyze the Container Purpose and Responsibilities 
- Review the requirements given below and identify the requirements that this container implements 
- Add only those requirement node IDs to the ImplementedRequirementIDs property, as an array of integers 

       {{ details_for_discussion.get('functional_requirements') | tojson(indent=2) }}
       {{ details_for_discussion.get('architectural_requirements') | tojson(indent=2) }}
        
7. Single Component Design:
   - Create ONE component that encapsulates all container functionality as child for this container.
   - Note: This is a C4-level component, representing a major logical part of the container
   - Focus on high-level component responsibilities and structure (Do not miss out on any information)
   - Document how it fulfills all container requirements

8. Repository Name:
   The container's repository name - provide suitable name based on its functionality:
   - Repository Name: <container-name-in-kebab-case>

9. Generate a C4 Container diagram showing:
   - The container with its corresponding component
   - External system interactions
   - Key interfaces at container level
   

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

Example C4 Container Diagram - use this diagram for reference purpose only - generate suitable diagram based on the given container details.
```
graph TB
    %% External Container with Interface
    ExtContainer["External Container Name<br/>[Container: External]"]
    ExtInterface["External Interface<br/>[Interface]"]
    ExtContainer -->|"exposes"| ExtInterface

    %% Current Container being configured
    subgraph ThisContainer[Container Name]
        direction TB
        MainComponent["Main Component<br/>[Component]"]
        
        %% Add other components based on container requirements
    end

    %% USES relationship from THIS container to external
    MainComponent -->|"USES<br/>Service: Service Name<br/>Protocol: Protocol Type<br/>Purpose: Integration Purpose"| ExtContainer
    
    %% Styling
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef component fill:#85bbf0,stroke:#5d82a8,color:#000000
    classDef external fill:#666666,stroke:#333333,color:#ffffff
    classDef interface fill:#85bbf0,stroke:#5d82a8,color:#000000,stroke-dasharray: 5 5
    
    class ThisContainer container
    class MainComponent component
    class ExtContainer external
    class ExtInterface interface
```

Change Needed: 
    - Set to False if changes are not required.

Change Log :
    - capture history of changes.
{% endblock %}

{% block autoconfig %}
Create a C4 container-level design with a single component that provides a clear structural view of the system's organization while adhering to C4 modeling principles. 
provide the component details as new child nodes
{% endblock %}

{% block auto_reconfig %}
Based on the current monolithic container and component, analyze if updates are needed to:

Better fulfill system capabilities
Support new user interactions
Enhance external system integration
Update technology stack
Optimize data flows

While maintaining strict monolithic constraints:

Must keep EXACTLY ONE container and component
Component must encapsulate ALL functionality
Preserve monolithic architecture pattern

If updates are needed:

Propose specific modifications to system context
Update component configuration
Revise interaction patterns
Document rationale in change_reason
Update change_log with modification history

If no updates needed:

Explain why current configuration remains valid
Set changes_needed to false
Document reasoning in change_reason
{% endblock %}


{% block node_details_interactive_reconfig %}
C4 container structure and component organization
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Container-level configuration with single component following C4 model principles
{% endblock  %}

{% block information_about_task %}
    {{ super() }}
    Container Details: {{ details_for_discussion.get('container') | tojson(indent=2) }}
    Existing Components: {{ details_for_discussion.get('existing_components') | tojson(indent=2) }}
    
{% endblock %}

{% block background_information %}
    {{ super() }}
    Project Details :  {{ details_for_discussion.get('project_details') }} 
    System Context Information:
    Users: {{ details_for_discussion.system_context.users | join(', ') }}
    External Systems: {{ details_for_discussion.system_context.external_systems | join(', ') }}
    System Description: {{ details_for_discussion.system_context.description }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements') | tojson(indent=2) }}
    Non-Functional Requirements: {{ details_for_discussion.get('architectural_requirements') | tojson(indent=2) }}
{% endblock %}
