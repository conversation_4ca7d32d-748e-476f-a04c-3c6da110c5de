from pydantic import BaseModel, Field
from enum import Enum

class ReasoningEffort(str, Enum):
    MINIMAL = "minimal"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    
    def __str__(self):
        return self.value.lower()

    def __repr__(self):
        return f"{self.__class__.__name__}.{self.name}"
    

def get_options_for_model(model: str):
    try:
        model_lower = model.lower()
        
        if "gpt-5" in model_lower:
            return [str(effort) for effort in ReasoningEffort]
        elif any(x in model_lower for x in ["o3"]):
            return [str(effort) for effort in ReasoningEffort if effort != ReasoningEffort.MINIMAL]
        else:
            return []
    except Exception as e:
        print(f"Error getting options for model {model}: {e}")
        return []

def check_valid_reasoning_effort(model: str, effort: str) -> bool:
    try:
        valid_options = get_options_for_model(model)

        if effort in valid_options:
            return True
        else:
            return False
    except Exception as e:
        print(f"Error checking valid reasoning effort for model {model}: {e}")
        return False
    
