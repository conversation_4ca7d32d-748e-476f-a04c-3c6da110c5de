# app/models/figma_oauth_model.py

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from app.utils.datetime_utils import generate_timestamp
from app.connection.establish_db_connection import get_mongo_db


class FigmaOAuthSession(BaseModel):
    """Model for Figma OAuth session data"""
    tenant_id: str
    user_id: str
    state: str
    code_verifier: str
    created_at: str = Field(default_factory=lambda: generate_timestamp())
    expires_at: str = Field(default_factory=lambda: generate_timestamp())
    used: bool = Field(default=False)
    
    def __init__(self, **data):
        super().__init__(**data)
        # Set expiration to 10 minutes from creation
        from datetime import datetime, timedelta
        if not data.get('expires_at'):
            expires_time = datetime.utcnow() + timedelta(minutes=10)
            self.expires_at = expires_time.isoformat()
        else:
            # If expires_at is provided, ensure it's in the right format
            if isinstance(self.expires_at, str):
                try:
                    # Validate the datetime format
                    datetime.fromisoformat(self.expires_at.replace('Z', '+00:00'))
                except ValueError:
                    # If invalid, set a new expiration
                    expires_time = datetime.utcnow() + timedelta(minutes=10)
                    self.expires_at = expires_time.isoformat()


class FigmaOAuthCredentials(BaseModel):
    """Model for Figma OAuth credentials"""
    tenant_id: str
    user_id: str
    figma_user_id: str
    figma_email: str
    figma_name: str
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = Field(default="bearer")
    expires_in: Optional[int] = None
    expires_at: Optional[str] = None
    scope: str = Field(default="files:read teams:read")
    created_at: str = Field(default_factory=lambda: generate_timestamp())
    updated_at: str = Field(default_factory=lambda: generate_timestamp())
    is_active: bool = Field(default=True)


class FigmaFileData(BaseModel):
    """Model for Figma file data"""
    key: str
    name: str
    thumbnail_url: Optional[str] = None
    last_modified: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    url: str
    project_id: Optional[str] = None
    team_id: Optional[str] = None
    editor_type: Optional[str] = None
    link_access: Optional[str] = None
    current_user_role: Optional[str] = None
    user_permissions: Dict[str, Any] = Field(default_factory=dict)


class FigmaTeamData(BaseModel):
    """Model for Figma team data"""
    id: str
    name: str
    img_url: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class FigmaProjectData(BaseModel):
    """Model for Figma project data"""
    id: str
    name: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class FigmaOAuthManager:
    """Manager class for Figma OAuth operations"""
    
    def __init__(self):
        self.sessions_collection = "figma_oauth_sessions"
        self.credentials_collection = "figma_oauth_credentials"
    
    async def save_oauth_session(self, session_data: FigmaOAuthSession) -> bool:
        """Save OAuth session to database"""
        try:
            mongo_handler = get_mongo_db(collection_name=self.sessions_collection)
            
            session_dict = session_data.dict()
            result = await mongo_handler.insert(session_dict, mongo_handler.db)
            
            return bool(result)
        except Exception as e:
            print(f"Error saving OAuth session: {str(e)}")
            return False
    
    async def get_oauth_session(self, state: str) -> Optional[Dict[str, Any]]:
        """Get OAuth session by state"""
        try:
            mongo_handler = get_mongo_db(collection_name=self.sessions_collection)
            
            session = await mongo_handler.get_one(
                {"state": state, "used": False},
                mongo_handler.db
            )
            
            if not session:
                return None
            
            # Check if session has expired
            from datetime import datetime
            expires_at = session.get("expires_at")
            if expires_at:
                try:
                    expires_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                    if datetime.utcnow() > expires_time:
                        print(f"OAuth session expired: {state}")
                        return None
                except Exception as e:
                    print(f"Error parsing session expiration: {str(e)}")
                    return None
            
            return session
        except Exception as e:
            print(f"Error getting OAuth session: {str(e)}")
            return None
    
    async def mark_session_used(self, state: str) -> bool:
        """Mark OAuth session as used"""
        try:
            mongo_handler = get_mongo_db(collection_name=self.sessions_collection)
            
            result = await mongo_handler.update_one(
                filter={"state": state},
                element={"used": True, "updated_at": generate_timestamp()},
                db=mongo_handler.db
            )
            
            return bool(result)
        except Exception as e:
            print(f"Error marking session as used: {str(e)}")
            return False
    
    async def save_credentials(self, credentials_data: FigmaOAuthCredentials) -> bool:
        """Save OAuth credentials to database"""
        try:
            mongo_handler = get_mongo_db(collection_name=self.credentials_collection)
            
            credentials_dict = credentials_data.dict()
            
            # Use upsert to update existing credentials or create new ones
            result = await mongo_handler.update_one(
                filter={
                    "tenant_id": credentials_data.tenant_id,
                    "user_id": credentials_data.user_id
                },
                element=credentials_dict,
                upsert=True,
                db=mongo_handler.db
            )
            
            return bool(result)
        except Exception as e:
            print(f"Error saving OAuth credentials: {str(e)}")
            return False
    
    async def get_credentials(self, tenant_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get OAuth credentials for a user"""
        try:
            mongo_handler = get_mongo_db(collection_name=self.credentials_collection)
            
            print(f"Getting credentials for tenant_id: {tenant_id}, user_id: {user_id}")
            
            credentials = await mongo_handler.get_one(
                {
                    "tenant_id": tenant_id,
                    "user_id": user_id,
                    "is_active": True
                },
                mongo_handler.db
            )
            
            print(f"Found credentials: {credentials is not None}")
            if credentials:
                print(f"Credentials is_active: {credentials.get('is_active')}")
            
            return credentials
        except Exception as e:
            print(f"Error getting OAuth credentials: {str(e)}")
            return None
    
    async def update_credentials(self, tenant_id: str, user_id: str, update_data: Dict[str, Any]) -> bool:
        """Update OAuth credentials"""
        try:
            mongo_handler = get_mongo_db(collection_name=self.credentials_collection)
            
            update_data["updated_at"] = generate_timestamp()
            
            result = await mongo_handler.update_one(
                filter={
                    "tenant_id": tenant_id,
                    "user_id": user_id
                },
                element=update_data,
                db=mongo_handler.db
            )
            
            return bool(result)
        except Exception as e:
            print(f"Error updating OAuth credentials: {str(e)}")
            return False
    
    async def deactivate_credentials(self, tenant_id: str, user_id: str) -> bool:
        """Deactivate OAuth credentials"""
        try:
            mongo_handler = get_mongo_db(collection_name=self.credentials_collection)
            
            result = await mongo_handler.update_one(
                filter={
                    "tenant_id": tenant_id,
                    "user_id": user_id
                },
                element={
                    "is_active": False,
                    "updated_at": generate_timestamp()
                },
                db=mongo_handler.db
            )
            
            return bool(result)
        except Exception as e:
            print(f"Error deactivating OAuth credentials: {str(e)}")
            return False
    
    async def delete_credentials(self, tenant_id: str, user_id: str) -> bool:
        """Delete OAuth credentials"""
        try:
            mongo_handler = get_mongo_db(collection_name=self.credentials_collection)
            
            result = await mongo_handler.delete_by_filter(
                {
                    "tenant_id": tenant_id,
                    "user_id": user_id
                },
                db=mongo_handler.db
            )
            
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting OAuth credentials: {str(e)}")
            return False


# Global instance
figma_oauth_manager = FigmaOAuthManager() 