
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from enum import Enum


class ContainerTypeEnum(str, Enum):
    FRONTEND = "frontend"
    BACKEND = "backend"
    MOBILE = "mobile"
    DATABASE = "database"

class RouteModel(BaseModel):
    path: str = Field(..., description="API route path")
    endpoint: str = Field(..., description="HTTP method and endpoint")

class ColorSchemeModel(BaseModel):
    primary: Optional[str] = Field(None, description="Primary color in HEX format")
    secondary: Optional[str] = Field(None, description="Secondary color in HEX format") 
    accent: Optional[str] = Field(None, description="Accent color in HEX format")
    background: Optional[str] = Field(None, description="Background color in HEX format")

class ContainerDetailsModel(BaseModel):
    features: Optional[List[str]] = Field(default_factory=list, description="List of container features")
    layout_description: Optional[str] = Field(None, description="Layout description for the container")
    style: Optional[str] = Field(None, description="Style theme (modern, minimalistic, etc.)")
    theme: Optional[str] = Field(None, description="Color theme (light, dark, custom)")
    colors: Optional[ColorSchemeModel] = Field(None, description="Color scheme configuration")

class ContainerModel(BaseModel):
    container_name: str = Field(..., description="Unique container name")
    description: str = Field(..., description="Container purpose and functionality")
    container_type: ContainerTypeEnum = Field(..., description="Type of container")
    framework: str = Field(..., description="Examples for frontend framework: react,  nextjs, vue, svelte, angular, vite, nuxt and so on. For backend: express, fastapi, django, flask, springboot, etc.")
    interfaces: Optional[str] = Field(None, description="External interfaces and APIs")
    port: int = Field(None, description="Default port number e.g. 3000 for frontend, 3001 for backend")
    dependent_containers: Optional[List[str]] = Field(default_factory=list, description="Container dependencies")
    workspace: str = Field(None, description="Workspace directory path. Typically its always the same as repository name. eg.<repository_name>")
    container_root: Optional[str] = Field(None, description="This is the path inside the workspace where the code for this container is located. For example, if the container is a frontend app, it could be 'frontend-app' or 'client'. If it's a backend service, it could be 'backend-service' or 'api'. e.g. <repository_name>/frontend-app or <repository_name>/backend-service")
    routes: Optional[List[RouteModel]] = Field(default_factory=list, description="API routes")
    buildCommand: str = Field(..., description="Build command. This is a important field and should be provided valid command to build the container. E.g. for frontend it could be 'npm run build' or 'yarn build'. For backend it could be 'python setup.py install' or 'mvn package' etc.")
    startCommand: str = Field(..., description="Start command. This is a important field and should be provided valid command to start the container. E.g. for frontend it could be 'npm start' or 'yarn start'. For backend it could be 'python app.py' or 'java -jar app.jar' etc.")
    installCommand: str = Field(..., description="Install dependencies command. This is a important field and should be provided valid command to install dependencies. E.g. for frontend it could be 'npm install' or 'yarn install'. For backend it could be 'pip install -r requirements.txt' or 'mvn install' etc.")
    lintCommand: Optional[str] = Field(..., description="Linting command")
    generateOpenapiCommand: Optional[str] = Field(..., description="OpenAPI generation command")
    container_details: Optional[ContainerDetailsModel] = Field(None, description="Container design and feature details")
    env: Optional[Dict[str, str]] = Field(default_factory=dict, description="Environment variables")

class OverviewModel(BaseModel):
    project_name: str = Field(..., description="Project name")
    description: str = Field(..., description="Project description") 
    third_party_services: Optional[List[str]] = Field(default_factory=list, description="External services used")
    env: Optional[Dict[str, str]] = Field(default_factory=dict, description="Global environment variables")

class ManifestModel(BaseModel):
    overview: OverviewModel
    containers: List[ContainerModel] = Field(..., min_items=1, description="List of project containers")

