"use client";

import React, { useState, useCallback } from "react";
import {
  ChevronDown,
  MessageCircleQuestion,
  ArrowUpRight,
  ArrowRight,
  LucideIcon,
  ArrowLeft,
} from "lucide-react";
import Image from "next/image";
import kaviaLogo from "../../../public/logo/kavia_logo.svg";
import ChatInterface from "./ChatInterface";

// Types
interface ChatOptionData {
  title: string;
  description: string;
  link: string | null;
  icon: LucideIcon;
}

interface ChatOptionProps {
  title: string;
  description: string;
  icon: LucideIcon;
  onClick: () => void;
}

// Constants
const CHAT_OPTIONS: ChatOptionData[] = [
  {
    title: "Join Our Discord Community",
    description:
      "Join our Discord to connect with the community and get support.",
    link: "https://discord.gg/feVKV77YJh",
    icon: ArrowUpRight,
  },
  {
    title: "Give Feedback",
    description: "Identify performance bottlenecks and receive feedback.",
    link: "https://forms.gle/jyjby6wAsiYsnAs17",
    icon: <PERSON>UpR<PERSON>,
  },
  {
    title: "Ask KAVIA AI",
    description: "Get instant help from our AI assistant.",
    link: null,
    icon: ArrowRight,
  },
] as const;

const BUTTON_STYLES = {
  borderRadius: "48px",
  background:
    "linear-gradient(180deg, hsl(var(--primary)) 22.12%, #8C3D10 100%)",
} as const;

// Components
const ChatOption: React.FC<ChatOptionProps> = React.memo(
  ({ title, description, icon: Icon, onClick }) => (
    <div className="relative group w-full">
      {/* Animated gradient border */}
      <div className="absolute inset-1 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
        <div
          className="absolute inset-1 rounded-xl"
          style={{
            background: `linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 25%, #d1d5db 50%, #d1d5db 100%), 
                        linear-gradient(180deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 25%, #d1d5db 50%, #d1d5db 100%), 
                        linear-gradient(270deg, #d1d5db 0%, #d1d5db 100%), 
                        linear-gradient(0deg, #d1d5db 0%, #d1d5db 100%)`,
            backgroundSize: "100% 1px, 1px 100%, 100% 1px, 1px 100%",
            backgroundPosition: "top, left, bottom, right",
            backgroundRepeat: "no-repeat",
          }}
        />
      </div>

      <button
        onClick={onClick}
        className="min-h-[5rem] relative flex items-center justify-between w-full p-4 gap-4 rounded-xl bg-white border border-gray-200 hover:border-transparent text-left group overflow-hidden z-10 transition-all duration-200"
        type="button"
      >
        <div className="relative z-10 flex items-center justify-between w-full gap-4">
          <div className="flex-1 min-w-0">
            <div className="font-semibold text-gray-900 text-sm truncate">
              {title}
            </div>
            <div className="text-xs text-gray-500 font-normal line-clamp-2">
              {description}
            </div>
          </div>

          <div className="flex items-center justify-center bg-gray-100 rounded-full p-2 size-8 group-hover:bg-primary text-gray-600 group-hover:text-white transition-all duration-300 flex-shrink-0">
            <Icon size={18} />
          </div>
        </div>
      </button>
    </div>
  )
);

ChatOption.displayName = "ChatOption";

const PopupHeader: React.FC = React.memo(() => (
  <div className="flex items-center">
    <Image
      src={kaviaLogo}
      alt="Kavia Logo"
      width={24}
      height={24}
      className="mr-2"
      priority
    />
    <span className="font-normal text-xl text-gray-900">KAVIA AI</span>
  </div>
));

PopupHeader.displayName = "PopupHeader";

const ChatHeader: React.FC<{ onBack: () => void }> = React.memo(
  ({ onBack }) => (
    <div className="flex items-center justify-between p-4 bg-white border-b border-gray-100">
      <div className="flex items-center gap-3">
        <button
          onClick={onBack}
          className="flex items-center justify-center p-1 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Go back"
        >
          <ArrowLeft size={18} className="text-gray-700" />
        </button>
        <span className="font-medium text-lg text-gray-900">KAVIA AI</span>
      </div>
      <button className="p-1 hover:bg-gray-100 rounded-full transition-colors">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          className="text-gray-600"
        >
          <path
            d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
    </div>
  )
);

ChatHeader.displayName = "ChatHeader";

const WelcomeMessage: React.FC<{
  onChatOpen: () => void;
  onOptionClick: (option: ChatOptionData) => void;
}> = React.memo(({ onChatOpen, onOptionClick }) => (
  <div className="flex flex-col gap-8">
    <div>
      <div className="text-xl font-normal text-gray-700">
        Hi there{" "}
        <span className="text-xl" role="img" aria-label="wave">
          👋
        </span>
      </div>
      <div className="text-gray-900 text-xl font-normal">How can we help?</div>
    </div>

    <div className="flex flex-col gap-4">
      {CHAT_OPTIONS.map((option) => (
        <ChatOption
          key={option.title}
          title={option.title}
          description={option.description}
          icon={option.icon}
          onClick={() => onOptionClick(option)}
        />
      ))}
    </div>
  </div>
));

WelcomeMessage.displayName = "WelcomeMessage";

const PopupBackground: React.FC = React.memo(() => (
  <>
    <div className="absolute -top-48 left-1/2 -translate-x-1/2 w-96 h-96 rounded-full bg-[radial-gradient(circle,_#F26A1B_0%,_white_100%)] opacity-60 blur-[100px] pointer-events-none -z-10" />
    <div className="absolute -bottom-32 -right-32 w-96 h-96 rounded-full bg-[radial-gradient(circle,_#F26A1B_0%,_white_100%)] opacity-60 blur-[100px] pointer-events-none -z-10" />
  </>
));

PopupBackground.displayName = "PopupBackground";

// Main Component
const KaviaChatBot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showChat, setShowChat] = useState(false);

  const handleToggle = useCallback(() => {
    setIsOpen((prev) => !prev);
    if (isOpen) {
      setShowChat(false);
    }
  }, [isOpen]);

  const handleChatOpen = useCallback(() => {
    setShowChat(true);
  }, []);

  const handleChatBack = useCallback(() => {
    setShowChat(false);
  }, []);

  const openInNewTab = useCallback((url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
    setIsOpen(false);
  }, []);

  const handleOptionClick = useCallback(
    (option: ChatOptionData) => {
      if (option.title === "Ask KAVIA AI") {
        handleChatOpen();
      } else if (option.link) {
        openInNewTab(option.link);
      }
    },
    [handleChatOpen, openInNewTab]
  );

  return (
    <div className="fixed bottom-4  right-4 sm:bottom-6 sm:right-6 z-[999] flex flex-col items-end">
      {/* Popup Menu */}
      {isOpen && (
        <div className="w-[24rem] h-[32rem] rounded-xl shadow-2xl bg-white/95 backdrop-blur-lg border border-gray-100 animate-fadeIn mb-4 relative overflow-hidden">
          <PopupBackground />

          <div className="flex flex-col h-full relative z-10 overflow-hidden">
            {showChat ? (
              <ChatInterface onBack={handleChatBack} />
            ) : (
              <div className="flex flex-col h-full justify-between p-3 sm:p-4 md:p-6">
                <PopupHeader />
                <WelcomeMessage
                  onChatOpen={handleChatOpen}
                  onOptionClick={handleOptionClick}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Main Floating Button */}
      <button
        onClick={handleToggle}
        style={BUTTON_STYLES}
        className="w-12 h-12 sm:w-14 sm:h-14 flex items-center justify-center p-3 shadow-lg transition-all duration-300 text-white hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
        aria-label={isOpen ? "Close support menu" : "Open support menu"}
        type="button"
      >
        {!isOpen ? (
          <MessageCircleQuestion size={28} strokeWidth={1.5} />
        ) : (
          <ChevronDown size={28} strokeWidth={1.5} />
        )}
      </button>
    </div>
  );
};

export default KaviaChatBot;
