"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import TermsAcceptanceModal from './TermsAcceptanceModal';
import { useUser } from '@/components/Context/UserContext';
import { updateTermsAcceptance } from '@/utils/api';

const TermsAcceptanceWrapper = ({ children }) => {
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { has_accepted_terms, fetchUserData, user_id } = useUser();

  useEffect(() => {
    
    
    // Only check terms if user is logged in
    if (user_id) {
      // Only proceed if has_accepted_terms is not null (data has been loaded)
      if (has_accepted_terms !== null) {
        
        if (has_accepted_terms === false) {
          
          setShowModal(true);
        } else {
          
          setShowModal(false);
        }
        setIsLoading(false);
      } else {
        
      }
    } else {
      
      setShowModal(false);
      setIsLoading(false);
    }
  }, [has_accepted_terms, user_id]);

  const handleAccept = async () => {
    try {
      const response = await updateTermsAcceptance();
      // Don't close modal immediately - let the modal handle success state first
      fetchUserData(); // Refresh user data
      return response; // Return the response for success message
    } catch (error) {
      console.error('Error accepting terms:', error);
      // Re-throw the error so the modal can handle it
      throw error;
    }
  };

  const handleLogout = () => {
    // Clear cookies and redirect to login
    document.cookie = "idToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "userId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "tenant_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    router.push('/users/login');
  };

  if (isLoading) {
    return null; // or a loading spinner
  }

  return (
    <>
      {showModal && (
        <TermsAcceptanceModal
          onAccept={handleAccept}
          onLogout={handleLogout}
        />
      )}
      {children}
    </>
  );
};

export default TermsAcceptanceWrapper; 