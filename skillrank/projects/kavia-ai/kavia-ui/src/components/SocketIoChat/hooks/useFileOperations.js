import { useState, useRef, useCallback } from 'react';

export const useFileOperations = (messages, autoScroll, messagesContainerRef, isAtBottom) => {
  // State for tracking file operations
  const [fileOperationsMap, setFileOperationsMap] = useState({});
  const [pendingFileOperations, setPendingFileOperations] = useState({});
  const [fileOpsUpdateCounter, setFileOpsUpdateCounter] = useState(0);
  
  // Add refs for tracking updates
  const isMessageUpdateScheduled = useRef(false);

  // Process file_updates and check_points from messages
  const processMessageFileUpdates = useCallback((message) => {
    if (!message || !message.id) return;

    // Process file_updates if they exist
    if (message.file_updates && Array.isArray(message.file_updates) && message.file_updates.length > 0) {
      const operations = message.file_updates.map(update => ({
        id: `${update.message_id || message.id}-${update.file_path}`,
        status: 'completed', // All file updates in the new structure are considered completed
        label: update.operation || 'write',
        path: update.file_path || update.file_name,
        operation: update.operation,
        content: update.content,
        timestamp: update.timestamp,
        sender: update.sender,
        msg_type: update.msg_type || 'file_update'
      }));

      setFileOperationsMap(prevMap => {
        const existingOps = prevMap[message.id] || [];
        const combinedOps = [...existingOps, ...operations];
        const uniqueOpsMap = new Map(combinedOps.map(op => [op.id, op]));
        return {
          ...prevMap,
          [message.id]: Array.from(uniqueOpsMap.values())
        };
      });

      // Trigger UI update
      setFileOpsUpdateCounter(prev => prev + 1);
    }
  }, []);

  // Update the file operations handling function
  const updateFileOperations = useCallback((messageId, operation, filePath, isEnd, operationId, forceUpdate = false) => {
    // Generate a consistent operation ID if not provided
    const opId = operationId || `${messageId}-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`;

    // For non-end operations, store them in pending state
    if (!isEnd && operation !== 'edit') {
      setPendingFileOperations(prev => ({
        ...prev,
        [opId]: { messageId, operation, filePath, operationId: opId, status: 'processing' }
      }));
    }

    // Update the main file operations map
    setFileOperationsMap(prevMap => {
      // Find the actual message ID to use
      const targetMessage = messages.find(m => m.id === messageId ||
        (typeof m.id === 'string' && typeof messageId === 'string' &&
        (m.id.includes(messageId) || messageId.includes(m.id))));

      const actualMessageId = targetMessage?.id || messageId;

      // Get existing operations or create new array
      const existingOps = prevMap[actualMessageId] || [];

      // Check if this operation already exists
      const existingOpIndex = existingOps.findIndex(op =>
        op.id === opId || op.path === filePath
      );

      // Create updated operations array
      let updatedOps;
      if (existingOpIndex >= 0) {
        // Update existing operation
        updatedOps = [...existingOps];
        updatedOps[existingOpIndex] = {
          ...updatedOps[existingOpIndex],
          status: isEnd || operation === 'edit' ? 'completed' : 'processing',
          label: operation || "",
          path: filePath,
          id: opId
        };
      } else {
        // Add new operation
        updatedOps = [
          ...existingOps,
          {
            id: opId,
            status: isEnd || operation === 'edit' ? 'completed' : 'processing',
            label: operation || "",
            path: filePath
          }
        ];
      }

      // Create new map with updated operations
      return { ...prevMap, [actualMessageId]: updatedOps };
    });

    // For completed operations or forced updates, trigger UI update
    if (isEnd || forceUpdate || operation === 'edit') {
      // Remove from pending operations if this is a completion
      if (isEnd || operation === 'edit') {
        setPendingFileOperations(prev => {
          const newPending = {...prev};
          delete newPending[opId];
          return newPending;
        });
      }

      // Batch update counter changes to reduce rerenders
      if (!isMessageUpdateScheduled.current) {
        isMessageUpdateScheduled.current = true;
        setTimeout(() => {
          setFileOpsUpdateCounter(prev => prev + 1);
          isMessageUpdateScheduled.current = false;

          // Minimal scrolling for file operations
          if (autoScroll && isAtBottom()) {
            setTimeout(() => {
              if (messagesContainerRef.current) {
                messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
              }
            }, 100);
          }
        }, 50);
      }
    }
  }, [messages, autoScroll, messagesContainerRef, isAtBottom]);

  return {
    fileOperationsMap,
    pendingFileOperations,
    fileOpsUpdateCounter,
    updateFileOperations,
    processMessageFileUpdates
  };
}; 