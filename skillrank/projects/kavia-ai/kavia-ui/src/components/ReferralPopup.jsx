import React, { useState, useCallback, useEffect } from "react";
import { validateReferralCode } from "../utils/api";
import { CheckCircle, AlertCircle, Loader2, X, ChevronRight } from "lucide-react";

const ReferralPopup = ({ isOpen, onClose, onSubmit, initialReferralCode = "" }) => {
  const [referralCode, setReferralCode] = useState(initialReferralCode);
  const [referralValidation, setReferralValidation] = useState({
    isValidating: false,
    isValid: false,
    referrerInfo: null,
    error: null
  });

  // Debounced referral code validation
  const validateReferral = useCallback(async (code) => {
    if (!code || code.length < 6) {
      setReferralValidation({
        isValidating: false,
        isValid: false,
        referrerInfo: null,
        error: null
      });
      return;
    }

    if (code.length >= 6) {
      setReferralValidation(prev => ({ ...prev, isValidating: true, error: null }));

      try {
        const result = await validateReferralCode(code);

        if (result && result.valid) {
          setReferralValidation({
            isValidating: false,
            isValid: true,
            referrerInfo: {
              name: result.referrer_name,
              organization: result.referrer_organization
            },
            error: null
          });
        } else {
          setReferralValidation({
            isValidating: false,
            isValid: false,
            referrerInfo: null,
            error: result?.message || 'Invalid referral code'
          });
        }
      } catch (error) {
        setReferralValidation({
          isValidating: false,
          isValid: false,
          referrerInfo: null,
          error: 'Failed to validate referral code'
        });
      }
    }
  }, []);

  // Effect to validate referral code when it changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (referralCode) {
        validateReferral(referralCode.toUpperCase());
      } else {
        setReferralValidation({
          isValidating: false,
          isValid: false,
          referrerInfo: null,
          error: null
        });
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [referralCode, validateReferral]);

  const handleReferralCodeChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    setReferralCode(value);
  };

  const handleContinue = () => {
    onSubmit(referralCode);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="relative w-full max-w-xl mx-4 rounded-2xl border border-white/10 bg-zinc-900/80 p-8 shadow-2xl ring-1 ring-white/10">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-zinc-500 hover:text-zinc-300"
          aria-label="Close"
        >
          <X className="h-5 w-5" />
        </button>

        <div className="text-center">
          <h3 className="text-2xl font-semibold tracking-tight text-white">
            Do you have a referral code?
          </h3>
          <p className="mt-2 text-sm text-zinc-400">
            Optional — you can skip by leaving it blank
          </p>
        </div>

        <div className="mt-6">
          <div className="relative">
            <input
              id="referralCode"
              type="text"
              value={referralCode}
              onChange={handleReferralCodeChange}
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleContinue();
              }}
              autoFocus
              placeholder="Enter referral code..."
              maxLength={12}
              className={`h-12 w-full rounded-xl border bg-zinc-800/60 pl-4 pr-12 text-zinc-100 placeholder-zinc-500 outline-none transition-colors focus:ring-2 focus:ring-white/20 ${
                referralValidation.isValidating
                  ? 'border-white/20'
                  : referralValidation.isValid
                    ? 'border-green-500/40'
                    : referralValidation.error
                      ? 'border-red-500/40'
                      : 'border-white/10'
              }`}
            />

            <button
              type="button"
              onClick={handleContinue}
              disabled={referralValidation.isValidating}
              className="absolute right-1.5 top-1.5 bottom-1.5 inline-flex w-9 items-center justify-center rounded-lg bg-white/10 text-zinc-200 transition-colors hover:bg-white/15 disabled:opacity-50"
              aria-label="Submit referral code"
            >
              {referralValidation.isValidating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <ChevronRight className="h-5 w-5" />
              )}
            </button>
          </div>

          {/* Subtle status line */}
          {referralValidation.isValid && referralValidation.referrerInfo && (
            <div className="mt-2 flex items-center justify-center text-xs text-green-400">
              <CheckCircle className="mr-1 h-3 w-3" />
              Referred by <span className="ml-1 font-medium">{referralValidation.referrerInfo.name}</span>
            </div>
          )}
          {referralValidation.error && (
            <div className="mt-2 flex items-center justify-center text-xs text-red-400">
              <AlertCircle className="mr-1 h-3 w-3" />
              {referralValidation.error}
            </div>
          )}
        </div>

        {/* Skip action removed; empty input submission already implies skip */}
      </div>
    </div>
  );
};

export default ReferralPopup;
