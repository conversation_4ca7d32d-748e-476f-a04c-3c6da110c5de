import React, { useEffect, useState, useContext } from 'react';
import { Link2, ChevronLeft, ChevronRight, ChevronDown, Plus, Check } from 'lucide-react';
import SupabaseConnectionModal from './SupabaseConnectionModal';
import { connectToSupabase, disconnectSupabaseProject } from '@/utils/api';
import { getGitHubConfigurations, getGitLabConfigurations } from '@/utils/scmAPI';
import { getOAuthLoginURL } from '@/utils/scmAPI';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { GitHubLogo, GitlabLogo, SupabaseLogo } from './ExternalIntegrationLogos';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  status: 'connected' | 'not_connected' | 'locked';
  color: {
    bg?: string;
    text: string;
    border: string;
    lightBg?: string;
  };
}

const ExternalIntegrations: React.FC = () => {
  const [supabaseModalOpen, setSupabaseModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedGithubOrg, setSelectedGithubOrg] = useState<any>(null);
  const [selectedGitlabOrg, setSelectedGitlabOrg] = useState<any>(null);
  const [showGithubDropdown, setShowGithubDropdown] = useState(false);
  const [showGitlabDropdown, *********************] = useState(false);
  const [githubOrganizations, setGithubOrganizations] = useState([]);
  const [gitlabOrganizations, **********************] = useState([]);
  const [loadingGithubOrgs, setLoadingGithubOrgs] = useState(false);
  const [loadingGitlabOrgs, setLoadingGitlabOrgs] = useState(false);
  const [connectingGithub, setConnectingGithub] = useState(false);
  const [connectingGitlab, setConnectingGitlab] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const [loadingIntegration, setLoadingIntegration] = useState<string | null>(null);

  // Integration status for non-SCM integrations
  const [integrationStatus, setIntegrationStatus] = useState<Record<string, 'connected' | 'not_connected'>>({
    supabase: 'not_connected',
    stripe: 'not_connected'
  });

  // Initialize session storage on mount
  useEffect(() => {
    sessionStorage.setItem('selected_github_id', '');
    sessionStorage.setItem('selected_gitlab_id', '');
  }, []);

  const checkSupabaseConnection = async () => {
    try {
      const projectId = sessionStorage.getItem('generated_project_id');
      if (!projectId) return;

      const response = await connectToSupabase(projectId.toString());
      if (response && response.status === "already_connected") {
        setIntegrationStatus(prev => ({
          ...prev,
          supabase: 'connected'
        }));
      }
    } catch (error) {
      console.error("Error checking supabase connection:", error);
    }
  };

  const loadGitHubOrganizations = async () => {
    setLoadingGithubOrgs(true);
    try {
      const response: any = await getGitHubConfigurations();
      if (response && response.data && response.data.configurations) {
        setGithubOrganizations(response.data.configurations);
      }
    } catch (error) {
      console.error("Error loading GitHub organizations:", error);
    } finally {
      setLoadingGithubOrgs(false);
    }
  };

  const loadGitLabOrganizations = async () => {
    setLoadingGitlabOrgs(true);
    try {
      const response: any = await getGitLabConfigurations();
      if (response && response.data && response.data.configurations) {
        **********************(response.data.configurations);
      }
    } catch (error) {
      console.error("Error loading GitLab organizations:", error);
    } finally {
      setLoadingGitlabOrgs(false);
    }
  };

  const integrations: Integration[] = [

    {
      id: 'supabase',
      name: 'Supabase',
      description: 'Database & Authentication',
      icon: <SupabaseLogo />,
      status: integrationStatus.supabase,
      color: {
        text: 'text-green-500',
        border: 'border-green-500',
        lightBg: 'bg-green-50'
      }
    },

        {
      id: 'github',
      name: 'GitHub',
      description: 'Code Repository & Version Control',
      icon: <GitHubLogo />,
      status: selectedGithubOrg ? 'connected' : 'not_connected',
      color: {
        bg: 'bg-gray-900',
        text: 'text-gray-900',
        border: 'border-gray-900',
        lightBg: 'bg-gray-50'
      }
    },
    {
      id: 'gitlab',
      name: 'GitLab',
      description: 'Code Repository & Version Control',
      icon: <GitlabLogo />,
      status: selectedGitlabOrg ? 'connected' : 'not_connected',
      color: {
        bg: 'bg-white-600',
        text: 'text-orange-600',
        border: 'border-orange-600',
        lightBg: 'bg-orange-50'
      }
    },
  ];

  // Pagination settings
  const itemsPerPage = 2;
  const totalPages = Math.ceil(integrations.length / itemsPerPage);
  const showCarouselControls = integrations.length > itemsPerPage;

  // Get current page items
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentIntegrations = integrations.slice(startIndex, endIndex);

  const handleConnect = (integrationId: string) => {
    const integration = integrations.find(i => i.id === integrationId);
    if (integration?.status === 'locked') {
      showAlert('This integration is coming soon!', 'info');
      return;
    }

    if (integrationId === 'supabase') {
      setSupabaseModalOpen(true);
    } else if (integrationId === 'github') {
      setShowGithubDropdown(true);
    } else if (integrationId === 'gitlab') {
      *********************(true);
    }
  };

  const handleDisconnect = async (integrationId: string) => {
    if (integrationId === 'github') {
      setSelectedGithubOrg(null);
      setShowGithubDropdown(false);
      sessionStorage.setItem('selected_github_id', '');
    } else if (integrationId === 'gitlab') {
      setSelectedGitlabOrg(null);
      *********************(false);
      sessionStorage.setItem('selected_gitlab_id', '');
    } else if (integrationId === 'supabase') {
      const projectId = sessionStorage.getItem('generated_project_id');
      if (!projectId) return;

      try {
        setLoadingIntegration(integrationId);
        const response = await disconnectSupabaseProject(projectId.toString());
        if (response.success && response.status === 'disconnected') {
          setIntegrationStatus(prev => ({
            ...prev,
            [integrationId]: 'not_connected'
          }));
          showAlert("Supabase disconnected successfully", "success");
        }
      } catch (error) {
        showAlert("Failed to disconnect the supabase", "danger");
      } finally {
        setLoadingIntegration(null);
      }
    } else {
      setIntegrationStatus(prev => ({
        ...prev,
        [integrationId]: 'not_connected'
      }));
    }
  };

  const handleSupabaseConnectionComplete = () => {
    setIntegrationStatus(prev => ({
      ...prev,
      supabase: 'connected'
    }));
    setSupabaseModalOpen(false);
  };

  const goToPreviousPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  const handleSupabaseModalClose = () => {
    setSupabaseModalOpen(false);
    setTimeout(() => {
      checkSupabaseConnection();
    }, 100);
  };

  // Handle GitHub organization selection
  const handleGithubOrgChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedId = e.target.value;
    if (selectedId) {
      sessionStorage.setItem("selected_github_id", selectedId);
      const selected = githubOrganizations.find(
        (org: any) => org.encrypted_scm_id === selectedId
      );
      setSelectedGithubOrg(selected || null);
    } else {
      setSelectedGithubOrg(null);
      sessionStorage.setItem('selected_github_id', '');
    }
  };

  // Handle GitLab organization selection
  const handleGitlabOrgChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedId = e.target.value;
    if (selectedId) {
      sessionStorage.setItem("selected_gitlab_id", selectedId);
      const selected = gitlabOrganizations.find(
        (org: any) => org.encrypted_scm_id === selectedId
      );
      setSelectedGitlabOrg(selected || null);
    } else {
      setSelectedGitlabOrg(null);
      sessionStorage.setItem('selected_gitlab_id', '');
    }
  };

  useEffect(() => {
    checkSupabaseConnection();
    loadGitHubOrganizations();
    loadGitLabOrganizations();
  }, []);

  const handleGitHubConnect = async () => {
    try {
      setConnectingGithub(true);
      sessionStorage.setItem("providerId","github")
      const data: any = await getOAuthLoginURL('github');
      const url = data.data.url;
      const width = 1028, height = 700;
      const left = (window.screen.width / 2) - (width / 2);
      const top = (window.screen.height / 2) - (height / 2);

      const popup = window.open(
        url,
        'GitHub OAuth',
        `width=${width},height=${height},top=${top},left=${left}`
      );

      if (!popup) {
        setConnectingGithub(false);
        return;
      }

      let messageReceived = false;

      const handleMessage = async (event: any) => {
        if (event.data === 'github_connected') {
          messageReceived = true;
          await loadGitHubOrganizations();
          setShowGithubDropdown(true);
          cleanup();
        }
      };

      const cleanup = () => {
        setConnectingGithub(false);
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
      };

      const checkClosed = setInterval(async () => {
        if (popup.closed) {
          await loadGitHubOrganizations();
          if (messageReceived) {
            setShowGithubDropdown(true);
          }
          cleanup();
        }
      }, 1000);

      window.addEventListener('message', handleMessage);

      setTimeout(() => {
        if (!popup.closed) {
          popup.close();
        }
        cleanup();
      }, 5 * 60 * 1000);

    } catch (error) {
      console.error('GitHub OAuth failed', error);
      setConnectingGithub(false);
    }
  };

  const handleGitLabConnect = async () => {
    try {
      setConnectingGitlab(true);
      sessionStorage.setItem("providerId","gitlab")
      const data: any = await getOAuthLoginURL('gitlab');
      const url = data.data.url;
      const width = 1028, height = 700;
      const left = (window.screen.width / 2) - (width / 2);
      const top = (window.screen.height / 2) - (height / 2);

      const popup = window.open(
        url,
        'GitLab OAuth',
        `width=${width},height=${height},top=${top},left=${left}`
      );

      if (!popup) {
        setConnectingGitlab(false);
        return;
      }

      let messageReceived = false;

      const handleMessage = async (event: any) => {
        if (event.data === 'gitlab_connected') {
          messageReceived = true;
          await loadGitLabOrganizations();
          *********************(true);
          cleanup();
        }
      };

      const cleanup = () => {
        setConnectingGitlab(false);
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
      };

      const checkClosed = setInterval(async () => {
        if (popup.closed) {
          await loadGitLabOrganizations();
          if (messageReceived) {
            *********************(true);
          }
          cleanup();
        }
      }, 1000);

      window.addEventListener('message', handleMessage);

      setTimeout(() => {
        if (!popup.closed) {
          popup.close();
        }
        cleanup();
      }, 5 * 60 * 1000);

    } catch (error) {
      console.error('GitLab OAuth failed', error);
      setConnectingGitlab(false);
    }
  };

  const renderSCMIntegration = (integration: Integration) => {
    const isGitHub = integration.id === 'github';
    const isGitLab = integration.id === 'gitlab';
    
    if (!isGitHub && !isGitLab) return null;

    const selectedOrg = isGitHub ? selectedGithubOrg : selectedGitlabOrg;
    const showDropdown = isGitHub ? showGithubDropdown : showGitlabDropdown;
    const organizations = isGitHub ? githubOrganizations : gitlabOrganizations;
    const loadingOrgs = isGitHub ? loadingGithubOrgs : loadingGitlabOrgs;
    const connecting = isGitHub ? connectingGithub : connectingGitlab;
    const handleOrgChange = isGitHub ? handleGithubOrgChange : handleGitlabOrgChange;
    const handleConnect = isGitHub ? handleGitHubConnect : handleGitLabConnect;
    const storageKey = isGitHub ? 'selected_github_id' : 'selected_gitlab_id';

    return (
      <div className="mt-4 space-y-3">
        {/* Selected Organization Display */}
        {selectedOrg && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Check size={16} className="text-green-600" />
                <span className="typography-body-sm font-weight-medium text-green-800">
                  {selectedOrg.organization || selectedOrg.credentials?.organization}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Account Selection */}
        {(showDropdown || organizations.length > 0) && !selectedOrg && (
          <div className="space-y-2">
            <label className="typography-caption text-gray-600 font-weight-medium block">
              Select {integration.name} Account
            </label>
            <div className="relative">
              <select
                value=""
                onChange={handleOrgChange}
                disabled={loadingOrgs}
                className={`w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary appearance-none bg-white typography-body-sm ${
                  loadingOrgs ? 'cursor-not-allowed opacity-60' : ''
                }`}
              >
                <option value="">
                  {loadingOrgs ? 'Loading accounts...' : 'Choose an account'}
                </option>
                {!loadingOrgs && organizations.map((org: any) => (
                  <option key={org.encrypted_scm_id} value={org.encrypted_scm_id}>
                    {org.organization || org.credentials?.organization}
                  </option>
                ))}
              </select>
              <ChevronDown size={16} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
            </div>
          </div>
        )}

        {/* Connect New Account Button */}
        <div className="flex items-center space-x-2">
          <button
            onClick={handleConnect}
            disabled={connecting || loadingOrgs}
            className={`flex-1 px-4 py-2.5 border ${integration.color.border} ${integration.color.text} bg-white rounded-lg hover:bg-gray-50 flex items-center justify-center transition-all duration-200 typography-body-sm font-weight-medium ${
              (connecting || loadingOrgs) ? 'cursor-not-allowed opacity-60' : ''
            }`}
          >
            {connecting ? (
              <>
                <div className={`animate-spin rounded-full h-4 w-4 border-b-2 ${integration.color.border.replace('border-', 'border-b-')} mr-2`}></div>
                Connecting...
              </>
            ) : (
              <>
                <Plus size={16} className="mr-2" />
                {organizations.length > 0 ? 'Add Account' : `Connect ${integration.name}`}
              </>
            )}
          </button>

          {/* Disconnect Button */}
          {selectedOrg && (
            <button
              onClick={() => handleDisconnect(integration.id)}
              className="px-4 py-2.5 border border-red-300 text-red-600 bg-white rounded-lg hover:bg-red-50 typography-body-sm font-weight-medium transition-colors duration-200"
            >
              Disconnect
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="mb-6">
        <h3 className="typography-body-lg font-weight-medium mb-4 text-gray-800">
          Third party Integrations
        </h3>

        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {currentIntegrations.map((integration) => {
              const isConnected = integration.status === 'connected';
              const isLocked = integration.status === 'locked';
              const isSCM = integration.id === 'github' || integration.id === 'gitlab';

              return (
                <div
                  key={integration.id}
                  className={`border rounded-lg p-4 bg-white transition-all duration-200 ${
                    isConnected
                      ? `border-green-500 bg-green-50`
                      : isLocked
                      ? 'border-gray-300 bg-gray-50 opacity-75'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div
                      className={`w-10 h-10 rounded-full ${integration.color.bg || 'bg-gray-100'} flex items-center justify-center flex-shrink-0`}
                    >
                      {integration.icon}
                    </div>

                    <div className="flex-1">
                      <h4 className="typography-body-sm font-weight-medium text-gray-900">
                        {integration.name}
                      </h4>
                      <p className="typography-caption text-gray-500 mt-0.5">
                        {integration.description}
                      </p>

                      <div className="flex items-center mt-2">
                        {isLocked ? (
                          <>
                            <svg className="w-3 h-3 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                            </svg>
                            <span className="typography-caption text-gray-500">
                              Coming Soon
                            </span>
                          </>
                        ) : (
                          <>
                            <span className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                            <span className={`typography-caption ${isConnected ? 'text-green-600' : 'text-gray-500'}`}>
                              {isConnected ? 'Connected' : 'Not Connected'}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* SCM Special Handling */}
                  {isSCM ? (
                    renderSCMIntegration(integration)
                  ) : (
                    /* Other Integrations Connect/Disconnect */
                    <div className="mt-4">
                      {isLocked ? (
                        <button
                          disabled
                          className="w-full px-4 py-2.5 border border-gray-300 text-gray-400 bg-gray-100 rounded-lg cursor-not-allowed flex items-center justify-center transition-colors duration-200 typography-body-sm font-weight-medium"
                        >
                          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                          Coming Soon
                        </button>
                      ) : isConnected ? (
                        <button
                          onClick={() => handleDisconnect(integration.id)}
                          className="w-full px-4 py-2.5 border border-gray-300 text-gray-600 bg-white rounded-lg hover:bg-gray-50 flex items-center justify-center transition-colors duration-200 typography-body-sm font-weight-medium"
                          disabled={loadingIntegration === 'supabase'}
                        >
                          {loadingIntegration === 'supabase' ? (
                            <div className="flex items-center gap-2">
                              <svg
                                className="w-4 h-4 animate-spin text-gray-600"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  className="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                ></circle>
                                <path
                                  className="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8v8H4z"
                                ></path>
                              </svg>
                              Disconnecting...
                            </div>
                          ) : (
                            "Disconnect"
                          )}
                        </button>
                      ) : (
                        <button
                          onClick={() => handleConnect(integration.id)}
                          className={`w-full px-4 py-2.5 border ${integration.color.border} ${integration.color.text} bg-white rounded-lg hover:bg-gray-50 flex items-center justify-center transition-colors duration-200 typography-body-sm font-weight-medium`}
                        >
                          <Link2 size={16} className="mr-2" />
                          Connect
                        </button>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Carousel Controls */}
          {showCarouselControls && (
            <>
              <button
                onClick={goToPreviousPage}
                disabled={currentPage === 0}
                className={`absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white rounded-full shadow-lg p-2 transition-all duration-200 ${
                  currentPage === 0
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-xl hover:scale-110 cursor-pointer'
                }`}
                aria-label="Previous page"
              >
                <ChevronLeft size={20} className="text-gray-600" />
              </button>

              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages - 1}
                className={`absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white rounded-full shadow-lg p-2 transition-all duration-200 ${
                  currentPage === totalPages - 1
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-xl hover:scale-110 cursor-pointer'
                }`}
                aria-label="Next page"
              >
                <ChevronRight size={20} className="text-gray-600" />
              </button>
            </>
          )}
        </div>

        {/* Dot Indicators */}
        {showCarouselControls && (
          <div className="flex justify-center items-center space-x-2 mt-4">
            {Array.from({ length: totalPages }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPage(index)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  index === currentPage
                    ? 'bg-gray-800 w-6'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Go to page ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      <SupabaseConnectionModal
        isOpen={supabaseModalOpen}
        onClose={handleSupabaseModalClose}
        onConnectionComplete={handleSupabaseConnectionComplete}
      />
    </>
  );
};

export default ExternalIntegrations;