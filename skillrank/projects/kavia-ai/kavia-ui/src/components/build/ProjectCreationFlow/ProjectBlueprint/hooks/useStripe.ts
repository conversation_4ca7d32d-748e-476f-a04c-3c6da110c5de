import { useState, useEffect, useCallback } from 'react';
import { stripeService } from '../services/stripeService';
import {
  StripeProduct,
  StripeConnectionState,
  StripeUIState,
  StripeIntegrationSettings
} from '../types/stripe';

/**
 * Hook for managing Stripe connection state
 * Provides methods to connect, verify, and manage Stripe integration
 */
export function useStripeConnection() {
  const [connectionState, setConnectionState] = useState<StripeConnectionState>(() => 
    stripeService.getConnectionState()
  );

  const connect = useCallback(async (apiKey: string) => {
    try {
      const result = await stripeService.verifyConnection(apiKey);
      setConnectionState(result);
      return result;
    } catch (error) {
      const errorState: StripeConnectionState = {
        isConnected: false,
        isVerifying: false,
        errors: [{
          code: 'connection_failed',
          message: error instanceof Error ? error.message : 'Unknown error',
          type: 'authentication_error'
        }]
      };
      setConnectionState(errorState);
      return errorState;
    }
  }, []);

  const disconnect = useCallback(() => {
    stripeService.disconnect();
    setConnectionState(stripeService.getConnectionState());
  }, []);

  const refreshConnectionState = useCallback(() => {
    setConnectionState(stripeService.getConnectionState());
  }, []);

  return {
    connectionState,
    connect,
    disconnect,
    refreshConnectionState,
    isConnected: connectionState.isConnected,
    isVerifying: connectionState.isVerifying,
    errors: connectionState.errors || [],
    account: connectionState.account,
    lastVerified: connectionState.lastVerified
  };
}

/**
 * Hook for managing Stripe products
 * Provides methods to fetch, create, update, and select products
 */
export function useStripeProducts() {
  const [products, setProducts] = useState<StripeProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);

  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const fetchedProducts = await stripeService.getProducts();
      setProducts(fetchedProducts);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch products');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const refreshedProducts = await stripeService.refreshProducts();
      setProducts(refreshedProducts);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh products');
    } finally {
      setLoading(false);
    }
  }, []);

  const toggleProductSelection = useCallback((productId: string) => {
    setSelectedProductIds(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });

    // Update the product's selected state for UI consistency
    setProducts(prev => 
      prev.map(product => 
        product.id === productId 
          ? { ...product, selected: !product.selected }
          : product
      )
    );
  }, []);

  const selectProducts = useCallback((productIds: string[]) => {
    setSelectedProductIds(productIds);
    setProducts(prev => 
      prev.map(product => ({
        ...product,
        selected: productIds.includes(product.id)
      }))
    );
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedProductIds([]);
    setProducts(prev => 
      prev.map(product => ({ ...product, selected: false }))
    );
  }, []);

  const getSelectedProducts = useCallback((): StripeProduct[] => {
    return products.filter(product => selectedProductIds.includes(product.id));
  }, [products, selectedProductIds]);

  return {
    products,
    loading,
    error,
    selectedProductIds,
    selectedProducts: getSelectedProducts(),
    fetchProducts,
    refreshProducts,
    toggleProductSelection,
    selectProducts,
    clearSelection,
    hasProducts: products.length > 0,
    hasSelection: selectedProductIds.length > 0
  };
}

/**
 * Hook for managing complete Stripe modal state
 * Combines connection and product management with UI state
 */
export function useStripeModal() {
  const connection = useStripeConnection();
  const products = useStripeProducts();
  
  const [uiState, setUIState] = useState<StripeUIState>({
    currentStep: 'api-key',
    apiKey: '',
    products: [],
    selectedProducts: [],
    loading: false,
    error: ''
  });

  // Update UI state when products change
  useEffect(() => {
    setUIState(prev => ({
      ...prev,
      products: products.products
    }));
  }, [products.products]);

  // Update UI state when selection changes
  useEffect(() => {
    setUIState(prev => ({
      ...prev,
      selectedProducts: products.selectedProductIds
    }));
  }, [products.selectedProductIds]);

  const setCurrentStep = useCallback((step: StripeUIState['currentStep']) => {
    setUIState(prev => ({ ...prev, currentStep: step }));
  }, []);

  const setApiKey = useCallback((apiKey: string) => {
    setUIState(prev => ({ ...prev, apiKey }));
  }, []);

  const setError = useCallback((error: string) => {
    setUIState(prev => ({ ...prev, error }));
  }, []);

  const clearError = useCallback(() => {
    setUIState(prev => ({ ...prev, error: '' }));
  }, []);

  const handleApiKeySubmit = useCallback(async () => {
    if (!uiState.apiKey.trim()) {
      setError('Please enter your Stripe API key');
      return false;
    }

    if (!uiState.apiKey.startsWith('sk_test_') && !uiState.apiKey.startsWith('pk_test_')) {
      setError('Please enter a valid Stripe API key');
      return false;
    }

    clearError();
    setCurrentStep('verifying');

    try {
      const result = await connection.connect(uiState.apiKey);
      
      if (result.isConnected) {
        // Automatically fetch products after successful connection
        await products.fetchProducts();
        setCurrentStep('product-selection');
        return true;
      } else {
        setError(result.errors?.[0]?.message || 'Connection failed');
        setCurrentStep('api-key');
        return false;
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Connection failed');
      setCurrentStep('api-key');
      return false;
    }
  }, [uiState.apiKey, connection, products, clearError, setError, setCurrentStep]);

  const handleProductRefresh = useCallback(async () => {
    try {
      await products.refreshProducts();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to refresh products');
    }
  }, [products, setError]);

  const handleCompleteSetup = useCallback(async () => {
    if (products.products.length > 0 && !products.hasSelection) {
      setError('Please select at least one product');
      return false;
    }

    clearError();
    setCurrentStep('completing');

    // Simulate setup completion
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setCurrentStep('success');
    return true;
  }, [products.products.length, products.hasSelection, setError, clearError, setCurrentStep]);

  const resetModal = useCallback(() => {
    setUIState({
      currentStep: 'api-key',
      apiKey: '',
      products: [],
      selectedProducts: [],
      loading: false,
      error: ''
    });
    products.clearSelection();
    connection.disconnect();
  }, [products, connection]);

  return {
    // UI State
    ...uiState,
    setCurrentStep,
    setApiKey,
    setError,
    clearError,
    
    // Connection state
    connection,
    
    // Products state
    products,
    
    // Combined operations
    handleApiKeySubmit,
    handleProductRefresh,
    handleCompleteSetup,
    resetModal,
    
    // Computed state
    canProceedFromProducts: products.hasProducts ? products.hasSelection : true,
    isTestMode: stripeService.isTestMode(uiState.apiKey)
  };
}

/**
 * Hook for managing Stripe integration settings
 * Handles configuration and preferences for the integration
 */
export function useStripeIntegration() {
  const [settings, setSettings] = useState<StripeIntegrationSettings>({
    selectedProducts: [],
    webhookEvents: ['customer.created', 'invoice.payment_succeeded', 'customer.subscription.updated'],
    customMetadata: {},
    defaultCurrency: 'usd',
    testMode: true
  });

  const updateSettings = useCallback((updates: Partial<StripeIntegrationSettings>) => {
    setSettings(prev => ({ ...prev, ...updates }));
  }, []);

  const updateSelectedProducts = useCallback((productIds: string[]) => {
    setSettings(prev => ({ ...prev, selectedProducts: productIds }));
  }, []);

  const updateWebhookEvents = useCallback((events: string[]) => {
    setSettings(prev => ({ ...prev, webhookEvents: events }));
  }, []);

  const updateMetadata = useCallback((key: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      customMetadata: { ...prev.customMetadata, [key]: value }
    }));
  }, []);

  const removeMetadata = useCallback((key: string) => {
    setSettings(prev => {
      const { [key]: removed, ...rest } = prev.customMetadata;
      return { ...prev, customMetadata: rest };
    });
  }, []);

  const resetSettings = useCallback(() => {
    setSettings({
      selectedProducts: [],
      webhookEvents: ['customer.created', 'invoice.payment_succeeded', 'customer.subscription.updated'],
      customMetadata: {},
      defaultCurrency: 'usd',
      testMode: true
    });
  }, []);

  return {
    settings,
    updateSettings,
    updateSelectedProducts,
    updateWebhookEvents,
    updateMetadata,
    removeMetadata,
    resetSettings
  };
}

/**
 * Hook for Stripe utilities and helper functions
 */
export function useStripeUtils() {
  const formatCurrency = useCallback((amount: number, currency = 'usd') => {
    return stripeService.formatCurrency(amount, currency);
  }, []);

  const isValidApiKey = useCallback((apiKey: string) => {
    const testKeyPattern = /^sk_test_[a-zA-Z0-9]{24,}$/;
    const liveKeyPattern = /^sk_live_[a-zA-Z0-9]{24,}$/;
    const publishableTestPattern = /^pk_test_[a-zA-Z0-9]{24,}$/;
    const publishableLivePattern = /^pk_live_[a-zA-Z0-9]{24,}$/;

    return testKeyPattern.test(apiKey) || 
           liveKeyPattern.test(apiKey) || 
           publishableTestPattern.test(apiKey) || 
           publishableLivePattern.test(apiKey);
  }, []);

  const isTestMode = useCallback((apiKey: string) => {
    return apiKey.includes('_test_');
  }, []);

  const generateWebhookEndpoint = useCallback((baseUrl: string) => {
    return `${baseUrl}/api/webhooks/stripe`;
  }, []);

  return {
    formatCurrency,
    isValidApiKey,
    isTestMode,
    generateWebhookEndpoint
  };
} 