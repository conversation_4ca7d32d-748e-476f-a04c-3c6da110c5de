import {
  StripeProduct,
  StripeAccount,
  StripeConnectionState,
  StripeServiceConfig,
  StripeListOptions,
  StripeCustomer,
  StripePrice
} from '../types/stripe';

/**
 * Stripe Service Layer
 * 
 * This service provides an abstraction layer for Stripe API interactions.
 * Currently returns mock data but is structured to easily integrate with real Stripe APIs.
 * 
 * To integrate with real Stripe:
 * 1. Install stripe npm package: npm install stripe
 * 2. Replace mock implementations with actual Stripe API calls
 * 3. Update environment configuration with real API keys
 */
class StripeService {
  private config: StripeServiceConfig | null = null;
  private connectionState: StripeConnectionState = {
    isConnected: false,
    isVerifying: false,
    errors: []
  };

  // Mock data for development/demo purposes
  private mockProducts: StripeProduct[] = [
    {
      id: 'prod_mock_1',
      object: 'product',
      active: true,
      created: Date.now() / 1000,
      description: 'Basic subscription plan for small businesses',
      features: [{ name: 'Up to 10 users' }, { name: 'Basic support' }],
      images: [],
      livemode: false,
      metadata: { tier: 'basic' },
      name: 'Basic Plan',
      type: 'service',
      updated: Date.now() / 1000,
      selected: false
    },
    {
      id: 'prod_mock_2',
      object: 'product',
      active: true,
      created: Date.now() / 1000,
      description: 'Professional subscription plan with advanced features',
      features: [{ name: 'Up to 50 users' }, { name: 'Priority support' }],
      images: [],
      livemode: false,
      metadata: { tier: 'professional' },
      name: 'Professional Plan',
      type: 'service',
      updated: Date.now() / 1000,
      selected: false
    },
    {
      id: 'prod_mock_3',
      object: 'product',
      active: true,
      created: Date.now() / 1000,
      description: 'Enterprise subscription plan for large organizations',
      features: [{ name: 'Unlimited users' }, { name: '24/7 support' }],
      images: [],
      livemode: false,
      metadata: { tier: 'enterprise' },
      name: 'Enterprise Plan',
      type: 'service',
      updated: Date.now() / 1000,
      selected: false
    },
    {
      id: 'prod_mock_4',
      object: 'product',
      active: true,
      created: Date.now() / 1000,
      description: 'Custom subscription plan tailored to your needs',
      features: [{ name: 'Custom features' }, { name: 'Dedicated support' }],
      images: [],
      livemode: false,
      metadata: { tier: 'custom' },
      name: 'Custom Plan',
      type: 'service',
      updated: Date.now() / 1000,
      selected: false
    }
  ];

  private mockAccount: StripeAccount = {
    id: 'acct_mock_123',
    object: 'account',
    business_type: 'company',
    charges_enabled: true,
    country: 'US',
    created: Date.now() / 1000,
    default_currency: 'usd',
    details_submitted: true,
    email: '<EMAIL>',
    metadata: {},
    payouts_enabled: true,
    type: 'standard'
  };

  /**
   * Initialize the Stripe service with configuration
   */
  initialize(config: StripeServiceConfig): void {
    this.config = config;
    this.connectionState = {
      isConnected: false,
      isVerifying: false,
      errors: []
    };
  }

  /**
   * Verify API key and establish connection
   * In production, this would make actual API calls to verify the key
   */
  async verifyConnection(apiKey: string): Promise<StripeConnectionState> {
    this.connectionState.isVerifying = true;
    this.connectionState.errors = [];

    try {
      // Simulate API verification delay
      await this.delay(2000);

      // Mock validation logic
      const isValidFormat = this.validateApiKeyFormat(apiKey);
      if (!isValidFormat) {
        throw new Error('Invalid API key format');
      }

      // Simulate occasional connection failures for demo
      if (Math.random() < 0.1) {
        throw new Error('Unable to connect to Stripe. Please check your API key.');
      }

      // Successful connection
      this.connectionState = {
        isConnected: true,
        isVerifying: false,
        lastVerified: new Date(),
        account: this.mockAccount,
        errors: []
      };

      this.config = {
        ...this.config!,
        apiKey
      };

    } catch (error) {
      this.connectionState = {
        isConnected: false,
        isVerifying: false,
        errors: [{
          code: 'connection_failed',
          message: error instanceof Error ? error.message : 'Unknown error',
          type: 'authentication_error'
        }]
      };
    }

    return this.connectionState;
  }

  /**
   * Fetch products from Stripe
   * In production, this would call the Stripe Products API
   */
  async getProducts(options: StripeListOptions = {}): Promise<StripeProduct[]> {
    if (!this.connectionState.isConnected) {
      throw new Error('Not connected to Stripe. Please verify your API key first.');
    }

    // Simulate API delay
    await this.delay(1000);

    // Apply limit if specified
    const limit = options.limit || this.mockProducts.length;
    return this.mockProducts.slice(0, limit);
  }

  /**
   * Fetch a specific product by ID
   */
  async getProduct(productId: string): Promise<StripeProduct | null> {
    if (!this.connectionState.isConnected) {
      throw new Error('Not connected to Stripe. Please verify your API key first.');
    }

    await this.delay(500);
    return this.mockProducts.find(p => p.id === productId) || null;
  }

  /**
   * Fetch prices for a product
   * In production, this would call the Stripe Prices API
   */
  async getPricesForProduct(productId: string): Promise<StripePrice[]> {
    if (!this.connectionState.isConnected) {
      throw new Error('Not connected to Stripe. Please verify your API key first.');
    }

    await this.delay(500);

    // Mock prices for the product
    return [
      {
        id: `price_${productId}_monthly`,
        object: 'price',
        active: true,
        billing_scheme: 'per_unit',
        created: Date.now() / 1000,
        currency: 'usd',
        livemode: false,
        metadata: {},
        product: productId,
        recurring: {
          interval: 'month',
          interval_count: 1,
          usage_type: 'licensed'
        },
        type: 'recurring',
        unit_amount: 2999 // $29.99
      }
    ];
  }

  /**
   * Create a new product
   * In production, this would call the Stripe Products API
   */
  async createProduct(productData: Partial<StripeProduct>): Promise<StripeProduct> {
    if (!this.connectionState.isConnected) {
      throw new Error('Not connected to Stripe. Please verify your API key first.');
    }

    await this.delay(1500);

    const newProduct: StripeProduct = {
      id: `prod_mock_${Date.now()}`,
      object: 'product',
      active: true,
      created: Date.now() / 1000,
      description: productData.description || null,
      features: productData.features || [],
      images: productData.images || [],
      livemode: false,
      metadata: productData.metadata || {},
      name: productData.name || 'New Product',
      type: productData.type || 'service',
      updated: Date.now() / 1000,
      selected: false
    };

    // Add to mock products
    this.mockProducts.push(newProduct);

    return newProduct;
  }

  /**
   * Update an existing product
   */
  async updateProduct(productId: string, updates: Partial<StripeProduct>): Promise<StripeProduct> {
    if (!this.connectionState.isConnected) {
      throw new Error('Not connected to Stripe. Please verify your API key first.');
    }

    await this.delay(1000);

    const productIndex = this.mockProducts.findIndex(p => p.id === productId);
    if (productIndex === -1) {
      throw new Error('Product not found');
    }

    this.mockProducts[productIndex] = {
      ...this.mockProducts[productIndex],
      ...updates,
      updated: Date.now() / 1000
    };

    return this.mockProducts[productIndex];
  }

  /**
   * Get account information
   */
  async getAccount(): Promise<StripeAccount | null> {
    if (!this.connectionState.isConnected) {
      throw new Error('Not connected to Stripe. Please verify your API key first.');
    }

    await this.delay(500);
    return this.mockAccount;
  }

  /**
   * Create a customer
   * In production, this would call the Stripe Customers API
   */
  async createCustomer(customerData: Partial<StripeCustomer>): Promise<StripeCustomer> {
    if (!this.connectionState.isConnected) {
      throw new Error('Not connected to Stripe. Please verify your API key first.');
    }

    await this.delay(1000);

    return {
      id: `cus_mock_${Date.now()}`,
      object: 'customer',
      balance: 0,
      created: Date.now() / 1000,
      currency: 'usd',
      delinquent: false,
      description: customerData.description || null,
      email: customerData.email || null,
      livemode: false,
      metadata: customerData.metadata || {},
      name: customerData.name || null,
      phone: customerData.phone || null
    };
  }

  /**
   * Refresh/sync products from Stripe
   * Simulates fetching updated product list
   */
  async refreshProducts(): Promise<StripeProduct[]> {
    await this.delay(1500);

    // Simulate adding a new product during refresh
    const newProduct: StripeProduct = {
      id: `prod_mock_${Date.now()}`,
      object: 'product',
      active: true,
      created: Date.now() / 1000,
      description: 'Newly synced product from Stripe',
      features: [{ name: 'Auto-synced' }],
      images: [],
      livemode: false,
      metadata: { synced: 'true' },
      name: `Synced Product ${this.mockProducts.length + 1}`,
      type: 'service',
      updated: Date.now() / 1000,
      selected: false
    };

    this.mockProducts.push(newProduct);
    return [...this.mockProducts];
  }

  /**
   * Get current connection state
   */
  getConnectionState(): StripeConnectionState {
    return { ...this.connectionState };
  }

  /**
   * Disconnect from Stripe
   */
  disconnect(): void {
    this.config = null;
    this.connectionState = {
      isConnected: false,
      isVerifying: false,
      errors: []
    };
  }

  /**
   * Get configuration (without sensitive data)
   */
  getConfig(): Omit<StripeServiceConfig, 'apiKey'> | null {
    if (!this.config) return null;
    
    const { apiKey, ...safeConfig } = this.config;
    return safeConfig;
  }

  // Private helper methods

  private validateApiKeyFormat(apiKey: string): boolean {
    // Validate Stripe API key format
    const testKeyPattern = /^sk_test_[a-zA-Z0-9]{24,}$/;
    const liveKeyPattern = /^sk_live_[a-zA-Z0-9]{24,}$/;
    const publishableTestPattern = /^pk_test_[a-zA-Z0-9]{24,}$/;
    const publishableLivePattern = /^pk_live_[a-zA-Z0-9]{24,}$/;

    return testKeyPattern.test(apiKey) || 
           liveKeyPattern.test(apiKey) || 
           publishableTestPattern.test(apiKey) || 
           publishableLivePattern.test(apiKey);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Utility method to determine if API key is for test mode
   */
  isTestMode(apiKey?: string): boolean {
    const key = apiKey || this.config?.apiKey;
    if (!key) return true;
    return key.includes('_test_');
  }

  /**
   * Format currency amount for display
   */
  formatCurrency(amount: number, currency = 'usd'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount / 100); // Stripe amounts are in cents
  }
}

// Export singleton instance
export const stripeService = new StripeService();

// Named exports for dependency injection or testing
export { StripeService };
export default stripeService; 