export interface Template {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  tags: string[];
  features: string[];
}

export interface Feature {
  id: string;
  name: string;
  description: string;
  isEnabled: boolean;
}

export interface TechStack {
  frontend: string[];
  backend: string[];
  database: string[];
}

export interface Colors {
  primary: string;
  secondary: string;
  accent: string;
}

export interface ProjectBlueprint {
  id: string;
  name: string;
  description: string;
  features: Feature[];
  techStack: TechStack;
  colors: Colors;
  theme: 'light' | 'dark' | 'auto';
  estimatedTime: string;
  complexity: string;
  layoutDescription: string;
  layouts?: any[]; // Optional layouts
  projectInfo?: any; // Additional project info for implementation
}

export interface ProjectCreationState {
  currentStep: 'template' | 'blueprint';
  selectedTemplate: Template | null;
  projectBlueprint: ProjectBlueprint | null;
  customRequirements: string;
}

export interface FrameworkOption {
  key: string;
  label: string;
  icon: any;
  type: string | string[];
  isDefault?: boolean;
} 