# Stripe Integration - Ready for Real API Connection

This document explains the Stripe integration architecture and how to connect it to real Stripe APIs.

## Overview

The Stripe integration has been restructured to be **integration-ready** while maintaining the existing UI components exactly as they were. The new architecture provides:

- **Clean separation** between UI and business logic
- **Type-safe** interfaces matching real Stripe API objects
- **Service layer** ready for real API integration
- **Comprehensive error handling**
- **Environment configuration**
- **React hooks** for state management

## Architecture

```
src/components/build/ProjectCreationFlow/ProjectBlueprint/
├── types/
│   └── stripe.ts              # TypeScript interfaces matching Stripe API
├── services/
│   └── stripeService.ts       # Service layer for API interactions
├── hooks/
│   └── useStripe.ts           # React hooks for state management
├── config/
│   └── stripe.config.ts       # Environment configuration
├── utils/
│   └── errorHandling.ts       # Error handling utilities
└── StripeConnectionModal.tsx  # UI component (unchanged interface)
```

## Key Features

### 1. **Type-Safe Interfaces** (`types/stripe.ts`)

All TypeScript interfaces match the real Stripe API objects:

```typescript
interface StripeProduct {
  id: string;
  object: 'product';
  active: boolean;
  created: number;
  name: string;
  // ... all other Stripe product properties
}
```

### 2. **Service Layer** (`services/stripeService.ts`)

Provides an abstraction layer for Stripe operations:

```typescript
class StripeService {
  async verifyConnection(apiKey: string): Promise<StripeConnectionState>
  async getProducts(options?: StripeListOptions): Promise<StripeProduct[]>
  async createProduct(productData: Partial<StripeProduct>): Promise<StripeProduct>
  // ... other methods
}
```

### 3. **React Hooks** (`hooks/useStripe.ts`)

Custom hooks for managing Stripe state:

```typescript
// Connection management
const { connect, disconnect, isConnected } = useStripeConnection();

// Product management
const { products, fetchProducts, toggleProductSelection } = useStripeProducts();

// Complete modal state
const stripeModal = useStripeModal();
```

### 4. **Configuration** (`config/stripe.config.ts`)

Centralized configuration with environment variables:

```typescript
export const STRIPE_CONFIG = {
  API_KEYS: { TEST: '...', LIVE: '...' },
  WEBHOOK: { ENDPOINT: '...', SECRET: '...' },
  FEATURES: { SUBSCRIPTIONS: true, ... }
};
```

### 5. **Error Handling** (`utils/errorHandling.ts`)

Comprehensive error handling with user-friendly messages:

```typescript
export function getErrorMessage(error: StripeConnectionError): string
export function getSuggestedActions(error: StripeConnectionError): string[]
export function isRecoverableError(error: StripeConnectionError): boolean
```

## How to Connect to Real Stripe APIs

### Step 1: Install Stripe SDK

```bash
npm install stripe
npm install @stripe/stripe-js  # For client-side usage
```

### Step 2: Environment Configuration

Create `.env.local` file:

```bash
# Stripe API Keys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_TEST=pk_test_...
STRIPE_SECRET_KEY_TEST=sk_test_...

# Webhook Configuration
STRIPE_WEBHOOK_ENDPOINT=/api/webhooks/stripe
STRIPE_WEBHOOK_SECRET=whsec_...

# Feature Flags
STRIPE_SUBSCRIPTIONS_ENABLED=true
STRIPE_ONE_TIME_PAYMENTS_ENABLED=true
```

### Step 3: Update Service Layer

Replace mock implementations in `stripeService.ts`:

```typescript
// Before (mock)
async getProducts(): Promise<StripeProduct[]> {
  await this.delay(1000);
  return this.mockProducts;
}

// After (real API)
async getProducts(): Promise<StripeProduct[]> {
  const stripe = new Stripe(this.config.apiKey);
  const response = await stripe.products.list({ active: true });
  return response.data;
}
```

### Step 4: Implement Real API Methods

#### Connection Verification

```typescript
async verifyConnection(apiKey: string): Promise<StripeConnectionState> {
  try {
    const stripe = new Stripe(apiKey);
    const account = await stripe.accounts.retrieve();
    
    return {
      isConnected: true,
      isVerifying: false,
      account,
      errors: []
    };
  } catch (error) {
    return {
      isConnected: false,
      isVerifying: false,
      errors: [parseError(error)]
    };
  }
}
```

#### Product Fetching

```typescript
async getProducts(options: StripeListOptions = {}): Promise<StripeProduct[]> {
  const stripe = new Stripe(this.config.apiKey);
  const response = await stripe.products.list({
    active: true,
    limit: options.limit || 100,
    starting_after: options.starting_after,
    ending_before: options.ending_before
  });
  
  return response.data;
}
```

#### Product Creation

```typescript
async createProduct(productData: Partial<StripeProduct>): Promise<StripeProduct> {
  const stripe = new Stripe(this.config.apiKey);
  
  return await stripe.products.create({
    name: productData.name!,
    description: productData.description,
    metadata: productData.metadata,
    type: productData.type || 'service'
  });
}
```

### Step 5: Set Up Webhook Handling

Create `/api/webhooks/stripe.ts`:

```typescript
import Stripe from 'stripe';
import { NextApiRequest, NextApiResponse } from 'next';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).end();
  }

  const signature = req.headers['stripe-signature'] as string;
  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(req.body, signature, webhookSecret);
  } catch (error) {
    return res.status(400).json({ error: 'Invalid signature' });
  }

  // Handle the event
  switch (event.type) {
    case 'customer.created':
      // Handle customer creation
      break;
    case 'invoice.payment_succeeded':
      // Handle successful payment
      break;
    // ... handle other events
  }

  res.status(200).json({ received: true });
}
```

### Step 6: Update Configuration

Ensure all environment variables are properly set and configuration is loaded:

```typescript
// Validate configuration on startup
const validation = validateStripeConfig();
if (!validation.isValid) {
  console.error('Stripe configuration errors:', validation.errors);
}
```

## Migration Checklist

- [ ] Install Stripe SDK packages
- [ ] Set up environment variables
- [ ] Replace mock implementations with real API calls
- [ ] Implement webhook handling
- [ ] Test connection with real Stripe account
- [ ] Test product fetching and creation
- [ ] Set up error monitoring
- [ ] Configure webhook endpoints in Stripe Dashboard
- [ ] Test end-to-end integration

## Testing

### Unit Tests

Test the service layer with mocked Stripe SDK:

```typescript
import { StripeService } from './stripeService';

jest.mock('stripe');

describe('StripeService', () => {
  it('should verify connection successfully', async () => {
    // Test implementation
  });
});
```

### Integration Tests

Test with Stripe's test mode:

```typescript
const testApiKey = 'sk_test_...';
const service = new StripeService();
service.initialize({ apiKey: testApiKey });

const connectionState = await service.verifyConnection(testApiKey);
expect(connectionState.isConnected).toBe(true);
```

## Current State

✅ **Complete and Ready:**
- TypeScript interfaces matching Stripe API
- Service layer architecture
- React hooks for state management
- Error handling utilities
- Configuration management
- UI components (unchanged)

🔄 **Next Steps for Real Integration:**
- Install Stripe SDK
- Replace mock implementations
- Set up webhook handling
- Configure environment variables

## Notes

- The UI components remain **exactly the same** - no changes needed
- All existing functionality is preserved
- The integration is backward compatible
- Error handling is enhanced with user-friendly messages
- The architecture supports easy testing and debugging

## Support

For questions about this integration:
1. Check the Stripe API documentation
2. Review the TypeScript interfaces in `types/stripe.ts`
3. Examine the service layer in `services/stripeService.ts`
4. Test with the provided hooks in `hooks/useStripe.ts` 