// components/dashboard/DeploymentsList.jsx
import React, { useState, useMemo } from 'react';
import { formatDate, getDeploymentStatusColor, getDeploymentStatusIcon, truncateId } from '../../lib/utils';
import Button from '../ui/Button';

const DeploymentsList = ({ 
  deployments, 
  projectId, 
  projectStats,
  onDeploymentSelect, 
  onBackToProjects, 
  loading, 
  error,
  onRetry 
}) => {
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Safe deployments array handling
  const deploymentsList = Array.isArray(deployments) ? deployments : [];

  // Filter deployments based on search term and status
  const filteredDeployments = useMemo(() => {
    let filtered = deploymentsList;

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(deployment => 
        deployment?.status === statusFilter
      );
    }

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(deployment => {
        const deploymentId = deployment?.deployment_id || '';
        const taskId = deployment?.task_id || '';
        const branchName = deployment?.branch_name || '';
        const message = deployment?.message || '';
        const appUrl = deployment?.app_url || '';
        
        return deploymentId.toLowerCase().includes(searchLower) ||
               taskId.toLowerCase().includes(searchLower) ||
               branchName.toLowerCase().includes(searchLower) ||
               message.toLowerCase().includes(searchLower) ||
               appUrl.toLowerCase().includes(searchLower);
      });
    }

    return filtered;
  }, [deploymentsList, searchTerm, statusFilter]);

  // Sort deployments
  const sortedDeployments = useMemo(() => {
    const sorted = [...filteredDeployments].sort((a, b) => {
      let aValue, bValue;

      switch (sortField) {
        case 'deployment_id':
          aValue = a.deployment_id || '';
          bValue = b.deployment_id || '';
          break;
        case 'status':
          aValue = a.status || '';
          bValue = b.status || '';
          break;
        case 'branch_name':
          aValue = a.branch_name || '';
          bValue = b.branch_name || '';
          break;
        case 'created_at':
          aValue = new Date(a.created_at || 0);
          bValue = new Date(b.created_at || 0);
          break;
        case 'updated_at':
          aValue = new Date(a.updated_at || 0);
          bValue = new Date(b.updated_at || 0);
          break;
        default:
          aValue = a[sortField] || '';
          bValue = b[sortField] || '';
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return sorted;
  }, [filteredDeployments, sortField, sortDirection]);

  // Paginate deployments
  const paginatedDeployments = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedDeployments.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedDeployments, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedDeployments.length / itemsPerPage);

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection(field === 'created_at' || field === 'updated_at' ? 'desc' : 'asc');
    }
    setCurrentPage(1);
  };

  // Reset pagination when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Loading state
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
          <div className="h-8 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        {/* Project stats skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {Array.from({ length: 5 }, (_, i) => (
            <div key={i} className="bg-white p-4 rounded-lg border">
              <div className="h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
              <div className="h-6 bg-gray-100 rounded w-8 animate-pulse"></div>
            </div>
          ))}
        </div>
        <div className="border rounded-lg overflow-hidden">
          <div className="h-12 bg-gray-100 animate-pulse"></div>
          {Array.from({ length: 5 }, (_, i) => (
            <div key={i} className="h-16 border-t border-gray-200 animate-pulse">
              <div className="p-4 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-100 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error && deploymentsList.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Deployments in {truncateId(projectId, 12)}
          </h3>
          <Button variant="ghost" onClick={onBackToProjects}>
            ← Back to Projects
          </Button>
        </div>
        <div className="text-center py-12">
          <div className="text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Deployments</h3>
          <p className="text-sm text-gray-500 mb-4">
            {error.message || 'There was an error loading the deployments data.'}
          </p>
          {onRetry && (
            <Button onClick={onRetry} variant="outline">
              Retry
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Back Button */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Deployments in {truncateId(projectId, 12)} ({sortedDeployments.length})
        </h3>
        <Button variant="ghost" onClick={onBackToProjects}>
          ← Back to Projects
        </Button>
      </div>

      {/* Project Stats */}
      {projectStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border text-center">
            <div className="text-sm text-gray-500">Total</div>
            <div className="text-2xl font-bold text-gray-900">{projectStats.total_deployments || 0}</div>
          </div>
          <div className="bg-white p-4 rounded-lg border text-center">
            <div className="text-sm text-gray-500">Completed</div>
            <div className="text-2xl font-bold text-green-600">{projectStats.completed_deployments || 0}</div>
          </div>
          <div className="bg-white p-4 rounded-lg border text-center">
            <div className="text-sm text-gray-500">Failed</div>
            <div className="text-2xl font-bold text-red-600">{projectStats.failed_deployments || 0}</div>
          </div>
          {/* <div className="bg-white p-4 rounded-lg border text-center">
            <div className="text-sm text-gray-500">Branches</div>
            <div className="text-2xl font-bold text-purple-600">{projectStats.unique_branches || 0}</div>
          </div> */}
        </div>
      )}

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search deployments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400">🔍</span>
            </div>
          </div>
{/* 
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Statuses</option>
            <option value="active">Active</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="cancelled">Cancelled</option>
          </select> */}
        </div>
        
        <select
          value={itemsPerPage}
          onChange={(e) => {
            setItemsPerPage(Number(e.target.value));
            setCurrentPage(1);
          }}
          className="border border-gray-300 rounded-md px-3 py-2 text-sm"
        >
          <option value={10}>10 per page</option>
          <option value={25}>25 per page</option>
          <option value={50}>50 per page</option>
        </select>
      </div>

      {/* Empty state */}
      {deploymentsList.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🚀</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Deployments Found</h3>
          <p className="text-sm text-gray-500">
            No deployments found in this project for the selected filters.
          </p>
        </div>
      ) : filteredDeployments.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🔍</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Matching Deployments</h3>
          <p className="text-sm text-gray-500">
            No deployments match your current search and filter criteria.
          </p>
          <Button 
            onClick={() => {
              setSearchTerm('');
              setStatusFilter('all');
            }}
            variant="outline"
            className="mt-4"
          >
            Clear Filters
          </Button>
        </div>
      ) : (
        <>
          {/* Table */}
          <div className="bg-white border rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('deployment_id')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Deployment</span>
                        {sortField === 'deployment_id' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </div>
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('status')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Status</span>
                        {sortField === 'status' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </div>
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('branch_name')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Branch</span>
                        {sortField === 'branch_name' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      URL
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Message
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('created_at')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Created</span>
                        {sortField === 'created_at' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </div>
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('updated_at')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Updated</span>
                        {sortField === 'updated_at' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedDeployments.map((deployment, index) => (
                    <tr key={deployment.deployment_id || index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {truncateId(deployment.deployment_id, 12) || 'Unknown'}
                          </div>
                          {deployment.task_id && (
                            <div className="text-xs text-gray-500">
                              Task: {truncateId(deployment.task_id, 8)}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">
                            {getDeploymentStatusIcon(deployment.status)}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${getDeploymentStatusColor(deployment.status)}`}>
                            {deployment.status || 'unknown'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {deployment.branch_name ? (
                          <span className="text-sm text-gray-900 bg-gray-100 px-2 py-1 rounded font-mono">
                            {deployment.branch_name}
                          </span>
                        ) : (
                          <span className="text-sm text-gray-400">-</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {deployment.app_url ? (
                          <div className="flex items-center space-x-2">
                            <a 
                              href={deployment.app_url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-sm text-blue-600 hover:text-blue-800 max-w-32 truncate"
                              title={deployment.app_url}
                            >
                              {deployment.app_url.replace(/^https?:\/\//, '')}
                            </a>
                            <span className="text-blue-600">🔗</span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">-</span>
                        )}
                        {deployment.custom_domain && (
                          <div className="text-xs text-gray-500 mt-1">
                            Custom: {deployment.custom_domain}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 max-w-xs">
                        <div className="text-sm text-gray-900 truncate" title={deployment.message}>
                          {deployment.message || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {deployment.created_at ? formatDate(deployment.created_at) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {deployment.updated_at ? formatDate(deployment.updated_at) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Button
                          onClick={() => onDeploymentSelect(deployment)}
                          variant="ghost"
                          size="sm"
                        >
                          View Details →
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-4 py-3 bg-white border border-gray-200 rounded-md">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                >
                  Previous
                </Button>
                <Button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  variant="outline"
                  size="sm"
                >
                  Next
                </Button>
              </div>
              
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * itemsPerPage, sortedDeployments.length)}
                    </span>{' '}
                    of <span className="font-medium">{sortedDeployments.length}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <Button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      variant="outline"
                      size="sm"
                      className="rounded-l-md"
                    >
                      Previous
                    </Button>
                    
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNumber;
                      if (totalPages <= 5) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNumber = totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }
                      
                      return (
                        <Button
                          key={pageNumber}
                          onClick={() => setCurrentPage(pageNumber)}
                          variant={currentPage === pageNumber ? "default" : "outline"}
                          size="sm"
                          className="border-l-0"
                        >
                          {pageNumber}
                        </Button>
                      );
                    })}
                    
                    <Button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      variant="outline"
                      size="sm"
                      className="rounded-r-md border-l-0"
                    >
                      Next
                    </Button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default DeploymentsList;