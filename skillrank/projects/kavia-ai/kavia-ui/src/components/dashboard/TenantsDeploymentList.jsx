// components/dashboard/TenantsDeploymentList.jsx
import React, { useState, useMemo } from 'react';
import { formatDate, getDeploymentStatusColor, truncateId } from '../../lib/utils';
import Button from '../ui/Button';

const TenantsDeploymentList = ({ 
  tenants, 
  onTenantSelect, 
  loading, 
  error,
  onRetry 
}) => {
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState('total_deployments');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');

  // Safe tenants array handling
  const tenantsList = Array.isArray(tenants) ? tenants : [];

  // Filter tenants based on search term
  const filteredTenants = useMemo(() => {
    if (!searchTerm.trim()) {
      return tenantsList;
    }

    const searchLower = searchTerm.toLowerCase().trim();
    return tenantsList.filter(tenant => {
      const tenantName = tenant?.tenant_name || '';
      const tenantId = tenant?.tenant_id || '';
      const projectId = tenant?.project_id || '';
      
      return tenantName.toLowerCase().includes(searchLower) ||
             tenantId.toLowerCase().includes(searchLower) ||
             projectId.toLowerCase().includes(searchLower);
    });
  }, [tenantsList, searchTerm]);

  // Sort tenants
  const sortedTenants = useMemo(() => {
    const sorted = [...filteredTenants].sort((a, b) => {
      let aValue, bValue;

      switch (sortField) {
        case 'tenant_name':
          aValue = a.tenant_name || '';
          bValue = b.tenant_name || '';
          break;
        case 'total_deployments':
          aValue = a.total_deployments || 0;
          bValue = b.total_deployments || 0;
          break;

        case 'completed_deployments':
          aValue = a.completed_deployments || 0;
          bValue = b.completed_deployments || 0;
          break;
        case 'failed_deployments':
          aValue = a.failed_deployments || 0;
          bValue = b.failed_deployments || 0;
          break;
        case 'last_deployment':
          aValue = new Date(a.last_deployment || 0);
          bValue = new Date(b.last_deployment || 0);
          break;
        default:
          aValue = a[sortField] || '';
          bValue = b[sortField] || '';
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return sorted;
  }, [filteredTenants, sortField, sortDirection]);

  // Paginate tenants
  const paginatedTenants = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedTenants.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedTenants, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedTenants.length / itemsPerPage);

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
    setCurrentPage(1);
  };

  // Reset pagination when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Loading state
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
        <div className="border rounded-lg overflow-hidden">
          <div className="h-12 bg-gray-100 animate-pulse"></div>
          {Array.from({ length: 5 }, (_, i) => (
            <div key={i} className="h-16 border-t border-gray-200 animate-pulse">
              <div className="p-4 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-100 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error && tenantsList.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Tenants</h3>
        <p className="text-sm text-gray-500 mb-4">
          {error.message || 'There was an error loading the tenants data.'}
        </p>
        {onRetry && (
          <Button onClick={onRetry} variant="outline">
            Retry
          </Button>
        )}
      </div>
    );
  }

  // Empty state
  if (tenantsList.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-4xl mb-4">🏢</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Tenants Found</h3>
        <p className="text-sm text-gray-500">
          No tenants with deployments found for the selected filters.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header and Search */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Tenants ({sortedTenants.length})
        </h3>
        
        <div className="flex items-center space-x-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search tenants..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400">🔍</span>
            </div>
          </div>
          
          <select
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value={10}>10 per page</option>
            <option value={25}>25 per page</option>
            <option value={50}>50 per page</option>
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('tenant_name')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Tenant</span>
                    {sortField === 'tenant_name' && (
                      <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('total_deployments')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Total</span>
                    {sortField === 'total_deployments' && (
                      <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </th>

                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('completed_deployments')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Completed</span>
                    {sortField === 'completed_deployments' && (
                      <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('failed_deployments')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Failed</span>
                    {sortField === 'failed_deployments' && (
                      <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('last_deployment')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Last Deployment</span>
                    {sortField === 'last_deployment' && (
                      <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Recent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedTenants.map((tenant) => (
                <tr key={tenant.tenant_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {tenant.tenant_name || 'Unknown Tenant'}
                      </div>
                      <div className="text-sm text-gray-500">
                        ID: {truncateId(tenant.tenant_id)}
                      </div>
                      {tenant.project_id && (
                        <div className="text-xs text-gray-400">
                          Project: {truncateId(tenant.project_id)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {tenant.total_deployments || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                    {tenant.completed_deployments || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                    {tenant.failed_deployments || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {tenant.last_deployment ? 
                      formatDate(tenant.last_deployment) : 
                      'Never'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {tenant.recent_deployments && tenant.recent_deployments.length > 0 ? (
                      <div className="space-y-1">
                        {tenant.recent_deployments.slice(0, 2).map((deployment, idx) => (
                          <div key={idx} className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs rounded-full ${getDeploymentStatusColor(deployment.status)}`}>
                              {deployment.status}
                            </span>
                            {deployment.app_url && (
                              <a 
                                href={deployment.app_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:text-blue-800"
                              >
                                🔗
                              </a>
                            )}
                          </div>
                        ))}
                        {tenant.recent_deployments.length > 2 && (
                          <div className="text-xs text-gray-400">
                            +{tenant.recent_deployments.length - 2} more
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">No recent deployments</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Button
                      onClick={() => onTenantSelect(tenant)}
                      variant="ghost"
                      size="sm"
                    >
                      View Projects →
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-3 bg-white border border-gray-200 rounded-md">
          <div className="flex-1 flex justify-between sm:hidden">
            <Button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              Previous
            </Button>
            <Button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
            </Button>
          </div>
          
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * itemsPerPage, sortedTenants.length)}
                </span>{' '}
                of <span className="font-medium">{sortedTenants.length}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <Button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                  className="rounded-l-md"
                >
                  Previous
                </Button>
                
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else if (currentPage <= 3) {
                    pageNumber = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + i;
                  } else {
                    pageNumber = currentPage - 2 + i;
                  }
                  
                  return (
                    <Button
                      key={pageNumber}
                      onClick={() => setCurrentPage(pageNumber)}
                      variant={currentPage === pageNumber ? "default" : "outline"}
                      size="sm"
                      className="border-l-0"
                    >
                      {pageNumber}
                    </Button>
                  );
                })}
                
                <Button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  variant="outline"
                  size="sm"
                  className="rounded-r-md border-l-0"
                >
                  Next
                </Button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TenantsDeploymentList;