import React, { useState, useEffect, useCallback, useRef, forwardRef, useImperativeHandle } from "react";
import { 
  Loader2, 
  RefreshCw,
  AlertTriangle,
  AlertCircle,
  Square,
  Monitor,
  Tablet,
  Smartphone,
  Settings,
  RotateCcw,
  Maximize2,
  Minimize2
} from "lucide-react";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import useAppetize from "@/components/CodeGenrationPanel/PreviewPanel/Appetize/useAppetize";
import AppetizeLogs from "@/components/CodeGenrationPanel/PreviewPanel/Appetize/AppetizeLogs";
import { ContainerStatusTypes } from "@/components/CodeGenrationPanel/PreviewPanel/ContainerTypes";

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url) => {
  if (!url || typeof url !== 'string') return url;
  return url.replace(':3000', ':4000');
};

// Device presets for responsive testing
const DEVICE_PRESETS = {
  desktop: {
    name: 'Desktop',
    width: '100%',
    height: '100%',
    icon: Monitor,
    category: 'desktop'
  },
  laptop: {
    name: 'Laptop',
    width: 1366,
    height: 768,
    icon: Monitor,
    category: 'desktop'
  },
  tablet: {
    name: 'Tablet',
    width: 768,
    height: 1024,
    icon: Tablet,
    category: 'tablet'
  },
  tabletLandscape: {
    name: 'Tablet Landscape',
    width: 1024,
    height: 768,
    icon: Tablet,
    category: 'tablet'
  },
  mobile: {
    name: 'Mobile',
    width: 375,
    height: 667,
    icon: Smartphone,
    category: 'mobile'
  },
  mobileLarge: {
    name: 'Mobile Large',
    width: 414,
    height: 896,
    icon: Smartphone,
    category: 'mobile'
  },
  // custom: {
  //   name: 'Custom',
  //   width: 1200,
  //   height: 800,
  //   icon: Settings,
  //   category: 'custom'
  // }
};

const APPETIZE_INIT_DELAY = 6000;

const PreviewPanel = ({ currentTaskId }) => {
  const { 
    wsConnection, 
    containers, 
    selectedContainer,
    setSelectedContainer 
  } = useCodeGeneration();
  
  const [iframeKey, setIframeKey] = useState(Date.now());
  const [iframeError, setIframeError] = useState(false);
  const [errorType, setErrorType] = useState(null);
  const [isCheckingUrl, setIsCheckingUrl] = useState(false);
  const [urlStatus, setUrlStatus] = useState('checking');
  const iframeRef = useRef(null);
  const containerRef = useRef(null);

  // Responsive testing states
  const [selectedDevice, setSelectedDevice] = useState('desktop');
  const [customWidth, setCustomWidth] = useState(1200);
  const [customHeight, setCustomHeight] = useState(800);
  const [isRotated, setIsRotated] = useState(false);
  const [showResponsiveControls, setShowResponsiveControls] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Appetize integration
  const {
    appetizeState,
    initializeAppetizeClient,
    updateUrl,
    clearLogs,
    forceSendLogs
  } = useAppetize(wsConnection, currentTaskId);

  // Auto-select first container if none is selected
  useEffect(() => {
    if (!selectedContainer && containers.length > 0) {
      setSelectedContainer(containers[0].name);
    }
  }, [selectedContainer, containers, setSelectedContainer]);

  // Find the selected container object
  const getSelectedContainerObj = useCallback(() => {
    return containers.find(c => c.name === selectedContainer);
  }, [containers, selectedContainer]);

  // Check if URL is from Appetize (local helper function)
  const checkIfAppetizeUrl = useCallback((url) => {
    if (!url || typeof url !== 'string') return false;
    return url.includes('appetize.io') || url.includes('app.appetize.io');
  }, []);

  // Check URL accessibility before showing iframe
  const checkUrlAccessibility = useCallback(async (url) => {
    if (checkIfAppetizeUrl(url)) {
      return { accessible: true, status: 200, isAppetize: true };
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);

      if (response.status === 404) {  
        return { accessible: false, status: 404, error: 'Not Found' };
      } else if (response.status === 502) {
        return { accessible: false, status: 502, error: 'Bad Gateway' };
      } else if (response.ok) {
        return { accessible: true, status: response.status };
      } else {
        return { accessible: false, status: response.status, error: response.statusText };
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        return { accessible: false, status: 'timeout', error: 'Request timeout' };
      }
      
      if (error.message.includes('CORS') || 
          error.message.includes('cross-origin') ||
          error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        return { accessible: true, status: 200, corsBlocked: true };
      }
      
      return { accessible: false, status: 'network', error: error.message };
    }
  }, [checkIfAppetizeUrl]);

  // Restart container method
  const restartContainer = useCallback((containerName = selectedContainer) => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      wsConnection.send(JSON.stringify({
        type: "restart_container",
        task_id: currentTaskId,
        input_data: {
          container_name: containerName
        }
      }));
    }
  }, [wsConnection, currentTaskId, selectedContainer]);

  // Check URL before loading iframe
  const handleUrlCheck = useCallback(async (url) => {
    if (!url) return;

    setIsCheckingUrl(true);
    setUrlStatus('checking');
    
    const result = await checkUrlAccessibility(url);
    
    if (result.accessible) {
      setUrlStatus('success');
      setIframeError(false);
      setErrorType(null);
    } else {
      setUrlStatus('error');
      setIframeError(true);
      
      if (result.status === 404) {
        setErrorType('404');
      } else if (result.status === 502) {
        setErrorType('502');
      } else {
        setErrorType('other');
      }
    }
    
    setIsCheckingUrl(false);
  }, [checkUrlAccessibility]);

  // Manual refresh function
  const refreshIframe = useCallback(() => {
    setIframeError(false);
    setErrorType(null);
    setIsCheckingUrl(false);
    setUrlStatus('checking');
    setIframeKey(Date.now());

    const container = getSelectedContainerObj();
    if (container?.url) {
      setTimeout(() => {
        handleUrlCheck(processPreviewUrl(container.url));
      }, 100);
    }
  }, [handleUrlCheck, getSelectedContainerObj]);

  // Reset states when container changes
  useEffect(() => {
    setIframeError(false);
    setErrorType(null);
    setIsCheckingUrl(false);
    setUrlStatus('checking');
    setIframeKey(Date.now());
  }, [selectedContainer]);

  // Check URL when container becomes available and update Appetize
  useEffect(() => {
    const container = getSelectedContainerObj();
    if (container?.status === 'running' && container.url) {
      const processedUrl = processPreviewUrl(container.url);
      handleUrlCheck(processedUrl);
      updateUrl(processedUrl);
    }
  }, [getSelectedContainerObj, handleUrlCheck, updateUrl]);

  // Initialize Appetize client when iframe loads
  useEffect(() => {
    let timer;
    
    // Only initialize if all conditions are met and not already initializing
    if (appetizeState.isAppetizeUrl && 
        urlStatus === 'success' && 
        iframeRef.current &&
        appetizeState.connectionStatus === 'disconnected') {
      
      console.log('Initializing Appetize client...');
      
      const timer = setTimeout(() => {
        initializeAppetizeClient(iframeRef.current);
      }, APPETIZE_INIT_DELAY);

      return () => clearTimeout(timer);
    }
  }, [
    appetizeState.isAppetizeUrl, 
    urlStatus, 
    appetizeState.connectionStatus,
    initializeAppetizeClient
  ]);

  // Get current device dimensions
  const getCurrentDimensions = useCallback(() => {
    const device = DEVICE_PRESETS[selectedDevice];
    // if (selectedDevice === 'custom') {
    //   return {
    //     width: isRotated ? customHeight : customWidth,
    //     height: isRotated ? customWidth : customHeight
    //   };
    // }
    
    if (device.width === '100%' || device.height === '100%') {
      return { width: '100%', height: '100%' };
    }
    
    return {
      width: isRotated ? device.height : device.width,
      height: isRotated ? device.width : device.height
    };
  }, [selectedDevice, customWidth, customHeight, isRotated]);

  // Handle device selection
  const handleDeviceChange = (deviceKey) => {
    setSelectedDevice(deviceKey);
    setIsRotated(false);
  };

  // Handle rotation
  const handleRotate = () => {
    if (selectedDevice !== 'desktop') {
      setIsRotated(!isRotated);
    }
  };

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    setShowResponsiveControls(true);
  };

  // Responsive Controls Component
  const ResponsiveControls = () => {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsDropdownOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    return (
      <div className="flex-shrink-0 bg-gray-50 border-b border-gray-200 p-3">
        <div className="flex items-center justify-between gap-4 flex-wrap">
          {/* Device Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center gap-2 px-3 py-1.5 bg-white border border-gray-300 rounded-md text-sm hover:bg-gray-50 transition-colors"
            >
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
              <span>{DEVICE_PRESETS[selectedDevice].name}</span>
              <svg
                className={`h-4 w-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-10">
                {Object.entries(DEVICE_PRESETS).map(([key, device]) => {
                  const IconComponent = device.icon;
                  return (
                    <button
                      key={key}
                      onClick={() => {
                        handleDeviceChange(key);
                        setIsDropdownOpen(false);
                      }}
                      className={`w-full flex items-center gap-3 px-4 py-2 text-sm transition-colors ${
                        selectedDevice === key
                          ? 'bg-primary text-white'
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      <IconComponent className="h-4 w-4" />
                      <span>{device.name}</span>
                    </button>
                  );
                })}
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="flex items-center gap-2">
            {/* Custom dimensions inputs */}
            {/* {selectedDevice === 'custom' && (
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  value={customWidth}
                  onChange={(e) => setCustomWidth(Math.max(200, parseInt(e.target.value) || 1200))}
                  className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                  min="200"
                  max="3000"
                />
                <span className="text-gray-500 text-sm">×</span>
                <input
                  type="number"
                  value={customHeight}
                  onChange={(e) => setCustomHeight(Math.max(200, parseInt(e.target.value) || 800))}
                  className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                  min="200"
                  max="3000"
                />
              </div>
            )} */}

            {/* Rotate button */}
            {selectedDevice !== 'desktop' && (
              <button
                onClick={handleRotate}
                className={`p-2 rounded-md border transition-colors ${
                  isRotated
                    ? 'bg-primary text-white border-primary'
                    : 'bg-white border-gray-300 hover:bg-gray-50'
                }`}
                title="Rotate viewport"
              >
                <RotateCcw className="h-4 w-4" />
              </button>
            )}

            {/* Fullscreen toggle */}
            <button
              onClick={toggleFullscreen}
              className="p-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </button>

            {/* Refresh button */}
            <button
              onClick={refreshIframe}
              className="p-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              title="Refresh preview"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Dimensions display */}
        <div className="mt-2 text-xs text-gray-500">
          {selectedDevice === 'desktop' ? (
            'Full viewport (100% × 100%)'
          ) : (
            `Current viewport: ${getCurrentDimensions().width}px × ${getCurrentDimensions().height}px${isRotated ? ' (rotated)' : ''}`
          )}
        </div>
      </div>
    );
  };

  const container = getSelectedContainerObj();

  // No containers available at all
  if (containers.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <p className="text-gray-500 text-sm">No containers available</p>
      </div>
    );
  }

  // Container not found
  if (!selectedContainer || !container) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <p className="text-gray-500 text-sm">Loading container...</p>
      </div>
    );
  }

  // Container is running - show iframe or error
  if (container.status === 'running' && container.url) {
    // Show checking message while determining URL accessibility
    if (isCheckingUrl || urlStatus === 'checking') {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-600 text-sm">
            Checking service availability...
          </p>
        </div>
      );
    }

    // Show error handling if URL is not accessible
    if (urlStatus === 'error' || iframeError) {
      const getErrorMessage = () => {
        switch (errorType) {
          case '404':
            return {
              icon: <AlertTriangle className="h-16 w-16 text-primary" />,
              title: "Project not available",
              description: "We couldn't find the project right now. It may be starting up — please check back shortly."
            };
          case '502':
            return {
              icon: <AlertTriangle className="h-16 w-16 text-primary" />,
              title: "Project is loading",
              description: "The project is taking longer than usual to respond. Please wait a moment and try again."
            };
          default:
            return {
              icon:<AlertTriangle className="h-16 w-16 text-primary" />,
              title: "Something went wrong",
              description: "We're having trouble connecting to the project. Please try again shortly."
            };
        }
      };

      const errorInfo = getErrorMessage();

      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          {errorInfo.icon}
          <p className="text-gray-600 text-sm font-medium text-center">
            {errorInfo.title}
          </p>
          <p className="text-gray-500 text-xs text-center max-w-md leading-relaxed">
            {errorInfo.description}
          </p>
          
          <button
            onClick={refreshIframe}
            className="px-6 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary-600 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </button>
        </div>
      );
    }

    // URL is accessible - show iframe with responsive controls
    if (urlStatus === 'success') {
      const finalUrl = processPreviewUrl(container.url);

      const dimensions = getCurrentDimensions();
      const isFixedSize = dimensions.width !== '100%' && dimensions.height !== '100%';

      return (
        <div className={`w-full h-full flex flex-col overflow-hidden ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
          {/* Responsive Controls */}
          {showResponsiveControls && <ResponsiveControls />}

          {/* Appetize Logs Panel */}
          {appetizeState.isAppetizeUrl && showResponsiveControls && (
            <div className="flex-shrink-0 p-2">
              <AppetizeLogs
                appetizeState={appetizeState}
                onClearLogs={clearLogs}
              />
            </div>
          )}

          {/* Preview Container */}
          <div className={`flex-1 relative overflow-hidden ${isFixedSize ? 'flex items-center justify-center bg-gray-100' : ''}`}>
            <div 
              ref={containerRef}
              className={`${isFixedSize ? 'border border-gray-300 shadow-lg bg-white' : 'w-full h-full'}`}
              style={isFixedSize ? {
                width: typeof dimensions.width === 'number' ? `${dimensions.width}px` : dimensions.width,
                height: typeof dimensions.height === 'number' ? `${dimensions.height}px` : dimensions.height,
                maxWidth: '100%',
                maxHeight: '100%'
              } : {}}
            >
              <iframe
                ref={iframeRef}
                key={`${container.name}-${iframeKey}-${selectedDevice}-${isRotated}`}
                src={finalUrl}
                className="w-full h-full border-none overflow-hidden"
                title={`Preview - ${container.name}`}
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                allow="microphone; camera; midi; encrypted-media;"
                onError={() => {
                  setIframeError(true);
                  setErrorType('other');
                }}
              />
            </div>

            {/* Floating controls for fullscreen mode */}
            {isFullscreen && (
              <div className="absolute top-4 right-4 flex gap-2">
                <button
                  onClick={refreshIframe}
                  className="p-2 bg-black/10 hover:bg-black/20 rounded-md transition-colors backdrop-blur-sm"
                  title="Refresh preview"
                >
                  <RefreshCw className="h-4 w-4 text-gray-700" />
                </button>
                <button
                  onClick={toggleFullscreen}
                  className="p-2 bg-black/10 hover:bg-black/20 rounded-md transition-colors backdrop-blur-sm"
                  title="Exit fullscreen"
                >
                  <Minimize2 className="h-4 w-4 text-gray-700" />
                </button>
              </div>
            )}
          </div>
        </div>
      );
    }
  }

  // Container is building/starting
  if (container.status === 'building' || container.status === 'starting') {
    return (
      <div className="w-full h-full flex items-center justify-center flex-col gap-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-gray-600 text-sm">
          {container.name} is {container.status}...
        </p>
        <p className="text-gray-500 text-xs text-center max-w-md">
          Please wait while the container starts up. This may take a few moments.
        </p>
      </div>
    );
  }

  // Container is stopped/not started/failed
  return (
    <div className="w-full h-full flex items-center justify-center flex-col gap-4">
      {container.status === 'failed' || container.status === 'error' ? (
        <>
          <AlertCircle className="h-8 w-8 text-red-500" />
          <p className="text-gray-600 text-sm">
            {container.name} failed to start
          </p>
          {container.error && (
            <p className="text-gray-500 text-xs max-w-md text-center">
              {container.error}
            </p>
          )}
        </>
      ) : container.status === ContainerStatusTypes.NOT_STARTED ? (
        <>
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-500 text-xs text-center max-w-md">
            The container is being automatically started. Please wait a moment.
          </p>
        </>
      ) : (
        <>
          <Square className="h-8 w-8 text-gray-400" />
          <p className="text-gray-600 text-sm">
            {container.name} is {container.status || 'not started'}
          </p>
        </>
      )}
    </div>
  );
};

PreviewPanel.displayName = 'PreviewPanel';

export default PreviewPanel;