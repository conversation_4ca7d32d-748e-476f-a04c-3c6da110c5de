import React from 'react';
import { ETAComponent } from '@/components/ETA';

const DeploymentPage = () => {
  // Example data - replace with your actual data
  const deploymentEstimatedTime = 30; // minutes
  const selectedItems = 5;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Deployment Overview</h1>
      
      {/* Using ETA Component */}
      <ETAComponent 
        estimatedMinutes={deploymentEstimatedTime}
        itemsSelected={selectedItems}
        displayText="30-60 mins"
      />
      
      {/* Rest of your page content */}
    </div>
  );
};

export default DeploymentPage;