"use client";

import React, { createContext, useContext, useState } from "react";

const SignupFlowContext = createContext(null);

export const SignupFlowProvider = ({ children }) => {
  const [referralCode, setReferralCode] = useState("");
  const [pendingSubmissionType, setPendingSubmissionType] = useState(null); // 'regular' | 'google' | null
  const [formSnapshot, setFormSnapshot] = useState(null);
  const [shouldAutoContinue, setShouldAutoContinue] = useState(false);
  const [hasAutoSubmitted, setHasAutoSubmitted] = useState(false);

  const value = {
    referralCode,
    setReferralCode,
    pendingSubmissionType,
    setPendingSubmissionType,
    formSnapshot,
    setFormSnapshot,
    shouldAutoContinue,
    setShouldAutoContinue,
    hasAutoSubmitted,
    setHasAutoSubmitted,
  };

  return (
    <SignupFlowContext.Provider value={value}>
      {children}
    </SignupFlowContext.Provider>
  );
};

export const useSignupFlow = () => {
  const ctx = useContext(SignupFlowContext);
  if (!ctx) throw new Error("useSignupFlow must be used within SignupFlowProvider");
  return ctx;
};


