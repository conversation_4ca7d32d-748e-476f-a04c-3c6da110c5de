"use client";

import React, { createContext, useState, useContext } from 'react';

// Create a context for Figma screen selection
const FigmaScreenContext = createContext();

// Create a provider component
export const FigmaScreenProvider = ({ children }) => {
  // Store selected screens by projectId
  const [selectedScreensByProject, setSelectedScreensByProject] = useState({});

  // Function to set selected screen for a specific project
  const setSelectedScreen = (projectId, screen) => {
    if (!projectId) return;
    
    setSelectedScreensByProject(prev => ({
      ...prev,
      [projectId]: screen ? {
        screen_id: screen.screen_id || screen.id,
        id: screen.id,
        screen_name: screen.screen_name || screen.name,
        name: screen.name,
        image_url: screen.image_url
      } : null
    }));
  };

  // Function to get selected screen for a specific project
  const getSelectedScreen = (projectId) => {
    if (!projectId) return null;
    return selectedScreensByProject[projectId] || null;
  };

  // Clear selected screen for a project
  const clearSelectedScreen = (projectId) => {
    if (!projectId) return;
    
    setSelectedScreensByProject(prev => {
      const newState = { ...prev };
      delete newState[projectId];
      return newState;
    });
  };

  return (
    <FigmaScreenContext.Provider 
      value={{ 
        selectedScreensByProject, 
        setSelectedScreen, 
        getSelectedScreen,
        clearSelectedScreen
      }}
    >
      {children}
    </FigmaScreenContext.Provider>
  );
};

// Default context value for when provider is missing
const defaultContextValue = {
  selectedScreensByProject: {},
  setSelectedScreen: () => console.warn('FigmaScreenProvider not found, using fallback'),
  getSelectedScreen: () => null,
  clearSelectedScreen: () => console.warn('FigmaScreenProvider not found, using fallback')
};

// Custom hook to use the Figma screen context
export const useFigmaScreen = () => {
  const context = useContext(FigmaScreenContext);
  // Return default context if provider is missing
  return context || defaultContextValue;
};

export default FigmaScreenContext;