"use client";

import React, { useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import * as Dialog from "@radix-ui/react-dialog";
import { FiPlus, FiMinus, FiMaximize, FiMinimize, FiDownload } from "react-icons/fi";

// Dynamically import svg-pan-zoom for client-side usage
const svgPanZoom = dynamic(() => import("svg-pan-zoom").then(mod => mod.default), { ssr: false });

const D2Chart = ({ chartDefinition }) => {
    const id = `d2-${Math.floor(1000 + Math.random() * 9000)}`;
    const chartRef = useRef(null);
    const modalChartRef = useRef(null);
    const panZoomRef = useRef(null);
    const modalPanZoomRef = useRef(null);
    const modalContentRef = useRef(null);

    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [isZoomEnabled, setIsZoomEnabled] = useState(false);
    const [isExpanded, setIsExpanded] = useState(false);
    const [isModalReady, setIsModalReady] = useState(false);
    const [diagramHeight, setDiagramHeight] = useState(0);
    const [svgContent, setSvgContent] = useState("");

    // Fetch SVG from Kroki
    const renderDiagram = async (container) => {
        if (!chartDefinition || !container.current) return;

        setIsLoading(true);
        setError(null);

        try {
            const response = await fetch("https://kroki.io/d2/svg", {
                method: "POST",
                headers: { "Content-Type": "text/plain" },
                body: chartDefinition,
            });

            if (!response.ok) throw new Error(`Kroki error: ${response.statusText}`);

            const svg = await response.text();
            setSvgContent(svg);
            container.current.innerHTML = svg;

            const svgElement = container.current.querySelector("svg");
            if (svgElement) {
                svgElement.style.width = "100%";
                svgElement.style.height = "100%";
                const naturalHeight = svgElement.getBoundingClientRect().height;
                setDiagramHeight(Math.min(window.innerHeight * 0.45, Math.max(350, naturalHeight)));
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    };

    // Initialize pan-zoom
    const initializePanZoom = async (container, ref) => {
        if (container.current) {
            const svgElement = container.current.querySelector("svg");
            if (!svgElement) return;

            if (ref.current) ref.current.destroy();

            try {
                const panZoomModule = await import("svg-pan-zoom");
                ref.current = panZoomModule.default(svgElement, {
                    zoomEnabled: true,
                    controlIconsEnabled: false,
                    mouseWheelZoomEnabled: true,
                    fit: true,
                    center: true,
                    minZoom: 0.1,
                    maxZoom: 10,
                });
            } catch (err) {
                console.error("PanZoom init error:", err);
            }
        }
    };

    const handleDownloadPNG = () => {
        if (!chartRef.current) {
            return;
        }

        const svg = chartRef.current.querySelector("svg");
        if (!svg) {
            return;
        }

        // Serialize SVG to string
        const serializer = new XMLSerializer();
        let source = serializer.serializeToString(svg);

        // Ensure SVG namespace is included
        if (!source.includes('xmlns="http://www.w3.org/2000/svg"')) {
            source = source.replace(/^<svg/, '<svg xmlns="http://www.w3.org/2000/svg"');
        }

        // Create a Blob for the SVG
        const svgBlob = new Blob([source], { type: "image/svg+xml;charset=utf-8" });
        const url = URL.createObjectURL(svgBlob);
        const img = new Image();

        img.onload = () => {
            try {
                // Get SVG dimensions from viewBox or attributes
                const viewBox = svg.getAttribute("viewBox")?.split(" ").map(Number) || [];
                const svgWidth = viewBox[2] || svg.getAttribute("width") || 800;
                const svgHeight = viewBox[3] || svg.getAttribute("height") || 600;

                // Set high scale factor for best quality
                const scaleFactor = 5; // Higher value for better quality
                const canvas = document.createElement("canvas");
                canvas.width = svgWidth * scaleFactor;
                canvas.height = svgHeight * scaleFactor;
                const ctx = canvas.getContext("2d");

                if (!ctx) {
                    console.error("Failed to get canvas 2D context.");
                    URL.revokeObjectURL(url);
                    return;
                }

                // Disable image smoothing for sharper output
                ctx.imageSmoothingEnabled = false;

                // White background for better contrast
                ctx.fillStyle = "#ffffff";
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Scale and draw the image
                ctx.scale(scaleFactor, scaleFactor);
                ctx.drawImage(img, 0, 0, svgWidth, svgHeight);

                // Export PNG with high quality
                canvas.toBlob(
                    (blob) => {
                        if (!blob) {
                            console.error("Failed to create PNG blob.");
                            return;
                        }
                        const a = document.createElement("a");
                        a.href = URL.createObjectURL(blob);
                        a.download = "diagram.png";
                        a.click();
                        URL.revokeObjectURL(a.href);
                    },
                    "image/png",
                    1.0 // Maximum quality
                );

                URL.revokeObjectURL(url);
            } catch (err) {
                console.error("Error generating PNG:", err);
                URL.revokeObjectURL(url);
            }
        };

        img.onerror = () => {
            console.error("Failed to load SVG image.");
            URL.revokeObjectURL(url);
        };

        img.src = url;
    };


    const handleZoomIn = (ref) => ref.current?.zoomIn();
    const handleZoomOut = (ref) => ref.current?.zoomOut();
    const handleReset = (ref) => ref.current?.reset();

    // Main diagram effect
    useEffect(() => {
        renderDiagram(chartRef).then(() => {
            if (isZoomEnabled) initializePanZoom(chartRef, panZoomRef);
        });
    }, [chartDefinition, isZoomEnabled]);

    // Modal diagram effect with delay to wait for modal open
    useEffect(() => {
        let timer;
        if (isExpanded && isModalReady) {
            timer = setTimeout(() => {
                renderDiagram(modalChartRef).then(() => initializePanZoom(modalChartRef, modalPanZoomRef));
            }, 200); // 200ms delay after modal opens
        }
        return () => clearTimeout(timer);
    }, [isExpanded, isModalReady, chartDefinition]);

    // Handle modal ready
    const toggleExpand = () => {
        setIsExpanded(prev => !prev);
        if (!isExpanded) setIsModalReady(true);
    };

    // Click outside modal to close
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalContentRef.current && !modalContentRef.current.contains(event.target)) {
                setIsExpanded(false);
            }
        };

        if (isExpanded) {
            document.addEventListener("mousedown", handleClickOutside, true);
        }

        return () => document.removeEventListener("mousedown", handleClickOutside, true);
    }, [isExpanded]);

    return (
        <div className="relative">
            <div className="bg-white rounded-md w-full">
                <div className="p-4 flex justify-between items-center border-b">
                    <button
                        onClick={() => setIsZoomEnabled(!isZoomEnabled)}
                        className="bg-gray-200 px-2 py-1 rounded"
                    >
                        {isZoomEnabled ? "Disable zoom" : "Enable zoom"}
                    </button>
                    <div className="flex space-x-2">
                        {isZoomEnabled && (
                            <>
                                <button onClick={() => handleZoomIn(panZoomRef)} className="p-1 rounded hover:bg-gray-100"><FiPlus /></button>
                                <button onClick={() => handleReset(panZoomRef)} className="p-1 rounded hover:bg-gray-100">Reset</button>
                                <button onClick={() => handleZoomOut(panZoomRef)} className="p-1 rounded hover:bg-gray-100"><FiMinus /></button>
                            </>
                        )}
                        <button onClick={toggleExpand} className="p-1 rounded hover:bg-gray-100">
                            {isExpanded ? <FiMinimize /> : <FiMaximize />}
                        </button>
                    </div>
                </div>

                <div className="p-4">
                    <div
                        className={`flex-grow overflow-auto relative ${isZoomEnabled ? 'cursor-grab' : ''} active:${isZoomEnabled ? 'cursor-grabbing' : ''}`}
                        style={{
                            height: `${diagramHeight}px`,
                            minHeight: '100px',
                            transition: 'height 0.3s ease',
                        }}
                    >
                        {isLoading && (
                            <div className="flex gap-2 items-center justify-center h-full">
                                <div className="animate-spin h-5 w-5 border-2 border-gray-900 rounded-full border-t-transparent" />
                                <p className="text-gray-700">Rendering diagram...</p>
                            </div>
                        )}
                        {error && (
                            <p className="text-red-600 text-center">Unable to render diagram. Please check syntax.</p>
                        )}
                        <div ref={chartRef} className="h-full w-full" style={{
                            position: 'relative',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }} />
                    </div>
                </div>
            </div>

            {/* Modal */}
            <Dialog.Root open={isExpanded} onOpenChange={setIsExpanded}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-50" />
                    <Dialog.Content className="fixed inset-0 flex items-center justify-center p-4 z-50 cursor-grab active:cursor-grabbing">
                        <div ref={modalContentRef} className="bg-white rounded-lg shadow-lg w-[90%] h-[90%] p-4 flex flex-col">
                            <div className="flex justify-end items-center border-b mb-4 space-x-2">
                                <button onClick={() => handleZoomIn(modalPanZoomRef)} className="p-1 rounded hover:bg-gray-100"><FiPlus /></button>
                                <button onClick={() => handleReset(modalPanZoomRef)} className="p-1 rounded hover:bg-gray-100">Reset</button>
                                <button onClick={() => handleZoomOut(modalPanZoomRef)} className="p-1 rounded hover:bg-gray-100"><FiMinus /></button>
                                <button onClick={() => { handleReset(panZoomRef); handleDownloadPNG(); }} className="p-1 rounded hover:bg-gray-100"><FiDownload /></button>
                                <Dialog.Close asChild>
                                    <button onClick={toggleExpand} className="p-1 rounded hover:bg-gray-100"><FiMinimize /></button>
                                </Dialog.Close>
                            </div>
                            <div className="flex-grow h-full w-full overflow-auto">
                                {isLoading && (
                                    <div className="flex gap-2 items-center justify-center h-full">
                                        <div className="animate-spin h-5 w-5 border-2 border-gray-900 rounded-full border-t-transparent" />
                                        <p className="text-gray-700">Rendering diagram...</p>
                                    </div>
                                )}
                                {error && (
                                    <p className="text-red-600 text-center">Unable to render diagram. Please check syntax.</p>
                                )}
                                <div ref={modalChartRef} className="h-full w-full" />
                            </div>
                        </div>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>
        </div>
    );
};

export default D2Chart;
