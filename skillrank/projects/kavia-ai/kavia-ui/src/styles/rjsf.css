/* Hide main form title */
.professional-form #root__title {
    display: none;
}

/* Hide section titles for single container form */
.single-container-form #root_container__title {
    display: none;
}

/* Hide array item titles and controls */
#root_overview_third_party_services .form-group > label {
    display: none;
}

.professional-form label[for*="third_party_services"] {
    display: none !important;
}

.professional-form label[for*="dependent_containers"] {
    display: none !important;
}


/* Hide move/remove buttons in single container form */
/* .single-container-form [id*="__move"],
.single-container-form [id*="__remove"] {
    display: none !important;
} */

/* Container styling when shown in array (not used anymore but kept for compatibility) */
#root_containers_0__title, 
#root_containers_1__title, 
#root_containers_2__title, 
#root_containers_3__title {
    margin-top: 20px !important;
    border: 1px solid #e5e7eb !important;
}

/* Hide array controls in main form */
[id*="root_containers_"][id*="__move"] {
    display: none !important;
}

/* Hide container delete button in array view */
.flex:has(.flex.items-end.justify-end:has([id*="root_containers_"][id*="__remove"])) {
    display: none !important;
}

/* Hide add container button in main form since we manage it in sidebar */
#root_containers__add {
    display: none !important;
}


.single-container-form .form-group {
    margin-bottom: 1.5rem;
}

.single-container-form label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    display: block;
}

.single-container-form input,
.single-container-form select,
.single-container-form textarea {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out;
}

.single-container-form input:focus,
.single-container-form select:focus,
.single-container-form textarea:focus {
    outline: none;
    border-color: #fb923c;
    box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.1);
}

/* Style the container details section */
.single-container-form fieldset {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.single-container-form fieldset legend {
    font-weight: 600;
    color: #1f2937;
    padding: 0 0.5rem;
}

/* Array fields within single container */
.single-container-form .array-item {
    background-color: #f9fafb;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    border: 1px solid #e5e7eb;
}

.single-container-form .array-item:hover {
    background-color: #f3f4f6;
}

/* Add item buttons styling */
.single-container-form button[id*="__add"] {
    background-color: #fb923c;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.15s ease-in-out;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.single-container-form button[id*="__add"]:hover {
    background-color: #f97316;
}

/* Remove item buttons in arrays */
.single-container-form .array-item button[id*="__remove"] {
    background-color: #ef4444;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.single-container-form .array-item button[id*="__remove"]:hover {
    background-color: #dc2626;
}

/* Help text styling */
.single-container-form .help-block {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

/* Error message styling */
.single-container-form .text-danger,
.single-container-form .has-error .help-block {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Required field indicator */
.single-container-form label.required::after {
    content: " *";
    color: #ef4444;
}

/* Disabled form styling */
.single-container-form[disabled] input,
.single-container-form[disabled] select,
.single-container-form[disabled] textarea,
.single-container-form[disabled] button {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Overview form specific styles */
.professional-form #root_overview {
    padding: 0;
}

.professional-form #root_overview__title {
    display: none;
}

/* Clean up spacing */
.professional-form .form-group:last-child,
.single-container-form .form-group:last-child {
    margin-bottom: 0;
}
/* Add help icons with sample messages using data attributes */
label[for*="auth"]::after { content: '?'; }
label[for*="auth"]::before { content: 'Authentication configuration for accessing external services and APIs'; }

label[for*="installCommand"]::after { content: '?'; }
label[for*="installCommand"]::before { content: 'Command to install dependencies (e.g., npm install, yarn install)'; }

label[for*="buildCommand"]::after { content: '?'; }
label[for*="buildCommand"]::before { content: 'Command to build the project for production deployment. e.g. npm run build or yarn build'; }

label[for*="startCommand"]::after { content: '?'; }
label[for*="startCommand"]::before { content: 'Command to start the development server with proper environment variables. e.g. npm start or yarn dev'; }

label[for*="lintCommand"]::after { content: '?'; }
label[for*="lintCommand"]::before { content: 'Command to run code linting and formatting tools'; }

label[for*="generateOpenapiCommand"]::after { content: '?'; }
label[for*="generateOpenapiCommand"]::before { content: 'Command to generate OpenAPI/Swagger documentation from your API code'; }


label[for*="port"]::after { content: '?'; }
label[for*="port"]::before { content: 'Port on which the container will run. This should be unique across all containers. e.g. 3000'; }

label[for*="workspace"]::after { content: '?'; }
label[for*="workspace"]::before { content: 'Workspace directory for the container. Typically refers the repository root directory. e.g. <repository-name>'; }

label[for*="container_root"]::after { content: '?'; }
label[for*="container_root"]::before { content: 'Root directory of the container where the application code is located. e.g. <repository-name>/app'; }
/* Base help icon styling */
label[for*="auth"]::after,
label[for*="installCommand"]::after,
label[for*="buildCommand"]::after,
label[for*="startCommand"]::after,
label[for*="lintCommand"]::after,
label[for*="generateOpenapiCommand"]::after,
label[for*="port"]::after,
label[for*="workspace"]::after,
label[for*="container_root"]::after {
    width: 16px;
    height: 16px;
    background-color: #6b7280;
    color: white;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    cursor: help;
    margin-left: 6px;
    vertical-align: middle;
    transition: background-color 0.2s ease;
    position: relative;
}

/* Hide tooltip by default */
label[for*="auth"]::before,
label[for*="installCommand"]::before,
label[for*="buildCommand"]::before,
label[for*="startCommand"]::before,
label[for*="lintCommand"]::before,
label[for*="generateOpenapiCommand"]::before,
label[for*="port"]::before ,
label[for*="workspace"]::before,
label[for*="container_root"]::before {
    display: none;
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    background-color: #1f2937;
    color: white;
    font-size: 0.75rem;
    line-height: 1.4;
    padding: 8px 12px;
    border-radius: 6px;
    max-width: 300px;
    width: max-content;
    text-align: left;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    word-wrap: break-word;
    white-space: normal;
}

/* Show tooltip on hover */
label[for*="auth"]:hover::before,
label[for*="installCommand"]:hover::before,
label[for*="buildCommand"]:hover::before,
label[for*="startCommand"]:hover::before,
label[for*="lintCommand"]:hover::before,
label[for*="generateOpenapiCommand"]:hover::before,
label[for*="port"]:hover::before,
label[for*="workspace"]:hover::before,
label[for*="container_root"]:hover::before {
    display: block;
    animation: fadeIn 0.2s ease-in-out;
}

/* Hover effect for icon */
label[for*="auth"]:hover::after,
label[for*="installCommand"]:hover::after,
label[for*="buildCommand"]:hover::after,
label[for*="startCommand"]:hover::after,
label[for*="lintCommand"]:hover::after,
label[for*="generateOpenapiCommand"]:hover::after,
label[for*="port"]:hover::after,
label[for*="workspace"]:hover::after,
label[for*="container_root"]:hover::after {
    background-color: #374151;
    transform: scale(1.1);
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Ensure labels are positioned */
label { position: relative; }