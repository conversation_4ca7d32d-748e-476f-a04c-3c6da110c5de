"use client";

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useUser } from "@/components/Context/UserContext";

const ArchitectureRedirect = () => {
  const pathname = usePathname();
  const router = useRouter();
  const projectId = pathname.split("/")[3];

  const { is_admin, tenant_id, fetchUserData } = useUser();

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  useEffect(() => {
    if (projectId) {
      router.replace(`/project/${projectId}/architecture/architecture-requirement`);
    }
  }, [projectId, router]);

  return null;
};

export default ArchitectureRedirect;
