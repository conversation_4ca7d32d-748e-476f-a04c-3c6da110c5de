// deployment/components/Overview/index.tsx

import React from 'react';
import dynamic from "next/dynamic";
import Image from 'next/image';
import { FaChevronDown, FaChevronUp } from "react-icons/fa";

const NoSSR = dynamic(() => import('@/components/Chart/MermaidChart'), { ssr: false });

// Types
interface AccordionSectionProps {
  title: string;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

// Accordion Component
const AccordionSection: React.FC<AccordionSectionProps> = ({ 
  title, 
  isExpanded, 
  onToggle, 
  children 
}) => (
  <div className="mb-6">
    <div 
      className="flex items-center cursor-pointer mb-2"
      onClick={onToggle}
    >
      {isExpanded ? (
        <FaChevronDown className="h-4 w-4 text-gray-500 mr-2" />
      ) : (
        <FaChevronUp className="h-4 w-4 text-gray-500 mr-2" />
      )}
      <h2 className="text-[16px] font-weight-semibold text-[#2A3439]">{title}</h2>
    </div>
    {isExpanded && (
      <div className="pl-6">
        {children}
      </div>
    )}
  </div>
);

// Empty State Component
const EmptyState = () => (
  <div className="px-4 py-8">
    <div className="mx-auto">
      <div className="flex justify-center mb-12">
        <Image
          src="/images/butterfly.png"
          alt="Kavia Logo"
          width={89}
          height={115}
          className="max-auto"
        />
      </div>
      <div className="text-center">
        <h3 className="text-[22px] font-weight-semibold text-[#2A3439] mb-2">
          Ready to Build Your Infrastructure?
        </h3>
        <p className="typography-body-lg text-[#2A3439] leading-[21px]">
          Get started by either creating a custom infrastructure setup or<br />
          using our intelligent auto-configuration.
        </p>
      </div>
    </div>
  </div>
);

// Mock Data
const mockData = {
  properties: {
    project_name: "Infrastructure Deployment 2024",
    platform: "AWS",
    region: "us-west-2",
    status: "Pending Deployment",
    created: "October 29, 2024 15:30 UTC",
    deployment_type: "High Availability",
    infrastructure_overview: `
C4Context
title AWS Web Application Infrastructure - Comprehensive C4 Diagram

Person(user, "End User", "External users accessing the web application")

Enterprise_Boundary(aws, "AWS Cloud Infrastructure") {
    Container(vpc, "Web Application VPC (10.0.0.0/16)", "network")
    
    Container(launch_template, "Launch Template", "AWS Launch Template", "EC2 instance configuration")
    Container(internet_gateway, "Internet Gateway", "AWS IGW", "Provides internet connectivity")
    Container(alb_sg, "ALB Security Group", "AWS Security Group", "Controls load balancer traffic")
    
    Container(asg, "Auto Scaling Group", "AWS ASG", "Manages EC2 instances scaling")
    Container(web_sg, "Web Security Group", "AWS Security Group", "Controls web tier traffic")
    
    Container(alb, "Application Load Balancer", "AWS ALB", "Distributes incoming traffic")
    
    Container(web_server1, "Web Server 1", "EC2 Instance", "Hosts web application")
    Container(web_server2, "Web Server 2", "EC2 Instance", "Hosts web application")
}

Rel(user, internet_gateway, "HTTPS/443")
Rel(internet_gateway, alb, "Routes traffic")
Rel(alb, web_server1, "Distributes requests")
Rel(alb, web_server2, "Distributes requests")
Rel(launch_template, asg, "Provides configuration")
Rel(asg, web_server1, "Manages")
Rel(asg, web_server2, "Manages")
Rel(alb_sg, alb, "Secures")
Rel(web_sg, web_server1, "Secures")
Rel(web_sg, web_server2, "Secures")
    `
  }
};

interface OverviewProps {
  showData: boolean;
}

const Overview: React.FC<OverviewProps> = ({ showData }) => {
  const [expandedSections, setExpandedSections] = React.useState({
    summary: true,
    overview: true
  });

  return (
    <div className="px-4 py-8">
      <div className="mx-auto">
        <div className="text-left mb-12">
          <h2 className="text-[20px] font-weight-semibold text-[#2A3439] mb-3">
            Streamlined Cloud Deployment
          </h2>
          <p className="text-[14px] text-[#2A3439] leading-[21px]">
            Effortlessly configure, visualize, and manage your infrastructure across multiple cloud platforms. 
            From initial setup to continuous monitoring, our intuitive interface guides you through every 
            step of the deployment process, ensuring optimal performance and scalability for your applications.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Overview;