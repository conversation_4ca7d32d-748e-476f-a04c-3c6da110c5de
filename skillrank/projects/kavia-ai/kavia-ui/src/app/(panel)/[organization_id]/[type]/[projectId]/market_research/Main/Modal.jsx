import React, { useRef, useState, useEffect, useContext } from "react";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { X, FilePlus2 } from 'lucide-react';

const Modal = ({ showModal, onClose }) => {
  const modalRef = useRef(null);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const { showAlert } = useContext(AlertContext);


  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  if (!showModal) return null;

  const showBtn = () => {
    if (!title || !description) {
      return;
    }
    showAlert("Functionality was not implemented", "info")
    onClose()
  }


  return (
    <>
      <div className="fixed inset-0 bg-gray-900 bg-opacity-50 z-40"></div>

      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="bg-white w-full max-w-2xl p-6 rounded-lg shadow-lg relative" ref={modalRef} >
          <div className="flex justify-end">
            <DynamicButton
              variant="ghost"
              size="sqSmall"
              icon={X}
              onClick={onClose}
              tooltip="Close"
            />
          </div>

          <h2 className="project-panel-title mb-4 border-b-2 border-gray-200 pb-2">
            Create Secondary Research Item
          </h2>

          <div className="mb-4">
            <label className="block create-secondary-research mb-1">
              Name
            </label>
            <input
              type="text"
              className="border border-gray-300 rounded-lg p-2 w-full"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Name"
            />
          </div>

          <div className="mb-4">
            <label className="block create-secondary-research mb-1">
              Description
            </label>
            <textarea
              className="border border-gray-300 rounded-lg p-2 w-full  max-h-40 overflow-y-auto custom-scrollbar"
              rows="4"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Description"
            ></textarea>
          </div>

          <div className="mb-4">
            <label className="block create-secondary-research mb-1">
              Select Type
            </label>
            <select className="border border-gray-300 rounded-lg p-2 w-full">
              <option value="">Type</option>
              <option value="Internal">Internal</option>
              <option value="Generic">Generic</option>
              <option value="Defined">Defined</option>
            </select>
          </div>

          <div className="flex justify-end">
            <DynamicButton
              type="submit"
              variant="primary"
              icon={FilePlus2}
              onClick={showBtn}
              text="Create"
            />
          </div>

        </div>
      </div>
    </>
  );
};

export default Modal;
