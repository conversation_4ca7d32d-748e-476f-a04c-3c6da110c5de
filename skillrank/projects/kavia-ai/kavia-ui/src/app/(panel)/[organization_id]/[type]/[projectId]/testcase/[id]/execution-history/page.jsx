"use client";
import React, { useState } from "react";
import { MoreVertical } from "lucide-react";
import TableComponent from "@/components/SimpleTable/DropDownTable";

const ExecutionHistoryTab = () => {
  const [testRuns] = useState([
    { id: 1, status: "SUCCESS", name: "Smoke Test", date: "07/09/2024" },
    { id: 2, status: "RUNNING", name: "Staging", date: "07/09/2024" },
    { id: 3, status: "FAILED", name: "Run 3", date: "07/09/2024" },
    { id: 4, status: "SUCCESS", name: "Run 4", date: "07/09/2024" },
    { id: 5, status: "RUNNING", name: "Run 5", date: "07/09/2024" },
    // Add more items to demonstrate scrolling
    ...Array(22)
      .fill()
      .map((_, i) => ({
        id: i + 6,
        status: ["SUCCESS", "FAILED", "RUNNING"][i % 3],
        name: `Run ${i + 6}`,
        date: "07/09/2024",
      })),
  ]);

  const headers = [
    { key: "status", label: "Status" },
    { key: "name", label: "Run" },
    { key: "date", label: "Date" },
    { key: "action", label: "", icon: <MoreVertical size={16} /> },
  ];

  const handleRowClick = (id) => {
    
  };

  const handleActionClick = (id) => {
    
  };

  return (
    <div className="pb-16">
      <h2 className="typography-body-lg font-weight-semibold mb-2">Execution History</h2>
      <p className="mb-4 typography-body-sm text-gray-600">
        This test case aims to validate the accuracy of the temperature
        prediction algorithm by comparing the predicted temperatures against
        actual recorded temperatures for a given set of locations over a
        specific time period.
      </p>
      <TableComponent
        title=""
        data={testRuns}
        headers={headers}
        onRowClick={handleRowClick}
        onActionClick={handleActionClick}
        sortableColumns={{ name: true, date: true }}
        itemsPerPage={20}
      />
    </div>
  );
};

export default ExecutionHistoryTab;
