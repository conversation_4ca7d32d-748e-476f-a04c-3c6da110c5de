import React from 'react';
import dynamic from 'next/dynamic';

const NoSSR = dynamic(() => import('@/components/Chart/MermaidChart'), {
  ssr: false
});

const MermaidChartWrapper = ({ chart }) => {
  const charts = Array.isArray(chart) ? chart : [{ chart }];

  return (
    <div className="p-6">
      <div className="bg-white rounded-lg shadow-sm">
        {charts.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && <hr className="my-6 border-gray-200" />}
            <NoSSR chartDefinition={item.chart} />
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default MermaidChartWrapper;