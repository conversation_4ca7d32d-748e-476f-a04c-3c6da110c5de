"use client"

import React, { useEffect, useContext } from 'react'
import WorkItems from '@/components/BrowsePanel/workItems'
import "@/styles/tabs/workItem.css"
import { useUser } from "@/components/Context/UserContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

const Page = () => {
  const { is_admin, tenant_id, fetchUserData } = useUser();
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  return (
    <>
      <div className="relative">
        <WorkItems />
      </div>
    </>
  )
}

export default Page
