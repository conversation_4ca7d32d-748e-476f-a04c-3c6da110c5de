// pages/deployment_tracker.jsx

"use client"
import { useEffect, useState, useMemo } from 'react';
import { useDeploymentDashboard, DEPLOYMENT_VIEWS, DEPLOYMENT_STATUSES } from '@/hooks/useDeploymentDashboard';
import { getDeploymentChartData } from '@/utils/api';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import StatsCards from '@/components/dashboard/StatsCards';
import Breadcrumb from '@/components/dashboard/Breadcrumb';
import LoadingOverlay from '@/components/dashboard/LoadingOverlay';
import DeploymentChart from '@/components/dashboard/DeploymentChart';
import TenantsDeploymentList from '@/components/dashboard/TenantsDeploymentList';
import ProjectsList from '@/components/dashboard/ProjectsList';
import DeploymentsList from '@/components/dashboard/DeploymentsList';
import DeploymentDetails from '@/components/dashboard/DeploymentDetails';

const Page = () => {
  const {
    // State
    currentView,
    loading,
    error,
    overallStats,
    tenants,
    projects,
    deployments,
    deploymentDetails,
    selectedTenant,
    selectedProject,
    globalFilters,

    // Navigation
    navigateToOverview,
    navigateToTenant,
    navigateToProject,
    navigateToDeployment,
    navigateToTenantView,
    navigateToProjectView,

    // Data loading
    loadOverallStats,
    loadTenants,
    loadProjects,
    loadDeployments,

    // Filtering
    applyFilters,
    refreshData,



    // Utilities
    clearError
  } = useDeploymentDashboard();

  const defaultDates = useMemo(() => {
    const now = new Date();

    // From Date: 7 days ago at 6:00 AM
    const fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7, 6, 0, 0);

    // To Date: Current date and time
    const toDate = new Date(now);

    // Format for datetime-local input using local time (YYYY-MM-DDTHH:mm)
    const formatDateTime = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day}T${hours}:${minutes}`;
    };

    return {
      from: formatDateTime(fromDate),
      to: formatDateTime(toDate)
    };
  }, []);

  // Local state for filters and dashboard data
  const [lastUpdated, setLastUpdated] = useState(null);
  const [filterLoading, setFilterLoading] = useState(false);
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    status: DEPLOYMENT_STATUSES.ALL
  });
  const [chartData, setChartData] = useState([]);
  const [chartLoading, setChartLoading] = useState(false);
  const [chartError, setChartError] = useState(null);
  const [granularity, setGranularity] = useState('day');

  // Initialize dashboard data - run only once on mount
  useEffect(() => {
    const initDashboard = async () => {
      try {
        // Only call clearFilters which will load data with proper default filters
        // This avoids duplicate API calls (first without filters, then with filters)
        await clearFilters();
      } catch (error) {
        console.error('Failed to initialize deployment dashboard:', error);
        setChartError({ message: 'Failed to initialize dashboard' });
      }
    };

    initDashboard();
  }, []); // Empty dependency array - run only once on mount

  const applyFiltersHandler = async (filterOverrides = {}, granularityOverride = null) => {
    try {
      setFilterLoading(true);
      setChartLoading(true);
      setChartError(null);

      const currentFilters = { ...filters, ...filterOverrides };
      const currentGranularity = granularityOverride || granularity;
      const { dateFrom, dateTo, status } = currentFilters;
      // Map frontend status to backend status
      let statusFilter = null;
      if (status && status !== DEPLOYMENT_STATUSES.ALL) {
        if (status === DEPLOYMENT_STATUSES.COMPLETED) {
          statusFilter = 'success'; // Backend uses 'success' for completed
        } else if (status === DEPLOYMENT_STATUSES.PROCESSING) {
          statusFilter = 'processing'; // Backend uses 'processing'
        } else if (status === DEPLOYMENT_STATUSES.FAILED) {
          statusFilter = 'build_failed'; // Backend uses 'build_failed' for failed
        }
      }

      // Convert datetime-local format to ISO string (only if not empty)
      const fromDate = dateFrom && dateFrom !== '' ? new Date(dateFrom).toISOString() : null;
      const toDate = dateTo && dateTo !== '' ? new Date(dateTo).toISOString() : null;

      // Apply filters using the hook
      applyFilters({ dateFrom: fromDate, dateTo: toDate, status: statusFilter });

      // Fetch chart data with status filter applied
      const chartDataResult = await getDeploymentChartData(fromDate, toDate, statusFilter, currentGranularity);

      // Handle chart data
      if (chartDataResult && chartDataResult.chart_data) {
        setChartData(chartDataResult.chart_data);
      } else if (Array.isArray(chartDataResult)) {
        setChartData(chartDataResult);
      } else {
        setChartData([]);
      }

      setLastUpdated(new Date().toLocaleTimeString());

    } catch (error) {
      console.error('Failed to apply filters:', error);
      setChartError({ message: 'Failed to load filtered data' });
    } finally {
      setFilterLoading(false);
      setChartLoading(false);
    }
  };

  // Clear filters - Shows ALL deployments without date limits
  const clearFilters = async () => {
    const resetFilters = {
      dateFrom: '',
      dateTo: '',
      status: DEPLOYMENT_STATUSES.ALL
    };

    try {
      setFilterLoading(true);
      setChartLoading(true);
      setChartError(null);

      // Update filters and granularity state
      setFilters(resetFilters);
      setGranularity('day');

      // Apply filters using the hook (no date limits)
      applyFilters({ dateFrom: null, dateTo: null, status: null });

      // Load chart data (all deployments from beginning of time)
      const chartDataResult = await getDeploymentChartData(null, null, null, 'day');

      // Handle chart data
      if (chartDataResult && chartDataResult.chart_data) {
        setChartData(chartDataResult.chart_data);
      } else if (Array.isArray(chartDataResult)) {
        setChartData(chartDataResult);
      } else {
        setChartData([]);
      }

      setLastUpdated(new Date().toLocaleTimeString());

    } catch (error) {
      console.error('Failed to clear filters:', error);
      setChartError({ message: 'Failed to load data' });
    } finally {
      setFilterLoading(false);
      setChartLoading(false);
    }
  };

  // Handle granularity change
  const handleGranularityChange = (newGranularity) => {
    setGranularity(newGranularity);
    if (chartData.length > 0) {
      applyFiltersHandler({}, newGranularity);
    }
  };

  // Navigation handlers
  const handleTenantClick = (tenant) => {
    navigateToTenant(tenant);
  };

  const handleProjectClick = (project) => {
    navigateToProject(project);
  };

  const handleDeploymentClick = (deployment) => {
    navigateToDeployment(deployment);
  };



  // Determine current data to display
  const currentStats = overallStats;
  const currentTenants = tenants;
  const hasFiltersApplied = filters.dateFrom !== '' || 
                            filters.dateTo !== '' || 
                            filters.status !== DEPLOYMENT_STATUSES.ALL;

  // Custom stats cards for deployment dashboard
  const deploymentStatsCards = currentStats ? [
    {
      icon: "🚀",
      label: "Total Deployments",
      value: (currentStats.total_deployments || 0).toLocaleString(),
      color: "primary"
    },
    {
      icon: "⏳",
      label: "Processing",
      value: (currentStats.processing_deployments || 0).toLocaleString(),
      color: "yellow"
    },
    {
      icon: "✅",
      label: "Completed Deployments",
      value: (currentStats.completed_deployments || 0).toLocaleString(),
      color: "green"
    },
    {
      icon: "❌",
      label: "Failed",
      value: (currentStats.failed_deployments || 0).toLocaleString(),
      color: "red"
    },
    {
      icon: "📁",
      label: "Total Projects",
      value: (currentStats.total_projects || 0).toLocaleString(),
      color: "purple"
    },
    {
      icon: "🏢",
      label: "Total Tenants",
      value: (currentStats.total_tenants || 0).toLocaleString(),
      color: "blue"
    }
  ] : null;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <DashboardHeader 
        onRefresh={refreshData}
        loading={loading || filterLoading}
        lastUpdated={lastUpdated}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters Section */}
        {currentView === DEPLOYMENT_VIEWS.OVERVIEW && (
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    From Date
                  </label>
                  <input
                    type="datetime-local"
                    value={filters.dateFrom}
                    onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    To Date
                  </label>
                  <input
                    type="datetime-local"
                    value={filters.dateTo}
                    onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option value={DEPLOYMENT_STATUSES.ALL}>All Statuses</option>
                    <option value={DEPLOYMENT_STATUSES.PROCESSING}>Processing</option>
                    <option value={DEPLOYMENT_STATUSES.COMPLETED}>Completed</option>
                    <option value={DEPLOYMENT_STATUSES.FAILED}>Failed</option>
                  </select>
                </div>
                
                <div className="flex items-end space-x-2">
                  <button
                    onClick={() => applyFiltersHandler()}
                    disabled={filterLoading}
                    className="flex-1 bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-700 disabled:opacity-50 text-sm font-medium"
                  >
                    {filterLoading ? 'Applying...' : 'Apply Filters'}
                  </button>
                </div>
              </div>

              {/* Active Filters Display */}
              {hasFiltersApplied && (
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">Active filters:</span>
                      <div className="flex flex-wrap gap-2">
                        {filters.dateFrom && filters.dateFrom !== '' && (
                          <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm">
                            From: {new Date(filters.dateFrom).toLocaleDateString()}
                          </span>
                        )}
                        
                        {filters.dateTo && filters.dateTo !== '' && (
                          <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm">
                            To: {new Date(filters.dateTo).toLocaleDateString()}
                          </span>
                        )}
                        
                        {filters.status !== DEPLOYMENT_STATUSES.ALL && (
                          <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded capitalize text-sm">
                            Status: {filters.status}
                          </span>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={clearFilters}
                      className="text-primary hover:text-primary-800 text-sm font-medium"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Error Banner */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error occurred</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error.message}</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={clearError}
                    className="text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200 transition-colors"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <StatsCards
          stats={currentStats}
          loading={filterLoading && !currentStats}
          error={!currentStats && !filterLoading ? { message: 'No data available' } : null}
          customCards={deploymentStatsCards}
        />

        {/* Chart Section - Only show on Overview */}
        {currentView === DEPLOYMENT_VIEWS.OVERVIEW && (
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
              <h3 className="text-lg font-semibold text-gray-900">Deployment Activity Chart</h3>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-700 whitespace-nowrap">Granularity:</label>
                  <select
                    value={granularity}
                    onChange={(e) => handleGranularityChange(e.target.value)}
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-primary focus:border-primary bg-white"
                  >
                    <option value="day">Daily</option>
                    <option value="hour">Hourly</option>
                  </select>
                </div>

                {chartData.length > 0 && (
                  <div className="text-sm text-gray-500">
                    {chartData.length} data point{chartData.length !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </div>

            <DeploymentChart
              chartData={chartData}
              loading={chartLoading}
              error={chartError}
              granularity={granularity}
            />
          </div>
        )}

        {/* Breadcrumb Navigation */}
        <Breadcrumb
          currentView={currentView}
          selectedTenant={selectedTenant}
          selectedUser={selectedProject} // Reusing the same breadcrumb component
          onNavigateToOverview={navigateToOverview}
          onNavigateToTenant={navigateToTenantView}
          onNavigateToUser={navigateToProjectView}
        />

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Overview Section */}
            {currentView === DEPLOYMENT_VIEWS.OVERVIEW && (
              <div>
                <TenantsDeploymentList
                  tenants={currentTenants}
                  onTenantSelect={handleTenantClick}
                  loading={filterLoading}
                  error={error}
                  onRetry={refreshData}
                />
              </div>
            )}

            {/* Tenant View */}
            {currentView === DEPLOYMENT_VIEWS.TENANT && (
              <ProjectsList
                projects={projects}
                tenantName={selectedTenant?.tenant_name}
                onProjectSelect={handleProjectClick}
                onBackToTenants={navigateToOverview}
                loading={loading}
                error={error}
                onRetry={refreshData}
              />
            )}

            {/* Project View */}
            {currentView === DEPLOYMENT_VIEWS.PROJECT && (
              <DeploymentsList
                deployments={deployments}
                projectId={selectedProject?.project_id}
                projectStats={selectedProject}
                onDeploymentSelect={handleDeploymentClick}
                onBackToProjects={navigateToTenantView}
                loading={loading}
                error={error}
                onRetry={refreshData}
              />
            )}

            {/* Deployment Details View */}
            {currentView === DEPLOYMENT_VIEWS.DEPLOYMENT && (
              <DeploymentDetails
                deploymentData={deploymentDetails}
                onBackToDeployments={navigateToProjectView}
                loading={loading}
              />
            )}
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      <LoadingOverlay show={loading && !hasFiltersApplied} />
    </div>
  );
};

export default Page;